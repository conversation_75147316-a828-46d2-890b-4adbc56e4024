"use strict";

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _vue = _interopRequireDefault(require("vue"));

var _App = _interopRequireDefault(require("./App"));

var _router = _interopRequireDefault(require("@/router"));

var _viewDesign = _interopRequireDefault(require("view-design"));

require("view-design/dist/styles/iview.css");

var _language = _interopRequireDefault(require("@/language"));

var _httpRequest = _interopRequireDefault(require("@/utils/httpRequest"));

var _vueCookie = _interopRequireDefault(require("vue-cookie"));

var _store = _interopRequireDefault(require("@/store"));

var _utils = require("@/utils");

var _cloneDeep = _interopRequireDefault(require("lodash/cloneDeep"));

require("@/assets/css/index.css");

require("@/icons");

require("viewerjs/dist/viewer.css");

var echarts = _interopRequireWildcard(require("echarts"));

var _jsencrypt = _interopRequireDefault(require("jsencrypt"));

var _elementUi = _interopRequireDefault(require("element-ui"));

require("element-ui/lib/theme-chalk/index.css");

require("./iconfont/iconfont.js");

require("./assets/font/font_2707944_wjceekf9nw/iconfont.css");

var _dataView = _interopRequireDefault(require("@jiaminghi/data-view"));

require("@wangeditor/editor/dist/css/style.css");

var html2canvas = _interopRequireWildcard(require("@/utils/html2canvas.js"));

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { "default": obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj["default"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
// import VueParticles from 'vue-particles'// 粒子背景
// api: http://www.iconfont.cn/
// import echarts from '@/utils/echarts'
// import flvjs from 'flv.js'
// import "lib-flexible/flexible.js";
// import BaiduMap from 'vue-baidu-map'
// Vue.use(BaiduMap, {
//   // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
//   ak: 'RQtRYAXG5Uw6CNToTqAwYePzyds1iPD5'
// })
_vue["default"].prototype.$echarts = echarts;
_vue["default"].config.productionTip = false;
_vue["default"].prototype.$http = _httpRequest["default"]; // ajax请求方法

_vue["default"].prototype.isAuth = _utils.isAuth; // 权限方法

_vue["default"].prototype.html2canvas = html2canvas;
/**
 * 配置全局的加密方法
 * @param obj 需要加密的字符串
 */

_vue["default"].prototype.$encruption = function (obj) {
  var encrypt = new _jsencrypt["default"]();
  encrypt.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAsn3LoyXzv1D6LVAC/Pg5Rbc/7F0yVbQVaSR9ebVAb9O8aC1573De+SA1nnZaVO9rvfrG84oMU25WMBkcpKbCqehczLSceFFE/JI+ZzrN/51Pe7RoS8LwIdW+MDneatfXi0zbceCmVeHNyhBUpLq0zIt2gannnPfk4sg1BaRKcwIDAQAB");
  return encrypt.encrypt(obj);
};

_vue["default"].use(_dataView["default"]);

_vue["default"].use(_elementUi["default"]);

_vue["default"].use(_vueCookie["default"]); // Vue.use(VueParticles)


_vue["default"].use(_viewDesign["default"]); // Vue.use(flvjs)
// 保存整站vuex本地储存初始状态


window.SITE_CONFIG['storeState'] = (0, _cloneDeep["default"])(_store["default"].state);
/* eslint-disable no-new */

var vueThis = new _vue["default"]({
  el: '#app',
  router: _router["default"],
  i18n: _language["default"],
  store: _store["default"],
  components: {
    App: _App["default"]
  },
  template: '<App/>'
});
var _default = vueThis;
exports["default"] = _default;