<template>
  <div class="maxnbox" :style="{ 'height': documentClientHeight + 'px' }">
    <div class="login-wrap" :style="{ 'height': documentClientHeight + 'px' }">
      <div class="login-head">
        <span class="login-title">{{$t('register.register')}}</span>
      </div>
<!--        <div class="steps">-->
<!--          <Steps :current="current">-->
<!--            <Step :title="$t('register.personalInformation')"></Step>-->
<!--            <Step :title="$t('register.verified')"></Step>-->
<!--            <Step :title="$t('register.complete')"></Step>-->
<!--          </Steps>-->
<!--        </div>-->
        <Divider />
      <div v-if="current===0">
        <div class="content" :style="{ 'height': documentClientHeight-120 + 'px' }">
          <div>
            <h2>{{$t('register.personalInformation')}}</h2>
          </div>
          <Form ref="dataForm" :model="dataForm" :rules="dataRule">
            <FormItem prop="username">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-contact" size="large" type="text" v-model="dataForm.username" :placeholder="$t('login.username')"/></Col>
              </Row>
            </FormItem>
            <FormItem prop="password">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-lock-outline" size="large" type="text" v-model="dataForm.password" :placeholder="$t('login.password')"/></Col>
              </Row>
            </FormItem>
            <FormItem prop="confirmPassword">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-lock-outline" size="large" type="text" v-model="dataForm.confirmPassword" :placeholder="$t('register.enterPassword')"/></Col>
              </Row>
            </FormItem>
            <FormItem prop="email">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-mail" size="large" type="text" v-model="dataForm.email" :placeholder="$t('register.mailbox')"></Input></Col>
              </Row>
            </FormItem>
            <FormItem prop="mailCode">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-mail" size="large" v-model="dataForm.mailCode"
                                      :placeholder="$t('common.PleaseInput') + $t('register.code')">
                  <div id="sendMailCode" @click="sendCode('email')" slot="append">
                    <a style="cursor: pointer">{{$t('register.getCode')}}</a>
                  </div>
                </Input></Col>
              </Row>
            </FormItem>
            <FormItem  prop="mobile">
              <Row>
                <Col span="1"></Col>
                <Col span="23"><Input prefix="ios-call" size="large" type="text" v-model="dataForm.mobile" :placeholder="$t('register.mobile')"></Input></Col>
              </Row>
            </FormItem>
            <FormItem prop="phoneCode">
              <Row>
                <Col span="1"></Col>
                <Col span="23"><Input prefix="ios-mail" size="large" v-model="dataForm.phoneCode"
                                      :placeholder="$t('common.PleaseInput') + $t('register.code')">
                  <div id="sendPhoneCode" @click="sendCode('phone')" slot="append">
                    <a style="cursor: pointer">{{$t('register.getCode')}}</a>
                  </div>
                </Input></Col>
              </Row>
            </FormItem>
            <!-- <FormItem  prop="imgCode">
            <Row>
              <Col span="16"><Input style="width:300px" prefix="md-photos" size="large" type="text"  v-model="phoneFormItem.imgCode" placeholder="请输入图形验证码"/></Col>
              <Col span="8"><v-verification></v-verification></Col>
            </Row>
          </FormItem> -->
            <FormItem prop="companyId">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.companyId" :placeholder="$t('register.companyId')"></Input></Col>
              </Row>
            </FormItem>

            <div>
              <h2>{{ $t('register.verified') }}</h2>
            </div>
            <FormItem>
              <Tabs  @on-click="handlerTab">
                <TabPane :label="$t('register.company')" icon="md-home" name="company">
                  <Form ref="dataForm" :model="dataForm" :rules="dataRule">
                    <FormItem  prop="companyName">
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"><Input :maxlength="90" prefix="ios-pricetags" size="large" type="text" v-model="dataForm.companyName" :placeholder="$t('register.companyName')"></Input></Col>
                      </Row>
                    </FormItem>
                    <FormItem prop="companyAddress">
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"><Input :maxlength="500" prefix="ios-home" size="large" type="text" v-model="dataForm.companyAddress" :placeholder="$t('register.companyAddress')"></Input></Col>
                      </Row>
                    </FormItem>
                    <FormItem prop="companyPhone" >
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"><Input :maxlength="90" prefix="ios-call" size="large" type="text" v-model="dataForm.companyPhone" :placeholder="$t('register.companyPhone')"></Input></Col>
                      </Row>
                    </FormItem>

                    <FormItem class="upload" label="">
                      <span>{{ $t ('register.companyLicense')}}</span>
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"><Upload
                          :before-upload="handleUploadFirst"
                          :action="''"
                          type="drag"
                          accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
                          <div style="padding: 20px 0">
                            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                            <p>{{$t('file.attachment1')}}</p>
                          </div>
                        </Upload></Col>
                      </Row>

                    </FormItem>
                    <div v-if="dataForm.firstFile!=null">
                      <span>{{dataForm.firstFile.name}}</span>
                      <Icon type="ios-close" size="20" style="float:right;" @click="dataForm.firstFile=null"></Icon>
                    </div>
                    <FormItem class="upload">
                      <span>{{ $t('register.cachet')}}</span>
                      <a v-if="language == 'cn'" :href="wordUrl" download="授权证书.docx">{{ $t('register.DownloadLicenseCertificate') }}</a>
                      <a v-else :href="wordUrlEn" download="Certificate of authorization.docx">{{ $t('register.DownloadLicenseCertificate') }}</a>
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"> <Upload
                          :before-upload="handleUploadSecond"
                          :action="''"
                          type="drag"
                          accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
                          <div style="padding: 20px 0">
                            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                            <p>{{$t('file.attachment1')}}</p>
                          </div>
                        </Upload></Col>
                      </Row>
                    </FormItem>
                    <div v-if="dataForm.secondFile!=null">
                      <span>{{dataForm.secondFile.name}}</span>
                      <Icon type="ios-close" size="20" style="float:right;" @click="dataForm.secondFile=null"></Icon>
                    </div>
                  </Form>
                </TabPane>
                <TabPane :label="$t('register.personal')" icon="ios-person" name="person">
                  <Form ref="dataForm" :model="dataForm" :rules="dataRule">
                    <FormItem prop="cardIdNumber" >
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"><Input prefix="md-contacts" size="large" type="text" v-model="dataForm.idCardNumber" :placeholder="$t('common.PleaseInput')+$t('register.idCardNumber')"></Input></Col>
                      </Row>
                    </FormItem>

                    <FormItem class="upload" label="">
                      <span>{{ $t('register.idCardFront') }}</span>
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23"><Upload
                          :before-upload="handleUploadFirst"
                          :action="''"
                          type="drag"
                          accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
                          <div style="padding: 20px 0">
                            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                            <p>{{$t('file.attachment1')}}</p>
                          </div>
                        </Upload></Col>
                      </Row>
                    </FormItem>
                    <div v-if="dataForm.firstFile!=null">
                      <span>{{dataForm.firstFile.name}}</span>
                      <Icon type="ios-close" size="20" style="float:right;" @click="dataForm.firstFile=null"></Icon>
                    </div>
                    <FormItem class="upload">
                      <span>{{ $t('register.idCardReverse') }}</span>
                      <Row>
                        <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                        <Col span="23">
                          <Upload
                            :before-upload="handleUploadSecond"
                            :action="''"
                            type="drag"
                            accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
                            <div style="padding: 20px 0">
                              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                              <p>{{$t('file.attachment1')}}</p>
                            </div>
                          </Upload>
                        </Col>
                      </Row>
                    </FormItem>
                    <div v-if="dataForm.secondFile!=null">
                      <span>{{dataForm.secondFile.name}}</span>
                      <Icon type="ios-close" size="20" style="float:right;" @click="dataForm.secondFile=null"></Icon>
                    </div>
                  </Form>
                </TabPane>
              </Tabs>
            </FormItem>
            <FormItem>
              <Checkbox v-model="dataForm.check">
                <span>{{$t('register.readAccepted')}}</span>
              </Checkbox>
              <!-- </Tooltip> -->
              <a style="font-weight: bold" href="javascript:void(0)" @click="handleAgreement">《{{$t('register.AIPSAgreement')}}》</a>
            </FormItem>
          </Form>
        </div>
        <Divider dashed />
        <div class="foot" style="margin-top:10px">
          <Button style="width:120px;height: 45px;margin-right:100px" size="large" type="primary" @click="loginPage()">{{$t('register.back')}}</Button>
          <Button style="width:120px;height: 45px;" size="large" :loading="loading" type="primary" @click="submitRegister()">{{$t('register.register')}}</Button>
        </div>
      </div>
      <div v-else>
        <div style="height:70px;"></div>
        <h2>{{$t('register.registeredSuccessfully')}}！</h2>
        <h4 style="color: red">{{$t('common.reviewCurrentUsers')}}</h4>
        <div style="height:130px;"></div>
        <a @click="loginPage()"><h3>{{$t('register.clickJumpOr')}} {{timeGoToLogin}} s {{$t('register.loginDisplayed')}}</h3></a>
      </div>
    </div>
  </div>

</template>
<script>
// import verification from '../common/verification' // 引入验证码组件
import { isEmail, isMobile, isSixCode, isNumOrLetter, PWDLenght ,isIdCardNumber} from '@/utils/validate'
import $ from 'jQuery'
export default {
  data () {
    return {
      wordUrl: require('@/assets/auth.docx'),
      wordUrlEn: require('@/assets/authEn.docx'),
      loading: false,
      timerGoToLogin: null,
      timeGoToLogin: 3,
      timerEmail: null,
      // 邮箱部分
      timerPhone: null,
      countdownEmail: 60,
      countdownPhone: 60,
      //
      phoneMail: true,
      current: 0,
      documentClientHeight: 0,
      dataForm: {
        username: '',
        password: '',
        confirmPassword: '',
        mobile: '',
        phoneCode: '',
        email: '',
        mailCode: '',
        check: false,
        companyId: '',
        companyName: '',
        companyAddress: '',
        companyPhone: '',
        //第一张上传的图片
        firstFile:null,
        //第二张上传的图片
        secondFile:null,
        //身份证号
        idCardNumber:'',
        //是公司还是个人，1 公司、2 个人
        isCompany:1,
      },
      custom: '',
    }
  },
  computed: {
    language: {
      get() { return this.$store.state.language.language },
    },
    dataRule: {
      get () {
        return {
          username: [
            { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isNumOrLetter(value)) {
                callback(new Error(this.$t('login.user4To17')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          password: [
            { required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!PWDLenght(value)) {
                callback(new Error(this.$t('login.passwordMore8')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          confirmPassword: [
            { validator: (rule, value, callback) => {
              if (!/\S/.test(value)) {
                callback(new Error(this.$t('validate.confirm_password_cannot_empty')))
              } else if (this.dataForm.password !== value) {
                callback(new Error(this.$t('validate.the_password_is_inconsistent')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          mobile: [
            { validator: (rule, value, callback) => {
              if (value) {
                if (!isMobile(value)) {
                  callback(new Error(this.$t('validate.mobile_format')))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          phoneCode: [
            { validator: (rule, value, callback) => {
              if (value) {
                if (!isSixCode(value)) {
                  callback(new Error(this.$t('validate.code_format')))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          email: [
            { required: true, message: this.$t('validate.email_cannot_empty'), trigger: 'blur' },
            // { type: 'email', message: this.$t('validate.incorrect_email_format'), trigger: 'blur' }
          ],
          mailCode: [
            { required: true, message: this.$t('validate.email_code_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isSixCode(value)) {
                callback(new Error(this.$t('validate.code_format')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          companyId: [
            { required: true, message: this.$t('validate.company_id_empty'), trigger: 'blur' }
          ],
          companyAddress: [
            { required: true, message: this.$t('validate.company_address_cannot_empty'), trigger: 'blur' }
          ],
          companyName: [
            { required: true, message: this.$t('validate.company_name_cannot_empty'), trigger: 'blur' }
          ],
          companyPhone: [
            { required: true, message: this.$t('validate.company_phone_number_cannot_empty'), trigger: 'blur' }
          ],
          idCardNumber: [
            { required: true, message: this.$t('validate.company_phone_number_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
                if (!isIdCardNumber(value)) {
                  callback(new Error(this.$t('validate.id_card_number_format_wrong')))
                } else {
                  callback()
                }
              }, trigger: 'blur' }
          ]
        }
      }
    }
  },
  methods: {
    // 重置窗口可视高度
    resetDocumentClientHeight () {
      this.documentClientHeight = document.documentElement['clientHeight']
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
      }
    },
    // continueToVerified(){
    //   this.$refs['dataForm'].validate((valid) => {
    //     if (valid) {
    //       if (this.dataForm.check){
    //         this.current=1;
    //       }else {
    //         this.$Message.error(this.$t('register.pleaseReadAndCheckAIPS'))
    //       }
    //     }
    //   })
    // },
    front(){
      this.current=0;
    },
    handlerTab(name){
      if (name==='company'){
        this.dataForm.isCompany=1;
      }else {
        this.dataForm.isCompany=2
      }
    },

    // 文件1上传前
    handleUploadFirst (selectFile) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      if(ele === "image"){
        if(ele === "image" && selectFile.size > (10 * 1024 *1024)){
          this.$Message.error(this.$t('screen.picture')+'：'+selectFile.name +this.$t('screen.sizeMore')+ '10M!')
          this.dataForm.firstFile = null //超过大小将文件清空
          return false
        }
      } else {
        this.$Message.error(this.$t('screen.picturesOrVideos'))
        this.dataForm.firstFile = null //将文件清空
        return false
      }
      this.dataForm.firstFile=selectFile
      return false
    },
    // 文件2上传前
    handleUploadSecond (selectFile) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      if(ele === "image"){
        if(ele === "image" && selectFile.size > (10 * 1024 *1024)){
          this.$Message.error(this.$t('screen.picture')+'：'+selectFile.name +this.$t('screen.sizeMore')+ '10M!')
          this.dataForm.secondFile = null //超过大小将文件清空
          return false
        }
      } else {
        this.$Message.error(this.$t('screen.picturesOrVideos'))
        this.dataForm.secondFile = null //将文件清空
        return false
      }
      this.dataForm.secondFile=selectFile
      return false
    },
    submitRegister () { // 注册
     this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.check === true) {
            if (this.dataForm.firstFile&&this.dataForm.secondFile){
              let formData = new FormData();
              formData.append("user",JSON.stringify(this.dataForm) );
              formData.append("firstFile",this.dataForm.firstFile);
              formData.append("secondFile",this.dataForm.secondFile);
              this.loading = true
              this.$http({
                url: this.$http.adornUrl('/sys/register'),
                method: 'post',
                headers: {'Content-Type': undefined},
                data:formData
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.current += 1
                  this.timeGoToLogin = 3
                  this.timerGoToLogin = setInterval(() => { this.timeJS() }, 1000)
                } else {
                  // if (this.userInfo.iconUrl) {
                  //   if (this.dataForm.iconUrl.substr(0, 6) !== 'aips40') {
                  //     this.dataForm.iconUrl = 'custom'
                  //   }
                  // }
                  this.$Message.error({
                    content: data.msg,
                    onClose: () => {
                      setTimeout(() => {
                        this.loading = false
                      }, 500)
                    }
                  })
                }
              })
            }else {
              this.$Message.error({
                content: this.$t('userAuth.pleaseUploadEnterpriseLicenseOfficialSeal'),
                onClose: () => {
                  setTimeout(() => {
                    this.loading = false
                  }, 500)
                }
              })
            }
          } else {
            this.$Message.error(this.$t('register.readAcceptAIPSAgreement'))
          }
        }
      })
    },
    timeJS () {
      this.timeGoToLogin -= 1
      if (this.timeGoToLogin <= 0) {
        this.loginPage()
      }
    },
    // 登录页面跳转
    loginPage () {
      this.$router.replace({ name: 'login' })
      this.timeGoToLogin = 3
      clearInterval(this.timerGoToLogin)
      this.timerGoToLogin = null
      this.current = 0
    },
    // 获取验证码
    sendCode (type) {
      if (type === 'email') {
        if (isEmail(this.dataForm.email)) {
          this.$http({
            // url: this.$http.adornUrl(`/sms/email/sendCode?email=${this.dataForm.email}`),
            url: this.$http.adornUrl(`/sms/email/sendCode`),
            method: 'post',
            data: this.$http.adornData({
              "email": this.$encruption(this.dataForm.email)
            })
          }).then(({data}) => {
            if (data && data.code !== 0) {
              this.$Message.error(data.msg)
              this.clearTimerEmail()
            }
          })
          this.timeoutChangeStyleEmail()
        } else {
          this.$Message.error(this.$t('validate.incorrect_email_format'))
        }
      } else {
        if (isMobile(this.dataForm.mobile)) {
          this.$http({
            // url: this.$http.adornUrl(`/sms/mobile/sendCode?mobile=${this.dataForm.mobile}`),
            url: this.$http.adornUrl(`/sms/mobile/sendCode`),
            method: 'post',
            data: this.$http.adornData({
              mobile: this.$encruption(this.dataForm.mobile)
            })
          }).then(({data}) => {
            if (data && data.code !== 0) {
              this.$Message.error(data.msg)
              this.clearTimerPhone()
            }
          })
          this.timeoutChangeStylePhone()
        } else {
          this.$Message.error(this.$t('validate.mobile_format'))
        }
      }
    },
    // 邮箱验证码
    timeoutChangeStyleEmail () {
      // 启动定时器
      this.loadingTimerEmail()
      this.timerEmail = setInterval(() => {
        // 创建定时器
        if (this.countdownEmail === 0) {
          this.clearTimerEmail() // 关闭定时器
        } else {
          this.loadingTimerEmail()
        }
      }, 1000)
    },
    loadingTimerEmail () {
      var str = `<div>${this.$t('register.prependResend')} ${this.countdownEmail} ${this.$t('register.appendResend')} </div>`
      $('#sendMailCode').html(str)
      this.countdownEmail-- // 定时器减1
    },
    clearTimerEmail () {
      // 清除定时器
      clearInterval(this.timerEmail)
      this.timerEmail = null
      this.countdownEmail = 60
      $('#sendMailCode').html(`<a style="cursor: pointer">${this.$t('register.getCode')}</a>`)
    },
    // 手机号验证码
    timeoutChangeStylePhone () {
      // 启动定时器
      this.loadingTimerPhone()
      this.timerPhone = setInterval(() => {
        // 创建定时器
        if (this.countdownPhone === 0) {
          this.clearTimerPhone() // 关闭定时器
        } else {
          this.loadingTimerPhone()
        }
      }, 1000)
    },
    loadingTimerPhone () {
      var str = `<div>${this.$t('register.prependResend')} ${this.countdownPhone} ${this.$t('register.appendResend')} </div>`
      $('#sendPhoneCode').html(str)
      this.countdownPhone-- // 定时器减1
    },
    clearTimerPhone () {
      // 清除定时器
      clearInterval(this.timerPhone)
      this.timerPhone = null
      this.countdownPhone = 60
      $('#sendPhoneCode').html(`<a style="cursor: pointer">${this.$t('register.getCode')}</a>`)
    },
    // 重置数据
    resetData () {
      if (this.$route.params.dataForm !== undefined) {
      this.dataForm = this.$route.params.dataForm
      }
      if (this.$route.params.check !== undefined) {
        this.dataForm.check = !this.$route.params.check
      }
      if (this.$route.params.phoneMail !== undefined) {
        this.phoneMail = this.$route.params.phoneMail
      }
    },
    // 跳转到协议页面
    handleAgreement () {
      //  通过computed属性获取
      const currentLanguage = this.language
      if(currentLanguage==='cn'){
      this.$router.replace({ name: 'agreement' , params: {dataForm: this.dataForm,phoneMail: this.phoneMail } })
      }else{
        this.$router.replace({ name: 'agreementEn' , params: {dataForm: this.dataForm,phoneMail: this.phoneMail } })
      }
      if (this.timerPhone) {
        this.clearTimerPhone()
      }
      if (this.timerEmail) {
        this.clearTimerEmail()
      }
    }
  },
  mounted () {
    this.resetDocumentClientHeight()
  },
  created () {
    this.resetData()
  },
}
</script>

<style scoped>
.ivu-steps .ivu-steps-title {
    display: inline-block;
    margin-bottom: 4px;
    padding-right: 10px;
    font-size: 14px;
    font-weight: 700;
    color: #666;
    background: rgba(255, 255, 255, 0.719);
}
.ivu-form-item {
  margin-bottom:20px;
}
.maxnbox{
  background-image: url(../../assets/img/register_background.png);
  background-repeat: repeat-x;
  background-size: 100% 100%;
  overflow: hidden;
}
.login-wrap {
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.719);
  width: 720px;
  box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.2);
  border-radius: 1%;
  overflow-x: hidden;
  overflow-y: auto;
}
/*.content {*/
/*  margin: 0 auto;*/
/*  text-align: center;*/
/*  width: 80%;*/
/*}*/
.content {
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 5px;
}

.login-head {
  text-align: center;
  height: 30px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.login-title {
  color: #6f7072;
  padding: 5px;
  font-weight: 600;
  text-shadow: 0 1px 0 #fff;
  font-size: 26px;
}
.steps {
  text-align: left;
}
</style>
