<template>
  <div class="page-not-fond">
    <h2 class="not-found-title">404</h2>
    <p class="not-found-desc">{{$t('sys.visited')}}<em>{{$t('sys.find')}}</em>，{{$t('sys.url')}}</p>
    <Button @click="$router.go(-1)">{{$t('sys.previousPage')}}</Button>
    <Button type="primary" class="not-found-btn-gohome" @click="$router.push({ name: 'home' })">{{$t('sys.enterHome')}}</Button>
  </div>
</template>

<script>
export default {
}
</script>

<style scoped>
  .page-not-fond {
    position: fixed;
    top: 15%;
    left: 50%;
    z-index: 2;
    padding: 30px;
    text-align: center;
    transform: translate(-50%, 0);
  }
  .not-found-title {
    margin: 20px 0 15px;
    font-size: 10em;
    font-weight: 400;
    color: rgb(55, 71, 79);
  }
  .not-found-desc {
    margin: 0 0 30px;
    font-size: 26px;
    text-transform: uppercase;
    color: rgb(118, 131, 143);
  }
  .not-found-desc em {
    font-style: normal;
    color: #ee8145;
  }
  .not-found-btn-gohome {
    margin-left: 30px;
  }
</style>
