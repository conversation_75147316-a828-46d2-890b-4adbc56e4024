<template>
  <div class="maxnbox" :style="{ 'height': documentClientHeight + 'px' }">
    <div class="password-wrap">
      <div class="head">
        <span class="title">{{$t('resetPWD.resetPassword')}}</span>
      </div>
      <div v-if="!isVerify">
        <Steps style="text-align: left;" :current="current">
          <Step :title="$t('resetPWD.accountNumber')"></Step>
          <Step :title="$t('resetPWD.repairMethod')"></Step>
          <Step :title="$t('resetPWD.changePassword')"></Step>
          <Step :title="$t('resetPWD.success')"></Step>
        </Steps>
        <div v-if="current === 0">
          <Form style="text-align: left;margin-top: 50px;" :model="formReset" label-position="top" ref="formReset" :rules="formResetRules" @keydown.native.enter.prevent ="()=>{}">
            <FormItem :label="$t('resetPWD.enterResetPassword')" prop="username" style="color: red">
              <Input v-model="formReset.username" size="large" type="text" :placeholder="$t('login.username')">
              </Input>
            </FormItem>
            <FormItem style="text-align: center;margin-top: 70px;">
              <Button style="width:120px;height: 45px;margin-right:100px" size="large" type="primary" @click="$router.replace({ name: 'login' })">{{$t('register.back')}}</Button>
              <Button style="width:120px;height: 45px;" size="large" :loading="loading" type="primary" @click="submitFindUser()">{{$t('common.nextStep')}}</Button>
            </FormItem>
          </Form>
        </div>
        <div v-else-if="current === 1">
          <Alert type="success" show-icon style="margin-top: 10px;">
            <span slot="desc">{{$t('register.youAreRight')}}<b style="color:#ff9900;cursor: default;">&nbsp;{{resData.username}}&nbsp;</b>
          {{$t('register.resettingVerification')}}：
            <div style="color:#0000cc;cursor: pointer;" v-if="resData.email && resData.mobile === 0"
            @click="formCode.flag === 0 ? formCode.flag = 1 : formCode.flag = 0">{{$t('register.switchingAuthentication')}}</div></span>
          </Alert>
          <Form style="text-align: left;margin-top: 40px;" :model="formCode" label-position="top" ref="formCode" :rules="formCodeRules" @keydown.native.enter.prevent ="()=>{}">
            <FormItem :label="formCode.flag === 0 ?  `${$t('login.clickCodeMailbox')}${resData.email} `: `${$t('login.clickCodePhone')}${resData.mobile}`" 
            prop="code">
              <Row>
                <Col span="16">
                  <Input prefix="ios-mail" size="large" v-model="formCode.code" type="text"
                  :placeholder="$t('common.PleaseInput') + $t('register.code')">
                  </Input>
                </Col>
                <Col span="6">
                  <Button style="margin-left: 5px" size="large" @click="sendCode" id="sendCode"><div>{{$t('register.getCode')}}</div></Button>
                </Col>
              </Row>
            </FormItem>
            <FormItem style="text-align: center;margin-top: 10px;">
              <Button style="height: 45px;" long size="large" :loading="loading" type="primary" @click="submitCode()">{{$t('common.nextStep')}}</Button>
            </FormItem>
          </Form>
        </div>
        <div v-else-if="current === 2">
          <Alert type="success" show-icon style="margin-top: 10px;">
            <span slot="desc">{{$t('register.pleaseSet')}} <b style="color:#ff9900;cursor: default;">&nbsp;{{resData.username}}&nbsp;</b>
            {{$t('register.passwordSecurity')}}
            </span>
          </Alert>
          <Form style="text-align: left;margin-top: 20px;" :model="formPassword" ref="formPassword" :rules="formPasswordRules" @keydown.native.enter.prevent ="()=>{}">
            <FormItem prop="password">
              <Input prefix="ios-lock-outline" size="large" type="password" password v-model="formPassword.password" :placeholder="$t('login.password')"/>
            </FormItem>
            <FormItem style="text-align: center;margin-top: 10px;">
              <Button style="height: 45px;" long size="large" :loading="loading" type="primary" @click="submitPassword()">{{$t('common.nextStep')}}</Button>
            </FormItem>
          </Form>
        </div>
        <div v-else>
        <div style="height:70px;"></div>
            <h2>{{$t('register.passwordChangedSuccessfully')}}</h2>
            <div style="height:50px;"></div>
            <a @click="loginPage()"><h3>{{$t('register.clickJumpOr')}} {{timeGoToLogin}} s {{$t('register.loginDisplayed')}}</h3></a>
        </div>
      </div>
      <div v-else-if="isVerify">
        <span style="color: red; font-size: 18px">{{$t('login.pleaseContactTheAdministrator')}}</span>
        <a @click="loginPage()"><h3>{{$t('register.back')}}</h3></a>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jQuery'
import { isNumOrLetter, isSixCode, PWDLenght } from '@/utils/validate'
export default {
  data () {
    var validateNumOrLetter = (rule, value, callback) => {
      if (!isNumOrLetter(value)) {
        callback(new Error(this.$t('login.user4To17')))
      } else {
        callback()
      }
    }
    var validateSixCode = (rule, value, callback) => {
      if (!isSixCode(value)) {
        callback(new Error(this.$t('validate.code_format')))
      } else {
        callback()
      }
    }
    var validatePWD = (rule, value, callback) => {
      if (!PWDLenght(value)) {
        callback(new Error(this.$t('login.passwordMore8')))
      } else {
        callback()
      }
    }
    return {
      documentClientHeight: 0,
      current: 0,
      formReset: {
        username: ''
      },
      formResetRules: {
        username: [
          { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' },
          { validator: validateNumOrLetter, trigger: 'blur' }
        ],
      },
      loading: false,
      resData: {},
      formCode: {
        code: '',
        flag: 0// 表示使用哪种方式验证 0 邮箱 1 短信 默认邮箱
      },
      formCodeRules: {
        code: [
          { required: true, message: this.$t('validate.code_cannot_empty'), trigger: 'blur' },
          { validator: validateSixCode, trigger: 'blur' }
        ]
      },
      countdown: 60,
      timer: null,
      formPassword: {
        password: ''
      },
      formPasswordRules: {
        password: [
          { required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur' },
          { validator: validatePWD, trigger: 'blur' }
        ]
      },
      timerGoToLogin: null,
      timeGoToLogin: 3,
      isVerify: window.SITE_CONFIG.isVerify
    }
  },
  methods: {
    // 重置窗口可视高度
    resetDocumentClientHeight () {
      this.documentClientHeight = document.documentElement['clientHeight']
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
      }
    },
    resetData () {
      this.current = 0
    },
    // 查询用户
    submitFindUser () {
      this.$refs['formReset'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(`/sys/findUser/${this.formReset.username}`),
            method: 'get',
            data: this.$http.adornData()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.resData = data.resData
              this.current = 1
              this.loading = false
              // 表示使用哪种方式验证 0 邮箱 1 短信 默认邮箱，优先邮箱
              if (this.resData.mobile) {
                this.formCode.flag = 1
              } else if (this.resData.email) {
                this.formCode.flag = 0
              }
            } else {
              this.$Message.error(data.msg)
              this.loading = false
            }
          })
        }
      })
    },
    sendCode () {
      this.$http({
        url: this.$http.adornUrl(`/sms/emailOrMobile/sendCode?username=${this.resData.username}&flag=${this.formCode.flag}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code !== 0) {
          this.$Message.error(data.msg)
          this.clearTimer()
        }
      })
      this.timeoutChangeStyle()
    },
    timeoutChangeStyle () {
      // 启动定时器
      this.loadingTime()
      this.timer = setInterval(() => {
        // 创建定时器
        if (this.countdown === 0) {
          this.clearTimer() // 关闭定时器
        } else {
          this.loadingTime()
        }
      }, 1000)
    },
    loadingTime () {
      // 启动定时器
      var str = `<div>${this.$t('register.prependResend')} ${this.countdown} ${this.$t('register.appendResend')} </div>`
      $('#sendCode').html(str)
      this.countdown-- // 定时器减1
    },
    clearTimer () {
      // 清除定时器
      clearInterval(this.timer)
      this.timer = null
      this.countdown = 60
      $('#sendCode').html(`<div>${this.$t('register.getCode')}</div>`)
    },
    // 提交验证码
    submitCode () {
      this.$refs['formCode'].validate((valid) => {
        if (valid) {
          this.current = 2
          this.clearTimer()
        }
      })
    },
    submitPassword () {
      this.$refs['formPassword'].validate((valid) => {
        if (valid) {
           this.$http({
            url: this.$http.adornUrl('/sys/updatePWD'),
            method: 'post',
            data: this.$http.adornData({
              'code': this.formCode.code,
              'password': this.$encruption(this.formPassword.password),
              'username': this.resData.username,
              'flag': this.formCode.flag
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.current = 3
              this.timeGoToLogin = 3
              this.timerGoToLogin = setInterval(() => { this.timeJS() }, 1000)
            } else {
              this.$Message.error(data.msg)
              this.current = 1
              this.formCode.code = ''
              this.formPassword.password = ''
            }
          })
        }
      })
    },
    // 登录页面跳转
    loginPage () {
      this.$router.replace({ name: 'login' })
      this.timeGoToLogin = 3
      clearInterval(this.timerGoToLogin)
      this.timerGoToLogin = null
      this.current = 0
      this.clearTimer()
    },
    timeJS () {
      this.timeGoToLogin -= 1
      if (this.timeGoToLogin <= 0) {
        this.loginPage()
      }
    },

  },
  mounted () {
    this.resetDocumentClientHeight()
  },
  created () {
    this.resetData()
  }
}
</script>
<style scoped>
  .maxnbox{
    background-image: url(../../assets/img/register_background.png);
    background-repeat: repeat-x;
    background-size: 100% 100%;
    overflow: hidden;
  }
  .password-wrap {
    position: absolute;
    left:50%;
    top:50%;
    transform: translate(-50%, -50%);
    text-align: center;
    height: 400px;
    background: rgba(255, 255, 255, 0.7);
    width: 45%;
    padding: 20px;
    margin:0 auto;
    border-radius: 1%;
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.2);
  }
  .head {
    height: 50px;
  }
  .title {
    color: #6f7072;
    padding: 8px;
    font-weight: 600;
    text-shadow: 0 1px 0 #fff;
    font-size: 20px;
  }
</style>

