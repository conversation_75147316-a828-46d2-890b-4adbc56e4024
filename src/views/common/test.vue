<template>
    <div>
        <!-- <div class="wfsjs"> -->
            <video id="videoElement"  width="640" autoplay muted height="480"></video>
            <!-- <video id="videoElement1"  width="640" autoplay muted height="480"></video>  -->
            <!-- <iframe allow="autoplay" style="width:640px; height:480px; display:none"></iframe> -->
            <!-- <img style="height: 480px; width: 640px" id="show_video" src=""> -->
            <!-- src="http://vjs.zencdn.net/v/oceans.mp4" -->
            <!-- <canvas id="videoElement"></canvas> -->
        <!-- </div> -->
    </div>
</template>
<script>
import $ from 'jQuery'
import flvjs from 'flv.js'
export default {
    data() {
        return {
            // src: "https://mister-ben.github.io/videojs-flvjs/bbb.flv",
            src: "*************:8088/websocket/123456",
        }
    },
    methods: {
        playVideo() {
            let videoWin = document.getElementById("videoElement");
            if (flvjs.isSupported()) {
                const flvPlayer = flvjs.createPlayer({
                    type: "flv",
                    //是否是实时流
                    isLive: true,
                    //是否有音频
                    hasAudio: false,
                    url: "http://*************:8888/live?port=1935&app=live&stream=123456",
                    enableStashBuffer: true,
                });
                flvPlayer.on("error", (err) => {
                    console.log("err", err);
                });
                flvPlayer.attachMediaElement(videoWin);
                flvPlayer.load();
                flvPlayer.play();
            }


            // let canvas = document.getElementById('videoElement')
            // let url = 'ws://*************:8088/websocket/123456'
            // let player = new JSMpeg.Player('../../assets/aa.ts', {canvas: canvas})


            // var ws = new WebSocket("ws://*************:8088/websocket/123456")
            // // 建立 web socket 连接成功触发事件
            // ws.onopen = function () {
            //     var json= '{"t":"open","c":"ch1","v":"NA"}'
            //     ws.send(json);//可以给后台发送参数
            // };
            // //接收到消息的回调方法
            // ws.onmessage = function (event) {
            //     // alert('数据回来了额'+event.data)
            //     // console.log(event.data);//后台不间断发送数据，持续接收。
            //     var data = JSON.parse(event.data);
            //     if (data.code === 0) {
            //         console.log(event)
            //     } else if (data.code === 201) {
            //         $("#show_video").attr("src", "data:image/*;base64," + data.data)
            //     }

            // }
            // //断开 web socket 连接成功触发事件
            // ws.onclose = function () {
            //     alert("连接已关闭...");
            // };



            // if (Wfs.isSupported()) {
            //     var videoElement = document.getElementById("videoElement1")
            //     var wfs = new Wfs({url: this.src});
            //     wfs.attachMedia(videoElement,'ch1');
            // }
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.playVideo(); //视频加载
        });

    },
}
</script>
