<template>
  <div class="maxnbox" :style="{ 'height': documentClientHeight + 'px' }">
    <div class="login-wrap">
      <div class="login-head">
        <span class="login-title">{{$t('register.register')}}</span>
      </div>
        <Divider />
      <div v-if="current===0">
        <div class="content">
          <div>
            <h2>{{$t('register.personalInformation')}}</h2>
          </div>
          <Form ref="dataForm" :model="dataForm" :rules="dataRule">
            <FormItem prop="username">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-contact" size="large" type="text" v-model="dataForm.username" :placeholder="$t('login.username')"/></Col>
              </Row>
            </FormItem>
            <FormItem prop="password">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-lock-outline" size="large" type="text" v-model="dataForm.password" :placeholder="$t('login.password')"/></Col>
              </Row>
            </FormItem>
            <FormItem prop="confirmPassword">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-lock-outline" size="large" type="text" v-model="dataForm.confirmPassword" :placeholder="$t('register.enterPassword')"/></Col>
              </Row>
            </FormItem>
            <FormItem prop="email">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-mail" size="large" type="text" v-model="dataForm.email" :placeholder="$t('register.mailbox')"></Input></Col>
              </Row>
            </FormItem>
            <FormItem prop="mailCode">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-mail" size="large" v-model="dataForm.mailCode"
                                      :placeholder="$t('common.PleaseInput') + $t('register.code')">
                  <div id="sendMailCode" @click="sendCode('email')" slot="append">
                    <a style="cursor: pointer">{{$t('register.getCode')}}</a>
                  </div>
                </Input></Col>
              </Row>
            </FormItem>
            <FormItem prop="companyId">
              <Row>
                <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.companyId" :placeholder="$t('register.companyId')"></Input></Col>
              </Row>
            </FormItem>
            <div style="display: none;">
              <div>
                <h2>{{ $t('nav.订单') }}</h2>
              </div>
              <FormItem prop="vipStatus">
                <Row>
                  <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                  <Col span="23" style="text-align: left;">
                    <RadioGroup v-model="dataForm.vipStatus">
                      <Radio v-for="(item, index) in vipList" :label="item.label" :key="index">
                        <Tooltip placement="bottom" style="width: 23px; height: 23px;">
                            <img style="vertical-align: middle;" :src="item.src" :width="item.default ? '20' : '23'" :height="item.default ? '20' : '23'">
                            <template #content>
                              {{ $t(item.title) }}
                            </template>
                        </Tooltip>
                      </Radio>
                    </RadioGroup> 
                  </Col>
                </Row>
              </FormItem>
              <FormItem prop="vipMonth">
                <Row>
                  <Col span="1"><Icon style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                  <Col span="23" style="text-align: left;">
                    <InputNumber style="width: 120px" :min="1" size="large" type="text" v-model="dataForm.vipMonth"/>
                    <span style="margin-left: 5px;">{{$t('pay.vipDuration')}}</span>
                  </Col>
                </Row>
              </FormItem>
              <FormItem>
                <Row>
                  <Col span="1"></Col>
                  <Col span="23"  style="color:#67C23A;text-align: left;">
                    <span v-if="dataForm.vipStatus == 1"> {{$t('pay.100ControlCards')}} </span>
                    <span v-else-if="dataForm.vipStatus == 2"> {{$t('pay.500ControlCards')}} </span>
                    <span v-else-if="dataForm.vipStatus == 3"> {{$t('pay.NumberOfControlCards1500')}} </span>
                    <span v-else-if="dataForm.vipStatus == 4"> ∞ </span>
                  </Col>
                </Row>
              </FormItem>
              <FormItem prop="vipCompanyName">
                <Row>
                  <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                  <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.vipCompanyName" :placeholder="$t('pay.companyName')"></Input></Col>
                </Row>
              </FormItem>
              <FormItem prop="vipAddress">
                <Row>
                  <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                  <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.vipAddress" :placeholder="$t('pay.address')"></Input></Col>
                </Row>
              </FormItem>
              <FormItem prop="vipContactPerson">
                <Row>
                  <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                  <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.vipContactPerson" :placeholder="$t('pay.contactPerson')"></Input></Col>
                </Row>
              </FormItem>
              <FormItem prop="vipTelephone">
                <Row>
                  <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
                  <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.vipTelephone" :placeholder="$t('pay.telephone')"></Input></Col>
                </Row>
              </FormItem>
              <FormItem>
                <Row>
                  <Col span="1"></Col>
                  <Col span="23"  style="text-align: left;">
                    <span> {{$t('pay.price')}}: </span>
                    <span v-if="dataForm.vipStatus == 1"> {{800 | currency}}</span>
                    <span v-else-if="dataForm.vipStatus == 2"> {{1600 | currency}}</span>
                    <span v-else-if="dataForm.vipStatus == 3"> {{2600 | currency}}</span>
                    <span v-else-if="dataForm.vipStatus == 4"> {{5000 | currency}}</span>
                    <span style="margin-left:8px;">{{$t('pay.totalPrice')}}:</span>
                    <span v-if="dataForm.vipStatus == 1"> {{800 * dataForm.vipMonth | currency}}</span>
                    <span v-else-if="dataForm.vipStatus == 2"> {{1600 * dataForm.vipMonth | currency}}</span>
                    <span v-else-if="dataForm.vipStatus == 3"> {{2600 * dataForm.vipMonth | currency}}</span>
                    <span v-else-if="dataForm.vipStatus == 4"> {{5000 * dataForm.vipMonth | currency}}</span>
                    <!-- <img width="23" height="23" style="margin-left: 5px;vertical-align: middle;" src="@/assets/img/hot.png">
                    <span style="color: #E6A23C"> {{ '活动期间加赠一个月' }} </span> -->
                  </Col>
                </Row>
              </FormItem>
            </div>
            <FormItem>
              <Checkbox v-model="dataForm.check">
                <span>{{$t('register.readAccepted')}}</span>
              </Checkbox>
              <a style="font-weight: bold" href="javascript:void(0)" @click="handleAgreement">《{{$t('register.AIPSAgreement')}}》</a>
            </FormItem>
          </Form>
        </div>
        <Divider dashed />
        <div class="foot" style="margin-top:10px">
          <Button style="width:120px;height: 45px;margin-right:100px" size="large" type="primary" @click="loginPage()">{{$t('register.back')}}</Button>
          <Button style="width:120px;height: 45px;" size="large" :loading="loading" type="primary" @click="submitRegister()">{{$t('register.register')}}</Button>
        </div>
      </div>
      <div v-else>
        <div style="height:70px;"></div>
        <h2>{{$t('register.registeredSuccessfully')}}！</h2>
        <h4 style="color: red">{{ $t('pay.tips') }}</h4>
        <div style="height:130px;"></div>
        <a @click="loginPage()"><h3>{{$t('register.clickJumpOr')}} {{timeGoToLogin}} s {{$t('register.loginDisplayed')}}</h3></a>
      </div>
    </div>
  </div>

</template>
<script>
// import verification from '../common/verification' // 引入验证码组件
import { isEmail, isSixCode, isNumOrLetter, PWDLenght} from '@/utils/validate'
import $ from 'jQuery'
export default {
  data () {
    return {
      wordUrl: require('@/assets/auth.docx'),
      wordUrlEn: require('@/assets/authEn.docx'),
      loading: false,
      timerGoToLogin: null,
      timeGoToLogin: 3,
      timerEmail: null,
      // 邮箱部分
      countdownEmail: 60,
      phoneMail: true,
      current: 0,
      documentClientHeight: 0,
      dataForm: {
        username: '',
        password: '',
        confirmPassword: '',
        email: '',
        mailCode: '',
        check: false,
        companyId: '',
        vipStatus: '1', // vip类型默认为1。分为1：银卡VIP1，2：金卡VIP2，3：钻石VIP3，4：极品砖石VIP4。
        vipMonth: 1, // vip时长默认为1年
        vipCompanyName: "", // 公司名称
        vipAddress: "", // 公司地址
        vipContactPerson: "", // 联系人
        vipTelephone:"", // 联系电话
      },
      vipList: [
        {label: "1",title: "pay.silverCardVip",src: require('@/assets/img/VIP1.png'), isDefault: true},
        {label: "2",title: "pay.goldCardVip",src: require('@/assets/img/VIP2.png'), isDefault: true},
        {label: "3",title: "pay.diamondVip",src: require('@/assets/img/VIP3.png'), isDefault: false},
        {label: "4",title: "pay.vip4",src: require('@/assets/img/VIP4.png'), isDefault: false}
      ]
    }
  },
  filters: {
    currency(value) {
        if (!value) return '$0.00';
        // 将值转换为字符串并移除前导零
        const stringValue = value.toString().replace(/^0+/, '');
        // 将值分割为组，每三位添加一个逗号
        const groups = stringValue.split('.');
        const integerPart = groups[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        const decimalPart = groups.length > 1 ? '.' + groups[1] : '';
        // 返回格式化后的字符串
        return '$' + integerPart + decimalPart;
    }
  },
  computed: {
    language: {
      get() { return this.$store.state.language.language },
    },
    dataRule: {
      get () {
        return {
          username: [
            { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isNumOrLetter(value)) {
                callback(new Error(this.$t('login.user4To17')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          password: [
            { required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!PWDLenght(value)) {
                callback(new Error(this.$t('login.passwordMore8')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          confirmPassword: [
            { validator: (rule, value, callback) => {
              if (!/\S/.test(value)) {
                callback(new Error(this.$t('validate.confirm_password_cannot_empty')))
              } else if (this.dataForm.password !== value) {
                callback(new Error(this.$t('validate.the_password_is_inconsistent')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          email: [
            { required: true, message: this.$t('validate.email_cannot_empty'), trigger: 'blur' },
            // { type: 'email', message: this.$t('validate.incorrect_email_format'), trigger: 'blur' }
          ],
          mailCode: [
            { required: true, message: this.$t('validate.email_code_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isSixCode(value)) {
                callback(new Error(this.$t('validate.code_format')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          companyId: [
            { required: true, message: this.$t('validate.company_id_empty'), trigger: 'blur' }
          ],
          /*vipStatus: [
            { required: true, message: 'VIP类型不能为空', trigger: 'blur' }
          ],
          vipMonth: [
            { required: true, message: 'VIP年限不能为空', trigger: 'blur' }
          ],
           vipCompanyName: [
            { required: true, message: '公司名称不能为空', trigger: 'blur' } 
          ],
          vipAddress: [
            { required: true, message: '公司地址不能为空', trigger: 'blur' }
          ],
          vipContactPerson: [
            { required: true, message: '联系人不能为空', trigger: 'blur' }
          ],
          vipTelephone: [
            { required: true, message: '联系电话不能为空', trigger: 'blur' } 
          ] */
        }
      }
    }
  },
  methods: {
    // 重置窗口可视高度
    resetDocumentClientHeight () {
      this.documentClientHeight = document.documentElement['clientHeight']
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
      }
    },
    submitRegister () { // 注册
     this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.check === true) {
            this.loading = true
            this.$http({
              url: this.$http.adornUrl('/sys/registerVIP'),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.current += 1
                this.timeGoToLogin = 3
                this.timerGoToLogin = setInterval(() => { this.timeJS() }, 1000)
              } else {
                this.$Message.error({
                  content: data.msg,
                  onClose: () => {
                    setTimeout(() => {
                      this.loading = false
                    }, 500)
                  }
                })
              }
            })
          } else {
            this.$Message.error(this.$t('register.readAcceptAIPSAgreement'))
          }
        }
      })
    },
    timeJS () {
      this.timeGoToLogin -= 1
      if (this.timeGoToLogin <= 0) {
        this.loginPage()
      }
    },
    // 登录页面跳转
    loginPage () {
      this.$router.replace({ name: 'login' })
      this.timeGoToLogin = 3
      clearInterval(this.timerGoToLogin)
      this.timerGoToLogin = null
      this.current = 0
    },
    // 获取验证码
    sendCode (type) {
      if (type === 'email') {
        if (isEmail(this.dataForm.email)) {
          this.$http({
            url: this.$http.adornUrl(`/sms/email/sendCode`),
            method: 'post',
            data: this.$http.adornData({
              "email": this.$encruption(this.dataForm.email)
            })
          }).then(({data}) => {
            if (data && data.code !== 0) {
              this.$Message.error(data.msg)
              this.clearTimerEmail()
            }
          })
          this.timeoutChangeStyleEmail()
        } else {
          this.$Message.error(this.$t('validate.incorrect_email_format'))
        }
      }
    },
    // 邮箱验证码
    timeoutChangeStyleEmail () {
      // 启动定时器
      this.loadingTimerEmail()
      this.timerEmail = setInterval(() => {
        // 创建定时器
        if (this.countdownEmail === 0) {
          this.clearTimerEmail() // 关闭定时器
        } else {
          this.loadingTimerEmail()
        }
      }, 1000)
    },
    loadingTimerEmail () {
      var str = `<div>${this.$t('register.prependResend')} ${this.countdownEmail} ${this.$t('register.appendResend')} </div>`
      $('#sendMailCode').html(str)
      this.countdownEmail-- // 定时器减1
    },
    clearTimerEmail () {
      // 清除定时器
      clearInterval(this.timerEmail)
      this.timerEmail = null
      this.countdownEmail = 60
      $('#sendMailCode').html(`<a style="cursor: pointer">${this.$t('register.getCode')}</a>`)
    },
    // 重置数据
    resetData () {
      if (this.$route.params.dataForm !== undefined) {
      this.dataForm = this.$route.params.dataForm
      }
      if (this.$route.params.check !== undefined) {
        this.dataForm.check = !this.$route.params.check
      }
      if (this.$route.params.phoneMail !== undefined) {
        this.phoneMail = this.$route.params.phoneMail
      }
    },
    // 跳转到协议页面
    handleAgreement () {
      this.$router.replace({ name: 'agreement' , params: {dataForm: this.dataForm,phoneMail: this.phoneMail } })
      if (this.timerEmail) {
        this.clearTimerEmail()
      }
    }
  },
  mounted () {
    this.resetDocumentClientHeight()
  },
  created () {
    this.resetData()
  },
}
</script>

<style scoped>
.ivu-steps .ivu-steps-title {
    display: inline-block;
    margin-bottom: 4px;
    padding-right: 10px;
    font-size: 14px;
    font-weight: 700;
    color: #666;
    background: rgba(255, 255, 255, 0.719);
}
.ivu-form-item {
  margin-bottom:20px;
}
.maxnbox{
  background-image: url(../../assets/img/register_background.png);
  background-repeat: repeat-x;
  background-size: 100% 100%;
  overflow: hidden;
}
.login-wrap {
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.719);
  width: 720px;
  height: 600px;
  box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.2);
  border-radius: 1%;
  overflow-x: hidden;
  overflow-y: auto;
}
/*.content {*/
/*  margin: 0 auto;*/
/*  text-align: center;*/
/*  width: 80%;*/
/*}*/
.content {
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 5px;
}

.login-head {
  text-align: center;
  height: 30px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.login-title {
  color: #6f7072;
  padding: 5px;
  font-weight: 600;
  text-shadow: 0 1px 0 #fff;
  font-size: 26px;
}
.steps {
  text-align: left;
}
</style>
