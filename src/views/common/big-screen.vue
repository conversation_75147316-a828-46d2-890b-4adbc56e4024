<template>
  <div id="data-view1">
    <dv-full-screen-container>
      <top-header  @call-method="closeStream" />
      <Row style="height: 100%;">
          <Col span="6">
            <content-left  ref="contentLeft"/>
          </Col>
          <Col span="12">
            <content-center ref="contentCenter"/>
          </Col>
          <Col span="6">
            <content-right ref="contentRight"/>
          </Col>
      </Row>
    </dv-full-screen-container>
  </div>
</template>

<script>
import topHeader from './bigScreen/topHeader'
import contentLeft from './bigScreen/contentLeft.vue'
import contentCenter from './bigScreen/contentCenter.vue'
import contentRight from './bigScreen/contentRight.vue'

export default {
  name: 'DataView',
  components: {
    topHeader,
    contentLeft,
    contentCenter,
    contentRight
  },
  data () {
    return {
      bigScreen: null
    }
  },
  mounted () {
    this.getDataList()
  },
  methods: {
    closeStream() {
      this.$refs.contentCenter.handleBeforeUnload();
    },
    // 初始化数据
    getDataList () {
      this.$http({
        url: this.$http.adornUrl('/lampPole/card/bigScreen'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.bigScreen = data.bigScreen
          this.$refs.contentLeft.getData(this.bigScreen)
          this.$refs.contentCenter.getData(this.bigScreen)
          this.$refs.contentRight.getData(this.bigScreen)
          // localStorage.setItem('bigScreen', JSON.stringify(data.bigScreen))
        } else {

        }
      })
    },
  },
}
</script>

<style scoped>
#data-view1 {
  width: 100%;
  height: 100%;
  background-color: #030409;
  color: #fff;
  display: flex;
}
#dv-full-screen-container {
  background-image: url('../../assets/img/home_background.png');
  background-size: 100% 100%;
  box-shadow: 0 0 3px blue;
  display: flex;
  flex-direction: column;
}
.main-content {
  /* margin-top: 8px; */
  flex: 1;
  display: flex;
  flex-direction: column;
}
.block-left-right-content {
  flex: 1;
  display: flex;
  margin-top: 8px;
  /* height: 84%;
  width: 100%; */
  /* background-color: #ffffff; */
  overflow-x: auto;
  overflow-y: hidden;
}
.block-top-bottom-content {
  flex: 1;
  height: 100%;
  width: 80%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-left: 20px;
}
.block-top-content {
  height: 55%;
  display: flex;
  flex-grow: 0;
  box-sizing: border-box;
  padding-bottom: 20px;
}
</style>
