<!-- 首页 -->
<template>
  <div class="common-layout">
    <Card :style="{ 'height': tableHeight + 171 + 'px', 'overflow-y': 'auto' }">
      <Row :style="{ 'height': (tableHeight + 171) / 2 - 32 + 'px' }">
        <Col span="11">
        <span style="font-size: 18px;font-weight: bold">{{ $t("home.Announcement") }}</span>
        <dv-scroll-board :config="configAnnouncement" @click="skipeo" v-model="announcementVisible"
          :style="{ 'width': '95%', 'height': (tableHeight + 171) / 2 - 64 + 'px', 'color': '#000' }" />
        </Col>
        <Col span="13">
        <!-- 设备统计 -->
        <div ref="deviceCountData" :style="{ 'height': (tableHeight + 171) / 2 - 32 + 'px' }">
        </div>
        </Col>
      </Row>
      <Row :style="{ 'height': (tableHeight + 171) / 2 - 32 + 'px', 'margin-top': '20px' }">
        <Col span="24">
        <!--  控制统计 -->
        <div ref="controlCountData" :style="{ 'height': (tableHeight + 171) / 2 - 32 + 'px' }"></div>
        </Col>
      </Row>
      <Modal v-model="announcementVisible" width="50%">
        <Spin size="large" fix v-if="spinShow"></Spin>
        <p slot="header" style="color:#f60;text-align:center">
          <span>{{ this.title }}</span>
        </p>
        <div style="height: 500px" v-html="clickAnnouncement">
        </div>
        <div slot="footer">
        </div>
      </Modal>
    </Card>
  </div>
</template>

<script>
export default {
  components: {

  },
  data() {
    return {
      // 设备统计
      deviceCountEChart: '',
      // 内圈
      poleStatisticsData: [],
      // 外圈
      equipmentStatisticsData: [],
      // 内外圈颜色
      deviceCountColor: [],
      // 公告数据
      announcementData: [],

      // 控制统计
      controlCountEChart: '',
      // 时间
      controlCountDate: [],
      // 智慧屏幕
      controlScreenCountData: [],
      // 智慧广播
      controlBroadcastScreenCountData: [],
      //公告可视
      announcementVisible: false,
      clickAnnouncement: "",
      title: "",
      spinShow: false,
      isVerify: window.SITE_CONFIG.isVerify,
    }
  },
  methods: {
    // 初始化数据
    getDataList() {
      this.deviceCountColor = []
      this.announcementData = []
      this.controlCountDate = []
      this.controlScreenCountData = []
      this.controlBroadcastScreenCountData = []
      this.poleStatisticsData = []
      this.equipmentStatisticsData = []
      this.$http({
        url: this.$http.adornUrl('/lampPole/card/home'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.announcementData = data.home.announcement
          this.controlCountDate = data.home.controlCount.controlCountDate
          this.controlScreenCountData = data.home.controlCount.controlScreenCountData
          if (this.isAuth('lampPole:broadcastControl')) {
            this.controlBroadcastScreenCountData = data.home.controlCount.controlBroadcastScreenCountData
          }
          this.markAnnouncement()
          if (data.home.deviceCount && data.home.deviceCount.length > 0) {
            // ['#00FF00', '#CCCCCC', '#1E90FF', '#DA70D6', '#EE82EE', '#9932CC', '#483D8B', '#00BFFF', '#FFD700']
            for (let i = 0; i < data.home.deviceCount.length; i++) {
              const element = data.home.deviceCount[i];
              if (element.name === 'online') {
                this.deviceCountColor.unshift('#CCCCCC')
                this.poleStatisticsData.push({ value: element.count, name: this.$t('cardDevice.online') })
              } else if (element.name === 'offline') {
                this.deviceCountColor.unshift('#00FF00')
                this.poleStatisticsData.push({ value: element.count, name: this.$t('lamp.offline'), color: "#fff" })
              } else if (element.name === 'name') {// this.isAuth('lampPole:screenControl') &&
                this.deviceCountColor.push('#1E90FF')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧屏幕') })
              } else if (this.isAuth('lampPole:meteorologicalEnvironment') && element.name === 'environment') {
                this.deviceCountColor.push('#DA70D6')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.气象环境') })
              } else if (this.isAuth('lampPole:lighting') && element.name === 'light') {
                this.deviceCountColor.push('#EE82EE')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧照明') })
              } else if (this.isAuth('lampPole:monitoringControl') && element.name === 'monitor') {
                this.deviceCountColor.push('#9932CC')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧监控') })
              } else if (this.isAuth('lampPole:broadcastControl') && element.name === 'radio') {
                this.deviceCountColor.push('#483D8B')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧广播') })
                } else if (this.isAuth('lampPole:passengerFlowStatistics') && element.name === 'traffic') {
                this.deviceCountColor.push('#00BFFF')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.客流统计') })
              } else if (this.isAuth('lampPole:powerManagement') && element.name === 'electricity') {
                this.deviceCountColor.push('#FFD700')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.电能管理') })
              } else if (this.isAuth('lampPole:radar') && element.name === 'radar') {
                this.deviceCountColor.push('#00FFD0FF')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.雷达测速') })
              }
            }
          }
          // this.deviceCountColor = Array.from(new Set(this.deviceCountColor))

          this.myEcharts()
        } else {
          this.deviceCountColor=[]
          this.announcementData = []
          this.controlCountDate = []
          this.controlScreenCountData = []
          this.controlBroadcastScreenCountData = []
          this.poleStatisticsData = []
          this.equipmentStatisticsData = []
        }
      })
    },
    //标记最近三个月的公告
    markAnnouncement() {
      //获取三个月前当天的时间
      var nowdate = new Date();
      nowdate.setMonth(nowdate.getMonth() - 3)
      if (this.announcementData !== null) {
        for (let i = 0; i < this.announcementData.length; i++) {
          var date2 = new Date(this.announcementData[i][2]);
          if (date2 > nowdate) {
            this.announcementData[i][0] = "<svg width=\"20px\" height=\"20px\" aria-hidden=\"true\" style=\"vertical-align: middle;\">\n" +
              "            <use xlink:href=\"#new\"></use>\n" +
              "          </svg>" + this.announcementData[i][0]
          }
        }
      }
    },
    // 创建图表
    myEcharts() {
      if (this.deviceCountEChart != null && this.deviceCountEChart != "" && this.deviceCountEChart != undefined) {
        this.deviceCountEChart.dispose();//销毁
      }
      this.deviceCountEChart = this.$echarts.init(this.$refs.deviceCountData);
      this.deviceCountEChart.setOption(this.deviceCountOption,true);
      if (this.controlCountEChart != null && this.controlCountEChart != "" && this.controlCountEChart != undefined) {
        this.controlCountEChart.dispose();//销毁
      }
      this.controlCountEChart = this.$echarts.init(this.$refs.controlCountData);
      this.controlCountEChart.setOption(this.controlCountOption,true);
    },
    //点击公告
    skipeo(config) {
      this.spinShow = true;
      this.clickAnnouncement = "";
      this.title = "";
      var temp = config.row[3]
      var id = temp.substring(27, temp.length - 7)
      this.announcementVisible = true
      this.$http({
        url: this.$http.adornUrl(`/sys/Announcement/one/` + id),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.spinShow = false;
          this.clickAnnouncement = data.msg.SysAnn.text;
          this.title = data.msg.SysAnn.title;
        }
      })
    }
  },
  mounted() {
    let that = this
    window.addEventListener("resize", () => {
      that.deviceCountEChart.resize();
      that.controlCountEChart.resize();
    });
  },
  activated() {
    if (!this.isVerify) {
      var isStrong = this.$cookie.get("isStrong")
      if(null == isStrong || undefined == isStrong) {
        this.getDataList()
      }
    } else {
      this.getDataList()
    }

    // console.log(this.deviceCountColor)
  },
  computed: {
    language: {
      get() { return this.$store.state.language.language },
    },
    tableHeight: {
      get() { return this.$store.state.common.tableHeight },
    },
    documentClientWidth: {
      get() { return this.$store.state.common.documentClientWidth },
    },
    // 设备统计
    deviceCountOption: {
      get() {
        return {
          title: {
            text: this.$t('home.EquipmentStatistics'),
            subtext: this.$t('home.unitPCS')
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          color: this.deviceCountColor,
          legend: {
            right: 'right',
          },
          series: [
            {
              name: this.$t('home.PoleStatistics'),
              type: 'pie',
              selectedMode: 'single',
              radius: [0, '30%'],
              label: {
                position: 'inner',
                fontSize: 14
              },
              labelLine: {
                show: false
              },
              data: this.poleStatisticsData
            },
            {
              name: this.$t('home.EquipmentStatistics'),
              type: 'pie',
              radius: ['45%', '60%'],
              labelLine: {
                length: 30
              },
              data: this.equipmentStatisticsData
            }
          ]
        }
      }
    },
    // 控制统计
    controlCountOption: {
      get() {
        if (this.isAuth('lampPole:broadcastControl')) {
          return {
            width: window.innerWidth - 500,
            title: {
              text: this.$t('home.ControlStatistics'),
              subtext: this.$t('home.UnitTimes')
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
                label: {
                  show: true
                }
              }
            },
            calculable: true,
            grid: {
              left: '1%',
              right: '10%',
              bottom: '5%',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                data: this.controlCountDate
              }
            ],
            yAxis: [
              {
                type: 'value',
              }
            ],
            legend: {
              right: 'right',
            },
            series: [
              {
                name: this.$t('nav.智慧屏幕'),
                type: 'bar',
                data: this.controlScreenCountData
              },
              {
                name: this.$t('nav.智慧广播'),
                type: 'bar',
                data: this.controlBroadcastScreenCountData
              }
            ]
          }
        } else {
          return {
            width: window.innerWidth - 500,
            title: {
              text: this.$t('home.ControlStatistics'),
              subtext: this.$t('home.UnitTimes')
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
                label: {
                  show: true
                }
              }
            },
            calculable: true,
            grid: {
              left: '1%',
              right: '10%',
              bottom: '5%',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                data: this.controlCountDate
              }
            ],
            yAxis: [
              {
                type: 'value',
              }
            ],
            legend: {
              right: 'right',
            },
            series: [
              {
                name: this.$t('nav.智慧屏幕'),
                type: 'bar',
                data: this.controlScreenCountData
              },
            ]
          }
        }
      }
    },
    // 公告
    configAnnouncement: {
      get() {
        return {
          header: [this.$t('announcement.title'), this.$t('announcement.content'), this.$t('common.createTime')],
          headerBGC: '#e8eaec',
          oddRowBGC: '#fff',
          evenRowBGC: '#f8f8f9',
          columnWidth: [120, 350, 155],
          data: this.announcementData
        }
      }
    }


  },
  watch: {
    language: {
      handler(newVal, oldVal) {
        // 只要语言发生变化就执行
        if (oldVal !== ''&&newVal !== oldVal && this.$route.path === "/home") {
          this.getDataList()
          // 立即更新图表以反映语言变化
          this.$nextTick(() => {
            if (this.deviceCountEChart && this.deviceCountEChart.setOption) {
              this.deviceCountEChart.setOption(this.deviceCountOption, true)
            }
            if (this.controlCountEChart && this.controlCountEChart.setOption) {
              this.controlCountEChart.setOption(this.controlCountOption, true)
            }
          })
        }
      },
    }
  },
}
</script>

<style scoped>
.common-layout {
  height: 100%;
  width: 100%;
}

#AnnContent {
  text-align: center;
  height: 500px
}

#AnnContent table {
  border: none;
  border-collapse: collapse;
}

#AnnContent table td,
#AnnContent table th {
  border: 1px solid #ccc;
  padding: 3px 5px;
  min-width: 50px;
  height: 20px;
}

#AnnContent table th {
  border-right: 1px solid #ccc;
  border-bottom: 2px solid #ccc;
  text-align: center;
  background-color: #f1f1f1;
}

#AnnContent blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}

#AnnContent code {
  display: inline-block;
  /* *display: inline; */
  /* *zoom: 1; */
  background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}

#AnnContent pre code {
  display: block;
}
</style>
