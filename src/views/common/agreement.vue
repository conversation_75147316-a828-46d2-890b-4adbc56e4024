<template>
  <div class="maxnbox" :style="{ 'height': documentClientHeight + 'px' }">
       <div class="login-wrap" :style="{ 'height': documentClientHeight + 'px' }">
            <div class="login-head">
                <span class="login-title">AIPS用户协议</span>
            </div>
            <Divider dashed />
            <div class="contet" :style="{ 'height': documentClientHeight-120 + 'px' }">
                <span>一、总则</span>
                <p>
                    1．1　用户应当同意本协议的条款并按照页面上的提示完成全部的注册程序。用户在进行注册程序过程中勾选“我已阅读并接受<a href="javascript:void(0)">《AIPS用户协议》</a>”
                    即表示用户与AIPS平台提供商达成协议，完全接受本协议项下的全部条款。
                </p>
                <p>
                    1．2　用户注册成功后，AIPS将给予每个用户一个公司帐号和管理员帐号及相应的密码，该用户帐号和密码由用户负责保管；
                    用户应当对以其用户帐号进行的所有活动和事件负法律责任。
                </p>
                <p>
                    1．3　用户一经注册AIPS帐号，除非子频道要求单独开通权限，用户有权利用该账号使用平台分配的所有功能；当用户使用AIPS各单项服务时，用户的使用行为
                    视为其对该单项服务的服务条款以及AIPS在该单项服务中发出的各类公告的同意。
                </p>
                <p>
                    1．4　AIPS会员服务协议以及各个频道单项服务条款和公告可由AIPS团队随时更新，且无需另行通知。您在使用相关服务时,应关注并遵守其所适用的相关条款。
                    您在使用AIPS提供的各项服务之前，应仔细阅读本服务协议。如您不同意本服务协议及/或随时对其的修改，您可以主动取消AIPS提供的服务；您一旦使用AIPS服务，
                    即视为您已了解并完全同意本服务协议各项内容，包括AIPS对服务协议随时所做的任何修改，并成为AIPS用户。
                </p>
                <p>
                    您在使用AIPS团队提供的各项服务之前，应仔细阅读本服务协议。如您不同意本服务协议及/或随时对其的修改，您可以主动取消AIPS团队提供的服务；您一旦使用AI
                    PS团队服务，即视为您已了解并完全同意本服务协议各项内容，包括AIPS团队对服务协议随时所做的任何修改，并成为AIPS团队用户。
                </p>
                <span>二、注册信息和隐私保护</span>
                <p>
                    2．1　AIPS帐号（即AIPS用户ID）的所有权归AIPS团队，用户完成注册申请手续后，获得AIPS帐号的使用权。用户应提供及时、详尽及准确的个人资料，并不断更新注册资料，符合及时、详尽准确的要求。
                    所有原始键入的资料将引用为注册资料。如果因注册信息不真实而引起的问题，并对问题发生所带来的后果，我公司不负任何责任。
                </p>
                <p>
                    2．2　用户不应将其帐号、密码转让、出售或出借予他人使用，若用户授权他人使用账户，应对被授权人在该账户下发生所有行为负全部责任。
                </p>
                <span>三、使用规则</span>
                <p>
                    3．1　用户在使用AIPS服务时，必须遵守中华人民共和国相关法律法规的规定，用户应同意将不会利用本服务进行任何违法或不正当的活动，包括但不限于下列行为∶
                    <p>
                        （1）上载、展示、张贴、传播或以其它方式传送含有下列内容之一的信息：
                    </p>
                    <b class="contetB">1） 反对宪法所确定的基本原则的；<br/></b>
                    <b class="contetB">2） 危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；<br/></b>
                    <b class="contetB">3） 损害国家荣誉和利益的； <br/></b>
                    <b class="contetB">4） 煽动民族仇恨、民族歧视、破坏民族团结的； <br/></b>
                    <b class="contetB">5） 破坏国家宗教政策，宣扬邪教和封建迷信的； <br/></b>
                    <b class="contetB">6） 散布谣言，扰乱社会秩序，破坏社会稳定的； <br/></b>
                    <b class="contetB">7） 散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的； <br/></b>
                    <b class="contetB">8） 侮辱或者诽谤他人，侵害他人合法权利的； <br/></b>
                    <b class="contetB" style="float: left;">9） 含有虚假、有害、胁迫、侵害他人隐私、骚扰、侵害、中伤、粗俗、猥亵、或其它道德上令人反感的内容； <br/></b>
                    <b class="contetB">10） 含有中国法律、法规、规章、条例以及任何具有法律效力之规范所限制或禁止的其它内容的；<br/></b>
                    <p>
                        （2）不得为任何非法目的而使用网络服务系统；
                    </p>
                    <p>
                        （3）用户需严格审查管理所属公司id的所有资源、用户、内容：
                    </p>
                    <b class="contetB">1）自行管理子用户的添加，删除，权限分配；<br/></b>
                    <b class="contetB">2）自行审查上传素材内容的合法性，不得侵犯他人知识产权；<br/></b>
                    <b class="contetB" style="float: left;">3）自行管理制作节目的内容合法性，严格审查发布，对发布到led大屏幕上的内容合法性承担所有法律责任； <br/></b>
                </p>
                <p>
                    3．2　用户违反本协议或相关的服务条款的规定，导致或产生的任何第三方主张的任何索赔、要求或损失，包括合理的律师费，您同意赔偿AIPS与合作公司、关联公司，
                    并使之免受损害。对此，AIPS有权视用户的行为性质，采取包括但不限于删除用户发布信息内容、暂停使用许可、终止服务、限制使用、回收AIPS帐号、追究法律责任等措施。
                    对恶意注册AIPS帐号或利用AIPS帐号进行违法活动、捣乱、骚扰、欺骗、其他用户以及其他违反本协议的行为，AIPS有权回收其帐号。同时，AIPS团队会视司法部门的要求，协助调查。
                </p>
                <p>
                    3．3　用户须对自己在使用AIPS服务过程中的行为承担法律责任。用户承担法律责任的形式包括但不限于：对受到侵害者进行赔偿，以及在AIPS团队首先承担了因用户行为导致的行政处罚或
                    侵权损害赔偿责任后，用户应给予AIPS团队等额的赔偿。
                </p>
                <span>四、服务内容</span>
                <p>
                    4．1　AIPS网络服务的具体内容由AIPS根据实际情况提供。
                </p>
                <p>
                    4．2　除非本服务协议另有其它明示规定，AIPS所推出的新产品、新功能、新服务，均受到本服务协议之规范。
                </p>
                <p>
                    4．3　为使用本服务，您必须能够自行经有法律资格对您提供互联网接入服务的第三方，进入国际互联网，并应自行支付相关服务费用。此外，您必须自行配备及负责与国际联网连线所需之
                    一切必要装备，包括计算机、数据机或其它存取装置。
                </p>
                <p>
                    4．4　鉴于网络服务的特殊性，用户同意AIPS有权不经事先通知，随时变更、中断或终止部分或全部的网络服务（包括收费网络服务）。AIPS不担保网络服务不会中断，
                    对网络服务的及时性、安全性、准确性也都不作担保。
                <p>
                    4．5　AIPS需要定期或不定期地对提供网络服务的平台或相关的设备进行检修或者维护，如因此类情况而造成网络服务（包括收费网络服务）在合理时间内的中断，AIPS无需为此承担任何责任。
                    AIPS保留不经事先通知为维修保养、升级或其它目的暂停本服务任何部分的权利。
                </p>
                <p>
                    4．6 本服务或第三人可提供与其它国际互联网上之网站或资源之链接。由于AIPS无法控制这些网站及资源，您了解并同意，此类网站或资源是否可供利用，AIPS不予负责，存在或源于此类网站或
                    资源之任何内容、广告、产品或其它资料，AIPS亦不予保证或负责。因使用或依赖任何此类网站或资源发布的或经由此类网站或资源获得的任何内容、商品或服务所产生的任何损害或损失，AIPS不承担任何责任。
                </p>
                <p>
                    4．7 用户明确同意其使用AIPS网络服务所存在的风险将完全由其自己承担。用户理解并接受下载或通过AIPS服务取得的任何信息资料取决于用户自己，并由其承担系统受损、资料丢失以及其它任何风险。
                    AIPS对在服务网上得到的任何商品购物服务、交易进程、招聘信息，都不作担保。
                </p>
                <p>
                    4．8 用户须知：AIPS提供的各种挖掘推送服务中（包括AIPS新首页的导航网址推送），推送给用户曾经访问过的网站或资源之链接是基于机器算法自动推出，AIPS不对其内容的有效性、安全性、合法性等做任何担保。
                </p>
                <p>
                    4．9　6个月未登陆的帐号，AIPS保留关闭的权利。
                </p>
                <p>
                   4．10　AIPS有权于任何时间暂时或永久修改或终止本服务（或其任何部分），而无论其通知与否，AIPS对用户和任何第三人均无需承担任何责任。
                </p>
                <p>
                   4．11　终止服务<br/>您同意AIPS得基于其自行之考虑，因任何理由，包含但不限于长时间未使用，或AIPS认为您已经违反本服务协议的文字及精神，终止您的密码、
                   帐号或本服务之使用（或服务之任何部分），并将您在本服务内任何内容加以移除并删除。您同意依本服务协议任何规定提供之本服务，无需进行事先通知即可中断或终止，
                   您承认并同意，AIPS可立即关闭或删除您的帐号及您帐号中所有相关信息及文件，及/或禁止继续使用前述文件或本服务。此外，您同意若本服务之使用被中断或终止或您的帐号
                   及相关信息和文件被关闭或删除，AIPS对您或任何第三人均不承担任何责任。
                </p>
                <span>五、其他</span>
                <p>
                    5．1　本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。
                </p>
                <p>
                    5．2　如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向AIPS所在地的人民法院提起诉讼。
                </p>
                <p>
                    5．3　AIPS未行使或执行本服务协议任何权利或规定，不构成对前述权利或权利之放弃。
                </p>
                <p>
                    5．4　如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。请您在发现任何违反本服务协议以及其他任何单项服务的服务条款、
                    AIPS各类公告之情形时，通知AIPS。您可以通过如下联络方式同AIPS联系：
                    <b style="padding-left: 45px;"><br/>上海市嘉定区金沙江西路1555弄C区6号3楼 邮编：201803<br/></b>
                    <b style="padding-left: 45px;">AIPS团队</b>
                </p>
            </div>
            <Divider dashed />
            <div class="foot">
                <Button size="default" type="info" style="margin-right: 20px;" @click="$router.push({ name: 'register' })">返回</Button>
                <Button size="default" type="success" :disabled="disabled"
                @click="$router.push({ name: 'register' , params: {check: false, dataForm: $route.params.dataForm, phoneMail: $route.params.phoneMail} })">已阅读</Button>
            </div>
       </div>
  </div>
</template>

<script>
export default {
    data () {
        return {
            documentClientHeight: 0,
            disabled: true,
        }
    },
    methods: {
        // 重置窗口可视高度
        resetDocumentClientHeight () {
            this.documentClientHeight = document.documentElement['clientHeight']
            window.onresize = () => {
                this.documentClientHeight = document.documentElement['clientHeight']
            }
        },
        scrolling () {
            this.$nextTick(() => {
                const el = document.querySelector('.contet');
                const offsetHeight = el.offsetHeight;
                el.onscroll = () => {
                    const scrollTop = el.scrollTop;
                    const scrollHeight = el.scrollHeight;
                    if ((offsetHeight + scrollTop) - scrollHeight >= -1) {
                        this.disabled = false
                    }
                }
            })
        }
    },
    mounted () {
        this.resetDocumentClientHeight()
        this.scrolling()
    }
}
</script>
<style scoped>
.maxnbox{
    background-image: url(../../assets/img/login_background.png);
    background-repeat: repeat-x;
    background-size: 100% 100%;
    overflow: hidden;
}
.login-wrap {
    position: absolute;
    left:50%;
    top:50%;
    transform: translate(-50%, -50%);
    background: rgb(255, 255, 255);
    margin:0 auto;
    width: 720px;
    border-radius: 1%;
    overflow: hidden;
}
.login-wrap:hover {
    box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.2);
}
.login-head {
    text-align: center;
    height: 30px;
    margin-top: 10px;
    margin-bottom: 10px;
}
.login-title {
    color: #6f7072;
    padding: 5px;
    font-weight: 600;
    text-shadow: 0 1px 0 #fff;
    font-size: 26px;
}
.contet {
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 5px;
}
.contet span {
    font-weight: 600;
    font-size: 15px;
}
.contet p {
    text-indent:25px
}
.contetB {
    padding-left: 55px;
}
.foot {
    float: right;
    margin-top: 10px;
    margin-right: 20px;
}
</style>
