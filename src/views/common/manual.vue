<template>
  <div>
    <div class="layout" :class="{'layout-hide-text': spanLeft < 5}">
      <Menu mode="horizontal" theme="dark" active-name="1">
        <div class="layout-nav">
          <Menu-item name="2" class="menuItem1" @click.native="visible = true" v-if="userId === 2">
            <Icon type="ios-people"></Icon>
            {{$t('screen.uploadFile')}}
          </Menu-item>
          <Menu-item name="3" class="menuItem1" @click.native="fileDownload()">
            <Icon type="ios-people"></Icon>
            <!-- <a style="color:white;" target="_blank" :href="src">文件下载</a> -->
            <span>{{$t('file.download')}}</span>
          </Menu-item>
          <Menu-item name="4" class="menuItem1" @click.native="fileDelete()" v-if="userId === 2">
            <Icon type="ios-people"></Icon>
            <span>{{$t('common.delete')}}</span>
          </Menu-item>
        </div>
      </Menu>
      <div class="layout-content" style="height: calc(100vh - 62px);">
        <Row type="flex" style="height: 100%;">
          <Col :span="spanLeft" class="layout-menu-left">
            <Menu :active-name="activeName" theme="dark" width="auto">
              <Menu-item :name="index" :key="index" v-for="(item,index) in dataList"
                         :class="activeName===(index + '')? 'ivu-menu-item ivu-menu-item-active ivu-menu-item-selected' : 'ivu-menu-item'"
                         @click.native="getFileId(item, index)">
                <span class="layout-text">
                  <Icon type="ios-navigate" :size="iconSize"></Icon>
                  {{ item.fileName }}
                </span>
              </Menu-item>
            </Menu>
          </Col>
          <Col :span="spanRight">
            <div class="layout-content" v-if="dataList!==null" style="height:100%">
              <iframe width="100%" height="100%"
                      :src="src"></iframe>
            </div>
            <div v-else class="withoutFile" style="width: 1360px;height: 100%;text-align: center;">
                 {{$t('home.temporarilyNoData')}}
            </div>
          </Col>
        </Row>
      </div>
    </div>
    <!-- 上传界面 -->
    <Modal v-model="visible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{ $t('screen.uploadFile') }}</span>
      </p>
      <Form :model="uploadForm" style="height: 310px">
        <Loading :loadBoolValue="load"></Loading>
        <FormItem class="upload">
          <Upload
            multiple
            :before-upload="handleUpload"
            :action="''"
            type="drag"
            :format="['docx']">
            <div style="padding: 20px 0">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <p>{{ $t('file.attachment1') }}</p>
            </div>
          </Upload>
          <div>
            <ul class="file-list" v-for="(list,index) in uploadForm.manualFile" :key="index">
              <li>{{ $t('file.name') }}: <span style="font-size:15px;">{{ list.name }}</span>
                <Icon type="ios-close" size="20" style="float:right;"
                      @click="uploadForm.manualFile.splice(index,1)"></Icon>
              </li>
            </ul>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button size="large" @click="visible = false">{{ $t('common.cancel') }}</Button>
        <Button type="primary" @click="uploadFormSubmit">{{ $t('file.upload') }}</Button>
      </div>
    </Modal>
  </div>

</template>
<script>
import axios from "axios";
import Loading from '@/utils/loading'
export default {
  data() {
    return {
      userId: null,
      src: null,
      loading: false,
      spanLeft: 5,
      spanRight: 19,
      dataList: [], //文件名列表
      fileId: '',
      activeName: "0",
      visible: false,
      load: false,
      token: this.$cookie.get('token'),
      uploadForm: {
        manualFile: [] // 临时数组，同时用于显示在页面
      }
    }
  },

  methods: {
    // 删除选中的
    fileDelete() {
      if (this.dataList.length >= this.activeName) {
        this.$Modal.confirm({
          title: this.$t("common.tips"),
          content: this.$t("common.delete_current_option") + ' ' + this.dataList[this.activeName].fileName,
          okText: this.$t("common.confirm"),
          cancelText: this.$t("common.cancel"),
          onOk: () => {
            this.$http({
              url: this.$http.adornUrl("/manual/file/delete"),
              method: "post",
              data: this.$http.adornData(this.dataList[this.activeName].fileId, false),
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.$Message.success({
                  content: this.$t("common.operationSuccessful"),
                  duration: 0.5,
                  onClose: () => {
                    this.getFileList()
                  },
                });
              } else {
                this.$Message.error(data.msg);
              }
            });
          },
        });
      }
    },
    // 获取列表
    getFileList(){
      this.$http({
        url: this.$http.adornUrl('/manual/file/list'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if(data && data.code === 0) {
          this.dataList = data.fileNameList;
          this.getFileId(this.dataList[0], 0)
        } else {
          this.dataList = null;
        }
      })
    },
    // 列表中数据详情
    getFileId(item, index) {
      this.activeName = index + ''
      this.fileId = item.fileId
      this.src = this.$http.adornUrl(`/manual/file/manualView/` + this.fileId + `?token=${this.$cookie.get("token")}`);
    },
    // 文件下载
    fileDownload(){
      this.src = this.$http.adornUrl(`/manual/file/download/` + this.fileId + `?token=${this.$cookie.get("token")}`);
    },
    // 文件上传前
    handleUpload(selectFile) {
      var type = selectFile.type
      var ele = type.substring(type.lastIndexOf('.') + 1, type.length)
      if (ele === "document") {
        if (ele === "document" && selectFile.size > (50 * 1024 * 1024)) {
          // this.$Message.error("文件超过大小")
          this.$Message.error({
            content: this.$t("file.fileOverSize")
          })
          this.selectFile = null //超过大小将文件清空
          return false
        }
      } else {
        this.$Message.error({
          content:this.$t("file.fileLimit")
        })
        this.selectFile = null //将文件清空
        return false
      }
      if (this.uploadForm.manualFile.length >= 5) {
        this.$Message.error(this.$t('file.YouCanOnlyUploadUpTo5Files'))
        return false
      }
      // 临时数组，同时用于显示在页面
      // this.uploadForm.dispalyFile = selectFile
      // this.uploadFormSubmit()
      this.uploadForm.manualFile.push(selectFile)
      return false
    },
    // 新增文件上传
    uploadFormSubmit() {
      if (this.uploadForm.manualFile.length > 0) {
        this.load = true
        let formData = new FormData()

        //多个文件上传
        for (var i = 0; i < this.uploadForm.manualFile.length; i++) {
          formData.append("file", this.uploadForm.manualFile[i]);   // 文件对象
        }
        axios.request({
          url: this.$http.adornUrl('/manual/file/upload'),
          method: 'post',
          data: formData,
          headers: {'Content-Type': 'multipart/form-data', 'token': this.token}
        }).then(res => {
          // 上传成功处理
          if (res.data) {
            if (res.data.code === 0) {
              this.load = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                  this.getFileList()
                }
              })
            } else {
              this.load = false
              this.$Message.error({
                content: res.data.msg,
                duration: 0.5,
                onClose: () => {
                  this.load = false
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }

          }
        })
      }
    },

  },
  created() {
    let receice = window.opener["filter"]
    this.userId = receice.userId
    this.getFileList()
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.uploadForm = {
          name: '',
          resourceType: 0,
          size: '',
          manualFile: []
        }
      }
    }
  },
  computed: {
    iconSize() {
      return this.spanLeft === 5 ? 14 : 24;
    },
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  },
  components: {
    Loading
  }
}
</script>


<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.layout-nav {
  margin: 0 auto;
}

.menuItem1 {
  float: left;
}


.layout-content {
  /*min-height: 200px;*/
  /*margin: 8px;*/
  /*overflow: hidden;*/
  background: #fff;
  /*border-radius: 4px;*/
}

.layout-menu-left {
  background: #464c5b;
  /*height: 960px;*/
}
.layout-li {
  height: 50px;
  background: #515a6e;
  line-height: 50px;
  color: #fff;
}
.layout-li-selected {
  color: #2d8cf0;
  background: #363e4f;
}
.layout-li-div {
  height: 50px;
  width: 80%;
  /* text-overflow: clip;
  overflow: hidden;
  white-space: nowrap; */
  /* background: #d7dde4; */
}
/* .layout-ceiling-main a {
  color: #9ba7b5;
} */

.layout-hide-text .layout-text {
  display: none;
}

</style>
