<template>
  <div id="top-header">
    <dv-decoration-1 class="header-left-decoration" />
    <dv-decoration-5 class="header-center-decoration" />
    <!-- <dv-decoration-8 class="header-right-decoration" :reverse="true" /> -->
    <div class="header-right-decoration">
      <!-- <div @click="launchFullscreen()" v-if="fullScreenFlag ===  false">
        <svg style="vertical-align: middle;margin: 15px 15px" width="30px" height="30px" aria-hidden="true">
          <use xlink:href="#full-screen"></use>
        </svg>
      </div> -->
      <div @click="exitFullscreen()">
        <svg style="vertical-align: middle;margin: 15px 15px" width="30px" height="30px" aria-hidden="true">
          <use xlink:href="#CancelFullScreen"></use>
        </svg>
      </div>
    </div>
    <div v-if="!isVerify" class="center-title"> {{$t('home.DynamicLargeScreen')}}</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fullScreenFlag: false,
      isVerify: window.SITE_CONFIG.isVerify,
    }
  },
  methods: {
    // 全屏显示
    launchFullscreen() {
      var element = document.getElementById("data-view1")
      if (element) {
        // this.fullScreenFlag =  true
        if(element.requestFullscreen) {
          element.requestFullscreen();
        } else if(element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if(element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen();
        } else if(element.msRequestFullscreen) {
          element.msRequestFullscreen();
        }
      }
    },
    // 退出全屏显示
    exitFullscreen() {
      this.$emit("call-method")
      this.$router.replace({name: 'home'})
      // this.fullScreenFlag =  false
      // if(document.exitFullscreen) {
      //   document.exitFullscreen();
      // } else if(document.mozCancelFullScreen) {
      //   document.mozCancelFullScreen();
      // } else if(document.webkitExitFullscreen) {
      //   document.webkitExitFullscreen();
      // }
    },
  },
  mounted () {
    this.launchFullscreen()
  }
}
</script>

<style scoped>
#top-header {
  position: relative;
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
}
.header-center-decoration {
    width: 40%;
    height: 60px;
    margin-top: 30px;
  }

.header-left-decoration {
  width: 25%;
  height: 60px;
}

.header-right-decoration {
  width: 25%;
  height: 60px;
  text-align: right;
}

.center-title {
  position: absolute;
  font-size: 25px;
  font-weight: bold;
  left: 50%;
  top: 15px;
  transform: translateX(-50%);
}
</style>
