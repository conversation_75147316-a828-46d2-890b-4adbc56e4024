<template>
  <div style="height: 100%;">
        <div style="height: 35%;">
            <dv-border-box-1>
              <div  style="display: flex;width: 100%;height: 100%;padding-top: 5%">
                <div ref="deviceCountData" style="width: 95%;height: 93%"></div>
              </div>
            </dv-border-box-1>
        </div>
        <div style="height: 65%;margin-top: 8px">
            <dv-border-box-7 style="width: 98%;height: 97%;margin: 0 auto">
                <div class="top-head-title" style="margin-top: 5px;">{{$t('bigScreen.DeviceList')}}</div>
                <dv-scroll-board :config="deviceListConfig" style="width:85%;height:88%; margin: 5px auto;" />
            </dv-border-box-7>
        </div>
  </div>
</template>

<script>
export default {
    data () {
        return {
            // 设备列表
            deviceListData: [],
          // 设备统计
          deviceCountEChart: '',
          // 内圈
          poleStatisticsData: [],
          // 外圈
          equipmentStatisticsData: [],
          // 内外圈颜色
          deviceCountColor: [],
        }
    },
    methods: {
        getData(bigScreen) {
          this.deviceListData = bigScreen.deviceListData
          if (bigScreen.deviceTypeCount&&bigScreen.deviceTypeCount.length>0) {
            // ['#00FF00', '#CCCCCC', '#1E90FF', '#DA70D6', '#EE82EE', '#9932CC', '#483D8B', '#00BFFF', '#FFD700']
            console.log(bigScreen.deviceTypeCount)
            for (let i = 0; i < bigScreen.deviceTypeCount.length; i++) {
              const element = bigScreen.deviceTypeCount[i];
              if (element.name === 'online') {
                this.deviceCountColor.unshift('#CCCCCC')
                this.poleStatisticsData.push({ value: element.count, name: this.$t('cardDevice.online') })
              } else if (element.name === 'offline') {
                this.deviceCountColor.unshift('#00FF00')
                this.poleStatisticsData.push({ value: element.count, name: this.$t('lamp.offline'), color: "#fff" })
              } else if (element.name === 'name') {// this.isAuth('lampPole:screenControl') &&
                this.deviceCountColor.push('#1E90FF')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧屏幕') })
              } else if (this.isAuth('lampPole:meteorologicalEnvironment') && element.name === 'environment') {
                this.deviceCountColor.push('#DA70D6')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.气象环境') })
              } else if (this.isAuth('lampPole:lighting') && element.name === 'light') {
                this.deviceCountColor.push('#EE82EE')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧照明') })
              } else if (this.isAuth('lampPole:monitoringControl') && element.name === 'monitor') {
                this.deviceCountColor.push('#9932CC')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧监控') })
              } else if (this.isAuth('lampPole:broadcastControl') && element.name === 'radio') {
                this.deviceCountColor.push('#483D8B')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.智慧广播') })
              } else if (this.isAuth('lampPole:passengerFlowStatistics') && element.name === 'traffic') {
                this.deviceCountColor.push('#00BFFF')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.客流统计') })
              } else if (this.isAuth('lampPole:powerManagement') && element.name === 'electricity') {
                this.deviceCountColor.push('#FFD700')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.电能管理') })
              } else if (this.isAuth('lampPole:radar') && element.name === 'radar') {
                this.deviceCountColor.push('#00FFD0FF')
                this.equipmentStatisticsData.push({ value: element.count, name: this.$t('nav.雷达测速') })
              }
            }
          }
          this.myEcharts()
        },
      myEcharts() {
        if (this.deviceCountEChart != null && this.deviceCountEChart != "" && this.deviceCountEChart != undefined) {
          this.deviceCountEChart.dispose();//销毁
        }
        this.deviceCountEChart = this.$echarts.init(this.$refs.deviceCountData);
        this.deviceCountEChart.setOption(this.deviceCountOption);

      },
    },
    mounted() {
        // let bigScreen = this.$emit("bigScreen").$attrs.bigScreen
        // console.log(this.$emit("bigScreen"))
        // if (bigScreen && bigScreen.deviceListData) {
        //     this.deviceListData = bigScreen.deviceListData
        // }
    },
    computed: {
        deviceListConfig: {
            get () {
                return {
                    header: ['ID', this.$t('lamp.poleName'), this.$t('lamp.online')],
                    data: this.deviceListData,
                    rowNum: 8,
                    columnWidth: [150],
                    align: ['center', 'center', 'center'],
                    carousel: 'page'
                }
            }
        },
      // 设备统计
      deviceCountOption: {
        get() {
          return {
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)',
            },
            color: this.deviceCountColor,
            legend: {
              // top: '5%',
              left: 'right',
              textStyle: {
                color: '#fff',
                fontWeight: 'bold',
                fontSize: 15,
              },
              // data: this.equipmentStatisticsData
            },
            series: [
              {
                name: this.$t('home.PoleStatistics'),
                type: 'pie',
                selectedMode: 'single',
                radius: [0, '35%'],
                label: {
                  // position: 'inner',
                  fontSize: 15,
                  // show: false,
                  formatter:  '{b}: {c}',
                  length: 100,
                  textStyle: {
                    color: '#fff',                    
                    fontWeight: 'bold',
                    fontSize: 15,
                  }
                },
                top: '35%',
                left: '6%',
                // labelLine: {
                //   show: false
                // },
                labelLine: {
                  length: 10
                },
                data: this.poleStatisticsData
              },
              {
                name: this.$t('home.EquipmentStatistics'),
                type: 'pie',
                radius: ['55%', '70%'],
                label: {
                  length:300,
                  formatter:  '{b}: {c}',
                  textStyle: {
                    fontWeight: 'bold',
                    color: '#fff',
                    fontSize: 15,
                  },
                  fontSize: 15
                },
                top: '35%',
                left: '6%',
                labelLine: {
                  length: 10
                },

                data: this.equipmentStatisticsData
              }
            ]
          }
        }
      },
    }
}
</script>

<style scoped>
.top-head-title {
    text-align: center;
    font-weight: bold;
    font-size: 17px;
}
.top-content {
    width:85%;
    height:80%;
    margin: 5px auto;
    background-color: #fff;
    border-radius: 2%;
}
</style>
