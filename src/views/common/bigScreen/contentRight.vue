<template>
  <div style="height: 100%;padding: 0 8px">
    <!--       <div style="height: 34%;">-->
    <!--           <dv-border-box-11 :title="$t('bigScreen.OperationLog')">-->
    <!--               <div class="top-content" ref="operationLogData"></div>-->
    <!--           </dv-border-box-11>-->
    <!--       </div>-->


    <div style="height: 34%;">

        <dv-border-box-11 :title="$t('electricity.electricityData')" style="font-size: 20px;font-weight: bold;display: flex;width: 100%;height: 100%" >
          <div v-if="electricityCardId.length===0" class="bottom-content">
            {{ $t('home.temporarilyNoData') }}
          </div>
          <div v-else style="justify-content:center;align-items:center;height: 100%;width: 100%" >
            <dv-decoration-7 style="margin-top: 10%;width: 100%;height: 20%; font-size: 20px;font-weight: bold;" >{{$t('home.cardNumber')}}：{{this.electricityCardId}}</dv-decoration-7>
            <dv-capsule-chart style="height: 60%;width: 90%;margin-left: 3%" :config="electricityDataConfig"/>
          </div>

<!--          <dv-decoration-7 style="width:100%;height:10%">卡号：{{this.electricityCardId}}</dv-decoration-7>-->
<!--          <div style="display: flex;justify-content:center;align-items:center;width: 100%;height: 100%">-->
<!--          <dv-capsule-chart :config="electricityDataConfig" style="height: 70%;width: 80%;"/>-->
<!--          </div>-->
      </dv-border-box-11>
    </div>

    <div style="height: 33%;">
      <dv-border-box-11 style="font-size: 20px;font-weight: bold;" :title="$t('bigScreen.ProgramPlayStatistics')">
        <div class="top-content" ref="programData"></div>
      </dv-border-box-11>
    </div>


    <div style="height: 33%;">
      <!--         <dv-border-box-11 :title="$t('nav.气象环境')">-->
      <!--           <div class="top-content" ref="environmentalData"></div>-->
      <!--         </dv-border-box-11>-->

      <dv-border-box-11 :title="$t('nav.气象环境')" style="font-size: 20px;font-weight: bold;" class="right_bottom_meteorological">
        <div v-if="environmentalXAxis===undefined||environmentalXAxis.length===0" class="bottom-content">
          {{ $t('home.temporarilyNoData') }}
        </div>
        <div class="top-content" v-else>
          <div class="meteorological_info">
            <dv-decoration-11 class="meteorological_info_detail">{{ $t('home.cardNumber') }}：{{
                environmentCardId
              }}
            </dv-decoration-11>
          </div>
          <div class="meteorological_info">
            <dv-decoration-11 class="meteorological_info_detail">PM10：{{ pm10 }} μg/m³</dv-decoration-11>
            <dv-decoration-11 class="meteorological_info_detail">PM2.5：{{ pm2_5 }} μg/m³</dv-decoration-11>
          </div>

          <div class="meteorological_info">
            <dv-decoration-11 class="meteorological_info_detail">{{ $t('card.temperature') }}：{{ temperature }} ℃
            </dv-decoration-11>
            <dv-decoration-11 class="meteorological_info_detail">{{ $t('program.noise') }}：{{ noise }} dB
            </dv-decoration-11>
          </div>

          <div class="meteorological_info">
            <dv-decoration-11 class="meteorological_info_detail">{{ $t('card.humidity') }}：{{ humidity }} RH
            </dv-decoration-11>
            <dv-decoration-11 class="meteorological_info_detail">{{
                $t('meteorological.Illuminance')
              }}：{{ sensorBrightness }}
            </dv-decoration-11>
          </div>

          <div class="meteorological_info">
            <dv-decoration-11 class="meteorological_info_detail">{{ $t('program.windDirection') }}：{{ windDirection }}
              °
            </dv-decoration-11>
            <dv-decoration-11 class="meteorological_info_detail">{{ $t('program.windSpeed') }}：{{ windSpeed }} m/s
            </dv-decoration-11>
          </div>
        </div>
      </dv-border-box-11>


      <!--         <dv-border-box-11>-->
      <!--           <dv-scroll-board :config="environmentConfig" style="width:100%;height:50%; margin:auto;"/>-->
      <!--           <dv-scroll-board :config="humidityDataConfig" style="width:20%;height:10%"/>-->
      <!--           <dv-scroll-board :config="noiseDataConfig" style="width:20%;height:10%"/>-->
      <!--         </dv-border-box-11>-->


      <!--         <dv-border-box-7 style="width: 98%;height: 97%;margin: 0 auto">-->
      <!--             <div class="top-head-title" style="margin-top: 5px;text-align: center;font-size: 20px">{{ $t('nav.气象环境') }}</div>-->
      <!--             <dv-scroll-board :config="environmentConfig" style="width:100%;height:88%; margin: 5px auto;" />-->
      <!--         </dv-border-box-7>-->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 节目播放统计
      programEChart: '',
      programDate: [],
      programData: [],
      // 操作日志
      operationLogDate: [],
      operationLogEChart: '',
      operationSuccessData: [],
      operationFailData: [],
      // 环境检测
      environmentalEChart: '',
      environmentalXAxis: [],
      environmentalXAxisShow: [],
      // pm10
      pm10Data: [],
      pm10DataShow: [],
      // pm2.5
      pm2_5Data: [],
      pm2_5Show: [],
      // 温度
      temperatureData: [],
      temperatureShow: [],
      // 噪音
      noiseData: [],
      noiseShow: [],
      // 湿度
      humidityData: [],
      humidityShow: [],
      // 传感器亮度
      sensorBrightnessData: [],
      sensorBrightnessShow: [],
      // 风向
      windDirectionData: [],
      windDirectionShow: [],
      // 风速
      windSpeedData: [],
      windSpeedShow: [],
      //环境监测指针
      environmentalIndex: 0,
      environmentalTimer: null,

      // pm10
      pm10: '',
      // pm2.5
      pm2_5: '',
      // 温度
      temperature: '',

      // 噪音
      noise: '',

      // 湿度
      humidity: '',

      // 传感器亮度
      sensorBrightness: '',

      // 风向
      windDirection: '',

      // 风速
      windSpeed: '',

      //环境监测卡号
      environmentCardId: '',

      //电能统计数据
      electricityDataList: [],
      //电流
      current: "",
      //功率
      power: "",
      //电能
      electricity: '',
      //电压
      voltage: '',
      //电能卡号
      electricityCardId: "",
      //电能指针
      electricityIndex: 0,
      //定时
      electricityTimer: null,
    }
  },
  methods: {
    getData(bigScreen) {
      if (bigScreen) {
        if (bigScreen.programDataList) {
          this.programDate = bigScreen.programDataList.programDate
          this.programData = bigScreen.programDataList.programData
        }
        if (bigScreen.operationLogDataList) {
          this.operationLogDate = bigScreen.operationLogDataList.operationLogDate
          this.operationSuccessData = bigScreen.operationLogDataList.operationSuccessData
          this.operationFailData = bigScreen.operationLogDataList.operationFailData
        }
        if (bigScreen.environmentalList) {
          this.environmentalXAxis = bigScreen.environmentalList.environmentalXAxis
          this.pm10Data = bigScreen.environmentalList.pm10Data
          this.pm2_5Data = bigScreen.environmentalList.pm2_5Data
          this.temperatureData = bigScreen.environmentalList.temperatureData
          this.noiseData = bigScreen.environmentalList.noiseData
          this.humidityData = bigScreen.environmentalList.humidityData
          this.sensorBrightnessData = bigScreen.environmentalList.sensorBrightnessData
          this.windDirectionData = bigScreen.environmentalList.windDirectionData
          this.windSpeedData = bigScreen.environmentalList.windSpeedData
        }
        if (bigScreen.electricityDataList) {
          this.electricityDataList = bigScreen.electricityDataList
        }
        if (this.isAuth('lampPole:meteorologicalEnvironment')) {
          this.environmentalDataPolling()
        }
        if (this.isAuth('lampPole:powerManagement')) {
          this.electricityDataPolling()
        }
        this.myEcharts()
      }
    },
    myEcharts() {
      if (this.programEChart != null && this.programEChart != "" && this.programEChart != undefined) {
        this.programEChart.dispose();//销毁
      }
      this.programEChart = this.$echarts.init(this.$refs.programData);
      this.programEChart.setOption(this.programOption);
      /* if (this.operationLogEChart != null && this.operationLogEChart != "" && this.operationLogEChart != undefined) {
        this.operationLogEChart.dispose();//销毁
      }
      this.operationLogEChart = this.$echarts.init(this.$refs.operationLogData);
      this.operationLogEChart.setOption(this.operationLogOption);
      if (this.environmentalEChart != null && this.environmentalEChart != "" && this.environmentalEChart != undefined) {
         this.environmentalEChart.dispose();//销毁
     }


   if (this.environmentalXAxis && this.environmentalXAxis.length > 0) {
         var length = 0
         if (this.environmentalIndex + 3 < this.environmentalXAxis.length) {
             length = 3;
         } else {
             length = (this.environmentalXAxis.length - this.environmentalIndex) + this.environmentalIndex;
         }
         this.environmentalXAxisShow = []
         this.pm10DataShow = []
         this.pm2_5Show = []
         this.temperatureShow = []
         this.noiseShow = []
         this.humidityShow = []
         this.sensorBrightnessShow = []
         this.windDirectionShow = []
         this.windSpeedShow = []
         for (let i = this.environmentalIndex; i < length; i++) {
             this.environmentalXAxisShow.push(this.environmentalXAxis[i]);
             this.pm10DataShow.push(this.pm10Data[i]);
             this.pm2_5Show.push(this.pm2_5Data[i]);
             this.temperatureShow.push(this.temperatureData[i]);
             this.noiseShow.push(this.noiseData[i]);
             this.humidityShow.push(this.humidityData[i]);
             this.sensorBrightnessShow.push(this.sensorBrightnessData[i]);
             this.windDirectionShow.push(this.windDirectionData[i]);
             this.windSpeedShow.push(this.windSpeedData[i]);
         }
         this.environmentalEChart = this.$echarts.init(this.$refs.environmentalData);
         this.environmentalEChart.setOption(this.environmentalOption);
         this.runenvironmentalData()
     }*/
    },

    //环境检测数据轮询
    environmentalDataPolling() {

        if (this.environmentalXAxis!==undefined&&this.environmentalXAxis.length > 0) {
          this.environmentCardId = this.environmentalXAxis[this.environmentalIndex]
          this.pm10 = this.pm10Data[this.environmentalIndex]
          this.pm2_5 = this.pm2_5Data[this.environmentalIndex]
          this.temperature = this.temperatureData[this.environmentalIndex]
          this.noise = this.noiseData[this.environmentalIndex]
          this.humidity = this.humidityData[this.environmentalIndex]
          this.sensorBrightness = this.sensorBrightnessData[this.environmentalIndex]
          this.windDirection = this.windDirectionData[this.environmentalIndex]
          this.windSpeed = this.windSpeedData[this.environmentalIndex]

          this.environmentalTimer = setInterval(() => {
            //指针指向数组中的下一组数据，为下一次赋值做准备
            this.environmentalIndex++
            var index = this.environmentalIndex
            //如果存在气象环境数据
            //让指针对数组长度取余，保证不出现数组越界情况
            index %= this.environmentalXAxis.length
            //将数组中的数据给对应的气象
            this.environmentCardId = this.environmentalXAxis[index]
            this.pm10 = this.pm10Data[index]
            this.pm2_5 = this.pm2_5Data[index]
            this.temperature = this.temperatureData[index]
            this.noise = this.noiseData[index]
            this.humidity = this.humidityData[index]
            this.sensorBrightness = this.sensorBrightnessData[index]
            this.windDirection = this.windDirectionData[index]
            this.windSpeed = this.windSpeedData[index]
          }, 1000 * 4)
        }

    },
    electricityDataPolling() {
        if (this.electricityDataList.length > 0) {
          this.electricityCardId = this.electricityDataList[this.electricityIndex].cardId
          this.electricity = this.electricityDataList[this.electricityIndex].electricity
          this.current = this.electricityDataList[this.electricityIndex].current
          this.power = this.electricityDataList[this.electricityIndex].power
          this.voltage = this.electricityDataList[this.electricityIndex].voltage

          this.electricityTimer = setInterval(() => {
            this.electricityIndex++
            var index = this.electricityIndex
            //如果存在气象环境数据
            if (this.electricityDataList.length > 0) {
              //让指针对数组长度取余，保证不出现数组越界情况
              index %= this.electricityDataList.length
              //将数组中的数据给对应的气象
              this.electricityCardId = this.electricityDataList[index].cardId

              this.electricity = this.electricityDataList[index].electricity
              this.current = this.electricityDataList[index].current
              this.power = this.electricityDataList[index].power
              this.voltage = this.electricityDataList[index].voltage
              //指针指向数组中的下一组数据，为下一次赋值做准备

            }
          }, 1000 * 4)
        }
    },

    runenvironmentalData() {
      this.environmentalTimer = setInterval(() => {
        var length = 0;
        if (this.environmentalIndex === 0) {
          this.environmentalIndex += 3
          length = this.environmentalIndex + 3
          if (length > this.environmentalXAxis.length) {
            if (this.environmentalXAxis.length <= 3) {
              this.environmentalIndex = 0
            }
            length = (this.environmentalXAxis.length - this.environmentalIndex) + this.environmentalIndex
          }
        } else {
          if (this.environmentalIndex + 3 < this.environmentalXAxis.length) {
            this.environmentalIndex += 3
            length = this.environmentalIndex + 3
            if (length > this.environmentalXAxis.length) {
              length = (this.environmentalXAxis.length - this.environmentalIndex) + this.environmentalIndex
            }
          } else {
            this.environmentalIndex = 0
            length = this.environmentalIndex + 3
          }
        }
        this.environmentalXAxisShow = []
        this.pm10DataShow = []
        this.pm2_5Show = []
        this.temperatureShow = []
        this.noiseShow = []
        this.humidityShow = []
        this.sensorBrightnessShow = []
        this.windDirectionShow = []
        this.windSpeedShow = []
        for (let i = this.environmentalIndex; i < length; i++) {
          this.environmentalXAxisShow.push(this.environmentalXAxis[i]);
          this.pm10DataShow.push(this.pm10Data[i]);
          this.pm2_5Show.push(this.pm2_5Data[i]);
          this.temperatureShow.push(this.temperatureData[i]);
          this.noiseShow.push(this.noiseData[i]);
          this.humidityShow.push(this.humidityData[i]);
          this.sensorBrightnessShow.push(this.sensorBrightnessData[i]);
          this.windDirectionShow.push(this.windDirectionData[i]);
          this.windSpeedShow.push(this.windSpeedData[i]);
        }
        this.environmentalEChart.setOption(this.environmentalOption);

      }, 1000 * 4)
    }
  },
  mounted() {
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.environmentalTimer)
      clearInterval(this.electricityTimer)
    });
  },
  computed: {
    programOption: {
      get() {
        return {
          title: {
            subtext: this.$t('home.UnitTimes')
          },
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '1%',
            right: '10%',
            bottom: '10%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: this.programDate
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                formatter: (value) => {
                    return /^-?\d+$/.test(value) ? value : '' // 判断y轴数值为整数的才显示
                }
              }
            }
          ],
          series: [
            {
              type: 'bar',
              data: this.programData
            }
          ]
        }
      }
    },
    operationLogOption: {
      get() {
        return {
          title: {
            subtext: this.$t('home.UnitTimes')
          },
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '1%',
            right: '10%',
            bottom: '10%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.operationLogDate
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              name: this.$t('log.requestSucceeded'),
              type: 'line',
              // stack: 'Total',
              data: this.operationSuccessData
            },
            {
              name: this.$t('bigScreen.RequestFailed'),
              type: 'line',
              // stack: 'Total',
              data: this.operationFailData
            }
          ]
        }
      }
    },
    environmentalOption: {
      get() {
        return {
          grid: {
            left: '10%',
            right: '10%',
            bottom: '10%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.environmentalXAxisShow,
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              name: 'PM10',
              type: 'line',
              data: this.pm10DataShow,
            },
            {
              name: 'PM2.5',
              type: 'line',
              data: this.pm2_5Show,
            },
            {
              name: this.$t('cardDevice.temperature'),
              type: 'line',
              data: this.temperatureShow,
            },
            {
              name: this.$t('program.noise'),
              type: 'line',
              data: this.noiseShow,
            },
            {
              name: this.$t('card.humidity'),
              type: 'line',
              data: this.humidityShow,
            },
            {
              name: this.$t('meteorological.Illuminance'),
              type: 'line',
              data: this.sensorBrightnessShow,
            },
            {
              name: this.$t('program.windDirection'),
              type: 'line',
              data: this.windDirectionShow,
            },
            {
              name: this.$t('program.windSpeed'),
              type: 'line',
              data: this.windSpeedShow,
            },
          ]
        }
      }
    },
    electricityDataConfig: {
      get() {
        return {
          data:[
          {
            name: this.$t('electricity.electricity'),
            value: this.electricity
          },
          {
            name: this.$t('electricity.current'),
            value: this.current
          },
          {
            name: this.$t('electricity.power'),
            value: this.power
          },
          {
            name: this.$t('electricity.voltage'),
            value: this.voltage
          },
        ],
          showValue: true
        }
      }
    }

  }
}
</script>

<style scoped>
.top-content {
  height: 75%;
  width: 90%;
  position: absolute;
  bottom: -34%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.bottom-content {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 50%;
  text-align: center;
  font-weight: bold;
  font-size: x-large;
}
.meteorological_info {
  height: 20%;
  display: flex;
  justify-content: center;
}


.meteorological_info_detail {
  width: 30%;
  height: 100%;
  flex: 1;
}

</style>
