<template>
    <div style="height: 100%;padding: 0 8px">
        <div style="height: 13%;">
            <dv-border-box-13>
                <Row type="flex" justify="center" align="bottom" class="top-row-bg">
                    <Col span="8" v-for="(item, index) in deviceStatusList" :key="index" class="top-col">
                    <div style="color: #00BAFF;height: 70%; font-size: 24px;font-weight: bold;">{{ item.text }}</div>
                    <span :style="{ 'color': item.color, 'font-size': '20px' }">{{ item.count }}</span>
                    </Col>
                </Row>
            </dv-border-box-13>
        </div>
<!--        <div style="height: 38%;">-->
<!--            <dv-border-box-8 :reverse="true">-->
<!--                <div ref="deviceCountData" style="width: 100%; height: 90%; padding-top: 8px;"></div>-->
<!--            </dv-border-box-8>-->
<!--        </div>-->
      <div style="height: 40%;">
        <dv-border-box-1 >
          <div class="top-head-title">{{$t('bigScreen.VideoSurveillance')}}</div>
          <div v-if="videoVisible" class="center-content" style="top: 10%">
            <video id="realPlay" autoplay muted="muted" style="width: 90%; height: 85%; object-fit: fill; margin: 0px auto">
            </video>
          </div>
          <div v-else class="center-content" style="top: 10%;">
            {{ $t('home.temporarilyNoData') }}
          </div>
        </dv-border-box-1>
      </div>
        <div style="height: 47%;">
<!--             <div style="background-color: #000;width: 98%;height: 96%;margin: 5px auto" id="BMap-22426"></div>-->
<!--            <div id="scatterBmap" ref="scatterBmap" style="width: 98%;height: 96%;overflow: hidden;margin-top: 5px;"></div>-->
            <div ref="mapData" style="width: 98%;height: 96%;overflow: hidden;margin-top: 5px;"></div>
            <div id="BMap-22426" style="width: 0;height: 0;overflow: hidden;margin-top: 5px;"></div>
        </div>
    </div>
</template>

<script>
import loadBMap from '../../../utils/loadMap'
import "echarts/extension/bmap/bmap"
import flvjs from "flv.js";
import 'echarts-extension-gmap';
import {generateToken} from '@/utils/jwtUtils'
export default {
    data() {
        return {
            deviceStatusList: [],
            date: [],
            lightPoleCountData: [],// 上线数
            offlineData: [],// 离线
            smartScreenData: [],// 智慧屏幕
            smartBroadcastData: [],// 智慧广播
            smartMonitoringData: [],// 智慧监控
            meteorologicalEnvironmentData: [],// 气象环境
            passengerStatisticsData: [],// 客流统计
            smartLightingData: [],// 智慧照明
            electricityData: [],// 电能管理
            radarData:[],//雷达测速
            deviceCountEChart: '',

            mapEChart:'',
            /**
             * 地图上不在线的设备
             */
            mapPoints: [],
            /**
             * 地图上在线的设备
             */
            onlinemapPoints: [],
            //地图数据
            mapData: {
                //中心坐标
                center: { lng: 114.06455184, lat: 22.54845664 },
                //缩放级别
                zoom: 14
            },
            myMap: null,
            mapPointIndex: 0,
            mapPointTimer: null,
            //  监控设备集合
            deviceList:[],
            //监控设备id
            deviceId:'',
            //播放器
            flvPlayer: null,
            //监控定时
            monitorTimer:null,
            //监控轮询指针
            monitorIndex:0,
            //监控是否可见
            videoVisible:true,
            lang:"",
            onlineMonitorDeviceList:[]
        }
    },
  methods: {
        getData(bigScreen) {
            if (bigScreen) {
                if (bigScreen.deviceStatusList) {
                    var deviceStatusList = bigScreen.deviceStatusList
                    for (let i = 0; i < deviceStatusList.length; i++) {
                        const element = deviceStatusList[i];
                        if (element.name === 'name') {
                            this.deviceStatusList.push({ 'text': this.$t('home.TotalNumberOfPoles'), 'count': element.count, 'color': '#fff' })
                        } else if (element.name === 'line') {
                            this.deviceStatusList.push({ 'text': this.$t('lamp.online'), 'count': element.count, 'color': '#00FF00' })
                        } else if (element.name === 'offline') {
                            this.deviceStatusList.push({ 'text': this.$t('lamp.offline'), 'count': element.count, 'color': '#CCCCCC' })
                        }
                    }
                }
                if (bigScreen.deviceCountData) {
                    this.date = bigScreen.deviceCountData.date
                    // 上线数
                    this.lightPoleCountData = bigScreen.deviceCountData.lightPoleCountData
                    // 离线
                    this.offlineData = bigScreen.deviceCountData.offlineData
                    // 智慧屏幕
                    this.smartScreenData = bigScreen.deviceCountData.smartScreenData
                    // 智慧广播
                    this.smartBroadcastData = bigScreen.deviceCountData.smartBroadcastData
                    // 智慧监控
                    this.smartMonitoringData = bigScreen.deviceCountData.smartMonitoringData
                    // 气象环境
                    this.meteorologicalEnvironmentData = bigScreen.deviceCountData.meteorologicalEnvironmentData
                    // 客流统计
                    this.passengerStatisticsData = bigScreen.deviceCountData.passengerStatisticsData
                    // 智慧照明
                    this.smartLightingData = bigScreen.deviceCountData.smartLightingData
                    // 智慧照明
                    this.electricityData = bigScreen.deviceCountData.electricityData
                    //雷达测速
                    this.radarData=bigScreen.deviceCountData.radarData
                }
                if (bigScreen.mapPoints) {
                    // this.mapPoints = bigScreen.mapPoints;
                    bigScreen.mapPoints.forEach(element => {
                        if (element.isOn == 1) {
                            this.onlinemapPoints.push({
                                name: element.deviceId,
                                value: [Number.parseFloat(element.longitude),Number.parseFloat(element.latitude) ,1]
                            })
                        } else {
                            this.mapPoints.push({
                                name: element.deviceId,
                                value: [Number.parseFloat(element.longitude),Number.parseFloat(element.latitude) ,1]
                            })
                        }
                    });
                    // console.log(this.mapPoints);
                }
                // this.myEcharts()
                // this.initMap()
              if (this.lang=='cn'){
                this.getLocationNow()
              }else {
                this.myEchartsMap()
              }
             // this.myEchartsMap()

              //验证是否有监控权限
              if(this.isAuth("lampPole:monitoringControl")){
                this.getDeviceList()
              }else {
                this.videoVisible=false
              }
            }
        },
        // myEcharts() {
        //     // 温度
        //     if (this.deviceCountEChart != null && this.deviceCountEChart != "" && this.deviceCountEChart != undefined) {
        //         this.deviceCountEChart.dispose();//销毁
        //     }
        //     this.deviceCountEChart = this.$echarts.init(this.$refs.deviceCountData);
        //     this.deviceCountEChart.setOption(this.deviceCountOption);
        // },
        /**
         * 大屏中间地图展示
         */
        myEchartsMap() {
          if (this.mapEChart != null && this.mapEChart != "" && this.mapEChart != undefined) {
            this.mapEChart.dispose();//销毁
          }
          this.mapEChart=this.$echarts.init(this.$refs.mapData)
          this.mapEChart.resize()
          if (this.lang=='cn'){
            this.mapEChart.setOption(this.mapOption)
          }else {
            this.loadGoogleMapScript().then(google => {
              //获取当前定位
              if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                  (position) => {
                    const pos = {
                      lat: position.coords.latitude,
                      lng: position.coords.longitude,
                    };
                    // console.log("谷歌地图：")
                    // console.log(pos)
                    this.googleOption.gmap.center=pos
                    this.googleOption.gmap.zoom=11
                    // const geocoder = new google.maps.Geocoder();
                    // geocoder.geocode({ location: pos })
                    // .then((response)=>{
                    //   if (response.results[0]) {
                    //     console.log(response)
                    //     console.log("详细地址")
                    //     console.log(response.results[0])
                    //     console.log(response.results[0].geometry.location)
                    //   }
                    // })
                    this.mapEChart.setOption(this.googleOption)
                  },
                );
              } else {
                // Browser doesn't support Geolocation
                // handleLocationError(false, infoWindow, map.getCenter());
              }

            })
          }
        },

        //在地图上选择区域
        getLocation(mapPoint) {
            //清除地图上所有的覆盖物(保证每次点击只有一个标记)
            this.myMap.clearOverlays()
            //创建定位标记
            var point = new BMap.Point(mapPoint.longitude, mapPoint.latitude)

            let marker = new BMap.Marker(point)
            //将标记添加到地图上
            this.myMap.addOverlay(marker)
            this.myMap.centerAndZoom(new BMap.Point(mapPoint.longitude, mapPoint.latitude), this.mapData.zoom)
            //创建坐标解析对象
            let geoc = new BMap.Geocoder()
            var address;
            //解析当前的坐标成地址
            geoc.getLocation(point, (rs) => {
                //获取地址对象
                let addressComp = rs.addressComponents
                //拼接出详细地址
                address = addressComp.province +
                    addressComp.city +
                    addressComp.district +
                    addressComp.street +
                    addressComp.streetNumber

                var xlink = 'on-line'
                if (mapPoint.isOn === 1) {
                    xlink = 'on-line'
                } else {
                    xlink = 'line'
                }
                var content = '<div>'
                content += '<span>' + this.$t('lamp.poleName') + '：</span>' + mapPoint.alias + '<br/>'
                content += '<span>ID：</span>' + mapPoint.deviceId + '<br/>'
                content += '<span>' + this.$t('bigScreen.Address') + '：</span>' + address + '<br/>'
                content += '<span>' + this.$t('lamp.online') + '：</span>' + '<svg width="20px" height="20px" aria-hidden="true" style="vertical-align: middle;"><use xlink:href="#' + xlink + '"></use></svg>'
                content += '</div>'
                var opts = {
                    width: 250,     // 信息窗口宽度
                    height: 100,     // 信息窗口高度
                    offset: new BMap.Size(0, -10)
                }
                var infoWindow = new BMap.InfoWindow("", opts);  // 创建信息窗口对象
                infoWindow.setContent(content)
                this.myMap.openInfoWindow(infoWindow, this.myMap.getCenter());      // 打开信息窗口
            })
        },
        initMap() {
            loadBMap().then(() => {
                // 百度地图API功能
                this.myMap = new BMap.Map("BMap-22426") // 创建Map实例
                this.myMap.setMapStyle({ style: 'grassgreen' })

                //将标记添加到地图上
                this.myMap.centerAndZoom(new BMap.Point(this.mapData.center.lng, this.mapData.center.lat), this.mapData.zoom)
                this.myMap.enableScrollWheelZoom(true) //开启鼠标滚轮缩放
                if (this.mapPoints && this.mapPoints.length > 0) {
                    var than = this
                    than.getLocation(than.mapPoints[than.mapPointIndex])
                    than.mapPointIndex++
                    this.mapPointTimer = setInterval(() => {
                        than.getLocation(than.mapPoints[than.mapPointIndex])
                        if (than.mapPointIndex < than.mapPoints.length - 1) {
                            than.mapPointIndex++
                        } else {
                            than.mapPointIndex = 0
                        }
                    }, 1000 * 5);
                }
                // 添加比例尺控件
                var scaleCtrl = new BMap.ScaleControl({
                    // 控件的停靠位置（可选，默认左上角）
                    anchor: BMAP_ANCHOR_BOTTOM_LEFT,
                    // 控件基于停靠位置的偏移量（可选）
                    offset: new BMap.Size(10, 10)
                });
                this.myMap.addControl(scaleCtrl);

                // 添加缩放控件
                var navigationControl = new BMap.NavigationControl({
                    anchor: BMAP_ANCHOR_TOP_RIGHT,
                    offset: new BMap.Size(10, 10)
                });
                this.myMap.addControl(navigationControl);

            }).catch(err => {
                console.log('地图加载失败')
            })
        },

      //获取位置
      getLocationNow() {
        loadBMap().then(() => {
          // 百度地图API功能
          this.myMap = new BMap.Map('BMap-22426') // 创建Map实例用来获取当前城市的经纬度
          // this.getCurCity()
          this.getCurCity(() => {
           //初始化echarts
           this.myEchartsMap()
          },{timeout:10000,});
        }).catch(err => {
          console.log(err)
          console.log('地图加载失败')
        })
      },
       //获取当前城市的经纬度
       getCurCity(callback){



//          直接将当前位置设置为中心
/*
        // 创建地理定位实例
        var geolocation = new BMap.Geolocation();
         // 开始获取当前定位信息
         geolocation.getCurrentPosition(
           (position) => {
             // 将经纬度转换为百度地图坐标
             const point = new BMap.Point(position.longitude, position.latitude);
             // var curCity=results.getPoi(0).point
             console.log(point)
             this.mapOption.bmap.center=[point.lng, point.lat]
             this.mapOption.bmap.zoom=11
             callback();
           },
           (error) => {
             console.error(error);
           });*/



         //获取当前位置所在市为中心，耗时长
         var geolocation = new BMap.Geolocation();
         geolocation.getCurrentPosition(
           (position) => {
             console.log(position)
             // 将经纬度转换为城市名称
             const geoc =   new BMap.Geocoder();
             geoc.getLocation(
               new BMap.Point(position.longitude, position.latitude),
               (result) => {
                 // 获取城市名称
                 const city = result.addressComponents.city;
                 // 将城市名称转换为城市中心的经纬度
                 const local =  new BMap.LocalSearch(city, {
                   onSearchComplete: (results) => {
                     if (local.getStatus() === BMAP_STATUS_SUCCESS) {
                       // 将地图中心设置为城市中心
                       // console.log("当前城市坐标为：")
                       // console.log(results.getPoi(0).point)
                       var curCity=results.getPoi(0).point
                       this.mapOption.bmap.center=[curCity.lng, curCity.lat]
                       this.mapOption.bmap.zoom=11
                       callback();
                     }
                   },
                 });
                 local.search(city);
               }
             );
           },
           (error) => {
             console.error(error);
           })
      },
      /**
       * 查询所有监控设备
       */
      getDeviceList() {
          this.$http({
            url: this.$http.adornUrl('/monitor/device/list'),
            method: 'get',
            params: this.$http.adornParams({
              'isOn': "1",
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.deviceList = data.page.list
              if (this.deviceList.length > 0) {
                if(this.deviceList){
                  this.onlineMonitorDeviceList= this.deviceList.filter(device=>device.isOn===1).map(device=>{
                    return device.deviceId
                  })
                }
                this.changeDeviceListStreamState(this.onlineMonitorDeviceList,1)
                // if (this.deviceList[0].deviceId) {
                  // this.deviceId = this.deviceList[0].deviceId
                  this.monitorPolling()
              } else {
                this.videoVisible=false
              }
            } else {
              this.deviceList = []
              this.videoVisible=false
            }
          })
        },
      /**
       * 监控画面
       */
        playVideo() {
          this.lastDecodedFrame = 0
          // 加载前先销毁
          let videoWin = document.getElementById("realPlay");
          if (flvjs.isSupported()) {
            this.flvPlayer = flvjs.createPlayer({
              type: "flv",// 媒体类型
              isLive: true,//是否是实时流
              hasAudio: false,//是否有音频
              url: window.SITE_CONFIG.rtmp + this.deviceId +"&token="+ generateToken(this.deviceId),// 视频流地址
              stashInitialSize: 128 // 减少首帧显示等待时长
            }, {
              enableWorker: false,// 不启动分离线程
              enableStashBuffer: false,// 关闭IO隐藏缓冲区
              reuseRedirectedURL: true,// 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
              autoCleanupSourceBuffer: true, // 自动清除缓存
              fixAudioTimestampGap: false,// false 音频同步
            });
            // 断流重连
            this.flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
              console.log("errorType:", errorType);
              console.log("errorDetail:", errorDetail);
              console.log("errorInfo:", errorInfo);
              if (this.flvPlayer) {
                this.destroyVideo();
              }
            });
            // 画面卡死重连
            var than = this
            this.flvPlayer.on("statistics_info", function (res) {
              if (than.lastDecodedFrame == 0) {
                than.lastDecodedFrame = res.decodedFrames;
                return;
              }
              if (than.lastDecodedFrame != res.decodedFrames) {
                than.lastDecodedFrame = res.decodedFrames;
              } else {
                than.lastDecodedFrame = 0;
                if (than.flvPlayer) {
                  this.numVisible = true
                  than.destroyVideo();
                  than.playVideo()

                }
              }
            });
            this.flvPlayer.attachMediaElement(videoWin);
            this.flvPlayer.load();
            let playPromise = this.flvPlayer.play();
            if (playPromise !== undefined) {
              playPromise.then(() => {
                this.flvPlayer.play()
                this.numVisible = true
              }).catch(() => {

              })
            }
          }
        },
        //销毁断流方法
        destroyVideo() {
          if (this.flvPlayer !== null) {
            this.flvPlayer.pause();
            this.flvPlayer.unload();
            this.flvPlayer.detachMediaElement();
            this.flvPlayer.destroy();
            this.flvPlayer = null;
          }
        },
        //监控画面轮询,120秒切换一次
        monitorPolling() {
        //  开始播放第一个监控画面
        this.deviceId = this.deviceList[this.monitorIndex].deviceId
        // this.destroyVideo()
        this.playVideo()

        this.monitorTimer = setInterval(() => {
          this.monitorIndex++
          var index = this.monitorIndex
          //如果存在监控设备
          if (this.deviceList.length > 0) {
            //让指针对数组长度取余，保证不出现数组越界情况
            index %= this.deviceList.length
            //将数组中的设备id赋值给要播放的设备id
            this.deviceId = this.deviceList[index].deviceId
            //指针指向数组中的下一组数据，为下一次赋值做准备
            this.destroyVideo()
            this.playVideo()
          }
        }, 1000 * 120)
      },
        //判断是否加载谷歌地图api
        isGoogleMapAPILoaded() {
          return typeof google !== 'undefined' && typeof google.maps !== 'undefined'
        },
        //加载谷歌地图
        loadGoogleMapScript() {
          const mapScriptURL = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyC7b2_5RxBtvTjnFPmLrzOa_nmXFdALQU8'
          return new Promise((resolve, reject) => {
            // if google map API script has loaded, no need to insert it again
            if (this.isGoogleMapAPILoaded()) {
              resolve(google)
            } else {
              const scriptNode = document.createElement('script')
              scriptNode.setAttribute('type', 'text/javascript')
              scriptNode.setAttribute('charset', 'utf-8')
              scriptNode.setAttribute('src', mapScriptURL)
              scriptNode.setAttribute('id', 'google-map-api-script')
              scriptNode.onload = () => {
                if (this.isGoogleMapAPILoaded()) {
                  resolve(google)
                } else {
                  reject('failed to load google map API script')
                }
              }
              scriptNode.onerror = e => {
                reject(e)
              }
              // check again before inserting script
              if (this.isGoogleMapAPILoaded()) {
                resolve(google)
              } else {
                document.head.appendChild(scriptNode)
              }
            }
          })
        },
        // 修改在线推流状态
        changeDeviceListStreamState(onlineDeviceList,state) {
          if (onlineDeviceList.length > 0) {
            for (let i = 0; i < onlineDeviceList.length; i++) {
              this.$http({
                url: this.$http.adornUrl(`/monitor/device/rtmpStreamState`),
                method: 'get',
                params: {deviceId: onlineDeviceList[i], state: state}
              }).then(({data}) => {
                if (data && data.code === 0) {
                  if (data.msg.msg === 'success') {
                  } else {
                    console.log(data.msg.msg)
                  }
                } else {
                  console.log(data.msg)
                }
              });
            }
          }
        },
        handleBeforeUnload(event) {
          this.changeDeviceListStreamState(this.onlineMonitorDeviceList, 0);
        },
    },
    mounted() {
      this.lang=localStorage.getItem('language');

      // if (this.lang=='cn'){
      //   loadBMap().then(() => {
      //     this.myEchartsMap()
      //   })
      // }

        this.$once('hook:beforeDestroy', () => {
            clearInterval(this.mapPointTimer)
            clearInterval(this.monitorTimer)
            this.destroyVideo()
        });
    },

    computed: {
        deviceCountOption: {
            get() {
                return {
                    legend: {
                        textStyle: {
                            fontSize: 13,//字体大小
                            color: '#ffffff'//字体颜色
                        },
                    },
                    color: ['#00FF00', '#CCCCCC', '#1E90FF', '#DA70D6', '#EE82EE', '#9932CC', '#483D8B', '#00BFFF', '#FFD700','#00FFD0FF'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                            label: {
                                show: true
                            }
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '7%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: this.date
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            name: this.$t('bigScreen.NumberOfOnline'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.lightPoleCountData
                        },
                        {
                            name: this.$t('lamp.offline'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.offlineData
                        },
                        {
                            name: this.$t('nav.智慧屏幕'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.smartScreenData
                        },
                        {
                            name: this.$t('nav.智慧广播'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.smartBroadcastData
                        },
                        {
                            name: this.$t('nav.智慧监控'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.smartMonitoringData
                        },
                        {
                            name: this.$t('nav.气象环境'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.meteorologicalEnvironmentData
                        },
                        {
                            name: this.$t('nav.客流统计'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.passengerStatisticsData
                        },
                        {
                            name: this.$t('nav.智慧照明'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.smartLightingData
                        },
                        {
                            name: this.$t('nav.电能管理'),
                            type: 'line',
                            // stack: 'Total',
                            data: this.electricityData
                        },
                      {
                        name: this.$t('nav.雷达测速'),
                        type: 'line',
                        // stack: 'Total',
                        data: this.radarData
                      },
                    ]
                }
            }
        },
        mapOption:{
          get(){
            return{
              // tooltip: {
              //     trigger: "item"
              // },
              bmap: {
                key: 'RQtRYAXG5Uw6CNToTqAwYePzyds1iPD5',
                center: [105, 15],
                zoom: 5,
                roam: true,
                // mapStyle: {
                //   style:'midnight'
                // },
                // mapStyleV2: {
                // },
              },
              series: [
                {
                  type: 'scatter',
                  coordinateSystem: 'bmap',
                  data: this.mapPoints,
                  encode: {
                    value: 2
                  },
                  label: {
                    formatter: "{b}",
                    position: "right",
                    show: true
                  },
                  emphasis: {
                    label: {
                      show: true
                    }
                  }
                },
                {
                  type: 'effectScatter',
                  color:'#3ba308',
                  coordinateSystem: 'bmap',
                  data:  this.onlinemapPoints,
                  label: {
                    formatter: "{b}",
                    position: "right",
                    show: true
                  },
                  emphasis: {
                    label: {
                      show: true
                    }
                  }
                }
              ]
            }
          }
      },
        googleOption:{
          get(){
            return{
              // load gmap component
              gmap: {
                center: { lng: 0, lat: 0 },
                // center: { lng: 108.39, lat: 39.9 },
                zoom: 2,
                disableDefaultUI:true,
                renderOnMoving: true,
                // the zIndex of echarts layer for Google Map. `2000` by default.
                echartsLayerZIndex: 2019,
                // whether to enable gesture handling. `true` by default.
                // since v1.4.0
                roam: true,
                minZoom: 2,
                maxZoom: 16,
                //限制拖拽范围，防止出现除地图外的灰色区域
                restriction: {
                  latLngBounds: {
                    north: 85,
                    south: -85,
                    east: 180,
                    west: -180,
                  },
                },
                styles: [
                  { elementType: "geometry", stylers: [{ color: "#242f3e" }] },
                  { elementType: "labels.text.stroke", stylers: [{ color: "#242f3e" }] },
                  { elementType: "labels.text.fill", stylers: [{ color: "#746855" }] },
                  {
                    featureType: "administrative.locality",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#d59563" }],
                  },
                  {
                    featureType: "poi",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#d59563" }],
                  },
                  {
                    featureType: "poi.park",
                    elementType: "geometry",
                    stylers: [{ color: "#263c3f" }],
                  },
                  {
                    featureType: "poi.park",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#6b9a76" }],
                  },
                  {
                    featureType: "road",
                    elementType: "geometry",
                    stylers: [{ color: "#38414e" }],
                  },
                  {
                    featureType: "road",
                    elementType: "geometry.stroke",
                    stylers: [{ color: "#212a37" }],
                  },
                  {
                    featureType: "road",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#9ca5b3" }],
                  },
                  {
                    featureType: "road.highway",
                    elementType: "geometry",
                    stylers: [{ color: "#746855" }],
                  },
                  {
                    featureType: "road.highway",
                    elementType: "geometry.stroke",
                    stylers: [{ color: "#1f2835" }],
                  },
                  {
                    featureType: "road.highway",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#f3d19c" }],
                  },
                  {
                    featureType: "transit",
                    elementType: "geometry",
                    stylers: [{ color: "#2f3948" }],
                  },
                  {
                    featureType: "transit.station",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#d59563" }],
                  },
                  {
                    featureType: "water",
                    elementType: "geometry",
                    stylers: [{ color: "#17263c" }],
                  },
                  {
                    featureType: "water",
                    elementType: "labels.text.fill",
                    stylers: [{ color: "#515c6d" }],
                  },
                  {
                    featureType: "water",
                    elementType: "labels.text.stroke",
                    stylers: [{ color: "#17263c" }],
                  },
                ],
              },
              series: [
                {
                  type: 'scatter',
                  coordinateSystem: 'gmap',
                  data: this.mapPoints,
                  encode: {
                    value: 2
                  },
                  label: {
                    formatter: "{b}",
                    position: "top",
                    show: true
                  },
                  emphasis: {
                    label: {
                      show: true
                    }
                  }
                },
                {
                  type: 'effectScatter',
                  color:'#3ba308',
                  coordinateSystem: 'gmap',
                  data: this.onlinemapPoints,
                  label: {
                    formatter: "{b}",
                    position: "top",
                    show: true
                  },
                  emphasis: {
                    label: {
                      show: true
                    }
                  }
                }
              ]
            }
          }
      }
    }
}
</script>
<style>
.anchorBL img {
    display: none;
}
.ec-extension-google-map{
  height: 50%;
  margin-top: 54%;
}

.BMap_cpyCtrl {
    display: none;
    visibility: hidden;
}
</style>
<style  scoped>
.center-content {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 50%;
  text-align: center;
  font-size: x-large;
  font-weight: bold;
}
.top-row-bg {
    height: 80%;
    width: 80%;
    margin: 0 auto;
}

.top-col {
    text-align: center;
    height: 70%;
}
.top-head-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
}
.top-content {
  width:100%;
  height:100%;
  margin: 5px auto;
  /*background-color: #000;*/
  border-radius: 2%;
}
</style>
