<template>
    <div class="video-tutorial">
        <!-- 添加搜索框 -->
        <div class="top">
            <svg class="video-tutorial-svg" aria-hidden="true">
                <use xlink:href="#videoTutorial"></use>
            </svg>
            <div class="video-tutorial-text">
                <span>{{$t('manual.videoTutorial')}}</span>
            </div>
        </div>
        <Input prefix="ios-search" v-model="searchQuery" @on-change="searchQueryChange" :placeholder="$t('common.search')"/>
        <div v-if="lang == 'cn' || lang == 'tw'"  class="video-tutorial-content">
            <el-card class="video-tutorial-item" shadow="hover" v-for="(item, index) in videoTutorial" :key="index">
                <video width="100%" height="100%" controls :src="item.url"></video>
                <div class="video-tutorial-item-title">{{ item.title }}</div>
            </el-card>
        </div>
        <div v-else  class="video-tutorial-content">
            <el-card class="video-tutorial-item" shadow="hover" v-for="(item, index) in usVideoTutorial" :key="index">
                <video width="100%" height="100%" controls :src="item.url"></video>
                <div class="video-tutorial-item-title">{{ item.title }}</div>
            </el-card>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            videoTutorial: [
                {
                    title: '注册及公司ID的绑定',
                    url: 'https://ledok.cn:8443/download/LEDOK IoT注册及公司ID的绑定.mp4'
                },
                {
                    title: '入网配置',
                    url: 'https://ledok.cn:8443/download/LEDOK IoT入网配置.mp4'
                },
                {
                    title: '发布广播节目',
                    url: 'https://ledok.cn:8443/download/LEDOK IoT发布广播节目.mp4'
                },
                {
                    title: '发布屏体节目',
                    url: 'https://ledok.cn:8443/download/LEDOK IoT发布屏体节目.mp4'
                }
            ],
            usVideoTutorial: [
                {
                    title: 'Register',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK Cloud IOT Register.mp4'
                },
                {
                    title: 'Online update',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Online update.mp4'
                },
                {
                    title: 'Time & Date',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Time &Date.mp4'
                },
                {
                    title: 'Hardware parameters',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Hardware parameters.mp4'
                },
                {
                    title: 'Bg Image function',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Bg Image function.mp4'
                },
                {
                    title: 'Brightness setup',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT brightness setup.mp4'
                },
                {
                    title: 'Alarm function',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Alarm function.mp4'
                },
                {
                    title: 'Clear program',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Clear program.mp4'
                },
                {
                    title: 'Export SIM information',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Export SIM information.mp4'
                },
                {
                    title: 'Live video',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Live video.mp4'
                },
                {
                    title: 'Restart function',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Restart function.mp4'
                },
                {
                    title: 'Screen switch',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Screen switch.mp4'
                },
                {
                    title: 'Screenshot function',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Screenshot function.mp4'
                },
                {
                    title: 'Sync & Asyncn',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Sync&Async.mp4'
                },
                {
                    title: 'Volume setup',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK IOT Volume setup.mp4'
                },
                {
                    title: 'Broadcast program',
                    url: 'https://ledok.cn:8443/download/iot/en/LEDOK Cloud IOT Broadcast program.mp4'
                },
            ],
            videoTutorialCopy: [],
            usVideoTutorialCopy: [],
            lang: "cn",
            searchQuery: '' // 新增搜索关键词
        }
    },
    mounted() {
        this.lang = localStorage.getItem('locale');
        this.videoTutorialCopy = this.videoTutorial;
        this.usVideoTutorialCopy = this.usVideoTutorial;
    },
    methods: {
        searchQueryChange() {
            if (this.lang == 'cn' || this.lang == 'tw') {
                this.videoTutorial = this.videoTutorialCopy; // 重置数据为原始数据，避免重复过滤
                this.videoTutorial = this.filteredVideoTutorial;
            } else {
                this.usVideoTutorial = this.usVideoTutorialCopy; // 重置数据为原始数据，避免重复过滤
                this.usVideoTutorial = this.filteredUsVideoTutorial;
            }
        },
    },
    computed: {
        // 计算属性，用于过滤中文视频教程
        filteredVideoTutorial() {
            // 过滤中文视频教程，根据搜索关键词进行过滤
            return this.videoTutorial.filter(item => {
                return item.title.toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        },
        // 计算属性，用于过滤英文视频教程
        filteredUsVideoTutorial() {
            // 过滤英文视频教程，根据搜索关键词进行过滤
            return this.usVideoTutorial.filter(item => {
                return item.title.toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        }
    }
}
</script>
<style scoped>
.video-tutorial{
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.top {
    height: 70px;
    padding-left: 20px;
    width: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
}
.video-tutorial-svg {
    display: inline-block;
    width: 60px;
    height: 60px;
}
.video-tutorial-text {
    display: inline-block;
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-left: 20px;
}
.video-tutorial-content {
    width: 100%;
    height: 87%;
    overflow-y: auto;
    padding: 0 20px;
}
.video-tutorial-item {
    display: inline-block;
    width: 580px;
    height: 370px;
    margin-top: 20px;
    margin-right: 10px;
}
.video-tutorial-item-title {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
}
</style>