<template>
  <Modal v-model="visible" fullscreen :mask-closable="false">
    <p slot="header" style="text-align:center">
      <span>{{ $t("manual.manualAndTutorial") }}</span>
    </p>
    <div class="tabs-style">
      <Tabs type="card" :value="tabName"  @on-click="handlerTab" :animated="false">
        <TabPane :label="$t('common.manual')" name="pdf" >
          <div style="overflow: hidden;overflow-y: auto">
            <Row>
              <Col span="11">
                <Card dis-hover>
                  <p slot="title" style="text-align:center">中文版</p>
                  <Card dis-hover v-if="zhDataList.length>0&&switchAuth(item.type)" v-for="(item,index) in zhDataList" :key="index" style="margin-top: 10px">
                    <p slot="title" style="text-align:center">{{ switchType(item.type) }}</p>
                      <ul v-for="(file,index) in item.fileList" :key="index" >
                        <li>
                          <a @click="openFile(file.fileId)">{{ file.fileName }}</a>
                        </li>
                      </ul>
                  </Card>
                </Card>
              </Col>
              <Col span="11" offset="2">
                <Card dis-hover>
                  <p slot="title" style="text-align:center">English Version</p>
                  <Card dis-hover v-if="enDataList.length>0&&switchAuth(item.type)" v-for="(item,index) in enDataList" :key="index" style="margin-top: 10px">
                    <p slot="title" style="text-align:center">{{ switchType(item.type) }}</p>
                    <ul v-for="(file,index) in item.fileList" :key="index">
                      <li>
                        <a @click="openFile(file.fileId)">{{ file.fileName }}</a>
                      </li>
                    </ul>
                  </Card>
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>
        <TabPane :label="$t('manual.videoTutorial')" name="video">
          <div style="overflow: hidden;overflow-y: auto">
            <Row>
              <Col span="11">
                <Card dis-hover>
                  <p slot="title" style="text-align:center">中文版</p>
                  <Card dis-hover v-if="zhVideoList.length>0&&switchAuth(item.type)"
                    v-for="(item,index) in zhVideoList" :key="index" style="margin-top: 10px">
                    <p slot="title" style="text-align:center">{{ switchType(item.type) }}</p>
                    <div>
                      <ul>
                        <li v-for="(file,index) in item.fileList" :key="index" style="margin: 10px ;display: inline-block">
                          <h5>{{file.fileName}}</h5>
                          <!-- <video ref="videoPlayer" controls width="320" height="180" preload="auto" webkit-playsinline playsinline crossOrigin="anonymous">
                            <source :src="downloadUrl+ file.fileId+ '?token=' + token" type="video/mp4">
                            Your browser does not support the video tag.
                          </video> -->
                          <div class="videoDiv" @click="videoPlay(file.fileId, file.fileName)">
                            <img width="320" height="180" src="@/assets/img/videoCover.png">
                            <div class="videoIcon"><Icon type="md-play" size="35"/></div>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </Card>
                </Card>
              </Col>
              <Col span="11" offset="2">
                <Card dis-hover>
                  <p slot="title" style="text-align:center">English Version</p>
                  <Card dis-hover v-if="enVideoList.length>0&&switchAuth(item.type)" v-for="(item,index) in enVideoList" :key="index" style="margin-top: 10px">
                    <p slot="title" style="text-align:center">{{ switchType(item.type) }}</p>
                    <div>
                      <ul>
                        <li v-for="(file,index) in item.fileList" :key="index" style="margin: 10px ;display: inline-block">
                          <h5>{{file.fileName}}</h5>
                          <!-- <video ref="videoPlayer" controls width="320" height="180" preload="auto" webkit-playsinline playsinline crossOrigin="anonymous">
                            <source :src="downloadUrl+ file.fileId+ '?token=' + token" type="video/mp4">
                            Your browser does not support the video tag.
                          </video> -->
                          <div class="videoDiv" @click="videoPlay(file.fileId, file.fileName)">
                            <img width="320" height="180" src="@/assets/img/videoCover.png">
                            <div class="videoIcon"><Icon type="md-play" size="35"/></div>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </Card>
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>
        <TabPane v-if="userId==2" :label="$t('manual.resourceManagement')"  name="manager">
          <Form :inline="true" >
            <FormItem>
              <Input size="large" v-model="key" :placeholder="$t('file.name')"></Input>
            </FormItem>
            <FormItem>
              <Button style="margin-right:6px" @click="manualPageList()"  size="large">
                <div style="margin:3px 8px">{{$t('common.query')}}</div>
              </Button>
              <Button v-if="isAuth('lampPole:device:delete')" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="toDelete()">
                <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
              </Button>
            </FormItem>
            <FormItem>
              <Button size="large" type="primary" style="float: right" @click="uploadFormHandler()">{{ $t('screen.uploadFile') }}</Button>
            </FormItem>
          </Form>
          <Table border :columns="dataColumns" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
                :loading="dataListLoading" :height="tableHeightData" ref="selection">
            <template slot-scope="{ row, index }" slot="number">
              {{index+1}}
            </template>
            <template slot-scope="{ row }" slot="fileSize">
              <span>{{ row.fileSize | filterType }}</span>
            </template>
            <template slot-scope="{ row }" slot="fileType">
              <span v-if="row.fileType===1">pdf</span>
              <span v-else>{{ $t('screen.video') }}</span>
            </template>
            <template slot-scope="{ row, index }" slot="operation">
              <Button type="error" size="small" style="margin-left: 5px" @click="toDelete(row.fileId)">{{ $t("common.delete") }}</Button>
            </template>
          </Table>
          <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
                show-elevator show-sizer :page-size-opts="[10,20,50,100,300,500]" show-total
                @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
        </TabPane>
      </Tabs>
    </div>
    <!-- 上传界面 -->
    <Modal v-model="uploadVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{ $t('screen.uploadFile') }}</span>
      </p>
      <Form :model="uploadForm" style="height: 310px">
        <FormItem :label="$t('file.type')">
          <Select v-model="type" style="width:120px">
            <Option v-for="item in typeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem :label="$t('file.fileVersion')">
          <Select v-model="langType" style="width:120px">
            <Option v-for="item in langList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <Loading :loadBoolValue="load"></Loading>
        <FormItem class="upload">
          <Upload
            multiple
            :before-upload="handleUpload"
            :action="''"
            type="drag"
            :format="['pdf','mp4']">
            <div style="padding: 20px 0">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <p>{{ $t('file.attachment1') }}</p>
            </div>
          </Upload>
          <div>
            <ul class="file-list" v-for="(list,index) in uploadForm.manualFile" :key="index">
              <li>{{ $t('file.name') }}: <span style="font-size:15px;">{{ list.name }}</span>
                <Icon type="ios-close" size="20" style="float:right;"
                      @click="uploadForm.manualFile.splice(index,1)"></Icon>
              </li>
            </ul>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button size="large" @click="uploadVisible = false">{{ $t('common.cancel') }}</Button>
        <Button type="primary" @click="uploadFormSubmit">{{ $t('file.upload') }}</Button>
      </div>
    </Modal>
    <!-- 播放 -->
    <Modal v-model="videoPlayModel" footer-hide width="800" @onCancle="videoPlayCancel">
      <template #header>
          <p style="text-align:center">
              <span>{{videoPlayTitle}}</span>
          </p>
      </template>
      <div style="width: 760px; height: 500px;">
        <video ref="videoPlayer" :src="downloadUrl+ videoPlayFileId+ '?token=' + token"
          style='object-fit:fill;width:100%;height:100%;' controls autoplay loop>
        </video>
      </div>
    </Modal>

    <div slot="footer">
    </div>
  </Modal>
</template>

<script>

import axios from "axios";
import Loading from '@/utils/loading'
export default {
  components: {
    Loading
  },

  data() {
    return {
      visible: false,
      userId: null,
      //下载接口
      downloadUrl: this.$http.adornUrl(`/manual/file/download/`),
      zhDataList: [], //文件名列表
      enDataList: [], //文件名列表
      //中文操作视频集合
      zhVideoList: [],
      //英文操作视频
      enVideoList:[],
      token:this.$cookie.get('token'),
      //tab
      tabName: "pdf",
      //表格栏
      dataColumns: [
        {type: 'selection', width: 60,fixed: 'left', align: 'center'},
        {title: this.$t('cardDevice.number'), width: 70,fixed: 'left', align: 'center',slot: 'number',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('file.name'),
          key: "fileName",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('file.name'))
          }
        },
        {
          title: this.$t("file.TheSize"),
          key: "fileSize",
          slot: "fileSize",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('file.TheSize'))
          }
        },
        {
          title: this.$t('menu.type'),
          key: "fileType",
          slot: "fileType",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('menu.type'))
          }
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        }
      ],
      //页数
      pageIndex: 1,
      //一页大小
      pageSize: 10,
      //总页码
      totalPage: 0,
      //查询关键字
      key:'',
      //文件表格loading
      dataListLoading: false,
      //所有文件列表
      dataList: [],
      isFirst:true,
      //选择
      dataListSelections: [],

      tableHeightData: 0,
      //上传文件表单
      uploadForm: {
        manualFile: [] // 临时数组，同时用于显示在页面
      },
      load: false,
      //上传表单可视化
      uploadVisible:false,
      //模块列表
      typeList: [
        {
          value: 0,
          label: this.$t('manual.universal')
        },
        {
          value: 1,
          label: this.$t('nav.智慧物联')
        },
        {
          value: 2,
          label: this.$t('nav.智慧屏幕')
        },
        {
          value: 3,
          label: this.$t('nav.智慧广播')
        },
        {
          value: 4,
          label: this.$t('nav.十字箭头')
        },
        {
          value: 5,
          label: this.$t('nav.气象环境')
        },
        {
          value: 6,
          label: this.$t('nav.智慧照明')
        },
        {
          value: 7,
          label: this.$t('nav.电能管理')
        },
        {
          value: 8,
          label: this.$t('nav.智慧监控')
        },
        {
          value: 9,
          label: this.$t('nav.客流统计')
        },
        {
          value: 10,
          label: this.$t('nav.智慧交通')
        },
        {
          value: 11,
          label: this.$t('nav.WIFI AC')
        },
        {
          value: 12,
          label: this.$t('nav.雷达测速')
        }
    ],
      //文件模块
      type:0,
      //语言类型
      langType:1,
      //语言版本
      langList:[
        {
          value: 1,
          label: '中文版'
        },
        {
          value: 2,
          label: 'English Version'
        }
      ],
      //判断上传的文件是否为pdf
      isPdf:false,
      //判断上传的文件是否为mp4
      isMp4:false,
      //文件类型 1pdf、2视频
      fileType:1,
      videoPlayModel: false,
      videoPlayTitle: '',
      videoPlayFileId:0
    }
  },
  created() {
    // console.log(this.userInfo)
    this.userId=this.userInfo.userId
  },
  methods: {
    videoPlayCancel(){
      console.log('videoPlayCancel')
    },
    // 实现视频播放
    videoPlay(fileId, fileName){
      this.videoPlayModel = true
      this.videoPlayTitle = fileName
      this.videoPlayFileId = fileId
    },
    // 初始化
    init() {
      this.tabName='pdf'
      this.getFileList(1,1);
      this.getFileList(2,1);
      this.videoPlayModel = false
      this.videoPlayTitle = ''
      this.videoPlayFileId = 0

    },
    //初始化表格数据
    initTable(){
      this.dataListLoading=false
      this.dataList=[]
      this.isFirst=true
      this.dataListSelections=[]
      this.pageIndex=1
      this.pageSize=10
      this.totalPage=0
      this.key=''

    },
    //切换tab时触发
    handlerTab (name) {
      this.tabName=name
      if (name==='video'){
        this.zhVideoList= []
        this.enVideoList= []
        this.getVideoList(1,2)
        this.getVideoList(2,2)
      }else if (name==='pdf') {
        this.zhDataList= []
        this.enDataList= []
        this.getFileList(1,1)
        this.getFileList(2,1)
      }else {
        this.initTable()
        this.manualPageList()

      }
    },
    // 获取pdf列表
    getFileList(lang,type) {
      this.$http({
        url: this.$http.adornUrl('/manual/file/list'),
        method: 'get',
        params: this.$http.adornParams({
          'lang':lang,
          'type':type
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (lang===1){
            this.zhDataList = data.list;
          }else if (lang===2){
            this.enDataList = data.list;
          }
        } else {
          if (lang===1){
            this.zhDataList = [];
          }else if (lang===2) {
            this.enDataList = [];
          }
        }
        this.visible = true
      })
    },
    // 获取视频列表
    //lang 语言 type pdf/视频
    getVideoList(lang,type) {
      this.$http({
        url: this.$http.adornUrl('/manual/file/list'),
        method: 'get',
        params: this.$http.adornParams({
          'lang':lang,
          'type':type
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (lang===1){
            this.zhVideoList = data.list;
          }else {
            this.enVideoList = data.list;
          }
        } else {
          if (lang===1){
            this.zhVideoList = [];
          }else {
            this.enVideoList = [];
          }
        }
        this.visible = true
      })
    },
    //pdf
    openFile(val) { // 点击预览按钮事件
      var url=this.downloadUrl+ val+ '?token=' + this.$cookie.get('token')
      // fetch(url)
      //   // val为传入的pdf下载地址链接
      //   .then((response) => response.blob())
      //   .then((res) => {
      //     let blob = new Blob([res], {type: "application/pdf"});
      //     // pdfurl即转化后的结果
      //     // let pdfurl = window.URL.createObjectURL(blob);
      //     // 获取文件名
      //     let file = new File([blob], fileName, {type: "application/pdf"});
      //     // pdfurl即转化后的结果
      //     let pdfurl = window.URL.createObjectURL(file);
      //     // 新标签页打开，即可预览并下载
      //     window.open(pdfurl);
      //   });
      window.open(url);
    },
    switchType(type) {
      var text = ''
      switch (type) {
        case 0 :
          text = this.$t('manual.universal')
          break;
        case 1:
          text =  this.$t('nav.智慧物联')
          break;
        case 2:
          text = this.$t('nav.智慧屏幕')
          break;
        case 3:
          text = this.$t('nav.智慧广播')
          break;
        case 4:
          text = this.$t('nav.十字箭头')
          break;
        case 5:
          text = this.$t('nav.气象环境')
          break;
        case 6:
          text = this.$t('nav.智慧照明')
          break;
        case 7:
          text = this.$t('nav.电能管理')
          break;
        case 8:
          text = this.$t('nav.智慧监控')
          break;
        case 9:
          text = this.$t('nav.客流统计')
          break;
        case 10:
          text = this.$t('nav.智慧交通')
          break;
        case 11:
          text = this.$t('nav.WIFI AC')
          break;
        case 12:
          text = this.$t('nav.雷达测速')
          break;
      }
      return text
    },
    switchAuth(type){
      var flag = ''
      switch (type) {
        case 0 :
          flag = true
          break;
        case 1:
          flag = this.isAuth('lampPole:device:list')
          break;
        case 2:
          flag = true
          break;
        case 3:
          flag = this.isAuth('lampPole:broadcastControl')
          break;
        case 4:
          flag = this.isAuth('crossArrow:card:list')
          break;
        case 5:
          flag = this.isAuth('lampPole:meteorologicalEnvironment')
          break;
        case 6:
          flag = this.isAuth('lampPole:lighting') || this.isAuth('lampPole:cat1') 
          break;
        case 7:
          flag = this.isAuth('lampPole:powerManagement')
          break;
        case 8:
          flag = this.isAuth('lampPole:monitoringControl')
          break;
        case 9:
          flag = this.isAuth('lampPole:passengerFlowStatistics')
          break;
        case 10:
          flag = this.isAuth('traffic:device')
          break;
        case 11:
          flag = this.isAuth('ac:management:list')
          break;
        case 12:
          flag = this.isAuth('lampPole:radar')
          break;
      }
      return flag
    },
    //分页查询所有文件
    manualPageList(){
      this.$http({
        url: this.$http.adornUrl('/manual/file/page'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex+"",
          'limit': this.pageSize+"",
          'key': this.key,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.isFirst){
            this.totalNum=data.page.totalCount
            this.isFirst=false
          }
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.manualPageList()
          }
          // 设置选中
          // var select = this.$refs.selection.getSelection().map(item => {return item.fileId})
          // if (select && select.length !== 0) {
          //   this.dataList.map(item => {
          //     if (select.indexOf(item.deviceId) != -1) {
          //       item._checked = true
          //     } else {
          //       item._checked = false
          //     }
          //   })
          // }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.manualPageList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.manualPageList()
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },

    //删除
    toDelete(id) {
      var fileId = id ? [id] : this.dataListSelections.map(item => {
        return item.fileId
      })
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          // 删除
          this.$http({
            url: this.$http.adornUrl("/manual/file/delete"),
            method: "post",
            data: this.$http.adornData(fileId, false),
          }).then(({ data }) => {
            if(data && data.code===0){
              this.$Message.success(data.msg)
            }else {
              this.$Message.error(data.msg)
            }
            this.initTable()
            this.manualPageList()
          });
        },
      });
    },
    //打开上传文件表单时初始化
    uploadFormHandler(){
      this.uploadVisible=true
      this.uploadForm.manualFile=[]
      this.type=0
      this.langType=1
      this.fileType=1
      this.isMp4=false
      this.isPdf=false
    },
    // 文件上传前
    handleUpload(selectFile) {
      if (this.uploadForm.manualFile.length===0){
        this.isPdf=false
        this.isMp4=false
      }
      var type = selectFile.type
      var ele = type.substring(type.lastIndexOf('/') + 1, type.length)
      if (ele === "pdf") {
        this.isPdf=true
        if (ele === "pdf" && selectFile.size > (50 * 1024 * 1024)) {
          this.isPdf=false
          this.$Message.error({
            content: this.$t("file.fileOverSize")
          })
          this.selectFile = null //超过大小将文件清空
          return false
        }
        if (this.isMp4){
          this.isPdf=false
          this.$Message.error({
            content: this.$t('manual.fileTips')
          })
          this.selectFile = null //超过大小将文件清空
          return false
        }
      }else if (ele === "mp4") {
        this.isMp4=true
        if (ele === "mp4" && selectFile.size > (500 * 1024 * 1024)) {
          this.isMp4=false
          this.$Message.error({
            content: this.$t("file.fileOverSize")
          })
          this.selectFile = null //超过大小将文件清空
          return false
        }
        if (this.isPdf){
          this.isMp4=false
          this.$Message.error({
            content: this.$t('manual.fileTips')
          })
          this.selectFile = null //超过大小将文件清空
          return false
        }
      } else {
        this.$Message.error({
          content:this.$t("file.fileLimitPdfAndVideo")
        })
        this.selectFile = null //将文件清空
        return false
      }
      if (this.uploadForm.manualFile.length >= 5) {
        this.$Message.error(this.$t('file.YouCanOnlyUploadUpTo5Files'))
        return false
      }
      // 临时数组，同时用于显示在页面
      this.uploadForm.manualFile.push(selectFile)
      return false
    },
    // 新增文件上传
    uploadFormSubmit() {
      if (this.uploadForm.manualFile.length > 0) {
        this.load = true
        let formData = new FormData()
        formData.append("type",this.type)
        formData.append("langType",this.langType)
        if(this.isPdf){
          formData.append("fileType",1)
        }
        if (this.isMp4){
          formData.append("fileType",2)
        }
        //多个文件上传
        for (var i = 0; i < this.uploadForm.manualFile.length; i++) {
          formData.append("file", this.uploadForm.manualFile[i]);   // 文件对象
        }
        axios.request({
          url: this.$http.adornUrl('/manual/file/upload'),
          method: 'post',
          data: formData,
          headers: {'Content-Type': 'multipart/form-data', 'token': this.token}
        }).then(res => {
          // 上传成功处理
          if (res.data) {
            if (res.data.code === 0) {
              this.load = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.uploadVisible = false
                  this.$emit('refreshDataList')
                  this.manualPageList()
                }
              })
            } else {
              this.load = false
              this.$Message.error({
                content: res.data.msg,
                duration: 0.5,
                onClose: () => {
                  this.load = false
                  this.uploadVisible = false
                  this.$emit('refreshDataList')
                }
              })
            }

          }
        })
      }
    },
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  },
  watch: {
    videoPlayModel: function (newVal, oldVal) {
      if (!newVal) {
        this.$refs.videoPlayer.pause();//暂停
      }
    }
  }
}
</script>

<style>
  .tabs-style > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background: #fff;
  }
  .tabs-style > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
    border-top: 1px solid #3399ff;
  }
  .tabs-style > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before{
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: #3399ff;
    position: absolute;
    top: 0;
    left: 0;
  }
  .videoDiv {
    width: 320px;
    height: 180px;
  }
  .videoIcon{
    position: relative;
    left:42%;
    bottom: 70%;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
  }
  .videoIcon:hover{
    background: #cfcfcf;
  }
  .videoIcon > i{
    margin-top: 8px;
    margin-left: 12px;
  }

</style>
