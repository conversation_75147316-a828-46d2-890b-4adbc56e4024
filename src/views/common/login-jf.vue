<template>
  <div :class="getClassName(selectedLanguage)" :style="{ 'height': height + 'px', 'width': width + 'px' }">
    <div class="login-wrap">
      <div v-if="changeLoginMode">
        <div class="login-head">
          <span v-if="!isVerify" :title="$t('login.dynamicCodeEntry')">
            <svg @click="changeLogin('randomCode')"
              style="cursor:pointer;float:left"  width="35px" height="35px" aria-hidden="true">
              <use xlink:href="#erweima"></use>
            </svg>
          </span>
          <span class="login-title">{{$t('login.passwordLogin')}}</span>
          <span style="float:right;margin-top:10px">
            <Dropdown>
              <a href="javascript:void(0)">
                  {{$t('common.language')}}
                  <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-for="item in localeList" :key="item.value" :selected="item.selected" @click.native="changeLocale(item.value)">{{item.name}}</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
        </div>
        <div class="login-form">
          <Form ref="dataForm" :model="dataForm" :rules="dataRule"  @keyup.enter.native="dataFormSubmit('dataForm')">
            <FormItem prop="userName">
              <Input prefix="ios-contact" size="large" type="text" @on-change="handleOnChange" v-model="dataForm.userName" :placeholder="$t('login.username')"/>
            </FormItem>
            <FormItem prop="password">
              <Input prefix="ios-lock-outline" size="large" type="password"  @on-change="handleOnChange" password v-model="dataForm.password" :placeholder="$t('login.password')"/>
            </FormItem>
            <!-- <FormItem>
              <VerificationCode v-model="identifyCode"></VerificationCode>
            </FormItem> -->
            <FormItem>
              <SliderCheck :successFun="handleSuccessFun" :errorFun="handleErrorFun" :key="reloadMe"
                :successText="$t('login.authenticationSuccessful')" :startText="$t('login.sliderRight')"></SliderCheck>
            </FormItem>
            <FormItem prop="code" v-show="loginCheckStatus" :label="resData.validate === 1 ?  `${$t('login.clickCodeMailbox')}${resData.email} `: `${$t('login.clickCodePhone')}${resData.mobile}`" >
              <Input prefix="ios-mail" size="large" v-model="dataForm.code"
                :placeholder="$t('common.PleaseInput') + $t('register.code')">
                <div id="sendCode" @click="sendCode()" slot="append">
                  <a style="cursor: pointer">{{$t('register.getCode')}}</a>
                </div>
              </Input>
            </FormItem>
            <FormItem>
              <span style="float:left;" v-if="!isVerify"><a href="javascript:void(0)" @click="$router.replace({ name: 'register' })">{{$t('register.register')}}</a></span>
              <span style="float:right"><a href="javascript:void(0)" @click="$router.replace({ name: 'password' })">{{$t('login.ForgetThePassword')}}</a></span>
            </FormItem>
            <FormItem>
                <Button long :loading="loading" size="large" type="primary" @click="dataFormSubmit('dataForm')">{{$t('login.login')}}</Button>
            </FormItem>
          </Form>
        </div>
      </div>

      <!-- 验证码登录 -->
      <div v-else>
        <div class="login-head">
          <span :title="$t('login.passwordLogin')">
            <svg  @click="changeLogin('compiter')"  style="cursor:pointer;float:left"  width="35px" height="35px" aria-hidden="true">
              <use xlink:href="#diannao"></use>
            </svg>
          </span>
          <span class="login-title">{{$t('login.dynamicCodeEntry')}}</span>
          <span style="float:right;margin-top:10px">
            <Dropdown>
              <a href="javascript:void(0)">
                  {{$t('common.language')}}
                  <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-for="item in localeList" :key="item.value" :selected="item.selected" @click.native="changeLocale(item.value)">{{item.name}}</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
        </div>
        <div class="login-form">
          <!-- <span style="color: red">{{$t("common.supportedTip")}}</span> -->
          <Form ref="codeDataForm" :model="codeDataForm" :rules="codeDataRule"  @keyup.enter.native="codeDataFormSubmit('codeDataForm')">
            <FormItem prop="userName">
              <Input prefix="ios-contact" size="large" type="text" @on-change="handleOnChange('code')" v-model="codeDataForm.userName" :placeholder="$t('login.username')"/>
            </FormItem>
            <FormItem>
              <SliderCheck :successFun="codeHandleSuccessFun" :errorFun="codeHandleErrorFun" :key="codeReloadMe"
                :successText="$t('login.authenticationSuccessful')" :startText="$t('login.sliderRight')"></SliderCheck>
            </FormItem>
            <FormItem prop="code" v-show="codeLoginCheckStatus" :label="codeResData.validate === 1 ?  `${$t('login.clickCodeMailbox')}${codeResData.email} `: `${$t('login.clickCodePhone')}${codeResData.mobile}`" >
              <Input prefix="ios-mail" size="large" v-model="codeDataForm.code"
                :placeholder="$t('common.PleaseInput') + $t('register.code')">
                <div id="codeSendCode" @click="codeSendCode()" slot="append">
                  <a style="cursor: pointer">{{$t('register.getCode')}}</a>
                </div>
              </Input>
            </FormItem>
            <FormItem prop="code" v-show="codeResData && codeResData.validate == 0">
              <span style="color: red">{{$t('login.dynamicCodeEntryTips')}}</span>
            </FormItem>
            <FormItem>
                <Button long :loading="codeLoading" size="large" type="primary" @click="codeDataFormSubmit('codeDataForm')">{{$t('login.login')}}</Button>
            </FormItem>
          </Form>
        </div>
      </div>
    </div>
    <div class="footer">
      <div style="width:300px;margin:0 auto; padding:5px 0;">
        <!-- <img src="../../assets/img/sysolution_logo.png" style="padding-left:38px;"/> -->
        <a target="_blank" href="https://beian.miit.gov.cn"
        style="display:inline-block;text-decoration:none;height:20px;line-height:20px;">
          <img src="../../assets/img/备案图标.png" style="float:left;">
          <p style="float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px; color:#939393;font-size:14px">备案号 浙ICP备2024128473号</p>
        </a>
      </div>
    </div>

    <Modal id="notifyModal" v-model="notifyModal" width="540" :mask-closable="false" footer-hide :closable="false">
      <div id="notifyModalDiv" style="height: 641.5px;color: #000;">
        <h2 style="text-align: center;">放假关停服务器通知</h2>
        <Divider />
        <div style="height: 550px;padding: 5px;">
          <!-- <div style="font-size:22px" class="text-left">尊敬的合作伙伴和客户朋友：</div>
          <div style="text-indent:36px;" class="text-left">您好！</div>
          <div style="font-size:16px;text-indent:36px;" class="text-left">
            感谢您一直以来对熙讯科技的支持与信任，值此新春佳节之际预祝大家阖家团圆，万事如意!我司根据国家规定及结合公司实际情况，现作出如下决定：</div>
            <div style="font-size:16px;text-indent:36px;" class="text-left">
              <b>2024年02月09 日至2024年02月17 日，</b>
              <span>我司将关闭 AIPS云平台服务器，国外服务器不受影响。国内服务器停止运行期间，将无法登录、编辑、发布和查看任何节目信息。在此之前，已下发的LED 显示屏节目信息可以正常播放，因假期给您造成不便请您谅解。</span>
            </div>
            <div style="font-size:16px;text-indent:36px;" class="text-left">
              在此，熙讯科技全体员工衷心祝愿大家在新的一年里，身体健康万事如意，龙年大吉!
            </div>
            <div style="float: right;font-size:22px;margin-top:10px">深圳市熙讯云科技有限公司</div>
            <div style="clear: both;float: right;font-size:22px;margin-top:5px">2024年01月29日</div> -->
        </div>
      <Button style="float: right;margin-right: 5px;" type="success" @click="notifyModal = false">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
  </div>

</template>

<script>
import $ from 'jQuery'
import SliderCheck from '@/utils/SliderCheck'
import VerificationCode from "@/utils/VerificationCode.vue";
import { isNumOrLetter, PWDLenght } from '@/utils/validate'
export default {
  data () {
    return {
      notifyModal: false,
      loading: false,
      codeLoading: false,
      documentClientHeight: 0,
      height: 1145,
      width: 2014,
      documentClientWidth: 0,
      changeLoginMode: true,
      dataForm: {
        userName: '',
        password: '',
        code: ''
      },
      codeDataForm: {
        userName: '',
        code: ''
      },
      loginCheck: false,
      loginCheckStatus: false,
      reloadMe: 0,
      codeLoginCheck: false,
      codeLoginCheckStatus: false,
      codeReloadMe: 0,
      resData: {},
      codeResData: {},
      countdown: 60,
      codeCountdown: 60,
      timer: null,
      codeTimer: null,
      localeList: [
        {name: 'English', value: 'us', selected: false},
        {name: '中文简体', value: 'cn', selected: true},
        {name: '中文繁體', value: 'tw', selected: false},
        {name: '日本語.', value: 'ja', selected: false}
      ],
      // localeList: [
      //   {name: 'English', value: 'us', selected: false},
      //   {name: '中文繁體', value: 'tw', selected: true}
      // ],
      isVerify: window.SITE_CONFIG.isVerify,
      //当前选择的语言
      selectedLanguage:'cn',
      //当前生成的验证码
      identifyCode: "",
    }
  },
  computed: {
    language: {
      get () { return this.$store.state.language.language },
      set (val) { this.$store.commit('language/setLanguage', val) }
    },
    codeDataRule: {
      get () {
        return {
          userName: [
            { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isNumOrLetter(value)) {
                callback(new Error(this.$t("login.user4To17")))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ]
        }
      }
    },
    dataRule: {
      get () {
        return {
          userName: [
            { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isNumOrLetter(value)) {
                callback(new Error(this.$t("login.user4To17")))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          password: [
            { required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur' },
            // { validator: (rule, value, callback) => {
            //   if (!PWDLenght(value)) {
            //     callback(new Error(this.$t("login.passwordMore8")))
            //   } else {
            //     callback()
            //   }
            // }, trigger: 'blur' }
          ]
        }
      }
    },
  },
  methods: {
    changeLocale (value) {
       this.$i18n.locale = value
       this.language = value
       this.localeList.map(item => {
         if (item.value === value) {
           item.selected = true
         } else {
           item.selected = false
         }
       })
    },
    // 登录方式
    changeLogin (name) {
      if (name === 'randomCode') {
        this.changeLoginMode = false
      } else if (name === 'compiter') {
        this.changeLoginMode = true
      }
    },
    // 重置窗口可视高度
    resetDocumentClientHeight () {
      this.documentClientHeight = document.documentElement['clientHeight']
      this.documentClientWidth = document.documentElement['clientWidth']
      // 2014 * 1145
      if (this.documentClientWidth < this.width) {
        var num = Math.round(this.width / this.documentClientWidth)
        this.width = this.documentClientWidth + 15
        this.height = Math.round(this.documentClientHeight * num) + 15
      }
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
        this.documentClientWidth = document.documentElement['clientWidth']
        if (this.documentClientWidth < this.width) {
          var num = Math.round(this.width / this.documentClientWidth)
          this.width = this.documentClientWidth + 15
          this.height = Math.round(this.documentClientHeight * num) + 15
        }
      }
    },
    // 滑块验证成功回调
    codeHandleSuccessFun() {
      this.codeLoginCheck = true
      if (isNumOrLetter(this.codeDataForm.userName)) {
        this.$http({
            url: this.$http.adornUrl(`/sys/checkValidate/${this.codeDataForm.userName}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
                this.codeResData = data.data
                setTimeout(() => {
                  if (this.codeResData.validate === 0) {
                    this.codeLoginCheckStatus = false
                  } else {
                    this.codeLoginCheckStatus = true
                  }
                }, 200);
            } else {
              this.$Message.error(data.msg)
              this.codeReloadMe += 1
            }
          })
      } else {
        this.$Message.error(this.$t('validate.account_cannot_empty'))
        this.codeReloadMe += 1
      }
    },
    // 滑块验证失败回调
    codeHandleErrorFun() {
      this.codeLoginCheck = false
    },
    // 滑块验证成功回调
    handleSuccessFun() {
      this.loginCheck = true
      if (isNumOrLetter(this.dataForm.userName)) {
        this.$http({
            url: this.$http.adornUrl(`/sys/checkValidate/${this.dataForm.userName}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
                this.resData = data.data
                setTimeout(() => {
                  if (this.resData.validate === 0) {
                    this.loginCheckStatus = false
                  } else {
                    this.loginCheckStatus = true
                  }
                }, 200);
            } else {
              this.$Message.error(data.msg)
              this.reloadMe += 1
            }
          })
      } else {
        this.$Message.error(this.$t('validate.account_cannot_empty'))
        this.reloadMe += 1
      }
    },
    // 滑块验证失败回调
    handleErrorFun() {
      this.loginCheck = false
    },
    codeSendCode () {
      var flag = ''
      if (this.codeResData.validate === 1) {
        flag = 0
      } else if (this.codeResData.validate === 2) {
        flag = 1
      }
      this.$http({
        url: this.$http.adornUrl(`/sms/emailOrMobile/sendCode?username=${this.codeResData.username}&flag=${flag}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code !== 0) {
          this.$Message.error(data.msg)
          this.codeClearTimer()
        }
      })
      this.codeTimeoutChangeStyle()
    },
    codeTimeoutChangeStyle () {
      // 启动定时器
      this.codeLoadingTime()
      this.codeTimer = setInterval(() => {
        // 创建定时器
        if (this.codeCountdown === 0) {
          this.codeClearTimer() // 关闭定时器
        } else {
          this.codeLoadingTime()
        }
      }, 1000)
    },
    sendCode() {
      var flag = ''
      if (this.resData.validate === 1) {
        flag = 0
      } else if (this.resData.validate === 2) {
        flag = 1
      }
      this.$http({
        url: this.$http.adornUrl(`/sms/emailOrMobile/sendCode?username=${this.resData.username}&flag=${flag}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code !== 0) {
          this.$Message.error(data.msg)
          this.clearTimer()
        }
      })
      this.timeoutChangeStyle()
    },
    timeoutChangeStyle () {
      // 启动定时器
      this.loadingTime()
      this.timer = setInterval(() => {
        // 创建定时器
        if (this.countdown === 0) {
          this.clearTimer() // 关闭定时器
        } else {
          this.loadingTime()
        }
      }, 1000)
    },
    handleOnChange (type) {
      if (type && type == "code") {
        this.codeReloadMe += 1,
        this.codeLoginCheck = false
        this.codeLoginCheckStatus = false
      } else {
        this.reloadMe += 1,
        this.loginCheck = false
        this.loginCheckStatus = false
        $('.login-wrap').css("height", "430px")
      }
    },
    codeLoadingTime () {
      // 启动定时器
      var str = `<div>${this.$t('register.prependResend')} ${this.codeCountdown} ${this.$t('register.appendResend')} </div>`
      $('#codeSendCode').html(str)
      this.codeCountdown-- // 定时器减1
    },
    codeClearTimer () {
      // 清除定时器
      clearInterval(this.codeTimer)
      this.codeTimer = null
      this.codeCountdown = 60
      $('#codeSendCode').html(`<div>${this.$t('register.getCode')}</div>`)
    },
    loadingTime () {
      // 启动定时器
      var str = `<div>${this.$t('register.prependResend')} ${this.countdown} ${this.$t('register.appendResend')} </div>`
      $('#sendCode').html(str)
      this.countdown-- // 定时器减1
    },
    clearTimer () {
      // 清除定时器
      clearInterval(this.timer)
      this.timer = null
      this.countdown = 60
      $('#sendCode').html(`<div>${this.$t('register.getCode')}</div>`)
    },
    // 免密登录
    secretFreeLogin(){
      this.$http({
        url: this.$http.adornUrl('/sys/login'),
        method: 'post',
        data: this.$http.adornData({
          'username': this.dataForm.userName,
          'password': this.$encruption(this.dataForm.password),
          'code': this.dataForm.code
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$cookie.set('token', data.token)
          if (data.isStrong != true) {
            this.$cookie.set('isStrong', data.isStrong)
          }
          if (data.isAuth != 1) {
            this.$cookie.set('isAuth',data.isAuth)
          }
          this.$router.replace({ name: 'home' }).catch(() => {})
          this.loading = false
        } else {
          this.$Message.error({
            content: data.msg,
            onClose: () => {
              setTimeout(() => {
                this.loading = false
                this.loginCheckStatus = false
                this.dataForm.password = ''
                this.dataForm.code = ''
                this.reloadMe += 1
                this.loginCheck = false
              }, 500)
            }
          })
        }
        this.clearTimer()
      }).catch(err => {
        setTimeout(() => {
          this.loading = false
          this.loginCheckStatus = false
          this.dataForm.password = ''
          this.dataForm.code = ''
          this.reloadMe += 1
          this.loginCheck = false
        }, 500)
      })
    },
    // 验证码登录提交表单
    codeDataFormSubmit (codeDataForm) {
      this.$refs[codeDataForm].validate((valid) => {
        if (valid) {
          if (this.codeLoginCheck === false) {
            this.$Message.error(this.$t("login.securityVerification"))
          } else {
            if (this.codeDataForm.code === '') {
              this.$Message.error(this.$t("login.enterVerificationCode"))
              return
            }
            this.codeLoading = true
            this.$http({
              url: this.$http.adornUrl('/sys/codeLogin'),
              method: 'post',
              data: this.$http.adornData({
                'username': this.codeDataForm.userName,
                'code': this.codeDataForm.code
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$cookie.set('token', data.token)
                if (data.isStrong != true) {
                  this.$cookie.set('isStrong', data.isStrong)
                }
                if (data.isAuth != 1) {
                  this.$cookie.set('isAuth',data.isAuth)
                }
                this.$router.replace({ name: 'home' }).catch(() => {})
                this.codeLoading = false
                // if (!data.isStrong){
                //   console.log("弱密码")
                //   // this.$router.push({ name: 'updateUserInfo' })
                // this.$router.push({ name: 'updateUserInfo'})

                // }
              } else {
                this.$Message.error({
                  content: data.msg,
                  onClose: () => {
                    setTimeout(() => {
                      this.codeLoading = false
                      this.codeLoginCheckStatus = false
                      this.codeDataForm.code = ''
                      this.codeReloadMe += 1
                      this.codeLoginCheck = false
                    }, 500)
                  }
                })
              }
              this.codeClearTimer()
            }).catch(err => {
              setTimeout(() => {
                this.codeLoading = false
                this.codeLoginCheckStatus = false
                this.codeDataForm.code = ''
                this.codeReloadMe += 1
                this.codeLoginCheck = false
              }, 500)
            })
          }
        }
      })
    },
    // 提交表单
    dataFormSubmit (dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          if (this.loginCheck === false) {
            this.$Message.error(this.$t("login.securityVerification"))
          } else {
            if (this.loginCheckStatus === true && this.dataForm.code === '') {
              this.$Message.error(this.$t("login.enterVerificationCode"))
              return
            }
            this.loading = true
            this.$http({
              url: this.$http.adornUrl('/sys/login'),
              method: 'post',
              data: this.$http.adornData({
                'username': this.dataForm.userName,
                'password': this.$encruption(this.dataForm.password),
                'code': this.dataForm.code
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$cookie.set('token', data.token)
                if (data.isStrong != true) {
                  this.$cookie.set('isStrong', data.isStrong)
                }
                if (data.isAuth != 1) {
                  this.$cookie.set('isAuth',data.isAuth)
                }
                this.$router.replace({ name: 'home' }).catch(() => {})
                this.loading = false
                // if (!data.isStrong){
                //   console.log("弱密码")
                //   // this.$router.push({ name: 'updateUserInfo' })
                // this.$router.push({ name: 'updateUserInfo'})

                // }
              } else {
                this.$Message.error({
                  content: data.msg,
                  onClose: () => {
                    setTimeout(() => {
                      this.loading = false
                      this.loginCheckStatus = false
                      this.dataForm.password = ''
                      this.dataForm.code = ''
                      this.reloadMe += 1
                      this.loginCheck = false
                    }, 500)
                  }
                })
              }
              this.clearTimer()
            }).catch(err => {
              setTimeout(() => {
                this.loading = false
                this.loginCheckStatus = false
                this.dataForm.password = ''
                this.dataForm.code = ''
                this.reloadMe += 1
                this.loginCheck = false
              }, 500)
            })
          }
        }
      })
    },
    //根据语言选择背景图
    getClassName(val){
      switch (val) {
        case "us":
          return ['maxnboxus']
        default :
          return ['maxnbox']
      }
    },
  },
  mounted () {
    this.resetDocumentClientHeight()
  },
  created () {
    if (this.$route.params.changeLoginMode !== undefined) {
      this.changeLoginMode = this.$route.params.changeLoginMode
    }
    this.changeLocale(localStorage.getItem('locale') === null ? 'cn' : localStorage.getItem('locale'))
    if (this.$route.query.flag == "false") {
      this.dataForm.userName = this.$route.query.username
      this.dataForm.password = this.$route.query.password
      this.dataForm.code = ""
      this.secretFreeLogin()
    }
    if (this.$route.query.lang) {
      this.changeLocale(this.$route.query.lang)
    }
  },
  components: {
    SliderCheck,
    VerificationCode
  },
  watch: {
    'loginCheckStatus': function(newVal, oldVal) {
      if (newVal === true) {
        $('.login-wrap').css("height", "520px")
      }
    },
    'language': function (newVal, OldVal) {
      this.selectedLanguage = newVal
    }
  }

}
</script>
<style>
  .ivu-form-item-error-tip{
    text-align: left;
  }
  #notifyModal .ivu-modal-wrap .ivu-modal-body {
    padding: 0px;
  }
</style>
<style scoped>
  #notifyModalDiv{
    background-image: url(../../assets/img/notifyModel.jpg);
    background-repeat: repeat-x;
    background-size: 100% 100%;
  }
  .maxnbox{
    background-image: url(../../assets/img/login_background_jf.png);
    background-repeat: repeat-x;
    background-size: 100% 100%;
    /* overflow: hidden; */
  }
  .maxnboxus{
    background-image: url(../../assets/img/login_background_jf.png);
    background-repeat: repeat-x;
    background-size: 100% 100%;
    /* overflow: hidden; */
  }
  .login-wrap {
    position: absolute;
    left:70%;
    top:50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 3%;
    height: 430px;
    width: 490px;
    padding: 20px;
    margin:0 auto;
    box-shadow: 0px 5px 20px rgba(68, 68, 68, 0.5);
  }
  .login-head {
    height: 60px;
  }
  .login-title {
    color: #6f7072;
    padding: 8px;
    font-weight: 600;
    text-shadow: 0 1px 0 #fff;
    font-size: 26px;
    margin-left: 20px;
  }
  .login-form {
    margin-top: 20px;
  }
  .footer {
    position: absolute;
    left:50%;
    top:97%;
    transform: translate(-50%, -50%);
  }
</style>
