<template>
  <div class="maxnbox" :style="{ 'height': documentClientHeight + 'px' }">
    <div class="login-wrap" :style="{ 'height': documentClientHeight + 'px' }">
      <div class="login-head">
        <span class="login-title">风险须知</span>
      </div>
      <Divider dashed />
      <div class="contet" :style="{ 'height': documentClientHeight-120 + 'px' }">
        <span>一、总则</span>
        <p>
          1．1　用户应当同意本协议的条款并按照页面上的提示完成全部的注册程序。用户在进行注册程序过程中勾选“我已阅读并接受<a href="javascript:void(0)">《风险须知》</a>”
          即表示用户与AIPS平台提供商达成协议，完全接受本协议项下的全部条款。
        </p>
        <p>
          1．2　用户注册成功后，AIPS将给予每个用户一个公司帐号和管理员帐号及相应的密码，该用户帐号和密码由用户负责保管；
          用户应当对以其用户帐号进行的所有活动和事件负法律责任。
        </p>
        <p>
          1．3　用户一经注册AIPS帐号，除非子频道要求单独开通权限，用户有权利用该账号使用平台分配的所有功能；当用户使用AIPS各单项服务时，用户的使用行为
          视为其对该单项服务的服务条款以及AIPS在该单项服务中发出的各类公告的同意。
        </p>
        <p>
          1．4　AIPS会员服务协议以及各个频道单项服务条款和公告可由AIPS团队随时更新，且无需另行通知。您在使用相关服务时,应关注并遵守其所适用的相关条款。
          您在使用AIPS提供的各项服务之前，应仔细阅读本服务协议。如您不同意本服务协议及/或随时对其的修改，您可以主动取消AIPS提供的服务；您一旦使用AIPS服务，
          即视为您已了解并完全同意本服务协议各项内容，包括AIPS对服务协议随时所做的任何修改，并成为AIPS用户。
        </p>
        <p>
          您在使用AIPS团队提供的各项服务之前，应仔细阅读本服务协议。如您不同意本服务协议及/或随时对其的修改，您可以主动取消AIPS团队提供的服务；您一旦使用AI
          PS团队服务，即视为您已了解并完全同意本服务协议各项内容，包括AIPS团队对服务协议随时所做的任何修改，并成为AIPS团队用户。
        </p>
        <p>
          5．4　如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。请您在发现任何违反本服务协议以及其他任何单项服务的服务条款、
          AIPS各类公告之情形时，通知AIPS。您可以通过如下联络方式同AIPS联系：
          <b style="padding-left: 45px;"><br/>上海市嘉定区金沙江西路1555弄C区6号3楼 邮编：201803<br/></b>
          <b style="padding-left: 45px;">AIPS团队</b>
        </p>
      </div>
      <Divider dashed />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      documentClientHeight: 0,
      disabled: true,
    }
  },
  methods: {
    // 重置窗口可视高度
    resetDocumentClientHeight () {
      this.documentClientHeight = document.documentElement['clientHeight']
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
      }
    },
    scrolling () {
      this.$nextTick(() => {
        const el = document.querySelector('.contet');
        const offsetHeight = el.offsetHeight;
        el.onscroll = () => {
          const scrollTop = el.scrollTop;
          const scrollHeight = el.scrollHeight;
          if ((offsetHeight + scrollTop) - scrollHeight >= -1) {
            this.disabled = false
          }
        }
      })
    }
  },
  mounted () {
    this.resetDocumentClientHeight()
    this.scrolling()
  }
}
</script>
<style scoped>
.maxnbox{
  background-image: url(../../assets/img/login_background.png);
  background-repeat: repeat-x;
  background-size: 100% 100%;
  overflow: hidden;
}
.login-wrap {
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%, -50%);
  background: rgb(255, 255, 255);
  margin:0 auto;
  width: 720px;
  border-radius: 1%;
  overflow: hidden;
}
.login-wrap:hover {
  box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.2);
}
.login-head {
  text-align: center;
  height: 30px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.login-title {
  color: #6f7072;
  padding: 5px;
  font-weight: 600;
  text-shadow: 0 1px 0 #fff;
  font-size: 26px;
}
.contet {
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 5px;
}
.contet span {
  font-weight: 600;
  font-size: 15px;
}
.contet p {
  text-indent:25px
}
.contetB {
  padding-left: 55px;
}
.foot {
  float: right;
  margin-top: 10px;
  margin-right: 20px;
}
</style>
