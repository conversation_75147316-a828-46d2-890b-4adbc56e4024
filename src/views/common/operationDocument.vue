<template>
    <div class="video-tutorial">
        <!-- 添加搜索框 -->
        <div class="top">
            <svg class="video-tutorial-svg" aria-hidden="true">
                <use xlink:href="#operationDocument"></use>
            </svg>
            <div class="video-tutorial-text">
                <span>{{$t('manual.operationDocument')}}</span>
            </div>
        </div>
        <Input prefix="ios-search" v-model="searchQuery" @on-change="searchQueryChange" :placeholder="$t('common.search')"/>
        <div v-if="lang == 'cn' || lang == 'tw'"  class="video-tutorial-content">
            <el-card class="video-tutorial-item" shadow="hover" v-for="(item, index) in operationDocument" :key="index">
                <div @click="operDocClick(item.url)">
                    <svg class="video-tutorial-svg" aria-hidden="true">
                        <use xlink:href="#Pdf"></use>
                    </svg>
                    <div class="video-tutorial-item-title">{{ item.title }}</div>
                </div>
            </el-card>
        </div>
        <div v-else  class="video-tutorial-content">
            <el-card class="video-tutorial-item" shadow="hover" v-for="(item, index) in usOperationDocument" :key="index">
                <div @click="operDocClick(item.url)">
                    <svg class="video-tutorial-svg" aria-hidden="true">
                        <use xlink:href="#Pdf"></use>
                    </svg>
                    <div class="video-tutorial-item-title">{{ item.title }}</div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            operationDocument: [
                {
                    title: '常规操作说明',
                    url: 'https://ledok.cn:8443/download/iot/zh/AIPS(4.0)云平台常规操作说明-V1.0.pdf'
                },
                {
                    title: '基础操作说明',
                    url: 'https://ledok.cn:8443/download/iot/zh/AIPS(4.0)云平台基础操作说明-V1.0.pdf'
                }
            ],
            usOperationDocument: [
                {
                    title: 'Platform Operation Instructions',
                    url: 'https://ledok.cn:8443/download/iot/en/AIPS (4.0) Cloud Platform Operation Instructions.pdf'
                },
                {
                    title: 'PlatformBasic Operation Instructions',
                    url: 'https://ledok.cn:8443/download/iot/en/AIPS (4.0) Cloud PlatformBasic Operation Instructions.pdf'
                }
            ],
            operationDocumentCopy: [],
            usOperationDocumentCopy: [],
            lang: "cn",
            searchQuery: '' // 新增搜索关键词
        }
    },
    mounted() {
        this.lang = localStorage.getItem('locale');
        this.operationDocumentCopy = this.operationDocument;
        this.usOperationDocumentCopy = this.usOperationDocument;
    },
    methods: {
        operDocClick(url) {
            window.open(url, '_blank');
        },
        searchQueryChange() {
            if (this.lang == 'cn' || this.lang == 'tw') {
                this.operationDocument = this.operationDocumentCopy; // 重置数据为原始数据，避免重复过滤
                this.operationDocument = this.filteredOperationDocument;
            } else {
                this.usOperationDocument = this.usOperationDocumentCopy; // 重置数据为原始数据，避免重复过滤
                this.usOperationDocument = this.filteredUsOperationDocument;
            }
        },
    },
    computed: {
        // 计算属性，用于过滤中文视频教程
        filteredOperationDocument() {
            // 过滤中文视频教程，根据搜索关键词进行过滤
            return this.operationDocument.filter(item => {
                return item.title.toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        },
        // 计算属性，用于过滤英文视频教程
        filteredUsOperationDocument() {
            // 过滤英文视频教程，根据搜索关键词进行过滤
            return this.usOperationDocument.filter(item => {
                return item.title.toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        }
    }
}
</script>
<style scoped>
.video-tutorial{
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.top {
    height: 70px;
    padding-left: 20px;
    width: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
}
.video-tutorial-svg {
    display: inline-block;
    width: 60px;
    height: 60px;
}
.video-tutorial-text {
    display: inline-block;
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-left: 20px;
}
.video-tutorial-content {
    width: 100%;
    height: 87%;
    overflow-y: auto;
    padding: 0 20px;
}
.video-tutorial-item {
    display: inline-block;
    cursor: pointer;
    width: 500px;
    height: 100px;
    margin-top: 20px;
    margin-right: 10px;
}
.video-tutorial-item-title {
    display: inline-block;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
}
</style>