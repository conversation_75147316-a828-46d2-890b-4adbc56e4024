<template>
    <div class="maxnbox" :style="{ 'height': documentClientHeight + 'px' }">
        <div class="login-wrap" :style="{ 'height': documentClientHeight + 'px' }">
            <div class="login-head">
                <span class="login-title">AIPS User Agreement</span>
            </div>
            <Divider dashed />
            <div class="contet" :style="{ 'height': documentClientHeight-120 + 'px' }">
                <span>I. General Provisions</span>
                <p>
                    1.1 Users must agree to the terms of this agreement and complete the entire registration process according to the prompts on the page. By checking "I have read and accepted the <a href="javascript:void(0)">《AIPS User Agreement》</a>" during the registration process, the user indicates that they have reached an agreement with the AIPS platform provider and fully accept all terms under this agreement.
                </p>
                <p>
                    1.2 After successful registration, AIPS will provide each user with a company account, an administrator account, and a corresponding password. The user is responsible for safeguarding these accounts and passwords; The user shall bear legal responsibility for all activities and events conducted under their user account.
                </p>
                <p>
                    1.3 Once a user registers an AIPS account, unless sub-channels require separate permission activation, the user has the right to use all functions allocated by the platform with that account. When the user uses individual AIPS services, their usage behavior is considered consent to the terms of service for that individual service and any announcements issued by AIPS within that service.
                </p>
                <p>
                    1.4 The AIPS Membership Service Agreement, as well as the terms and announcements for individual channel services, can be updated by the AIPS team at any time without prior notice. When using related services, you should pay attention to and comply with the applicable terms. Before using any services provided by AIPS, you should carefully read this service agreement. If you do not agree with this service agreement and/or any modifications made to it at any time, you may proactively cancel the services provided by AIPS; once you use AIPS services, you are deemed to have understood and fully agreed to all contents of this service agreement, including any modifications made by AIPS to the service agreement at any time, and become an AIPS user.
                </p>
                <p>
                    Before using any services provided by the AIPS team, you should carefully read this service agreement. If you do not agree with this service agreement and/or any modifications made to it at any time, you may proactively cancel the services provided by the AIPS team; once you use AIPS team services, you are deemed to have understood and fully agreed to all contents of this service agreement, including any modifications made by the AIPS team to the service agreement at any time, and become an AIPS team user.
                </p>
                <span>II. Registration Information and Privacy Protection</span>
                <p>
                    2.1 The ownership of the AIPS account (i.e., AIPS User ID) belongs to the AIPS team. After completing the registration application procedures, the user obtains the right to use the AIPS account. Users should provide timely, detailed, and accurate personal information and continuously update their registration data to meet the requirements of timeliness, detail, and accuracy. All originally entered data will be referred to as registration data. Our company assumes no responsibility for problems arising from untrue registration information and the consequences thereof.
                </p>
                <p>
                    2.2 Users shall not transfer, sell, or lend their account or password to others for use. If a user authorizes another person to use the account, they shall bear full responsibility for all actions taken by the authorized person under that account.
                </p>
                <span>III. Usage Rules</span>
                <p>
                    3.1 When using AIPS services, users must comply with the relevant laws and regulations of the People's Republic of China. Users agree not to use this service for any illegal or improper activities, including but not limited to the following:
                    <p>
                        (1) Uploading, displaying, posting, disseminating, or otherwise transmitting information containing any of the following content:
                    </p>
                    <b class="contetB">1) Opposing the basic principles established by the Constitution;<br/></b>
                    <b class="contetB">2) Endangering national security, leaking state secrets, subverting state power, or undermining national unity;<br/></b>
                    <b class="contetB">3) Harming national honor and interests; <br/></b>
                    <b class="contetB">4) Inciting ethnic hatred, ethnic discrimination, or undermining ethnic unity; <br/></b>
                    <b class="contetB">5) Undermining national religious policies, promoting cults, or feudal superstitions; <br/></b>
                    <b class="contetB">6) Spreading rumors, disrupting social order, or undermining social stability; <br/></b>
                    <b class="contetB">7) Spreading obscenity, pornography, gambling, violence, murder, terror, or inciting crime; <br/></b>
                    <b class="contetB">8) Insulting or slandering others, infringing upon the legitimate rights of others; <br/></b>
                    <b class="contetB" style="float: left;">9) Containing false, harmful, threatening content, infringing upon others' privacy, harassing, infringing, slandering, vulgar, obscene, or other morally offensive content; <br/></b>
                    <b class="contetB">10) Containing other content restricted or prohibited by Chinese laws, regulations, rules, ordinances, and any legally effective norms;<br/></b>
                    <p>
                        (2) Not using the network service system for any illegal purpose;
                    </p>
                    <p>
                        (3) Users must strictly review and manage all resources, users, and content under their company ID:
                    </p>
                    <b class="contetB">1) Independently manage the addition, deletion, and permission allocation of sub-users;<br/></b>
                    <b class="contetB">2) Independently review the legality of uploaded material content and must not infringe upon the intellectual property rights of others;<br/></b>
                    <b class="contetB" style="float: left;">3) Independently manage the legality of the content of produced programs, strictly review publications, and bear all legal responsibilities for the legality of content published on LED screens; <br/></b>
                </p>
                <p>
                    3.2 If a user violates this agreement or related service terms, leading to or causing any claims, demands, or losses from any third party, including reasonable attorney fees, you agree to indemnify AIPS, its partner companies, and affiliated companies, and hold them harmless. In this regard, AIPS has the right, depending on the nature of the user's behavior, to take measures including but not limited to deleting user-published information, suspending usage licenses, terminating services, restricting usage, reclaiming AIPS accounts, and pursuing legal responsibilities. For malicious registration of AIPS accounts or using AIPS accounts for illegal activities, disruption, harassment, deception, affecting other users, and other behaviors violating this agreement, AIPS has the right to reclaim their accounts. At the same time, the AIPS team will assist in investigations as required by judicial departments.
                </p>
                <p>
                    3.3 Users must bear legal responsibility for their actions while using AIPS services. Forms of legal responsibility include, but are not limited to: compensating those who have been harmed, and after the AIPS team has first borne administrative penalties or infringement damage compensation responsibilities caused by user behavior, the user shall provide equivalent compensation to the AIPS team.
                </p>
                <span>IV. Service Content</span>
                <p>
                    4.1 The specific content of AIPS network services is provided by AIPS based on actual conditions.
                </p>
                <p>
                    4.2 Unless otherwise explicitly stated in this service agreement, all new products, new features, and new services launched by AIPS are subject to the regulations of this service agreement.
                </p>
                <p>
                    4.3 To use this service, you must be able to access the international internet through a third party legally qualified to provide you with internet access services, and you shall bear the relevant service fees. In addition, you must provide and be responsible for all necessary equipment required for connection to the international network, including computers, modems, or other access devices.
                </p>
                <p>
                    4.4 Given the special nature of network services, users agree that AIPS has the right to change, interrupt, or terminate part or all of the network services (including paid network services) at any time without prior notice. AIPS does not guarantee that network services will not be interrupted and makes no guarantees regarding the timeliness, security, or accuracy of network services.
                </p>
                <p>
                    4.5 AIPS needs to regularly or irregularly inspect or maintain the platform or related equipment providing network services. If such situations cause interruptions in network services (including paid network services) within a reasonable time, AIPS shall not bear any responsibility. AIPS reserves the right to suspend any part of this service for maintenance, upgrades, or other purposes without prior notice.
                </p>
                <p>
                    4.6 This service or third parties may provide links to other websites or resources on the international internet. Since AIPS cannot control these websites and resources, you understand and agree that AIPS is not responsible for the availability of such websites or resources, and does not guarantee or take responsibility for any content, advertising, products, or other materials existing on or originating from such websites or resources. AIPS assumes no responsibility for any damage or loss arising from the use or reliance on any content, goods, or services published by or obtained through such websites or resources.
                </p>
                <p>
                    4.7 Users expressly agree that the risks associated with using AIPS network services will be borne entirely by themselves. Users understand and accept that any information obtained by downloading or through AIPS services depends on the users themselves, and they shall bear the risks of system damage, data loss, and any other risks. AIPS makes no guarantees regarding any merchandise shopping services, transaction processes, or recruitment information obtained on the service network.
                </p>
                <p>
                    4.8 User Notice: In the various data mining and push services provided by AIPS (including navigation URL pushes on the AIPS new homepage), links to websites or resources previously visited by users are automatically pushed based on machine algorithms. AIPS makes no guarantees regarding the validity, security, or legality of their content.
                </p>
                <p>
                    4.9 AIPS reserves the right to close accounts that have not been logged into for 6 months.
                </p>
                <p>
                    4.10 AIPS has the right to temporarily or permanently modify or terminate this service (or any part thereof) at any time, with or without notice, and AIPS shall not be liable to users or any third party.
                </p>
                <p>
                    4.11 Termination of Service<br/>You agree that AIPS may, based on its own considerations and for any reason, including but not limited to prolonged non-use, or if AIPS believes you have violated the letter and spirit of this service agreement, terminate your password, account, or the use of this service (or any part thereof), and remove and delete any content you have within this service. You agree that this service, provided in accordance with any provision of this service agreement, may be interrupted or terminated without prior notice. You acknowledge and agree that AIPS may immediately close or delete your account and all related information and files in your account, and/or prohibit further use of the aforementioned files or this service. Furthermore, you agree that if the use of this service is interrupted or terminated, or your account and related information and files are closed or deleted, AIPS shall not be liable to you or any third party.
                </p>
                <span>V. Miscellaneous</span>
                <p>
                    5.1 The establishment, execution, interpretation, and resolution of disputes regarding this agreement shall all be governed by the laws of the People's Republic of China.
                </p>
                <p>
                    5.2 If any dispute arises between the parties regarding the content of this agreement or its execution, both parties should try to resolve it through friendly negotiation; if negotiation fails, either party may file a lawsuit with the people's court in AIPS's location.
                </p>
                <p>
                    5.3 AIPS's failure to exercise or enforce any right or provision of this service agreement does not constitute a waiver of such right or provision.
                </p>
                <p>
                    5.4 If any provision in this agreement is wholly or partially invalid or unenforceable for any reason, the remaining provisions of this agreement shall remain valid and binding. Please notify AIPS if you discover any violation of this service agreement, the service terms of any other individual services, or any AIPS announcements. You can contact AIPS through the following contact information:
                    <b style="padding-left: 45px;"><br/>3rd Floor, Building 6, Zone C, Lane 1555, Jinshajiang West Road, Jiading District, Shanghai. Postal Code: 201803<br/></b>
                    <b style="padding-left: 45px;">AIPS Team</b>
                </p>
            </div>
            <Divider dashed />
            <div class="foot">
                <Button size="default" type="info" style="margin-right: 20px;" @click="$router.push({ name: 'register' })">Return</Button>
                <Button size="default" type="success" :disabled="disabled"
                @click="$router.push({ name: 'register' , params: {check: false, dataForm: $route.params.dataForm, phoneMail: $route.params.phoneMail} })">Read & Acknowledged</Button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data () {
        return {
            documentClientHeight: 0,
            disabled: true,
        }
    },
    methods: {
        // Reset window visual height
        resetDocumentClientHeight () {
            this.documentClientHeight = document.documentElement['clientHeight']
            window.onresize = () => {
                this.documentClientHeight = document.documentElement['clientHeight']
            }
        },
        scrolling () {
            this.$nextTick(() => {
                const el = document.querySelector('.contet');
                const offsetHeight = el.offsetHeight;
                el.onscroll = () => {
                    const scrollTop = el.scrollTop;
                    const scrollHeight = el.scrollHeight;
                    if ((offsetHeight + scrollTop) - scrollHeight >= -1) {
                        this.disabled = false
                    }
                }
            })
        }
    },
    mounted () {
        this.resetDocumentClientHeight()
        this.scrolling()
    }
}
</script>

<style scoped>
.maxnbox{
    background-image: url(../../assets/img/login_background_us.png);
    background-repeat: repeat-x;
    background-size: 100% 100%;
    overflow: hidden;
}
.login-wrap {
    position: absolute;
    left:50%;
    top:50%;
    transform: translate(-50%, -50%);
    background: rgb(255, 255, 255);
    margin:0 auto;
    width: 720px;
    border-radius: 1%;
    overflow: hidden;
}
.login-wrap:hover {
    box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.2);
}
.login-head {
    text-align: center;
    height: 30px;
    margin-top: 10px;
    margin-bottom: 10px;
}
.login-title {
    color: #6f7072;
    padding: 5px;
    font-weight: 600;
    text-shadow: 0 1px 0 #fff;
    font-size: 26px;
}
.contet {
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 5px;
}
.contet span {
    font-weight: 600;
    font-size: 15px;
}
.contet p {
    text-indent:25px
}
.contetB {
    padding-left: 55px;
}
.foot {
    float: right;
    margin-top: 10px;
    margin-right: 20px;
}
</style>