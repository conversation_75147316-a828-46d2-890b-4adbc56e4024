<template>
  <div class="opera_div" >
    <body id="investmentRisk" class="investmentRisk_body">
      <div id="container" >
        <header style="height:10.8vh;">
          <Row>
            <Col span="1" >

            <!-- <div class="container"> -->
              <Tooltip :content="$t('home.clickRefresh')">
                <div class="xfClass" >
                    <div class="loader" >
                      <div class="loader2">
                        <svg
                        @click="refreshHome()"
                            style="width: 3vh;height: 3vh;cursor: pointer;"
                          >
                            <use xlink:href="#sx"></use>
                          </svg>
                      </div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:1"></div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:2"></div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:3"></div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:4"></div>
                        
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:5"></div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:6"></div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:7"></div>
                        <div class="petal" name="petal" @mouseover="tensile(this)" style="--i:8"></div>
                    </div>
                </div>
              </Tooltip>
                  <!-- <div class="progress"></div> -->
              <!-- </div> -->
              
            </Col>
            <!-- 导航条 -->
            <Col span="23">
              <div class="lampPole_lighting_top">
                <div class="li">
                  <svg
                    style="float: left; margin-top: 2.5vh; margin-left: 2.5vh"
                    width="2.5vw"
                    height="2.5vw"
                  >
                    <use xlink:href="#chip"></use>
                  </svg>
                  <p class="p1">{{ onlineFrom.count }}</p>
                  <p class="p2">{{ $t("home.totalNumber") }}</p>
                </div>
                <div class="li">
                  <svg
                    style="float: left; margin-top: 2.5vh"
                    width="2.5vw"
                    height="2.5vw"
                  >
                    <use xlink:href="#online"></use>
                  </svg>
                  <p class="p1">
                    <span class="span1"
                      >{{ onlineFrom.probability }}%
                      <Icon type="ios-fastforward" />
                      {{ onlineFrom.online }}</span
                    >
                  </p>
                  <p class="p2">
                    {{ $t("home.onlineRate") }} / {{ $t("home.number") }}
                  </p>
                </div>
                <div class="li">
                  <svg
                    style="float: left; margin-top: 2.5vh"
                    width="2.5vw"
                    height="2.5vw"
                  >
                    <use xlink:href="#lamp"></use>
                  </svg>
                  <p class="p1">
                    <span class="span1"
                      >{{ screenFrom.ok }}% <Icon type="ios-fastforward" />
                      {{ screenFrom.screen }}</span
                    >
                  </p>
                  <p class="p2">
                    {{ $t("home.brightScreen") }} / {{ $t("home.number") }}
                  </p>
                </div>
                <div class="li">
                  <svg
                    style="float: left; margin-top: 2.5vh"
                    width="2.5vw"
                    height="2.5vw"
                  >
                    <use xlink:href="#statistical"></use>
                  </svg>
                  <p class="p1">
                    <span class="span1">{{ operationCount }}</span>
                  </p>
                  <p class="p2">{{ $t("home.operating") }}</p>
                </div>
                <div class="li">
                  <svg
                    style="float: left; margin-top: 2.5vh"
                    width="2.5vw"
                    height="2.5vw"
                  >
                    <use xlink:href="#statistical"></use>
                  </svg>
                  <p class="p1">
                    <span class="span1"
                      >{{ showNum.showCount }} <Icon type="ios-fastforward" />
                      {{ showNum.showOk }}</span
                    >
                  </p>
                  <p class="p2">{{ $t("home.show") }}/{{ $t("home.by") }}</p>
                </div>
                <div class="li">
                  <svg
                    style="float: left; margin-top: 2.5vh"
                    width="2.5vw"
                    height="2.5vw"
                  >
                    <use xlink:href="#switch"></use>
                  </svg>
                  <p class="p1">
                    <i-Switch v-model="switchTwo" @on-change="changeTwo">
                    </i-Switch>
                  </p>
                  <p class="p2">
                    {{ $t("home.switchDate") }}/{{ $t("home.month") }}
                  </p>
                </div>
              </div>
            </Col>
          </Row>
        </header>
        <!-- 展示界面 -->
        <main style="margin-top:-0.8vh;">
          <div class="m" style="flex: 0 0 25%;" >
            <div class="m-t">
              <div class="chart-wrap">
                <h3>
                  <svg style="float: left" width="3.5vh" height="3.5vh">
                    <use xlink:href="#query"></use>
                  </svg>
                  {{ $t("home.Announcement") }}
                </h3>
                <!-- 公告详情 -->
                <div class="chartA" id="ec03_barV_timeDistribute">
                  <div v-if="announcementList.length > 0">
                    <Scroll
                      :on-reach-bottom="queryAnnouncement"
                      :height="documentClientHeight * 0.26"
                      :distance-to-edge="scrollDistance"
                      style="overflow-y: auto"
                    >
                      <Card
                        dis-hover
                        v-for="(item, index) in announcementList"
                        :key="index"
                        class="scrollCard"
                      >
                        <a @click="detailsClick(index)" class="aText"
                          ><div class="cardTitle">{{ item.title }}</div>
                          <div class="cardTIme">
                            --{{ item.createTime.substring(0, 10) }}
                          </div>
                        </a>
                      </Card>
                    </Scroll>
                  </div>
                 <div v-else style="text-align: center;height:5.6vh;">
                    <div style="margin-top:10vh;">
                    {{$t("home.temporarilyNoData")}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="m-b">
              <div class="chart-wrap">
                <!-- 审核通过率 -->
                <h3>
                  <svg style="float: left" width="3.5vh" height="3.5vh">
                    <use xlink:href="#query"></use>
                  </svg> 
                  {{ $t("home.Reviewrate") }}
                </h3>
                <div id="showdashboard" style="height: 26vh; width: 18vw;"></div>
              </div>
            </div>
          </div>
          <div class="m">
            <div class="m-t">
              <div class="chart-wrap">
                <h3>
                  <svg style="float: left" width="3.5vh" height="3.5vh">
                    <use xlink:href="#query"></use>
                  </svg>
                  {{$t('home.warningNotice')}}
                </h3>
                <div class="chartA" id="ec04_pie_computerBroken">
                  <div v-if="alarmList.length > 0">
                    <Scroll
                      :on-reach-bottom="queryAlarm"
                      :height="documentClientHeight * 0.26"
                      :distance-to-edge="scrollDistance"
                    >
                      <Card
                        dis-hover
                        v-for="(item, index) in alarmList"
                        :key="index"
                        class="scrollCard"
                      >
                        <a @click="alarmClick(index)" class="bText"
                          >
                          <div class="alarmTitle">{{item.cardId}} - 
                            <span v-if="item.type == 'temperature'">{{$t('home.temperatureWarning')}}</span>
                            <span v-else-if="item.type == 'humidity'">{{$t('home.humidityWarning')}}</span>
                            <span v-else-if="item.type == 'voltage'">{{$t('home.voltageWarning')}}</span>
                            <span v-else-if="item.type == 'voltage1'">{{$t('home.voltage1Warning')}}</span>
                            <span v-else-if="item.type == 'voltage2'">{{$t('home.voltage2Warning')}}</span>
                            <span v-else-if="item.type == 'doorOpened'">{{$t('home.doorOpenWarning')}}</span>
                            <span v-else-if="item.type == 'smoke'">{{$t('home.smokeWarning')}}</span>
                            <span  v-else>{{$t('home.unknownWarning')}}</span>
                            </div>
                          <div class="cardTIme">
                            --{{ item.createTime.substring(0, 10) }}
                          </div>
                        </a>
                      </Card>
                    </Scroll>
                  </div>
                  <div v-else style="text-align: center;height:5.6vh;">
                    <div style="margin-top:10vh;">
                     {{$t("home.temporarilyNoData")}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="m-b">
              <!-- 节目创建详情 -->
              <div class="chart-wrap">
                <h3>
                  <svg style="float: left" width="3.5vh" height="3.5vh">
                    <use xlink:href="#query"></use>
                  </svg>
                  {{ $t("home.programmeStatistics") }}
                </h3>
                <div id="show" class="chart-soShow"></div>
              </div>
            </div>
          </div>
          <!-- 580px 260px-->
          <div class="m" style="flex: 0 0 41.66%">
            <div class="m-t">
              <!-- 节目发布详情 -->
              <div class="chart-wrap">
                <h3>
                  <svg style="float: left" width="3.5vh" height="3.5vh">
                    <use xlink:href="#query"></use>
                  </svg>
                  {{ $t("home.statistics") }}
                  <div
                    style="float: right; font-size: 1.8vh; margin-top: 0.5vh;margin-left=4.5vw;"
                  >
                  <span style="font-size:0.2vw;">({{$t('home.showsSentProgram')}})</span>  {{ $t("home.cardNumber") }}
                    <i-Switch v-model="CardShow" @on-change="SetCardShowType">
                    </i-Switch>
                    {{ $t("home.show") }}
                  </div>
                </h3>
                <Row>
                  <Col span="6">
                    <div class="circle">
                      <div class="circlePO">
                        <p class="p2">
                          <span class="span1">{{
                            $t("home.releaseAmount")
                          }}</span>
                        </p>
                        <p class="p1" style="margin-top: 0.5vh">
                          {{ logFrom.logCount }}
                        </p>
                      </div>
                      <div class="circleP">
                        <p class="p2">
                          <span class="span1">{{
                            $t("home.successRate")
                          }}</span>
                        </p>
                        <p class="p1" style="margin-top: 0.5vh">
                          {{ logFrom.logProbability }}%
                        </p>
                      </div>
                      <div class="circleP">
                        <p class="p2">
                          <span class="span1">{{
                            $t("home.totalSuccess")
                          }}</span>
                        </p>
                        <p class="p1" style="margin-top: 0.5vh">
                          {{ logFrom.logOk }}
                        </p>
                      </div>
                    </div>
                  </Col>
                  <Col span="18">
                    <div id="log" class="logClass">
                      <Table
                        stripe
                        border
                        :columns="cardShowConlums"
                        :data="cardShowList"
                        :height="documentClientHeight * 0.24"
                        :loading="cardShowLoading"
                        @on-row-click="SelectOne"
                        size="small"
                      >
                        <template slot-scope="{ row }" slot="probability">
                          {{ row.probability }}%
                        </template>
                      </Table>
                      <Page
                        style="float: right; margin-top: 0.8vh"
                        :total="totalPage"
                        :current="pageIndex"
                        :page-size="pageSize"
                        show-elevator
                        show-total
                        size="small"
                        @on-change="currentChangeHandle"
                      />
                      <!-- 点击某一行展示节目发布对话框 -->
                      <Modal
                        width="800"
                        v-model="SelectOnemodal"
                        :title="ModalTitle"
                        @on-visible-change="ModalClear"
                      >
                        <Table
                          stripe
                          :columns="ModalConlums"
                          :data="ModalList"
                          :max-height="ModalTableHeight"
                          :loading="ModaldataListLoading"
                        >
                          <template slot-scope="{ row }" slot="status">
                            <div v-if="row.status == 0">
                              {{ $t("home.successful") }}
                            </div>
                            <div v-else>{{ $t("home.failure") }}</div>
                          </template>
                        </Table>
                        <div slot="footer">
                          <Page
                            :total="selectOnePage"
                            :current="selectOneIndex"
                            :page-size="selectOneSize"
                            show-elevator
                            show-total
                            @on-change="ModalcurrentChangeHandle"
                          />
                        </div>
                      </Modal>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>
            <div class="m-b">
              <!-- 操作详情 -->
              <div class="chart-wrap">
                <h3>
                  <svg style="float: left" width="3.5vh" height="3.5vh">
                    <use xlink:href="#query"></use>
                  </svg>
                  {{ $t("home.operationStatistics") }}
                </h3>
                <Row>
                  <Col span="13"
                    ><div id="option" class="chart-soOperation"></div
                  ></Col>
                  <Col span="11"
                    ><div id="timeAvg" class="chart-soOperationAvg"></div
                  ></Col>
                </Row>
              </div>
            </div>
          </div>
        </main>
      </div>
    </body>
    <!-- 公告详情 -->
    <Modal v-model="detailsModal" class="qzcxQzc" >
      <div slot="header"  >
        <div class="details-title">
          <span>{{$t("home.announcementDetails")}}</span>
        </div>
      </div>
      <div class="detailsDiv" style="height:420px;">
        <Form :model="announcementFrom" label-colon>
          <FormItem :label="$t('announcement.title')">
            <Input readonly v-model="announcementFrom.title"></Input>
          </FormItem>
          <FormItem :label="$t('announcement.content')">
            <Input
              readonly
              type="textarea"
              :autosize="{ minRows: 8, maxRows: 8 }"
              v-model="announcementFrom.text"
            ></Input>
          </FormItem>
          <FormItem :label="$t('home.releasePeople')">
            <Input readonly v-model="announcementFrom.createUserName"></Input>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="details-footer">
        {{ $t("home.date") }}：{{
          announcementFrom.createdDate
        }}
      </div>
    </Modal>
    <!-- 报警详情 -->
    <Modal v-model="alarmModal" class="qzcxQzc">
      <div slot="header" >
        <div class="details-title">
          {{$t("home.policeDetails")}}
        </div>
      </div>
      <div class="detailsDiv">
        <Form   label-colon>
          <FormItem :label="$t('home.cardNumber')">
            <Input
              readonly
              type="text"
              v-model="alarmFrom.cardId"
            ></Input>
          </FormItem>
          <FormItem :label="$t('menu.type')">
            <span v-if="alarmFrom.type == 'temperature'"><Input readonly type="text" :value="$t('home.temperatureWarning')"></Input></span>
            <span v-else-if="alarmFrom.type == 'humidity'"><Input readonly type="text" :value="$t('home.voltageWarning')"></Input></span>
            <span v-else-if="alarmFrom.type == 'voltage'"><Input readonly type="text" :value="$t('home.voltageWarning')"></Input></span>
            <span v-else-if="alarmFrom.type == 'voltage1'"><Input readonly type="text" :value="$t('home.voltage1Warning')"></Input></span>
            <span v-else-if="alarmFrom.type == 'voltage2'"><Input readonly type="text" :value="$t('home.voltage2Warning')"></Input></span>
            <span v-else-if="alarmFrom.type == 'doorOpened'"><Input readonly type="text" :value="$t('home.doorOpenWarning')"></Input></span>
            <span v-else-if="alarmFrom.type == 'smoke'"><Input readonly type="text" :value="$t('home.smokeWarning')"></Input></span>
            <span  v-else><Input readonly type="text" :value="$t('home.unknownWarning')"></Input></span>
          </FormItem>
          <FormItem :label="$t('common.state')">
            <span v-if="alarmFrom.status == '0'" ><Input readonly type="text" :value="$('home.untreated')"></Input></span>
            <span v-else><Input readonly type="text" :value="$t('home.haveDeal')"></Input></span>
          </FormItem>
        </Form>
        
      </div>
      <div slot="footer" class="details-footer">
        {{ $t("home.date") }}：{{
          alarmFrom.createdDate
        }}
      </div>
    </Modal>
  </div>
</template>

<script>
var myDate = new Date();
var year = myDate.getFullYear();
var month = myDate.getMonth() + 1; // 获取当前月份(1-12,1代表1月)
var day = myDate.getDate(); // 获取当前日(1-31)
var dateList = [];
var yearList = [];
for (var i = 6; i >= 0; i--) {
  //取出今天的月日到6天前的年月
  if (month - i <= 0) {
    // 1-6 月 倒扣一年
    if (12 + (month - i) < 10) {
      yearList.push(year - 1 + ".0" + (12 + (month - i)));
    } else {
      yearList.push(year - 1 + "." + (12 + (month - i)));
    }
  } else {
    // 7-12 月
    if (month - i < 10) {
      yearList.push(year + ".0" + (month - i));
    } else {
      yearList.push(year + "." + (month - i));
    }
  }
  // ------------------------------------------
  var md = "";
  if (day - i <= 0) {
    //取出今天的年月到6个月前的月日
    var a = month - 1;
    if (
      a === 1 ||
      a === 3 ||
      a === 5 ||
      a === 7 ||
      a === 8 ||
      a === 10 ||
      a === 12
    ) {
      md = a + "." + (31 + (day - i));
    } else if (a === 4 || a === 6 || a === 9 || a === 11) {
      md = a + "." + (30 + (day - i));
    } else {
      if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {
        md = a + "." + (29 + (day - i));
      } else {
        md = a + "." + (28 + (day - i));
      }
    }
  } else {
    if (day - i<10) {md = month + ".0" + (day - i);}
    else{
      md = month + "." + (day - i);
    }
    
  }
  dateList.push(md);
}
export default {
  data() {
    return {
      loadNum: 1,
      // 1930  960
      // scrollHeight: document.documentElement.clientHeight * 0.26,
      scrollDistance: [13, 13],
      page: 1, // 公告分页
      limit: 5,
      pages: 0,
      alarmPage: 1,
      alarmLimit: 5,
      alarmPages: 0,
      screenFrom: {
        // 亮屏表单
        count: "",
        ok: "",
        screen: "",
      },
      onlineFrom: {
        // 在线表单
        count: "",
        online: "",
        probability: "",
      },
      detailsModal: false,
      announcementFrom: {
        // 便签公告
        annoId: "", // 编号
        title: "", // 标题
        text: "", // 内容
        createdDate: "", // 创建时间
        createUserName: "", // 创建人名字
        deleted: "", //状态
      },
      announcementList: [], // 公告集合
      online: 0,
      switchTwo: false, // 日月状态
      switchTwoType: "day",
      timeAvg: 0, // 平均操作毫秒数
      operationCount: 0, // 操作总量
      optionCountList: [], // 总数数据集合
      optionAvgList: [], // 平均毫秒数
      load: false,
      showList: [], //节目集合
      showOKList: [], //节目审核通过集合
      showNum: {
        showCount: 0,
        showOk: 0,
        showProbability: 0, // 节目审核通过率
      },
      logFrom: {
        //节目发布渲染类
        logCount: 0,
        logOk: 0,
        logProbability: 0,
      },
      CardShow: false, // 卡节目状态
      CardShowType: "card", // 节目名
      // cardShowHeight: document.documentElement.clientHeight * 0.245, // 表格高度
      cardShowLoading: false, // 是否加载中
      // 根据卡或者节目展示成功率
      cardShowConlums: [
        {
          title: this.$t("menu.name"),
          key: "name",
          align: "center",
          width: this.documentClientWidth * 0.073,
          tooltip: true,
        },
        {
          title: this.$t("home.successRate"),
          key: "probability",
          align: "center",
          slot: "probability",
        },
        {
          title: this.$t("home.founder"),
          key: "createName",
          align: "center",
          width: this.documentClientWidth * 0.057,
          tooltip: true,
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          align: "center",
          width: this.documentClientWidth * 0.068,
          tooltip: true,
        },
      ],
      ModalConlums: [
        {
          title: this.$t("file.SerialNumber"),
          key: "LogId",
          align: "center",
          width: 160,
          tooltip: true,
        },
        {
          title: this.$t("home.cardNumber"),
          key: "deviceId",
          align: "center",
          width: 160,
          tooltip: true,
        },
        {
          title: this.$t("program.name"),
          key: "programName",
          align: "center",
          width: 160,
          tooltip: true,
        },
        {
          title: this.$t("common.state"),
          key: "status",
          align: "center",
          slot: "status",
        },
        {
          title: this.$t("common.createTime"),
          key: "createdDate",
          align: "center",
          width: 160,
          tooltip: true,
        },
      ],
      cardShowList: [], //卡的节目集合
      // 分页查询
      pageIndex: 1, //  第几页
      pageSize: 5, // 每页几条
      totalPage: 0, // 总共几条
      //点击某一行分页查询 节目发布统计
      rowIndex: null, // 记录第几行
      selectOneIndex: 1,
      selectOneSize: 10, // 每页几条
      selectOnePage: 0, // 总共几条
      SelectOnemodal: false,
      ModalTitle: "",
      ModalList: [],
      ModalTableHeight: 550,
      ModaldataListLoading: false,
      alarmList: [], //报警集合
      alarmModal: false,
      alarmFrom:"", //选中的报警对象
      typeList: ["1", "2", "3", "4", "5", "6", "7","8"], //要执行的操作类型
      optionEcharts: '', //渲染组件
      timeAvgEcharts: '',
      showEcharts: '',
      showdashboardEcharts: '',
    };
  },
  methods: {
    selectOperation() {
      this.resize() //所有分页查询从第一页开始
      // 首页批量查询
      this.optionCountList = [];
      this.optionAvgList = [];
      this.showList = [];
      this.showOKList = [];
      this.$http({
        url: this.$http.adornUrl("/screen/card/queryOperation"),
        method: "post", // 执行操作类型、日或月、日月集合
        data: this.$http.adornData(
          {
            typeList: this.typeList, //  集合，判断要执行哪些查询功能
            date: this.switchTwoType, // day或month
            page: this.page, // 公告查询第几页
            limit: this.limit, // 公告查询每页大小
            pageIndex: this.pageIndex, //  节目发布第几页
            pageSize: this.pageSize, // 节目发布每页几条
            cardShowType: this.CardShowType, // 节目发布分页查询根据卡号或节目名查询
            alarmPage: this.alarmPage,
            alarmLimit: this.alarmLimit
          },
          false
        ),
      }).then(({ data }) => {
        var list = this.switchTwoType === "day" ? dateList : yearList; // 日期或月份展示
        for (var i in this.typeList) {
          if (this.typeList[i] === "1") {
            this.timeAvg = data.msg.timeAvg; //取出操作平均毫秒数
            this.operationCount = data.msg.operationCount; // 取出log表操作总数
            for (var i in list) {
              var booleanCount = true;
              ///查询log表操作数据
              for (var n in data.msg.operation) {
                if (list[i] === data.msg.operation[n].createDate) {
                  booleanCount = false;
                  this.optionCountList.push(
                    data.msg.operation[n].count == null
                      ? 0
                      : data.msg.operation[n].count
                  );
                  this.optionAvgList.push(
                    data.msg.operation[n].time == null
                      ? 0
                      : data.msg.operation[n].time
                  );
                  continue;
                }
              }
              if (booleanCount) {
                this.optionCountList.push(0);
                this.optionAvgList.push(0);
              }
            }
            this.optionEchartsHandle()
            this.timeAvgEchartsHandle()
          } else if (this.typeList[i] === "2") {
            this.onlineFrom = data.msg.onlineFrom; // 取出在线率
          } else if (this.typeList[i] === "3") {
            this.screenFrom = data.msg.screenFrom; // 取出亮屏率
          } else if (this.typeList[i] === "4") {
            this.pages = data.msg.AnnouncementPage.pages; // 公告页数
            this.announcementList = data.msg.AnnouncementPage.records; // 取出公告
          } else if (this.typeList[i] === "5") {
            this.showNum = data.msg.showNum; //取出节目表单
            for (var i in list) {
              var booleanCount = true;
              var booleanOK = true;
              ///取出节目审核数据
              for (var n in data.msg.show.count) {
                if (list[i] === data.msg.show.count[n].createdDate) {
                  booleanCount = false;
                  this.showList.push(
                    data.msg.show.count[n].count == null
                      ? 0
                      : data.msg.show.count[n].count
                  );
                  continue;
                }
              }
              for (var n in data.msg.show.ok) {
                if (list[i] === data.msg.show.ok[n].createdDate) {
                  booleanOK = false;
                  this.showOKList.push(
                    data.msg.show.ok[n].count == null
                      ? 0
                      : data.msg.show.ok[n].count
                  );
                  continue;
                }
              }
              if (booleanCount) {
                this.showList.push(0);
              }
              if (booleanOK) {
                this.showOKList.push(0);
              }
            }
            // 分段总量审核量
            this.showEchartsHandle()
            // 审核通过率
            this.showdashboardEchartsHandle()
          } else if (this.typeList[i] === "6") {
            // 节目发布 数量统计
            this.logFrom = data.msg.logFrom;
            this.logFrom.logProbability = this.logFrom.logProbability;
          } else if (this.typeList[i] === "7") {
            // 节目发布 分页查询
            this.cardShowList = data.msg.logPageFrom.records;
            this.pageIndex = data.msg.logPageFrom.current;
            this.totalPage = data.msg.logPageFrom.total;
          }  else if (this.typeList[i] === "8") {
            //报警通知
              this.alarmPages = data.msg.alarmPage.pages
              this.alarmList  = data.msg.alarmPage.records
          }
          // 8....
        }
      });
    },
    alarmClick(index){
      // 报警详情查询单个  查询数据库，防止刷新
     this.$http({
        url: this.$http.adornUrl("/sys/Alarm/one/"+this.alarmList[index].id),
        method: "Get",
      }).then(({data}) =>{
        this.alarmFrom = data.msg.alarmEntity;
        this.alarmModal = true;
      })
    },
    // 单击节目发布表某一行数据
    SelectOne(row) {
      (this.rowIndex = row),
        this.$http({
          url: this.$http.adornUrl("/program/playLog/selectLogOne"),
          method: "post",
          params: this.$http.adornParams({
            type: this.CardShowType,
            name: row.name,
            selectOneIndex: this.selectOneIndex,
            selectOneSize: this.selectOneSize,
          }),
        }).then(({ data }) => {
          (this.ModalList = data.records),
          (this.selectOneIndex = data.current),
          (this.selectOnePage = data.total), // 总共几条
          (this.ModalTitle = this.CardShow
            ? this.$t("program.name") + "：" + row.name
            : this.$t("home.cardNumber") + "：" + row.name),
          (this.SelectOnemodal = true)
        });
    },
    ModalClear() {
      // 关闭模态框触发
      if (this.SelectOnemodal) {
      } else {
        (this.selectOneIndex = 1),
          (this.selectOnePage = 0), // 总共几条
          (this.ModalTitle = ""),
          (this.ModalList = []);
      }
    },
    ModalcurrentChangeHandle(val) {
      this.selectOneIndex = val;
      this.SelectOne(this.rowIndex);
    },
    // 当前页  分页查询
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.queryLogList();
    },
    // 切换卡号或节目名查询
    SetCardShowType() {
      this.pageIndex = 1;
      if (this.CardShow) {
        this.CardShowType = "show";
      } else {
        this.CardShowType = "card";
      }
      this.queryLogList();
    },
    //局部刷新节目发布集合
    queryLogList() {
      this.cardShowList = [];
      this.cardShowLoading = true;
      this.$http({
        url: this.$http.adornUrl("/screen/card/queryOperation"),
        method: "post", // 执行操作类型、日或月、日月集合
        data: this.$http.adornData(
          {
            typeList: ["7"], //  集合，判断要执行哪些查询功能
            pageIndex: this.pageIndex, //  节目发布第几页
            pageSize: this.pageSize, // 节目发布每页几条
            cardShowType: this.CardShowType, // 节目发布分页查询根据卡号或节目名查询
          },
          false
        ),
      }).then(({ data }) => {
        // 节目发布 分页查询
        this.cardShowList = data.msg.logPageFrom.records;
        this.pageIndex = data.msg.logPageFrom.current;
        this.totalPage = data.msg.logPageFrom.total;
        this.cardShowLoading = false;
      });
    },
    queryAlarm() {
      //无线滚动条报警通知
      this.alarmPage = this.alarmPage + 1;
      if (this.alarmPage > this.alarmPages) {
        this.$Message.warning(this.$t('home.noMoreCalls'));
        return;
      }
      return new Promise((resolve) => {
        this.$http({
          url: this.$http.adornUrl("/screen/card/queryOperation"),
          method: "post", 
          data: this.$http.adornData(
            {
              typeList: ["8"],
              alarmPage: this.alarmPage,
              alarmLimit: this.alarmLimit
            },
            false
          ),
        }).then(({ data }) => {
          for (var a in data.msg.alarmPage.records) {
            // 取出公告
            this.alarmList.push(data.msg.alarmPage.records[a]);
          }
          resolve();
        });
      });
    },
    queryAnnouncement() {
      //无限滚动条公告分页
      this.page = this.page + 1;
      if (this.page > this.pages) {
        this.$Message.warning(this.$t("home.noMoreAnnouncements"));
        return;
      }
      return new Promise((resolve) => {
        this.$http({
          url: this.$http.adornUrl("/screen/card/queryOperation"),
          method: "post", 
          data: this.$http.adornData(
            {
              typeList: ["4"],
              page: this.page,
              limit: this.limit,
            },
            false
          ),
        }).then(({ data }) => {
          for (var a in data.msg.AnnouncementPage.records) {
            // 取出公告
            this.announcementList.push(data.msg.AnnouncementPage.records[a]);
          }
          resolve();
        });
      });
    },
    detailsClick(index) {
      // 公告详情查询单个  查询数据库，防止刷新
     this.$http({
        url: this.$http.adornUrl("/sys/Announcement/one/"+this.announcementList[index].annoId),
        method: "Get",
      }).then(({data}) =>{
        this.announcementFrom = data.msg.SysAnn;
        this.announcementFrom.createUserName = data.msg.username;
        this.detailsModal = true;
      })
    },
    changeTwo(status) {
      // 切换月或者日
      if (status) {
        this.switchTwoType = "month";
      } else {
        this.switchTwoType = "day";
      }
      this.selectOperation();
    },
    optionEchartsHandle () {
      if (this.optionEcharts != null && this.optionEcharts != "" && this.optionEcharts != undefined) {
        this.optionEcharts.dispose();//销毁
      }
      this.optionEcharts = this.$echarts.init(document.getElementById("option")) // 渲染操作折线图
      this.optionEcharts.setOption(this.setOption());
    },
    timeAvgEchartsHandle () {
      if (this.timeAvgEcharts != null && this.timeAvgEcharts != "" && this.timeAvgEcharts != undefined) {
        this.timeAvgEcharts.dispose();//销毁
      }
      this.timeAvgEcharts = this.$echarts.init(document.getElementById("timeAvg")) // 渲染操作平均毫秒数
      this.timeAvgEcharts.setOption(this.setTimeAvg());
    },
    showEchartsHandle () {
      if (this.showEcharts != null && this.showEcharts != "" && this.showEcharts != undefined) {
        this.showEcharts.dispose();//销毁
      }
      this.showEcharts = this.$echarts.init(document.getElementById("show"))
      this.showEcharts.setOption(this.setShow());
    },
    showdashboardEchartsHandle () {
      if (this.showdashboardEcharts != null && this.showdashboardEcharts != "" && this.showdashboardEcharts != undefined) {
        this.showdashboardEcharts.dispose();//销毁
      }
      this.showdashboardEcharts = this.$echarts.init(document.getElementById("showdashboard"))
      this.showdashboardEcharts.setOption(this.setShowDashboard());
    },
    setTimeAvg() {
      // 渲染操作平均毫秒数
      this.timeAvg = this.timeAvg + "";
      var maxa = "1";
      for (var i = 0; i < this.timeAvg.length - 1; i++) {
        maxa = maxa + "0";
      }

      var option = {
        series: [
          {
            type: "gauge",
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 10,
            splitNumber: 10,
            itemStyle: {
              color: "#58D9F9",
              shadowColor: "rgba(0,138,255,0.45)",
              shadowBlur: 10,
              shadowOffsetX: 1,
              shadowOffsetY: 1,
            },
            progress: {
              show: true,
              roundCap: true,
              width: 12,
            },
            pointer: {
              icon: "path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z",
              length: "65%",
              width: 10,
              offsetCenter: [0, "25%"],
            },
            axisLine: {
              roundCap: true,
              lineStyle: {
                width: 12,
              },
            },
            axisTick: {
              splitNumber: 4,
              lineStyle: {
                width: 2,
                color: "#999",
              },
            },
            splitLine: {
              length: 12,
              lineStyle: {
                width: 3,
                color: "#999",
              },
            },
            axisLabel: {
              distance: 15,
              color: "#999",
              fontSize: 12,
            },
            title: {
              show: true,
              offsetCenter: [0, "-120%"],
            },
            detail: {
              backgroundColor: "#fff",
              borderColor: "#999",
              borderWidth: 2,
              width: "80%",
              lineHeight: 30,
              height: 20,
              borderRadius: 8,
              offsetCenter: [0, "80%"],
              valueAnimation: true,
              formatter: function (value) {
                return "{value|" + value + "}";
              },
              rich: {
                value: {
                  fontSize: 20,
                  fontWeight: "bolder",
                  color: "#777",
                },
              },
            },
            data: [
              {
                value: this.timeAvg / maxa,
                name:
                  this.$t("home.TotalAverageMilliseconds") +
                  " x " +
                  maxa +
                  " ms",
              },
            ],
          },
        ],
      };

      return option;
    },
    setShowDashboard() {
      var _this = this;
      // 审核通过率
      var ShowDashboard = {
        series: [
          {
            type: "gauge",
            startAngle: 360,
            endAngle: 0,
            min: 0,
            max: 1,
            splitNumber: 8,
            axisLine: {
              lineStyle: {
                width: 3,
                color: [
                  [0.25, "#FF6E76"],
                  [0.5, "#FDDD60"],
                  [0.75, "#58D9F9"],
                  [1, "#7CFFB2"],
                ],
              },
            },
            pointer: {
              icon: "path://M12.8,0.7l12,40.1H0.7L12.8,0.7z",
              length: "18%",
              width: 15,
              offsetCenter: [0, "-45%"],
              itemStyle: {
                color: "auto",
              },
            },
            axisTick: {
              length: 12,
              lineStyle: {
                color: "auto",
                width: 2,
              },
            },
            splitLine: {
              length: 20,
              lineStyle: {
                color: "auto",
                width: 5,
              },
            },
            axisLabel: {
              color: "#464646",
              fontSize: 20,
              distance: -60,
              formatter: function (value) {
                if (value === 0.875) {
                  return _this.$t("home.great");
                } else if (value === 0.625) {
                  return _this.$t("home.good");
                } else if (value === 0.375) {
                  return _this.$t("home.center");
                } else if (value === 0.125) {
                  return _this.$t("home.poor");
                }
                return "";
              },
            },
            title: {
              offsetCenter: [0, "-20%"],
              fontSize: 15,
            },
            detail: {
              fontSize: 20,
              offsetCenter: [0, "0%"],
              valueAnimation: true,
              formatter: function (value) {
                return Math.round(value * 100) + "%";
              },
              color: "inherit",
            },
            data: [
              {
                value: this.showNum.showProbability,
                name: "",
              },
            ],
          },
        ],
      };
      return ShowDashboard;
    },
    setShow() {
      //节目审核渲染
      var show = {
        title: {
          text: "",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [this.$t("home.show"), this.$t("home.by")],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.switchTwoType === "day" ? dateList : yearList,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: this.$t("home.show"),
            type: "line",
            data: this.showList,
            smooth: true,
          },
          {
            name: this.$t("home.by"),
            type: "line",
            data: this.showOKList,
            smooth: true,
          },
        ],
      };
      return show;
    },
    setOption() {
      // 操作渲染
      var option = {
        title: {
          text: "",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [this.$t("home.operat"), this.$t("home.operatingSpeed")],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.switchTwoType === "day" ? dateList : yearList,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: this.$t("home.operat"),
            type: "line",
            data: this.optionCountList,
            smooth: true,
          },
          {
            name: this.$t("home.operatingSpeed"),
            type: "line",
            data: this.optionAvgList,
            smooth: true,
          },
        ],
      };
      return option;
    },
    resize () {
    this.page = 1;
    this.alarmPage = 1;
    this.pageIndex = 1;
    },
    refreshHome() {
      this.selectOperation();
      if(this.loadNum !== 1){
        this.optionEcharts.resize();
        this.timeAvgEcharts.resize();
        this.showEcharts.resize();
        this.showdashboardEcharts.resize();
        this.$Notice.success({
          title: this.$t("common.tips"),
          desc: this.$t('home.refreshSuccessful'),
          duration: 3,
        });
      }
      
      this.loadNum ++;
    },
    tensile (ob) {
      // console.log(document.getElementsByName("petal"))
    }
  },
  activated() {
    let that = this
    //切换界面时重新加载/首次加载
    
    this.selectOperation();
    if(this.loadNum !== 1){
      this.optionEcharts.resize();
      this.timeAvgEcharts.resize();
      this.showEcharts.resize();
      this.showdashboardEcharts.resize();
    }
    this.loadNum ++;
  },
  watch: {
    'language': function(newVal, OldVal) {
      this.optionEchartsHandle()
      this.timeAvgEchartsHandle()
      this.showEchartsHandle()
      this.showdashboardEchartsHandle()
    }
  },
  computed: {
    language: {
      get () { return this.$store.state.language.language },
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      }
    },
    documentClientHeight: {
      get () { return this.$store.state.common.documentClientHeight }
    },
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth }
    },
  },
  mounted() {
    let that = this
    if (this.optionEcharts == null) {
      this.optionEchartsHandle()
    }
    if (this.timeAvgEcharts == null) {
      this.timeAvgEchartsHandle()
    }
    if (this.showEcharts == null) {
      this.showEchartsHandle()
    }
    if (this.showdashboardEcharts == null) {
      this.showdashboardEchartsHandle()
    }
    window.addEventListener("resize", () => { 
      that.optionEcharts.resize();
      that.timeAvgEcharts.resize();
      that.showEcharts.resize();
      that.showdashboardEcharts.resize();
    });
  },
};
</script>

<style scoped>
@import "../../assets/css/map2d.css";
@import "../../assets/css/common.css";
@import "../../assets/css/index.css";
@import "../../assets/css/globe3d.css";
@import "../../assets/css/styles.css";
.opera_div {
  height: 90vh;
  width: 87vw;
  border-radius: 1%;
  clear: both;
  background-color: rgb(249, 255, 255);
  overflow-y: auto;
}
.lampPole_lighting_top {
  margin: -2.5vh 0px 0px 40px;
  display: flex;
  width: 78vw;
  height: 10vh;
  border-radius: 0.2rem;
  border: 1px solid #8ec1f1;
  background: linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) right bottom,
    linear-gradient(#00faff, #00faff) right bottom;
  background-repeat: no-repeat;
  background-size: 3px 20px, 20px 3px;
}
.li {
  width: calc(25% - 0.1rem);
  text-align: center;
}
.line {
  margin-top: 1.5rem;
  width: 0.2rem;
  height: 4.2rem;
  background-color: #f0f0f0;
}
.p1 {
  margin-top: 1.1rem;
  font-size: 1.2vw;
  font-family: PingFang-SC-Heavy, PingFang-SC;
  font-weight: 800;
  color: #333;
}
.p2 {
  font-size: 1vw;
  font-family: PingFang-SC-Medium, PingFang-SC;
  font-weight: 500;
  color: #999;
}
.chartA {
  height: 27.5vh;
  padding: 3vh;
  margin-top: -4vh;
}
.pText {
  display: block;
  height: 3.7vh;
  width: 14vw;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.aText {
  color: black;
  width: 4vw;
  white-space: nowrap;
  font-size: 1.5vh;
}
.bText {
  color: black;
  width: 4vw;
  white-space: nowrap;
  font-size: 1.5vh;
}
.chart-soShow {
  width: 25vw;
  height: 25vh;
}
.chart-soOperation {
  width: 20vw;
  height: 25vh;
}
.chart-soOperationAvg {
  width: 15vw;
  height: 25vh;
  margin-top: 2vh;
  margin-left: 1vw;
}

.imgClass {
  width: 5vh;
  height: 5vh;
  cursor: pointer;
}
.circle {
  width: 10vh;
  height: 10vh;
  text-align: center;
}
.logClass {
  width: 26vw;
  height: 23vh;
  margin-left: -2vw;
  margin-top: -1.2vh;
}
.circlePO {
  margin-left: 0.5vw;
  width: 5vw;
  height: 8vh;
  background: linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) right bottom,
    linear-gradient(#00faff, #00faff) right bottom;
  background-repeat: no-repeat;
  background-size: 3px 15px, 15px 3px;
}
.circleP {
  margin-top: 1vh;
  margin-left: 0.5vw;
  width: 5vw;
  height: 8vh;
  background: linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) right bottom,
    linear-gradient(#00faff, #00faff) right bottom;
  background-repeat: no-repeat;
  background-size: 3px 15px, 15px 3px;
}
.scrollCard {
  margin: 5px 0;
  height: 5.6vh;
  background: linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) left top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) right top,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) left bottom,
    linear-gradient(#00faff, #00faff) right bottom,
    linear-gradient(#00faff, #00faff) right bottom;
  background-repeat: no-repeat;
  background-size: 3px 30px, 30px 3px;
}
.cardTitle {
  font-size: 2vh;
  margin-top: -1vh;
  color: rgb(131, 139, 218);
}
.alarmTitle {
  font-size: 1.8vh;
  margin-top: -1vh;
  color: rgb(131, 139, 218);
}
.cardTIme {
  float: right;
  margin-top: -1vh;
  color: rgb(172, 180, 180);
}
.cardTitle:hover {
  color: rgb(252, 144, 144);
  -webkit-animation: Glow 1s ease infinite alternate;
  animation: Glow 1s ease infinite alternate;
}
@keyframes Glow {
  from {
    text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px rgb(112, 97, 250),
      0 0 40px rgb(134, 131, 167), 0 0 70px rgb(157, 154, 184);
  }
  to {
    text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px rgb(112, 97, 250),
      0 0 40px rgb(134, 131, 167), 0 0 70px rgb(157, 154, 184);
  }
}
.detailsDiv {
  height: 330px;
  overflow-y: auto;
}
.qzcxQzc {
  display: flex;
  justify-content: center;
  align-items: center;
}
.details-title {
  text-align: center;
  font-size: 20px;
}
.details-footer {
  text-align: center;
  font-size: 20px;
}
.xfClass {
  width:7vh;
  height:7vh;
}
</style>
