<template>
  <Modal v-model="visible" width="900">

    <p slot="header" style="text-align:center">
      <span>{{ $t('crossArrow.arrowProgram') }}</span>
    </p>
    <Tabs :value="tabValue" @on-click="handlerTab" >
      <TabPane :label="this.$t('common.query')" name="query">
        <div>
          <Button  type="primary" :loading="queryLoading" @click="queryArrowStatus()">{{$t('crossArrow.curProgram') }}</Button>
        </div>
          <div v-if="queryData.length > 0" style="height: 495px;overflow-y: auto">
            <div style="color: red; display: inline-block;margin-right: 20px;margin-bottom: 20px" v-for="(rItem, rIndex) in queryData" :key="rIndex" >
              <div style="display: inline-block" v-if="rItem._type==='success'">
                <Card style="width:400px">
                  <p slot="title">
                    {{rItem.deviceId}}
                  </p>
                  <div v-if="rItem.name==='red'||rItem.name==='green'||rItem.name==='gray'">
                    <img :src="require(`@/assets/img/${rItem.name}Arrow.png`)" style="height: 50px;width: 50px;"/>
                  </div>
                  <div v-else>
                    <p>
                      {{rItem.name}}
                    </p>
                  </div>
                </Card>
              </div>
              <div style="display: inline-block" v-if="rItem._type!=='success'">
                <Card style="width:400px">
                  <p slot="title">
                    {{rItem.deviceId}}
                  </p>
                 <p>
                   {{rItem.msg}}
                 </p>
                </Card>
              </div>
            </div>
          </div>
      </TabPane>
      <TabPane :label="this.$t('common.set')" name="set">
        <Tooltip :content=" $t('crossArrow.redArrow')">
          <div style="height: 50px;width: 50px; display: inline-block;margin-top: 5px;margin-right: 10px; "  @click="clickPlay(1)">
            <img src="@/assets/img/redArrow.png" height="50px" width="50px" />
          </div>
        </Tooltip>

        <Tooltip :content="$t('crossArrow.greenArrow')">
          <div style="height: 50px;width: 50px; display: inline-block;margin-top: 5px;margin-right: 10px;  "  @click="clickPlay(2)" >
            <img src="@/assets/img/greenArrow.png" height="50px" width="50px"/>
          </div>
        </Tooltip>
        <Tooltip :content="$t('crossArrow.closeScreen')">
          <div style="height: 50px;width: 50px; display: inline-block; margin-top: 5px;margin-right: 10px; "  @click="clickPlay(3)">
            <img src="@/assets/img/grayArrow.png"  height="50px" width="50px"/>
          </div>
        </Tooltip>

        <!--      <Button  type="error" :loading="redLoading" @click="clickPlay(1)">{{ $t('crossArrow.redArrow') }}</Button>-->
        <!--      <Button  type="success" :loading="greenLoading" @click="clickPlay(2)">{{ $t('crossArrow.greenArrow') }}</Button>-->
        <!--      <Button :loading="closeLoading"  @click="clickPlay(3)" >{{ $t('crossArrow.closeScreen') }}</Button>-->
        <div style="height: 500px">
          <div v-if="resultData.length > 0" style="height: 495px;overflow-y: auto">
            <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </div>
      </TabPane>
    </Tabs>


    <div slot="footer" style="text-align: left;">
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"

export default {
  name: "device-arrow-program",
  data() {
    return {
      visible: false,
      ids: [],
      resultData: [],
      tabValue:"query",
      queryData: [],
      queryLoading:false,
    }
  },
  components: {
    cardResult
  },
  methods: {
    // 初始化
    init(ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
      }
      this.resultData = []
      this.queryData=[]
      this.queryLoading=false
      this.tabValue="query"

    },
    //点播
    clickPlay(order) {
      if (this.ids.length > 0) {
        this.resultData = []
        this.$http({
          url: this.$http.adornUrl('/card/set/clickPlay'),
          method: 'post',
          data: this.$http.adornData({
            "deviceIds": this.ids,
            "order": order
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
        })
      }
    },
    closeScreen(status) {
      if (this.ids.length > 0) {
        this.resultData = []
        this.closeLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/screenSwitch'),
          method: 'post',
          data: this.$http.adornData({'ids': this.ids, 'status': status}, false)
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg);
          }
          this.closeLoading = false
        })
      }
    },

    //查询箭头状态
    queryArrowStatus(){
      if (this.ids.length > 0) {
        this.queryLoading=true
        this.queryData = []
        this.$http({
          url: this.$http.adornUrl('/card/query/curProgramName'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.queryData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.queryLoading=false
        })
      }
    },
    handlerTab () {
      this.resultData = []
      this.queryData=[]
      this.queryLoading=false
    },
  },
}
</script>

<style scoped>
</style>
