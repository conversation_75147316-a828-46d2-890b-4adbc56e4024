<template>
  <Modal v-model="addToCrossArrowVisible" width="900">
    <p slot="header" style="text-align:center">
      <span>{{ $t("crossArrow.addToCross") }}</span>
    </p>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1,0)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(null,1,0)" size="large">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataColumns" :data="dataList" @on-selection-change="selectionChangeHandle"
           @on-row-click="selectThisRow"
           :loading="dataListLoading" :height="tableHeightData" ref="selection">
      <template slot-scope="{ row, index }" slot="number">
        {{index+1}}
      </template>
      <template slot-scope="{ row, index }" slot="deviceId">
        {{row.deviceId}}
        <span v-if="row.msg === 1">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#send"></use>
          </svg>
        </span>
        <span v-if="row.msg === 2">
          <Poptip placement="right-start" v-if="row.text && row.text !== ''"
                  trigger="hover" transfer :title="$t('common.tips')" :content="tipChange(row.text)">
            <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
              <use xlink:href="#fail"></use>
            </svg>
          </Poptip>
          <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#fail"></use>
          </svg>
        </span>
        <span v-if="row.msg === 3">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#success"></use>
          </svg>
        </span>
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div>
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use :xlink:href="row.isOn === 1 ? '#on-line' : '#line'"></use>
          </svg>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="netType">
        <div v-if="row.netType === 'WIFI'">
          {{row.netType}}
          <svg v-if="row.rssi >= -50 && row.rssi <= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level4"></use>
          </svg>
          <svg v-else-if="row.rssi >= -70 && row.rssi < -50" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level3"></use>
          </svg>
          <svg  v-else-if="row.rssi >= -80 && row.rssi < -70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level2"></use>
          </svg>
          <svg v-else-if="row.rssi >= -100 && row.rssi < -80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level1"></use>
          </svg>
        </div>
        <div v-else-if="row.netType === 'LTE' || row.netType === 'UMTS' || row.netType === 'HSPA'
         || row.netType === 'HSPA+' || row.netType === 'EDGE'  || row.netType === 'GPRS'">
          {{row.netType}}
          <svg v-if="row.asu >= 12 && row.asu != 99" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-0"></use>
          </svg>
          <svg v-else-if="row.asu >= 8 && row.asu  < 12" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-1"></use>
          </svg>
          <svg  v-else-if="row.asu >= 5 && row.asu < 8" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-2"></use>
          </svg>
          <svg v-else-if="row.asu >= 3 && row.asu < 5" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-3"></use>
          </svg>
        </div>
        <div v-else>
          {{row.netType}}
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="resolvingPower">
        <div v-if="row.width && row.height">{{row.width}} * {{row.height}}</div>
        <div v-else></div>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex"
          :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <div slot="footer">
      <Button size="large" @click="crossCancelSelect()">{{$t('common.cancel')}}</Button>
      <Button type="primary" size="large" @click="crossDataFormSubmit()">{{$t('common.confirm')}}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        key: '',
        group: []
      },
      dataColumns:  [
        {type: 'selection', width: 60,fixed: 'left', align: 'center'},
        {title: 'ID', key: 'deviceId',fixed: 'left', align: 'center', slot: 'deviceId'},
        {title: this.$t('cardDevice.deviceName'), key: 'alias',  align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.deviceName'))
          }
        },
        {title: this.$t('cardDevice.online'), key: 'isOn',  slot: 'online', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.online'))
          }
        },
        {title: this.$t('cardDevice.networkType'), key: 'netType',align: 'center', slot: 'netType',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.networkType'))
          }
        },
        {title: this.$t('cardDevice.resolvingPower'), slot: 'resolvingPower', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.resolvingPower'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      //是否第一次打开该页面
      isFirst: true,
      //总数量
      totalNum: 0,
      tableHeightData: 0,
      isCrossArrow: 1,
      addToCrossArrowVisible: false,
    }
  },
  methods: {
    // 初始化数据
    init() {
      this.addToCrossArrowVisible = true
      this.dataForm = {
        key: '',
        group: []
      }
      this.dataList = []
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListLoading = false
      this.dataListSelections = []
      this.isFirst = true
      //总数量
      this.totalNum = 0
      this.tableHeightData = this.tableHeight
      this.getDataList(null, null, 0)
    },

    // 获取数据列表
    getDataList(loading, isQuery, isCrossArrow) {
      if (loading) {
        this.dataListLoading = true
      }
      if (isQuery === 1) {
        this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/crossArrow/card/list'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex + "",
          'limit': this.pageSize + "",
          'key': this.dataForm.key,
          'group': this.dataForm.group,
          'isCrossArrow': isCrossArrow
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount

          if (this.isFirst) {
            this.totalNum = data.page.totalCount
            this.isFirst = false
          }
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage > this.totalPage && this.totalPage !== 0) {
            this.pageIndex = 1
            this.getDataList(loading, isQuery, isCrossArrow)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.getDataList(null, null, 0)
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList(null, null, 0)
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 关闭弹窗并触发自定义事件通知list页面
    closePopup() {
      this.addToCrossArrowVisible = false;
      this.$emit('close-modal');
    },
    //取消
    crossCancelSelect(){
      this.addToCrossArrowVisible=false
    },
    //tip国际化
    tipChange(tip){
      if (tip=="控制卡连接已断开"){
        return this.$t('monitor.offLineOrNotExist')
      }else {
        return this.$t('log.connectionClosed')
      }
    },
    //提交，将设备添加到十字箭头
    crossDataFormSubmit() {
      // 获取已选的卡
      var deviceIds = null
      if (this.dataListSelections.length > 0) {
        deviceIds = this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/enableCrossArrow/'+1),
          method: 'post',
          data: deviceIds
        }).then(({data}) => {
          if (data && data.code === 0) {
            //成功后点播
            this.clickPlay(1,deviceIds)
            // this.$Message.success("success")
            this.closePopup();
          }else {
            this.$Message.error(data.msg)
          }
        })
        this.addToCrossArrowVisible = false
      }
    },
    //点播
    clickPlay(order,deviceIds) {
      if (deviceIds.length > 0) {
        this.resultData = []
        this.$http({
          url: this.$http.adornUrl('/card/set/clickPlay'),
          method: 'post',
          data: this.$http.adornData({
            "deviceIds": deviceIds,
            "order": order
          })
        }).then(({data}) => {
        })
      }
    },
  },

  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight - 155
      }
    }
  },
}
</script>
