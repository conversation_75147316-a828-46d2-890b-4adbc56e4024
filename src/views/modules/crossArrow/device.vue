<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(null,1,1)" size="large">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
        <Button style="margin-right:6px" type="primary" @click="addDevices()" size="large">
          <div style="margin:3px 8px">{{ $t('common.add') }}</div>
        </Button>
        <Button style="margin-right:6px" size="large" @click="changeGroup()">
          <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
        </Button>
        <Button style="margin-right:6px" @click="getTerminalInfo()" :loading="terminalInfoLoading" type="primary"
                size="large" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{ $t('cardDevice.queryTerminalInfo') }}</div>
        </Button>
        <Button style="margin-right:6px" size="large" type="error" @click="batchRemove()">
          <div style="margin:3px 8px">{{$t('crossArrow.batchRemove')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataColumns" :data="dataList" @on-selection-change="selectionChangeHandle"
           @on-row-click="selectThisRow"
           :loading="dataListLoading" :height="tableHeightData" ref="selection">
      <template slot-scope="{ row, index }" slot="number">
        {{index+1}}
      </template>
      <template slot-scope="{ row, index }" slot="deviceId">
        {{row.deviceId}}
        <span v-if="row.msg === 1">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#send"></use>
          </svg>
        </span>
        <span v-if="row.msg === 2">
          <Poptip placement="right-start" v-if="row.text && row.text !== ''"
                  trigger="hover" transfer :title="$t('common.tips')" :content="tipChange(row.text)">
            <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
              <use xlink:href="#fail"></use>
            </svg>
          </Poptip>
          <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#fail"></use>
          </svg>
        </span>
        <span v-if="row.msg === 3">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#success"></use>
          </svg>
        </span>
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div>
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use :xlink:href="row.isOn === 1 ? '#on-line' : '#line'"></use>
          </svg>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="netType">
        <div v-if="row.netType === 'WIFI'">
          {{row.netType}}
          <svg v-if="row.rssi >= -50 && row.rssi <= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level4"></use>
          </svg>
          <svg v-else-if="row.rssi >= -70 && row.rssi < -50" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level3"></use>
          </svg>
          <svg  v-else-if="row.rssi >= -80 && row.rssi < -70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level2"></use>
          </svg>
          <svg v-else-if="row.rssi >= -100 && row.rssi < -80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level1"></use>
          </svg>
        </div>
        <div v-else-if="row.netType === 'LTE' || row.netType === 'UMTS' || row.netType === 'HSPA'
         || row.netType === 'HSPA+' || row.netType === 'EDGE'  || row.netType === 'GPRS'">
          {{row.netType}}
          <svg v-if="row.asu >= 12 && row.asu != 99" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-0"></use>
          </svg>
          <svg v-else-if="row.asu >= 8 && row.asu  < 12" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-1"></use>
          </svg>
          <svg  v-else-if="row.asu >= 5 && row.asu < 8" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-2"></use>
          </svg>
          <svg v-else-if="row.asu >= 3 && row.asu < 5" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
            <use xlink:href="#gsm-3"></use>
          </svg>
        </div>
        <div v-else>
          {{row.netType}}
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="resolvingPower">
        <div v-if="row.width && row.height">{{row.width}} * {{row.height}}</div>
        <div v-else></div>
      </template>
      <template slot-scope="{ row, index }" slot="brightness">
        <div v-if="row.brightnessPercentage && row.brightnessPercentage != -1">{{row.brightnessPercentage}}%</div>
        <div v-else>{{row.brightness}}</div>
      </template>
      <template slot-scope="{ row, index }" slot="currentProgramName">
        <div v-if="row.currentProgramName && row.currentProgramName=='暂无节目'">
          {{ $t('broadcast.noProgram')  }}
        </div>
        <div v-else>
          {{row.currentProgramName}}
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="screenStatus">
        <div>
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use :xlink:href="row.screenStatus === 'on' ? '#on' : '#off'"></use>
          </svg>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="temperature">
        <div v-if="row.temperature">
          {{row.temperature}} ℃
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button  :disabled="row._disabled" type="error" size="small" style="margin-right: 5px;font-size: 11px"  @click="removeOne(row.deviceId)">{{$t('crossArrow.remove')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex"
          :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

    <!--分组弹出框-->
    <Modal v-model="selectGroupVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{$t('common.selectGroup')}}</span>
      </p>
      <Alert type="info" show-icon >
        <span>{{this.$t('tips.groupTip')}}</span>
      </Alert>
      <div>
        <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
      </div>
      <div slot="footer">
        <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>

    <!-- 功能菜单-->
    <div style="height: 20px; background:#eee; clear: both;">
      <svg v-if="!isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuixiaohua"></use>
      </svg>
      <svg v-if="isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuidahua"></use>
      </svg>
    </div>
    <div v-if="!isMinimize" class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in operation" :key="index">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{ $t(item.text) }}</div>
            </div>
            <div class="opera_list" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{ $t(item.text) }}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div v-if="isMinimize" style="height: 50px;" class="opera_div">
      <ul class="opera_ul1">
        <div v-for="(item, index) in operation" :key="index" :title="$t(item.text)">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list1" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
            <div class="opera_list1" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
          </li>
        </div>
      </ul>
    </div>

    <no-cross-arrow-device v-if="addToCrossArrowVisible"  @close-modal="getDataList(null,null,1)" ref="noCrossArrow"></no-cross-arrow-device>
    <device-arrow-program v-if="arrowProgramVisible" ref="arrowProgram"></device-arrow-program>
    <device-screen-brightness v-if="brightnessVisible" ref="brightness"></device-screen-brightness>
  </div>
</template>

<script>
import noCrossArrowDevice from "./device/no-cross-arrow-device";
import deviceArrowProgram from "./device/device-arrow-program";
import deviceScreenBrightness from "../screen/device/device-screen-brightness";


export default {
  components:{
    noCrossArrowDevice,
    deviceArrowProgram,
    deviceScreenBrightness
  },
  data() {
    return {
      dataForm: {
        key: '',
        group: []
      },
      dataColumns:  [
        {type: 'selection', width: 60,fixed: 'left', align: 'center'},
        {title: this.$t('cardDevice.number'), width: 70,fixed: 'left', align: 'center',slot: 'number',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.number'))
          }
        },
        {title: 'ID', key: 'deviceId',fixed: 'left', width: 170, align: 'center', slot: 'deviceId'},
        {title: this.$t('cardDevice.deviceName'), key: 'alias', width: 130, align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.deviceName'))
          }
        },
        {title: this.$t('cardDevice.online'), key: 'isOn', width: 100, slot: 'online', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.online'))
          }
        },
        {title: this.$t('cardDevice.networkType'), key: 'netType', width: 130,align: 'center', slot: 'netType',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.networkType'))
          }
        },
        {title: this.$t('cardDevice.resolvingPower'), slot: 'resolvingPower', width: 145, align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.resolvingPower'))
          }
        },
        {title: this.$t('cardDevice.programTask'), key: 'currentProgramName', width: 130, align: 'center', tooltip: true,slot: 'currentProgramName' ,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.programTask'))
          }
        },
        {title: this.$t('cardDevice.brightness'), key: 'brightness', width: 115, align: 'center', slot: 'brightness',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.brightness'))
          }
        },
        {title: this.$t('cardDevice.volume'), key: 'volume', width: 90, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.volume'))
          }
        },
        {title: this.$t('cardDevice.screenStatus'), key: 'screenStatus', width: 135, align: 'center', slot: 'screenStatus',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.screenStatus'))
          }
        },
        {title: this.$t('cardDevice.temperature'), key: 'temperature', width: 125, align: 'center', slot: 'temperature',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.temperature'))
          }
        },
        {title: this.$t('cardDevice.fireWare'), key: 'fireware',width: 245, align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.fireWare'))
          }
        },
        {title: 'CardSystem', key: 'ledsetVersion',width: 130, align: 'center', tooltip: true},
        {title: 'Conn', key: 'connVersion',width: 130, align: 'center', tooltip: true},
        {title: 'Player', key: 'playerVersion',width: 130, align: 'center', tooltip: true},
        {title: 'Starter', key: 'starterVersion',width: 140, align: 'center', tooltip: true},
        {title: 'Display', key: 'displayVersion',width: 140, align: 'center', tooltip: true},
        {title: this.$t('operation.group'), key: 'groupName', align: 'center', width: 100, tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('operation.group'))
          }
        },
        {title: "IP", key: 'realIp', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div','IP')
          }
        },
        {title: this.$t('cardDevice.lastOffline'), key: 'lastOffTime', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.lastOffline'))
          }
        },

        {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', width: 150, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        }
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      terminalInfoLoading: false,
      groupList: [],
      operation: [
        {
          id: 'arrowProgram',
          icon: 'arrowProgram',
          text: 'crossArrow.arrowProgram',
          disable: false,
          auth: 'crossArrow:program',
          checked: true
        },
        {
          id: 'screenBrightness',
          icon: 'liangdu',
          text: 'operation.screenBrightness',
          disable: false,
          auth: 'device:screenBrightness',
          checked: true
        },

      ],
      setLightingBrightness: false,
      lightingSwitchVisible: false,
      groupVisible: false,
      //选择分组时，分组框是否可见
      selectGroupVisible:false,
      //  分组名
      groupName:"",
      rootNode:null,
      //是否第一次打开该页面
      isFirst:true,
      //总数量
      totalNum:0,
      tableHeightData: 0,
      isMinimize: false,
      addToCrossArrowVisible:false,
      arrowProgramVisible:false,
      brightnessVisible:false,
    }
  },
  activated() {
    this.initData()
    this.getDataList('loading',null,1)
  },
  methods: {
    // 初始化数据
    initData() {
      this.dataForm = {
        key: '',
        group: []
      }
      this.dataList = []
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListLoading = false
      this.dataListSelections = []
      this.groupName=""
      this.rootNode=null
      this.isFirst=true
      //总数量
      this.totalNum=0
      this.terminalInfoLoading= false
      this.tableHeightData = this.tableHeight
    },
    // 查询分组列表
    getGroupList() {
      this.$http({
        url: this.$http.adornUrl('/sys/group/crossArrowList'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.groupList = data.group
          this.getUnGroupDevice()
        } else {
          this.groupList = []
        }
      })
    },
    // 获取数据列表
    getDataList(loading,isQuery,isCrossArrow) {
      if (loading) {
        this.dataListLoading = true
      }
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/crossArrow/card/list'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex+"",
          'limit': this.pageSize+"",
          'key': this.dataForm.key,
          'group': this.dataForm.group,
          'isCrossArrow':isCrossArrow
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataForm.group.length===0){
            this.groupName=""
          }
          if (this.isFirst){
            this.totalNum=data.page.totalCount
            this.isFirst=false
          }
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(loading,isQuery,isCrossArrow)
          }
          // this.dataForm.group=[]
          // 设置选中
          var select = this.$refs.selection.getSelection().map(item => {
            return item.deviceId
          })
          if (select && select.length !== 0) {
            this.dataList.map(item => {
              if (select.indexOf(item.deviceId) != -1) {
                item._checked = true
              } else {
                item._checked = false
              }
            })
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },

    removeOne(deviceId){
      var deviceIds=[];
      deviceIds.push(deviceId)
      this.removeToLamp(deviceIds)
    },
    batchRemove(){
      var deviceIds = null
      if (this.dataListSelections.length > 0) {
        deviceIds = this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.removeToLamp(deviceIds)
      }
    },

    //从十字箭头中移除
    removeToLamp(deviceIds){
      this.$Modal.confirm({
          title: this.$t('common.tips'),
          content: this.$t('common.delete_current_option'),
          okText: this.$t('common.confirm'),
          cancelText: this.$t('common.cancel'),
          onOk: () => {
            this.$http({
              url: this.$http.adornUrl('/lampPole/card/enableCrossArrow/'+0),
              method: 'post',
              data: deviceIds
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                    // this.$Message.success("success")
                    this.getDataList(null,null,1)
                  }})
              }else {
                this.$Message.error(data.msg)
              }
            })
          }})
    },

    //开放十字箭头功能
    addDevices(){
      this.addToCrossArrowVisible=true
      this.$nextTick(() => {
        this.$refs.noCrossArrow.init()
      })

    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.getDataList(null,null,1)
    },
    // 当前页
    currentChangeHandle(val) {
      console.log(val)
      this.pageIndex = val
      this.getDataList(null,null,1)
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 查询终端信息
    getTerminalInfo() {
      // 判断选中的列表不为空
      if (this.dataListSelections.length > 0) {
        var deviceIds = this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.dataList = this.dataList.filter(data => {
          if (deviceIds.indexOf(data.deviceId) !== -1) {
            data.msg = 1
            data._checked = true
          } else {
            data._checked = false
          }
          return data
        })
        this.terminalInfoLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getCardInformation'),
          method: 'post',
          data: deviceIds
        }).then(({ data }) => {
          if (data && data.cards) {
            for (let i = 0; i < data.cards.length; i++) {
              const element = data.cards[i];
              if (element.card.text) {
                element.card.msg = 2
              } else {
                element.card.msg = 3
              }
              element.card._checked = true
              this.dataList.forEach((item, index) => {
                if (element.card.deviceId.indexOf(item.deviceId) !== -1) {
                  this.dataList.splice(index, 1, element.card)
                } else {
                  item = item
                }
              })
            }
          } else {
            this.$Message.error(data.msg)
          }
          this.terminalInfoLoading = false
        });
      }
    },
    // 不支持
    operaErrorHandle(text) {
      this.$Message.warning({
        content: this.$t('common.supportedTip') + this.$t(text),
        duration: 2
      })
    },
    // 成功
    operaSuccessHandle(id, checked) {
      if (id) {
        if (checked === true) {
          // 获取已选的卡
          var deviceIds = null
          if (this.dataListSelections.length > 0) {
            deviceIds = this.dataListSelections.map(item => {
              return item.deviceId
            })
          } else {
            this.$Message.warning({
              content: this.$t('common.selectDevice'),
              duration: 2
            })
            return
          }
          if (deviceIds.length >= 1) {
            if (id === 'arrowProgram') { // 箭头节目
              this.arrowProgramVisible = true
              this.$nextTick(() => {
                this.$refs.arrowProgram.init(deviceIds)
              })
            }else if (id === 'screenBrightness') { // 亮度
              this.brightnessVisible = true
              this.$nextTick(() => {
                this.$refs.brightness.init(deviceIds)
              })
            }
          }
        } else if (checked === false) {
          // 不需要选中卡

        }
      }
    },
    changeGroup(){
      this.selectGroupVisible=true
    },
    //tip国际化
    tipChange(tip){
      if (tip=="控制卡连接已断开"){
        return this.$t('monitor.offLineOrNotExist')
      }else {
        return this.$t('log.connectionClosed')
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.dataForm.group=[]
      this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      this.getChildrenNodes(this.rootNode)
      this.selectGroupVisible=false
      this.getDataList(null,1,1)
      this.groupName=this.rootNode.name
      this.rootNode=null
    },
    //取消选择分组
    cancelSelect(){
      this.selectGroupVisible=false
      this.dataForm.group=[]
      this.groupName=""
      this.rootNode=null
      this.getDataList(null,null,1)
      this.getGroupList()
    },

    //获取该分组及其子分组的groupId
    getChildrenNodes(rootNode){
      this.dataForm.group.push(rootNode.id)
      var childNode=rootNode.children;
      if (childNode){
        for (var i=0; i<childNode.length; i++) {
          this.getChildrenNodes(childNode[i])
        }
      }
    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },

    // 最大化最小化
    handleMinimize (isMinimize) {
      this.isMinimize = !isMinimize
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight - 155
      }
    }
  },
  watch: {
    'totalNum': function (newVal, OldVal) {
      this.getGroupList();
    },
    'tableHeight': function(newVal, oldVal) {
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    }
  },
}
</script>

<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 180px;
  background: #eee;
  overflow: hidden;
  overflow-y: auto;
}

.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}

.opera_ul li {
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 100px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}

.opera_list {
  padding-top: 10px;
  width: 105px;
  height: 105px;
  margin-left: 8%;
}
.opera_ul li:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}

.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}
.opera_ul1 li{
  text-align: center;
  float: left;
  list-style: none;
  width: 40px;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list1 {
  padding-top: 5px;
  width: 40px;
  height: 40px;
}
.opera_ul1 li:hover {
  background-color: rgb(210, 174, 245);
  cursor: pointer;
}
.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}

.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
</style>
