<template>
  <div class="verify-line">
    <Row>
      <Col span="5">
        <Input size="large" v-model="key" :placeholder="$t('cardDevice.deviceName') + '/ID'" :border="false" @keyup.native="getDataList(true)"></Input>
        <Card shadow :style="{'height': tableHeight + 140 + 'px'}">
          <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight + 45"> -->
          <div :style="{'height':tableHeight + 45+ 'px', 'overflow-y': 'auto'}">
            <ul class="opera_ul" v-if="dataList && dataList.length > 0">
              <li v-for="(item, index) in dataList" :key="index" @click="changeSelect(item, index)">
                <div :class="select === index ? 'select': ''" style="padding-left: 10px;padding-right: 10px;">
                  {{item.deviceId}}
                  <span v-if="item.isOn === 1" style="float: right">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#on-line"></use>
                    </svg>
                  </span>
                  <span v-else style="float: right">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#line"></use>
                    </svg>
                  </span>
                </div>
              </li>
            </ul>
            <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
              {{$t('home.temporarilyNoData')}}
            </div>
          </div>
          <!-- </Scroll> -->
          <Page size="small" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-total @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
        </Card>
      </Col>
      <!-- <Col span="1"></Col> -->
      <Col span="19">
        <div  :style="{'height': tableHeight + 175 + 'px', 'overflow-y': 'auto'}">
          <div v-show="list && list.length > 0">
            <!-- 温度 -->
            <div ref="temperatureData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 湿度 -->
            <div ref="humidityData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 噪音 -->
            <div ref="noiseData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 风速 -->
            <div ref="windSpeedData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 风向 -->
            <div ref="windDirectionData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 光照度 -->
            <div ref="sensorBrightnessData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- pm10 -->
            <div ref="pm10Data" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- pm2.5 -->
            <div ref="pm2_5Data" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 气压 -->
            <div ref="pressureData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 雨量 -->
            <div ref="rainFallData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
            <!-- 辐射 -->
            <div ref="radiationData" :style="{'width': documentClientWidth - 530 + 'px', 'height': '310px'}"></div>
          </div>
          <div v-if="list.length === 0" class="no-data">
            <div>
              <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
                <use xlink:href="#notData"></use>
              </svg>
              <div>{{$t('home.temporarilyNoData')}}</div>
            </div>
          </div>
        </div>
      </Col>
    </Row>
  </div>
</template>

<script>
export default {
  data () {
    return {
      key: '',
      select: 0,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      startDate: '',
      endDate: '',
      list:[],
      date: [],
      // pm10
      pm10Data: [],
      pm10EChart: '',
      // pm2.5
      pm2_5Data: [],
      pm2_5DataEChart: '',
      // 温度
      temperatureData: [],
      temperatureEChart: '',
      // 噪音
      noiseData: [],
      noiseEChart: '',
      // 湿度
      humidityData: [],
      humidityEChart: '',
      // 光照度
      sensorBrightnessData: [],
      sensorBrightnessEChart: '',
      // 风向
      windDirectionData: [],
      windDirectionEChart: '',
      // 风速
      windSpeedData: [],
      windSpeedEChart: '',
      // 气压
      pressureData: [],
      pressureEChart: '',
      // 雨量
      rainFallData: [],
      rainFallEChart: '',
      // 辐射
      radiationData: [],
      radiationEChart: '',
    }
  },
  activated () {
    this.getDataList()
  },
  methods: {
    changeSelect(item, index) {
      this.select = index
      this.getCardInfo(item.deviceId)
    },
    handleReachBottom () {
      return new Promise(resolve => {
        setTimeout(() => {
          this.pageSize = this.pageSize + 10;
          if (this.pageSize > this.totalCount) {
            this.$Message.warning(this.$t('common.noMoreData'));
            return;
          }
          this.getDataList();
          resolve();
        }, 200);
      });
    },
    getCardInfo (id) {
      this.date =  []
      this.pm10Data =  []
      this.pm2_5Data =  []
      this.temperatureData =  []
      this.noiseData =  []
      this.humidityData =  []
      this.sensorBrightnessData =  []
      this.windDirectionData =  []
      this.windSpeedData =  []
      this.pressureData = []
      this.rainFallData = []
      this.radiationData = []
      this.list = []
      this.startDate = ''
      this.endDate = ''
      var url;
      var timezone=Intl.DateTimeFormat().resolvedOptions().timeZone;
      if(timezone !='Etc/GMT-8'){
        url="/environment/card/info/"+id+"?timezone="+timezone
      }else {
        url="/environment/card/info/"+id
      }
      this.$http({
        url: this.$http.adornUrl(url),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.values &&  data.values.list) {
            this.list = data.values.list
          }
          if (this.list && this.list.length > 0) {
            this.startDate = data.values.startDate
            this.endDate = data.values.endDate
            this.list.forEach(item => {
              this.date.push((item.time !== '' ? item.time.replace(" ", "\n") : ''));
              this.pm2_5Data.push(item.pm2_5 == -1 ? '--' : item.pm2_5);
              this.pm10Data.push(item.pm10 == -1 ? '--' : item.pm10);
              this.temperatureData.push(item.temperature == 255 ? '--' : item.temperature);
              this.noiseData.push(item.noise == -1 ? '--' : item.noise);
              this.humidityData.push(item.humidity == -1 ? '--' : item.humidity);
              this.sensorBrightnessData.push(item.sensorBrightness);
              this.windDirectionData.push(item.windDirection == -1 ? '--' : item.windDirection);
              this.windSpeedData.push(item.windSpeed == -1 ? '--' : item.windSpeed);
              this.radiationData.push(item.radiation == -1 ? '--' : item.radiation);
              this.pressureData.push(item.pressure == -1 ? '--' : item.pressure);
              this.rainFallData.push(item.rainFall == -1 ? '--' : item.rainFall);
            })
            this.myEcharts();
          }
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    getDataList (isQuery) {
      if (isQuery) {
         this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/environment/card/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.key,
        })
      }).then(({data}) => {
         if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataList && this.dataList.length > 0) {
            if (this.dataList[0].deviceId) {
              this.getCardInfo(this.dataList[0].deviceId)
              this.select = 0
            }
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
      })
    },
    myEcharts(){
      // 温度
      if (this.temperatureEChart != null && this.temperatureEChart != "" && this.temperatureEChart != undefined) {
        this.temperatureEChart.dispose();//销毁
      }
      // this.temperatureEChart = this.$echarts.init(document.getElementById('temperatureData'));
      this.temperatureEChart = this.$echarts.init(this.$refs.temperatureData);
      this.temperatureEChart.setOption(this.temperatureOption);
      // 湿度
      if (this.humidityEChart != null && this.humidityEChart != "" && this.humidityEChart != undefined) {
        this.humidityEChart.dispose();//销毁
      }
      // this.humidityEChart = this.$echarts.init(document.getElementById('humidityData'));
      this.humidityEChart = this.$echarts.init(this.$refs.humidityData);
      this.humidityEChart.setOption(this.humidityOption);
      // 噪音
      if (this.noiseEChart != null && this.noiseEChart != "" && this.noiseEChart != undefined) {
        this.noiseEChart.dispose();//销毁
      }
      // this.noiseEChart = this.$echarts.init(document.getElementById('noiseData'));
      this.noiseEChart = this.$echarts.init(this.$refs.noiseData);
      this.noiseEChart.setOption(this.noiseOption);
      // 风速
      if (this.windSpeedEChart != null && this.windSpeedEChart != "" && this.windSpeedEChart != undefined) {
        this.windSpeedEChart.dispose();//销毁
      }
      // this.windSpeedEChart = this.$echarts.init(document.getElementById('windSpeedData'));
      this.windSpeedEChart = this.$echarts.init(this.$refs.windSpeedData);
      this.windSpeedEChart.setOption(this.windSpeedOption);
      // 风向
      if (this.windDirectionEChart != null && this.windDirectionEChart != "" && this.windDirectionEChart != undefined) {
        this.windDirectionEChart.dispose();//销毁
      }
      // this.windDirectionEChart = this.$echarts.init(document.getElementById('windDirectionData'));
      this.windDirectionEChart = this.$echarts.init(this.$refs.windDirectionData);
      this.windDirectionEChart.setOption(this.windDirectionOption);
      // 光照度
      if (this.sensorBrightnessEChart != null && this.sensorBrightnessEChart != "" && this.sensorBrightnessEChart != undefined) {
        this.sensorBrightnessEChart.dispose();//销毁
      }
      // this.sensorBrightnessEChart = this.$echarts.init(document.getElementById('sensorBrightnessData'));
      this.sensorBrightnessEChart = this.$echarts.init(this.$refs.sensorBrightnessData);
      this.sensorBrightnessEChart.setOption(this.sensorBrightnessOption);
      // PM10
      if (this.pm10EChart != null && this.pm10EChart != "" && this.pm10EChart != undefined) {
        this.pm10EChart.dispose();//销毁
      }
      // this.pm10EChart = this.$echarts.init(document.getElementById('pm10Data'));
      this.pm10EChart = this.$echarts.init(this.$refs.pm10Data);
      this.pm10EChart.setOption(this.pm10Option);
      // pm2_5
      if (this.pm2_5DataEChart != null && this.pm2_5DataEChart != "" && this.pm2_5DataEChart != undefined) {
        this.pm2_5DataEChart.dispose();//销毁
      }
      // this.pm2_5DataEChart = this.$echarts.init(document.getElementById('pm2_5Data'));
      this.pm2_5DataEChart = this.$echarts.init(this.$refs.pm2_5Data);
      this.pm2_5DataEChart.setOption(this.pm2_5Option);
      // 辐射
      if (this.radiationEChart != null && this.radiationEChart != "" && this.radiationEChart != undefined) {
        this.radiationEChart.dispose();//销毁
      }
      this.radiationEChart = this.$echarts.init(this.$refs.radiationData);
      this.radiationEChart.setOption(this.radiationOption);
      // 气压
      if (this.pressureEChart != null && this.pressureEChart != "" && this.pressureEChart != undefined) {
        this.pressureEChart.dispose();//销毁
      }
      this.pressureEChart = this.$echarts.init(this.$refs.pressureData);
      this.pressureEChart.setOption(this.pressureOption);
      // 雨量
      if (this.rainFallEChart != null && this.rainFallEChart != "" && this.rainFallEChart != undefined) {
        this.rainFallEChart.dispose();//销毁
      }
      this.rainFallEChart = this.$echarts.init(this.$refs.rainFallData);
      this.rainFallEChart.setOption(this.rainFallOption);
    }
  },
  // mounted(){
    // this.myEcharts();
    // document.addEventListener('touchstart', function(event) {
    //   event.preventDefault();
    // }, false);
  // },
  // destroyed() {
  //   document.addEventListener('touchstart', function(event) {
  //     event.preventDefault();
  //   }, false);
  // },
  watch: {
    'language': function(newVal, OldVal) {
      this.myEcharts();
    }
  },
  computed: {
    language: {
      get () { return this.$store.state.language.language },
    },
    tableHeight: {
      get () { return this.$store.state.common.tableHeight },
    },
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth },
    },
    date1: {
      get () {
        return ['2022-03-25 00:00', '2022-03-25 01:00', '2022-03-25 02:00', '2022-03-25 03:00', '2022-03-25 04:00','2022-03-25 05:00',
                  '2022-03-25 06:00', '2022-03-25 07:00', '2022-03-25 08:00', '2022-03-25 09:00', '2022-03-25 10:00', '2022-03-25 11:00',
                  '2022-03-25 12:00', '2022-03-25 13:00', '2022-03-25 14:00', '2022-03-25 15:00', '2022-03-25 16:00', '2022-03-25 17:00',
                  '2022-03-25 18:00',  '2022-03-25 19:00','2022-03-25 20:00', '2022-03-25 21:00', '2022-03-25 22:00', '2022-03-25 23:00',
                  '2022-03-26 00:00', '2022-03-26 01:00', '2022-03-26 02:00', '2022-03-26 03:00', '2022-03-26 04:00','2022-03-26 05:00',
                  '2022-03-26 06:00', '2022-03-26 07:00', '2022-03-26 08:00', '2022-03-26 09:00', '2022-03-26 10:00', '2022-03-26 11:00',
                  '2022-03-26 12:00', '2022-03-26 13:00', '2022-03-26 14:00', '2022-03-26 15:00', '2022-03-26 16:00', '2022-03-26 17:00',
                  '2022-03-26 18:00',  '2022-03-26 19:00','2022-03-26 20:00', '2022-03-26 21:00', '2022-03-26 22:00', '2022-03-26 23:00',
                  '2022-03-27 00:00', '2022-03-27 01:00', '2022-03-27 02:00', '2022-03-27 03:00', '2022-03-27 04:00','2022-03-27 05:00',
                  '2022-03-27 06:00', '2022-03-27 07:00', '2022-03-27 08:00', '2022-03-27 09:00', '2022-03-27 10:00', '2022-03-27 11:00',
                  '2022-03-27 12:00', '2022-03-27 13:00', '2022-03-27 14:00', '2022-03-27 15:00', '2022-03-27 16:00', '2022-03-27 17:00',
                  '2022-03-27 18:00',  '2022-03-27 19:00','2022-03-27 20:00', '2022-03-27 21:00', '2022-03-27 22:00', '2022-03-27 23:00',
                  '2022-03-28 00:00', '2022-03-28 01:00', '2022-03-28 02:00', '2022-03-28 03:00', '2022-03-28 04:00','2022-03-28 05:00',
                  '2022-03-28 06:00', '2022-03-28 07:00', '2022-03-28 08:00', '2022-03-28 09:00', '2022-03-28 10:00', '2022-03-28 11:00',
                  '2022-03-28 12:00', '2022-03-28 13:00', '2022-03-28 14:00', '2022-03-28 15:00', '2022-03-28 16:00', '2022-03-28 17:00',
                  '2022-03-28 18:00',  '2022-03-28 19:00','2022-03-28 20:00', '2022-03-28 21:00', '2022-03-28 22:00', '2022-03-28 23:00',
                  '2022-03-29 00:00', '2022-03-29 01:00', '2022-03-29 02:00', '2022-03-29 03:00', '2022-03-29 04:00','2022-03-29 05:00',
                  '2022-03-29 06:00', '2022-03-29 07:00', '2022-03-29 08:00', '2022-03-29 09:00', '2022-03-29 10:00', '2022-03-29 11:00',
                  '2022-03-29 12:00', '2022-03-29 13:00', '2022-03-29 14:00', '2022-03-29 15:00', '2022-03-29 16:00', '2022-03-29 17:00',
                  '2022-03-29 18:00',  '2022-03-29 19:00','2022-03-29 20:00', '2022-03-29 21:00', '2022-03-29 22:00', '2022-03-29 23:00',
                  '2022-03-30 00:00', '2022-03-30 01:00', '2022-03-30 02:00', '2022-03-30 03:00', '2022-03-30 04:00','2022-03-30 05:00',
                  '2022-03-30 06:00', '2022-03-30 07:00', '2022-03-30 08:00', '2022-03-30 09:00', '2022-03-30 10:00', '2022-03-30 11:00',
                  '2022-03-30 12:00', '2022-03-30 13:00', '2022-03-30 14:00', '2022-03-30 15:00', '2022-03-30 16:00', '2022-03-30 17:00',
                  '2022-03-30 18:00',  '2022-03-30 19:00','2022-03-30 20:00', '2022-03-30 21:00', '2022-03-30 22:00', '2022-03-30 23:00',
                  '2022-03-31 00:00', '2022-03-31 01:00', '2022-03-31 02:00', '2022-03-31 03:00', '2022-03-31 04:00','2022-03-31 05:00',
                  '2022-03-31 06:00', '2022-03-31 07:00', '2022-03-31 08:00', '2022-03-31 09:00', '2022-03-31 10:00', '2022-03-31 11:00',
                  '2022-03-31 12:00', '2022-03-31 13:00', '2022-03-31 14:00', '2022-03-31 15:00', '2022-03-31 16:00', '2022-03-31 17:00',
                  '2022-03-31 18:00',  '2022-03-31 19:00','2022-03-31 20:00', '2022-03-31 21:00', '2022-03-31 22:00', '2022-03-31 23:00']
      }
    },
    data: {
      get () {
        return [30, 28, 25, 26, 27, 30, 30, 26, 30, 31, 31, 28, 24, 30, 30, 30, 30, 30, 28, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 31, 31, 30, 31, 31, 25, 24, 31, 30, 30, 30, 30, 30, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 25, 25, 30, 31, 31, 25, 24, 30, 30, 30, 30, 30, 30, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 30, 31, 30, 31, 31, 25, 24, 30, 30, 30, 28, 25, 26, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 25, 30, 30, 31, 31, 29, 24, 30, 28, 30, 30, 30, 28, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 27, 28, 30, 31, 31, 27, 24, 31, 28, 30, 30, 25, 25, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 30, 31, 30, 31, 31, 28, 24, 31, 28, 30, 30, 30, 30, 30, 30, 25, 30, 25]
      }
    },
    // 温度
    temperatureOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FFBF00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('cardDevice.temperature') + ' {c} ℃'
          },
          title: {
            left: 'center',
            text: this.$t('cardDevice.temperature'),
            subtext: this.$t('meteorological.temperatureSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} ℃'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              name: '温度',
              type: 'line',
              data: this.temperatureData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(224, 62, 76)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(255, 191, 0)'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    //湿度
    humidityOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#37A2FF'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('card.humidity') + ' {c} RH'
          },
          title: {
            left: 'center',
            text: this.$t('card.humidity'),
            subtext: this.$t('meteorological.humiditySubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} RH'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.humidityData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(116, 21, 219)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(55, 162, 255)'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 噪音
    noiseOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#80FFA5'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('program.noise') + ' {c} dB'
          },
          title: {
            left: 'center',
            text: this.$t('program.noise'),
            subtext: this.$t('meteorological.noiseSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} dB'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.noiseData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(1, 191, 236)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(128, 255, 165)'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 风速
    windSpeedOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#4169E1'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('program.windSpeed') + ' {c} m/s'
          },
          title: {
            left: 'center',
            text: this.$t('program.windSpeed'),
            subtext: this.$t('meteorological.windSpeedSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} m/s'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.windSpeedData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#4169E1'
                  },
                  {
                    offset: 1,
                    color: '#6495ED'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 风向
    windDirectionOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#ADD8E6'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('program.windDirection') + ' {c} °'
          },
          title: {
            left: 'center',
            text: this.$t('program.windDirection'),
            subtext: this.$t('meteorological.windDirectionSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} °'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.windDirectionData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#ADD8E6'
                  },
                  {
                    offset: 1,
                    color: '#B0E0E6'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 光照度
    sensorBrightnessOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#afb4db'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('meteorological.Illuminance') + ' {c}'
          },
          title: {
            left: 'center',
            text: this.$t('meteorological.Illuminance'),
            subtext: this.$t('meteorological.illuminationSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start:2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.sensorBrightnessData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#afb4db'
                  },
                  {
                    offset: 1,
                    color: '#9b95c9'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // PM10
    pm10Option: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FF8C00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> PM10 {c} μg/m³'
          },
          title: {
            left: 'center',
            text: 'PM10',
            subtext: this.$t('meteorological.PM10SubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} μg/m³'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.pm10Data,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#FF8C00'
                  },
                  {
                    offset: 1,
                    color: '#FFA500'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // pm2.5
    pm2_5Option: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FF8C00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> PM2.5 {c} μg/m³'
          },
          title: {
            left: 'center',
            text: 'PM2.5',
            subtext: this.$t('meteorological.PM25SubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} μg/m³'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.pm2_5Data,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#FF8C00'
                  },
                  {
                    offset: 1,
                    color: '#FFA500'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 辐射
    radiationOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FF8C00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> '+ this.$t('meteorological.radiation') +' {c} W/m²'
          },
          title: {
            left: 'center',
            text: this.$t('meteorological.radiation'),
            subtext: this.$t('meteorological.radiationSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} W/m²'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.radiationData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#9BA4AF'
                  },
                  {
                    offset: 1,
                    color: '#C1CCD7'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 气压
    pressureOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FF8C00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> '+ this.$t('meteorological.pressure') +' {c} hPa'
          },
          title: {
            left: 'center',
            text: this.$t('meteorological.pressure'),
            subtext: this.$t('meteorological.pressureSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} hPa'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.pressureData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#296DFF'
                  },
                  {
                    offset: 1,
                    color: '#75AAFF'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    // 雨量
    rainFallOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FF8C00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> '+ this.$t('meteorological.rainFall') +' {c} mm'
          },
          title: {
            left: 'center',
            text: this.$t('meteorological.rainFall'),
            subtext: this.$t('meteorological.rainFallSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.date,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} mm'
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
              endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
            },
            {
              type: 'inside',
              start: 2,
              end: 2
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              type: 'line',
              data: this.rainFallData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#21CCCC'
                  },
                  {
                    offset: 1,
                    color: '#8FEBE1'
                  }
                ])
              },
            }
          ]
        }
      }
    }
  }
}
</script>

<style scoped>
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  background-color: rgb(255, 255, 255);
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-bottom: 5px;
  cursor: pointer;
}
.opera_ul li:hover {
  background-color: rgb(204, 204, 204);
  cursor: pointer;
}
.select {
  background-color: rgb(204, 204, 204);
}
</style>

/* let date = ['2022-03-26 00:00', '2022-03-26 01:00', '2022-03-26 02:00', '2022-03-26 03:00', '2022-03-26 04:00','2022-03-26 05:00',
                  '2022-03-26 06:00', '2022-03-26 07:00', '2022-03-26 08:00', '2022-03-26 09:00', '2022-03-26 10:00', '2022-03-26 11:00',
                  '2022-03-26 12:00', '2022-03-26 13:00', '2022-03-26 14:00', '2022-03-26 15:00', '2022-03-26 16:00', '2022-03-26 17:00',
                  '2022-03-26 18:00',  '2022-03-26 19:00','2022-03-26 20:00', '2022-03-26 21:00', '2022-03-26 22:00', '2022-03-26 23:00',
                  '2022-03-27 00:00', '2022-03-27 01:00', '2022-03-27 02:00', '2022-03-27 03:00', '2022-03-27 04:00','2022-03-27 05:00',
                  '2022-03-27 06:00', '2022-03-27 07:00', '2022-03-27 08:00', '2022-03-27 09:00', '2022-03-27 10:00', '2022-03-27 11:00',
                  '2022-03-27 12:00', '2022-03-27 13:00', '2022-03-27 14:00', '2022-03-27 15:00', '2022-03-27 16:00', '2022-03-27 17:00',
                  '2022-03-27 18:00',  '2022-03-27 19:00','2022-03-27 20:00', '2022-03-27 21:00', '2022-03-27 22:00', '2022-03-27 23:00',
                  '2022-03-28 00:00', '2022-03-28 01:00', '2022-03-28 02:00', '2022-03-28 03:00', '2022-03-28 04:00','2022-03-28 05:00',
                  '2022-03-28 06:00', '2022-03-28 07:00', '2022-03-28 08:00', '2022-03-28 09:00', '2022-03-28 10:00', '2022-03-28 11:00',
                  '2022-03-28 12:00', '2022-03-28 13:00', '2022-03-28 14:00', '2022-03-28 15:00', '2022-03-28 16:00', '2022-03-28 17:00',
                  '2022-03-28 18:00',  '2022-03-28 19:00','2022-03-28 20:00', '2022-03-28 21:00', '2022-03-28 22:00', '2022-03-28 23:00',
                  '2022-03-29 00:00', '2022-03-29 01:00', '2022-03-29 02:00', '2022-03-29 03:00', '2022-03-29 04:00','2022-03-29 05:00',
                  '2022-03-29 06:00', '2022-03-29 07:00', '2022-03-29 08:00', '2022-03-29 09:00', '2022-03-29 10:00', '2022-03-29 11:00',
                  '2022-03-29 12:00', '2022-03-29 13:00', '2022-03-29 14:00', '2022-03-29 15:00', '2022-03-29 16:00', '2022-03-29 17:00',
                  '2022-03-29 18:00',  '2022-03-29 19:00','2022-03-29 20:00', '2022-03-29 21:00', '2022-03-29 22:00', '2022-03-29 23:00',
                  '2022-03-30 00:00', '2022-03-30 01:00', '2022-03-30 02:00', '2022-03-30 03:00', '2022-03-30 04:00','2022-03-30 05:00',
                  '2022-03-30 06:00', '2022-03-30 07:00', '2022-03-30 08:00', '2022-03-30 09:00', '2022-03-30 10:00', '2022-03-30 11:00',
                  '2022-03-30 12:00', '2022-03-30 13:00', '2022-03-30 14:00', '2022-03-30 15:00', '2022-03-30 16:00', '2022-03-30 17:00',
                  '2022-03-30 18:00',  '2022-03-30 19:00','2022-03-30 20:00', '2022-03-30 21:00', '2022-03-30 22:00', '2022-03-30 23:00',
                  '2022-03-31 00:00', '2022-03-31 01:00', '2022-03-31 02:00', '2022-03-31 03:00', '2022-03-31 04:00','2022-03-31 05:00',
                  '2022-03-31 06:00', '2022-03-31 07:00', '2022-03-31 08:00', '2022-03-31 09:00', '2022-03-31 10:00', '2022-03-31 11:00',
                  '2022-03-31 12:00', '2022-03-31 13:00', '2022-03-31 14:00', '2022-03-31 15:00', '2022-03-31 16:00', '2022-03-31 17:00',
                  '2022-03-31 18:00',  '2022-03-31 19:00','2022-03-31 20:00', '2022-03-31 21:00', '2022-03-31 22:00', '2022-03-31 23:00',
                  '2022-04-01 00:00', '2022-04-01 01:00', '2022-04-01 02:00', '2022-04-01 03:00', '2022-04-01 04:00','2022-04-01 05:00',
                  '2022-04-01 06:00', '2022-04-01 07:00', '2022-04-01 08:00', '2022-04-01 09:00', '2022-04-01 10:00', '2022-04-01 11:00',
                  '2022-04-01 12:00', '2022-04-01 13:00', '2022-04-01 14:00', '2022-04-01 15:00', '2022-04-01 16:00', '2022-04-01 17:00',
                  '2022-04-01 18:00',  '2022-04-01 19:00','2022-04-01 20:00', '2022-04-01 21:00', '2022-04-01 22:00', '2022-04-01 23:00'];
      let data = [30, 28, 25, 26, 27, 30, 30, 26, 30, 31, 31, 28, 24, 30, 30, 30, 30, 30, 28, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 31, 31, 30, 31, 31, 25, 24, 31, 30, 30, 30, 30, 30, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 25, 25, 30, 31, 31, 25, 24, 30, 30, 30, 30, 30, 30, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 30, 31, 30, 31, 31, 25, 24, 30, 30, 30, 28, 25, 26, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 25, 30, 30, 31, 31, 29, 24, 30, 28, 30, 30, 30, 28, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 27, 28, 30, 31, 31, 27, 24, 31, 28, 30, 30, 25, 25, 30, 30, 25, 30, 25,
            30, 28, 25, 26, 27, 30, 30, 31, 30, 31, 31, 28, 24, 31, 28, 30, 30, 30, 30, 30, 30, 25, 30, 25];
      var option = {
         tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
          formatter : '{b} <br/> 温度 {c} ℃'
        },
        title: {
          left: 'center',
          text: '温度'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: _this.date,
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} ℃'
          },
          boundaryGap: [0, '100%']
        },
        dataZoom: [
          {
            startValue: this.startDate,
            endValue:  this.endDate
          },
          {
            type: 'inside',
            start: 0,
            end: 14
          },
          {
            start: 0,
            end: 10
          }
        ],
        series: [
          {
            type: 'line',
            data: _this.temperatureData
          }
        ]
      }; */
