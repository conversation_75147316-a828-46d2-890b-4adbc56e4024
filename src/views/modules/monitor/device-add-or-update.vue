<template>
    <Modal v-model="visible" width="500">
      <p slot="header" style="text-align:center">
          <span>{{!dataForm.deviceId ? $t('common.newlyBuild') : $t('common.update')}}</span>
      </p>
      <Alert v-if="deviceIdDisabled" type="warning" show-icon ><b class="tip">{{$t('monitor.monitorSaveTip')}}</b></Alert>
      <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 250px;" :label-width="120" label-position="left"
      @keyup.enter.native="dataFormSubmit()">
        <FormItem prop="deviceId" :label="$t('monitor.serialNumber')">
          <Input size="large" v-model="dataForm.deviceId" :disabled="deviceIdDisabled"
          :placeholder="$t('common.PleaseInput') + $t('monitor.serialNumber')"/>
        </FormItem>
        <FormItem prop="username" :label="$t('login.username')">
          <Input size="large" v-model="dataForm.username"
          :placeholder="$t('common.PleaseInput') + $t('login.username')"/>
        </FormItem>
        <FormItem prop="password" :label="$t('login.password')">
          <Input size="large" v-model="dataForm.password"
          :placeholder="$t('common.PleaseInput') + $t('login.password')"/>
        </FormItem>
        <FormItem prop="alias" :label="$t('operation.thealias')">
          <Input size="large" v-model="dataForm.alias"
                 :placeholder="$t('common.PleaseInput') + $t('operation.thealias')"/>
        </FormItem>
        <FormItem prop="type" :label="$t('monitor.cameraType')">
          <Select v-model="dataForm.type" size="large" filterable clearable transfer>
            <Option v-for="item in functionList" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
          </Select>
        </FormItem>
        <FormItem prop="owningTerminal" :label="$t('monitor.owning_terminal')">
          <Select size="large" v-model="dataForm.owningTerminal" filterable clearable transfer
          :placeholder="$t('common.PleaseSelect') + $t('monitor.owning_terminal')" >
            <Option v-for="item in terminalList" :value="item.deviceId" :key="item.deviceId" >{{ item.deviceId }} ({{ item.alias }})</Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer">
          <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
          <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {
        deviceId: '',
        username: '',
        password: '',
        alias:'',
        owningTerminal: '',
        /**
         * 功能类型：
         * 0：普通摄像头
         * 1：客流统计
         * 2：人群聚集
         * 3：违停抓拍
         */
        type:'0'
      },
      /**
       * 原来功能对应的类型
       */
      originType:'',
      originUsername: '',
      originPassword: '',
      terminalList: [],
      deviceIdDisabled: false,

      /**
       * 功能列表
       */
      functionList:[
        {
          value: '0',
          label: 'monitor.normal'
        },
        {
          value: '1',
          label: 'monitor.humanNumberStatistic'
        },
        {
          value: '2',
          label: 'monitor.insideHumanNumber'
        },
        {
          value: '3',
          label: 'monitor.traffic'
        }
      ]
    }
  },
  methods: {
    // 初始化
    init (deviceId) {
      this.dataForm.type=''
      this.deviceIdDisabled = false
      this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
      })
      this.$http({
        url: this.$http.adornUrl(`/monitor/device/getTerminal`),
        method: 'get',
        params: {deviceId: deviceId || ''}
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.terminalList = data.result
        }
      }).then(() => {
        this.visible = true
        this.dataForm.deviceId = deviceId || ''
        if (this.dataForm.deviceId) {
          this.deviceIdDisabled = true
          this.$http({
            url: this.$http.adornUrl(`/monitor/device/info/${this.dataForm.deviceId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm.deviceId = data.deviceNode.deviceId
              this.dataForm.username = data.deviceNode.username
              this.dataForm.password = data.deviceNode.password
              this.dataForm.alias=data.deviceNode.alias
              this.dataForm.owningTerminal = data.deviceNode.owningTerminal
              if (data.deviceNode.humanStatisticState==1){
                this.dataForm.type='1'
                this.originType='1'
              } else if (data.deviceNode.insideHumanState==1){
                this.dataForm.type='2'
                this.originType='2'
              } else if (data.deviceNode.trafficState==1){
                this.dataForm.type='3'
                this.originType='3'
              }else {
                this.dataForm.type='0'
                this.originType='0'
              }
              // this.dataForm.type=data.deviceNode.type
              // 另存原用户名密码如果改动，后台需要重新登录设备
              this.originUsername = data.deviceNode.username
              this.originPassword = data.deviceNode.password
            }
          })
          }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 如果修改的时候修改了用户名或密码，点击确定才发送请求
          if (this.deviceIdDisabled) {
            // 如果修改的时候修改了用户名或密码
            if (this.originUsername != this.dataForm.username || this.originPassword != this.dataForm.password||this.originType!=this.dataForm.type) {
              this.$Modal.confirm({
                title: this.$t('common.tips'),
                content: this.$t('monitor.monitorSaveTip1') + this.dataForm.username + ", "+ this.$t('login.password') + this.dataForm.password+","+this.$t('monitor.monitorSaveTip2')+this.$t(this.functionList[this.dataForm.type].label),
                okText: this.$t('common.confirm'),
                cancelText: this.$t('common.cancel'),
                onOk: () => {
                  this.updateOrSave({
                    'deviceId': this.dataForm.deviceId || undefined,
                    'username': this.dataForm.username,
                    'password': this.dataForm.password,
                    'alias':this.dataForm.alias,
                    'owningTerminal': this.dataForm.owningTerminal,
                    'type':this.dataForm.type,
                    'reset': true
                  })
                }
              })
            } else {
              this.updateOrSave({
                'deviceId': this.dataForm.deviceId || undefined,
                'username': this.dataForm.username,
                'password': this.dataForm.password,
                'alias':this.dataForm.alias,
                'owningTerminal': this.dataForm.owningTerminal,
                'type':this.dataForm.type
              })
            }

          } else {
            this.updateOrSave({
              'deviceId': this.dataForm.deviceId || undefined,
              'username': this.dataForm.username,
              'password': this.dataForm.password,
              'alias':this.dataForm.alias,
              'owningTerminal': this.dataForm.owningTerminal,
              'type':this.dataForm.type
            })
          }
        }
      })
    },
    updateOrSave(data) {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl(`/monitor/device/${!this.deviceIdDisabled ? 'save' : 'update'}`),
        method: 'post',
        data: this.$http.adornData(data)
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.msg.msg) {
            if (data.msg.msg !== 'success') {
                this.$Message.error(data.msg.msg)
            }
            setTimeout(() => {
                this.loading = false
            }, 500)
          } else {
            this.loading = false
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }
        } else {
          this.$Message.error(data.msg)
          setTimeout(() => {
              this.loading = false
          }, 500)
        }
      })
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          deviceId: [
            { required: true, message: this.$t('monitor.Device_name_cannot_be_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (value.length < 15) {
                callback(new Error(this.$t('monitor.Please_enter_the_device_serial_number')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
          username: [
            { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' }
          ],
          password: [
            { required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur' }
          ],
          alias: [
            { required: true, message: this.$t('validate.alias_cannot_empty'), trigger: 'blur' }
          ],
          type: [
            { required: true, message: this.$t('validate.alias_cannot_empty'), trigger: 'blur' }
          ]
        }
      }
    },
  }
}
</script>
