<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="this.$t('operation.thealias')"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(1)"  size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button v-if="isAuth('monitor:device:save')" style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
          <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
        </Button>
        <Button v-if="isAuth('monitor:device:delete')" style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
      <template slot-scope="{ row, index }" slot="online">
        <div v-if="row.isOn === 1 ">
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#on-line"></use>
            </svg>
         </div>
         <div v-else>
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#line"></use>
            </svg>
         </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
          <Button v-if="isAuth('monitor:device:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.deviceId)">{{$t('common.update')}}</Button>
          <Button v-if="isAuth('monitor:device:delete')" type="error" size="small" :disabled="row.isOn === 1" style="font-size: 11px" @click="deleteHandle(row.deviceId)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './device-add-or-update'
export default {
    data() {
        return {
            dataForm: {
                key: '',
            },
            dataListSelections: [],
            dataConlums:[
                {type: 'selection', width:100,  align: 'center', fixed: 'left',},
                {title: this.$t('monitor.serialNumber'), key: 'deviceId',  align: 'center',fixed: 'left', tooltip: true,width:180,
                    renderHeader:(h)=>{
                        return h('div',this.$t('monitor.serialNumber'))
                    }
                },
                {title: this.$t('cardDevice.online'), key: 'isOn', slot: 'online', align: 'center', width: 100,
                    renderHeader:(h)=>{
                        return h('div',this.$t('cardDevice.online'))
                    }
                },
                {title: this.$t('login.username'), key: 'username',  align: 'center', tooltip: true, width: 110,
                    renderHeader:(h)=>{
                        return h('div',this.$t('login.username'))
                    }
                },
                {title: this.$t('login.password'), key: 'password',  align: 'center', tooltip: true, width: 110,
                    renderHeader:(h)=>{
                        return h('div',this.$t('login.password'))
                    }
                },
              {title: this.$t('operation.thealias'), key: 'alias',  align: 'center', tooltip: true, width: 110,
                renderHeader:(h)=>{
                  return h('div',this.$t('operation.thealias'))
                }
              },
                {title: this.$t('monitor.device_ip'), key: 'deviceIp', align: 'center', width: 110,
                    renderHeader:(h)=>{
                        return h('div',this.$t('monitor.device_ip'))
                    }
                },
                {title: this.$t('monitor.port'), key: 'port', align: 'center',width: 100,
                    renderHeader:(h)=>{
                        return h('div',this.$t('monitor.port'))
                    }
                },
                {title: this.$t('monitor.owning_terminal'), key: 'owningTerminal', align: 'center', width: 150,
                    renderHeader:(h)=>{
                        return h('div',this.$t('monitor.owning_terminal'))
                    }
                },
                {title: this.$t('lamp.updateTime'), key: 'updateTime', align: 'center',tooltip: true, width: 180,
                    renderHeader:(h)=>{
                        return h('div',this.$t('lamp.updateTime'))
                    }
                },
                {title: this.$t('common.createTime'), key: 'createTime', align: 'center',tooltip: true, width: 180,
                    renderHeader:(h)=>{
                        return h('div',this.$t('common.createTime'))
                    }
                },
                {title: this.$t('common.operation'), slot: 'operation', align: 'center',fixed: 'right', width: 180,
                    renderHeader:(h)=>{
                        return h('div',this.$t('common.operation'))
                    }
                },
            ],
            dataList:[],
            dataListLoading: false,
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            addOrUpdateVisible: false
        }
    },
    components: {
        AddOrUpdate
    },
    methods: {
        initData () {
            this.dataForm = {
                key: ''
            }
            this.pageIndex = 1
            this.pageSize = 10
            this.totalPage = 0
            this.dataListSelections = []
        },
        // 获取数据列表
        getDataList (isQuery) {
            this.dataListLoading = true
          if (isQuery===1){
            this.pageIndex=1
          }
            this.$http({
                url: this.$http.adornUrl('/monitor/device/list'),
                method: 'get',
                params: this.$http.adornParams({
                    'page': this.pageIndex,
                    'limit': this.pageSize,
                    'key': this.dataForm.key
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                this.dataList = data.page.list
                this.totalPage = data.page.totalCount
                  //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
                  if (data.page.currPage>this.totalPage&&this.totalPage!==0){
                    this.pageIndex=1
                    this.getDataList(isQuery)
                  }
                } else {
                this.dataList = []
                this.totalPage = 0
                }
                this.dataListSelections = []
                this.dataListLoading = false
            })
        },
         // 多选
        selectionChangeHandle () {
            this.dataListSelections = this.$refs.selection.getSelection()
        },
        selectThisRow(data, index) {
            this.$refs.selection.toggleSelect(index);
        },
        // 每页数
        sizeChangeHandle (val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle (val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 新增 / 修改
        addOrUpdateHandle (id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        deleteHandle(id) {
            var deviceIds = id ? [id] : this.dataListSelections.map(item => {
                return item.deviceId
            })
            this.$Modal.confirm({
                title: this.$t('common.tips'),
                content: this.$t('common.delete_current_option'),
                okText: this.$t('common.confirm'),
                cancelText: this.$t('common.cancel'),
                onOk: () => {
                this.$http({
                    url: this.$http.adornUrl('/monitor/device/delete'),
                    method: 'post',
                    data: this.$http.adornData(deviceIds, false)
                }).then(({data}) => {
                    if (data && data.code === 0) {
                    this.$Message.success({
                        content: this.$t('common.operationSuccessful'),
                        duration: 0.5,
                        onClose: () => {
                        if (this.pageIndex != 1 && this.dataList.length === deviceIds.length) {
                            this.pageIndex--
                        }
                        this.getDataList()
                        this.dataListSelections = []
                        }
                    })
                    } else {
                    this.$Message.error(data.msg)
                    }
                })
                }
            })
        }
    },
    activated () {
        this.initData()
        this.getDataList()
    },
    computed: {
        tableHeight: {
            get () { return this.$store.state.common.tableHeight }
        }
    }

}
</script>

<style>

</style>
