<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('monitor.serialNumber') + '/'+$t('file.name')"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(1)" size="large">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
        <Button
          type="primary"
          size="large"
          style="margin-right: 5px; font-size: 11px"
          @click="isDownloadVisible()"
        >{{$t('monitor.downloadPlayback')}}
        </Button>
        <Button style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0"
                @click="deleteHandle()">
          <div style="margin:3px 8px">{{ $t('common.batchDel') }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataColumns" :data="dataList" @on-selection-change="selectionChangeHandle"
           @on-row-click="selectThisRow"
           :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
      <template slot-scope="{ row, index }" slot="operation">
        <Button type="primary" size="small" style="font-size: 11px" @click="downloadPlayBackToLocal(row.id)">
          {{ $t('file.download') }}
        </Button>
        <Button type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">
          {{ $t('common.delete') }}
        </Button>
      </template>
      <template slot-scope="{ row, index }" slot="fileSize">
        <span>{{ row.fileSize | filterType }}</span>
      </template>
    </Table>

    <Modal v-model="downloadVisible" @on-visible-change="onChange">
      <p slot="header" style="text-align:center">
        <Icon type="information-circled"></Icon>
        <span>{{$t('monitor.downloadPlayback')}}</span>
      </p>
      <Alert type="warning" show-icon>{{$t('monitor.tip')}}</Alert>
      <div>
        <p>{{$t('cardDevice.deviceName')}}</p>
        <Select v-model="deviceId" style="width:200px;margin-bottom: 10px">
          <Option v-for="item in deviceList" :value="item.deviceId" :key="item.deviceId" v-if="item.isOn===1">
            {{ item.deviceId }}
          </Option>
        </Select>
        <p>{{$t('file.name')}}</p>
        <Input v-model="fileName" style="width: 300px;margin-bottom: 10px"></Input>
        <p>{{$t('monitor.selectDateRange')}}</p>
        <Date-picker type="datetimerange" :options="playbackOption" v-model="date" transfer format="yyyy-MM-dd HH:mm:ss" :placeholder="$t('card.DateRange')"
                     style="width: 320px"></Date-picker>
      </div>
      <div slot="footer">
        <Button size="large" @click="downloadPlayBack()">{{$t('monitor.downloadPlayback')}}</Button>
      </div>
    </Modal>


    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
  </div>
</template>

<script>
export default {

  data() {
    return {

      dataForm: {
        key: '',
      },
      downloadVisible: false,
      dataListSelections: [],
      date: [],
      dataColumns: [
        {type: 'selection', width: 60, align: 'center', fixed: 'left',},
        {
          title: this.$t('monitor.serialNumber'), key: 'deviceId', align: 'center', fixed: 'left', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('monitor.serialNumber'))
          }
        },
        {
          title: this.$t('file.name'), key: 'fileName', align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('file.name'))
          }
        },
        {
          title: this.$t('file.size'), key: "fileSize", slot: "fileSize", align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('file.size'))
          }
        },
        {
          title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.createTime'))
          }
        },
        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center', fixed: 'right',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      deviceList: [],
      dataListLoading: false,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      addOrUpdateVisible: false,
      deviceId: "",
      src: "",
      startTime: "",
      endTime: "",
      fileName: Date.now(),
      //限制下载区间，只能选择近7天的数据
      playbackOption:{
        disabledDate (date) {
          return  Date.now() -1000*60*60*24*7 >date.valueOf() || date.valueOf()> Date.now();
        }
      }
    }
  },
  methods: {
    initData() {
      this.dataForm = {
        key: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
    },
    // 查询回放列表
    getDataList(isQuery) {
      this.dataListLoading = true
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/monitor/record/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },
    // 获取数据列表
    getDeviceList() {
      this.$http({
        url: this.$http.adornUrl('/monitor/device/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.deviceList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.deviceList = []
          this.totalPage = 0
        }
      })
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    //下载可视化
    isDownloadVisible() {
      this.downloadVisible = true
      this.getDeviceList()
    },
    //下载回放
    downloadPlayBack() {
      this.startTime = this.DateToStr(this.date[0])
      this.endTime = this.DateToStr(this.date[1])
      this.$http({
        url: this.$http.adornUrl('/monitor/device/downloadRecordFile'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': this.deviceId,
          'startTime': this.startTime,
          'endTime': this.endTime,
          'fileName': this.fileName
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.msg.msg === "success") {
            this.$Message.success(this.$t('home.successful'))
          } else {
            this.$Message.error(data.msg.msg)
          }
        } else {
          this.$Message.error(data.msg.msg)
        }
        this.date = []
        this.startTime = ''
        this.endTime = ''
        this.deviceId = ''
        // this.fileName=Date.now()

      })
      this.downloadVisible = false
    },
    // date格式转成yy-MM-dd HH:mm:ss
    DateToStr(dd) {
      var y = dd.getFullYear();
      var m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
      var d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
      var h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
      var n = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
      var s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
      return y + '-' + m + '-' + d + ' ' + h + ':' + n + ':' + s;
    },

    onChange() {
      this.date = []
      this.deviceId = ''
      this.fileName = Date.now()
    },
    //下载回放文件到本地
    downloadPlayBackToLocal(fileId) {
      window.open(this.$http.adornUrl(`/monitor/record/download?token=${this.$cookie.get('token')}&id=${fileId}`))
    },
    deleteHandle(id) {
      var deviceIds = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/monitor/record/delete'),
            method: 'post',
            data: this.$http.adornData(deviceIds, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex !== 1 && this.dataList.length === deviceIds.length) {
                    this.pageIndex--
                  }
                  this.getDataList()
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  activated() {
    this.initData()
    this.getDataList()
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight
      }
    }
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },

}
</script>

<style>

</style>
