<template>
  <div>
    <Row>
      <Col span="4" :style="{ 'height': tableHeight + 178 + 'px' }">
        <div style="height: 80px; width: 100%">
          <span class="leftSpan">{{ $t('monitor.Split_screen') }}</span>
          <Select v-model="selectProportion" transfer @on-change= "changeMultiVideo()">
            <Option v-for="item in proportionList" :value="item.value" :key="item.value" :disabled="item.disabled">{{ item.label }}
            </Option>
          </Select>
        </div>
        <div style="height: 260px; width:100%">
          <span class="leftSpan">{{ $t('monitor.PTZ_control') }}</span>
          <Row style="background-color: #fff; padding-bottom: 5px;">
            <Col span="8" v-for="item in controlList" :key="item.opener">
              <div style="text-align: center; margin-top: 5px">
                <Button v-if="item.opener" shape="circle" @mousedown.native="PTZMousedown(item.opener)" :disabled="!screenState"
                        @mouseup.native="PTZMouseup(item.opener)">
                  <svg width="20px" height="20px" aria-hidden="true" style="vertical-align:middle;">
                    <use :xlink:href="'#' + item.opener"></use>
                  </svg>
                </Button>
              </div>
            </Col>
            <Col span="8" v-for="(control, index) in controlOtherList" :key="index">
              <div style="text-align: center; margin-top: 5px">
                <span v-if="control.text">{{ $t(control.text) }}</span>
                <Button v-if="control.icon" :icon="control.icon" @mousedown.native="PTZMousedown(control.opener)" :disabled="!screenState || control.disabled"
                        @mouseup.native="PTZMouseup(control.opener)"></Button>
              </div>
            </Col>
          </Row>
        </div>
        <div :style="{ 'height': tableHeight - 162 + 'px', 'width': '100%' }">
          <div class="container">
            <div class="row">
              <div class="label">{{ $t('monitor.rtmpStreamState') }}：</div>
              <div class="button-group">
                <Button type="primary" size="small" :disabled="!screenState || deviceId.startsWith('xixun-')" @click="changeStreamState(1)">
                  {{ $t('sys.open') }}
                </Button>
                <Button type="error" size="small" :disabled="!screenState || deviceId.startsWith('xixun-')" @click="changeStreamState(0)">
                  {{ $t('sys.close') }}
                </Button>
              </div>
            </div>
            <div class="row">
              <div class="label">{{ $t('monitor.equipment') }}</div>
              <div class="button-group">
                <Button type="dashed" :disabled="!screenState || deviceId.startsWith('xixun-')" size="small">
                  <a download :href="src">{{ $t('monitor.screenshot') }}</a>
                </Button>
              </div>
            </div>
          </div>
          <Input search enter-button @on-search="searchByKey" :placeholder="this.$t('cardDevice.deviceName')"
                 style="margin-bottom: 5px;margin-top: 10px"/>
          <div :style="{ 'height': tableHeight - 268 + 'px', 'width': '100%', 'background-color': '#fff' }">
            <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight - 268">
              <div class="opera_ul" v-if="dataList && dataList.length > 0">
                <div class="opera_li" v-for="(item, index) in dataList" :key="index">
                  <div :class="select == index ? 'select' : ''"
                       style="padding-left: 6px;padding-right: 6px;display: flex;justify-content: space-between;">
                    <div style="flex: 1;" @click="changeSelect(item, index)">
                      <span class="box">
                        <Badge :status="item.isOn === 1 ? 'success' : 'error'" style="padding-left: 2px;"/>
                        {{ item.alias }}
                      </span>
                      <div v-if="dataList.length > 1" style="display: inline-block;">
                        <span class="box" style="width: 20px" v-if="index === 0">
                          <Icon type="md-arrow-down" @click="down(index)"/>
                        </span>
                        <span class="box" style="width: 20px" v-else-if="index === dataList.length - 1">
                          <Icon type="md-arrow-up" @click="up(index)"/>
                        </span>
                        <span class="box" style="width: 40px" v-else>
                          <Icon type="md-arrow-up" style="display: inline-block;" @click="up(index)"/>
                          <Icon type="md-arrow-down" style="display: inline-block;" @click="down(index)"/>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
                {{ $t('home.temporarilyNoData') }}
              </div>
            </Scroll>
          </div>
        </div>
      </Col>
      <Col span="20" :style="{ 'height': tableHeight + 178 + 'px' }">
        <div style="background-color: #fff; margin-left: 10px; width: 100%; height: 100%; padding: 5px;">
          <div v-if="selectProportion === 1" class="cell-player">
            <div :class="cellClass(1)">
              <div class="player">
                <video id="elementId" v-if="dataList[select] && dataList[select].isOn" autoplay muted="muted" width="100%" height="100%" >
                  {{$t('common.Loading')}}
                </video>
                <div v-else class="videoText">
                  {{ dataList[select] ? dataList[select].alias: '' }}
                  <div class="msgVideoText">{{ $t('monitor.offLineOrNotExist') }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="cell-player">
            <div v-if="dataList.length > 0" :class="cellClass(i)" v-for="i in selectProportion" :key="i">

              <div class="player">
                <video  :id="'videoElement' + (i-1)" v-if="dataList[i-1].isOn===1"  autoplay muted="muted" width="100%" height="100%">
                  {{$t('common.Loading')}}
                </video>
                <div v-else class="videoText">
                  {{ dataList[i-1].alias }}
                  <div class="msgVideoText">{{ $t('monitor.offLineOrNotExist') }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Col>
    </Row>
  </div>
</template>

<script>
// import cellPlayer from '@/utils/cellPlayer.vue'
import flvjs from "flv.js";
import {generateToken} from '@/utils/jwtUtils'
export default {
  // components: {cellPlayer},
  data() {
    return {
      select: 0,
      selectProportion: 1,
      proportionList: [
        {value: 1, label: "1 × 1", disabled: false},
        {value: 4, label: "2 × 2", disabled: false},
        {value: 6, label: "2 × 3", disabled: false},
        // {value: 9, label: "3 × 3"},
        // {value: 16, label: "4 × 4"},
      ],
      controlList: [{opener: "LEFT_TOP"}, {opener: "UP"}, {opener: "RIGHT_TOP"}, {opener: "LEFT"}, {}, {opener: "RIGHT"}, {opener: "LEFT_DOWN"}, {opener: "DOWN"}, {opener: "RIGHT_DOWN"}],
      controlOtherList: [
        {icon: "md-add", opener: "ZOOM_ADD"},
        {text: "monitor.zoom"},
        {icon: "md-remove", opener: "ZOOM_DEC"},
        {icon: "md-add", opener: "FOCUS_ADD", disabled: true},
        {text: "monitor.focus"},
        {icon: "md-remove", opener: "FOCUS_DEC", disabled: true},
        {icon: "md-add", opener: "APERTURE_ADD", disabled: true},
        {text: "monitor.aperture"},
        {icon: "md-remove", opener: "APERTURE_DEC", disabled: true},
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      key: '',
      deviceId: '',
      src: '',
      screenState: false,

      flvPlayer: null,
      lastDecodedFrame: 0,
      //video标签id
      elementId: "element",
      //flvPlayer数组，方便销毁
      flvPlayerArr:[],
      streamState:true,
      onlineDeviceList:[]
    }
  },
  activated() {
    this.getDataList()
    this.pageSize = 10
    this.key = ""
    this.selectProportion = 1
    this.proportionList = [
      {value: 1, label: "1 × 1", disabled: false},
      {value: 4, label: "2 × 2", disabled: false},
      {value: 6, label: "2 × 3", disabled: false},
    ]
    this.changeDeviceListStreamState(this.onlineDeviceList,1)

  },
  methods: {
    changeSelect(item, index) {
      this.select = index
      this.deviceId = item.deviceId
      this.snapPicture();
      this.changeMultiVideo()
      this.screenState = this.dataList[index].isOn === 1;
    },
    snapPicture() {
      this.src = this.$http.adornUrl(`/monitor/device/snapPicture/` + this.deviceId + `?token=${this.$cookie.get("token")}`);
    },
    PTZMousedown(opener) {
      if (this.dataList.length > 0) {
        this.$http({
          url: this.$http.adornUrl(`/monitor/device/RPC`),
          method: 'get',
          params: {deviceId: this.deviceId, direction: opener}
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.msg.msg) {
              if (data.msg.msg !== 'success') {
                this.$Message.error(data.msg.msg)
              }
            }
          } else {
            this.$Message.error(data.msg)
          }
        })
      }
    },
    PTZMouseup(opener) {
      this.$http({
        url: this.$http.adornUrl(`/monitor/device/RPCStop`),
        method: 'get',
        params: {deviceId: this.deviceId, direction: opener}
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.msg.msg !== 'success') {
            this.$Message.error(data.msg.msg)
          }
        } else {
          this.$Message.error(data.msg)
        }
      })
    },
    searchByKey(value) {
      this.key = value
      this.getDataList();
    },
    up(index) {
      if (this.dataList.length > 1) {
        var temp = this.dataList[index]
        this.dataList.splice(index, 1, this.dataList[index - 1])
        this.dataList.splice(index - 1, 1, temp)
        this.changeMultiVideo()
      }
    },
    down(index) {
      if (this.dataList.length > 1) {
        var temp = this.dataList[index]
        this.dataList.splice(index, 1, this.dataList[index + 1])
        this.dataList.splice(index + 1, 1, temp)
        this.changeMultiVideo()
      }
    },
    handleReachBottom() {
      return new Promise(resolve => {
        setTimeout(() => {
          this.pageSize = this.pageSize + 10;
          if (this.pageSize > this.totalCount) {
            this.$Message.warning(this.$t('common.noMoreData'));
            return;
          }
          this.getDataList();
          resolve();
        }, 200);
      });
    },
    getDataList() {
      this.$http({
        url: this.$http.adornUrl('/monitor/device/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.key
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataList.length > 0) {
            if(this.dataList){
              this.onlineDeviceList= this.dataList.filter(device=>device.isOn===1).map(device=>{
                return device.deviceId
              })
            }
            if (this.dataList.length < 6) {
              this.proportionList.splice(2, 1, {value: 6, label: "2 × 3", disabled: true})
            }
            if (this.dataList.length < 4) {
              this.proportionList = [
                {value: 1, label: "1 × 1", disabled: false},
                {value: 4, label: "2 × 2", disabled: true},
                {value: 6, label: "2 × 3", disabled: true},
              ]
            }
            if (this.dataList[0].deviceId) {
              if (this.dataList[0].isOn === 1) {
                this.$nextTick(()=>{
                  this.playVideo(this.dataList[0],"")
                });
                this.deviceId = this.dataList[0].deviceId
                this.screenState = true
                this.src = this.$http.adornUrl(`/monitor/device/snapPicture/` + this.deviceId + `?token=${this.$cookie.get("token")}`);
                this.select = 0
              }
            }
          }
        } else {
          this.dataList = []
          this.totalPage = 0
          this.$Message.error(data.msg)
        }
      })
    },
    playVideo(device, id) {
      let _this = this;
      var deviceId = device.deviceId
      // var prefix = "xixun-"
      // if (deviceId.startsWith(prefix)) {
      //   deviceId = deviceId.substring(prefix.length)
      // }
      _this.lastDecodedFrame = 0
      _this.elementId = deviceId
      let videoWin = ""
      if (_this.selectProportion === 1) {
        videoWin = document.getElementById("elementId");
      } else {
        _this.elementId = id
        videoWin = document.getElementById('videoElement' + id);
      }
      // 加载前先销毁
      if (flvjs.isSupported()) {
        let flvPlayer = flvjs.createPlayer({
          type: "flv",// 媒体类型
          isLive: true,//是否是实时流
          hasAudio: false,//是否有音频
          url: window.SITE_CONFIG.rtmp + deviceId +"&token="+ generateToken(deviceId),// 视频流地址
          stashInitialSize: 128 // 减少首帧显示等待时长
        }, {
          enableWorker: false,// 不启动分离线程
          enableStashBuffer: false,// 关闭IO隐藏缓冲区
          reuseRedirectedURL: true,// 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
          autoCleanupSourceBuffer: true, // 自动清除缓存
          fixAudioTimestampGap: false,// false 音频同步
        });
        // 断流重连
        flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
          console.log("errorType:", errorType);
          console.log("errorDetail:", errorDetail);
          console.log("errorInfo:", errorInfo);
          if (flvPlayer) {
            this.destroyVideo();
          }
        });
        // 画面卡死重连

        flvPlayer.on("statistics_info", function (res) {
          if (_this.lastDecodedFrame == 0) {
            _this.lastDecodedFrame = res.decodedFrames;
            return;
          }
          if (_this.lastDecodedFrame != res.decodedFrames) {
            _this.lastDecodedFrame = res.decodedFrames;
          } else {
            _this.lastDecodedFrame = 0;
            if (flvPlayer) {
              // _this.destroyVideo();
              // _this.playVideo(device,deviceId)
              _this.changeMultiVideo()
            }
          }
        });
        flvPlayer.attachMediaElement(videoWin);
        flvPlayer.load();
        _this.flvPlayerArr.push(flvPlayer)
      }
    },
    //销毁断流方法
    destroyVideo() {
      let _this=this;
      for(let i=0;i<_this.flvPlayerArr.length;i++){//卸载已经加载的视频资源
        if (_this.flvPlayerArr[i]!=null){
          _this.flvPlayerArr[i].pause();
          _this.flvPlayerArr[i].unload();
          _this.flvPlayerArr[i].detachMediaElement();
          _this.flvPlayerArr[i].destroy();
          _this.flvPlayerArr[i] = null;
        }
      }
    },

    //不同的播放策略
    changeMultiVideo(){
      this.destroyVideo()
      this.$nextTick(()=>{
        //单个监控则不需要额外的video和id
        if (this.selectProportion===1){
          if (this.dataList[this.select].isOn===1) {
            this.playVideo(this.dataList[this.select], "")
          }
        }else {
          for (let i = 0; i < this.selectProportion; i++) {
            if (this.dataList[i].isOn===1){
              this.playVideo(this.dataList[i],i)
            }
          }
        }
      });
    },
    changeStreamState(state){
      var content
      if (state===0){
        content=this.$t('monitor.streamCloseTip')
      }else {
        content=this.$t('monitor.streamOpenTip')
      }

      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: content,
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl(`/monitor/device/rtmpStreamState`),
            method: 'get',
            params: {deviceId: this.deviceId, state: state}
          }).then(({data}) => {
            if (data && data.code === 0) {
              if (data.msg.msg === 'success') {
                this.$Message.success(data.msg.msg)
              } else {
                this.$Message.error(data.msg.msg)
              }
            } else {
              this.$Message.error(data.msg)
            }
          });
        }
      })


    },
    // 修改在线推流状态
    changeDeviceListStreamState(onlineDeviceList,state) {
      if (this.onlineDeviceList.length > 0) {
        for (let i = 0; i < onlineDeviceList.length; i++) {
          this.$http({
            url: this.$http.adornUrl(`/monitor/device/rtmpStreamState`),
            method: 'get',
            params: {deviceId: onlineDeviceList[i], state: state}
          }).then(({data}) => {
            if (data && data.code === 0) {
              if (data.msg.msg === 'success') {
              } else {
                console.log(data.msg.msg)
              }
            } else {
              console.log(data.msg)
            }
          });
        }
      }
    },
    handleBeforeUnload(event) {
      this.changeDeviceListStreamState(this.onlineDeviceList, 0);
    }
  },


  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight
      },
    },
    cellClass() {
      return function (index) {
        switch (this.selectProportion) {
          case 1:
            return ['cell-player-1']
          case 4:
            return ['cell-player-4']
          case 6:
              // if (index == 1)
              //     return ['cell-player-6-1']
              // if (index == 2)
              //     return ['cell-player-6-2']
              // if (index == 3)
              //     return ['cell-player-6-none']
              return ['cell-player-6']
          case 9:
            return ['cell-player-9']
          case 16:
            return ['cell-player-16']
          default:
            break;
        }

      }
    },
  },
  //切换页面关闭video
  deactivated() {
    this.destroyVideo()
    this.changeDeviceListStreamState(this.onlineDeviceList,0)
  },
  mounted() {
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  flex: 1;
  min-width: 0;
  white-space: normal;
  word-wrap: break-word;
  margin-right: 10px;
}

.button-group {
  display: flex;
  gap: 5px;
  flex-shrink: 0;
}

.button-group :deep(button) {
  font-size: 11px;
}
.leftSpan {
  font-size: 20px;
  margin-left: 10px;
}

.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}

.opera_li {
  background-color: rgb(255, 255, 255);
  height: 33px;
  line-height: 33px;
  margin-bottom: 2px;
  cursor: pointer;
}


.box {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 180px;
}

.cell-tool {
  height: 40px;
  line-height: 30px;
  padding: 0 7px;
}

.cell-player {
  height: 100%;
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.cell-player-1 {
  width: 100%;
  box-sizing: border-box;
}
.cell-player-4 {
  width: 50%;
  height: 50% !important;
  box-sizing: border-box;
}

.cell-player-6 {
  width: 33.33%;
  height: 50% !important;
  box-sizing: border-box;
}

.cell-player-9 {
  width: 33.33%;
  height: 33.33% !important;
  box-sizing: border-box;
}

.cell-player-16 {
  width: 25%;
  height: 25% !important;
  box-sizing: border-box;
}

.cell {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.select {
  background-color: rgb(204, 204, 204);
}

.player {
  background-color: black;
  height: 100%;
  border: 1px solid white;
  color: white;
  text-align: center;
}
.videoText {
  color: red;
}
.msgVideoText {
  margin-top: 30px;
  color: #ff9900;
  font-size: 20px;
  font-weight: 500;
}
</style>
