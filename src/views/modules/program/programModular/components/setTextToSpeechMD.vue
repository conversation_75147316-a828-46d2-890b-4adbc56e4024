<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'textToSpeech'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      <br />
      W:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :min="0" :max="programData.canvasWidth"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :min="0" :max="programData.canvasHeight"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      <el-divider content-position="left">{{ $t('program.PlayProperties') }}</el-divider>
    </div>
    <!-- <span>入场特效:</span>
    <img src="" alt="">
    <el-select
      v-model="dropdownData.select.enter"
      placeholder="请选择入场特效"
      style="width:195px;margin-left:20px;"
      size="small"
      @change="enterEffects"
    >
      <el-option
        :label="item.name"
        :value="item.value"
        v-for="(item, index) in dropdownData.enter"
        :key="index"
      ></el-option>
    </el-select>
    <br />
    <span style="margin-top:4px;">入场时间:</span>
    <el-input
      size="small"
      style="width:195px;margin-left:20px;margin-top:4px;"
      v-model="dropdownData.select.enterDeta"
    ></el-input>
    <br />
    <span style="margin-top:4px;">出场特效:</span>
    <el-select
      v-model="dropdownData.select.Out"
      placeholder="请选择出场特效"
      style="width:195px;margin-left:20px;margin-top:4px;"
      size="small"
    >
      <el-option
        :label="item.name"
        :value="item.value"
        v-for="(item, index) in dropdownData.Out"
        :key="index"
      ></el-option>
    </el-select>
    <br />
    <span style="margin-top:4px;">出场时间:</span>
    <el-input
      size="small"
      style="width:195px;margin-left:20px;margin-top:4px;"
      v-model="dropdownData.select.OutDeta"
    ></el-input>
    <br />
    <span style="margin-top:4px;">默认行高:</span>
    <el-input
      size="small"
      style="width:195px;margin-left:20px;margin-top:4px;"
      v-model="dropdownData.select.lineH"
    ></el-input>
    <br /> -->
    <div style="margin-top:4px;">
      <el-radio-group v-model="proData.textRadio" @change="setTextformat">
        <el-radio label="1">{{ $t('program.static') }}</el-radio>
        <el-radio label="2">{{ $t('program.scroll') }}</el-radio>
        <el-radio label="3">{{ $t('program.turnPages') }}</el-radio>
      </el-radio-group>
    </div>
    <div v-show="proData.isShow">
      <span>{{ $t('program.PlayTime') }}:</span>
      <el-input-number style="width:195px;margin-left:20px;margin-top:4px;" v-model="proData.inputDuration"
        controls-position="right" @change="ChangeDuration" :min="0"></el-input-number>
      <br/>
      <span>{{ $t('task.speed') }}:</span>
      <!-- <el-select v-model="proData.voiceRate" :placeholder="$t('task.speed')"
        style="width:195px;margin-left:48px;margin-top:5px;">
        <el-option :label="item.text" :value="item.value" v-for="(item, index) in speedList" :key="index"></el-option>
      </el-select> -->
      <el-input-number v-model="proData.voiceRate"  controls-position="right" :precision="1" :step="0.1" :max="20" :min="0.1"
        style="width:195px;margin-left:48px;margin-top:5px;"
        :placeholder="$t('task.speed')">
      </el-input-number>
    </div>
    <div v-show="proData.textRadio === '1'">
      <span>{{ $t('program.broadcastSort') }}:</span>
      <el-checkbox style="margin-top:4px;margin-left:20px;" @change="changeVerticalRow" v-model="proData.isVerticalRow" size="large" border>
        {{proData.isVerticalRow ? $t('program.verticalRow'):$t('program.horizontal')}}
      </el-checkbox>
    </div>
    <div v-show="proData.runSpeedShow" style="margin:4px 0px;">
      <span style="margin-top:4px;">{{ $t('program.speed') }}:</span>
      <el-input-number controls-position="right" v-model="proData.textSpeed"
        style="margin-left:48px;width:85px;"></el-input-number>
      <span style="margin-left:5px;">{{ $t('program.ms/pixel') }}</span>
      <br/>
      <span style="margin-top:4px;">{{ $t('program.direction') }}</span>
      <el-select v-model="proData.effect" :placeholder="''" @change="effectChange" style="width:195px;margin-left:25px;margin-top:4px;">
        <el-option :label="$t('program.' + item.text)" :value="item.value" v-for="(item, index) in effectList" :key="index"></el-option>
      </el-select>
    </div>
    <div v-show="proData.runPageShow">
      {{ $t('program.turnPages') }}:{{ $t('program.total') }} {{ handler }}{{ $t('program.Page') }}
      <el-input-number controls-position="right" style="margin-left:10px;width:85px;margin-top:4px;" v-model="proData.ImagePage" :min="1" :max="handler"
         @change="programData.changeImagePage()"></el-input-number>
    </div>
    <el-button v-show="proData.preview" type="primary" icon="el-icon-s-promotion" @click="promotion">
      {{ $t('program.preview') }}</el-button>
    <div>
      <el-divider content-position="left">{{ $t('program.TextEditor') }}</el-divider>
      <!-- <div ref="wangEditor1" id="wangEditor1"></div> -->
      <!-- <div ref="wangEditor2" id="wangEditor2"></div> position: relative;z-index: 100000; -->
        <!-- <Toolbar style="border-bottom: 1px solid #ccc;" :editor="editor"
          :defaultConfig="toolbarConfig" :mode="mode" />
        <Editor style="overflow-y: hidden;height: calc(100% - 81px);" v-model="html" :defaultConfig="editorConfig" :mode="mode"
          @onCreated="onCreated" @onChange="onChange"/> -->
      <editor :htmlProps="html" :heightProps="heightProps" :isAlignmentMode="true"  :heightEditorProps="heightEditorProps" @clickAlignmentMode="clickAlignmentMode"
        @onChange="onChange" v-if="proData.type == 'textToSpeech'"  :key="refreshComKey"/>
    </div>
  </div>

</template>
<script>
// import E from "wangeditor"; //wangeditor编辑器
import program from "../program.js"; //被监听对象
// import { Message } from 'view-design'
// import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import editor from "@/utils/editor.vue";
export default {
  // components: { Editor, Toolbar },
  components: { editor },
  props: {
    proData: Object
  },
  data() {
    return {
      refreshComKey: 0, //刷新组件
      speedList: [
        {text: '1.0X', value: 1},
        {text: '1.5X', value: 1.5},
        {text: '2.0X', value: 2}
      ],
      effectList: [
        {text: 'ToTheLeft', value: 'right to left'},
        {text: 'upward', value: 'bottom to top'},
        {text: 'ToTheRight', value: 'left to right'},
        {text: 'down', value: 'top to bottom'},
      ],
      handler: 0,
      total: 4,
      name: "小明",
      programData: {},
      // assemblyData: {
      //   //小组间数据
      //   PlaybackProperties: {
      //     //开始时间结束时间
      //     Playback1: new Date(0, 0, 0, 0, 0),
      //     Playback2: new Date(0, 0, 0, 0, 0),
      //   },
      // },
      dropdownData: {
        // 下拉菜单数据
        enter: [
          //入场特效
          { name: "无", value: "None" },
          { name: "淡入", value: "ALPHA_IN" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "放大", value: "ZOOM_IN" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角放大", value: "ZOOM_IN_LEFT_TOP" },
          { name: "左下角放大", value: "ZOOM_IN_LEFT_BOTTOM" },
          { name: "右上角放大", value: "ZOOM_IN_RIGHT_TOP" },
          { name: "右下角放大", value: "ZOOM_IN_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" },
        ],
        Out: [
          //出场特效
          { name: "无", value: "None" },
          { name: "淡出", value: "ALPHA_OUT" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "缩小", value: "ZOOM_OUT" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角缩小", value: "ZOOM_OUT_LEFT_TOP" },
          { name: "左下角缩小", value: "ZOOM_OUT_LEFT_BOTTOM" },
          { name: "右上角缩小", value: "ZOOM_OUT_RIGHT_TOP" },
          { name: "右下角缩小", value: "ZOOM_OUT_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" },
        ],
        select: {
          enter: "无", //选中的特效
          Out: "无",
          enterDeta: "", //出入场时间
          OutDeta: "",
          lineH: "", //行高
          speed: "", //速度
        },
      },
      html: "",
      heightProps: 410,
      heightEditorProps: 490,
      /* editor: null,
      toolbarConfig: {
        // 显示哪些菜单，如何排序、分组
        toolbarKeys: [
          // 菜单 key
          // 'headerSelect', // 文本、H1、H2...
          'bold', // 加粗
          'italic',// 斜体
          'fontSize',// 字号
          'color',// 字体颜色
          'bgColor',// 背景色
          'fontFamily',// 字体
          'justifyLeft', // 左对齐
          'justifyRight', // 右对齐
          'justifyCenter', // 居中对齐
        ],
        // excludeKeys:[  隐藏哪些菜单  ]
      },
      editorConfig: {
        placeholder: "请输入内容...",
        // autoFocus: false,

        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
        },
      },
      mode: "simple" */
    };
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                if (newVal.alignmentMode == "vertical") {
                  program.newText()
                }
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    proData: {
      handler(newName, oldName) {
        if (newName) {
          if (newName.type == "textToSpeech") {
              this.html = newName.TextHtml ? newName.TextHtml : newName.TextHtml_m
              if (program.isSetData == false) {
                this.refreshComKey++;
              }
          }

          if (newName.pageTextList) {
            this.handler = newName.pageTextList.length
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true,
    },
  },
  mounted() {
    program.cleartextSpeed()
    // this.setcse();
  },
  beforeDestroy() {
    /* console.log("ads")
    const editor = this.editor
    const windowEditor = window.editor
    if (editor != null || windowEditor != null) {
      editor.destroy() // 组件销毁时，及时销毁编辑器
      windowEditor.destroy() // 组件销毁时，及时销毁编辑器
    } */
  },
  methods: {
    clickAlignmentMode(type, editor) {
      this.programData.ALLBoxData[this.programData.selectBox].alignmentMode = type
      program.newText(editor.getHtml())
    },
    // specialTime(e) {
    //   if (e > this.programData.ALLBoxData[this.programData.selectBox].inputDuration) {
    //     this.$Message.error("特效时长不能大于播放时长")
    //     this.programData.ALLBoxData[this.programData.selectBox].specialTime = this.programData.ALLBoxData[this.programData.selectBox].inputDuration
    //   } else {
    //     this.programData.ALLBoxData[this.programData.selectBox].specialTime = e
    //   }
    // },
    ChangeDuration(e) {// 不执行
      if (e < this.programData.ALLBoxData[this.programData.selectBox].specialTime) {
        this.$Message.error("特效时长不能大于播放时长")
        this.programData.ALLBoxData[this.programData.selectBox].inputDuration = this.programData.ALLBoxData[this.programData.selectBox].specialTime
      } else {
        this.programData.ALLBoxData[this.programData.selectBox].inputDuration = e
      }
    },
    // 修改文本排序
    changeVerticalRow(e){
      // program.changeVerticalRow()
    },
    ChangeVoiceRate(e) {
      this.programData.ALLBoxData[this.programData.selectBox].voiceRate = e
    },
    // 预览
    promotion() {
      program.setTextSpeed();
    },
    setTextformat(e) {
      program.textFormat(e, this.programData.selectBox);
    },
    effectChange(val) {
      program.setEffectChange(val, this.programData.selectBox);
    },
    enterEffects(e) {
      if (e === "MOVING_LEFT") {
        console.log("连续左移", e);
      }
    },
    /* onCreated(editor) {
      console.log("sss")
      this.editor = Object.seal(editor)
      window.editor = Object.seal(editor) // 【注意】一定要用 Object.seal() 否则会报错
    }, */
    onChange(editor) {
      program.newText(editor.getHtml())
      // setTimeout(() => program.newText(editor.getHtml()));
    },
    /* setcse() {
      // program.cleartextSpeed()
      //文本编辑器的初始化
      window.editor = new E(this.$refs.wangEditor1);
      editor.config.menus = [
        "bold",
        "fontSize",
        "fontName",
        "italic",
        "underline",
        "justify",
        'foreColor',
        'backColor',
      ];
      editor.config.colors = [
        "#000000", "#ffffff", "#eeece0", "#1c487f", "#4d80bf", "#c24f4a", "#8baa4a", "#7b5ba1", "#46acc8",
        "#f9963b",
        "#FF0000",
        "#00FF00",
        "#0000FF",
        "#FF00FF",
        "#00FFFF",
        "#FFFF00",
        "#000000",
        "#70DB93",
        "#5C3317",
        "#9F5F9F",
        "#B5A642",
        "#D9D919",
        "#A67D3D",
        "#8C7853",
        "#A67D3D",
        "#5F9F9F",
        "#D98719",
        "#B87333"
      ]
      editor.config.showFullScreen = false;

      window.editor.create();

      window.editor.config.onchange = function (newHtml) {
        // console.log(newHtml)
        //wangEditor4打包后，将光标移到句首，回车后删除会增加文字颜色，且样式特殊 style="color: var(--w-e-textarea-color);"
        //所以当出现了这种样式就直接将样式删除
        if (-1!==newHtml.search(/<span.*?color: var.*?>/g)){
          newHtml=newHtml.replace(/color: var.*?;|background-color: var.*?;/g,"")
          // newHtml=newHtml.replace(/<span\s+[^>]*>|<\/span>/g,"")
        //   newHtml=newHtml.replace(/color: var.*;|background-color: var.*;/g,"")
        }
        // var str="<p><span style=\"color: var(--w-e-textarea-color);\">大大大大 打</span><br/></p>"
        // if (-1!==str.search(/<span.*?color: var.*?>/g)){
        //   console.log("进入测试")
        //   console.log(str.replace(/<span\s+[^>]*>|<\/span>/g,""))
        // }

        // console.log(newHtml)
        program.newText(newHtml)
      }
    }, */

  },
};
</script>
<style scoped>
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}

/* #wangEditor2 {
  width: 100%;
  height: 118px;
  padding-bottom: 5px;
} */
</style>
