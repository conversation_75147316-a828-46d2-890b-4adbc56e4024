<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'sensorsShareData'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      <br />
      W:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :min="0" :max="programData.canvasWidth"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :min="0" :max="programData.canvasHeight"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
    </div>
    <el-divider content-position="left">{{ $t('program.SelectedMaterialInformation') }}</el-divider>
    <div class="block">
      <span>{{ $t('program.deviceAddress') }}:</span>
      <Tooltip placement="top" max-width="275">
        <Icon type="md-alert" size="19" color="#f00"/>
        <template #content>
          <p>{{ $t('program.deviceAddrTip') }}</p>
        </template>
      </Tooltip>
      <el-input size="small" style="width:185px;margin-left:5px;" v-model="proData.url"></el-input>
    </div>
    <div class="block">
      <span>{{ $t('program.deviceKey') }}:</span>
      <Tooltip placement="top" max-width="275">
        <Icon type="md-alert" size="19" color="#f00"/>
        <template #content>
          <p>{{ $t('program.deviceKeyTip') }}</p>
        </template>
      </Tooltip>
      <el-input size="small" style="width:185px;margin-left:5px;" v-model="proData.token"></el-input>
    </div>
    <!-- <div class="block">
      <span>{{ $t('program.fontSize') }}:</span>
      <el-input-number controls-position="right" size="small" style="width:185px;margin-left:1px;"
        v-model="proData.fontSize" :min="12" @change="programData.monitorWeatherSize()"></el-input-number>
    </div> -->
    <div class="block">
      <span>{{ $t('program.PlayTime') }}:</span>
      <el-input-number size="small" style="width:185px;margin-left:5px;" :min="0" v-model="proData.inputDuration"
        controls-position="right"></el-input-number>
    </div>
    <div>
      <span>{{ $t('program.CustomHTML') }}:</span>
      <Tooltip placement="top">
        <Icon type="md-alert" size="19" color="#f00"/>
        <template #content>
          <div style="word-break: break-word; white-space: normal;">
            <p>%{sensorBrightness} {{ $t('meteorological.Illuminance') }}</p>
            <p>%{temperature} {{ $t('card.temperature') }}</p>
            <p>%{humidity} {{ $t('card.humidity') }}</p>
            <p>%{noise} {{ $t('program.noise') }}</p>
            <p>%{pm2_5} PM2.5</p>
            <p>%{pm10} PM10</p>
            <p>%{windSpeed} {{ $t('program.windSpeed') }}</p>
            <p>%{windDirection} {{ $t('program.windDirection') }}</p>
            <p>%{so2} SO2</p>
            <p>%{no2} NO2</p>
            <p>%{co} CO</p>
            <p>%{o3} O3</p>
            <p>%{pressure} {{ $t('program.atmosphericPressure') }}</p>
            <p>%{rainfall} {{ $t('program.rainfall') }}</p>
            <p>%{radiation} {{ $t('program.radiation') }}</p>
            <p>%{beam} {{ $t('program.lightIntensity') }}</p>
          </div>
        </template>
      </Tooltip>
      <editor :htmlProps="html" :heightProps="heightProps" :heightEditorProps="heightEditorProps" 
        @clickAlignmentMode="clickAlignmentMode" @onChange="onChange" :key="refreshComKey"/>
    </div>
  </div>
</template>

<script>
import program from "../program.js"; //被监听对象
import editor from "@/utils/editor.vue";
export default {
  components: { editor },
  props: {
    proData: Object
  },
  data() {
    return {
      refreshComKey: 0,
      value: [],
      programData: {},
      assemblyData: {
        //小组间数据
        PlaybackProperties: {
          //开始时间结束时间
          Playback1: new Date(0, 0, 0, 0, 0),
          Playback2: new Date(0, 0, 0, 0, 0),
        },
      },
      dropdownData: {
        // 下拉菜单数据
        enter: [
          //入场特效
          { name: "无", value: "None" },
          { name: "淡入", value: "ALPHA_IN" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "放大", value: "ZOOM_IN" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角放大", value: "ZOOM_IN_LEFT_TOP" },
          { name: "左下角放大", value: "ZOOM_IN_LEFT_BOTTOM" },
          { name: "右上角放大", value: "ZOOM_IN_RIGHT_TOP" },
          { name: "右下角放大", value: "ZOOM_IN_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" },
        ],
        Out: [
          //出场特效
          { name: "无", value: "None" },
          { name: "淡出", value: "ALPHA_OUT" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "缩小", value: "ZOOM_OUT" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角缩小", value: "ZOOM_OUT_LEFT_TOP" },
          { name: "左下角缩小", value: "ZOOM_OUT_LEFT_BOTTOM" },
          { name: "右上角缩小", value: "ZOOM_OUT_RIGHT_TOP" },
          { name: "右下角缩小", value: "ZOOM_OUT_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" },
        ],
        select: {
          enter: "无", //选中的特效
          Out: "无",
          enterDeta: "", //出入场时间
          OutDeta: "",
          lineH: "", //行高
          speed: "", //速度
        },
      },
      html: "",
      heightProps: 440,
      heightEditorProps: 520,
    };
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch: {
    proData: {
      handler(newName, oldName) {
        if (newName) {
          if (newName.type == "sensorsShareData") {
              this.html = newName.html
              if (program.isSetData == false) {
              this.refreshComKey++;
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true,
    },
  },
  mounted() {
    // this.handleChange(this.defaultWeather)
  },
  methods: {
    clickAlignmentMode(type, editor) {
      this.programData.ALLBoxData[this.programData.selectBox].alignmentMode = type
      program.newText(editor.getHtml())
    },
    onChange(editor) {
      // console.log(editor.getHtml())
      // this.proData.html = editor.getHtml()
      program.sensorsShareDataNewText(editor.getHtml())
    },
  },
};
</script>
<style scoped>
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}


.block {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 2px 0;
  display: block;
}

#wangEditor2 {
  width: 100%;
  height: 118px;
  padding-bottom: 5px;
}
</style>
