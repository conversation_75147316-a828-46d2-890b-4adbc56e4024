<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'DigitalClock'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      <br>
      W: <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :max="programData.canvasWidth" :min="0"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H: <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :max="programData.canvasHeight" :min="0"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      <el-divider content-position="left">{{ $t('program.clockProperties') }}</el-divider>
    </div>
    <!-- <span>{{ $t('setTime.theTimeZone') }}</span>s
    <el-input-number size="small" v-model="proData.SQnum" :min="-12" :max="12"
      :label="$t('program.PleaseSelectATimeZone')" style="margin-left:20px;"></el-input-number>
    <br/> -->
    <el-input-number v-show="false" size="small" v-model="proData.SQnum" :min="-12" :max="12"
      :label="$t('program.PleaseSelectATimeZone')" style="margin-left:20px;"></el-input-number>
    <span>{{ $t('setTime.theTimeZone') }}</span>
    <Select style="margin-left:25px;font-size:16px;width:200px" @on-change="setSQnum" label-in-value filterable clearable  v-model="proData.timeZone">
        <Option v-for="item in timezoneS" :value="item.value" :key="item.value">{{ item.name }}</Option>
    </Select>
    <el-checkbox-group @change="checkSetDom" v-model="proData.numberTime"
      style="margin-left:55px;margin-top:10px;font-size:16px;">
      <el-checkbox :label="$t('program.year')" size="medium"></el-checkbox>
      <el-checkbox :label="$t('program.month')" size="medium"></el-checkbox>
      <el-checkbox :label="$t('program.day')" size="medium"></el-checkbox>
      <br>
      <el-checkbox :label="$t('program.hour')" size="medium"></el-checkbox>
      <el-checkbox :label="$t('program.Minute')" size="medium"></el-checkbox>
      <el-checkbox :label="$t('program.Second')" size="medium"></el-checkbox>
      <br>
      <el-checkbox :label="$t('program.Week')" size="medium"></el-checkbox>
      <el-checkbox :label="$t('program.fourYears')" size="medium"></el-checkbox>
      <br>
      <el-checkbox :label="$t('program.12HourClock')" size="medium"></el-checkbox>
      <el-checkbox :label="$t('program.morningAfternoon')" size="medium"></el-checkbox>
    </el-checkbox-group>
    <el-divider content-position="left">{{ $t('program.style') }}</el-divider>
    <span>{{ $t('program.dateStyle') }}</span>
    <el-select v-model="proData.Rqfg" :placeholder="$t('program.dateStyle')" style="width:195px;margin-left:20px;"
      size="small">
      <el-option :label="item" :value="item" v-for="(item, index) in proData.timeData_rq" :key="index"></el-option>
    </el-select>
    <br>
    <span style="margin-top:5px;">{{ $t('program.timeStyle') }}</span>
    <el-select v-model="proData.Sjfg" :placeholder="$t('program.timeStyle')"
      style="width:195px;margin-left:20px;margin-top:5px;" size="small">
      <el-option :label="item" :value="item" v-for="(item, index) in proData.timeData_sj" :key="index"></el-option>
    </el-select>
    <br>
    <div style="height:10px;width:100%;"></div>
    <span>{{ $t('program.displayStyle') }}</span>
    <el-radio-group style="margin-left:10px;margin-top:-5px;" v-model="proData.isSingleLine" @change="setSingleLine">
      <el-radio label="1" style="margin-left:10px;margin-top:5px;">{{ $t('program.singleLine') }}</el-radio>
      <el-radio label="2" style="margin-top:5px;">{{ $t('program.Multi-line') }}</el-radio>
    </el-radio-group>
    <el-divider content-position="left">{{ $t('program.fontSettings') }}</el-divider>
    <span>{{ $t('program.fontSize') }}</span>
    <el-input-number controls-position="right" :min="12" size="small" v-model="proData.fontSize" class="numberMini"
      @change="programData.setSizeDX()"></el-input-number>
    <div style="position: relative;top:10px;">
      <span>{{ $t('program.fontColor') }}</span>
      <el-color-picker v-model="proData.textColor" size="mini" style="position:absolute;top:-5px;"
        @change="textColor"></el-color-picker>
      <div class="textType textTypeB" @click="setStyle('b')" :class="proData.textStyle.b ? 'selectType' : ''">B</div>
      <div class="textType textTypeI" @click="setStyle('i')" :class="proData.textStyle.i ? 'selectType' : ''">I</div>
      <div class="textType textTypeU" @click="setStyle('u')" :class="proData.textStyle.u ? 'selectType' : ''">U</div>
    </div>
    <el-divider content-position="left">{{ $t('program.PlayProperties') }}</el-divider>
    <span>{{ $t('program.PlayTime') }}/s</span>
    <el-input-number :min="0" controls-position="right" size="small" v-model="proData.PlaybackDuration"
      class="numberMini" style="width:100px;"></el-input-number>
  </div>
</template>

<script>
import program from "../program.js" //被监听对象
import timeZoneData from '@/assets/json/timeZone.json'
export default {
  props: {
    proData: Object
  },
  data() {
    return {
      timezoneS: timeZoneData,
      radio: "1",
      color1: '#FF0000',
      timeData_rq: [],
      timeData_sj: [
        "HH:MM:SS",
        "H:MM:SS",
      ],
      timeData_xs: [
        { name: "无", value: "None" },
        { name: "淡入", value: "ALPHA_IN" },
        { name: "连续左移", value: "MOVING_LEFT" },
        { name: "连续右移", value: "MOVING_RIGHT" },
        { name: "连续上移", value: "MOVING_TOP" },
        { name: "连续下移", value: "MOVING_BOTTOM" },
        { name: "放大", value: "ZOOM_IN" },
      ],
      programData: {},
      selectTime: "MOVING_LEFT",
    }
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top 
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: { //监听的值
      handler(newName, oldName) {
        this.programData = program
      },
      immediate: true,
    }
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  methods: {
    setSQnum(timeZone) {
      if (timeZone && timeZone.label) {
        let labelSplit = timeZone.label.split(" ")
        if (labelSplit.length > 1) {
          let timeSplit = labelSplit[1].split(":")
          if (timeSplit.length > 1) {
            this.proData.SQnum = timeSplit[0]
          }
        }
      }
    },
    checkSetDom() {
      program.setNumberTime()
    },
    textColor() {
      program.setDigitalClockColor()
    },
    setStyle(x) {
      this.proData.textStyle[x] = !this.proData.textStyle[x]
      program.setTextStyle()
    },
    setSingleLine() {
      program.setSingleLine()
    },
  }
}
</script>
<style>

.el-color-picker--mini {
  width: 24px;
  height: 24px;
  margin-top: 2px;
}
</style>
<style scoped>
.ivu-select .ivu-select-dropdown {
    width: 150px;
}
.textType {
  width: 28px;
  height: 28px;
  border: solid 1px #e6e6e6;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  margin-top: -2px;
  border-radius: 4px;
  user-select: none;
}

.textTypeB {
  margin-left: 36px;
}

.textTypeI,
.textTypeU {
  margin-left: 5px;
}

.textTypeU {
  text-decoration: underline;
}

.selectType {
  background: #9dc9f3;
}

.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}
</style>
