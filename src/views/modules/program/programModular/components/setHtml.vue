<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'WebURL'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      <br />
      W:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :min="0" :max="programData.canvasWidth"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :min="0" :max="programData.canvasHeight"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
    </div>
    <el-divider content-position="left">{{ $t('program.SelectedMaterialInformation') }}</el-divider>

    <el-form :model="proData" label-width="80px" size="small" class="narrow-form space-y-4">
      <el-form-item :label="$t('program.weburl')" class="mb-0">
        <el-input v-model="proData.url" class="w-full" placeholder="https://example.com"></el-input>
      </el-form-item>
      <el-form-item :label="$t('program.PlayTime')" class="mb-0">
        <el-input-number
          :min="0"
          v-model="proData.inputDuration"
          controls-position="right"
          class="w-full"
          placeholder="Seconds"
        ></el-input-number>
      </el-form-item>
      <el-form-item :label="$t('program.refreshSec')" class="mb-0">
        <div class="flex items-center">
          <el-input-number
            :min="0"
            v-model="proData.refreshSec"
            controls-position="right"
            class="flex-1"
          ></el-input-number>
          <span class="text-gray-600 ml-2 flex-shrink-0">s</span>
        </div>
      </el-form-item>
      <el-form-item :label="$t('program.zoom')" class="mb-0">
        <el-input-number
          :min="1"
          v-model="proData.zoom"
          controls-position="right"
          class="w-full"
        ></el-input-number>
      </el-form-item>
      <div class="flex gap-4">
        <el-form-item :label="$t('program.offset')+' X'" class="mb-0 flex-1">
          <el-input-number
            :min="0"
            v-model="proData.offX"
            controls-position="right"
            class="w-full"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('program.offset')+' Y'" class="mb-0 flex-1">
          <el-input-number
            :min="0"
            v-model="proData.offY"
            controls-position="right"
            class="w-full"
          ></el-input-number>
        </el-form-item>
      </div>
      <div class="flex gap-4">
        <el-form-item :label="$t('program.scale')+' X'" class="mb-0 flex-1">
          <div class="flex items-center">
            <el-input-number
              :min="0"
              v-model="proData.scaleX"
              controls-position="right"
              class="flex-1"
            ></el-input-number>
            <span class="text-gray-600 ml-2 flex-shrink-0">%</span>
          </div>
        </el-form-item>
        <el-form-item :label="$t('program.scale')+' Y'" class="mb-0 flex-1">
          <div class="flex items-center">
            <el-input-number
              :min="0"
              v-model="proData.scaleY"
              controls-position="right"
              class="flex-1"
            ></el-input-number>
            <span class="text-gray-600 ml-2 flex-shrink-0">%</span>
          </div>
        </el-form-item>
      </div>
    </el-form>

  </div>
</template>

<script>
import program from "../program.js"; //被监听对象
export default {
  props: {
    proData: Object
  },
  data() {
    return {
      programData: {}
    };
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true,
    }
  },
  updated() {
  },
  methods: {
  },
};
</script>
<style scoped>
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}
.block {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 2px 0;
  display: block;
}
.narrow-form {
  max-width: 330px;
  margin: 0 auto;
  padding: 10px;
  box-sizing: border-box;
}

.narrow-form .el-form-item .el-input {
  width: 100%;
}

.el-input-number--small {
  line-height: 32px;
}

.el-form-item__label {
  @apply text-gray-600 font-medium;
}

.el-divider--horizontal {
  @apply my-6;
}
</style>
