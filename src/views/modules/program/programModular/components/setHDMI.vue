<template>
  <div class="elementType">
    <div v-if="programData.assemblyProp == 'hdmi'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.hdmiObj.left" :min="0"
        :max="programData.canvasWidth - programData.hdmiObj.width" class="numberMini"
        @change="programData.modifyHDMIXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.hdmiObj.top" :min="0"
        :max="programData.canvasHeight - programData.hdmiObj.height" class="numberMini"
        @change="programData.modifyHDMIXOrY()"></el-input-number>
      <br />
      W:
      <el-input-number controls-position="right" size="small"
        v-model="programData.hdmiObj.width" :min="0" :max="programData.canvasWidth"
        class="numberMini" @change="programData.modifyHDMIWidthOrHeight()"></el-input-number>
      H:
      <el-input-number controls-position="right" size="small"
        v-model="programData.hdmiObj.height" :min="0" :max="programData.canvasHeight"
        class="numberMini" @change="programData.modifyHDMIWidthOrHeight()"></el-input-number>

      <el-divider content-position="left">{{ $t('program.HDMITypeDescription') }}</el-divider>
      <div style="color: #515a6e;">
        <span>{{ $t('program.HDMIDescription1') }}</span>
        <br/>
        <span>{{ $t('program.HDMIDescription2') }}</span>
      </div>
    </div>

    
  </div>
</template>

<script>
import program from "../program.js"; //被监听对象
export default {
  props: {
    proData: Object
  },
  data() {
    return {
      programData: {}
    };
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && newVal.hdmiObj && oldVal && oldVal.hdmiObj) {
          if (newVal.hdmiObj.left != oldVal.hdmiObj.left || newVal.hdmiObj.top != oldVal.hdmiObj.top 
              || newVal.hdmiObj.width != oldVal.hdmiObj.width || newVal.hdmiObj.height != oldVal.hdmiObj.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true,
    }
  },
  updated() {
  },
  methods: {
  },
};
</script>
<style scoped>
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}
.block {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 2px 0;
  display: block;
}

</style>
