<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'Weather'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
        <!-- {{programData.ALLBoxData[programData.selectBox].top}} -->
      <br />
      W:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :min="0" :max="programData.canvasWidth"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :min="0" :max="programData.canvasHeight"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
    </div>
    <el-divider content-position="left">{{ $t('program.SelectedMaterialInformation') }}</el-divider>
    <div class="block">
      <Row>
        <Col :span="textSpan">
          <span>{{$t('program.timeType')}}:</span>
          <Tooltip placement="top" :transfer="true">
            <Icon type="md-alert" size="19" color="#f00"/>
            <template #content>
              <div>{{$t('program.timeTypeTip')}}</div>
            </template>
          </Tooltip>
        </Col>
        <Col :span="inputSpan">
          <el-select v-model="proData.timeType" :placeholder="$t('program.timeType')"
            style="width:195px;" size="small" @change="changeTimeType">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in timeTypeList" :key="index"></el-option>
          </el-select>
        </Col>
      </Row>
    </div>
    <div class="block">
      <Row>
        <Col :span="textSpan"><span>{{ $t('program.province') }}:</span></Col>
        <Col :span="inputSpan">
          <Cascader :data="options" v-model="proData.valueWeather" style="width:195px;" @on-change="handleChange" transfer
            ref="areaCascader" size="large" @on-visible-change="visibleChange"/>
          <!-- <el-cascader style="width:195px;" v-model="proData.valueWeather" :value="proData.valueWeather"
            @change="handleChange" :options="options" ref="areaCascader" size="small" @visible-change="visibleChange">
          </el-cascader> -->
        </Col>
      </Row>
    </div>
    <!-- <div class="block">
      <Row>
        <Col :span="textSpan"><span>{{ $t('program.fontSize') }}:</span></Col>
        <Col :span="inputSpan">
          <el-input-number controls-position="right" size="small" style="width:195px;margin-left:1px;"
            v-model="proData.fontSize" :min="12" @change="programData.monitorWeatherSize()">
          </el-input-number>
        </Col>
      </Row>
    </div> -->
    <div class="block">
      <Row>
        <Col :span="textSpan"><span>{{ $t('program.PlayTime') }}:</span></Col>
        <Col :span="inputSpan">
          <el-input-number size="small" style="width:195px;" :min="0" v-model="proData.inputDuration"
            controls-position="right"></el-input-number>
        </Col>
      </Row>
    </div>
    <div class="block">
      <span>{{ $t('program.CustomHTML') }}:</span>
      <Tooltip placement="top">
        <Icon type="md-alert" size="19" color="#f00"/>
        <template #content>
          <div style="word-break: break-word; white-space: normal;">
            <p>%{current}: {{ $t('program.weatherTip1') }}</p>
            <p>%{aqi}: {{ $t('program.weatherTip2') }}</p>
            <p>%{arr.0.date}: {{ $t('program.weatherTip3') }}</p>
            <p>%{arr.0.type}: {{ $t('program.weatherTip4') }}</p>
            <p>%{arr.0.high}: {{ $t('program.weatherTip5') }}</p>
            <p>%{arr.0.low}: {{ $t('program.weatherTip6') }}</p>
            <p>%{arr.0.fx}: {{ $t('program.weatherTip7') }}</p>
            <p>%{arr.0.fl}: {{ $t('program.weatherTip8') }}</p>
            <p>%{arr.0.img-32-32}: {{ $t('program.weatherTip9') }}</p>
            <p>{{ $t('program.weatherTip10') }}</p>
          </div>
        </template>
      </Tooltip>
      <editor :htmlProps="html" :heightProps="heightProps" :heightEditorProps="heightEditorProps" @onChange="onChange" :key="refreshComKey"/>
    </div>


  </div>
</template>

<script>
import program from "../program.js"; //被监听对象
import options from "@/assets/region.json";
import editor from "@/utils/editor.vue";
export default {
  components: { editor },
  props: {
    proData: Object
  },
  data() {
    return {
      refreshComKey: 0,
      defaultWeather: ['北京市', '北京市', 2],
      value: [],
      options: options,
      programData: {},
      assemblyData: {
        //小组间数据
        PlaybackProperties: {
          //开始时间结束时间
          Playback1: new Date(0, 0, 0, 0, 0),
          Playback2: new Date(0, 0, 0, 0, 0),
        },
      },
      dropdownData: {
        // 下拉菜单数据
        enter: [
          //入场特效
          { name: "无", value: "None" },
          { name: "淡入", value: "ALPHA_IN" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "放大", value: "ZOOM_IN" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角放大", value: "ZOOM_IN_LEFT_TOP" },
          { name: "左下角放大", value: "ZOOM_IN_LEFT_BOTTOM" },
          { name: "右上角放大", value: "ZOOM_IN_RIGHT_TOP" },
          { name: "右下角放大", value: "ZOOM_IN_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" },
        ],
        Out: [
          //出场特效
          { name: "无", value: "None" },
          { name: "淡出", value: "ALPHA_OUT" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "缩小", value: "ZOOM_OUT" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角缩小", value: "ZOOM_OUT_LEFT_TOP" },
          { name: "左下角缩小", value: "ZOOM_OUT_LEFT_BOTTOM" },
          { name: "右上角缩小", value: "ZOOM_OUT_RIGHT_TOP" },
          { name: "右下角缩小", value: "ZOOM_OUT_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" },
        ],
        select: {
          enter: "无", //选中的特效
          Out: "无",
          enterDeta: "", //出入场时间
          OutDeta: "",
          lineH: "", //行高
          speed: "", //速度
        },
      },
      timeTypeList: [
        {value: 0, label: "昨天"},
        {value: 1, label: "今天"},
        {value: 2, label: "明天"},
        {value: 3, label: "后天"},
        {value: 4, label: "大后天"},
        {value: 5, label: "大大后天"}
      ],
      textSpan: 8,
      inputSpan: 16,
      html: "",
      heightProps: 460,
      heightEditorProps: 540,
    };
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch: {
    proData: {
      handler(newName, oldName) {
        if (newName) {
          if (newName.type == "Weather") {
            this.html = newName.weatherHtml
            if (program.isSetData == false) {
              this.refreshComKey++;
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.proData && this.proData.valueWeather.length > 0) {
      this.handleChange(this.proData.valueWeather)
    } else {
      this.handleChange(this.defaultWeather)
    }
  },
  // updated() {
  //   this.handleChange(this.defaultWeather)
  // },
  methods: {
    onChange(editor) {
      // console.log(editor.getHtml())
      // this.proData.html = editor.getHtml()
      program.weatherNewText(editor.getHtml())
    },
    changeTimeType(val) {
      this.proData.weatherHtml = ''
      program.setWeather();
      window.editor.setHtml(this.proData.weatherHtml)
    },
    visibleChange(val) {
      if (val) {
		  //此处可以出来选中下拉选项，清空input搜索值
      }
      this.$nextTick(() => {
        // 添加这段代码
        const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
        Array.from($el).map((item) => item.removeAttribute('aria-owns'));
      });
    },
    handleChange(value) {
      //  ['北京市', '北京市', 2]
      let code = value[2];
      // console.log(selectData)
      // let city = this.$refs.areaCascader.getCheckedNodes()[0].pathLabels[2];
      if (this.$refs.areaCascader.currentValue.length <= 0) {
        return
      }
      let city = this.$refs.areaCascader.currentValue[2];
      this.$nextTick(() => {
        this.$http({
          url: this.$http.adornUrl('/sys/thirdParty/weather/toDay'),
          method: 'get',
          params: this.$http.adornParams({'code': code})
        }).then(({data}) => {
          // console.log(data)
          if (data && data.code === 0) {
            this.proData.saveWeather = data.data
            program.setWeather();
            window.editor.setHtml(this.proData.weatherHtml)
          } else {
            this.$Message.error(data.msg);
            program.setWeather(true, code, city);
            window.editor.setHtml(this.proData.weatherHtml)
          }
        })
        // $.ajax(`/weath/${code}`, {
        //   success: (x) => {
        //     this.proData.saveWeather = x
        //     program.setWeather();
        //   },
        //   error: (err) => {
        //     console.log(err);
        //   }
        // });
      })
    },
  },
};
</script>
<style scoped>
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}

.block {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 2px 0;
  display: block;
}

#wangEditor2 {
  width: 100%;
  height: 118px;
  padding-bottom: 5px;
}
</style>
