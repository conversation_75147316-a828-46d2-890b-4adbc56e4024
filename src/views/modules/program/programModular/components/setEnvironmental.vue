<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'environmental'" class="indent">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      <br>
      W: <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :max="programData.canvasWidth" :min="0"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H: <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :max="programData.canvasHeight" :min="0"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      <el-divider content-position="left">{{ $t('program.MonitoringProperties') }}</el-divider>
    </div>
    <div>
      <el-checkbox v-model="proData.assePropData.display['标题']" @change="PBoxDisplay">
        {{ $t('announcement.title') }}</el-checkbox>
      <el-input v-model="proData.assePropData.title" :placeholder="$t('program.PleaseEnterContent')" size="mini"
        style="width:195px;" @input="titleInputText"></el-input>
    </div>
    <div style="margin-top:15px;">
      <el-checkbox v-model="proData.assePropData.display['温度']" @change="PBoxDisplay">
        {{ $t('cardDevice.temperature') }}</el-checkbox>
      <el-radio v-model="proData.assePropData.temperatureNum" label="1" style="width:10px;"
        @change="setTemperatureNum">℃</el-radio>
      <el-radio v-model="proData.assePropData.temperatureNum" label="2" style="width:10px;"
        @change="setTemperatureNum">℉</el-radio>
      <el-input v-model="proData.assePropData.temperature" size="mini" style="width:86px;" @input="setDataInput('temperature')">
      </el-input>
    </div>
    <div style="margin-top:5px;">
      <span style="margin-left:127px">{{ $t('program.compensate') }}</span>
      <el-input-number controls-position="right" size="mini" v-model="proData.assePropData.temperatureBC"
        style="margin-left:10px;width:85px;"></el-input-number>
    </div>
    <div style="margin-top:15px;" class="setData">
      <el-checkbox v-model="proData.assePropData.display['湿度']" @change="PBoxDisplay">
        {{ $t('card.humidity') }}</el-checkbox>
      <span class="setDataSpan">%RH</span>
      <el-input v-model="proData.assePropData.humidity" size="mini" class="el_input" @input="setDataInput('humidity')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['风速']" @change="PBoxDisplay">
        {{ $t('program.windSpeed') }}</el-checkbox>
      <span class="setDataSpan">m/s</span>
      <el-input v-model="proData.assePropData.windSpeed" size="mini" class="el_input" @input="setDataInput('windSpeed')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['风向']" @change="PBoxDisplay">
        {{ $t('program.windDirection') }}</el-checkbox>
      <el-input v-model="proData.assePropData.windDirection" size="mini" class="el_input" @input="setDataInput('windDirection')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['噪音']" @change="PBoxDisplay">
        {{ $t('program.noise') }}</el-checkbox>
      <span class="setDataSpan">dB(A)</span>
      <el-input v-model="proData.assePropData.noise" size="mini" class="el_input" @input="setDataInput('noise')">
      </el-input>
    </div>
    <div class="setData" style='margin-bottom:5px;'>
      <el-checkbox v-model="proData.assePropData.display['PM10']" @change="PBoxDisplay">PM10
      </el-checkbox>
      <span class="setDataSpan">μg/m³</span>
      <el-input v-model="proData.assePropData.Pm10" size="mini" class="el_input" @input="setDataInput('pm10')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['PM2.5']" @change="PBoxDisplay">PM2.5
      </el-checkbox>
      <span class="setDataSpan">μg/m³</span>
      <el-input v-model="proData.assePropData.Pm25" size="mini" class="el_input" @input="setDataInput('pm25')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['SO2']" @change="PBoxDisplay">SO₂
      </el-checkbox>
      <span class="setDataSpan">ppb</span>
      <el-input v-model="proData.assePropData.SO2" size="mini" class="el_input" @input="setDataInput('SO2')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['NO2']" @change="PBoxDisplay">NO₂
      </el-checkbox>
      <span class="setDataSpan">ppb</span>
      <el-input v-model="proData.assePropData.NO2" size="mini" class="el_input" @input="setDataInput('NO2')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['CO']" @change="PBoxDisplay">CO
      </el-checkbox>
      <span class="setDataSpan">ppb</span>
      <el-input v-model="proData.assePropData.CO" size="mini" class="el_input" @input="setDataInput('CO')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['O3']" @change="PBoxDisplay">O₃
      </el-checkbox>
      <span class="setDataSpan">ppb</span>
      <el-input v-model="proData.assePropData.O3" size="mini" class="el_input" @input="setDataInput('O3')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['pressure']" @change="PBoxDisplay">
      {{$t('meteorological.pressure')}}
      </el-checkbox>
      <span class="setDataSpan">hPa</span>
      <el-input v-model="proData.assePropData.pressure" size="mini" class="el_input" @input="setDataInput('pressure')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['rainfall']" @change="PBoxDisplay">
        {{$t('meteorological.rainFall')}}
      </el-checkbox>
      <span class="setDataSpan">mm</span>
      <el-input v-model="proData.assePropData.rainfall" size="mini" class="el_input" @input="setDataInput('rainfall')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['radiation']" @change="PBoxDisplay">
        {{$t('meteorological.radiation')}}
      </el-checkbox>
      <span class="setDataSpan">W/m²</span>
      <el-input v-model="proData.assePropData.radiation" size="mini" class="el_input" @input="setDataInput('radiation')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['beam']" @change="PBoxDisplay">
        {{$t('program.lightIntensity')}}
      </el-checkbox>
      <span class="setDataSpan">lux</span>
      <el-input v-model="proData.assePropData.beam" size="mini" class="el_input" @input="setDataInput('beam')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['CO2']" @change="PBoxDisplay">CO₂
      </el-checkbox>
      <span class="setDataSpan">ppm</span>
      <el-input v-model="proData.assePropData.CO2" size="mini" class="el_input" @input="setDataInput('CO2')">
      </el-input>
    </div>
    <div class="setData">
      <el-checkbox v-model="proData.assePropData.display['NAI']" @change="PBoxDisplay">NAI
      </el-checkbox>
      <span class="setDataSpan">cm³</span>
      <el-input v-model="proData.assePropData.NAI" size="mini" class="el_input" @input="setDataInput('NAI')">
      </el-input>
    </div>

    <div style="margin-top:5px;height:5px;"></div>
    <el-divider content-position="left">{{ $t('program.DisplayMode') }}</el-divider>
    <div class="setData">
      <el-radio v-model="proData.assePropData.position" @change="alignPosition" label="1"
        style="width:35px;margin-left:5px;">{{ $t('program.stayLeft') }}</el-radio>
      <el-radio v-model="proData.assePropData.position" @change="alignPosition" label="2"
        style="width:35px;margin-left:5px;">{{ $t('program.Centered') }}</el-radio>
      <el-radio v-model="proData.assePropData.position" @change="alignPosition" label="3"
        style="width:35px;margin-left:5px;">{{ $t('program.KeepRight') }}</el-radio>
    </div>
    <!-- <div class="setData">
        <el-radio v-model="proData.assePropData.position" @change="alignPosition" label="4" style="width:35px;margin-left:9px;">{{$t('program.singleLineScroll')}}</el-radio>
        <span style="margin-left:20px;">{{$t('program.speed')}}</span>
        <el-input-number :min="1" @change="programData.set_HjjcRollingSpeed()" controls-position="right" size="mini" v-model="proData.assePropData.RollingSpeed" style="margin-left:5px;width:85px;"></el-input-number>
        <span style="margin-left:5px;">{{$t('program.ms/pixel')}}</span>        
    </div> -->
    <div style="margin-top:5px;">
      <el-row>
        <el-col :span="9">
          <el-radio v-model="proData.assePropData.position" @change="alignPosition" label="4" style="width:35px;margin-left:5px;">{{$t('program.singleLineScroll')}}</el-radio>
        </el-col>
        <el-col :span="3">
          <span style="margin-left:10px;">{{$t('program.speed')}}</span>
        </el-col>
        <el-col :span="7">
          <el-input-number :min="1" @change="programData.setRollingSpeed()" controls-position="right" size="mini" v-model="proData.assePropData.RollingSpeed" style="margin-left:10px;width:90px;"></el-input-number>
        </el-col>
        <el-col :span="5">
          <span style="margin-left:5px;">{{$t('program.ms/pixel')}}</span> 
        </el-col>
      </el-row>
    </div>
    <div style="margin-top:5px;margin-left:9px;">
      <span>{{ $t('program.fontSize') }}</span>
      <el-input-number controls-position="right" :min="12" size="mini" v-model="proData.fontSize"
        style="margin-left:5px;width:85px;margin-top:3px;" @change="programData.setEnvironmentalFontSize()"></el-input-number>
    </div>
    <div style="position: relative;top:10px;margin-left:9px;">
      <span>{{ $t('program.fontColor') }}</span>
      <el-color-picker v-model="proData.textColor" size="mini" style="position:absolute;top:-5px" @change="textColor">
      </el-color-picker>
      <div class="textType textTypeB" @click="setStyle('b')" :class="proData.textStyle.b ? 'selectType' : ''">B</div>
      <div class="textType textTypeI" @click="setStyle('i')" :class="proData.textStyle.i ? 'selectType' : ''">I</div>
      <div class="textType textTypeU" @click="setStyle('u')" :class="proData.textStyle.u ? 'selectType' : ''">U</div>
    </div>
    <el-divider content-position="left">{{ $t('program.PlaybackMode') }}</el-divider>
    <div>
      <span>{{ $t('program.refreshCycle') }}</span>
      <el-input-number :min="1" controls-position="right" size="small" v-model="proData.RefreshCycle"
        class="numberMiniT" ></el-input-number>
      <span>{{ $t('program.minute') }}</span>
    </div>
    <div>
      <span>{{ $t('program.PlayTime') }}</span>
      <el-input-number :min="1" controls-position="right" size="small" v-model="proData.PlaybackDuration"
        class="numberMiniT"></el-input-number>
      <span>{{ $t('program.Second') }}</span>
    </div>
  </div>
</template>

<script>
import program from "../program.js" //被监听对象
export default {
  props: {
    proData: Object
  },
  data() {
    return {
      programData: {},
      input: "",
      radio: '1',
      num: '0',
    }
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top 
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: { //监听的值
      handler(newName, oldName) {
        this.programData = program
      },
      immediate: true,
    }
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  methods: {
    titleInputText() {
      program.setTitleInputText()
    },
    PBoxDisplay() {
      program.setPBoxDisplay()
    },
    setTemperatureNum() {
      program.setTemperatureNum()
    },
    setDataInput(item) {
      program.setDataInput(item)
    },
    alignPosition() {
      program.setAlignPosition()
    },
    setStyle(x) {
      program.ALLBoxData[program.selectBox].textStyle[x] = !program.ALLBoxData[program.selectBox].textStyle[x]
      program.setEnvironmentalTextStyle()
    },
    textColor() {
      program.setEnvironmentalColor()
    }
  }
}
</script>
<style>
.el-color-picker--mini {
  width: 24px;
  height: 24px;
  margin-top: 2px;
}
</style>
<style scoped>
.textType {
  width: 28px;
  height: 28px;
  border: solid 1px #e6e6e6;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  margin-top: -2px;
  border-radius: 4px;
  user-select: none;
}

.textTypeB {
  margin-left: 36px;
}

.textTypeI,
.textTypeU {
  margin-left: 5px;
}

.textTypeU {
  text-decoration: underline;
}

.selectType {
  background: #9dc9f3;
}
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}

.numberMiniT {
  width: 180px;
  height: 32px;
  margin: 5px 5px 0 5px;
}
.setData {
  height: 22px;
  line-height: 22px;
  margin-top: 14px;
  position: relative;
}

.el_input {
  width: 86px;
  position: absolute;
  right: 40px;
}

.setDataSpan {
  position: absolute;
  right: 130px;
}

span {
  font-size: 13px;
}
</style>