<template>
  <div class="elementType">
    <div v-if="programData.assemblyProp!='multiWindow'">
      <el-divider content-position="left">{{$t('program.BasicProperties')}}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].left"
        :max="programData.canvasWidth-programData.ALLBoxData[programData.selectBox].width" :min="0" class="numberMini" @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].top"
        :max="programData.canvasHeight-programData.ALLBoxData[programData.selectBox].height" :min="0" class="numberMini" @change="programData.modifyXOrY()"></el-input-number>
      <br>
      W: <el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].width"
        :max="programData.canvasWidth" :min="0" class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H: <el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].height"
        :max="programData.canvasHeight" :min="0" class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      <el-divider content-position="left">{{$t('program.fileProperties')}}</el-divider>
      <p >{{$t('file.name')}}: {{programData.ALLBoxData[programData.selectBox].media.fileName}}</p>
      <el-button style="margin-top: 5px;" type="success" size="small" @click="discountPreview()"
        v-if="programData.isDiscount && programData.ALLBoxData[programData.selectBox].media.suffix!='mp3'">{{ $t('program.preview') }}</el-button>
      <el-divider content-position="left">{{$t('program.PlayProperties')}}</el-divider>
      <span >{{$t('program.PlayTime')}}:</span>
      <el-input-number :min="1" :disabled="programData.ALLBoxData[programData.selectBox].media.suffix=='mp4'?true:false" controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].PlaybackDuration" class="numberMini" style="width:100px;margin-left:20px;"></el-input-number>
      <div style="margin-top:4px;">
        <span>{{ $t('program.specialEffects') }}:</span>
        <el-checkbox-group style="display: inline-block;margin-left:48px;" v-model="proData.specialEffectMode" :max="1">
          <el-checkbox label="blink" size="small" border >{{ $t('program.blink') }}</el-checkbox>
          <el-checkbox label="breathe" size="small" border >{{ $t('program.breathe') }}</el-checkbox>
        </el-checkbox-group>
        <div>
          <span>{{ $t('program.specificFrequency') }}:</span>
          <el-input-number style="width:195px;margin-left:20px;margin-top:4px;" v-model="proData.specificFrequency"
            controls-position="right" :min="0.1" :precision="1" :step="0.1"></el-input-number> Hz
        </div>
      </div>
    </div>
    <div v-else>
      <p >{{$t('file.name')}}:{{programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].media.fileName}}</p>
      <el-divider content-position="left">{{$t('program.PlayProperties')}}</el-divider>
      <span >{{$t('program.PlayTime')}}</span>
      <el-input-number :min="1" :disabled="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].media.suffix=='mp4'?true:false" controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].PlaybackDuration" class="numberMini" style="width:100px;"></el-input-number>
      <div style="margin-top:4px;">
        <span>{{ $t('program.specialEffects') }}:</span>
        <el-checkbox-group style="display: inline-block;margin-left:48px;" v-model="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].specialEffectMode" :max="1">
          <el-checkbox label="blink" size="small" border >{{ $t('program.blink') }}</el-checkbox>
          <el-checkbox label="breathe" size="small" border >{{ $t('program.breathe') }}</el-checkbox>
        </el-checkbox-group>
        <div>
          <span>{{ $t('program.specificFrequency') }}:</span>
          <el-input-number style="width:195px;margin-left:20px;margin-top:4px;" v-model="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].specificFrequency"
            controls-position="right" :min="0.1" :precision="1" :step="0.1"></el-input-number> Hz
        </div>
      </div>
    </div>
    <el-dialog :title="$t('program.preview')" :visible.sync="showDiscount" :width="canvasWidth + 'px'">
      <div :style="{'height': showDiscountHeight + 'px', 'overflow': 'auto', 'background-color': '#000'}">
        <div id="showDiscount" :style="{ 'height': canvasHeight + 'px', 'position ': 'absolute',
          'width' : canvasWidth + 'px', 'background-color': '#000', 'overflow': 'hidden'}">
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDiscount = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" size="small" @click="showDiscount = false">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="$t('program.preview')" :visible.sync="showDiscountH" width="50%">
      <div :style="{'width': canvasWidth + 'px', 'overflow': 'auto', 'background-color': '#000',}">
        <div id="showDiscountH" :style="{ 'height': canvasHeight + 'px', 'position ': 'absolute',
          'width' : canvasWidth + 'px', 'background-color': '#000', 'overflow': 'hidden'}">
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDiscountH = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" size="small" @click="showDiscountH = false">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import program from "../program.js" //被监听对象
import $ from "jQuery"
export default {
  props: {
    proData: Object
  },
  data(){
    return {
      programData:{},
      showDiscount: false,
      showDiscountHeight: 0,
      showDiscountH: false,
      showDiscountWidth: 0,
      canvasHeight: 0,
      canvasWidth: 0,
    }
  },
  computed: {
    proDataCopy: {
      get() {
        if (undefined != this.proData) {
          return JSON.parse(JSON.stringify(this.proData))
        }
      }
    },
  },
  watch:{
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program:{ //监听的值
      handler(newName, oldName){
        this.programData=program
      },
      immediate: true,
    },
  },
  methods:{
    async discountPreview() {
      // 判断是否需要打折
      var data = this.programData
      var itemData = this.programData.ALLBoxData[this.programData.selectBox]
      if (data.isDiscount && itemData.media.suffix != 'mp3') {
        // 超宽
        if (data.discountModel == "width") {
          this.showDiscount = true
          var documentClientHeight = document.documentElement['clientHeight']
          this.showDiscountHeight = (documentClientHeight - (documentClientHeight * 15 / 100)) - 200
          var split = data.discount.split(",")
          // 计算实际使用到的宽高
          var discountWH = await this.computeHW(data.canvasHeight, data.canvasWidth, data.discount)
          this.canvasHeight = discountWH.height
          this.canvasWidth = discountWH.width

          $('#showDiscount').html('');
          if (itemData.media.suffix != "mp4") {// 如果为图片
            var imgHtml = ''
            var mediaTZboxList = $('#canvasbox').children(".mediaTZbox")
            if (mediaTZboxList && mediaTZboxList.length > 0) {
              for (let i = 0; i < mediaTZboxList.length; i++) {
                const element = mediaTZboxList[i];
                const P_TZD = $(element).children(".P_TZD")
                if (P_TZD && P_TZD.length > 0) {
                  const media = $(element).children(".media")[0]
                  if (media) {
                    const img = $(media).children("img")[0]
                    if (img) {
                      imgHtml += `<img src="${$(img).attr("src")}"
                        style="width:${$(element).css("width")};height:${$(element).css("height")};display:block;"></img>`
                    }
                  }
                }
              }
              if (imgHtml) {
                var showDivImg = ''
                var total = 0
                for (let i = 0; i < split.length; i++) {
                  const element = parseFloat(split[i]);
                  total += element
                  if (i > 0) {
                    showDivImg += `<div style="position:relative;width: ${total}px; overflow: hidden;right: ${total - element}px">${imgHtml}</div>`
                  } else {
                    showDivImg += `<div style="position:relative;width: ${total}px; overflow: hidden">${imgHtml}</div>`
                  }
                }
                if (total < data.canvasWidth) {
                  showDivImg += `<div style="position:relative;width: ${data.canvasWidth}px; overflow: hidden;right: ${total}px">${imgHtml}</div>`
                }
                $('#showDiscount').append(showDivImg)
              }
            }
          } else {
            // 如果为视频的情况
            var videoHtml = ''
            var mediaTZboxList = $('#canvasbox').children(".mediaTZbox")
            if (mediaTZboxList && mediaTZboxList.length > 0) {
                for (let i = 0; i < mediaTZboxList.length; i++) {
                  const element = mediaTZboxList[i];
                  const P_TZD = $(element).children(".P_TZD")
                  if (P_TZD && P_TZD.length > 0) {
                    const media = $(element).children(".media")[0]
                    if (media) {
                      const video = $(media).children("video")[0]
                      if (video) {
                        videoHtml += `<video src="${$(video).attr("src")}"
                          style="width:${$(element).css("width")};height:${$(element).css("height")};object-fit:fill;" autoplay loop muted></video>`
                      }
                    }
                  }
                }
                if (videoHtml) {
                  var showDivVideo = ''
                  var total = 0
                  for (let i = 0; i < split.length; i++) {
                    const element = parseFloat(split[i]);
                    total += element
                    if (i > 0) {
                      showDivVideo += `<div style="position:relative;width: ${total}px; overflow: hidden;right: ${total - element}px">${videoHtml}</div>`
                    } else {
                      showDivVideo += `<div style="position:relative;width: ${total}px; overflow: hidden">${videoHtml}</div>`
                    }
                  }
                  if (total < data.canvasWidth) {
                    showDivVideo += `<div style="position:relative;width: ${data.canvasWidth}px; overflow: hidden;right: ${total}px">${videoHtml}</div>`
                  }
                  $('#showDiscount').append(showDivVideo)
                }
            }
          }
        } else {// 超高
          this.showDiscountH = true
          var split = data.discount.split(",")
          // 此处宽高需要互换
          var discountWH = await this.computeHW(data.canvasWidth, data.canvasHeight, data.discount)
          this.canvasHeight = discountWH.width
          this.canvasWidth = discountWH.height
          $('#showDiscountH').html('');
          if (itemData.media.suffix != "mp4") {// 如果为图片
            var imgHtml = ''
            var mediaTZboxList = $('#canvasbox').children(".mediaTZbox")
            if (mediaTZboxList && mediaTZboxList.length > 0) {
              for (let i = 0; i < mediaTZboxList.length; i++) {
                const element = mediaTZboxList[i];
                const P_TZD = $(element).children(".P_TZD")
                if (P_TZD && P_TZD.length > 0) {
                  const media = $(element).children(".media")[0]
                  if (media) {
                    const img = $(media).children("img")[0]
                    if (img) {
                      imgHtml += `<img src="${$(img).attr("src")}"
                        style="width:${$(element).css("width")};height:${$(element).css("height")};display:block;"></img>`
                    }
                  }
                }
              }
              if (imgHtml) {
                var showDivImg = ''
                var total = 0
                var temp = []
                for (let i = 0; i < split.length; i++) {
                  const element = parseFloat(split[i]);
                  total += element
                  if (i > 0) {
                    temp.push(total - element)
                    var sum = temp.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
                    sum = sum + total - element
                    showDivImg += `<div style="position:relative;width:${data.canvasWidth}px; height: ${total}px; overflow: hidden;top: -${sum}px;left: ${data.canvasWidth * i}px">${imgHtml}</div>`
                  } else {
                    showDivImg += `<div style="position:relative;width:${data.canvasWidth}px;height: ${total}px;top: 0px;left: 0px; overflow: hidden">${imgHtml}</div>`
                  }
                }
                if (total < data.canvasWidth) {
                  temp.push(total)
                  var sum = temp.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
                  sum = sum + temp[temp.length - 1]
                  showDivImg += `<div style="position:relative;width:${data.canvasWidth}px;height: ${data.canvasWidth}px; top: -${sum}px;overflow: hidden;left: ${data.canvasWidth * split.length}px">${imgHtml}</div>`
                }
                $('#showDiscountH').append(showDivImg)
              }
            }
          } else {
            // 如果为视频的情况
            var videoHtml = ''
            var mediaTZboxList = $('#canvasbox').children(".mediaTZbox")
            if (mediaTZboxList && mediaTZboxList.length > 0) {
                for (let i = 0; i < mediaTZboxList.length; i++) {
                  const element = mediaTZboxList[i];
                  const P_TZD = $(element).children(".P_TZD")
                  if (P_TZD && P_TZD.length > 0) {
                    const media = $(element).children(".media")[0]
                    if (media) {
                      const video = $(media).children("video")[0]
                      if (video) {
                        videoHtml += `<video src="${$(video).attr("src")}"
                          style="width:${$(element).css("width")};height:${$(element).css("height")};object-fit:fill;" autoplay loop muted></video>`
                      }
                    }
                  }
                }
                if (videoHtml) {
                  var showDivVideo = ''
                  var total = 0
                  var temp = []
                  for (let i = 0; i < split.length; i++) {
                    const element = parseFloat(split[i]);
                    total += element
                    if (i > 0) {
                      temp.push(total - element)
                      var sum = temp.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
                      sum = sum + total - element
                      showDivVideo += `<div style="position:relative;width:${data.canvasWidth}px; height: ${total}px; overflow: hidden;top: -${sum}px;left: ${data.canvasWidth * i}px">${videoHtml}</div>`
                    } else {
                      showDivVideo += `<div style="position:relative;width:${data.canvasWidth}px;height: ${total}px;top: 0px;left: 0px; overflow: hidden">${videoHtml}</div>`
                    }
                  }
                  if (total < data.canvasWidth) {
                    temp.push(total)
                    var sum = temp.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
                    sum = sum + temp[temp.length - 1]
                    showDivVideo += `<div style="position:relative;width:${data.canvasWidth}px;height: ${data.canvasWidth}px; top: -${sum}px;overflow: hidden;left: ${data.canvasWidth * split.length}px">${videoHtml}</div>`
                  }
                  $('#showDiscountH').append(showDivVideo)
                }
            }
          }
        }
      }
    },
    // 计算需要使用到的宽高
    computeHW(canvasHeight, canvasWidth, discount) {
      return new Promise(function(resolve, reject) {
        var split = discount.split(",")
        var width = parseFloat(split[0])
        var total = 0
        var line = 0
        for (let i = 0; i < split.length; i++) {
            const element = parseFloat(split[i]);
            if (width < element) {
              width = element
            }
            total += element
            if (total > canvasWidth) {
              var remain = canvasWidth - total
              if (width < remain) {
                width = remain
              }
            } else {
              line += 1
            }
        }
        if (total < canvasWidth) {
          var remain = canvasWidth - total
          if (width < remain) {
            width = remain
          }
          line += 1
        }
        resolve({height: canvasHeight * line, width: width})
      })
    },
  }
}
</script>
<style>
/* 针对 Element UI 的滚动条样式进行覆盖 */
.el-dialog__body {
  overflow: auto; /* 隐藏滚动条 */
}
</style>
<style scoped>
.numberMini{
  margin-left:5px;
  width:100px;
  height:32px;
  margin-top:5px;
}
</style>
