<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'textTemplate' || programData.ALLBoxData[programData.selectBox].type == 'templateText'">
      <el-divider content-position="left">{{ $t('program.BasicProperties') }}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
        :max="programData.canvasWidth - programData.ALLBoxData[programData.selectBox].width" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
        :max="programData.canvasHeight - programData.ALLBoxData[programData.selectBox].height" class="numberMini"
        @change="programData.modifyXOrY()"></el-input-number>
      <br />
      W:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].width" :min="0" :max="programData.canvasWidth"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H:
      <el-input-number controls-position="right" size="small"
        v-model="programData.ALLBoxData[programData.selectBox].height" :min="0" :max="programData.canvasHeight"
        class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
    </div>
    <div  v-if="proData.type == 'textTemplate'">
      <el-divider content-position="left">{{$t('program.SelectedMaterialInformation')}}</el-divider>
      <div class="block">
        <span>{{$t('program.PlayTime')}}:</span>
        <el-input-number :min="1" controls-position="right" size="small"
          v-model="programData.ALLBoxData[programData.selectBox].PlaybackDuration" class="numberMini" style="width:100px;"></el-input-number>
      </div>
      <div class="block">
        <span>{{$t('program.addText')}}:</span>
        <Button style="margin-left: 5px" size="small" icon="md-add" type="primary" @click="clickAddText()"></Button>
      </div>
    </div>
    <div v-if="proData.type == 'templateText'">
      <el-divider content-position="left">{{ $t('program.TextEditor') }}</el-divider>
      <editor :heightProps="200" :htmlProps="html" @onChange="onChange"/>
      <!-- <el-divider content-position="left">{{ $t('program.TextEditor') }}</el-divider>
      <div ref="wangEditor1" id="wangEditor1"></div> -->
    </div>

  </div>
</template>
<script>
import program from "../program.js"; //被监听对象
// import E from "wangeditor"; //wangeditor编辑器
// import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import editor from "@/utils/editor.vue";
export default {
  // components: { Editor, Toolbar },
  components: { editor },
  props: {
    proData: Object
  },
  data() {
    return {
      programData: {},
      html: "",
    };
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true,
    }
  },
  mounted() {
    // this.setcse();
  },
  methods: {
    clickAddText() {
      program.assemblyProp = 'templateText'
      program.addDom(null, false, this.proData.textTempIndex)
      console.log(this.programData)
      console.log(this.proData)
    },
    onChange(editor) {
      console.log(editor.getHtml())
    },
    /* setcse() {
      //文本编辑器的初始化
      window.editor = new E(this.$refs.wangEditor1);
      editor.config.menus = [
        "bold",
        "fontSize",
        "fontName",
        "italic",
        "underline",
        "strikeThrough",
        "justify",
        "lineHeight",
        'foreColor',
        'backColor',
      ];
      editor.config.colors = [
        '#000000',
        '#ffffff',
        '#fff000',
        '#ff00ff',
        '#00ffff',
        '#ff0000',
        '#0000ff',
        '#00ff00',
      ]
      editor.config.showFullScreen = false;
      window.editor.create();

      window.editor.config.onchange = function (newHtml) {
        // console.log(newHtml)
        //wangEditor4打包后，将光标移到句首，回车后删除会增加文字颜色，且样式特殊 style="color: var(--w-e-textarea-color);"
        //所以当出现了这种样式就直接将样式删除
        if (-1!==newHtml.search(/<span.*?color: var.*?>/g)){
          newHtml=newHtml.replace(/color: var.*?;|background-color: var.*?;/g,"")
          // newHtml=newHtml.replace(/<span\s+[^>]*>|<\/span>/g,"")
        //   newHtml=newHtml.replace(/color: var.*;|background-color: var.*;/g,"")
        }
        // var str="<p><span style=\"color: var(--w-e-textarea-color);\">大大大大 打</span><br/></p>"
        // if (-1!==str.search(/<span.*?color: var.*?>/g)){
        //   console.log("进入测试")
        //   console.log(str.replace(/<span\s+[^>]*>|<\/span>/g,""))
        // }

        // console.log(newHtml)
        // program.newText(newHtml)
      }
    }, */
  }
};
</script>
<style scoped>
.numberMini {
  margin-left: 5px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
}

.block {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 2px 0;
  display: block;
}
</style>
