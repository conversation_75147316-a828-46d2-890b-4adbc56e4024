<template>
  <div class="elementType">
    <el-divider content-position="left">{{$t('program.Multi-MaterialBasicProperties')}}</el-divider>
    X: &nbsp;<el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].left" :min="0"
      :max="programData.canvasWidth-programData.ALLBoxData[programData.selectBox].width" class="numberMini" @change="programData.modifyXOrY()"></el-input-number>
    Y: &nbsp;<el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].top" :min="0"
      :max="programData.canvasHeight-programData.ALLBoxData[programData.selectBox].height" class="numberMini" @change="programData.modifyXOrY()"></el-input-number>
    <br>
    W: <el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].width"
        :max="programData.canvasWidth" :min="0" class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
    H: <el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].height"
        :max="programData.canvasHeight" :min="0" class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
    <el-divider content-position="left">{{$t('program.mediaList')}}</el-divider>
    <div class="mediaList">
        <div class="setBtn">
            <div class="control" :title="$t('common.add')">
                <el-dropdown trigger="click" placement="bottom-start" @command="addWindowItem">
                    <i class="el-icon-plus" style="color:#333;font-size:20px;"></i>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="text">
                          <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-text"></use>
                          </svg>
                          <span>{{$t('program.text')}}</span>
                        </el-dropdown-item>
                        <el-dropdown-item command="media">
                          <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-wenjian"></use>
                          </svg>
                          <span>{{$t('task.media')}}</span>
                        </el-dropdown-item>
                        <el-dropdown-item command="DigitalClock">
                          <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-Digital-AlarmClock"></use>
                          </svg>
                          <span>{{$t('program.DigitalClock')}}</span></el-dropdown-item>
                        <el-dropdown-item command="simulationClock">
                          <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-huanjingjiance"></use>
                          </svg>
                          <span>{{$t('program.analogClock')}}</span></el-dropdown-item>
                        <el-dropdown-item command="environmental">
                          <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-shijian3"></use>
                          </svg>
                          <span>{{$t('program.EnvironmentalMonitoring')}}</span></el-dropdown-item>
                        <el-dropdown-item command="Weather">
                          <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-tianqi"></use>
                          </svg>
                          <span>{{$t('program.weather')}}</span></el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <div class="control" :title="$t('common.delete')" @click="setDomStyle('delete')" v-if="programData.ALLBoxData[programData.selectBox].multiWindowData.length>0?'isCursor':''">
                <i class="el-icon-delete" ></i>
            </div>
            <div class="isCursor" :title="$t('common.delete')" v-else>
                <i class="el-icon-delete" ></i>
            </div>
            <div class="control" :title="$t('program.empty')" @click="setDomStyle('empty')" v-if="programData.ALLBoxData[programData.selectBox].multiWindowData.length>0?'isCursor':''">
                <div :title="$t('program.empty')">
                  <svg class="iconT" aria-hidden="true">
                    <use xlink:href="#icon-qingkong"></use>
                  </svg>
                </div>
            </div>
            <div class="isCursor" :title="$t('program.empty')" v-else>
                <div :title="$t('program.empty')">
                  <svg class="jinyong" aria-hidden="true">
                    <use xlink:href="#icon-qingkong"></use>
                  </svg>
                </div>
            </div>
            <div class="control" :title="$t('program.up')" @click="setDomStyle('up')" v-if="programData.ALLBoxData[programData.selectBox].multiWindowData.length>1 && programData.windowPage!=0?'isCursor':''">
                <i class="el-icon-upload2"></i>
            </div>
            <div class="isCursor" :title="$t('program.up')" v-else>
                <i class="el-icon-upload2"></i>
            </div>
            <div class="control" :title="$t('program.down')" @click="setDomStyle('down')" v-if="programData.ALLBoxData[programData.selectBox].multiWindowData.length>1 && programData.windowPage!=programData.ALLBoxData[programData.selectBox].multiWindowData.length-1?'isCursor':''">
                <i class="el-icon-download"></i>
            </div>
            <div class="isCursor" :title="$t('program.down')" v-else>
                <i class="el-icon-download"></i>
            </div>
        </div>
        <ul class="windowUl">
            <li class="windowLi" :class="programData.windowPage==index?'windowLiSelect':''" v-for="(item,index) in programData.ALLBoxData[programData.selectBox].multiWindowData" :key="index" @click="clickWindowList(index)">
                <span class="indent">{{index+1}},</span>
                <!-- <span v-if="item.type=='media'">{{$t('task.media')}} - {{item.media.fileName}}.{{item.media.suffix}}</span> -->
                <span v-if="item.type=='media'">{{$t('task.media')}}</span>
                <!-- <span style="font-size:14px;" v-if="item.type=='media'"></span> -->
                <span v-if="item.type=='text'">{{$t('program.text')}}</span>
                <span v-if="item.type=='DigitalClock'">{{$t('program.DigitalClock')}}</span>
                <span v-if="item.type=='environmental'">{{$t('program.EnvironmentalMonitoring')}}</span>
                <span v-if="item.type=='simulationClock'">{{$t('program.analogClock')}}</span>
                <span v-if="item.type=='Weather'">{{$t('program.weather')}}</span>
            </li>
        </ul>
    </div>
    <el-divider content-position="left">{{$t('program.BasicProperties')}}</el-divider>
    <div class="NavContent" v-if="programData.attributeNav!='page'&&programData.ALLBoxData[programData.selectBox].multiWindowData.length>0">
      <setText :proData="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage]" v-if="programData.attributeNav==='assembly'&&programData.assemblyProp=='multiWindow'&&programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].type=='text'"></setText>
      <setMedia v-if="programData.attributeNav==='assembly'&&programData.assemblyProp=='multiWindow'&&programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].type=='media'"></setMedia>
      <setEnvironmental :proData="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage]"  v-if="programData.attributeNav==='assembly'&&programData.assemblyProp=='multiWindow'&&programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].type=='environmental'"></setEnvironmental>
      <setDigitalClock :proData="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage]" v-if="programData.attributeNav==='assembly'&&programData.assemblyProp=='multiWindow'&&programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].type=='DigitalClock'"></setDigitalClock>
      <setWeatherMD :proData="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage]" v-if="programData.attributeNav==='assembly'&&programData.assemblyProp=='multiWindow'&&programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].type=='Weather'"></setWeatherMD>
      <setSimulationClock :proData="programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage]" v-if="programData.attributeNav==='assembly'&&programData.assemblyProp=='multiWindow'&&programData.ALLBoxData[programData.selectBox].multiWindowData[programData.windowPage].type=='simulationClock'"></setSimulationClock>
    </div>

  </div>
</template>

<script>
import program from "../program.js" //被监听对象
import EventBus from "../../EventBus.js"
import setMedia from "./setMediaMD.vue"
import setDigitalClock from "./setDigitalClock.vue"
import setEnvironmental from "./setEnvironmental.vue"
import setSimulationClock from "./setSimulationClock.vue"
import setText from "./setTextMD.vue"
import setWeatherMD from "./setWeatherMD.vue"
export default {
  props:{
    proData:Object
  },
  data(){
    return {
      programData:{},
    }
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  watch:{
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
   },
   program:{ //监听的值
     handler(newName, oldName){
       this.programData=program
     },
    immediate: true,
    }
  },
  components:{
      setMedia,setDigitalClock,setEnvironmental,setSimulationClock,setText,setWeatherMD
  },
  methods:{
      clickWindowList(index){//鼠标点击切换窗口素材
        program.windowPage=index
        program.addWindowItem(program.ALLBoxData[program.selectBox].multiWindowData[program.windowPage],program.ALLBoxData[program.selectBox].multiWindowData[program.windowPage].media)
      },
      setDomStyle(type){ //窗口素材增删改换位
        program.setDomStyle(type)
      },
      addWindowItem(type){ //添加窗口素材
        if(type == "media"){// 如果为媒体类型
            EventBus.$emit("mediaMethod", "multiWindow");
        }else{
            program.addWindowItem(type)
        }
      }
  }
}
</script>
<style >
.el-dropdown-menu__item{
  padding:0 12px !important ;
  line-height: 30px !important;

}
</style>
<style scoped>
.windowUl{
    margin-top:10px;
    font-size: 15px;
}
.windowLi{
    width:100%;
    height:20px;
    line-height:20px;
    cursor: pointer;
}
.windowLi:hover{
    background:#dce3f3;
}
.windowLiSelect{
    background:#dce3f3;
}
.numberMini{
  margin-left:5px;
  width:100px;
  height:32px;
  margin-top:5px;
}

.mediaList{
    width:100%;
    min-height:30px;
}
.setBtn{
    width:100%;
    height:27px;
    display:flex;
    justify-content: space-around;
    align-items: center;
}
.control{
  width:30px;
  height:30px;
  text-align:center;
  margin-top:5px;
  border-radius:3px;
  background:#fff;
  color:#333;
  font-size:20px;
  line-height:30px;
  border: 1px solid #dce3f3;
  cursor: pointer;
}
.control:hover{
  border: 1px solid #68b5fc;
}
.isCursor{
  width:30px;
  height:30px;
  margin-top:5px;
  text-align:center;
  border-radius:3px;
  background:#fff;
  color:#333;
  font-size:20px;
  line-height:30px;
  border: 1px solid #dce3f3;
  cursor: not-allowed;
}
.jinyong{
  width: 28px;
  height: 28px;
  fill: currentColor;
  overflow: hidden;
  cursor: pointer;
  border:#fff solid 1px;
  padding:3px;
}
.jinyong:hover{
  cursor: not-allowed;
}

.icon {
  width: 54px;
  height: 30px;
  padding-top: 15px;
}
.iconT{
  width: 28px;
  height: 28px;
  fill: currentColor;
  overflow: hidden;
  cursor: pointer;
  border:#fff solid 1px;
  padding:3px;
}
</style>
