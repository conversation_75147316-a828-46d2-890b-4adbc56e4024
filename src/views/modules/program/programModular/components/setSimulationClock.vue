<template>
  <div class="elementType">
    <div v-if="programData.ALLBoxData[programData.selectBox].type == 'simulationClock'">
      <el-divider content-position="left">{{$t('program.BasicProperties')}}</el-divider>
      X: &nbsp;<el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].left"
                :min="0" :max="programData.canvasWidth-programData.ALLBoxData[programData.selectBox].width" class="numberMini" @change="programData.modifyXOrY()"></el-input-number>
      Y: &nbsp;<el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].top"
                :min="0" :max="programData.canvasHeight-programData.ALLBoxData[programData.selectBox].height" class="numberMini" @change="programData.modifyXOrY()"></el-input-number>
      <br />
      W: <el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].width"
            :min="0" :max="programData.canvasWidth" class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      H: <el-input-number controls-position="right" size="small" v-model="programData.ALLBoxData[programData.selectBox].height"
            :min="0" :max="programData.canvasHeight" class="numberMini" @change="programData.modifyWidthOrHeight()"></el-input-number>
      <el-divider content-position="left">{{$t('program.SelectedMaterialInformation')}}</el-divider>
    </div>

    <div class="block">
      <span>{{$t('program.PlayTime')}}:</span>
      <el-input-number size="small" style="width:180px;margin-left:15px" :min="0" v-model="proData.inputDuration" controls-position="right" @change="ChangeDuration"></el-input-number>
    </div>
    <div class="block">
      <span class="demonstration">{{$t('program.HourMarkColor')}}:</span>
      <el-color-picker v-model="proData.colorKS" @change="colorstyleS()"></el-color-picker>
    </div>
    <div class="block">
      <span class="demonstration">{{$t('program.minuteScaleColor')}}:</span>
      <el-color-picker v-model="proData.colorKF" @change="colorstylF()"></el-color-picker>
    </div>
    <div class="block">
      <span class="demonstration">{{$t('program.hourHandColor')}}:</span>
      <el-color-picker v-model="proData.colorS" @change="colorstyleZ()"></el-color-picker>
    </div>
    <div class="block">
      <span class="demonstration">{{$t('program.minuteHandColor')}}:</span>
      <el-color-picker v-model="proData.colorF" @change="colorstyleFZ()"></el-color-picker>
    </div>
    <div class="block">
      <span class="demonstration">{{$t('program.pointerColor')}}:</span>
      <el-color-picker v-model="proData.colorM" @change="colorstyleM()"></el-color-picker>
    </div>
    <div class="block">
      <span class="demonstration">{{$t('program.backgroundColor')}}:</span>
      <el-color-picker v-model="proData.colorB" @change="colorstyleB()"></el-color-picker>
    </div>
    <!-- <span>入场特效:</span>
    <el-select v-model="dropdownData.select.enter" placeholder="请选择入场特效" style="width:195px;margin-left:20px;" size="small">
      <el-option :label="item.name" :value="item.value" v-for="(item, index) in dropdownData.enter" :key="index"></el-option>
    </el-select>
    <br />
    <span style="margin-top:4px;">入场时间:</span>
    <el-input size="small" style="width:195px;margin-left:20px;margin-top:4px;" v-model="dropdownData.select.enterDeta"></el-input>
    <br />
    <span style="margin-top:4px;">出场特效:</span>
    <el-select v-model="dropdownData.select.Out" placeholder="请选择出场特效" style="width:195px;margin-left:20px;margin-top:4px;" size="small">
      <el-option :label="item.name" :value="item.value" v-for="(item, index) in dropdownData.Out" :key="index"></el-option>
    </el-select>
    <br />
    <span style="margin-top:4px;">出场时间:</span>
    <el-input size="small" style="width:195px;margin-left:20px;margin-top:4px;" v-model="dropdownData.select.OutDeta"></el-input>
    <br /> -->
  </div>
</template>

<script>
import program from "../program.js"; //被监听对象
export default {
  props: {
    proData: Object
  },
  data() {
    return {
      // colorKS: "#FF1493",
      // colorKF: "#8A2BE2",
      // colorS: "#6495ED",
      // colorF: "#00FFFF",
      // colorM: "#3CB371",
      // colorB: "#ff0033",
      programData: {},
      assemblyData: {
        //小组间数据
        PlaybackProperties: {
          //开始时间结束时间
          Playback1: new Date(0, 0, 0, 0, 0),
          Playback2: new Date(0, 0, 0, 0, 0)
        }
      },
      dropdownData: {
        // 下拉菜单数据
        enter: [
          //入场特效
          { name: "无", value: "None" },
          { name: "淡入", value: "ALPHA_IN" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "放大", value: "ZOOM_IN" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角放大", value: "ZOOM_IN_LEFT_TOP" },
          { name: "左下角放大", value: "ZOOM_IN_LEFT_BOTTOM" },
          { name: "右上角放大", value: "ZOOM_IN_RIGHT_TOP" },
          { name: "右下角放大", value: "ZOOM_IN_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" }
        ],
        Out: [
          //出场特效
          { name: "无", value: "None" },
          { name: "淡出", value: "ALPHA_OUT" },
          { name: "连续左移", value: "MOVING_LEFT" },
          { name: "连续右移", value: "MOVING_RIGHT" },
          { name: "连续上移", value: "MOVING_TOP" },
          { name: "连续下移", value: "MOVING_BOTTOM" },
          { name: "缩小", value: "ZOOM_OUT" },
          { name: "向右旋转", value: "ROTATE_RIGHT" },
          { name: "向左旋转", value: "ROTATE_LEFT" },
          { name: "左上角缩小", value: "ZOOM_OUT_LEFT_TOP" },
          { name: "左下角缩小", value: "ZOOM_OUT_LEFT_BOTTOM" },
          { name: "右上角缩小", value: "ZOOM_OUT_RIGHT_TOP" },
          { name: "右下角缩小", value: "ZOOM_OUT_RIGHT_BOTTOM" },
          { name: "随机", value: "RANDOM" }
        ],
        select: {
          enter: "无", //选中的特效
          Out: "无",
          enterDeta: "", //出入场时间
          OutDeta: "",
          lineH: "", //行高
          speed: "" //速度
        }
      }
    };
  },
  watch: {
    proDataCopy: {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          if (newVal.left != oldVal.left || newVal.top != oldVal.top
              || newVal.width != oldVal.width || newVal.height != oldVal.height) {
                program.getThumbnail();
          }
        }
      },
      deep: true,
      immediate: true,
    },
    program: {
      //监听的值
      handler(newName, oldName) {
        this.programData = program;
      },
      immediate: true
    }
  },
  computed: {
    proDataCopy: {
      get() {
        return JSON.parse(JSON.stringify(this.proData))
      }
    }
  },
  mounted() {},
  methods: {
    ChangeDuration(e) {
      this.programData.ALLBoxData[this.programData.selectBox].inputDuration = e;
    },
    //   weatherStart(e){
    //  this.programData.setWeathertime( e,1)
    // },
    // weatherContinue(e){
    //        this.programData.setWeathertime( e,2)
    // },
    handleChange() {
      program.setSimulation();
    },
    colorstyleS() {
      this.handleChange();
    },
    colorstylF() {
      this.handleChange();
    },
    colorstyleZ() {
      this.handleChange();
    },
    colorstyleFZ() {
      this.handleChange();
    },
    colorstyleM() {
      this.handleChange();
    },
    colorstyleB() {
      this.handleChange();
    }
  }
};
</script>

<style scoped>
.numberMini {
  width: 100px;
  height: 32px;
  margin-left: 5px;
  margin-top: 5px;
}
.block {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 4px 0;
  display: block;
}
.demonstration {
  /*     // display: block; */
  float: left;
  width: 120px;
  height: 40px;
  line-height: 40px;
}
.el-color-picker {
  height: 32px;
}
#wangEditor2 {
  width: 100%;
  height: 118px;
  padding-bottom: 5px;
}
</style>
