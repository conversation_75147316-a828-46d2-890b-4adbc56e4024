<template>
  <div class="LeftSurface">
    <div class="returnInfo">
      <el-button type="info" icon="el-icon-arrow-left" size="small" circle @click="back()"
        style="position:absolute;left:5px;top:10px;"></el-button>
      <div style="font-size:13px; font-weight:500;line-height:55px">{{ programData.canvasName }}</div>
      <el-button type="info" icon="el-icon-s-tools" size="small" circle style="position:absolute;right:13px;top:10px;"
        @click="setPxVsbl = true"></el-button>
    </div>
    <div class="toolbar">
      <div class="control" :title="$t('common.add')" @click="addItem">
        <i class="el-icon-plus"></i>
      </div>
      <div class="control" :title="$t('common.copy')" @click="copyItem">
        <i class="el-icon-document-copy"></i>
      </div>
      <div :title="$t('common.delete')" v-if="programData.CurrentPageAll.length == 1" class='isCursor'>
        <i class="el-icon-delete"></i>
      </div>
      <div class="control" v-else :title="$t('common.delete')" @click="deleteItem">
        <i class="el-icon-delete"></i>
      </div>
      <div class="isCursor" :title="$t('program.up')" v-if="programData.currentPage == 0">
        <i class="el-icon-upload2"></i>
      </div>
      <div class="control" :title="$t('program.up')" @click="upItem" v-else>
        <i class="el-icon-upload2"></i>
      </div>
      <div class="isCursor" :title="$t('program.down')"
        v-if="programData.currentPage == (programData.CurrentPageAll.length - 1)">
        <i class="el-icon-download"></i>
      </div>
      <div class="control" :title="$t('program.down')" @click="downItem" v-else>
        <i class="el-icon-download"></i>
      </div>
    </div>
    <div class="editList" v-if="programData.CurrentPageAll.length > 0">
      <!-- {{programData.image_url}} -->
      <div class="sortable_box"  v-for="(item, index) in programData.CurrentPageAll" @click="clickPage($event,index)"
        :key="index" :class="index == programData.currentPage ? 'sekect' : 'sortable_box'">
        <p class="list_PO">{{ index + 1 }}</p>
        <div class="list_view" :id="'list_view' + index" :style="{'width':viewWidth + 'px', 'height': viewHeight + 'px'}">
          <canvas class="listViewCanvas" :width="viewWidth" :height="viewHeight">
            您的浏览器不支持 HTML5 canvas 标签。
          </canvas>
        </div>
        <!-- <div class="list_view" v-if="programData.image_url.length > 0">
          !-- {{index}} --
          <img style="height: 70px;width:70px" :src="programData.image_url[index]"/>
        </div>
        <div class="list_view" v-else>
          <div style="height: 70px;width:70px;background-color: black;"></div>
        </div> -->
        <p class="list_PT">{{ $t('program.play') }} {{ item.PlaybackTimes }} {{ $t('program.times') }}</p>
      </div>
    </div>
    <el-dialog :title="$t('program.ProgramInfo')" :visible.sync="setPxVsbl" width="650px">
      <el-form label-width="90px">
        <el-form-item :label="$t('task.name')" style="text-align: left;">
          <el-input v-model="canvasName" :placeholder="$t('program.PleaseEnterContent')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('cardDevice.resolvingPower')" style="text-align: left;">
          <span>{{ $t('cardDevice.width') }}</span>
          <template>
            <el-input-number :max="70000" :min="1"  size="mini" v-model="canvasWidth" style="margin-left:10px;"></el-input-number>
          </template>
          <span style="margin-left:8px;">{{ $t('cardDevice.height') }}</span>
          <template style="margin-left:5px;">
            <el-input-number  :max="10000" :min="1"  size="mini" v-model="canvasHeight" style="margin-left:10px;"></el-input-number>
          </template>
        </el-form-item>
        <el-form-item :label="$t('menu.type')" style="text-align: left;">
          <el-radio-group v-model="insert" class="ml-4">
            <el-radio :label="0" size="large">{{$t('program.ordinaryProgram')}}</el-radio>
            <el-radio :label="1" size="large">{{$t('program.insertProgram')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('program.isDiscount')" style="text-align: left;">
          <el-switch v-model="isDiscount" @change="changeDiscount"></el-switch>
        </el-form-item>
        <el-form-item v-if="isDiscount" :label="$t('program.discountMode')" style="text-align: left;">
          <el-radio-group v-model="discountModel" size="small" is-button>
            <el-radio-button label="width">{{$t("program.level")}}</el-radio-button>
            <el-radio-button label="height">{{$t("program.vertical")}}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="isDiscount" :label="$t('program.discount')" style="text-align: left;">
          <el-input v-model="discount" :placeholder="$t('program.discountText')"></el-input>
        </el-form-item>
      </el-form>
      <!-- <div>
        <span>{{ $t('program.name') }}</span>
        <el-input v-model="canvasName" :placeholder="$t('program.PleaseEnterContent')"
          style="width:350px;margin-left:10px;"></el-input>
      </div>
      <div style="margin-top:10px;">
        <span>{{ $t('program.type') }}</span>
        <el-radio-group v-model="insert" class="ml-4">
          <el-radio :label="0" size="large">{{$t('program.ordinaryProgram')}}</el-radio>
          <el-radio :label="1" size="large">{{$t('program.insertProgram')}}</el-radio>
        </el-radio-group>
      </div>
      <div style="margin-top:10px;">
        <span>{{ $t('cardDevice.resolvingPower') }}</span>
        <span style="margin-left:10px;">{{ $t('cardDevice.width') }}</span>
        <template>
          <el-input-number :max="3840" :min="1"  size="mini" v-model="canvasWidth" style="margin-left:15px;"></el-input-number>
        </template>
        <span style="margin-left:10px;">{{ $t('cardDevice.height') }}</span>
        <template style="margin-left:5px;">
          <el-input-number  :max="2560" :min="1"  size="mini" v-model="canvasHeight" style="margin-left:15px;"></el-input-number>
        </template>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="setPxVsbl = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" size="small"
          :disabled="!(canvasWidth && canvasHeight && canvasName && programData.CurrentPageAll.length > 0)"
          @click="currentGroup">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import program from "./program.js"
export default {
  data() {
    return {
      editListData: [
        {
          frequency: "1",//播放次数
        }
      ],
      current: 0,//选中列表中的第几个
      setPxVsbl: false,
      programData: {},
      canvasWidth: Number,  //临时使用中间件 点击确定更改后启用
      canvasHeight: Number,
      canvasName: "",
      backRouter: "",
      viewWidth: Number,
      viewHeight: Number,
      insert: 0,
      isDiscount: false,
      discount: "",
      discountModel: "width"
    }
  },
  mounted() {

    // program.getThumbnail();
    let addFrom = this.$route.params.addFrom
    let backRouter = this.$route.params.backRouter
    if (backRouter) {
      this.backRouter = backRouter
    }
    this.canvasWidth = program.canvasWidth
    this.canvasHeight = program.canvasHeight
    this.canvasName = program.canvasName
    this.isDiscount = program.isDiscount
    this.discount = program.discount
    this.discountModel = program.discountModel
    this.insert = program.insert
    this.programData.loadingSave = false
    this.programData.loadingSaveAndExit = false
    if (addFrom) {
      if (addFrom.canvasWidth != undefined) {
        this.canvasWidth = addFrom.canvasWidth
        this.programData.canvasWidth = this.canvasWidth
      }

      if (addFrom.canvasHeight != undefined) {
        this.canvasHeight = addFrom.canvasHeight
        this.programData.canvasHeight = this.canvasHeight
      }

      if (addFrom.canvasName != undefined) {
        this.canvasName = addFrom.canvasName
        this.programData.canvasName = this.canvasName
      }

      if (addFrom.insert != undefined) {
        this.insert = addFrom.insert
        this.programData.insert = this.insert
      }

      if (addFrom.isDiscount != undefined) {
        this.isDiscount = addFrom.isDiscount
        this.programData.isDiscount = this.isDiscount
      }
      if (addFrom.discountModel != undefined) {
        this.discountModel = addFrom.discountModel
        this.programData.discountModel = this.discountModel
      }
      if (addFrom.discount != undefined) {
        this.discount = addFrom.discount
        this.programData.discount = this.discount
      }
    }
    var res = program.calculateCanvasSize(150, 80)
    this.viewWidth = res.width
    this.viewHeight = res.height
  },
  watch: {
    program: { //监听的对象  this.programData指向program
      handler() {
        this.programData = program
      },
      immediate: true,
    }
  },
  methods: {
    changeDiscount(status) {
      this.isDiscount = status
      if (status == false) {
        this.discount = ""
        this.programData.discount = this.discount
      }
      this.programData.isDiscount = this.isDiscount
    },
    clickPage($event,index){
      program.clickPage($event,index)
    },
    back() {
      program.clearAllDom()
      if (this.backRouter) {
        var subMenuActiveName = this.backRouter === 'screen-program' ? '节目管理' : '设备管理'
        this.$router.push({ name: this.backRouter, params: { subMenuActiveName: subMenuActiveName, menuActiveName: '智慧屏幕' } })
      } else {
        this.$router.push({ name: 'screen-program', params: { subMenuActiveName: '节目管理', menuActiveName: '智慧屏幕' } })
      }
      program.emptyDom()
    },
    addItem() { //添加一项
      program.addPAage()
    },
    copyItem() { //复制选中一项
      program.copyPage()
    },
    deleteItem() { //删除选中一项
      program.deletePage()
    },
    upItem() {  //向上移动选中一项
      program.upItemPage()
    },
    downItem() { //向下移动选中一项
      program.downPage()
    },
    currentGroup() { //点击确定更改宽高name
      if (this.isDiscount) {
        // 如果开启打折必须输入打折宽度
        if (!this.discount) {
          this.$message({
            message: this.$t('program.PleaseEnterDiscountWidth'),
            type: 'warning'
          });
          return
        } else {
          var split = this.discount.split(",")
          var reg = /^\d+(?=\.{0,1}\d+$|$)/
          var flag = false
          var total = 0
          for (let i = 0; i < split.length; i++) {
            const element = split[i];
            if (!reg.test(element)) {
              flag = true
            }
            total += parseFloat(element)
          }
          if (flag) {
            this.$message({
              message: this.$t('program.PleaseEnterTheCorrectContentFormat'),
              type: 'warning'
            });
            return
          }
          if (this.discountModel == "width") {
            if (total > this.canvasWidth) {
              this.$message({
                message: this.$t('program.totalWidthDiscountCannotWidth'),
                type: 'warning'
              });
              return
            }
          } else {
            if (total > this.canvasHeight) {
              this.$message({
                message: this.$t('program.totalWidthDiscountCannotWidth'),
                type: 'warning'
              });
              return
            }
          }

        }
      }
      this.setPxVsbl = false
      program.canvasWidth = this.canvasWidth
      program.canvasHeight = this.canvasHeight
      program.canvasName = this.canvasName
      program.insert = this.insert
      program.isDiscount = this.isDiscount
      program.discount = this.discount
      program.discountModel = this.discountModel
      var res = program.calculateCanvasSize(150, 80)
      this.viewWidth = res.width
      this.viewHeight = res.height
    },
  }
}
</script>

<style scoped>
.LeftSurface {
  width: 272px;
  border-bottom: #dce3f3 solid 1px;
  text-align: center;
}

.returnInfo {
  width: 100%;
  height: 65px;
  position: relative;
  border-bottom: #dce3f3 solid 1px;
}

.toolbar {
  width: 100%;
  height: 45px;
  border-bottom: #dce3f3 solid 1px;
  display: flex;
  justify-content: space-around;
}

.control {
  width: 30px;
  height: 30px;
  margin-top: 5px;
  border-radius: 3px;
  background: #fff;
  color: #333;
  font-size: 20px;
  line-height: 30px;
  border: 1px solid #dce3f3;
  cursor: pointer;
}

.control:hover {
  border: 1px solid #68b5fc;
}

.isCursor {
  width: 30px;
  height: 30px;
  margin-top: 5px;
  border-radius: 3px;
  background: #fff;
  color: #333;
  font-size: 20px;
  line-height: 30px;
  border: 1px solid #dce3f3;
  cursor: not-allowed;
}

.editList {
  overflow: auto;
  height: calc(100% - 101px);
}

/* //修改overflow滚动条 外边框 */
.editList::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: rgba(240, 240, 240, 1);
  ;
}

/* //修改overflow滚动条  滚动条 */
.editList::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  background-color: #aeb7cf;
}

.sekect {
  background: #68b5fc;
  color: #fff;
}

.sortable_box {
  width: 100%;
  height: 90px;
  border-bottom: 1px solid #dce3f3;
  display: flex;
  justify-content: space-around;
  cursor: pointer;
}

.list_PO {
  line-height: 90px;
}

.list_view {
  width: 150px;
  height: 80px;
  background: #000;
  /* border: #fff solid 1px; */
  margin-top: 5px;
  text-align: left;
}

.list_PT {
  width: 70px;
  line-height: 90px;
  font-size: 13px;
}
</style>
