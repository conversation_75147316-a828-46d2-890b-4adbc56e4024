"use strict";

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _jQuery = _interopRequireWildcard(require("jQuery"));

var _httpRequest = _interopRequireDefault(require("@/utils/httpRequest"));

var _viewDesign = require("view-design");

var _router = _interopRequireDefault(require("@/router"));

var _axios = _interopRequireDefault(require("axios"));

var _main = _interopRequireDefault(require("@/main"));

var _domToImageMore = _interopRequireDefault(require("dom-to-image-more"));

var _flv = _interopRequireDefault(require("flv.js"));

var _audio = _interopRequireDefault(require("@/assets/img/audio.png"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { "default": obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj["default"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance"); }

function _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }

Date.prototype.format = function (fmt) {
  var o = {
    "M+": this.getMonth() + 1,
    //月份
    "d+": this.getDate(),
    //日
    "h+": this.getHours(),
    //小时
    "m+": this.getMinutes(),
    //分
    "s+": this.getSeconds(),
    //秒
    "q+": Math.floor((this.getMonth() + 3) / 3),
    //季度
    "S": this.getMilliseconds() //毫秒

  };

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }

  return fmt;
};
/*
* 重写时间的toJSON方法，因为在调用JSON.stringify的时候，时间转换就调用的toJSON，这样会导致少8个小时，所以重写它的toJSON方法
*/


Date.prototype.toJSON = function () {
  return this.format("yyyy-MM-dd hh:mm:ss"); // util.formatDate是自定义的个时间格式化函数
};
/**
 * 实现全局替换
 * @param {*} s1 
 * @param {*} s2 
 * @returns 
 */


String.prototype.replaceAll = function (s1, s2) {
  return this.replace(new RegExp(s1, "gm"), s2);
};

var textIndex = 0; // 文本索引

var multTextIndex = 100; // 多窗口文本索引

var streamIndex = 0; // 流媒体索引

var htmlIndex = 0; // 网页索引

var _default = {
  loadingSave: false,
  // 等待保存
  loadingSaveAndExit: false,
  // 等待保存并退出
  isView: false,
  // 是否预览模式
  infoId: 0,
  // 详情Id
  programId: "",
  //修改用到的设备id
  sourceFileIds: "",
  //修改用到的媒体id
  assemblyProp: '',
  //  组件属性按需展示不同页面 其中media为媒体    text为文本  DigitalClock为数字时钟  environmental为环境监测 weather为天气  simulationClock为模拟时钟   multiWindow为多素材窗口
  // 编辑框宽高
  canvasWidth: 512,
  canvasHeight: 512,
  attributeNav: "page",
  //page显示的为页面属性 assembly为组件属性
  canvasName: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
  ALLBoxData: [],
  //页面所有元素的数据
  ALLBoxDataParent: [[]],
  //所有页面元素的数据
  windowPage: 0,
  //多素材窗口选中的元素下标
  CurrentPageAll: [//所有页面属性 multiWindow
  {
    isEffective: false,
    StartDate: new Date(),
    // EndDate:new Date().setDate(new Date().getDate()+30),
    EndDate: new Date(),
    PlaybackTimes: 1,
    DayOfWeekPlay: [],
    name: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds()
  }],
  currentPage: 0,
  //选中的页面
  selectBox: 0,
  //选中的素材下标
  deviationX: 0,
  //重复添加X轴偏移计数
  deviationY: 0,
  //重复添加Y轴偏移计数
  maxHierarchy: [0],
  //最大层级
  weatherHtml: '',
  //天气的数据
  weatherCity: '',
  //天气的城市
  weatherCode: '',
  //天气的编码
  sortXB: [[]],
  sortIndexXB: [0],
  textstatic: 0,
  flvPlayer: null,

  /**
   * 添加一个可拖拽的dom元素
   * @param {*} item 修改时传入具体的数据
   * @param {*} isSetData
   * @param {*} media
   */
  addDom: function addDom(item, isSetData, media) {
    var _this = this;

    var then, html, currentDomIndex, self, type, domObj, ZboxWidth, ZboxHeight, wepL, wepT, _html, imgWidthAndHeight, _currentDomIndex, longWidth, simulationWidth, simulationHeight, textStyle;

    return regeneratorRuntime.async(function addDom$(_context) {
      while (1) {
        switch (_context.prev = _context.next) {
          case 0:
            then = this;
            this.attributeNav = 'assembly'; //right区域展示组件

            if (!(item && isSetData == false)) {
              _context.next = 71;
              break;
            }

            if (!(item.type == "media")) {
              _context.next = 14;
              break;
            }

            // 媒体
            this.assemblyProp = "media";
            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'></div>")); //添加拖拽框

            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            html = "";

            if (item.media.suffix == 'mp4') {
              html = "<div class=\"media\">\n                                <video src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + item.media.fileId, "\"\n                                    style='object-fit:fill;width:100%;height:100%;' autoplay loop muted>\n                                </video>\n                            </div>");
            }

            if (item.media.suffix == 'jpeg' || item.media.suffix == 'png' || item.media.suffix == 'jfif' || item.media.suffix == 'jpg' || item.media.suffix == 'gif') {
              html = "<div class=\"media\">\n                                <img class=\"media\" src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + item.media.fileId, "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                            </div>");
            }

            if (item.media.suffix == 'mp3') {
              html = "<div class=\"media\">\n                                <img class=\"media\" src=\"".concat(_audio["default"], "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                            </div>");
            }

            (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).append(html);
            _context.next = 69;
            break;

          case 14:
            if (!(item.type == "DigitalClock")) {
              _context.next = 26;
              break;
            }

            // 数字时钟
            this.assemblyProp = "DigitalClock";

            if ((0, _jQuery["default"])('.mediaTZbox').length > 0) {
              currentDomIndex = (0, _jQuery["default"])('.mediaTZbox').length;
            } else {
              currentDomIndex = 0;
            }

            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'>\n                                            <div class='DigitalClock' style=\"width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;user-select:none;\">\n                                                <div class='digitalClockDiv' style=\"user-select:none;\">\n                                                    <span>\n                                                        <div style=\"display:inherit;\">").concat(then.getNYR("n", currentDomIndex), "</div>\n                                                        <div style=\"display:inherit;\"></div>\n                                                    </span><b>/</b>\n                                                    <span>").concat(then.getNYR("y", currentDomIndex), "</span><b>/</b>\n                                                    <span>").concat(then.getNYR("r", currentDomIndex), "</span>\n                                                </div>\n                                                <div class='digitalClockDiv' style=\"user-select:none;\">\n                                                    <span style=\"white-space:nowrap;line-height:1;\">").concat(then.getDay(currentDomIndex), "</span>\n                                                </div>\n                                                <div class='digitalClockDiv' style=\"user-select:none;\">\n                                                    <i class=\"isVcbl\" style=\"display:none;font-style:normal;\">\n                                                        <i class=\"SW\" style=\"font-style:normal;\">\u4E0A\u5348</i>\n                                                        <i class=\"XW\" style=\"font-style:normal;\">\u4E0B\u5348</i>&nbsp;\n                                                    </i>\n                                                    <span>").concat(then.getSFM("s", currentDomIndex), "</span><b>:</b>\n                                                    <span>").concat(then.getSFM("f", currentDomIndex), "</span><b>:</b>\n                                                    <span>").concat(then.getSFM("m", currentDomIndex), "</span>\n                                                </div>\n                                            </div>\n                                        </div>"));
            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            this.setNumberTime(false);
            this.setSingleLine(false);
            this.setTextStyle(false);
            this.setSizeDX(false);
            this.setDigitalClockColor(false);
            _context.next = 69;
            break;

          case 26:
            if (!(item.type == "environmental")) {
              _context.next = 37;
              break;
            }

            // 环境检测
            this.assemblyProp = "environmental";
            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'>\n                                            <div style=\"width:100%;height:100%;overflow: hidden;position:relative;\">\n                                                <div class=\"assemblyPropDisplay\" style=\"font-size:").concat(item.fontSize, "px;\">\n                                                    <div class=\"assemblyPropDisplayDiv\" style=\"display:none;\">\n                                                        <p>").concat(item.assePropData.title, "</p>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.temperature, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">\n                                                            0<span>\u2103</span><span style=\"display:none;\">\u2109</span>\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.humidity, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0RH</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.noise, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0dB</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.windSpeed, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0m/s</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.windDirection, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.Pm10, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(item.assePropData.Pm25, ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>")); //添加拖拽框

            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            this.setAlignPosition(false);
            this.setTemperatureNum(false);
            this.setEnvironmentalFontSize(false);
            this.setEnvironmentalColor(false);
            this.setEnvironmentalTextStyle(false);
            _context.next = 69;
            break;

          case 37:
            if (!(item.type == "Weather")) {
              _context.next = 45;
              break;
            }

            //天气
            this.assemblyProp = "weather";
            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'>\n                                            <div class='weatherDisplay'  style=\"width:100%;height:100%;\"></div>\n                                        </div>"));
            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            this.setWeather(false);
            this.monitorWeatherSize(false);
            _context.next = 69;
            break;

          case 45:
            if (!(item.type == "streaming")) {
              _context.next = 51;
              break;
            }

            //流媒体
            this.assemblyProp = "streaming";
            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'>\n                                            <div class='streamingDisplay' style=\"width:100%;height:100%;\">\n                                                <video id = \"streaming").concat(item.streamIndex, "\"  autoplay muted width=\"100%\"  height=\"100%\"></video>\n                                            </div>\n                                        </div>"));
            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            _context.next = 69;
            break;

          case 51:
            if (!(item.type == "simulationClock")) {
              _context.next = 58;
              break;
            }

            //模拟时钟
            this.assemblyProp = "simulationClock";
            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='text-align:center;width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'>\n                                            <div class='simulation'></div>\n                                        </div>"));
            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            this.setSimulation(item.width, item.height, false);
            _context.next = 69;
            break;

          case 58:
            if (!(item.type == "text")) {
              _context.next = 68;
              break;
            }

            //文本
            this.assemblyProp = "text";
            (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;overflow: hidden;'>\n                                            <div class='").concat(item.curchange, "   ").concat(item.selectText, "' id='textbigbox' style=\"font-smoothing:none;width:100%;height:100%;position:relative;overflow: hidden;\">\n                                                <div class=\"textbig\" id = \"scrolltext").concat(item.selectText, "\" style=\"font-smoothing:none;position:absolute;\"></div>\n                                            </div>\n                                        </div>"));
            this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            self = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            (0, _jQuery["default"])((0, _jQuery["default"])("body:eq(0)")).css({
              "overflow": "visible"
            });
            _context.next = 66;
            return regeneratorRuntime.awrap(this.textFormat(item.textRadio, self, false));

          case 66:
            _context.next = 69;
            break;

          case 68:
            if (item.type == "multiWindow") {
              // 多窗口素材
              this.assemblyProp = "multiWindow";
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;'>\n                                            <div class=\"multiWindow\" style=\"width:100%;height:100%;\">\n                                                <p style=\"color:#878787;\">").concat(_main["default"].$t('program.Multi-material_text'), "</p>\n                                            </div>\n                                        </div>")); //添加拖拽框

              this.selectBox = (0, _jQuery["default"])(".mediaTZbox").length - 1;
            } else if (item.type == "WebURL") {
              //网页
              this.assemblyProp = "WebURL";
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(item.width, "px;height:").concat(item.height, "px;top:").concat(item.top, "px;left:").concat(item.left, "px;z-index:").concat(item.z_index, ";user-select:none;overflow: hidden;'>\n                                            <div class = \"htmlbox\" id = \"sethtml").concat(item.htmlIndex, "\"></div>\n                                        </div>"));
            } else if (item.type == "textTemplate") {//文字模版
              // TODO
            }

          case 69:
            _context.next = 103;
            break;

          case 71:
            //没有值就是新增
            type = ""; // 节目类型

            domObj = {};
            ZboxWidth = '';
            ZboxHeight = '';
            parseInt(this.canvasWidth) < 300 ? ZboxWidth = this.canvasWidth : ZboxWidth = 300;
            parseInt(this.canvasHeight) < 100 ? ZboxHeight = this.canvasHeight : ZboxHeight = 100;
            this.canvasWidth < 500 ? wepL = 0 : wepL = this.deviationX * 10;
            this.canvasHeight < 200 ? wepT = 0 : wepT = this.deviationY * 10;

            if (this.canvasWidth <= ZboxWidth + wepL) {
              wepL = 0;
              this.deviationX = 0;
            }

            if (this.canvasHeight <= ZboxHeight + wepT) {
              wepT = 0;
              this.deviationY = 0;
            }

            if (this.assemblyProp == "media") {
              // 媒体文件
              domObj.media = media;
              type = "media";
              _html = "";

              if (media.duration) {
                domObj.PlaybackDuration = media.duration;
              } else {
                domObj.PlaybackDuration = 10;
              }

              if (media.suffix == 'mp4') {
                _html = "<div class=\"mediaTZbox\" style='width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                <div class=\"media\">\n                                    <video src=\"").concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + media.fileId, "\"\n                                        style='object-fit:fill;width:100%;height:100%;' autoplay loop muted >\n                                    </video>\n                                </div>\n                            </div>");
              } else if (media.suffix == 'jpeg' || media.suffix == 'png' || media.suffix == 'jfif' || media.suffix == 'jpg' || media.suffix == 'gif') {
                if (media.height && media.width) {
                  imgWidthAndHeight = this.calculateWidthAndHeight(media.width, media.height);
                  ZboxHeight = imgWidthAndHeight.height;
                  ZboxWidth = imgWidthAndHeight.width;

                  if (this.canvasHeight <= ZboxHeight + wepT) {
                    wepT = 0;
                  }

                  if (this.canvasWidth <= ZboxWidth + wepL) {
                    wepL = 0;
                  }
                }

                _html = "<div class=\"mediaTZbox\" style='width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                <div class=\"media\">\n                                    <img src=\"").concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + media.fileId, "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                                </div>\n                            </div>");
              } else if (media.suffix == 'mp3') {
                imgWidthAndHeight = this.calculateWidthAndHeight(300, 300);
                ZboxHeight = imgWidthAndHeight.height;
                ZboxWidth = imgWidthAndHeight.width;

                if (this.canvasHeight <= ZboxHeight + wepT) {
                  wepT = 0;
                }

                if (this.canvasWidth <= ZboxWidth + wepL) {
                  wepL = 0;
                }

                _html = "<div class=\"mediaTZbox\" style='width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                <div class=\"media\">\n                                    <img src=\"").concat(_audio["default"], "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                                </div>\n                            </div>");
              }

              (0, _jQuery["default"])(".canvasBox").append(_html); //添加拖拽框
            } else if (this.assemblyProp == "text") {
              // 文本文件
              domObj.curchange = "static";
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;\n                                            z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;overflow: hidden'>\n                                            <div  class='").concat(domObj.curchange, "   ").concat(textIndex, "' id='textbigbox'  style=\"font-smoothing:none;width:100%;height:100%;position:relative;overflow: hidden;\">\n                                                <div id=\"").concat(textIndex, "\" style=\"width: 100%; height: 100%;\">\n                                                    <div class=\"textbig\"  id = \"scrolltext").concat(textIndex, "\"  style=\"font-smoothing:none;position:absolute;\"></div>\n                                                </div>\n                                            </div>\n                                        </div>"));
              setTimeout(function () {
                window.editor.setHtml('');
              }, 200);
              (0, _jQuery["default"])((0, _jQuery["default"])("body:eq(0)")).css({
                "overflow": "visible"
              });
              type = "text";
              domObj.selectText = textIndex;
              domObj.textSpeed = "10";
              domObj.effect = "right to left";
              domObj.textRadio = "1";
              domObj.showWndImage = [];
              domObj.ImagePage = '1';
              domObj.PageArrSum = [];
              domObj.base64Page = '';
              domObj.isShow = true;
              domObj.runSpeedShow = false;
              domObj.runPageShow = false;
              domObj.preview = false;
              textIndex++;
            } else if (this.assemblyProp == "DigitalClock") {
              //数字时钟
              if ((0, _jQuery["default"])('.mediaTZbox').length > 0) {
                _currentDomIndex = (0, _jQuery["default"])('.mediaTZbox').length;
              } else {
                _currentDomIndex = 0;
              }

              type = "DigitalClock";
              domObj.numberTime = ["年", "月", "日", "时", "分", "秒", "星期", "四位年", "時", "Year", "Month", "Day", "Hour", "Minute", "Second", "Week", "SiWei years"]; // domObj.numberTime = ["年", "月", "日", "时", "分", "秒", "星期", "四位年", "12小时制", "上午/下午", "時", "12小時制", "Year", "Month", "Day", "Hour", "Minute", "Second", "Week", "SiWei years", "12 hours system", "AM/PM"]

              domObj.timeData_sj = ["HH:MM:SS", "H:MM:SS"];
              domObj.timeData_rq = ["1970/01/02", "1970/1/2", "1/2/1970", "01/02/1970", "1970年/01月/02日", "1970年/1月/2日"];
              domObj.Sjfg = "HH:MM:SS";
              domObj.Rqfg = "1970/01/02";
              domObj.isSingleLine = "2";
              domObj.SQnum = "8";
              domObj.PlaybackDuration = "10";
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                            <div class='DigitalClock' style=\"width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;user-select:none;font-size:18px;\">\n                                                <div class='digitalClockDiv' style=\"user-select:none;\">\n                                                    <span>\n                                                        <div style=\"display:inherit;\">").concat(then.getNYR("n", _currentDomIndex), "</div>\n                                                        <div style=\"display:inherit;\"></div>\n                                                    </span><b>/</b>\n                                                    <span>").concat(then.getNYR("y", _currentDomIndex), "</span><b>/</b>\n                                                    <span>").concat(then.getNYR("r", _currentDomIndex), "</span>\n                                                </div>\n                                                <div class='digitalClockDiv' style=\"user-select:none;\">\n                                                    <span style=\"white-space:nowrap;line-height:1;\">").concat(then.getDay(_currentDomIndex), "</span>\n                                                </div>\n                                                <div class='digitalClockDiv' style=\"user-select:none;\">\n                                                    <i class=\"isVcbl\" style=\"display:none;font-style:normal;\">\n                                                        <i class=\"SW\" style=\"font-style:normal;\">\u4E0A\u5348</i>\n                                                        <i class=\"XW\" style=\"font-style:normal;\">\u4E0B\u5348</i>&nbsp;\n                                                    </i>\n                                                        <span>").concat(then.getSFM('s', _currentDomIndex), "</span><b>:</b>\n                                                        <span>").concat(then.getSFM("f", _currentDomIndex), "</span><b>:</b>\n                                                        <span>").concat(then.getSFM("m", _currentDomIndex), "</span>\n                                                </div>\n                                            </div>\n                                        </div>"));
              domObj.SJitv0 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", _currentDomIndex)));
              }, 1000);
              domObj.SJitv2 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(0)").find("span:eq(1)").text("".concat(then.getNYR("y", _currentDomIndex)));
              }, 1000);
              domObj.SJitv3 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(0)").find("span:eq(2)").text("".concat(then.getNYR("r", _currentDomIndex)));
              }, 1000);
              domObj.SJitv4 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(4)").find("span:eq(0)").text("".concat(then.getSFM("s", _currentDomIndex)));
              }, 100);
              domObj.SJitv5 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(4)").find("span:eq(1)").text("".concat(then.getSFM("f", _currentDomIndex)));
              }, 100);
              domObj.SJitv6 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(4)").find("span:eq(2)").text("".concat(then.getSFM("m", _currentDomIndex)));
              }, 100);
              domObj.SJitv7 = setInterval(function () {
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_currentDomIndex]).find(".DigitalClock").find("div:eq(3)").find("span:eq(0)").text("".concat(then.getDay(_currentDomIndex)));
              }, 1000);
            } else if (this.assemblyProp == "environmental") {
              //环境监测
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;\n                                            z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                            <div style=\"width:100%;height:100%;overflow: hidden;position:relative;\">\n                                                <div class=\"assemblyPropDisplay\" style=\"font-size:18px;\">\n                                                    <div class=\"assemblyPropDisplayDiv\" style=\"display:none;\">\n                                                        <p></p>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('cardDevice.temperature'), ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">\n                                                            0<span>\u2103</span><span style=\"display:none;\">\u2109</span>\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('card.humidity'), ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0RH</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('program.noise'), ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0dB</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('program.windSpeed'), ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0m/s</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('program.windDirection'), ":</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">PM10:</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                                    </div>\n                                                    <div class=\"assemblyPropDisplayDiv\">\n                                                        <span style=\"display: inline-block; text-align: right;\">PM25:</span>\n                                                        <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>")); // 将所有字的宽度设置为最宽字体宽度

              longWidth = this.findEnvironmentalLongWidth();
              this.setEnvironmentalWidth(longWidth);
              type = "environmental";
              domObj.assePropData = {
                title: " ",
                temperatureNum: "1",
                temperature: _main["default"].$t('cardDevice.temperature'),
                temperatureBC: "",
                humidity: _main["default"].$t('card.humidity'),
                windSpeed: _main["default"].$t('program.windSpeed'),
                windDirection: _main["default"].$t('program.windDirection'),
                noise: _main["default"].$t('program.noise'),
                Pm10: "PM10",
                Pm25: "PM2.5",
                position: "2",
                RollingSpeed: "10",
                RefreshZQ: "10",
                playSC: "10",
                display: {
                  '标题': false,
                  '温度': true,
                  '湿度': true,
                  '风速': true,
                  '风向': true,
                  '噪音': true,
                  'PM2.5': true,
                  'PM10': true
                }
              };
              domObj.RefreshCycle = "10";
              domObj.PlaybackDuration = "10";
            } else if (this.assemblyProp == "weather") {
              //天气
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy, ";user-select:none;'>\n                                            <div class='weatherDisplay' style=\"width:100%;height:100%;\"> </div>\n                                        </div>"));
              type = "Weather";
              domObj.province = "";
              domObj.valueWeather = [];
              domObj.saveWeather = {};
            } else if (this.assemblyProp == "streaming") {
              //流媒体
              domObj.streamingUrl = 'http://192.168.1.135:8888/live?port=1935&app=live&stream=8C045BAPAN2DE80';
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy, ";user-select:none;'>\n                                            <div class='streamingDisplay' style=\"width:100%;height:100%;\">\n                                                <video id = \"streaming").concat(streamIndex, "\"  autoplay muted width=\"100%\"  height=\"100%\"></video>\n                                            </div>\n                                        </div>"));
              type = "streaming";
              domObj.streamingName = "LiveVivo";
              domObj.streamingtype = "LiveVivo";
              domObj.streamIndex = streamIndex;
              streamIndex++;
            } else if (this.assemblyProp == "simulationClock") {
              //模拟时钟
              // this.selectBox = this.ALLBoxData.length
              // console.log(this.ALLBoxData[this.selectBox].width);
              simulationWidth = 150;
              simulationHeight = 150;

              if (this.canvasHeight < 300 || this.canvasWidth < 300) {
                if (ZboxWidth > ZboxHeight) {
                  (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;text-align:center;width:".concat(ZboxHeight, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                                    <div class='simulation'></div>\n                                                </div>"));
                  simulationWidth = ZboxHeight;
                  simulationHeight = ZboxHeight;
                } else {
                  (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;text-align:center;width:".concat(ZboxWidth, "px;height:").concat(ZboxWidth, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                                    <div class='simulation'></div>\n                                                </div>"));
                  simulationWidth = ZboxWidth;
                  simulationHeight = ZboxWidth;
                }
              } else {
                (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;text-align:center;width:150px;height:150px;top:".concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                                <div class='simulation'></div>\n                                            </div>"));
              }

              domObj.simulationTime = null;
              setTimeout(function () {
                _this.setSimulation(simulationWidth, simulationHeight);
              });
              type = "simulationClock";
              domObj.colorKS = "#EFFB0B";
              domObj.colorKF = "#00FF00";
              domObj.colorS = "#FFFF00";
              domObj.colorF = "#00FF00";
              domObj.colorM = "#FF0000";
              domObj.colorB = "#0000FF";
            } else if (this.assemblyProp == "multiWindow") {
              //添加多素材窗口
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                            <div class=\"multiWindow\" style=\"width:100%;height:100%;\">\n                                                <p style=\"color:#878787;\">").concat(_main["default"].$t('program.Multi-material_text'), "</p>\n                                            </div>\n                                        </div>")); //添加拖拽框

              type = "multiWindow";
              domObj.multiWindowData = [];
            } else if (this.assemblyProp == "WebURL") {
              // 添加网页
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='font-smoothing:none;width:".concat(ZboxWidth, "px;height:").concat(ZboxHeight, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy, ";user-select:none;'>\n                                            <div class = \"htmlbox\" id = \"sethtml").concat(htmlIndex, "\"></div>\n                                        </div>"));
              type = "WebURL";
              domObj.htmlIndex = htmlIndex;
              htmlIndex++;
            } else if (this.assemblyProp == "textTemplate") {
              // 文字模版
              (0, _jQuery["default"])(".canvasBox").append("<div class=\"mediaTZbox\" style='width:".concat(this.canvasWidth, "px;height:").concat(this.canvasHeight / 2, "px;top:").concat(wepT, "px;left:").concat(wepL, "px;z-index:").concat(this.maxHierarchy[this.currentPage], ";user-select:none;'>\n                                            <div class='textTemplate'></div>\n                                        </div>")); //添加拖拽框

              type = "textTemplate";
              domObj.PlaybackDuration = "10";
              domObj.textTemplateTextList = [];
            }

            this.deviationX++;
            this.deviationY++;
            textStyle = {
              b: false,
              i: false,
              u: false
            };
            this.sortXB[this.currentPage].push(this.sortIndexXB[this.currentPage]);
            this.sortIndexXB[this.currentPage]++;
            domObj.left = (0, _jQuery["default"])('.mediaTZbox')[(0, _jQuery["default"])('.mediaTZbox').length - 1].offsetLeft;
            domObj.top = (0, _jQuery["default"])('.mediaTZbox')[(0, _jQuery["default"])('.mediaTZbox').length - 1].offsetTop;
            domObj.width = parseInt((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[(0, _jQuery["default"])('.mediaTZbox').length - 1]).width() + 2);
            domObj.height = parseInt((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[(0, _jQuery["default"])('.mediaTZbox').length - 1]).height() + 2);
            domObj.TextHtml = '';
            domObj.fontSize = '18';
            domObj.inputDuration = 10; // domObj.media = {};

            domObj.z_index = this.maxHierarchy[this.currentPage];
            domObj.type = type;
            domObj.textColor = '#FF0000';
            domObj.textStyle = textStyle;
            domObj.weatherName = '';
            domObj.simulationName = '';
            domObj.url = "";
            this.maxHierarchy[this.currentPage]++; // if (this.assemblyProp == "DigitalClock")
            //     domObj.numberTime = ["年", "月", "日", "时", "分", "秒", "星期", "四位年", "時", "Year", "Month", "Day", "Hour", "Minute", "Second", "Week", "SiWei years"]

            this.ALLBoxData.push(domObj);

          case 103:
            this.selectBox = this.ALLBoxData.length - 1;
            this.addLocationP8((0, _jQuery["default"])(".mediaTZbox")[(0, _jQuery["default"])(".mediaTZbox").length - 1]);
            (0, _jQuery["default"])(".mediaTZbox").on("mousedown", function (e) {
              if (this.textstatic == 2) {
                (0, _jQuery["default"])("#scrolltext" + textIndex).css({
                  lineHeight: (0, _jQuery["default"])("#scrolltext" + textIndex).height() + "px"
                });
              }

              then.addLocationP8((0, _jQuery["default"])(this));
              then.selectBox = (0, _jQuery["default"])(this).index();
              var then2 = this;
              var disX = e.offsetX + (0, _jQuery["default"])(".canvasBox").offset().left;
              var disY = e.offsetY + (0, _jQuery["default"])(".canvasBox").offset().top;

              if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children("#textbigbox").length > 0) {
                //文本编辑模块执行  编辑器内的dom同步
                then.assemblyProp = "text";
                window.editor.setHtml(then.ALLBoxData[then.selectBox].TextHtml);
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".DigitalClock").length > 0) {
                then.assemblyProp = "DigitalClock";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children("div").children(".assemblyPropDisplay").length > 0) {
                then.assemblyProp = "environmental";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".weatherDisplay").length > 0) {
                then.assemblyProp = "weather";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".streamingDisplay").length > 0) {
                then.assemblyProp = "streaming";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".simulation").length > 0) {
                then.assemblyProp = "simulationClock";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
                then.assemblyProp = "multiWindow";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".htmlbox").length > 0) {
                then.assemblyProp = "WebURL";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".textTemplate").length > 0) {
                then.assemblyProp = "textTemplate";
              } else if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".media").length > 0) {
                then.assemblyProp = "media";
              }

              (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (e) {
                //鼠标按下并移动的事件
                //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
                // console.log(e.clientX,disX)
                var x = e.clientX - disX - 1;
                var y = e.clientY - disY - 1;

                if (x < 0) {
                  //处理移到画布外面情况
                  x = 0;
                } else if (x > (0, _jQuery["default"])(".canvasBox").width() - (0, _jQuery["default"])((0, _jQuery["default"])('.mediaTZbox')[then.selectBox]).outerWidth(true)) {
                  x = (0, _jQuery["default"])(".canvasBox").width() - (0, _jQuery["default"])((0, _jQuery["default"])('.mediaTZbox')[then.selectBox]).outerWidth(true);
                }

                if (y < 0) {
                  y = 0;
                } else if (y > (0, _jQuery["default"])(".canvasBox").height() - (0, _jQuery["default"])((0, _jQuery["default"])('.mediaTZbox')[then.selectBox]).outerHeight(true)) {
                  y = (0, _jQuery["default"])(".canvasBox").height() - (0, _jQuery["default"])((0, _jQuery["default"])('.mediaTZbox')[then.selectBox]).outerHeight(true);
                }

                then.ALLBoxData[then.selectBox].left = x;
                then.ALLBoxData[then.selectBox].top = y; //移动当前元素

                (0, _jQuery["default"])(then2).css({
                  'left': x + 'px',
                  'top': y + 'px'
                });
                (0, _jQuery["default"])(".canvasBox_wep").css({
                  "overflow": "hidden"
                });
              });
            });
            (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
              //console.log('canvasBox_wep')
              console.log('mouseup');
              then.getThumbnail(); // $(".canvasBox_wep").off('mousemove');
            });
            (0, _jQuery["default"])(document).on("mouseup", function () {
              var dom = document.getElementById("canvasbox");

              if (dom) {
                //console.log('document')
                (0, _jQuery["default"])(".canvasBox_wep").off('mousemove');
              }
            });
            console.log('addDom');
            this.getThumbnail();

          case 110:
          case "end":
            return _context.stop();
        }
      }
    }, null, this);
  },

  /**
   * 添加多素材窗口的每一项
   * @param {*} type 具体添加的节目类型
   * @param {*} media 媒体类型数据
   */
  addWindowItem: function addWindowItem(type, media) {
    var _this2 = this;

    var obj, html, currentDomIndex, currentWimdowIndex, longWidth, _html2, _currentDomIndex2, _currentWimdowIndex;

    return regeneratorRuntime.async(function addWindowItem$(_context2) {
      while (1) {
        switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return regeneratorRuntime.awrap(this.clearWindowTimeing());

          case 2:
            obj = {};
            html = "";

            if (typeof type == "string") {
              //type如果是字符串就是新增
              if (type == "media") {
                // 媒体
                if (media.duration) {
                  obj.PlaybackDuration = media.duration;
                } else {
                  obj.PlaybackDuration = 10;
                }

                if (media.suffix == 'mp4') {
                  html = "<div class=\"media\">\n                                <video src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + media.fileId, "\"\n                                    style='object-fit:fill;width:100%;height:100%;' autoplay loop muted>\n                                </video>\n                            </div>");
                }

                if (media.suffix == 'jpeg' || media.suffix == 'png' || media.suffix == 'jfif' || media.suffix == 'jpg' || media.suffix == 'gif') {
                  html = "<div class=\"media\">\n                                <img src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + media.fileId, "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                            </div>");
                }

                if (media.suffix == 'mp3') {
                  html = "<div class=\"media\">\n                                <img src=\"".concat(_audio["default"], "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                            </div>");
                }

                obj.media = media;
                obj.type = "media";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
              } else if (type == "DigitalClock") {
                // 数字时钟
                currentDomIndex = (0, _jQuery["default"])(".mediaTZbox").length - 1;
                obj.numberTime = ["年", "月", "日", "时", "分", "秒", "星期", "四位年", "時", "Year", "Month", "Day", "Hour", "Minute", "Second", "Week", "SiWei years"];
                obj.timeData_sj = ["HH:MM:SS", "H:MM:SS"];
                obj.timeData_rq = ["1970/01/02", "1970/1/2", "1/2/1970", "01/02/1970", "1970年/01月/02日", "1970年/1月/2日"];
                obj.Sjfg = "HH:MM:SS";
                obj.Rqfg = "1970/01/02";
                obj.isSingleLine = "2";
                obj.SQnum = 8;
                obj.PlaybackDuration = "10";
                obj.RefreshCycle = "10";
                obj.textStyle = {
                  b: false,
                  i: false,
                  u: false
                };
                obj.fontSize = '18';
                obj.textColor = '#FF0000';
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
                currentWimdowIndex = this.windowPage;
                html = "<div class='DigitalClock' style=\"width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;user-select:none;font-size:18px;\">\n                            <div class='digitalClockDiv' style=\"user-select:none;\">\n                                <span>\n                                    <div style=\"display:inherit;\">".concat(this.getNYR("n", currentDomIndex, currentWimdowIndex), "</div>\n                                    <div style=\"display:inherit;\"></div>\n                                </span><b>/</b>\n                                <span>").concat(this.getNYR("y", currentDomIndex, currentWimdowIndex), "</span><b>/</b>\n                                <span>").concat(this.getNYR("r", currentDomIndex, currentWimdowIndex), "</span>\n                            </div>\n                            <div class='digitalClockDiv' style=\"user-select:none;\">\n                                <span style=\"white-space:nowrap;line-height:1;\">").concat(this.getDay(currentDomIndex, currentWimdowIndex), "</span>\n                            </div>\n                            <div class='digitalClockDiv' style=\"user-select:none;\">\n                                <i class=\"isVcbl\" style=\"display:none;font-style:normal;\">\n                                    <i class=\"SW\" style=\"font-style:normal;\">\u4E0A\u5348</i>\n                                    <i class=\"XW\" style=\"font-style:normal;\">\u4E0B\u5348</i>&nbsp;\n                                </i>\n                                <span>").concat(this.getSFM("s", currentDomIndex, currentWimdowIndex), "</span><b>:</b>\n                                <span>").concat(this.getSFM("f", currentDomIndex, currentWimdowIndex), "</span><b>:</b>\n                                <span>").concat(this.getSFM("m", currentDomIndex, currentWimdowIndex), "</span>\n                            </div>\n                        </div>");
                obj.type = "DigitalClock";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                obj.SJitv0 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(_this2.getNYR("n", currentDomIndex, currentWimdowIndex)));
                }, 1000);
                obj.SJitv2 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(0)").find("span:eq(1)").text("".concat(_this2.getNYR("y", currentDomIndex, currentWimdowIndex)));
                }, 1000);
                obj.SJitv3 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(0)").find("span:eq(2)").text("".concat(_this2.getNYR("r", currentDomIndex, currentWimdowIndex)));
                }, 1000);
                obj.SJitv4 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(4)").find("span:eq(0)").text("".concat(_this2.getSFM("s", currentDomIndex, currentWimdowIndex)));
                }, 100);
                obj.SJitv5 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(4)").find("span:eq(1)").text("".concat(_this2.getSFM("f", currentDomIndex, currentWimdowIndex)));
                }, 100);
                obj.SJitv6 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(4)").find("span:eq(2)").text("".concat(_this2.getSFM("m", currentDomIndex, currentWimdowIndex)));
                }, 100);
                obj.SJitv7 = setInterval(function () {
                  (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).find(".multiWindow").find(".DigitalClock").find("div:eq(3)").find("span:eq(0)").text("".concat(_this2.getDay(currentDomIndex, currentWimdowIndex)));
                }, 1000);
              } else if (type == "environmental") {
                // 环境检测
                html = "<div style=\"width:100%;height:100%;overflow: hidden;position:relative;\">\n                            <div class=\"assemblyPropDisplay\" style=\"font-size:18px;\">\n                                <div class=\"assemblyPropDisplayDiv\" style=\"display:none;\">\n                                    <p></p>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">".concat(_main["default"].$t('cardDevice.temperature'), ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">\n                                        0<span>\u2103</span><span style=\"display:none;\">\u2109</span>\n                                    </span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('card.humidity'), ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0RH</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('program.noise'), ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0dB</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('program.windSpeed'), ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0m/s</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(_main["default"].$t('program.windDirection'), ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">PM10:</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">PM25:</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                </div>\n                            </div>\n                        </div>"); // 将所有字的宽度设置为最宽字体宽度

                obj.type = "environmental";
                obj.assePropData = {
                  title: " ",
                  temperatureNum: "1",
                  temperature: _main["default"].$t('cardDevice.temperature'),
                  temperatureBC: "",
                  humidity: _main["default"].$t('card.humidity'),
                  windSpeed: _main["default"].$t('program.windSpeed'),
                  windDirection: _main["default"].$t('program.windDirection'),
                  noise: _main["default"].$t('program.noise'),
                  Pm10: "PM10",
                  Pm25: "PM2.5",
                  position: "2",
                  RollingSpeed: "10",
                  RefreshZQ: "10",
                  playSC: "10",
                  display: {
                    '标题': false,
                    '温度': true,
                    '湿度': true,
                    '风速': true,
                    '风向': true,
                    '噪音': true,
                    'PM2.5': true,
                    'PM10': true
                  }
                };
                obj.RefreshCycle = "10";
                obj.PlaybackDuration = "10";
                obj.textStyle = {
                  b: false,
                  i: false,
                  u: false
                };
                obj.fontSize = '18';
                obj.textColor = '#FF0000';
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                longWidth = this.findEnvironmentalLongWidth();
                this.setEnvironmentalWidth(longWidth);
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
              } else if (type == "simulationClock") {
                // 模拟时钟
                html = "<div class='simulation' style=\"width:100%;height:100%;\"></div>";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                obj.simulationTime = null;
                setTimeout(function () {
                  _this2.setSimulation((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_this2.selectBox]).find(".multiWindow").height(), (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_this2.selectBox]).find(".multiWindow").width());
                });
                obj.type = "simulationClock";
                obj.colorKS = "#EFFB0B", obj.colorKF = "#00FF00", obj.colorS = "#FFFF00", obj.colorF = "#00FF00", obj.colorM = "#FF0000", obj.colorB = "#0000FF";
                obj.inputDuration = "10";
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
              } else if (type == "Weather") {
                //天气
                html = "<div class='weatherDisplay'> </div>";
                obj.type = "Weather";
                obj.province = "";
                obj.valueWeather = [];
                obj.saveWeather = {};
                obj.fontSize = '18';
                obj.inputDuration = "10";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
              } else if (type == "streaming") {
                // 流媒体
                html = "<div class='streamingDisplay' style=\"width:100%;height:100%;\"> <img src=\"rtmp://csztv.2500sz.com/live/c01}\" alt=\"\"> </div>";
                obj.type = "streaming";
                obj.streamingUrl = '';
                obj.streamingName = "LiveVivo";
                obj.streamingtype = "LiveVivo";
                obj.inputDuration = "10";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
              } else if (type == "text") {
                // 文本
                obj.curchange_m = "static_m";
                html = "<div  class='".concat(obj.curchange_m, " ").concat(multTextIndex, "' id='textbigbox_m'  style=\"font-smoothing:none;width:100%;height:100%;position:relative;overflow: hidden;\">\n                            <div class='textbig_m' id=\"scrolltext_m").concat(multTextIndex, "\"  style='font-smoothing:none;width:100%;height:100%;position:absolute;' ></div>\n                        </div>");
                obj.selectText_m = multTextIndex;
                obj.type = "text";
                obj.textSpeed = "10";
                obj.effect = "right to left";
                obj.textRadio = "1";
                obj.showWndImage = [];
                obj.ImagePage = '1';
                obj.inputDuration = "10";
                obj.PageArrSum = [];
                obj.base64Page = '';
                obj.showWndImage_m = [];
                obj.isShow = true;
                obj.runSpeedShow = false;
                obj.runPageShow = false;
                obj.preview = false;
                obj.TextHtml_m = "";
                multTextIndex++;
                setTimeout(function () {
                  window.editor.setHtml('');
                }, 200);
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(html);
                this.windowPage = this.ALLBoxData[this.selectBox].multiWindowData.length;
              }

              obj.width = this.ALLBoxData[this.selectBox].width;
              obj.left = this.ALLBoxData[this.selectBox].left;
              obj.height = this.ALLBoxData[this.selectBox].height;
              obj.top = this.ALLBoxData[this.selectBox].top;
              this.ALLBoxData[this.selectBox].multiWindowData.push(obj);
            } else {
              //否则为切换页面
              // this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage].type=''
              _html2 = '';

              if (type.type == "media") {
                // 媒体
                if (type.media.suffix == 'mp4') {
                  _html2 = "<div class=\"media\">\n                                <video src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + type.media.fileId, "\"\n                                    style='object-fit:fill;width:100%;height:100%;' autoplay loop muted>\n                                </video>\n                            </div>");
                }

                if (type.media.suffix == 'jpeg' || type.media.suffix == 'png' || type.media.suffix == 'jfif' || type.media.suffix == 'jpg' || type.media.suffix == 'gif') {
                  _html2 = "<div class=\"media\">\n                                <img src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + type.media.fileId, "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                            </div>");
                }

                if (type.media.suffix == 'mp3') {
                  _html2 = "<div class=\"media\">\n                                <img src=\"".concat(_audio["default"], "\" style=\"width:100%;height:100%;display:block;pointer-events:none;\"></img>\n                            </div>");
                }

                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
              } else if (type.type == "environmental") {
                // 环境检测
                _html2 = "<div style=\"width:100%;height:100%;overflow: hidden;position:relative;\">\n                            <div class=\"assemblyPropDisplay\" style=\"font-size:".concat(type.fontSize, "px;\">\n                                <div class=\"assemblyPropDisplayDiv\" style=\"display:none;\">\n                                    <p>").concat(type.assePropData.title, "</p>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.temperature, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">\n                                        0<span>\u2103</span><span style=\"display:none;\">\u2109</span>\n                                    </span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.humidity, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0RH</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.noise, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0dB</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.windSpeed, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0m/s</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.windDirection, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.Pm10, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                </div>\n                                <div class=\"assemblyPropDisplayDiv\">\n                                    <span style=\"display: inline-block; text-align: right;\">").concat(type.assePropData.Pm25, ":</span>\n                                    <span style=\"display: inline-block; text-align: left;\">0\u03BCg/m\xB3</span>\n                                </div>\n                            </div>\n                        </div>");
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
                this.setAlignPosition(false);
                this.setTemperatureNum(false);
                this.setEnvironmentalFontSize(false);
                this.setEnvironmentalColor(false);
                this.setEnvironmentalTextStyle(false);
              } else if (type.type == "simulationClock") {
                //模拟时钟
                _html2 = "<div class='simulation' style=\"width:100%;height:100%;\"></div>";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
                setTimeout(function () {
                  _this2.setSimulation((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_this2.selectBox]).find(".multiWindow").height(), (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[_this2.selectBox]).find(".multiWindow").width(), false);
                });
              } else if (type.type == "Weather") {
                // 天气
                _html2 = "<div class='weatherDisplay'></div>";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
                this.setWeather(false);
                this.monitorWeatherSize(false);
              } else if (type.type == "streaming") {
                // 流媒体
                _html2 = "<div class='streamingDisplay'  style=\"width:100%;height:100%;\"><img src=\"rtmp://csztv.2500sz.com/live/c01}\" alt=\"\"></div>";
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
              } else if (type.type == "DigitalClock") {
                // 数字时钟
                _currentDomIndex2 = this.selectBox;
                _currentWimdowIndex = this.windowPage;
                _html2 = "<div class='DigitalClock' style=\"width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;user-select:none; \">\n                            <div class='digitalClockDiv' style=\"user-select:none;\">\n                                <span>\n                                    <div style=\"display:inherit;\">".concat(this.getNYR("n", _currentDomIndex2, _currentWimdowIndex), "</div>\n                                    <div style=\"display:inherit;\"></div>\n                                </span><b>/</b>\n                                <span>").concat(this.getNYR("y", _currentDomIndex2, _currentWimdowIndex), "</span><b>/</b>\n                                <span>").concat(this.getNYR("r", _currentDomIndex2, _currentWimdowIndex), "</span>\n                            </div>\n                            <div class='digitalClockDiv' style=\"user-select:none;\">\n                                <span style=\"white-space:nowrap;line-height:1;\">").concat(this.getDay(_currentDomIndex2, _currentWimdowIndex), "</span>\n                            </div>\n                            <div class='digitalClockDiv' style=\"user-select:none;\">\n                                <i class=\"isVcbl\" style=\"display:none;font-style:normal;\">\n                                    <i class=\"SW\" style=\"font-style:normal;\">\u4E0A\u5348</i>\n                                    <i class=\"XW\" style=\"font-style:normal;\">\u4E0B\u5348</i>&nbsp;\n                                </i>\n                                <span>").concat(this.getSFM("s", _currentDomIndex2, _currentWimdowIndex), "</span><b>:</b>\n                                <span>").concat(this.getSFM("f", _currentDomIndex2, _currentWimdowIndex), "</span><b>:</b>\n                                <span>").concat(this.getSFM("m", _currentDomIndex2, _currentWimdowIndex), "</span>\n                            </div>\n                        </div>");
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
                this.setSingleLine(false);
                this.setTextStyle(false);
                this.setSizeDX(false);
                this.setNumberTime(false);
                this.setDigitalClockColor(false);
              } else if (type.type == "text") {
                //文本
                _html2 = "<div  class='".concat(type.curchange_m, "  ").concat(type.selectText_m, "' id='textbigbox_m'  style=\"font-smoothing:none;width:100%;height:100%;position:relative;overflow: hidden;\">\n                            <div class='textbig_m' id=\"scrolltext_m").concat(type.selectText_m, "\" style='font-smoothing:none;width:100%;height:100%;position:absolute;'></div>\n                        </div>");
                (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html(_html2);
                (0, _jQuery["default"])((0, _jQuery["default"])("body:eq(0)")).css({
                  "overflow": "visible"
                });
                window.editor.setHtml(type.TextHtml_m);
              }
            }

          case 5:
          case "end":
            return _context2.stop();
        }
      }
    }, null, this);
  },

  /**
   * 清除多窗口中的定时
   */
  clearWindowTimeing: function clearWindowTimeing() {
    if (this.ALLBoxData[this.selectBox].multiWindowData && this.ALLBoxData[this.selectBox].multiWindowData.length > 0) {
      this.ALLBoxData[this.selectBox].multiWindowData.forEach(function (item, index) {
        if (item.type == "DigitalClock") {
          clearInterval(item.SJitv0);
          clearInterval(item.SJitv2);
          clearInterval(item.SJitv3);
          clearInterval(item.SJitv4);
          clearInterval(item.SJitv5);
          clearInterval(item.SJitv6);
          clearInterval(item.SJitv7);
        } else if (item.type == "environmental") {
          clearInterval(item.textRoll);
        } else if (item.type == "simulationClock") {
          clearInterval(item.simulationTime);
        }
      });
    }
  },
  clearWindowTimeingByMultiWindowData: function clearWindowTimeingByMultiWindowData(multiWindowData) {
    if (multiWindowData) {
      if (multiWindowData.type == "DigitalClock") {
        clearInterval(multiWindowData.SJitv0);
        clearInterval(multiWindowData.SJitv2);
        clearInterval(multiWindowData.SJitv3);
        clearInterval(multiWindowData.SJitv4);
        clearInterval(multiWindowData.SJitv5);
        clearInterval(multiWindowData.SJitv6);
        clearInterval(multiWindowData.SJitv7);
      } else if (multiWindowData.type == "environmental") {
        clearInterval(multiWindowData.textRoll);
      } else if (multiWindowData.type == "simulationClock") {
        clearInterval(multiWindowData.simulationTime);
      }
    }
  },

  /**
   * 多窗口素材操作素材位置
   * @param {*} type 操作类型 delete 删除 empty 清空 up 向上 down 向下
   */
  setDomStyle: function setDomStyle(type) {
    if (type == "delete") {
      // 删除
      var item = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
      this.clearWindowTimeingByMultiWindowData(item);
      this.ALLBoxData[this.selectBox].multiWindowData.splice(this.windowPage, 1);

      if (this.ALLBoxData[this.selectBox].multiWindowData.length == 0) {
        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html('<p style="color:#878787;">' + _main["default"].$t('program.Multi-material_text') + '</p>');
      } else {
        this.windowPage = 0;
        this.addWindowItem(this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage], this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage].media);
      }
    } else if (type == "empty") {
      // 清空
      this.clearWindowTimeing();
      this.ALLBoxData[this.selectBox].multiWindowData = [];
      (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".multiWindow").html('<p style="color:#878787;">' + _main["default"].$t('program.Multi-material_text') + '</p>');
    } else if (type == "up") {
      // 向上
      var obj = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
      this.ALLBoxData[this.selectBox].multiWindowData.splice(this.windowPage, 1);
      this.ALLBoxData[this.selectBox].multiWindowData.splice(this.windowPage - 1, 0, obj);
      this.windowPage--;

      if (this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage].type == "DigitalClock") {
        this.addWindowItem(this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage]);
      }
    } else if (type == "down") {
      // 向下
      var _obj = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
      this.ALLBoxData[this.selectBox].multiWindowData.splice(this.windowPage, 1);
      this.ALLBoxData[this.selectBox].multiWindowData.splice(this.windowPage + 1, 0, _obj);
      this.windowPage++;

      if (this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage].type == "DigitalClock") {
        this.addWindowItem(this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage]);
      }
    }
  },

  /**
   * 为鼠标按下的元素添加8拖拽变大按钮
   * @param {*} taget 需要添加拖拽功能的完整html元素
   */
  addLocationP8: function addLocationP8(taget) {
    var html = "";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:-3px;top:-3px;cursor:se-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:-3px;top:calc(50% - 2px);cursor:w-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:-3px;top:calc(100% - 5px);cursor:ne-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:calc(50% - 2px);top:calc(100% - 5px);cursor:s-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:calc(100% - 5px);top:calc(100% - 5px);cursor:se-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:calc(100% - 5px);top:calc(50% - 2px);cursor:w-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:calc(100% - 5px);top:-5px;cursor:ne-resize;'></p>";
    html += "<p class=\"P_TZD\" style='width:10px;height:10px;border:solid 1px #fff;position:absolute;left:calc(50% - 2px);top:-3px;cursor:s-resize;'></p>";
    (0, _jQuery["default"])(".mediaTZbox").each(function (index, item) {
      //添加按钮前先清空所有mediaTZbox下p按钮
      (0, _jQuery["default"])(item).find(".P_TZD").remove();
      (0, _jQuery["default"])(item).css({
        "border": "dashed 1px #fff"
      });
    }); // console.log($(taget));

    (0, _jQuery["default"])(taget).css({
      "border": "solid 1px #fff"
    });
    (0, _jQuery["default"])(taget).append(html); //添加按钮

    var then = this;
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(0).on("mousedown", function (e) {
      e.stopImmediatePropagation(); //每次点击获取下mediaTZbox的左上偏移量 和宽高

      var left = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetLeft;
      var top = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetTop;
      var w = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].clientWidth;
      var h = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].clientHeight;
      var x = e.pageX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft + w; //点击时候mediaTZbox长距 距离canvasBox边界距离

      var y = e.pageY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop + h;
      var data = then.getDataByMulti(then);
      var assemblyPropDisplay = data.assemblyPropDisplay;
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        if (left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w) < 0) {
          then.ALLBoxData[then.selectBox].left = 0;
        } else if (left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w) > left + w) {
          then.ALLBoxData[then.selectBox].left = left + w;
        } else {
          then.ALLBoxData[then.selectBox].left = left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w);
        }

        if (top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h) < 0) {
          then.ALLBoxData[then.selectBox].top = 0;
        } else if (top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h) > top + h) {
          then.ALLBoxData[then.selectBox].top = top + h;
        } else {
          then.ALLBoxData[then.selectBox].top = top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h);
        }

        then.ALLBoxData[then.selectBox].width = x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft);
        then.ALLBoxData[then.selectBox].height = y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop);
        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
          'width': then.ALLBoxData[then.selectBox].width + 'px',
          'height': then.ALLBoxData[then.selectBox].height + 'px',
          'top': then.ALLBoxData[then.selectBox].top + "px",
          'left': then.ALLBoxData[then.selectBox].left + "px"
        });

        if (then.ALLBoxData[then.selectBox].isSingleLine == "1") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".DigitalClock").css({
            "line-height": then.ALLBoxData[then.selectBox].height + "px"
          });
        }

        if (tempData) {
          if (tempData.type == "environmental" && tempData.assePropData.position == 4) {
            assemblyPropDisplay.css({
              "line-height": assemblyPropDisplay.height() + "px"
            });
          } else if (tempData.type == "simulationClock") {
            then.setSimulation();
          }
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        // $(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 0 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(1).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var left = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetLeft;
      var w = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].clientWidth;
      var x = e.pageX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft + w;
      var data = then.getDataByMulti(then);
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        if (left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w) < 0) {
          then.ALLBoxData[then.selectBox].left = 0;
        } else if (left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w) > left + w) {
          then.ALLBoxData[then.selectBox].left = left + w;
        } else {
          then.ALLBoxData[then.selectBox].left = left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w);
        }

        then.ALLBoxData[then.selectBox].width = x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft);
        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
          'width': then.ALLBoxData[then.selectBox].width + 'px',
          'left': then.ALLBoxData[then.selectBox].left + "px"
        });

        if (tempData && tempData.type == "simulationClock") {
          then.setSimulation();
        }

        if (then.assemblyProp == "text") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find('.textbig').css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
          then.type = 'text';
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        // $(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 1 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(2).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var left = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetLeft;
      var top = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetTop;
      var w = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].clientWidth;
      var x = e.pageX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft + w;
      var data = then.getDataByMulti(then);
      var assemblyPropDisplay = data.assemblyPropDisplay;
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        var disY = e.offsetY + (0, _jQuery["default"])(".canvasBox").offset().top;

        if (left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w) < 0) {
          then.ALLBoxData[then.selectBox].left = 0;
        } else if (left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w) > left + w) {
          then.ALLBoxData[then.selectBox].left = left + w;
        } else {
          then.ALLBoxData[then.selectBox].left = left - (x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft) - w);
        }

        then.ALLBoxData[then.selectBox].width = x - (evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft);
        then.ALLBoxData[then.selectBox].height = evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop - top;

        if (evt.clientY - disY > (0, _jQuery["default"])(".canvasBox").height()) {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'height': (0, _jQuery["default"])(".canvasBox").height() - top + 'px'
          });
        } else {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'height': then.ALLBoxData[then.selectBox].height + 'px'
          });
        }

        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
          'width': then.ALLBoxData[then.selectBox].width + 'px',
          'left': then.ALLBoxData[then.selectBox].left + "px"
        });

        if (then.ALLBoxData[then.selectBox].isSingleLine == "1") {
          //单行显示居中效果
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".DigitalClock").css({
            "line-height": then.ALLBoxData[then.selectBox].height + "px"
          });
        }

        if (tempData) {
          if (tempData.type == "environmental" && tempData.assePropData.position == 4) {
            assemblyPropDisplay.css({
              "line-height": assemblyPropDisplay.height() + "px"
            });
          } else if (tempData.type == "simulationClock") {
            then.setSimulation();
          }
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        // $(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 2 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(3).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var top = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetTop;
      var data = then.getDataByMulti(then);
      var assemblyPropDisplay = data.assemblyPropDisplay;
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        var disY = e.offsetY + (0, _jQuery["default"])(".canvasBox").offset().top;
        then.ALLBoxData[then.selectBox].height = evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop - top;

        if (evt.clientY - disY > (0, _jQuery["default"])(".canvasBox").height()) {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'height': (0, _jQuery["default"])(".canvasBox").height() - top + 'px'
          });
        } else {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'height': then.ALLBoxData[then.selectBox].height + 'px'
          });
        }

        if (then.ALLBoxData[then.selectBox].isSingleLine == "1") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".DigitalClock").css({
            "line-height": then.ALLBoxData[then.selectBox].height + "px"
          });
        }

        if (tempData) {
          if (tempData.type == "environmental" && tempData.assePropData.position == 4) {
            assemblyPropDisplay.css({
              "line-height": assemblyPropDisplay.height() + "px"
            });
          } else if (tempData.type == "simulationClock") {
            then.setSimulation();
          }
        }

        if (then.assemblyProp == "text") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find('.textbig').css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
          then.type = 'text';
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        (0, _jQuery["default"])(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 3 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    }); // TODO

    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(4).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var left = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetLeft;
      var top = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetTop;
      var data = then.getDataByMulti(then);
      var assemblyPropDisplay = data.assemblyPropDisplay;
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        var disX = e.offsetX + (0, _jQuery["default"])(".canvasBox").offset().left;
        var disY = e.offsetY + (0, _jQuery["default"])(".canvasBox").offset().top;
        then.ALLBoxData[then.selectBox].height = evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop - top;
        then.ALLBoxData[then.selectBox].width = evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft - left;

        if (evt.clientX - disX > (0, _jQuery["default"])(".canvasBox").width()) {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'width': (0, _jQuery["default"])(".canvasBox").width() - left + 'px'
          });
        } else {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
        }

        if (evt.clientY - disY > (0, _jQuery["default"])(".canvasBox").height()) {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'height': (0, _jQuery["default"])(".canvasBox").height() - top + 'px'
          });
        } else {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'height': then.ALLBoxData[then.selectBox].height + 'px'
          });
        }

        if (then.ALLBoxData[then.selectBox].isSingleLine == "1") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".DigitalClock").css({
            "line-height": then.ALLBoxData[then.selectBox].height + "px"
          });
        }

        if (tempData) {
          if (tempData.type == "environmental" && tempData.assePropData.position == 4) {
            assemblyPropDisplay.css({
              "line-height": assemblyPropDisplay.height() + "px"
            });
          } else if (tempData.type == "simulationClock") {
            then.setSimulation();
          }
        } //  if (then.assemblyProp == "text") {
        //     $($(".mediaTZbox")[then.selectBox]).find('.textbig').css({
        //         'width': then.ALLBoxData[then.selectBox].width + 'px',
        //     });
        //     then.type = 'text'
        // }

      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        (0, _jQuery["default"])(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 4 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(5).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var left = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetLeft;
      var data = then.getDataByMulti(then);
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        var disX = e.offsetX + (0, _jQuery["default"])(".canvasBox").offset().left;
        then.ALLBoxData[then.selectBox].width = evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft - left;

        if (evt.clientX - disX > (0, _jQuery["default"])(".canvasBox").width()) {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'width': (0, _jQuery["default"])(".canvasBox").width() - left + 'px'
          });
        } else {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
        }

        if (tempData && tempData.type == "simulationClock") {
          then.setSimulation();
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        (0, _jQuery["default"])(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 5 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(6).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var left = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetLeft;
      var top = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetTop;
      var h = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].clientHeight;
      var y = e.pageY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop + h;
      var data = then.getDataByMulti(then);
      var assemblyPropDisplay = data.assemblyPropDisplay;
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        var disX = e.offsetX + (0, _jQuery["default"])(".canvasBox").offset().left;

        if (top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h) < 0) {
          then.ALLBoxData[then.selectBox].top = 0;
        } else if (top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h) > top + h) {
          then.ALLBoxData[then.selectBox].top = top + h;
        } else {
          then.ALLBoxData[then.selectBox].top = top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h);
        }

        then.ALLBoxData[then.selectBox].width = evt.clientX - (0, _jQuery["default"])(".canvasBox")[0].offsetLeft - left;
        then.ALLBoxData[then.selectBox].height = y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop);

        if (evt.clientX - disX > (0, _jQuery["default"])(".canvasBox").width()) {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'width': (0, _jQuery["default"])(".canvasBox").width() - left + 'px'
          });
        } else {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
        }

        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
          'height': then.ALLBoxData[then.selectBox].height + 'px',
          'top': then.ALLBoxData[then.selectBox].top + "px"
        });

        if (then.ALLBoxData[then.selectBox].isSingleLine == "1") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".DigitalClock").css({
            "line-height": then.ALLBoxData[then.selectBox].height + "px"
          });
        }

        if (tempData) {
          if (tempData.type == "environmental" && tempData.assePropData.position == 4) {
            assemblyPropDisplay.css({
              "line-height": assemblyPropDisplay.height() + "px"
            });
          } else if (tempData.type == "simulationClock") {
            then.setSimulation();
          }
        }

        if (then.assemblyProp == "text") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find('.textbig').css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
          then.type = 'text';
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        (0, _jQuery["default"])(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 6 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
    (0, _jQuery["default"])(".mediaTZbox").find(".P_TZD").eq(7).on("mousedown", function (e) {
      e.stopImmediatePropagation();
      var top = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].offsetTop;
      var h = (0, _jQuery["default"])(".mediaTZbox")[then.selectBox].clientHeight;
      var y = e.pageY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop + h;
      var data = then.getDataByMulti(then);
      var assemblyPropDisplay = data.assemblyPropDisplay;
      var textbig = data.textbig;
      var textbigImg = data.textbigImg;
      var tempData = data.tempData;
      (0, _jQuery["default"])(".canvasBox_wep").mousemove(function (evt) {
        if (top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h) < 0) {
          then.ALLBoxData[then.selectBox].top = 0;
        } else if (top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h) > top + h) {
          then.ALLBoxData[then.selectBox].top = top + h;
        } else {
          then.ALLBoxData[then.selectBox].top = top - (y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop) - h);
        }

        then.ALLBoxData[then.selectBox].height = y - (evt.clientY - (0, _jQuery["default"])(".canvasBox")[0].offsetTop);
        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).css({
          'height': then.ALLBoxData[then.selectBox].height + 'px',
          'top': then.ALLBoxData[then.selectBox].top + "px"
        });

        if (then.ALLBoxData[then.selectBox].isSingleLine == "1") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".DigitalClock").css({
            "line-height": then.ALLBoxData[then.selectBox].height + "px"
          });
        }

        if (tempData) {
          if (tempData.type == "environmental" && tempData.assePropData.position == 4) {
            assemblyPropDisplay.css({
              "line-height": assemblyPropDisplay.height() + "px"
            });
          } else if (tempData.type == "simulationClock") {
            then.setSimulation();
          }
        }

        if (then.assemblyProp == "text") {
          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find('.textbig').css({
            'width': then.ALLBoxData[then.selectBox].width + 'px'
          });
          then.type = 'text';
        }
      });
      (0, _jQuery["default"])(".canvasBox_wep").on("mouseup", function () {
        // console.log($(".mediaTZbox").find(".P_TZD").eq(4))
        // console.log('ssdds')
        // $(".canvasBox").off('mousemove');
        (0, _jQuery["default"])(".canvasBox_wep").off('mousemove');
        console.log('P_TZD 7 mouseup');
        then.getThumbnail();
        then.modifyWidthOrHeight();
        then.mouseupText(tempData, then, textbig, textbigImg);
      });
    });
  },

  /**
   * 获取年月日
   * @param {*} item 需要转换的类型 n 年，y 月，r 日
   * @param {*} currentDomIndex 选择的元素的下标
   * @param {*} currentWimdowIndex 多窗口素材当前选的元素的下标
   * @returns
   */
  getNYR: function getNYR(item, currentDomIndex, currentWimdowIndex) {
    var date = new Date();
    var option, numberTime;
    var temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).children(".multiWindow").length > 0) {
      temporaryData = this.ALLBoxData[currentDomIndex].multiWindowData[currentWimdowIndex];
    } else {
      temporaryData = this.ALLBoxData[currentDomIndex];
    }

    if (temporaryData) {
      date.setHours(date.getHours() - 8 + temporaryData.SQnum);
      option = temporaryData.Rqfg;
      numberTime = temporaryData.numberTime; //多选框信息
    }

    var N, Y, R;
    N = date.getFullYear();
    if (date.getMonth() < 10 && option !== "1970/1/2" && option !== "1/2/1970" && option !== "1970年/1月/2日" && option !== "1月/2日/1970年") Y = "0" + (date.getMonth() + 1);else Y = date.getMonth() + 1;
    if (date.getDate() < 10 && option !== "1970/1/2" && option !== "1/2/1970" && option !== "1970年/1月/2日" && option !== "1月/2日/1970年") R = "0" + date.getDate();else R = date.getDate();

    if (option) {
      if (option == "1970/1/2" || option == "1970/01/02") {
        if (item == "n") return N + "";
        if (item == "y") return Y;
        if (item == "r") return R;
      } else if (option == "1/2/1970" || option == "01/02/1970") {
        if (item == "n") return Y;
        if (item == "y") return R;
        if (item == "r") return N + "";
      } else if (option == "1月/2日/1970年" || option == "01月/02日/1970年") {
        if (item == "n") return Y + "月";
        if (item == "y") return R + "日";
        if (item == "r") return N + "年";
      } else if (option == "1970年/1月/2日" || option == "1970年/01月/02日") {
        if (item == "n") return N + "年";
        if (item == "y") return Y + "月";
        if (item == "r") return R + "日";
      }
    } else {
      if (item == "n") return N + "";
      if (item == "y") return Y;
      if (item == "r") return R;
    }
  },

  /**
   * 获取时分秒
   * @param {*} item 需要转换的类型 s 时，f 分，m 秒
   * @param {*} currentDomIndex 选择的元素的下标
   * @param {*} currentWimdowIndex 多窗口素材当前选的元素的下标
   * @returns
   */
  getSFM: function getSFM(item, currentDomIndex, currentWimdowIndex) {
    var date = new Date();
    var temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).children(".multiWindow").length > 0) {
      temporaryData = this.ALLBoxData[currentDomIndex].multiWindowData[currentWimdowIndex];
    } else {
      temporaryData = this.ALLBoxData[currentDomIndex];
    }

    if (temporaryData) {
      date.setHours(date.getHours() - 8 + temporaryData.SQnum); //处理时区问题

      var S, F, M;
      if (date.getHours() < 10 && temporaryData.Sjfg == "HH:MM:SS") S = "0" + date.getHours();else S = date.getHours();
      if (date.getMinutes() < 10 && temporaryData.Sjfg == "HH:MM:SS") F = "0" + date.getMinutes();else F = date.getMinutes();
      if (date.getSeconds() < 10 && temporaryData.Sjfg == "HH:MM:SS") M = "0" + date.getSeconds();else M = date.getSeconds();
      if (item == "s") return S;
      if (item == "f") return F;
      if (item == "m") return M;
    } else {
      var _S, _F, _M;

      if (date.getHours() < 10) _S = "0" + date.getHours();else _S = date.getHours();
      if (date.getMinutes() < 10) _F = "0" + date.getMinutes();else _F = date.getMinutes();
      if (date.getSeconds() < 10) _M = "0" + date.getSeconds();else _M = date.getSeconds();
      if (item == "s") return _S;
      if (item == "f") return _F;
      if (item == "m") return _M;
    }
  },

  /**
   * 获取星期几
   * @param {*} currentDomIndex 选择的元素的下标
   * @param {*} currentWimdowIndex 多窗口素材当前选的元素的下标
   * @returns
   */
  getDay: function getDay(currentDomIndex, currentWimdowIndex) {
    var date = new Date();
    var temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[currentDomIndex]).children(".multiWindow").length > 0) {
      temporaryData = this.ALLBoxData[currentDomIndex].multiWindowData[currentWimdowIndex];
    } else {
      temporaryData = this.ALLBoxData[currentDomIndex];
    }

    if (temporaryData) {
      date.setHours(date.getHours() - 8 + temporaryData.SQnum);
    }

    switch (date.getDay()) {
      case 0:
        return "星期日";

      case 1:
        return "星期一";

      case 2:
        return "星期二";

      case 3:
        return "星期三";

      case 4:
        return "星期四";

      case 5:
        return "星期五";

      case 6:
        return "星期六";
    }
  },

  /**
   * 转换时间戳和时间格式为   1997-1-1
   */
  transformationDate: function transformationDate(data) {
    var time = new Date(data);
    var Y = time.getFullYear();
    var Mon = time.getMonth() + 1;
    var Day = time.getDate();
    Mon < 10 ? Mon = "0" + Mon : Mon = Mon;
    Day < 10 ? Day = "0" + Day : Day = Day;
    return "".concat(Y, "-").concat(Mon, "-").concat(Day);
  },

  /**
   * 转换时间戳和时间格式为   H:Min
   */
  transformationDateTime: function transformationDateTime(data) {
    var time = new Date(data);
    var H = time.getHours();
    var Min = time.getMinutes();
    var html = '';
    H < 10 ? html = "0" + H : html = H;
    Min < 10 ? html += ":0" + Min : html += ":" + Min;
    return html;
  },

  /**
   * 设置模拟时钟
   * @param {*} w 宽度
   * @param {*} h 高度
   */
  setSimulation: function setSimulation(w, h, isSet) {
    var temporaryTaget, temporaryData;
    var multiWindow = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow");

    if (multiWindow.length > 0) {
      multiWindow.find(".simulation").width((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").width());
      multiWindow.find(".simulation").height((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").height());
      temporaryTaget = multiWindow.find(".simulation");
      temporaryData = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".simulation");
      temporaryData = this.ALLBoxData[this.selectBox];
    }

    clearInterval(temporaryData.simulationTime);
    temporaryTaget.html('');
    var width = w || temporaryTaget.width() || temporaryTaget.width;
    var height = h || temporaryTaget.height() || temporaryData.height;
    var hrlfR;

    if (width >= height) {
      hrlfR = height;
    } else {
      hrlfR = width;
    } // if(this.canvasHeight<300 || this.canvasWidth <300){
    //     if(this.canvasWidth>this.canvasHeight){
    //         hrlfR = this.canvasHeight
    //     }else{
    //         hrlfR = this.canvasWidth
    //     }
    // }else{
    //     if (width >= height) {
    //         hrlfR = height
    //     } else {
    //         hrlfR = width
    //     }
    // }
    // console.log(this.canvasHeight, this.canvasWidth);


    temporaryTaget.append("<canvas id=\"canvas\" width=\"".concat(hrlfR, "\" height=\"").concat(hrlfR, "\"></canvas>")); // $('.simulati

    var canvas = temporaryTaget.find("#canvas")[0];
    var cxt = canvas.getContext("2d");
    var r = hrlfR / 2;
    var colorB = temporaryData.colorB;
    var colorKS = temporaryData.colorKS;
    var colorKF = temporaryData.colorKF;

    function drawBg() {
      //重置原点
      cxt.save();
      cxt.translate(r, r); // 画时钟外圈

      cxt.beginPath();
      cxt.arc(0, 0, r - 2, 0, 2 * Math.PI, true);
      cxt.fillStyle = colorB;
      cxt.fill();
      cxt.lineWidth = 1;
      cxt.stroke(); //画小时数

      var hour = [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2];
      hour.forEach(function (num, i) {
        var rad = 2 * Math.PI / 12 * i;
        var x = Math.cos(rad) * (r - 30);
        var y = Math.sin(rad) * (r - 30);
        cxt.font = "".concat(hrlfR / 10, "px sans-serif");
        cxt.fillStyle = colorKS;
        cxt.textAlign = "center";
        cxt.textBaseline = "middle";
        cxt.fillText(num, x, y);
      }); // 画刻度

      for (var i = 0; i < 60; i++) {
        var rad = 2 * Math.PI / 60 * i;
        var x = Math.cos(rad) * (r - 18);
        var y = Math.sin(rad) * (r - 18);
        cxt.beginPath();

        if (i % 5 == 0) {
          cxt.fillStyle = colorKS;
          cxt.arc(x, y, 2, 0, 2 * Math.PI, true);
        } else {
          cxt.fillStyle = colorKF;
          cxt.arc(x, y, 2, 0, 2 * Math.PI, true);
        }

        cxt.fill();
      }
    } // 画时针


    var colorSZ = temporaryData.colorS;

    function drawHour(hour, minute) {
      cxt.save();
      var rad = 2 * Math.PI / 12 * hour + 2 * Math.PI / 12 * minute / 60;
      cxt.beginPath();
      cxt.rotate(rad);
      cxt.moveTo(2, 35);
      cxt.lineTo(2, 25);
      cxt.lineTo(-2, 25);
      cxt.lineTo(-1, -r + 45);
      cxt.lineTo(1, -r + 45);
      cxt.lineTo(2, 35);
      cxt.lineWidth = 5;
      cxt.fillStyle = colorSZ; // cxt.fillStyle = "#fff";
      // cxt.fillStyle = e.colorS;

      cxt.fill();
      cxt.restore();
    }

    var colorFZ = temporaryData.colorF; // 画分针

    function drawMinute(minute) {
      cxt.save();
      var rad = 2 * Math.PI / 60 * minute;
      cxt.beginPath();
      cxt.rotate(rad); // cxt.moveTo(0, 18);
      // cxt.lineTo(0, -r + 30);

      cxt.moveTo(2, 35);
      cxt.lineTo(2, 25);
      cxt.lineTo(-2, 25);
      cxt.lineTo(-1, -r + 35);
      cxt.lineTo(1, -r + 35);
      cxt.lineTo(2, 25);
      cxt.lineWidth = 3;
      cxt.fillStyle = colorFZ;
      cxt.fill();
      cxt.restore();
    } // 画秒针


    var colorM = temporaryData.colorM;

    function drawSecond(second) {
      cxt.save();
      var rad = 2 * Math.PI / 60 * second;
      cxt.beginPath();
      cxt.rotate(rad);
      cxt.moveTo(0, 25);
      cxt.lineTo(2, 25);
      cxt.lineTo(-2, 25);
      cxt.lineTo(-1, -r + 25);
      cxt.lineTo(1, -r + 25);
      cxt.lineTo(2, 25);
      cxt.lineWidth = 1;
      cxt.fillStyle = colorM;
      cxt.fill();
      cxt.restore();
    } // 画中心点


    function drawDot() {
      cxt.beginPath();
      cxt.arc(0, 0, 2, 0, 2 * Math.PI, true);
      cxt.fillStyle = "#ADFF2F";
      cxt.fill();
    } // 绘制真实时间


    function draw() {
      cxt.clearRect(0, 0, hrlfR, hrlfR);
      var now = new Date();
      var hour = now.getHours();
      var minute = now.getMinutes();
      var second = now.getSeconds();
      drawBg();
      drawHour(hour, minute);
      drawMinute(minute);
      drawSecond(second);
      drawDot();
      cxt.restore();
    }

    draw();
    temporaryData.simulationTime = setInterval(function () {
      draw();
    }, 1000);

    if (isSet != false) {
      console.log('setSimulation');
      this.getThumbnail();
    }
  },

  /**
   * 向天气的dom元素中添加天气数据
   * @param {*} x
   */
  setWeather: function setWeather(isSet) {
    var weatherDisplay, temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").length > 0) {
      weatherDisplay = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").find(".weatherDisplay");
      temporaryData = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
    } else {
      weatherDisplay = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".weatherDisplay");
      temporaryData = this.ALLBoxData[this.selectBox];
    }

    weatherDisplay.html('');
    var _temporaryData$saveWe = temporaryData.saveWeather,
        cityInfo = _temporaryData$saveWe.cityInfo,
        data = _temporaryData$saveWe.data,
        time = _temporaryData$saveWe.time;
    this.weatherCity = cityInfo.city;
    this.weatherCode = cityInfo.citykey;
    this.weatherHtml = "\n                    \u4ECA\u5929:".concat(time, "<br>\n                    \u57CE\u5E02:").concat(this.weatherCity, "<br>\n                    \u7A7A\u6C14\u8D28\u91CF:").concat(data.quality, "<br>\n                    \u5B9E\u65F6\u6E29\u5EA6:").concat(data.wendu, "\xBAC<br>\n                    \u6700\u9AD8:").concat(data.forecast[0].high, "<br>\n                    \u6700\u4F4E:").concat(data.forecast[0].low, "<br>\n                    \u98CE\u5411:").concat(data.forecast[0].fx, "<br>\n                    \u98CE\u529B:").concat(data.forecast[0].fl);
    weatherDisplay.append(this.weatherHtml);

    if (isSet != false) {
      console.log('setWeather');
      this.getThumbnail();
    }
  },

  /**
   * 修改节目尺寸值 对相对应的dom 移动  X轴和Y轴的位置
   */
  modifyXOrY: function modifyXOrY() {
    if (this.ALLBoxData[this.selectBox].left > (0, _jQuery["default"])(".canvasBox").width() - (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).outerWidth(true)) {
      this.ALLBoxData[this.selectBox].left = 0;
    } else if (this.ALLBoxData[this.selectBox].left < 0) {
      this.ALLBoxData[this.selectBox].left = (0, _jQuery["default"])(".canvasBox").width() - (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).outerWidth(true);
    }

    if (this.ALLBoxData[this.selectBox].top > (0, _jQuery["default"])(".canvasBox").height() - (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).outerHeight(true)) {
      this.ALLBoxData[this.selectBox].top = 0;
    } else if (this.ALLBoxData[this.selectBox].top < 0) {
      this.ALLBoxData[this.selectBox].top = (0, _jQuery["default"])(".canvasBox").height() - (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).outerHeight(true);
    }

    (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css({
      'left': this.ALLBoxData[this.selectBox].left + 'px',
      'top': this.ALLBoxData[this.selectBox].top + 'px'
    });
    console.log('modifyXOrY');
    this.getThumbnail();
  },

  /**
   * 修改节目尺寸值 对相对应的dom 修改  X轴和Y轴宽及高的位置
   */
  modifyWidthOrHeight: function modifyWidthOrHeight() {
    var then, data, textbig, textbigImg, tempData, reg;
    return regeneratorRuntime.async(function modifyWidthOrHeight$(_context3) {
      while (1) {
        switch (_context3.prev = _context3.next) {
          case 0:
            then = this;
            data = then.getDataByMulti(then);
            textbig = data.textbig;
            textbigImg = data.textbigImg;
            tempData = data.tempData; // console.log(this.assemblyProp)
            // console.log(then.ALLBoxData)
            // console.log(temporaryData)

            if (tempData) {
              reg = /^[1-9][0-9]*$/;

              if (!reg.test(then.ALLBoxData[then.selectBox].width)) {
                then.ALLBoxData[then.selectBox].width = parseInt(then.ALLBoxData[then.selectBox].width);
              }

              tempData.width = parseInt(then.ALLBoxData[then.selectBox].width);

              if (then.ALLBoxData[then.selectBox].width > (0, _jQuery["default"])(".canvasBox").width() - then.ALLBoxData[then.selectBox].left) {
                // then.ALLBoxData[then.selectBox].width = $(".canvasBox").width() - then.ALLBoxData[then.selectBox].left - 2
                then.ALLBoxData[then.selectBox].left = 0;

                if (then.ALLBoxData[then.selectBox].width > (0, _jQuery["default"])(".canvasBox").width()) {
                  then.ALLBoxData[then.selectBox].width = parseInt((0, _jQuery["default"])(".canvasBox").width());
                }
              }

              if (this.ALLBoxData[this.selectBox].height > (0, _jQuery["default"])(".canvasBox").height() - this.ALLBoxData[this.selectBox].top) {
                this.ALLBoxData[this.selectBox].top = 0;

                if (this.ALLBoxData[this.selectBox].height > (0, _jQuery["default"])(".canvasBox").height()) {
                  this.ALLBoxData[this.selectBox].height = parseInt((0, _jQuery["default"])(".canvasBox").height());
                }
              }

              if (this.assemblyProp == "simulationClock") {
                this.setSimulation();
                this.type = 'simulationClock';
              }

              if (this.assemblyProp == "text" || this.assemblyProp == "multiWindow") {
                if (tempData.textRadio == '3') {
                  textbigImg.empty();

                  if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
                    textbig.html(tempData.TextHtml_m);
                    window.editor.setHtml(tempData.TextHtml_m);
                  } else {
                    textbig.html(tempData.TextHtml);
                    window.editor.setHtml(tempData.TextHtml);
                  }
                } else if (tempData.textRadio == '2') {
                  if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
                    then.newText(tempData.TextHtml_m);
                  } else {
                    then.newText(tempData.TextHtml);
                  }
                }

                this.type = 'text';
              }

              (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css({
                'top': this.ALLBoxData[this.selectBox].top + 'px',
                'left': this.ALLBoxData[this.selectBox].left + 'px',
                'width': this.ALLBoxData[this.selectBox].width + 'px',
                'height': this.ALLBoxData[this.selectBox].height + 'px'
              }); // console.log('modifyWidthOrHeight')
              // this.getThumbnail();
            }

          case 6:
          case "end":
            return _context3.stop();
        }
      }
    }, null, this);
  },

  /**
   * 设置文本自体移动速度
   */
  setTextSpeed: function setTextSpeed() {
    var then = this;
    var data = then.getDataByMulti(then);
    var textbig = data.textbig;
    var tempData = data.tempData;
    tempData.textRadio = '2';
    clearInterval(tempData.textspeedRoll); //直接使用计时器会有100毫秒延迟  先渲染再计时

    if (tempData.effect == 'right to left') {
      // 向左
      var leftPtext = tempData.width;
      textbig.css({
        "left": leftPtext + "px",
        "line-height": textbig.height() + "px",
        "top": "0"
      });

      if (leftPtext <= -textbig.width()) {
        leftPtext = tempData.width;
      }

      leftPtext--;
      tempData.textspeedRoll = setInterval(function () {
        textbig.css({
          "left": leftPtext + "px",
          "line-height": textbig.height() + "px",
          "top": "0"
        });

        if (leftPtext <= -textbig.width()) {
          leftPtext = tempData.width;
        }

        leftPtext--;
      }, tempData.textSpeed);
    } else if (tempData.effect == 'left to right') {
      // 向右
      var _leftPtext = -tempData.width;

      textbig.css({
        "left": _leftPtext + "px",
        "line-height": textbig.height() + "px",
        "top": "0"
      });

      if (_leftPtext >= textbig.width()) {
        _leftPtext = -tempData.width;
      }

      _leftPtext++;
      tempData.textspeedRoll = setInterval(function () {
        textbig.css({
          "left": _leftPtext + "px",
          "line-height": textbig.height() + "px",
          "top": "0"
        });

        if (_leftPtext >= textbig.width()) {
          _leftPtext = -tempData.width;
        }

        _leftPtext++;
      }, tempData.textSpeed);
    } else if (tempData.effect == 'bottom to top') {
      // 向上
      var heightPtext = tempData.height;
      textbig.css({
        "top": heightPtext + "px",
        "left": "0"
      });

      if (heightPtext <= -textbig.height()) {
        heightPtext = tempData.height;
      }

      heightPtext--;
      tempData.textspeedRoll = setInterval(function () {
        textbig.css({
          "top": heightPtext + "px",
          "left": "0"
        });

        if (heightPtext <= -textbig.height()) {
          heightPtext = tempData.height;
        }

        heightPtext--;
      }, tempData.textSpeed);
    } else if (tempData.effect == 'top to bottom') {
      // 向下
      var _heightPtext = -tempData.height;

      textbig.css({
        "top": _heightPtext + "px",
        "left": "0"
      });

      if (_heightPtext >= textbig.height()) {
        _heightPtext = -tempData.height;
      }

      _heightPtext++;
      tempData.textspeedRoll = setInterval(function () {
        textbig.css({
          "top": _heightPtext + "px",
          "left": "0"
        });

        if (_heightPtext >= textbig.height()) {
          _heightPtext = -tempData.height;
        }

        _heightPtext++;
      }, tempData.textSpeed);
    }
  },

  /**
   * 修改播放属性
   * @param {*} val
   * @param {*} selectBox 当前选中的数据
   */
  setEffectChange: function setEffectChange(val, selectBox) {
    // console.log(val)
    this.setTextZY(selectBox);
  },

  /**
   * 设置文本类型
   * @param {*} e 静态 1 滚动 2 翻页 3
   * @param {*} self
   * @param {*} isSet
   */
  textFormat: function textFormat(e, self, isSet) {
    var then, temporaryTaget, temporaryData, temporaryTagetImg, temporaryTagetPage, temporaryTagetStatic, temporaryTagetRoll, multiWindow, pbox, allPboxSum, i, _pbox, _allPboxSum;

    return regeneratorRuntime.async(function textFormat$(_context4) {
      while (1) {
        switch (_context4.prev = _context4.next) {
          case 0:
            then = this;
            this.textstatic = e;
            multiWindow = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).children(".multiWindow");

            if (!(multiWindow.length > 0)) {
              _context4.next = 31;
              break;
            }

            temporaryTaget = multiWindow.find(".textbig_m");
            temporaryTagetPage = multiWindow.find(".page_m");
            temporaryTagetStatic = multiWindow.find(".static_m");
            temporaryTagetRoll = multiWindow.find(".roll_m");
            temporaryTagetImg = multiWindow.find(".textbig_m img");
            temporaryData = then.ALLBoxData[self].multiWindowData[then.windowPage];

            if (!(e == "2")) {
              _context4.next = 28;
              break;
            }

            temporaryData.preview = true;
            temporaryData.runSpeedShow = true;
            temporaryData.runPageShow = false;
            temporaryData.textRadio == '2';

            if (temporaryTagetPage.length > 0) {
              temporaryTagetPage.attr('class', 'roll_m');
              temporaryTagetPage.attr('class', temporaryData.selectText_m);
            } else if (temporaryTagetStatic.length > 0) {
              temporaryTagetStatic.attr('class', 'roll_m');
              temporaryTagetStatic.attr('class', temporaryData.selectText_m);
            }

            temporaryTagetImg.remove();
            temporaryTaget.html(temporaryData.TextHtml_m);
            _context4.next = 20;
            return regeneratorRuntime.awrap(then.setTextZY(self));

          case 20:
            if (then.ALLBoxData[self].type == "multiWindow") {
              pbox = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).children(".multiWindow").find(".textbig_m p");
            } else {
              pbox = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".textbig p");
            }

            allPboxSum = 0;

            for (i = 0; i < pbox.length; i++) {
              allPboxSum += Number(pbox[i].offsetWidth);
            }

            if (allPboxSum >= temporaryData.width) {
              temporaryData.TextWidth = allPboxSum;
            } else {
              temporaryData.TextWidth = temporaryData.width;
            }

            temporaryData.curchange_m = 'roll_m';
            temporaryTaget.css({
              "width": temporaryData.TextWidth
            });
            _context4.next = 29;
            break;

          case 28:
            if (e == "1") {
              temporaryData.preview = false;
              temporaryData.runSpeedShow = false;
              temporaryData.runPageShow = false;
              temporaryData.textRadio == '1';

              if (temporaryTagetPage.length > 0) {
                temporaryTagetPage.attr('class', 'static_m');
                temporaryTagetPage.attr('class', temporaryData.selectText_m);
              } else if (temporaryTagetRoll.length > 0) {
                temporaryTagetRoll.attr('class', 'static_m');
                temporaryTagetRoll.attr('class', temporaryData.selectText_m);
              }

              temporaryTaget.css({
                "line-height": "normal"
              });
              temporaryData.curchange_m = 'static_m';
              temporaryTaget.empty();
              temporaryTaget.html(temporaryData.TextHtml_m);
              then.setTextStatic();
            } else if (e == "3") {
              temporaryData.preview = false;
              temporaryData.runSpeedShow = false;
              temporaryData.runPageShow = true;
              temporaryData.textRadio == '3';

              if (temporaryTagetStatic.length > 0) {
                temporaryTagetStatic.attr('class', 'page_m');
                temporaryTagetStatic.attr('class', temporaryData.selectText_m);
              } else if (temporaryTagetRoll.length > 0) {
                temporaryTagetRoll.attr('class', 'page_m');
                temporaryTagetRoll.attr('class', temporaryData.selectText_m);
              }

              if (temporaryData.TextHtml_m.length > 0) {
                temporaryData.curchange_m = 'page_m';
                temporaryTaget.css({
                  "line-height": "normal"
                });
                then.computePageText(temporaryData);

                if (temporaryData.pageTextList && temporaryData.pageTextList.length > 0) {
                  temporaryTaget.html(temporaryData.pageTextList[temporaryData.ImagePage - 1]);
                }
              }
            }

          case 29:
            _context4.next = 56;
            break;

          case 31:
            temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".textbig");
            temporaryTagetPage = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".page");
            temporaryTagetStatic = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".static");
            temporaryTagetRoll = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".roll");
            temporaryTagetImg = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".textbig img");
            temporaryData = then.ALLBoxData[self];

            if (!(e == "2")) {
              _context4.next = 55;
              break;
            }

            // 滚动
            temporaryData.preview = true;
            temporaryData.runSpeedShow = true;
            temporaryData.runPageShow = false;
            temporaryData.textRadio == '2';

            if (temporaryTagetPage.length > 0) {
              temporaryTagetPage.attr('class', 'roll');
              temporaryTagetPage.addClass("".concat(temporaryData.selectText));
            } else if (temporaryTagetStatic.length > 0) {
              temporaryTagetStatic.attr('class', 'roll');
              temporaryTagetStatic.addClass("".concat(temporaryData.selectText));
            }

            temporaryTagetImg.remove();
            temporaryTaget.html(temporaryData.TextHtml);
            _context4.next = 47;
            return regeneratorRuntime.awrap(then.setTextZY(self));

          case 47:
            if (then.ALLBoxData[self].type == "multiWindow") {
              _pbox = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).children(".multiWindow").find(".textbig_m p");
            } else {
              _pbox = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".textbig p");
            }

            _allPboxSum = 0;

            for (i = 0; i < _pbox.length; i++) {
              _allPboxSum += Number(_pbox[i].offsetWidth);
            }

            if (_allPboxSum >= temporaryData.width) {
              temporaryData.TextWidth = _allPboxSum;
            } else {
              temporaryData.TextWidth = temporaryData.width;
            }

            temporaryData.curchange = 'roll';
            temporaryTaget.css({
              "width": temporaryData.TextWidth
            });
            _context4.next = 56;
            break;

          case 55:
            if (e == "1") {
              // 静态
              temporaryData.preview = false;
              temporaryData.runSpeedShow = false;
              temporaryData.runPageShow = false;
              temporaryData.textRadio == '1';

              if (temporaryTagetPage.length > 0) {
                temporaryTagetPage.attr('class', 'static');
              } else if (temporaryTagetRoll.length > 0) {
                temporaryTagetRoll.attr('class', 'static');
              }

              temporaryTaget.css({
                "line-height": "normal"
              });
              temporaryData.curchange = 'static';
              temporaryTaget.empty();
              temporaryTaget.html(temporaryData.TextHtml);
              then.setTextStatic();
            } else if (e == "3") {
              // 翻页
              temporaryData.preview = false;
              temporaryData.runSpeedShow = false;
              temporaryData.runPageShow = true;
              temporaryData.textRadio == '3';

              if (temporaryTagetStatic.length > 0) {
                temporaryTagetStatic.attr('class', 'page');
                temporaryTagetStatic.addClass("".concat(temporaryData.selectText));
              } else if (temporaryTagetRoll.length > 0) {
                temporaryTagetRoll.attr('class', 'page');
                temporaryTagetRoll.addClass("".concat(temporaryData.selectText));
              }

              if (temporaryData.TextHtml.length > 0) {
                temporaryTaget.css({
                  "line-height": "normal"
                });
                temporaryData.curchange = 'page';
                then.computePageText(temporaryData);

                if (temporaryData.pageTextList && temporaryData.pageTextList.length > 0) {
                  temporaryTaget.html(temporaryData.pageTextList[temporaryData.ImagePage - 1]);
                }
              }
            }

          case 56:
            if (isSet != false) {
              console.log('textFormat');
              this.getThumbnail();
            }

          case 57:
          case "end":
            return _context4.stop();
        }
      }
    }, null, this);
  },

  /**
   * 输入文本内容
   * @param {*} newHtml
   */
  newText: function newText(newHtml) {
    var then = this;
    var temporaryData, temporaryTaget;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
      temporaryData = then.ALLBoxData[then.selectBox].multiWindowData[then.windowPage];
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").find(".textbig_m");
      temporaryData.TextHtml_m = newHtml;
      temporaryTaget.html(temporaryData.TextHtml_m);
    } else {
      temporaryData = then.ALLBoxData[then.selectBox];
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".textbig");
      temporaryData.TextHtml = newHtml;
      temporaryTaget.html(temporaryData.TextHtml);
    }

    then.cleartextSpeed();

    if (temporaryData.textRadio == '2') {
      temporaryData.curchange_m = 'roll_m';
      temporaryData.curchange = 'roll';
      setTimeout(function () {
        then.textFormat('2', then.selectBox, false);
      }, 200);
    } else if (temporaryData.textRadio == '1') {
      // 静态文本
      temporaryData.curchange_m = 'static_m';
      temporaryData.curchange = 'static';
      then.setTextStatic(); // then.cleartextSpeed()
    } else if (temporaryData.textRadio == '3') {
      temporaryData.curchange_m = 'page_m';
      temporaryData.curchange = 'page';
      then.computePageText(temporaryData);

      if (temporaryData.pageTextList && temporaryData.pageTextList.length > 0) {
        temporaryTaget.html(temporaryData.pageTextList[temporaryData.ImagePage - 1]);
      }
    }

    console.log('newText');
    this.getThumbnail();
  },
  computePageText: function computePageText(temporaryData) {
    /**
     * 将数据根据不同标签拆分[{key: "left", value:[{label: "other", style: "", text: "骄傲是"}, 
     * {label: "span,em,strong", style: "font-size: 15px; font-family: 楷体;", text: "快乐"}]}, 
     * {key: "right", value:[{label: "other", style: "", text: "阿斯达数据库里的"}]}]
     */
    var textBigMap = this.splitHtmlByHtmlHandle(temporaryData);
    /**
     * 对文本进行分页处理
     */

    this.pageHandleByList(temporaryData, textBigMap);

    if (temporaryData.pageTextList && temporaryData.pageTextList.length > 0) {
      temporaryData.ImagePage = temporaryData.pageTextList.length;
    }
  },

  /**
   * 将html根据不同标签进行拆分
   * @param {*} temporaryData 
   * @returns [{key: "left", value:[{label: "other", style: "", text: "骄傲是"}, 
   * {label: "span,em,strong", style: "font-size: 15px; font-family: 楷体;", text: "快乐"}]}, 
   * {key: "right", value:[{label: "other", style: "", text: "阿斯达数据库里的"}]}]
   * 一个{}，表示一个P标签， key为文本对齐方式，value为具体的数据
   * {label: other（表示没有加标签修饰），style为使用的样式，text为具体文本内容}
   */
  splitHtmlByHtmlHandle: function splitHtmlByHtmlHandle(temporaryData) {
    var res = []; // 所有文本数组

    var pList = []; // 初始化文本为<p><br></p>multiWindow

    var multiWindow = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow");
    var p = [];

    if (multiWindow.length > 0) {
      p = (0, _jQuery["default"])("#scrolltext_m" + temporaryData.selectText_m).find("p");
    } else {
      p = (0, _jQuery["default"])("#scrolltext" + temporaryData.selectText).find("p");
    }

    if (p.length > 0) {
      // 取出所有的文本
      for (var i = 0; i < p.length; i++) {
        var element = p[i];
        pList.push(element.innerHTML);
      }

      if (pList.length > 0) {
        var _loop = function _loop(_i) {
          // 默认左对齐
          var textAlign = p[_i].style.textAlign ? p[_i].style.textAlign : "left";
          var pItem = [];
          var valueList = []; // 初始化文本为<p><br></p>

          var elementP = pList[_i];

          if (elementP != "<br>") {
            // 临时文本
            var tempText = ""; // 拆分

            _toConsumableArray(elementP).forEach(function (item, index) {
              tempText += item; // 如果是<开头说明是标签

              if (tempText.substring(0, 1) == "<") {
                // 如果到了>说明到了标签的结尾
                if (item == ">") {
                  if (pItem.length > 0) {
                    // 如果上一个也是标签做拼接
                    if (pItem[pItem.length - 1].substring(0, 1) == "<") {
                      // 都为尾标签时
                      if (pItem[pItem.length - 1].substring(1, 2) == "/" && tempText.substring(1, 2) == "/") {
                        pItem[pItem.length - 1] += tempText;
                      } else if (pItem[pItem.length - 1].substring(1, 2) != "/" && tempText.substring(1, 2) != "/") {
                        // 都为头标签时
                        pItem[pItem.length - 1] += tempText;
                      } else {
                        pItem.push(tempText);
                      }
                    } else {
                      pItem.push(tempText);
                    }
                  } else {
                    pItem.push(tempText);
                  }

                  tempText = "";
                }
              } else {
                // 如果到<说明到了文本的结尾
                if (item == "<") {
                  pItem.push(tempText.substring(0, tempText.length - 1)); // 保留最后一个<

                  tempText = tempText.substring(tempText.length - 1, tempText.length);
                }
              }
            });

            if (tempText) {
              pItem.push(tempText);
            }
          } // 将文本根据不同标签进行重组 [{label: "span,em,strong", style: "font-size: 24px; font-family: Arial;", text: "静安"},{label: "other", style: "", text: "寺看"}]


          if (pItem.length > 0) {
            tempPItem = ""; // 取出文本进行处理

            label = "other";
            style = "";
            text = "";

            for (var j = 0; j < pItem.length; j++) {
              var elementPItem = pItem[j]; // 标签的结尾

              if (elementPItem.substring(0, 2) == "</") {
                tempPItem = "";
                valueList.push({
                  "label": label,
                  "style": style,
                  "text": pItem[j - 1]
                });
                label = "other";
                style = "";
              } else if (elementPItem.substring(0, 1) == "<") {
                // 标签的开头
                tempPItem = elementPItem; // 说明当前标签拥有样式

                if (elementPItem.indexOf("style=") != -1) {
                  var styleList = elementPItem.split("style=");
                  label = styleList[0].substring(1, styleList[0].length - 1); // 固定格式<span style=""><em><strong>只会在span中加入style，所以styleList.length == 2

                  if (styleList[1].indexOf("><") != -1) {
                    style = styleList[1].substring(1, styleList[1].indexOf(">") - 1);
                    var tempLabel = styleList[1].substring(styleList[1].indexOf(">"), styleList[1].length - 1); // ><em tempLabel.split("><").join(",")

                    tempLabel = tempLabel.replaceAll("><", ",");
                    label += tempLabel;
                  } else {
                    // 去除最后一个">和前面的"
                    style = styleList[1].substring(1, styleList[1].length - 2);
                  }
                } else {
                  // 当前标签没有样式
                  if (elementPItem.indexOf("><") != -1) {
                    // 如果存在><说明拥有多个标签
                    label = elementPItem.replace("><", ",");
                    label = label.substring(1, label.length - 1);
                  } else {
                    // 只有一个标签
                    label = elementPItem.substring(1, elementPItem.length - 1);
                  }
                }
              } else {
                // 文本
                // 如果为空说明是普通文本直接push
                if (tempPItem == "") {
                  text = elementPItem;
                  valueList.push({
                    "label": label,
                    "style": style,
                    "text": text
                  });
                  text = "";
                }
              }
            }

            if (valueList.length > 0) {
              // res.push({key: textAlign, value: valueList})
              // 每一个p标签设置的对齐方式
              valueList[0].label = "text-align:" + textAlign + "," + valueList[0].label;
              res = res.concat(valueList);
            }
          }
        };

        // 拆分成如下格式：["按时间看到了骄", "<span style="font-family: 标楷体;">", "<strong>傲深", "</strong>", "</span>刻理解"]
        for (var _i = 0; _i < pList.length; _i++) {
          var tempPItem;
          var label;
          var style;
          var text;

          _loop(_i);
        }
      }
    }

    return res;
  },

  /**
   * 对文本进行分页处理
   * @param {*} temporaryData 
   * @param {*} textBigMap [{label: "other", style: "", text: "骄傲是"}, 
   * {label: "span,em,strong", style: "font-size: 15px; font-family: 楷体;", text: "快乐"}]
   */
  pageHandleByList: function pageHandleByList(temporaryData, textBigMap) {
    var _this3 = this;

    if (textBigMap.length > 0) {
      // 初始化数组，防止push报错
      temporaryData.pageTextList = []; // 文本框的总高度和宽度

      var multiWindow = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow");
      var scrolltext = [];

      if (multiWindow.length > 0) {
        scrolltext = (0, _jQuery["default"])("#scrolltext_m" + temporaryData.selectText_m);
      } else {
        scrolltext = (0, _jQuery["default"])("#scrolltext" + temporaryData.selectText);
      }

      var width = scrolltext[0].offsetWidth;
      var height = scrolltext[0].offsetHeight; // 字体默认为14

      var fontSize = 14; // 当前行的高度  乘1.5的行高

      var lineHeight = 14 * 1.5; // 字体样式

      var face = ""; // 加粗，斜体

      var font = ""; // bold、oblique

      var color = "";
      var bgColor = "";
      var tempMaxWidth = 0;
      var textItemValue = [];
      var lineNum = 1; // 算出每段文本所在的行号位置，进行拆分记录行号

      for (var i = 0; i < textBigMap.length; i++) {
        var textBigItem = textBigMap[i]; // 重置属性

        fontSize = 14;
        lineHeight = 14 * 1.5;
        face = "";
        font = "";
        color = "";
        bgColor = ""; // let headLable = "text-align:left"
        // 记录对齐方式，如果需要翻页时拆分是需要记录头

        if (textBigItem.label.indexOf("text-align:") == 0) {
          // headLable = textBigItem.label.split(",")[0];
          if (i != 0) {
            // 如果是text-align: 开头说明为P标签需要换行
            lineNum++;
            tempMaxWidth = 0;
          }
        } // 当前文本拥有样式


        if (textBigItem.label.indexOf("span") != -1) {
          var styleList = textBigItem.style.split(";");

          for (var j = 0; j < styleList.length; j++) {
            var styleItem = styleList[j];

            if (styleItem != "") {
              var style = styleItem.split(":"); // 字体大小

              if (style[0].indexOf("font-size") != -1) {
                fontSize = parseInt(style[1]); // 乘1.5的行高

                lineHeight = fontSize * 1.5;
              } else if (style[0].indexOf("font-family") != -1) {
                face = this.trim(style[1]);
              } else if (style[0].indexOf("color") != -1 && style[0].indexOf("-") == -1) {
                color = this.trim(style[1]);
              } else if (style[0].indexOf("background-color") != -1) {
                bgColor = this.trim(style[1]);
              }
            }
          }
        } // 当前文本加入斜体样式


        if (textBigItem.label.indexOf("em") != -1) {
          font += " oblique";
        } // 当前文本加入加粗样式


        if (textBigItem.label.indexOf("strong") != -1) {
          font += " bold";
        }

        var drawTextList = []; // 截取文本，如果没有叠加宽度说明在一行的开头

        if (tempMaxWidth == 0) {
          drawTextList = this.drawText(textBigItem.text, width, fontSize, face, font);
        } else {
          drawTextList = this.drawText(textBigItem.text, width - tempMaxWidth, fontSize, face, font, width);
        } // 如果数组不为空说明当前文本需要拆分


        if (drawTextList.length > 0) {
          var itemWidth = 0;

          for (var _j = 0; _j < drawTextList.length; _j++) {
            var drawTextItem = drawTextList[_j];
            itemWidth = this.measureText(drawTextItem, fontSize, face, font);

            if (_j != 0) {
              lineNum++;
            }

            var labelTemp = textBigItem.label;

            if (_j != 0) {
              if (labelTemp.indexOf("text-align:") == 0) {
                labelTemp = labelTemp.substring(labelTemp.indexOf(",") + 1, labelTemp.length);
              }

              if (textItemValue.length > 0) {
                // 说明前面只有一行，需要截取
                if (textItemValue.length == 1) {
                  var tempTextItemLabel = textItemValue[0].label.substring(textItemValue[0].label.indexOf(",") + 1, textItemValue[0].label.length);

                  if (labelTemp == tempTextItemLabel || textBigItem.label == tempTextItemLabel.substring(1)) {
                    labelTemp = '&' + labelTemp;
                  }
                } else {
                  if (labelTemp == textItemValue[textItemValue.length - 1].label) {
                    labelTemp = '&' + labelTemp;
                  }
                }
              }
            }

            textItemValue.push({
              label: labelTemp,
              lineHeight: lineHeight,
              width: itemWidth,
              lineNum: lineNum,
              text: drawTextItem,
              font: font,
              fontSize: fontSize,
              face: face,
              color: color,
              bgColor: bgColor,
              style: textBigItem.style
            });
          } // 最后一行叠加宽度方便计算下一行


          tempMaxWidth = itemWidth;
        } else {
          tempMaxWidth += this.measureText(textBigItem.text, fontSize, face, font);

          if (textItemValue.length > 0) {
            // 说明前面只有一行，需要截取
            if (textItemValue.length == 1) {
              var _tempTextItemLabel = textItemValue[0].label.substring(textItemValue[0].label.indexOf(",") + 1, textItemValue[0].label.length);

              if (textBigItem.label == _tempTextItemLabel || textBigItem.label == _tempTextItemLabel.substring(1)) {
                textBigItem.label = '&' + textBigItem.label;
              }
            } else {
              if (textBigItem.label == textItemValue[textItemValue.length - 1].label) {
                textBigItem.label = '&' + textBigItem.label;
              }
            }
          }

          textItemValue.push({
            label: textBigItem.label,
            lineHeight: lineHeight,
            width: this.measureText(textBigItem.text, fontSize, face, font),
            lineNum: lineNum,
            text: textBigItem.text,
            font: font,
            fontSize: fontSize,
            face: face,
            color: color,
            bgColor: bgColor,
            style: textBigItem.style
          });
        }
      }
      /**
       * 得到最终每一行的文本
       *  [{label: "text-align:left,span", style: "color: rgb(250, 250, 250); font-size: 22px;", text: "撒娇", lineHeight: 33, width: 44, lineNum: 1 },
       *  {label: "other", style: "", text: "地", lineHeight: 21, width: 14, lineNum: 1 },
       *  {label: "span,strong", style: "font-size: 19px;", text: "看了按时间", lineHeight: 28.5, width: 95, lineNum: 1 },
       *  {label: "other", lineHeight: 21, lineNum: 1, text: "看来大家安徽的健康回", width: 140 },
       *  {label: "other", lineHeight: 21, width: 42, lineNum: 2, text: "家撒谎"}...]
       */
      // 将文本根据行进行分组并算出最大行高


      var tempDataArr = [];

      if (textItemValue.length > 0) {
        textItemValue.map(function (mapItem) {
          if (tempDataArr.length == 0) {
            tempDataArr.push({
              lineNum: mapItem.lineNum,
              lineHeight: mapItem.lineHeight,
              list: [mapItem]
            });
          } else {
            var res = tempDataArr.some(function (item) {
              //判断相同行，有就添加到当前项
              if (item.lineNum == mapItem.lineNum) {
                item.list.push(mapItem);
                item.lineHeight = _this3.countMax(item.list, 'lineHeight');
                return true;
              }
            });

            if (!res) {
              //如果没找相同行添加一个新对象
              tempDataArr.push({
                lineNum: mapItem.lineNum,
                lineHeight: mapItem.lineHeight,
                list: [mapItem]
              });
            }
          }
        });
      } // 超出行数进行数组拆分


      var maxHeightTemp = 0;
      var pageTextItem = [];
      var tempPageTextList = [];

      if (tempDataArr.length > 0) {
        for (var _i2 = 0; _i2 < tempDataArr.length; _i2++) {
          var tempDataItem = tempDataArr[_i2]; // 大于如果大于行高，再判断是否超过字体大小的高度

          if (tempDataItem.lineHeight + maxHeightTemp < height || tempDataItem.lineHeight / 1.5 + 2 + maxHeightTemp < height) {
            maxHeightTemp += tempDataItem.lineHeight;
            pageTextItem = pageTextItem.concat(tempDataItem.list);
          } else {
            if (pageTextItem.length > 0) {
              // 判断是否有头标签
              if (tempPageTextList.length > 0) {
                if (pageTextItem[0].label.indexOf("text-align:") != 0) {
                  if (tempPageTextList[tempPageTextList.length - 1][0].label.indexOf("text-align:") == 0) {
                    pageTextItem[0].label = tempPageTextList[tempPageTextList.length - 1][0].label.split(",")[0] + "," + pageTextItem[0].label;
                  }
                }
              }

              tempPageTextList.push(pageTextItem);
              pageTextItem = tempDataItem.list;
              maxHeightTemp = 0;
              maxHeightTemp += tempDataItem.lineHeight;
            }
          }
        }
      }

      if (pageTextItem.length > 0) {
        // 判断是否有头标签
        if (tempPageTextList.length > 0) {
          if (pageTextItem[0].label.indexOf("text-align:") != 0) {
            if (tempPageTextList[tempPageTextList.length - 1][0].label.indexOf("text-align:") == 0) {
              pageTextItem[0].label = tempPageTextList[tempPageTextList.length - 1][0].label.split(",")[0] + "," + pageTextItem[0].label;
            }
          }
        }

        tempPageTextList.push(pageTextItem);
        maxHeightTemp = 0;
        pageTextItem = [];
      } // 将数组中的数据转为html


      var tempItemHtml = "";
      var endFlag = false;
      var tempLabelEnd = "";

      if (tempPageTextList.length > 0) {
        for (var _i3 = 0; _i3 < tempPageTextList.length; _i3++) {
          var tempPageItem = tempPageTextList[_i3];

          if (tempPageItem.length > 0) {
            for (var _j2 = 0; _j2 < tempPageItem.length; _j2++) {
              var tempPageValue = tempPageItem[_j2]; // 拼接html头部

              var labelTempList = tempPageValue.label.split(",");

              if (tempPageValue.label.indexOf("&") != 0) {
                if (tempLabelEnd) {
                  var labelTempEndList = tempLabelEnd.split(",");

                  for (var k = labelTempEndList.length - 1; k >= 0; k--) {
                    var labelTempItem = labelTempEndList[k];

                    if (labelTempItem.indexOf("other") != 0) {
                      tempItemHtml += "</".concat(labelTempItem, ">");
                    }
                  }

                  tempLabelEnd = "";
                }

                if (tempPageValue.label.indexOf("text-align:") == 0) {
                  endFlag = true;
                  tempLabelEnd = tempPageValue.label.substring(tempPageValue.label.indexOf(",") + 1, tempPageValue.label.length);

                  if (tempItemHtml) {
                    tempItemHtml += "</p>";
                  }

                  tempItemHtml += "<p style=\"".concat(labelTempList[0], ";\">");

                  for (var _k = 1; _k < labelTempList.length; _k++) {
                    var _labelTempItem = labelTempList[_k];

                    if (_labelTempItem.indexOf("other") == -1 && _labelTempItem.indexOf("span") == -1) {
                      tempItemHtml += "<".concat(_labelTempItem, ">");
                    } else if (_labelTempItem.indexOf("span") != -1) {
                      if (tempPageValue.style) {
                        tempItemHtml += "<".concat(_labelTempItem, " style=\"").concat(tempPageValue.style, "\">");
                      }
                    }
                  }
                } else {
                  for (var _k2 = 0; _k2 < labelTempList.length; _k2++) {
                    var _labelTempItem2 = labelTempList[_k2];

                    if (_labelTempItem2.indexOf("other") == -1 && _labelTempItem2.indexOf("span") == -1) {
                      tempItemHtml += "<".concat(_labelTempItem2, ">");
                    } else if (_labelTempItem2.indexOf("span") != -1) {
                      if (tempPageValue.style) {
                        tempItemHtml += "<".concat(_labelTempItem2, " style=\"").concat(tempPageValue.style, "\">");
                      }
                    }
                  }
                }
              }

              tempItemHtml += tempPageValue.text;

              if (tempPageValue.label.indexOf("&") != 0) {
                if (tempPageValue.label.indexOf("text-align:") != 0) {
                  for (var _k3 = labelTempList.length - 1; _k3 >= 0; _k3--) {
                    var _labelTempItem3 = labelTempList[_k3];

                    if (_labelTempItem3.indexOf("other") != 0) {
                      tempItemHtml += "</".concat(_labelTempItem3, ">");
                    }
                  }
                }
              }

              if (_j2 == tempPageItem.length - 1 && endFlag) {
                if (tempLabelEnd) {
                  var _labelTempEndList = tempLabelEnd.split(",");

                  for (var _k4 = _labelTempEndList.length - 1; _k4 >= 0; _k4--) {
                    var _labelTempItem4 = _labelTempEndList[_k4];

                    if (_labelTempItem4.indexOf("other") != 0) {
                      tempItemHtml += "</".concat(_labelTempItem4, ">");
                    }
                  }

                  tempLabelEnd = "";
                }

                tempItemHtml += "</p>";
                endFlag = false;
              }
            }

            if (tempItemHtml) {
              temporaryData.pageTextList.push(tempItemHtml);
              tempItemHtml = "";
            }
          }
        }
      } // console.log(temporaryData.pageTextList)

    }
  },

  /**
   * 找数组中某一个属性的最大值
   * @param {*} arr 对比的数组
   * @param {*} key 对比的属性
   * @returns 
   */
  countMax: function countMax(arr, key) {
    return Math.max.apply(Math, arr.map(function (item) {
      return item[key];
    }));
  },

  /**
   * 更改为单行文本滚动
   * @param {*} self
   */
  setTextZY: function setTextZY(self) {
    var then, temporaryTaget, temporaryTagetBox, temporaryTagetP, multiWindow;
    return regeneratorRuntime.async(function setTextZY$(_context5) {
      while (1) {
        switch (_context5.prev = _context5.next) {
          case 0:
            then = this;
            multiWindow = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).children(".multiWindow");

            if (multiWindow.length > 0) {
              temporaryTaget = multiWindow.find(".textbig_m");
              temporaryTagetP = multiWindow.find(".textbig_m p");
              temporaryTagetBox = multiWindow.find("#textbigbox_m");
            } else {
              temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".textbig");
              temporaryTagetP = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find(".textbig p");
              temporaryTagetBox = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[self]).find("#textbigbox");
            }

            temporaryTaget.css({
              "white-space": "nowrap",
              "height": temporaryTagetBox.height() + "px",
              "line-height": temporaryTagetBox.height() + "px"
            });

            if (then.ALLBoxData[then.selectBox].effect == 'bottom to top' || then.ALLBoxData[then.selectBox].effect == 'top to bottom') {
              temporaryTaget.css({
                "line-height": "normal"
              });
            }

            if (then.ALLBoxData[then.selectBox].multiWindowData && then.ALLBoxData[then.selectBox].multiWindowData.length > 0) {
              if (then.ALLBoxData[then.selectBox].multiWindowData[then.windowPage].type == 'text') {
                if (then.ALLBoxData[then.selectBox].multiWindowData[then.windowPage].effect == 'bottom to top' || then.ALLBoxData[then.selectBox].multiWindowData[then.windowPage].effect == 'top to bottom') {
                  temporaryTaget.css({
                    "line-height": "normal"
                  });
                }
              }
            } // temporaryTagetP.css({
            //     "display": "inline"
            // })


            _context5.next = 8;
            return regeneratorRuntime.awrap(then.setTextWidth());

          case 8:
          case "end":
            return _context5.stop();
        }
      }
    }, null, this);
  },

  /**
   * 更新单行文本的宽度
   */
  setTextWidth: function setTextWidth() {
    var then, data, textbig, tempData;
    return regeneratorRuntime.async(function setTextWidth$(_context6) {
      while (1) {
        switch (_context6.prev = _context6.next) {
          case 0:
            then = this;
            data = then.getDataByMulti(then);
            textbig = data.textbig;
            tempData = data.tempData;
            setTimeout(function () {
              textbig.css({
                "width": tempData.TextWidth
              });
            });

          case 5:
          case "end":
            return _context6.stop();
        }
      }
    }, null, this);
  },

  /**
   * 修改图片页数
   */
  changeImagePage: function changeImagePage() {
    this.changeSelect2();
  },
  changeSelect2: function changeSelect2() {
    var then = this;
    var tempData, textbig;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
      tempData = then.ALLBoxData[then.selectBox].multiWindowData[then.windowPage];
      textbig = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").find(".textbig_m"); // textbig.html(`<img  src="${tempData.showWndImage_m[tempData.ImagePage - 1]}" alt="">`)

      if (tempData.pageTextList && tempData.pageTextList.length > 0) {
        textbig.html(tempData.pageTextList[tempData.ImagePage - 1]);
      }
    } else {
      tempData = then.ALLBoxData[then.selectBox];
      textbig = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".textbig"); // textbig.html(`<img  src="${tempData.showWndImage[tempData.ImagePage - 1]}" alt="">`)

      if (tempData.pageTextList && tempData.pageTextList.length > 0) {
        textbig.html(tempData.pageTextList[tempData.ImagePage - 1]);
      }
    }
  },

  /**
   * 更改单行文本静态
   */
  setTextStatic: function setTextStatic() {
    var then = this;
    var data = then.getDataByMulti(then);
    var textbig = data.textbig;
    var tempData = data.tempData;
    clearInterval(tempData.textspeedRoll);
    textbig.css({
      "white-space": "normal",
      "word-break": "break-all",
      "left": "0px",
      // "width": tempData.width,
      "width": "100%",
      "height": tempData.textRadio == 1 ? "99%" : "",
      "overflow-y": "hidden" // "line-height": "normal"

    });
  },

  /**
   * 更改滚动速度
   */
  setRollingSpeed: function setRollingSpeed() {
    var _this4 = this;

    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;
    clearInterval(tempData.textRoll);

    if (tempData.assePropData.position == 4) {
      var leftPx = this.ALLBoxData[this.selectBox].width; //直接使用计时器会有100毫秒延迟  先渲染再计时

      assemblyPropDisplay.css({
        "left": leftPx + "px"
      });
      leftPx--;
      var tagetBox = this.selectBox;
      tempData.textRoll = setInterval(function () {
        assemblyPropDisplay.css({
          "left": leftPx + "px"
        });

        if (leftPx <= -assemblyPropDisplay.width()) {
          leftPx = _this4.ALLBoxData[tagetBox].width;
        }

        leftPx--;
      }, tempData.assePropData.RollingSpeed);
    }
  },

  /**
   * 清除文本移动计时器
   */
  cleartextSpeed: function cleartextSpeed() {
    var then = this;
    var data = then.getDataByMulti(then);
    var textbig = data.textbig;
    var tempData = data.tempData;
    textbig.css({
      "left": "0px"
    });
    clearInterval(tempData.textspeedRoll);
  },
  //////////////////////////////////////环境检测///////////////////////////////////////////////

  /**
   * 将所有字的宽度设置为最宽字体宽度
   * @param {*} longWidth 需要设置的文本宽度和高度
   */
  setEnvironmentalWidth: function setEnvironmentalWidth(longWidth) {
    var assemblyPropDisplayDivList = document.getElementsByClassName("assemblyPropDisplayDiv");

    for (var i = 0; i < assemblyPropDisplayDivList.length; i++) {
      var element = assemblyPropDisplayDivList[i];
      var span = element.getElementsByTagName("span");

      if (span.length > 0) {
        span[0].style.width = longWidth.titleWidth + "px";
        span[1].style.width = longWidth.unitWidth + "px";
      }
    }
  },

  /**
   * 环境检测找最长的标题和单位长度
   */
  findEnvironmentalLongWidth: function findEnvironmentalLongWidth() {
    var assemblyPropDisplayDivList = document.getElementsByClassName("assemblyPropDisplayDiv");
    var titleWidth = 0;
    var unitWidth = 0;

    for (var i = 0; i < assemblyPropDisplayDivList.length; i++) {
      var element = assemblyPropDisplayDivList[i];
      var span = element.getElementsByTagName("span");

      if (span.length > 0) {
        if (span[0].clientWidth > titleWidth) {
          titleWidth = span[0].clientWidth;
        }

        if (span[1].clientWidth > unitWidth) {
          unitWidth = span[1].clientWidth;
        }
      }
    }

    return {
      titleWidth: Number(titleWidth) + 2,
      unitWidth: unitWidth
    };
  },

  /**
   * 环境检测设置标题文本
   */
  setTitleInputText: function setTitleInputText() {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;
    assemblyPropDisplay.find("p:eq(0)").text(tempData.assePropData.title);
    console.log('setTitleInputText');
    this.getThumbnail();
  },

  /**
   * 环境检测字段的显示隐藏
   */
  setPBoxDisplay: function setPBoxDisplay(isSet) {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;

    if (tempData.assePropData.display["标题"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(0)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(0)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(0)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["温度"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(1)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(1)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(1)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["湿度"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(2)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(2)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(2)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["风速"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(4)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(4)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(4)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["风向"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(5)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(5)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(5)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["噪音"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(3)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(3)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(3)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["PM2.5"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(7)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(7)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(7)").css({
        "display": "none"
      });
    }

    if (tempData.assePropData.display["PM10"]) {
      if (tempData.assePropData.position == 4) {
        assemblyPropDisplay.find("div:eq(6)").css({
          "display": "inline"
        });
      } else {
        assemblyPropDisplay.find("div:eq(6)").css({
          "display": ""
        });
      }
    } else {
      assemblyPropDisplay.find("div:eq(6)").css({
        "display": "none"
      });
    }

    if (isSet != false) {
      console.log('setPBoxDisplay');
      this.getThumbnail();
    }
  },

  /**
   * 更改刷新周期
    setRefreshCycle() {
      let then = this
      let data = then.getDataByMulti(then)
      let assemblyPropDisplay = data.assemblyPropDisplay
      let tempData = data.tempData
      console.log(tempData)
  },*/

  /**
   * 更改播放时长
    setPlaybackDuration() {
      let then = this
      let data = then.getDataByMulti(then)
      let assemblyPropDisplay = data.assemblyPropDisplay
      let tempData = data.tempData
      console.log(tempData)
  },*/

  /**
   * 环境检测摄氏度和华氏度转换
   */
  setTemperatureNum: function setTemperatureNum(isSet) {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;

    if (tempData.assePropData.temperatureNum == "1") {
      assemblyPropDisplay.find("div:eq(1)").find("span:eq(1)").find("span:eq(1)").css({
        "display": "none"
      });
      assemblyPropDisplay.find("div:eq(1)").find("span:eq(1)").find("span:eq(0)").css({
        "display": "inline"
      });
    } else {
      assemblyPropDisplay.find("div:eq(1)").find("span:eq(1)").find("span:eq(0)").css({
        "display": "none"
      });
      assemblyPropDisplay.find("div:eq(1)").find("span:eq(1)").find("span:eq(1)").css({
        "display": "inline"
      });
    }

    if (isSet != false) {
      console.log('setTemperatureNum');
      this.getThumbnail();
    }
  },

  /**
   * 更改环境检测节目标题
   * @param {*} item
   */
  setDataInput: function setDataInput(item) {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;

    if (item == "temperature") {
      // 温度
      assemblyPropDisplay.find("div:eq(1)").find("span:eq(0)").text(tempData.assePropData.temperature + ":");
    } else if (item == "humidity") {
      // 湿度
      assemblyPropDisplay.find("div:eq(2)").find("span:eq(0)").text(tempData.assePropData.humidity + ":");
    } else if (item == "noise") {
      // 噪音
      assemblyPropDisplay.find("div:eq(3)").find("span:eq(0)").text(tempData.assePropData.noise + ":");
    } else if (item == "windSpeed") {
      // 风速
      assemblyPropDisplay.find("div:eq(4)").find("span:eq(0)").text(tempData.assePropData.windSpeed + ":");
    } else if (item == "windDirection") {
      //风向
      assemblyPropDisplay.find("div:eq(5)").find("span:eq(0)").text(tempData.assePropData.windDirection + ":");
    } else if (item == "pm10") {
      assemblyPropDisplay.find("div:eq(6)").find("span:eq(0)").text(tempData.assePropData.Pm10 + ":");
    } else if (item == "pm25") {
      assemblyPropDisplay.find("div:eq(7)").find("span:eq(0)").text(tempData.assePropData.Pm25 + ":");
    }

    assemblyPropDisplay.find("div").find("span:eq(0)").css({
      "width": ""
    });
    assemblyPropDisplay.find("div").find("span:eq(0)").css({
      "display": "inline-block",
      "text-align": "right"
    });
    var longWidth = this.findEnvironmentalLongWidth();
    this.setEnvironmentalWidth(longWidth);
    console.log('setDataInput');
    this.getThumbnail();
  },

  /**
   * 环境监测展示方式
   */
  setAlignPosition: function setAlignPosition(isSet) {
    var _this5 = this;

    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;

    if (tempData.assePropData.position == 1) {
      clearInterval(tempData.textRoll);
      assemblyPropDisplay.css({
        "text-align": "left",
        "white-space": "",
        "line-height": "",
        "width": "",
        "left": ""
      });
      this.resetEnvironmentalSpanCss(assemblyPropDisplay);
      var longWidth = this.findEnvironmentalLongWidth();
      this.setEnvironmentalWidth(longWidth);
    } else if (tempData.assePropData.position == 2) {
      clearInterval(tempData.textRoll);
      assemblyPropDisplay.css({
        "text-align": "center",
        "white-space": "",
        "line-height": "",
        "width": "",
        "left": ""
      });
      this.resetEnvironmentalSpanCss(assemblyPropDisplay);
      var longWidth = this.findEnvironmentalLongWidth();
      this.setEnvironmentalWidth(longWidth);
    } else if (tempData.assePropData.position == 3) {
      clearInterval(tempData.textRoll);
      assemblyPropDisplay.css({
        "text-align": "right",
        "white-space": "",
        "line-height": "",
        "width": "",
        "left": ""
      });
      this.resetEnvironmentalSpanCss(assemblyPropDisplay);
      var longWidth = this.findEnvironmentalLongWidth();
      this.setEnvironmentalWidth(longWidth);
    } else if (tempData.assePropData.position == 4) {
      clearInterval(tempData.textRoll);
      assemblyPropDisplay.css({
        "white-space": "nowrap",
        "text-align": "left",
        "line-height": this.ALLBoxData[this.selectBox].height + "px",
        "width": "auto"
      });
      assemblyPropDisplay.find("div").css({
        "display": "inline"
      });
      assemblyPropDisplay.find("div").find("span").css({
        "width": ""
      });
      assemblyPropDisplay.find("div").find("p").css({
        "display": "inline-block"
      }); // let leftPx = assemblyPropDisplay.width()

      var leftPx = this.ALLBoxData[this.selectBox].width;
      console.log(leftPx); //直接使用计时器会有100毫秒延迟  先渲染再计时

      assemblyPropDisplay.css({
        "left": leftPx + "px"
      });
      leftPx--;
      tempData.textRoll = setInterval(function () {
        assemblyPropDisplay.css({
          "left": leftPx + "px"
        });

        if (leftPx <= -assemblyPropDisplay.width()) {
          leftPx = _this5.ALLBoxData[_this5.selectBox].width;
        }

        leftPx--;
      }, tempData.assePropData.RollingSpeed);
    }

    this.setPBoxDisplay(isSet); // console.log('setAlignPosition')
    // this.getThumbnail();
  },

  /**
   * 重置环境检测标题和单位的样式
   */
  resetEnvironmentalSpanCss: function resetEnvironmentalSpanCss(assemblyPropDisplay) {
    assemblyPropDisplay.find("div").find("span:eq(0)").css({
      "display": "inline-block",
      "text-align": "right",
      "width": ""
    });
    assemblyPropDisplay.find("div").find("span:eq(1)").css({
      "display": "inline-block",
      "text-align": "left",
      "width": ""
    });
  },

  /**
   * 设置环境检测字体大小
   */
  setEnvironmentalFontSize: function setEnvironmentalFontSize(isSet) {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;
    assemblyPropDisplay.css({
      "font-size": tempData.fontSize
    }); // 修改完字体大小需要重置一下样式，如果是单行滚动不需要重置

    if (tempData.assePropData.position != 4) {
      this.resetEnvironmentalSpanCss(assemblyPropDisplay);
      var longWidth = this.findEnvironmentalLongWidth();
      this.setEnvironmentalWidth(longWidth);
    }

    if (isSet != false) {
      console.log('setEnvironmentalFontSize');
      this.getThumbnail();
    }
  },

  /**
   * 更改环境监测文本颜色
   */
  setEnvironmentalColor: function setEnvironmentalColor(isSet) {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;
    assemblyPropDisplay.css({
      "color": tempData.textColor
    });

    if (isSet != false) {
      console.log('setEnvironmentalColor');
      this.getThumbnail();
    }
  },

  /**
   * 空转文本加粗倾斜下划线的方法
   */
  setEnvironmentalTextStyle: function setEnvironmentalTextStyle(isSet) {
    var then = this;
    var data = then.getDataByMulti(then);
    var assemblyPropDisplay = data.assemblyPropDisplay;
    var tempData = data.tempData;

    if (tempData.textStyle.b) {
      assemblyPropDisplay.css({
        "font-weight": 700
      });
    } else {
      assemblyPropDisplay.css({
        "font-weight": 500
      });
    }

    if (tempData.textStyle.i) {
      assemblyPropDisplay.css({
        "font-style": "oblique"
      });
    } else {
      assemblyPropDisplay.css({
        "font-style": "normal "
      });
    }

    if (tempData.textStyle.u) {
      assemblyPropDisplay.css({
        "text-decoration": "underline"
      });
      assemblyPropDisplay.find("div").find("span:eq(0)").css({
        "text-decoration": "underline"
      });
      assemblyPropDisplay.find("div").find("span:eq(1)").css({
        "text-decoration": "underline"
      });
    } else {
      assemblyPropDisplay.css({
        "text-decoration": "none"
      });
      assemblyPropDisplay.find("div").find("span:eq(0)").css({
        "text-decoration": "none"
      });
      assemblyPropDisplay.find("div").find("span:eq(1)").css({
        "text-decoration": "none"
      });
    }

    if (isSet != false) {
      console.log('setEnvironmentalTextStyle');
      this.getThumbnail();
    }
  },

  /**
   * 文字模版添加文本数据
   */
  clickAddText: function clickAddText() {//console.log('ssss')
  },
  // TODO

  /**
   * 添加文字模版的背景图
   * @param {*} media 媒体数据
   */
  addTextTemplateImgDom: function addTextTemplateImgDom(media) {
    var html = "";

    if (media.duration) {
      this.ALLBoxData[this.selectBox].PlaybackDuration = media.duration;
    } else {
      this.ALLBoxData[this.selectBox].PlaybackDuration = 10;
    }

    if (media.suffix == 'jpeg' || media.suffix == 'png' || media.suffix == 'jpg' || media.suffix == 'jfif') {
      html = "<img src=\"".concat(_httpRequest["default"].adornUrl("/sys/program/file/download/") + media.fileId, "\" style=\"width:100%;display:block;pointer-events:none;\"></img>");
    }

    (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".textTemplate").append(html);
  },

  /**
   * 添加媒体文件
   * @param {*} media
    addFindDom(media) {
      this.ALLBoxData[this.selectBox].media = media
      let html = ``
      if (media.duration) {
          this.ALLBoxData[this.selectBox].PlaybackDuration = media.duration
      } else {
          this.ALLBoxData[this.selectBox].PlaybackDuration = 10
      }
      if (media.suffix == 'mp4') {
          html = `<video src="${httpRequest.adornUrl(`/sys/program/file/download/`) + media.fileId}"
                      style='object-fit:fill;width:100%;height:100%;' autoplay loop muted>
                  </video>`
      } else if (media.suffix == 'jpeg' || media.suffix == 'png' || media.suffix == 'jfif' || media.suffix == 'jpg' || media.suffix == 'gif') {
          html = `<img src="${httpRequest.adornUrl(`/sys/program/file/download/`) + media.fileId}" style="width:100%;height:100%;display:block;pointer-events:none;"></img>`
      }else if (media.suffix == 'mp3') {
          html = `<img src="${audioPng}" style="width:100%;height:100%;display:block;pointer-events:none;"></img>`
      }
      $($(".mediaTZbox")[this.selectBox]).children(".media").append(html)
  },*/

  /**
   * 数字时钟 numberTime改变
   */
  setNumberTime: function setNumberTime(isSet) {
    var then = this;
    var CurrentItem;
    var currentWimdowIndex = this.windowPage;
    var DQselectBox = this.selectBox;
    var temporaryTaget, temporaryData, option;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).children(".multiWindow").length > 0) {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).children(".multiWindow").find(".DigitalClock");
      temporaryData = this.ALLBoxData[DQselectBox].multiWindowData[this.windowPage];
      CurrentItem = this.ALLBoxData[DQselectBox].multiWindowData[this.windowPage].numberTime;
      option = this.ALLBoxData[DQselectBox].multiWindowData[this.windowPage].Rqfg;
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).find(".DigitalClock");
      temporaryData = this.ALLBoxData[DQselectBox];
      CurrentItem = this.ALLBoxData[this.selectBox].numberTime;
      option = this.ALLBoxData[this.selectBox].Rqfg;
    } // console.log(CurrentItem.indexOf("上午/下午"));
    // console.log(CurrentItem.indexOf("12小時制"));


    if (CurrentItem.indexOf("上午/下午") != -1 && CurrentItem.indexOf("12小时制") == -1 || CurrentItem.indexOf("上午/下午") != -1 && CurrentItem.indexOf("12小時制") == -1) {
      CurrentItem.push("12小时制");
      CurrentItem.push("12小時制");
    } else if (CurrentItem.indexOf("AM/PM") != -1 && CurrentItem.indexOf("12 hours system") == -1) {
      CurrentItem.push("12 hours system");
    }

    if (option == "1970/01/02" || option == "1970/1/2" || option == "1970年/01月/02日" || option == "1970年/1月/2日") {
      if (CurrentItem.indexOf("年") == -1 || CurrentItem.indexOf("Year") == -1) {
        clearInterval(temporaryData.SJitv0);
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").hide();
      } else if (CurrentItem.indexOf("年") != -1 && CurrentItem.indexOf("四位年") == -1 || CurrentItem.indexOf("Year") != -1 && CurrentItem.indexOf("SiWei years") == -1) {
        // console.log("siweinian")
        clearInterval(temporaryData.SJitv0);
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").show();
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", DQselectBox, currentWimdowIndex).substr(2, 3)));
        temporaryData.SJitv0 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", DQselectBox, currentWimdowIndex).substr(2, 3)));
        }, 1000);
      } else {
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", DQselectBox, currentWimdowIndex)));
        clearInterval(temporaryData.SJitv0);
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").show();
        temporaryData.SJitv0 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", DQselectBox, currentWimdowIndex)));
        }, 1000);
      }

      if (CurrentItem.indexOf("月") == -1 || CurrentItem.indexOf("Month") == -1) {
        clearInterval(temporaryData.SJitv2);
        temporaryTaget.find("div:eq(0)").find("span:eq(1)").hide();
      } else {
        (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".DigitalClock").find("div:eq(0)").find("span:eq(1)").show();
        temporaryData.SJitv2 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(1)").text("".concat(then.getNYR("y", DQselectBox, currentWimdowIndex)));
        }, 1000);
      }

      if (CurrentItem.indexOf("日") == -1 || CurrentItem.indexOf("Day") == -1) {
        clearInterval(temporaryData.SJitv3);
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").hide();
      } else {
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").show();
        clearInterval(temporaryData.SJitv3);
        temporaryData.SJitv3 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(2)").text("".concat(then.getNYR("r", DQselectBox, currentWimdowIndex)));
        }, 1000);
      }

      if (CurrentItem.indexOf("年") == -1 || CurrentItem.indexOf("月") == -1 && CurrentItem.indexOf("日") == -1 || CurrentItem.indexOf("Year") == -1 || CurrentItem.indexOf("Month") == -1 && CurrentItem.indexOf("Day") == -1) {
        //清除/分隔符逻辑
        temporaryTaget.find("div:eq(0)").find("b:eq(0)").hide();
      } else {
        temporaryTaget.find("div:eq(0)").find("b:eq(0)").show();
      }

      if (CurrentItem.indexOf("月") == -1 || CurrentItem.indexOf("日") == -1 || CurrentItem.indexOf("Month") == -1 || CurrentItem.indexOf("Day") == -1) {
        temporaryTaget.find("div:eq(0)").find("b:eq(1)").hide();
      } else {
        temporaryTaget.find("div:eq(0)").find("b:eq(1)").show();
      }
    } else {
      if (CurrentItem.indexOf("年") == -1 && then.getNYR("r", DQselectBox, currentWimdowIndex).length >= 4 || CurrentItem.indexOf("Year") == -1 && then.getNYR("r", DQselectBox, currentWimdowIndex).length >= 4) {
        clearInterval(temporaryData.SJitv3);
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").hide();
      } else if (CurrentItem.indexOf("年") != -1 && CurrentItem.indexOf("四位年") == -1 || CurrentItem.indexOf("Year") != -1 && CurrentItem.indexOf("SiWei years") == -1) {
        clearInterval(temporaryData.SJitv3);
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").show();
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").text("".concat(then.getNYR("r", DQselectBox, currentWimdowIndex).substr(2, 3)));
        temporaryData.SJitv3 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(2)").find("div:eq(0)").text("".concat(then.getNYR("r", DQselectBox, currentWimdowIndex).substr(2, 3)));
        }, 1000);
      } else {
        clearInterval(temporaryData.SJitv3);
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").show();
        temporaryTaget.find("div:eq(0)").find("span:eq(2)").text("".concat(then.getNYR("r", DQselectBox, currentWimdowIndex)));
        temporaryData.SJitv3 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(2)").text("".concat(then.getNYR("r", DQselectBox, currentWimdowIndex)));
        }, 1000);
      }

      if (CurrentItem.indexOf("月") == -1 && then.getNYR("r", DQselectBox, currentWimdowIndex).length >= 4 || CurrentItem.indexOf("Month") == -1 && then.getNYR("r", DQselectBox, currentWimdowIndex).length >= 4) {
        clearInterval(temporaryData.SJitv0);
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").hide();
      } else {
        clearInterval(temporaryData.SJitv0);
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").show();
        temporaryTaget.find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", DQselectBox, currentWimdowIndex)));
        temporaryData.SJitv0 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(0)").find("div:eq(0)").text("".concat(then.getNYR("n", DQselectBox, currentWimdowIndex)));
        }, 1000);
      }

      if (CurrentItem.indexOf("日") == -1 && then.getNYR("r", DQselectBox, currentWimdowIndex).length >= 4 || CurrentItem.indexOf("Day") == -1 && then.getNYR("r", DQselectBox, currentWimdowIndex).length >= 4) {
        clearInterval(temporaryData.SJitv2);
        temporaryTaget.find("div:eq(0)").find("span:eq(1)").hide();
      } else {
        clearInterval(temporaryData.SJitv2);
        temporaryTaget.find("div:eq(0)").find("span:eq(1)").show();
        temporaryTaget.find("div:eq(0)").find("span:eq(1)").text("".concat(then.getNYR("y", DQselectBox, currentWimdowIndex)));
        temporaryData.SJitv2 = setInterval(function () {
          temporaryTaget.find("div:eq(0)").find("span:eq(1)").text("".concat(then.getNYR("y", DQselectBox, currentWimdowIndex)));
        }, 1000);
      }

      if (CurrentItem.indexOf("年") == -1 || CurrentItem.indexOf("月") == -1 && CurrentItem.indexOf("日") == -1 || CurrentItem.indexOf("Year") == -1 || CurrentItem.indexOf("Month") == -1 && CurrentItem.indexOf("Day") == -1) {
        temporaryTaget.find("div:eq(0)").find("b:eq(1)").hide();
      } else {
        temporaryTaget.find("div:eq(0)").find("b:eq(1)").show();
      }

      if (CurrentItem.indexOf("月") == -1 || CurrentItem.indexOf("日") == -1 || CurrentItem.indexOf("Month") == -1 || CurrentItem.indexOf("Day") == -1) {
        temporaryTaget.find("div:eq(0)").find("b:eq(0)").hide();
      } else {
        temporaryTaget.find("div:eq(0)").find("b:eq(0)").show();
      }
    }

    if (CurrentItem.indexOf("时") == -1 || CurrentItem.indexOf("Hour") == -1 || CurrentItem.indexOf("時") == -1) {
      clearInterval(temporaryData.SJitv4);
      temporaryTaget.find("div:eq(4)").find("span:eq(0)").hide();
    } else if (CurrentItem.indexOf("时") != -1 && CurrentItem.indexOf("12小时制") != -1 || CurrentItem.indexOf("Hour") != -1 && CurrentItem.indexOf("12 hours system") != -1 || CurrentItem.indexOf("時") != -1 && CurrentItem.indexOf("12小時制") != -1) {
      // console.log("12小時制");
      temporaryTaget.find("div:eq(4)").find("span:eq(0)").show();
      var item2 = "";

      if (then.getSFM("s", DQselectBox, currentWimdowIndex) > 12) {
        if (then.getSFM("s", DQselectBox, currentWimdowIndex) < 22 && temporaryData.Sjfg == "HH:MM:SS") {
          item2 = "0" + (then.getSFM("s", DQselectBox, currentWimdowIndex) - 12);
        } else {
          item2 = then.getSFM("s", DQselectBox, currentWimdowIndex) - 12;
        }
      } else if (then.getSFM("s", DQselectBox, currentWimdowIndex) == "0") {
        item2 = "12";
      } else {
        item2 = then.getSFM("s", DQselectBox, currentWimdowIndex);
      }

      temporaryTaget.find("div:eq(4)").find("span:eq(0)").text("".concat(item2));
      clearInterval(temporaryData.SJitv4);
      var item = "";
      temporaryData.SJitv4 = setInterval(function () {
        if (then.getSFM("s", DQselectBox, currentWimdowIndex) > 12) {
          if (then.getSFM("s", DQselectBox, currentWimdowIndex) < 22 && temporaryData.Sjfg == "HH:MM:SS") {
            item = "0" + (then.getSFM("s", DQselectBox, currentWimdowIndex) - 12);
          } else {
            item = then.getSFM("s", DQselectBox, currentWimdowIndex) - 12;
          }
        } else if (then.getSFM("s", DQselectBox, currentWimdowIndex) == "0") {
          item = "12";
        } else {
          item = then.getSFM("s", DQselectBox, currentWimdowIndex);
        }

        temporaryTaget.find("div:eq(4)").find("span:eq(0)").text("".concat(item));
      }, 100);
    } else {
      temporaryTaget.find("div:eq(4)").find("span:eq(0)").show();
      temporaryTaget.find("div:eq(4)").find("span:eq(0)").text("".concat(then.getSFM("s", DQselectBox, currentWimdowIndex)));
      clearInterval(temporaryData.SJitv4);
      temporaryData.SJitv4 = setInterval(function () {
        temporaryTaget.find("div:eq(4)").find("span:eq(0)").text("".concat(then.getSFM("s", DQselectBox, currentWimdowIndex)));
      }, 100);
    }

    if (CurrentItem.indexOf("分") == -1 || CurrentItem.indexOf("Minute") == -1) {
      clearInterval(temporaryData.SJitv5);
      temporaryTaget.find("div:eq(4)").find("span:eq(1)").hide();
    } else {
      temporaryTaget.find("div:eq(4)").find("span:eq(1)").show();
      temporaryData.SJitv5 = setInterval(function () {
        temporaryTaget.find("div:eq(4)").find("span:eq(1)").text("".concat(then.getSFM("f", DQselectBox, currentWimdowIndex)));
      }, 100);
    }

    if (CurrentItem.indexOf("秒") == -1 || CurrentItem.indexOf("Second") == -1) {
      clearInterval(temporaryData.SJitv6);
      temporaryTaget.find("div:eq(4)").find("span:eq(2)").hide();
    } else {
      temporaryTaget.find("div:eq(4)").find("span:eq(2)").show();
      temporaryData.SJitv6 = setInterval(function () {
        temporaryTaget.find("div:eq(4)").find("span:eq(2)").text("".concat(then.getSFM("m", DQselectBox, currentWimdowIndex)));
      }, 100);
    }

    if (CurrentItem.indexOf("星期") == -1 || CurrentItem.indexOf("Week") == -1) {
      clearInterval(temporaryData.SJitv7);
      temporaryTaget.find("div:eq(3)").find("span:eq(0)").hide();
    } else {
      temporaryTaget.find("div:eq(3)").find("span:eq(0)").show();
      temporaryData.SJitv7 = setInterval(function () {
        temporaryTaget.find("div:eq(3)").find("span:eq(0)").text("".concat(then.getDay(DQselectBox, currentWimdowIndex)));
      }, 1000);
    }

    if (CurrentItem.indexOf("时") == -1 || CurrentItem.indexOf("分") == -1 && CurrentItem.indexOf("秒") == -1 || CurrentItem.indexOf("Hour") == -1 || CurrentItem.indexOf("Minute") == -1 && CurrentItem.indexOf("Second") == -1 || CurrentItem.indexOf("時") == -1 || CurrentItem.indexOf("分") == -1 && CurrentItem.indexOf("秒") == -1) {
      temporaryTaget.find("div:eq(4)").find("b:eq(0)").hide();
    } else {
      temporaryTaget.find("div:eq(4)").find("b:eq(0)").show();
    }

    if (CurrentItem.indexOf("分") == -1 || CurrentItem.indexOf("秒") == -1 || CurrentItem.indexOf("Minute") == -1 || CurrentItem.indexOf("Second") == -1) {
      temporaryTaget.find("div:eq(4)").find("b:eq(1)").hide();
    } else {
      temporaryTaget.find("div:eq(4)").find("b:eq(1)").show();
    }

    if (CurrentItem.indexOf("上午/下午") != -1 || CurrentItem.indexOf("AM/PM") != -1) {
      temporaryTaget.find("div:eq(4)").find(".isVcbl").show();

      if (then.getSFM("s", DQselectBox, currentWimdowIndex) > 12) {
        // console.log(CurrentItem.indexOf("AM/PM"))
        // console.log(CurrentItem.indexOf("上午/下午"))
        temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".SW").hide();
        temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".XW").show();
      } else {
        // console.log(CurrentItem.indexOf("AM/PM"))
        // console.log(CurrentItem.indexOf("上午/下午"))
        temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".SW").show();
        temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".XW").hide();
      }

      temporaryData.SJitv8 = setInterval(function () {
        if (then.getSFM("s", DQselectBox, currentWimdowIndex) > 12) {
          temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".SW").hide();
          temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".XW").show();
        } else {
          temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".SW").show();
          temporaryTaget.find("div:eq(4)").find(".isVcbl").find(".XW").hide();
        }
      }, 1000);
    } else {
      // console.log(CurrentItem.indexOf("AM/PM"))
      // console.log(CurrentItem.indexOf("上午/下午"))
      clearInterval(temporaryData.SJitv8);
      temporaryTaget.find("div:eq(4)").find(".isVcbl").hide();
    }

    if (isSet != false) {
      console.log('setNumberTime');
      this.getThumbnail();
    }
  },

  /**
   * 更改数字时钟文本颜色
   */
  setDigitalClockColor: function setDigitalClockColor(isSet) {
    var temporaryTaget, temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").length > 0) {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").find(".DigitalClock");
      temporaryData = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".DigitalClock");
      temporaryData = this.ALLBoxData[this.selectBox];
    }

    temporaryTaget.css({
      "color": temporaryData.textColor
    });

    if (isSet != false) {
      console.log('setDigitalClockColor');
      this.getThumbnail();
    }
  },

  /**
   * 显示方式改变  多行还是单行
   */
  setSingleLine: function setSingleLine(isSet) {
    var DQselectBox = this.selectBox;
    var temporaryTaget, temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).children(".multiWindow").length > 0) {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).children(".multiWindow").find(".DigitalClock");
      temporaryData = this.ALLBoxData[DQselectBox].multiWindowData[this.windowPage];
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).find(".DigitalClock");
      temporaryData = this.ALLBoxData[DQselectBox];
    }

    if (temporaryData.isSingleLine == "1") {
      temporaryTaget.css({
        "flex-direction": "row",
        "justify-content": "center",
        "line-height": (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[DQselectBox]).height() + "px"
      });
      temporaryTaget.find("div:gt(2)").css({
        "margin-left": "5px"
      });
    }

    if (temporaryData.isSingleLine == "2") {
      temporaryTaget.css({
        "flex-direction": "column",
        "justify-content": "center",
        "line-height": 1
      });
      temporaryTaget.find("div:gt(2)").css({
        "margin-left": "0"
      });
    }

    if (isSet != false) {
      console.log('setSingleLine');
      this.getThumbnail();
    }
  },

  /**
   * /更改天气字体大小
   */
  monitorWeatherSize: function monitorWeatherSize(isSet) {
    var temporaryTaget, temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").length > 0) {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow");
      temporaryData = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]);
      temporaryData = this.ALLBoxData[this.selectBox];
    }

    temporaryTaget.find(".weatherDisplay").css({
      "font-size": temporaryData.fontSize
    });

    if (isSet != false) {
      console.log('monitorWeatherSize');
      this.getThumbnail();
    }
  },

  /**
   * 更改数字时钟文本字体大小
   */
  setSizeDX: function setSizeDX(isSet) {
    var temporaryTaget, temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").length > 0) {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").find(".DigitalClock");
      temporaryData = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".DigitalClock");
      temporaryData = this.ALLBoxData[this.selectBox];
    }

    temporaryTaget.css({
      "font-size": temporaryData.fontSize
    });

    if (isSet != false) {
      console.log('setSizeDX');
      this.getThumbnail();
    }
  },

  /**
   * 更改数字时钟文本样式 加粗 斜体 下划线
   */
  setTextStyle: function setTextStyle(isSet) {
    var temporaryTaget, temporaryData;

    if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").length > 0) {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).children(".multiWindow").find(".DigitalClock");
      temporaryData = this.ALLBoxData[this.selectBox].multiWindowData[this.windowPage];
    } else {
      temporaryTaget = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).find(".DigitalClock");
      temporaryData = this.ALLBoxData[this.selectBox];
    }

    if (temporaryData.textStyle.b) {
      temporaryTaget.css({
        "font-weight": 700
      });
    } else {
      temporaryTaget.css({
        "font-weight": 500
      });
    }

    if (temporaryData.textStyle.i) {
      temporaryTaget.css({
        "font-style": "oblique"
      });
    } else {
      temporaryTaget.css({
        "font-style": "normal "
      });
    }

    if (temporaryData.textStyle.u) {
      temporaryTaget.css({
        "text-decoration": "underline"
      });
    } else {
      temporaryTaget.css({
        "text-decoration": "none"
      });
    }

    if (isSet != false) {
      console.log('setTextStyle');
      this.getThumbnail();
    }
  },

  /**
   * 此方法创建一个新id ：b6733a9e-bd6e-40b3-834c-7e4298f352a9
   * @returns
   */
  createId: function createId() {
    function S4() {
      return ((1 + Math.random()) * 0x10000 | 0).toString(16).substring(1);
    }

    ; // Generate a pseudo-GUID by concatenating random hexadecimal.

    return S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4();
  },

  /**
   * 此方法创建一个32位id ：c582ea7f5ef9b55124f546c760b21174
   * @returns
   */
  uuid2: function uuid2() {
    var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    var uuid = [],
        i;

    for (i = 0; i < 32; i++) {
      uuid[i] = chars[0 | Math.random() * 32];
    }

    return uuid.join('');
  },

  /**
   * 清除页面中所有的计时器
   */
  clearAllTiming: function clearAllTiming() {
    var _this6 = this;

    if (this.ALLBoxData && this.ALLBoxData.length > 0) {
      this.ALLBoxData.forEach(function (item, index) {
        if (item.type == "DigitalClock") {
          // 数字时钟
          clearInterval(_this6.ALLBoxData[index].SJitv0);
          clearInterval(_this6.ALLBoxData[index].SJitv2);
          clearInterval(_this6.ALLBoxData[index].SJitv3);
          clearInterval(_this6.ALLBoxData[index].SJitv4);
          clearInterval(_this6.ALLBoxData[index].SJitv5);
          clearInterval(_this6.ALLBoxData[index].SJitv6);
          clearInterval(_this6.ALLBoxData[index].SJitv7);
        } else if (item.type == "environmental") {
          // 环境检测
          clearInterval(_this6.ALLBoxData[index].textRoll);
        } else if (item.type == "simulationClock") {
          // 模拟时钟
          clearInterval(_this6.ALLBoxData[index].simulationTime);
        } else if (item.type == "multiWindow") {
          // 多素材窗口
          if (_this6.ALLBoxData[index].multiWindowData && _this6.ALLBoxData[index].multiWindowData.length > 0) {
            _this6.ALLBoxData[index].multiWindowData.forEach(function (item, index) {
              if (item.type == "DigitalClock") {
                // 数字时钟
                clearInterval(item.SJitv0);
                clearInterval(item.SJitv2);
                clearInterval(item.SJitv3);
                clearInterval(item.SJitv4);
                clearInterval(item.SJitv5);
                clearInterval(item.SJitv6);
                clearInterval(item.SJitv7);
              } else if (item.type == "environmental") {
                // 环境检测
                clearInterval(item.textRoll);
              } else if (item.type == "simulationClock") {
                // 模拟时钟
                clearInterval(item.simulationTime);
              }
            });
          }
        }
      });
    }
  },

  /**
   * 清除指定数据的定时
   */
  clearTimingByCurrentPage: function clearTimingByCurrentPage(boxData) {
    if (boxData) {
      if (boxData.type == "DigitalClock") {
        // 数字时钟
        clearInterval(boxData.SJitv0);
        clearInterval(boxData.SJitv2);
        clearInterval(boxData.SJitv3);
        clearInterval(boxData.SJitv4);
        clearInterval(boxData.SJitv5);
        clearInterval(boxData.SJitv6);
        clearInterval(boxData.SJitv7);
      } else if (boxData.type == "environmental") {
        // 环境检测
        clearInterval(boxData.textRoll);
      } else if (boxData.type == "simulationClock") {
        // 模拟时钟
        clearInterval(boxData.simulationTime);
      } else if (boxData.type == "multiWindow") {
        // 多素材窗口
        if (boxData.multiWindowData && boxData.multiWindowData.length > 0) {
          boxData.multiWindowData.forEach(function (item, index) {
            if (item.type == "DigitalClock") {
              // 数字时钟
              clearInterval(item.SJitv0);
              clearInterval(item.SJitv2);
              clearInterval(item.SJitv3);
              clearInterval(item.SJitv4);
              clearInterval(item.SJitv5);
              clearInterval(item.SJitv6);
              clearInterval(item.SJitv7);
            } else if (item.type == "environmental") {
              // 环境检测
              clearInterval(item.textRoll);
            } else if (item.type == "simulationClock") {
              // 模拟时钟
              clearInterval(item.simulationTime);
            }
          });
        }
      }
    }
  },

  /**
   * 添加一页
   */
  addPAage: function addPAage() {
    this.clearAllTiming();
    var CurrentPageJB = {
      isEffective: false,
      StartDate: new Date(),
      EndDate: new Date(),
      PlaybackTimes: "1",
      DayOfWeekPlay: [],
      name: this.getName()
    }; //因为是后加的这一项   目前想到的是左边栏和右边栏关联 中间栏每次添加都先把之前的节目push进去  最后一个页面是看不到数据的（这里没做绑定处理）

    this.ALLBoxDataParent[this.ALLBoxDataParent.length - 1] = this.ALLBoxData;
    this.ALLBoxData = [];
    this.ALLBoxDataParent.push(this.ALLBoxData);
    (0, _jQuery["default"])(".canvasBox").html("");
    this.selectBox = 0;
    this.assemblyProp = '';
    this.attributeNav = "page";
    this.CurrentPageAll.push(CurrentPageJB);
    this.currentPage = this.CurrentPageAll.length - 1;
    this.deviationX = 0;
    this.deviationY = 0;
    this.maxHierarchy.push(0);
    this.sortXB.push([]);
    this.sortIndexXB.push(0);
  },

  /**
   * 删除选中一项
   */
  deletePage: function deletePage() {
    var _this7 = this;

    this.clearAllTiming();
    this.CurrentPageAll.splice(this.currentPage, 1);
    this.ALLBoxDataParent.splice(this.currentPage, 1);
    this.sortXB.splice(this.currentPage, 1);
    this.sortIndexXB.splice(this.currentPage, 1);
    this.currentPage = 0;
    this.ALLBoxData = this.ALLBoxDataParent[this.currentPage];

    if (this.ALLBoxData.length > 0) {
      this.ALLBoxData.forEach(function (item, index) {
        if (item.type) {
          _this7.addDom(item, false);
        }
      });
    } else {
      (0, _jQuery["default"])(".canvasBox").html("");
      this.attributeNav = "page";
      this.assemblyProp = '';
    }

    (0, _jQuery["default"])("div[class=editList] .sortable_box ")[0].click();
  },

  /**
   * 向上移动一选中页面
   */
  upItemPage: function upItemPage() {
    // $("div[class=editList] .sortable_box ")[this.currentPage].click()
    //console.log(this.currentPage)
    var pageObj = this.CurrentPageAll[this.currentPage];
    var domObj = this.ALLBoxDataParent[this.currentPage];
    var sortObj = this.sortXB[this.currentPage];
    var sortIndexObj = this.sortIndexXB[this.currentPage];
    this.CurrentPageAll.splice(this.currentPage, 1);
    this.ALLBoxDataParent.splice(this.currentPage, 1);
    this.sortXB.splice(this.currentPage, 1);
    this.sortIndexXB.splice(this.currentPage, 1);
    this.sortIndexXB.splice(this.currentPage - 1, 0, sortIndexObj);
    this.sortXB.splice(this.currentPage - 1, 0, sortObj);
    this.CurrentPageAll.splice(this.currentPage - 1, 0, pageObj);
    this.ALLBoxDataParent.splice(this.currentPage - 1, 0, domObj);
    this.currentPage--;
    this.ALLBoxData = this.ALLBoxDataParent[this.currentPage];
  },

  /**
   * 向下移动一项
   */
  downPage: function downPage() {
    // $("div[class=editList] .sortable_box ")[this.currentPage + 1].click()
    // $("div[class=editList] .sortable_box ")[this.currentPage].click()
    var pageObj = this.CurrentPageAll[this.currentPage];
    var domObj = this.ALLBoxDataParent[this.currentPage];
    var sortObj = this.sortXB[this.currentPage];
    var sortIndexObj = this.sortIndexXB[this.currentPage];
    this.CurrentPageAll.splice(this.currentPage, 1);
    this.ALLBoxDataParent.splice(this.currentPage, 1);
    this.sortXB.splice(this.currentPage, 1);
    this.sortIndexXB.splice(this.currentPage, 1);
    this.sortIndexXB.splice(this.currentPage + 1, 0, sortIndexObj);
    this.sortXB.splice(this.currentPage + 1, 0, sortObj);
    this.CurrentPageAll.splice(this.currentPage + 1, 0, pageObj);
    this.ALLBoxDataParent.splice(this.currentPage + 1, 0, domObj);
    this.currentPage++;
    this.ALLBoxData = this.ALLBoxDataParent[this.currentPage];
  },

  /**
   * 复制选中一项
   */
  copyPage: function copyPage() {
    // console.log($("div[class=editList] img"));
    this.ALLBoxDataParent[this.currentPage] = JSON.parse(JSON.stringify(this.ALLBoxData));
    this.CurrentPageAll.push(JSON.parse(JSON.stringify(this.CurrentPageAll[this.currentPage])));
    this.ALLBoxDataParent.push(JSON.parse(JSON.stringify(this.ALLBoxData)));
    this.sortIndexXB.push(JSON.parse(JSON.stringify(this.sortIndexXB[this.currentPage])));
    this.sortXB.push(JSON.parse(JSON.stringify(this.sortXB[this.currentPage])));
    this.currentPage = this.CurrentPageAll.length - 1;
  },

  /**
   * 点击切换页面
   * @param {*} e
   * @param {*} item
   */
  clickPage: function clickPage(e, item) {
    var _this8 = this;

    return regeneratorRuntime.async(function clickPage$(_context8) {
      while (1) {
        switch (_context8.prev = _context8.next) {
          case 0:
            this.clearAllTiming();
            textIndex = 0;
            multTextIndex = 100;
            this.ALLBoxDataParent[this.currentPage] = JSON.parse(JSON.stringify(this.ALLBoxData));
            this.currentPage = item;
            this.ALLBoxData = this.ALLBoxDataParent[this.currentPage];
            (0, _jQuery["default"])(".canvasBox").html("");

            if (this.ALLBoxData.length > 0) {
              this.ALLBoxData.forEach(function _callee(item, index) {
                return regeneratorRuntime.async(function _callee$(_context7) {
                  while (1) {
                    switch (_context7.prev = _context7.next) {
                      case 0:
                        _context7.next = 2;
                        return regeneratorRuntime.awrap(_this8.addDom(item, false));

                      case 2:
                        if (!(item.type == "multiWindow")) {
                          _context7.next = 7;
                          break;
                        }

                        _this8.selectBox = index;
                        _this8.windowPage = item.multiWindowData.length - 1;
                        _context7.next = 7;
                        return regeneratorRuntime.awrap(_this8.addWindowItem(item.multiWindowData[_this8.windowPage]));

                      case 7:
                      case "end":
                        return _context7.stop();
                    }
                  }
                });
              });
            } else {
              (0, _jQuery["default"])(".canvasBox").html("");
              this.attributeNav = "page";
              this.assemblyProp = '';
            }

          case 8:
          case "end":
            return _context8.stop();
        }
      }
    }, null, this);
  },

  /**
   * 使用时间拼接名称
   * @returns
   */
  getName: function getName() {
    return "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds();
  },

  /**
   * 截取缩略图
   * TODO
   */
  getThumbnail: function getThumbnail() {
    // 左侧缩略图
    var listView = document.getElementById("list_view" + this.currentPage);
    var viewWidth = listView.clientWidth;
    viewWidth = parseFloat(viewWidth);
    var viewtHeight = listView.clientHeight;
    viewtHeight = parseFloat(viewtHeight); // 左侧缩略图Canvas

    var listViewCanvas = listView.getElementsByClassName("listViewCanvas")[0]; // 编辑区域

    var canvasbox = document.getElementById("canvasbox"); // 编辑区域宽高

    var boxWidth = canvasbox.clientWidth;
    boxWidth = parseFloat(boxWidth);
    var boxHeight = canvasbox.clientHeight;
    boxHeight = parseFloat(boxHeight); // 计算缩放比例

    var widthScaling = Number(boxWidth / viewWidth);
    var heightScaling = Number(boxHeight / viewtHeight); // 所有节目数据

    var mediaTZboxList = canvasbox.getElementsByClassName("mediaTZbox");

    if (mediaTZboxList && mediaTZboxList.length > 0) {
      // 初始化canvas
      var ctx = listViewCanvas.getContext("2d");
      var w = listViewCanvas.width;
      var h = listViewCanvas.height;
      listViewCanvas.width = w;
      listViewCanvas.height = h;
      ctx.clearRect(0, 0, w, h);
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 0.5; // 遍历所有的节目素材

      for (var i = 0; i <= mediaTZboxList.length - 1; i++) {
        var left = mediaTZboxList[i].offsetLeft == 0 ? 0 : Number(mediaTZboxList[i].offsetLeft / widthScaling);
        var top = mediaTZboxList[i].offsetTop == 0 ? 0 : Number(mediaTZboxList[i].offsetTop / heightScaling);
        var width = Number((mediaTZboxList[i].offsetWidth - 2) / widthScaling);
        var height = Number((mediaTZboxList[i].offsetHeight - 2) / heightScaling); // 选中状态时

        var tzd = mediaTZboxList[i].getElementsByClassName("P_TZD");

        if (tzd && tzd.length > 0) {
          // 实线外边框
          ctx.lineWidth = 0.5;
          ctx.setLineDash([]);
          ctx.strokeRect(left, top, width, height); // 选中状态时的八个小框

          for (var j = 0; j <= tzd.length - 1; j++) {
            var leftTzd = tzd[j].offsetLeft == 0 ? 0 : Number(tzd[j].offsetLeft / widthScaling);
            var topTzd = tzd[j].offsetTop == 0 ? 0 : Number(tzd[j].offsetTop / heightScaling);
            var widthTzd = Number(tzd[j].offsetWidth / widthScaling);
            var heightTzd = Number(tzd[j].offsetHeight / heightScaling);
            var offsetLeft = 0;

            if (leftTzd < 0) {
              leftTzd = Math.abs(leftTzd);
              offsetLeft = Number(left - leftTzd);
            } else {
              offsetLeft = Number(left + leftTzd);
            }

            var offsetTop = 0;

            if (topTzd < 0) {
              topTzd = Math.abs(topTzd);
              offsetTop = Number(top - topTzd);
            } else {
              offsetTop = Number(top + topTzd);
            }

            ctx.strokeRect(offsetLeft, offsetTop, widthTzd, heightTzd);
          }
        } else {
          // 未选中状态时，画虚线框
          ctx.lineWidth = 1;
          ctx.setLineDash([1]);
          ctx.strokeRect(left, top, width, height);
        }

        console.log(mediaTZboxList[i]); // 根据不同类型展示不同形式数据

        if (mediaTZboxList[i].getElementsByClassName("media").length > 0) {
          // 媒体类型
          ctx.strokeStyle = '#ffffff';
          var media = mediaTZboxList[i].getElementsByClassName("media")[0]; // 只对img类型进行处理 video类型不显示

          if (media) {
            var img = media.getElementsByTagName("img")[0]; // 转换图片

            if (img) {
              ctx.drawImage(img, left, top, width, height);
            }
          }
        } else if (mediaTZboxList[i].getElementsByClassName("textbig").length > 0 || mediaTZboxList[i].getElementsByClassName("textbig_m").length > 0) {
          // 文本类型
          // TODO
          ctx.strokeStyle = '#ffffff';
          var textbig = mediaTZboxList[i].getElementsByClassName("textbig");

          if (textbig.length <= 0) {
            textbig = mediaTZboxList[i].getElementsByClassName("textbig_m");
          }
        } else if (mediaTZboxList[i].getElementsByClassName("DigitalClock").length > 0) {
          // 数字时钟
          ctx.strokeStyle = '#ffffff';
          var digitalClock = mediaTZboxList[i].getElementsByClassName("DigitalClock")[0];
          var font = ""; // 斜体

          if (digitalClock.style.fontStyle == 'oblique') {
            font = "oblique ";
          } // 加粗


          if (digitalClock.style.fontWeight == 700) {
            font += "bold ";
          } // 获取字体大小


          var fontSize = parseInt(digitalClock.style.fontSize); // 设置字体颜色，居中，字体大小（等比缩小）

          ctx.fillStyle = digitalClock.style.color ? digitalClock.style.color : "red";

          if (digitalClock.style.textDecoration == 'underline') {
            ctx.strokeStyle = digitalClock.style.color ? digitalClock.style.color : "red";
          }

          ctx.textAlign = "center";
          ctx.textBaseline = 'middle';

          if (widthScaling <= heightScaling) {
            fontSize = Number(fontSize / widthScaling);
          } else {
            fontSize = Number(fontSize / heightScaling);
          }

          ctx.font = font + fontSize + "px Arial"; // 获取具体时钟内容

          var digitalClockDiv = digitalClock.getElementsByClassName("digitalClockDiv");
          var year = '';
          var week = '';
          var time = '';

          if (digitalClockDiv.length == 3) {
            // 年
            var yearSpan = digitalClockDiv[0].getElementsByTagName("span");
            var yearDiv = yearSpan[0].getElementsByTagName("div")[0];
            var yearB = digitalClockDiv[0].getElementsByTagName("b");
            year = (yearSpan[0].style.display == 'none' ? '' : yearDiv.innerText + ' ') + (yearB[0].style.display == 'none' ? '' : yearB[0].innerText + ' ') + (yearSpan[1].style.display == 'none' ? '' : yearSpan[1].innerText) + (yearB[1].style.display == 'none' ? '' : yearB[1].innerText + ' ') + (yearSpan[2].style.display == 'none' ? '' : yearSpan[2].innerText); // 星期

            var weekSpan = digitalClockDiv[1].getElementsByTagName("span");
            week = weekSpan[0].style.display == 'none' ? '' : weekSpan[0].innerText; // 时间

            var timeSpan = digitalClockDiv[2].getElementsByTagName("span");
            var timeB = digitalClockDiv[2].getElementsByTagName("b");
            var isVcbl = digitalClockDiv[2].getElementsByClassName("isVcbl");
            var sw = null;
            var xw = null;

            if (isVcbl[0].style.display != 'none') {
              // 上午
              sw = isVcbl[0].getElementsByClassName("SW")[0]; // 下午

              xw = isVcbl[0].getElementsByClassName("XW")[0];
            }

            time = (sw && sw.style.display != 'none' ? sw.innerText + ' ' : '') + (xw && xw.style.display != 'none' ? xw.innerText + ' ' : '') + (timeSpan[0].style.display == 'none' ? '' : timeSpan[0].innerText) + (timeB[0].style.display == 'none' ? '' : timeB[0].innerText) + (timeSpan[1].style.display == 'none' ? '' : timeSpan[1].innerText) + (timeB[1].style.display == 'none' ? '' : timeB[1].innerText) + (timeSpan[2].style.display == 'none' ? '' : timeSpan[2].innerText);
          }

          var fontX = Number(Number(width / 2) + Number(left));
          var fontY = Number(Number(height / 2) + Number(top));
          var flexDirection = digitalClock.style.flexDirection;

          if (flexDirection == 'row') {
            var row = year + week + time;
            var rowDrawText = this.drawText(row, width, fontSize);
            var rowText = '';

            if (rowDrawText.length > 0) {
              rowText = rowDrawText[0];
            } else {
              rowText = row;
            } // 字加下划线


            if (digitalClock.style.textDecoration == 'underline') {
              var textWidth = this.measureText(rowText, fontSize);
              this.drawLine(ctx, Number(fontX - textWidth / 2), Number(fontY) + 2, Number(Number(fontX) + textWidth / 2), Number(fontY) + 2);
            }

            ctx.fillText(rowText, fontX, fontY);
          } else {
            // 计算年是否需要换行
            var yearDrawText = this.drawText(year, width, fontSize);

            if (yearDrawText.length > 0) {
              for (var _j3 = 0; _j3 < yearDrawText.length; _j3++) {
                var element = yearDrawText[_j3];

                var _yearFontY = Number(fontY - Number((fontSize + fontSize * 0.3) * (yearDrawText.length - _j3)));

                var yearLineY = Number(_yearFontY + 4); // 字加下划线

                if (top < yearLineY && digitalClock.style.textDecoration == 'underline') {
                  var yearWidth = this.measureText(element, fontSize);
                  this.drawLine(ctx, Number(fontX - yearWidth / 2), yearLineY, Number(Number(fontX) + yearWidth / 2), yearLineY);
                }

                if (top < _yearFontY) {
                  ctx.fillText(element, fontX, _yearFontY);
                }
              }
            } else {
              // 字加下划线
              var yearFontY = Number(fontY - (Number(fontSize) + fontSize * 0.3));

              var _yearLineY = Number(yearFontY + 4);

              if (top < _yearLineY && digitalClock.style.textDecoration == 'underline') {
                var yearWidth = this.measureText(year, fontSize);
                this.drawLine(ctx, Number(fontX - yearWidth / 2), _yearLineY, Number(fontX + yearWidth / 2), _yearLineY);
              } // 如果显示的位置大于最大高度不显示


              if (top < yearFontY) {
                ctx.fillText(year, fontX, yearFontY);
              }
            } // 星期无需换行，超出隐藏


            if (Number(height) > Number(fontSize)) {
              var weekDrawText = this.drawText(week, width, fontSize);
              var weekText = '';

              if (weekDrawText.length > 0) {
                weekText = weekDrawText[0];
              } else {
                weekText = week;
              } // 字加下划线


              if (digitalClock.style.textDecoration == 'underline') {
                var weekWidth = this.measureText(weekText, fontSize);
                this.drawLine(ctx, Number(fontX - weekWidth / 2), Number(fontY) + 4, Number(Number(fontX) + weekWidth / 2), Number(fontY) + 4);
              }

              ctx.fillText(weekText, fontX, fontY);
            } // 计算时间是否需要换行


            var timeDrawText = this.drawText(time, width, fontSize);
            var frameBottom = Number(top) + Number(height);

            if (timeDrawText.length > 0) {
              for (var _j4 = 0; _j4 < timeDrawText.length; _j4++) {
                var _element = timeDrawText[_j4];

                var _timeFontY = Number(Number(fontY) + (fontSize + fontSize * 0.3) * (_j4 + 1));

                var _timeLineY = Number(_timeFontY) + 4; // 字加下划线


                if (frameBottom > _timeLineY && digitalClock.style.textDecoration == 'underline') {
                  var timeWidth = this.measureText(_element, fontSize);
                  this.drawLine(ctx, Number(fontX - timeWidth / 2), _timeLineY, Number(Number(fontX) + timeWidth / 2), _timeLineY);
                }

                if (frameBottom > _timeFontY) {
                  ctx.fillText(_element, fontX, _timeFontY);
                }
              }
            } else {
              // 字加下划线
              var timeFontY = Number(Number(fontY) + (Number(fontSize) + fontSize * 0.3));
              var timeLineY = Number(timeFontY) + 4;

              if (frameBottom > timeLineY && digitalClock.style.textDecoration == 'underline') {
                var timeWidth = this.measureText(time, fontSize);
                this.drawLine(ctx, Number(fontX - timeWidth / 2), timeLineY, Number(Number(fontX) + timeWidth / 2), timeLineY);
              }

              if (frameBottom > timeFontY) {
                ctx.fillText(time, fontX, timeFontY);
              }
            }
          }
        } else if (mediaTZboxList[i].getElementsByClassName("assemblyPropDisplay").length > 0) {
          // 环境监测
          ctx.strokeStyle = '#ffffff';
          var assemblyPropDisplay = mediaTZboxList[i].getElementsByClassName("assemblyPropDisplay")[0]; // 获取字体颜色与大小

          var fontSize = parseInt(assemblyPropDisplay.style.fontSize ? assemblyPropDisplay.style.fontSize : 18);
          var font = ""; // 斜体

          if (assemblyPropDisplay.style.fontStyle == 'oblique') {
            font = "oblique ";
          } // 加粗


          if (assemblyPropDisplay.style.fontWeight == 700) {
            font += "bold ";
          } // 设置字体颜色，居中，字体大小（等比缩小）


          ctx.fillStyle = assemblyPropDisplay.style.color ? assemblyPropDisplay.style.color : "red";

          if (assemblyPropDisplay.style.textDecoration == 'underline') {
            ctx.strokeStyle = assemblyPropDisplay.style.color ? assemblyPropDisplay.style.color : "red";
          }

          if (widthScaling <= heightScaling) {
            fontSize = Number(fontSize / widthScaling);
          } else {
            fontSize = Number(fontSize / heightScaling);
          }

          ctx.font = font + fontSize + "px Arial"; // 获取文本对齐方式

          var textAlign = assemblyPropDisplay.style.textAlign;
          ctx.textAlign = "center";
          ctx.textBaseline = 'bottom';
          var fontX = Number(Number(width / 2) + Number(left));
          var fontY = Number(Number(height / 2) + Number(top));

          if (textAlign == 'left') {
            ctx.textAlign = "left";
            fontX = left;
          } else if (textAlign == 'right') {
            ctx.textAlign = "right";
            fontX = Number(Number(width) + Number(left));
          } // 具体内容


          var assemblyPropDisplayDiv = assemblyPropDisplay.getElementsByClassName("assemblyPropDisplayDiv");

          if (assemblyPropDisplayDiv.length > 0) {
            var readEnvironmentalShowList = []; // 筛选display none的数据

            for (var _j5 = 0; _j5 < assemblyPropDisplayDiv.length; _j5++) {
              var _element2 = assemblyPropDisplayDiv[_j5];

              if (_element2.style.display != 'none') {
                var environmentalTitleP = _element2.getElementsByTagName("p"); // 如果标题未填内容也不显示


                if (environmentalTitleP.length > 0) {
                  if (environmentalTitleP[0].innerText) {
                    readEnvironmentalShowList.push(_element2);
                  }
                } else {
                  readEnvironmentalShowList.push(_element2);
                }
              }
            } // 标题、温度、湿度、噪音、风速、风向、PM10、PM25


            if (readEnvironmentalShowList.length > 0) {
              ctx.textBaseline = 'middle'; // 单行滚动文本

              if (assemblyPropDisplay.style.whiteSpace == "nowrap") {
                var environmentalText = '';

                for (var _j6 = 0; _j6 < readEnvironmentalShowList.length; _j6++) {
                  var _element3 = readEnvironmentalShowList[_j6];

                  var environmentalSpan = _element3.getElementsByTagName("span");

                  if (environmentalSpan.length > 0) {
                    environmentalText += environmentalSpan[0].innerText + " " + environmentalSpan[1].innerText + " ";
                  } else {
                    var environmentalTitleP = _element3.getElementsByTagName("p")[0];

                    environmentalText += environmentalTitleP.innerText;
                  }

                  var environmentalTextList = this.drawText(environmentalText, width, fontSize);

                  if (environmentalTextList.length > 0) {
                    environmentalText = environmentalTextList[0];
                  }
                } // 加下划线


                if (assemblyPropDisplay.style.textDecoration == 'underline') {
                  var environmentalTextWidth = this.measureText(environmentalText, fontSize);
                  this.drawLine(ctx, left, Number(fontY) + 4, Number(Number(left) + environmentalTextWidth), Number(fontY) + 4);
                }

                ctx.fillText(environmentalText, left, fontY);
              } else {
                for (var _j7 = 0; _j7 < readEnvironmentalShowList.length; _j7++) {
                  var _element4 = readEnvironmentalShowList[_j7];

                  var environmentalSpan = _element4.getElementsByTagName("span");

                  var environmentalText = '';

                  if (environmentalSpan.length > 0) {
                    environmentalText = environmentalSpan[0].innerText + " " + environmentalSpan[1].innerText;
                  } else {
                    var environmentalTitleP = _element4.getElementsByTagName("p")[0];

                    environmentalText = environmentalTitleP.innerText;
                  }

                  var environmentalTextList = this.drawText(environmentalText, width, fontSize);

                  if (environmentalTextList.length > 0) {
                    environmentalText = environmentalTextList[0];
                  }

                  var textFontY = Number(Number(top) + (Number(fontSize) + Number(fontSize) * 0.3) * Number(_j7 + 1));

                  if (Number(textFontY) < Number(height)) {
                    // 加下划线
                    if (assemblyPropDisplay.style.textDecoration == 'underline') {
                      var environmentalTextWidth = this.measureText(environmentalText, fontSize);
                      this.drawLine(ctx, Number(fontX - environmentalTextWidth / 2), Number(textFontY) + 4, Number(Number(fontX) + environmentalTextWidth / 2), Number(textFontY) + 4);
                    }

                    ctx.fillText(environmentalText, fontX, textFontY);
                  } else {
                    break;
                  }
                }
              }
            }
          }
        } else if (mediaTZboxList[i].getElementsByClassName("weatherDisplay").length > 0) {
          // 天气
          ctx.strokeStyle = '#ffffff';
          var weatherDisplay = mediaTZboxList[i].getElementsByClassName("weatherDisplay")[0]; // 获取字体大小，默认为18px

          var fontSize = parseInt(weatherDisplay.style.fontSize ? weatherDisplay.style.fontSize : 18); // 设置字体颜色，字体大小（等比缩小）

          ctx.fillStyle = "#ffffff";
          ctx.textBaseline = 'bottom';
          ctx.textAlign = "start";

          if (widthScaling <= heightScaling) {
            fontSize = Number(fontSize / widthScaling);
          } else {
            fontSize = Number(fontSize / heightScaling);
          }

          ctx.font = fontSize + "px Arial"; // 取出文本数组

          var textList = weatherDisplay.innerHTML.split("<br>");

          if (textList.length > 0) {
            for (var _j8 = 0; _j8 < textList.length; _j8++) {
              var _element5 = this.trim(textList[_j8]);

              var drawText = this.drawText(_element5, width, fontSize);

              if (drawText.length > 0) {
                _element5 = drawText[0];
              }

              var _textFontY = Number(Number(top) + (Number(fontSize) + Number(fontSize) * 0.3) * (Number(_j8) + 1)); // 如果高度过高显示


              if (Number(_textFontY) < Number(height)) {
                ctx.fillText(_element5, left, _textFontY);
              } else {
                break;
              }
            }
          } else {
            ctx.fillText(weatherDisplay.innerText, left, Number(top) + Number(fontSize) + fontSize * 0.3);
          }
        } else if (mediaTZboxList[i].getElementsByClassName("streamingDisplay").length > 0) {
          // 流媒体
          ctx.strokeStyle = '#ffffff'; // var streamingDisplay = mediaTZboxList[i].getElementsByClassName("streamingDisplay")[0]
          // 不做处理
        } else if (mediaTZboxList[i].getElementsByClassName("simulation").length > 0) {
          // 模拟时钟
          ctx.strokeStyle = '#ffffff'; // var simulation = mediaTZboxList[i].getElementsByClassName("simulation")[0]

          var simulationCanvas = document.getElementById("canvas");

          if (simulationCanvas) {
            ctx.drawImage(simulationCanvas, left, top, width, height);
          } // console.log(simulation)

        } else if (mediaTZboxList[i].getElementsByClassName("multiWindow").length > 0) {
          // 多素材窗口
          ctx.strokeStyle = '#ffffff';
          var multiWindow = mediaTZboxList[i].getElementsByClassName("multiWindow")[0];
          var multiWindowP = multiWindow.getElementsByTagName("p")[0];
          var fontSize = 14;
          ctx.fillStyle = multiWindowP.style.color;
          ctx.textBaseline = 'top';
          ctx.textAlign = "start";

          if (widthScaling <= heightScaling) {
            fontSize = Number(fontSize / widthScaling);
          } else {
            fontSize = Number(fontSize / heightScaling);
          }

          ctx.font = fontSize + "px Arial";
          var drawText = this.drawText(multiWindowP.innerText, width, fontSize);

          if (drawText.length > 0) {
            // 计算一共需要使用多少高度
            var totalHeight = drawText.length * fontSize;
            var subLength = 0;

            if (totalHeight >= height) {
              subLength = Number((totalHeight - height) / fontSize);
            }

            for (var _j9 = 0; _j9 < drawText.length - subLength; _j9++) {
              var _element6 = drawText[_j9];
              ctx.fillText(_element6, left, Number(top) + Number(fontSize * _j9) + Number(fontSize / 2));
            }
          } else {
            ctx.fillText(multiWindowP.innerText, left, Number(top) + Number(fontSize));
          }

          console.log(multiWindow);
        } else if (mediaTZboxList[i].getElementsByClassName("htmlbox").length > 0) {
          // 网页
          ctx.strokeStyle = '#ffffff'; // var htmlbox = mediaTZboxList[i].getElementsByClassName("htmlbox")[0]
          // 不做处理
        } else if (mediaTZboxList[i].getElementsByClassName("textTemplate").length > 0) {
          // 文字模版
          ctx.strokeStyle = '#ffffff';
          var textTemplate = mediaTZboxList[i].getElementsByClassName("textTemplate")[0];
          console.log(textTemplate);
        }
      }
    } else {
      var ctx = listViewCanvas.getContext("2d");
      var w = listViewCanvas.width;
      var h = listViewCanvas.height;
      listViewCanvas.width = w;
      listViewCanvas.height = h;
      ctx.clearRect(0, 0, w, h);
    }
  },

  /**
   * 计算文本是否超出矩形范围
   * @param {*} text 文本内容
   * @param {*} width 边框的宽度
   * @param {*} fontSize 字体大小
   * @param {*} face font-family
   * @param {*} style 加粗斜体 bold oblique
   * @param {*} otherWidth 按照width 截取后剩下的字使用otherWidth 截取
   * @returns
   */
  drawText: function drawText(text, width, fontSize, face, style, otherWidth) {
    var chr = text.split("");
    var temp = "";
    var temp1 = "";
    var row = [];
    var flag = false;

    if (this.measureText(text, fontSize, face, style) > width) {
      for (var a = 0; a < chr.length; a++) {
        if (otherWidth && flag) {
          width = otherWidth;
        }

        if (a != 0) {
          temp1 += chr[a - 1];
        }

        temp += chr[a];

        if (this.measureText(temp, fontSize, face, style) > width) {
          row.push(temp1);
          temp = temp.substring(temp.length - 1, temp.length);
          temp1 = "";
          flag = true;
        }
      }

      row.push(temp);
    }

    return row;
  },

  /**
  * 计算文本占用的宽度
  * @param {*} val 文本
  * @param {*} fontSize 字体大小
  * @param {*} face font-family
  * @param {*} style 加粗斜体 bold oblique
  * @returns 
  */
  measureText: function measureText(val, fontSize, face, style) {
    face = face && face != "" ? face : "arial";
    style = style && face != "" ? style : "";
    var font = "".concat(style, " ").concat(fontSize, "px ").concat(face);
    var canvas = document.createElement("canvas");
    var context = canvas.getContext("2d");
    context.font = font;

    var _context$measureText = context.measureText(val),
        width = _context$measureText.width;

    return width;
  },

  /**
   * 绘制一根线条
   * @param {*} ctx
   * @param {*} moveX
   * @param {*} moveY
   * @param {*} lineX
   * @param {*} lineY
   */
  drawLine: function drawLine(ctx, moveX, moveY, lineX, lineY) {
    ctx.moveTo(moveX, moveY);
    ctx.lineTo(lineX, lineY);
    ctx.stroke();
  },

  /**
   * 去除两边空格
   * @param {*} str 去空格文本
   * @returns
   */
  trim: function trim(str) {
    if (str) {
      return str.replace(/(^\s*)|(\s*$)/g, "");
    }

    return '';
  },

  /**
   * 显示预览效果
   */
  showVideo: function showVideo() {
    var _this9 = this;

    // console.log($("#streamingUrl").val());
    var streamingUrl = (0, _jQuery["default"])("#streamingUrl").val();

    if (streamingUrl) {
      var videodom = document.getElementById("streaming" + this.selectBox);

      if (_flv["default"].isSupported()) {
        this.flvPlayer = _flv["default"].createPlayer({
          type: "flv",
          // 媒体类型
          isLive: true,
          //是否是实时流
          hasAudio: false,
          //是否有音频
          url: streamingUrl,
          // 视频流地址
          stashInitialSize: 128 // 减少首帧显示等待时长

        }, {
          enableWorker: false,
          // 不启动分离线程
          enableStashBuffer: false,
          // 关闭IO隐藏缓冲区
          reuseRedirectedURL: true,
          // 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
          autoCleanupSourceBuffer: true,
          // 自动清除缓存
          fixAudioTimestampGap: false // false 音频同步

        }); // 断流重连

        this.flvPlayer.on(_flv["default"].Events.ERROR, function (errorType, errorDetail, errorInfo) {
          console.log("errorType:", errorType);
          console.log("errorDetail:", errorDetail);
          console.log("errorInfo:", errorInfo);

          if (_this9.flvPlayer) {
            _this9.destoryVideo();
          }
        }); // 画面卡死重连

        var than = this;
        this.flvPlayer.on("statistics_info", function (res) {
          if (than.lastDecodedFrame == 0) {
            than.lastDecodedFrame = res.decodedFrames;
            return;
          }

          if (than.lastDecodedFrame != res.decodedFrames) {
            than.lastDecodedFrame = res.decodedFrames;
          } else {
            than.lastDecodedFrame = 0;

            if (than.flvPlayer) {
              than.destoryVideo();
            }
          }
        });
        this.flvPlayer.attachMediaElement(videodom);
        this.flvPlayer.load();
        this.flvPlayer.play(); // console.log('showVideo')
        // this.getThumbnail();
      }
    }
  },

  /**
   * 销毁断流方法
   */
  destoryVideo: function destoryVideo() {
    if (this.flvPlayer !== null) {
      this.flvPlayer.pause();
      this.flvPlayer.unload();
      this.flvPlayer.detachMediaElement();
      this.flvPlayer.destroy();
      this.flvPlayer = null;
    }
  },

  /**
   * 保存时处理相关数据
   * @param {*} item
   * @param {*} itemAll 
   * @param {*} indexAll
   * @param {*} index
   * @param {*} XXData
   * @param {*} fileIds
   * @param {*} resolve
   * @param {*} reject
   * @param {*} sourcesBox
   * @param {*} items
   */
  hanDleData: function hanDleData(item, itemAll, indexAll, index, XXData, fileIds, resolve, reject, sourcesBox, items) {
    var _this10 = this;

    var thisOutside = this;

    if (items.width && items.height) {
      item.width = items.width;
      item.height = items.height;
      item.top = items.top;
      item.left = items.left;
    }

    new Promise(function (resolve, reject) {
      if (item.type === "media") {
        //媒体情况
        var type = item.media.suffix;
        XXData = {
          "_type": type == "mp4" ? "Video" : type == "jpeg" || type == "jpg" || type == "png" || type == "gif" || type == "gfif" || type == "jfif" ? "Image" : type == "mp3" ? "Audio" : type,
          "deleted": null,
          "deletedBy": null,
          "enabled": true,
          "enabledBy": "",
          "entryEffect": 0,
          "entryEffectTimeSpan": 0,
          "exitEffect": 0,
          "exitEffectTimeSpan": 0,
          "fileExt": type == "mp4" ? "Video" : type == "jpeg" || type == "jpg" || type == "png" ? "Image" : type == "gif" || type == "gfif" || type == "jfif" ? "gif" : type == "mp3" ? "Audio" : type,
          "height": item.height,
          "id": item.media.fileId,
          "user": item.media.user,
          "left": item.left,
          "md5": item.media.fileId,
          "mediaGroup": null,
          "mime": type == "mp4" ? "video/mp4" : type == "jpeg" || type == "jfif" ? "image/jpeg" : type == "jpg" ? "image/jpg" : type == "png" ? "image/png" : type == "gif" ? "image/gif" : type == "mp3" ? "Audio" : type,
          "name": item.media.fileName,
          "newName": null,
          "oldFilePath": "",
          "playTime": 0,
          //  播放开始时间的+=
          "size": item.media.fileSize,
          "timeSpan": item.PlaybackDuration,
          //视频播放时长  s
          "top": item.top,
          "useType": null,
          "width": item.width
        };
        resolve([item.media.fileSize, item.PlaybackDuration]);
      } else if (item.type === "DigitalClock") {
        //数字时钟
        var addImgarr = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "上午", "日", "星期五", "-", ":", "星期一", "月", "下午", "星期六", "星期日", "星期四", "星期二", "星期三", "/", "年"];
        var addUmgName = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "AM", "DAY", "FRI", "hengxian", "maohao", "MON", "MONTH", "PM", "SAT", "SUN", "THU", "TUE", "WED", "xiegang", "YEAR"];
        var html = '';
        var SZimgXY = []; //25张图片的坐标  方便截取

        var imgH;
        var then = _this10;
        addImgarr.forEach(function (itemc, index) {
          html += "<span display:inline-block;>".concat(itemc, "</span><br>");
        });
        (0, _jQuery["default"])("body").append("<div class=\"SZSZFH".concat(indexAll).concat(index, "\" style=\"display:inline-block;line-height:1;\">").concat(html, "</div> ")).find(".SZSZFH" + indexAll + index).css({
          "color": item.textColor,
          "fontSize": item.fontSize,
          "lineHeight": 1
        }); //添加截图所需要的dom

        imgH = (0, _jQuery["default"])(".SZSZFH" + indexAll + index).height();
        html2canvas((0, _jQuery["default"])(".SZSZFH" + indexAll + index), {
          // background: '#fff', // 背景颜色 不添加背景为透明
          dpi: 300,
          onrendered: function onrendered(image) {
            var ImgFile = image.toDataURL();
            (0, _jQuery["default"])(".SZSZFH" + indexAll + index).remove();
            var heightNum = 0;
            var indexHeight = imgH / 25 * 100;

            for (var i = 0; i < addImgarr.length; i++) {
              var XYN = thisOutside.getTextWH(addImgarr[i], item.fontSize);
              var SNXYN = {};
              heightNum += indexHeight;
              SNXYN.name = addUmgName[i];
              SNXYN.startY = (heightNum - indexHeight) / 100;
              SNXYN.endY = heightNum / 100;
              SNXYN.startX = 0;
              SNXYN.endX = XYN.x;
              SNXYN.id = thisOutside.uuid2();
              SZimgXY.push(SNXYN);
            }

            var formData = {
              type: 1,
              base64: ImgFile
            };

            _axios["default"].request({
              url: _httpRequest["default"].adornUrl('/sys/program/file/base64ToImage'),
              method: 'post',
              data: formData,
              headers: {
                'token': thisOutside.getToken()
              }
            }).then(function (res) {
              XXData = {
                "AmPm": item.numberTime.indexOf("上午/下午") == -1 ? false : true,
                "_type": "DigitalClockXb",
                "arrayPics": SZimgXY,
                //图片数组
                "dateStyle": item.Rqfg == "1970年/01月/02日" ? 0 : item.Rqfg == "1970年/1月/2日" ? 1 : item.Rqfg == "1/2/1970" ? 2 : item.Rqfg == "01/02/1970" ? 3 : item.Rqfg == "1970/01/02" ? 7 : item.Rqfg == "1970/1/2" ? 6 : 1,
                "day": item.numberTime.indexOf("日") == -1 ? false : true,
                "entryEffect": "None",
                "entryEffectTimeSpan": 0,
                "exitEffect": "None",
                "exitEffectTimeSpan": 0,
                "fontSize": item.fontSize,
                "fullYear": item.numberTime.indexOf("四位年") == -1 ? false : true,
                "height": item.height,
                "hour": item.numberTime.indexOf("时") == -1 ? false : true,
                "hour12": item.numberTime.indexOf("12小时制") == -1 ? false : true,
                "iPicCount": 25,
                "id": res.data.files.id,
                "left": item.left,
                "min": item.numberTime.indexOf("分") == -1 ? false : true,
                "month": item.numberTime.indexOf("月") == -1 ? false : true,
                "multiline": item.isSingleLine == 1 ? false : true,
                //是否多行
                "name": "DigitalClockNew",
                "playTime": then.num,
                "sec": item.numberTime.indexOf("秒") == -1 ? false : true,
                "timeSpan": item.PlaybackDuration,
                "timeStyle": item.Sjfg == "HH:MM:SS" ? 0 : 1,
                "timezone": item.SQnum,
                "top": item.top,
                "weekly": item.numberTime.indexOf("星期") == -1 ? false : true,
                "width": item.width,
                "year": item.numberTime.indexOf("年") == -1 ? false : true,
                "size": res.data.files.fileSize
              };
              fileIds.push(res.data.files.id);
              resolve([res.data.files.fileSize, item.PlaybackDuration]);
            }); // $.ajax(httpRequest.adornUrl('/sys/program/file/base64ToImage'),{
            //     method:"post",
            //     data:JSON.stringify(formData),
            //     headers: {'token': thisOutside.getToken()},
            //     contentType:'application/json; charset=utf-8',
            //     success:function(ess){
            //         console.log(ess)
            //     }
            // })

          }
        });
      } else if (item.type === "environmental") {
        //环境监测
        var _addImgarr = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        var _addUmgName = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
        var _html3 = ''; // let ImgFile // 42张图片的精灵图

        var HJimgXY = []; //25张图片的坐标  方便截取

        var _imgH, imgW;

        var _then = _this10;

        _addUmgName.push("labeltitle");

        _addImgarr.push(item.assePropData.title);

        _addUmgName.push("labeltemperature");

        _addImgarr.push(item.assePropData.temperature + ":");

        _addUmgName.push("labelhumidity");

        _addImgarr.push(item.assePropData.humidity + ":");

        _addUmgName.push("labelnoise");

        _addImgarr.push(item.assePropData.noise + ":");

        _addUmgName.push("labelwindSpeed");

        _addImgarr.push(item.assePropData.windSpeed + ":");

        _addUmgName.push("labelwindDirection");

        _addImgarr.push(item.assePropData.windDirection + ":");

        _addUmgName.push("labelpm10");

        _addImgarr.push(item.assePropData.Pm10 + ":");

        _addUmgName.push("labelpm25");

        _addImgarr.push(item.assePropData.Pm25 + ":");

        var locale = localStorage.getItem('locale') === null ? 'cn' : localStorage.getItem('locale');

        if (locale === 'tw') {
          _addImgarr.push.apply(_addImgarr, ["℃", "℉", "RH", 'dB', "m/s", "μg/m³", "μg/m³", "-", "北", "北東北", "東北", "東東北", "東", "東東南", "東南", "南東南", "南", "南西南", "西南", "西西南", "西", "西西北", "西北", "北西北"]);
        } else {
          _addImgarr.push.apply(_addImgarr, ["℃", "℉", "RH", 'dB', "m/s", "μg/m³", "μg/m³", "-", "北", "北东北", "东北", "东东北", "东", "东东南", "东南", "南东南", "南", "南西南", "西南", "西西南", "西", "西西北", "西北", "北西北"]);
        } // addImgarr.push(...["℃","℉","RH",'dB',"m/s","μg/m³","μg/m³","-","北","北东北","东北","东东北","东","东东南","东南","南东南","南","南西南","西南","西西南","西","西西北","西北","北西北"])


        _addUmgName.push.apply(_addUmgName, ["unit_celsius", "unit_fahrenheit", "unit_humidity", "unit_noise", "unit_windspeed", "unit_pm10", "unit_pm25", "minus_sign", "N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE", "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]);

        _addImgarr.forEach(function (itemc, index) {
          _html3 += "<span display:inline-block;>".concat(itemc, "</span><br>");
        });

        (0, _jQuery["default"])("body").append("<div class=\"HJJCFH".concat(indexAll).concat(index, "\" style=\"display:inline-block;line-height:1;\">").concat(_html3, "</div>")).find(".HJJCFH" + indexAll + index).css({
          "color": item.textColor,
          "fontSize": item.fontSize,
          "lineHeight": 1
        }); //添加截图所需要的dom

        _imgH = (0, _jQuery["default"])(".HJJCFH" + indexAll + index).height();
        imgW = (0, _jQuery["default"])(".HJJCFH" + indexAll + index).width();
        html2canvas((0, _jQuery["default"])(".HJJCFH" + indexAll + index), {
          // background: '#fff', // 背景颜色 不添加背景为透明
          dpi: 300,
          onrendered: function onrendered(image) {
            var ImgFile = image.toDataURL();
            (0, _jQuery["default"])(".HJJCFH" + indexAll + index).remove();
            var heightNum = 0;
            var indexHeight = _imgH / 42 * 100;

            for (var i = 0; i < _addImgarr.length; i++) {
              var XYN = thisOutside.getTextWH(_addImgarr[i], item.fontSize);
              var SNXYN = {};
              heightNum += indexHeight;
              SNXYN.name = _addUmgName[i];
              i == 24 ? SNXYN.startY = (heightNum - indexHeight) / 100 + 1 : SNXYN.startY = (heightNum - indexHeight) / 100;
              i == 24 || i == 23 ? SNXYN.endY = heightNum / 100 + 1 : SNXYN.endY = heightNum / 100;
              SNXYN.startX = 0;
              SNXYN.endX = XYN.x > imgW ? imgW : XYN.x == 0 ? 1 : XYN.x;
              SNXYN.id = thisOutside.uuid2();
              HJimgXY.push(SNXYN);
            }

            var formData = {
              type: 3,
              base64: ImgFile
            };

            _axios["default"].request({
              url: _httpRequest["default"].adornUrl('/sys/program/file/base64ToImage'),
              method: 'post',
              data: formData,
              headers: {
                'token': thisOutside.getToken()
              }
            }).then(function (res) {
              XXData = {
                "_type": "EnvironmentalMonitoringXb",
                "alignType": item.assePropData.position == 4 ? 1 : item.assePropData.position - 1,
                "arrayPics": HJimgXY,
                "bHumidity": item.assePropData.display['温度'],
                "bNoise": item.assePropData.display['噪音'],
                "bPM10": item.assePropData.display['PM10'],
                "bPM25": item.assePropData.display['PM2.5'],
                "bSingleScroll": item.assePropData.position == 4 ? true : false,
                "bTemperature": item.assePropData.display['温度'],
                "bTitle": item.assePropData.display['标题'],
                "bWindDirection": item.assePropData.display['风向'],
                "bWindSpeed": item.assePropData.display['风速'],
                "bgColor": 0,
                "font_Bold": item.textStyle.b,
                "font_Color": -16776961,
                "font_Italics": item.textStyle.i,
                "font_Name": "Arial",
                "font_Size": item.fontSize,
                "font_Underline": item.textStyle.u,
                "height": item.height,
                "iPicCount": 42,
                "iScrollSpeed": item.assePropData.RollingSpeed,
                "id": res.data.files.id,
                "left": item.left,
                "name": "EnvironmentalMonitoring",
                "playTime": _then.num,
                //播放开始时间
                "temperatureCompensation": item.assePropData.temperatureBC,
                "temperatureStyle": item.assePropData.temperatureNum == "1" ? 0 : 1,
                "timeSpan": item.PlaybackDuration,
                "top": item.top,
                "width": item.width,
                "size": res.data.files.fileSize,
                "RefreshCycle": item.RefreshCycle * 60
              };
              fileIds.push(res.data.files.id);
              resolve([res.data.files.fileSize, item.PlaybackDuration]);
            });
          }
        });
      } else if (item.type === "Weather") {
        //天气
        XXData = {
          "id": thisOutside.createId(),
          "name": item.weatherName,
          "_type": item.type,
          "city": item.saveWeather.cityInfo.city,
          "lineHeight": 1,
          "html": "<span style=\"font-size:".concat(item.fontSize, "px;>\u4ECA\u5929\uFF1A%{arr.0.date}</span><br /><span style=\"font-size:").concat(item.fontSize, "px;\">\u5B9E\u65F6\u6E29\u5EA6\uFF1A%{current}\u2103</span><br /><span style=\"font-size:").concat(item.fontSize, "px;\">\u7A7A\u6C14\u8D28\u91CF\uFF1A%{aqi}</span><br /><span style=\"font-size:").concat(item.fontSize, "px;\">%{arr.0.img-32-32}%{arr.0.type}</span><br /><span style=\"font-size:").concat(item.fontSize, "px;\">\u6700\u9AD8:%{arr.0.high}\u2103 \u6700\u4F4E\uFF1A%{arr.0.low}\u2103</span><br /><span style=\"font-size:").concat(item.fontSize, "px;\">%{arr.0.fx} %{arr.0.fl}</span>"),
          "playTime": _this10.num,
          "timeSpan": item.inputDuration,
          "left": item.left,
          "top": item.top,
          "width": item.width,
          "height": item.height,
          "entryEffect": "None",
          "exitEffect": "None",
          "entryEffectTimeSpan": 0,
          "exitEffectTimeSpan": 0,
          "code": item.saveWeather.cityInfo.citykey,
          "size": 0
        };
        resolve([0, item.inputDuration]);
      } else if (item.type === "simulationClock") {
        //模拟时钟
        XXData = {
          "id": thisOutside.createId(),
          "name": item.simulationName,
          "_type": "AnalogClock",
          "shade": 0,
          "opacity": 1,
          "showBg": true,
          "bgColor": item.colorB,
          "showHourScale": true,
          "scaleHourColor": item.colorKS,
          "showMinScale": true,
          "scaleMinColor": item.colorKF,
          "scaleStyle": 3,
          "showScaleNum": true,
          "pinStyle": 1,
          "pinHourColor": item.colorS,
          "pinMinColor": item.colorF,
          "pinSecColor": item.colorM,
          "showSecond": true,
          "playTime": _this10.num,
          "timeSpan": item.inputDuration,
          "left": item.left,
          "top": item.top,
          "width": item.width,
          "height": item.height,
          "entryEffect": "None",
          "exitEffect": "None",
          "entryEffectTimeSpan": 0,
          "exitEffectTimeSpan": 0
        };
        resolve([0, item.inputDuration]);
      } else if (item.type === "streaming") {
        //直播流
        XXData = {
          "id": thisOutside.createId(),
          "name": item.streamingName,
          "_type": "LiveVideo",
          "url": item.streamingUrl,
          "playTime": _this10.num,
          "timeSpan": item.inputDuration,
          "left": item.left,
          "top": item.top,
          "width": item.width,
          "height": item.height,
          "entryEffect": "None",
          "exitEffect": "None",
          "entryEffectTimeSpan": 0,
          "exitEffectTimeSpan": 0,
          "streamIndex": item.streamIndex
        };
        resolve([0, item.inputDuration]);
      } else if (item.type == "WebURL") {
        // console.log("网页信息")
        XXData = {
          "id": thisOutside.createId(),
          "name": "webURL",
          "_type": "WebURL",
          "url": item.url,
          "left": item.left,
          "top": item.top,
          "width": item.width,
          "height": item.height,
          "entryEffect": "None",
          "exitEffect": "None",
          "entryEffectTimeSpan": 0,
          "exitEffectTimeSpan": 0,
          "playTime": _this10.num,
          "timeSpan": item.inputDuration
        };
        resolve([0, item.inputDuration]);
      } else if (item.type === "text") {
        //文本情况
        var numtext = 0;
        var arrayPics = [];
        var imgFile;
        var souceSize = 0;

        var _XXData;

        if (items.type == "multiWindow") {
          if (item.textRadio == "1" && item.curchange_m == 'static_m') {
            new Promise(function (resolve, reject) {
              var scrolltext = document.getElementById("scrolltext_m" + item.selectText_m);

              _domToImageMore["default"].toPng(scrolltext, {
                bgcolor: "#000000"
              }).then(function (dataurl) {
                // for (var i = 0; i < item.showWndImage_m.length; i++) {
                imgFile = thisOutside.dataURLtoFile(dataurl); //base64转为图片的实例   上传用这个

                var formData = new FormData(); // file为后台接收参数

                formData.append('files', imgFile);

                _axios["default"].request({
                  url: _httpRequest["default"].adornUrl('/sys/program/file/upload/0'),
                  method: 'post',
                  data: formData,
                  headers: {
                    'Content-Type': 'multipart/form-data',
                    'token': thisOutside.getToken()
                  }
                }).then(function (res) {
                  if (res.data) {
                    if (res.data.code === 0) {
                      souceSize += res.data.files[0].fileSize;
                      arrayPics.push({
                        "effect": "no",
                        "effectSpeed": 0,
                        "id": res.data.files[0].id,
                        "mime": "image/png",
                        "picDuration": item.inputDuration,
                        "size": res.data.files[0].fileSize
                      }); //取到的数据id  push到数组中以下面这种格式

                      fileIds.push(res.data.files[0].id);
                      _XXData = {
                        "_type": "MultiPng",
                        "arrayPics": arrayPics,
                        "size": souceSize,
                        // base64:base64,
                        "html": item.TextHtml,
                        "curchange_m": item.curchange_m,
                        "height": item.height,
                        "iPicCount": arrayPics.length,
                        "id": thisOutside.createId(),
                        "left": item.left,
                        "name": "MultiPng",
                        "playTime": numtext,
                        "timeSpan": item.inputDuration * arrayPics.length,
                        "top": item.top,
                        "width": item.width
                      };
                      resolve([souceSize, item.inputDuration * arrayPics.length, _XXData]);
                    } else {
                      _viewDesign.Message.error({
                        content: res.data.msg,
                        duration: 0.5
                      });
                    }
                  }
                }); // }

              });
            }).then(function (res) {
              resolve(res);
            });
          } else if (item.textRadio == "2" && item.curchange_m == 'roll_m') {
            new Promise(function (resolve, reject) {
              // TODO
              var scrolltext = document.getElementById("scrolltext_m" + item.selectText_m);

              _domToImageMore["default"].toPng(scrolltext, {
                bgcolor: "#000000"
              }).then(function (dataurl) {
                // for (var i = 0; i < item.showWndImage_m.length; i++) {
                imgFile = thisOutside.dataURLtoFile(dataurl); //base64转为图片的实例   上传用这个

                var formData = new FormData(); // file为后台接收参数

                formData.append('files', imgFile);

                _axios["default"].request({
                  url: _httpRequest["default"].adornUrl('/sys/program/file/upload/0'),
                  method: 'post',
                  data: formData,
                  async: false,
                  headers: {
                    'Content-Type': 'multipart/form-data',
                    'token': thisOutside.getToken()
                  }
                }).then(function (res) {
                  // 上传成功处理
                  if (res.data) {
                    if (res.data.code === 0) {
                      souceSize += res.data.files[0].fileSize;
                      arrayPics.push({
                        "effect": item.effect,
                        "effectSpeed": item.textSpeed,
                        "id": res.data.files[0].id,
                        "mime": "image/png",
                        "picDuration": 0,
                        "size": res.data.files[0].fileSize
                      }); //取到的数据id  push到数组中以下面这种格式

                      fileIds.push(res.data.files[0].id);
                      _XXData = {
                        "_type": "MultiPng",
                        "arrayPics": arrayPics,
                        "size": souceSize,
                        // base64:base64,
                        "html": item.TextHtml,
                        "curchange_m": item.curchange_m,
                        "height": item.height,
                        "iPicCount": arrayPics.length,
                        "id": thisOutside.createId(),
                        "left": item.left,
                        "name": "MultiPng",
                        "playTime": numtext,
                        "timeSpan": item.inputDuration * arrayPics.length,
                        "top": item.top,
                        "width": item.width
                      };
                      resolve([souceSize, item.inputDuration * arrayPics.length, _XXData]);
                    } else {
                      _viewDesign.Message.error({
                        content: res.data.msg,
                        duration: 0.5
                      });
                    }
                  }
                }); // }

              });
            }).then(function (res) {
              resolve(res);
            });
          } else if (item.textRadio == "3" && item.curchange_m == 'page_m') {
            new Promise(function _callee2(resolve, reject) {
              var scrolltext, width, height, _loop2, i;

              return regeneratorRuntime.async(function _callee2$(_context9) {
                while (1) {
                  switch (_context9.prev = _context9.next) {
                    case 0:
                      if (item.pageTextList && item.pageTextList.length > 0) {
                        scrolltext = (0, _jQuery["default"])("#scrolltext_m" + item.selectText_m);
                        width = scrolltext[0].clientWidth;
                        height = scrolltext[0].clientHeight;

                        _loop2 = function _loop2(i) {
                          var element = item.pageTextList[i];
                          var spanDom = document.createElement("div");
                          spanDom.style.width = width + "px";
                          spanDom.style.height = height + "px";
                          spanDom.style.lineHeight = "normal";
                          spanDom.style.whiteSpace = "normal";
                          spanDom.style.wordBreak = "break-all";
                          spanDom.style.color = "#ffffff";
                          spanDom.style.backgroundColor = "#000000";
                          spanDom.innerHTML = element;
                          document.body.append(spanDom);

                          _domToImageMore["default"].toPng(spanDom, {
                            bgcolor: "#000000"
                          }).then(function (dataurl) {
                            imgFile = thisOutside.dataURLtoFile(dataurl); //base64转为图片的实例   上传用这个

                            var formData = new FormData(); // file为后台接收参数

                            formData.append('files', imgFile);

                            _axios["default"].request({
                              url: _httpRequest["default"].adornUrl('/sys/program/file/upload/0'),
                              method: 'post',
                              data: formData,
                              async: true,
                              headers: {
                                'Content-Type': 'multipart/form-data',
                                'token': thisOutside.getToken()
                              }
                            }).then(function (res) {
                              // 上传成功处理
                              if (res.data) {
                                if (res.data.code === 0) {
                                  arrayPics.push({
                                    "effect": "no",
                                    "effectSpeed": 0,
                                    "id": res.data.files[0].id,
                                    "mime": "image/png",
                                    "picDuration": item.inputDuration,
                                    "size": res.data.files[0].fileSize
                                  }); //取到的数据id  push到数组中以下面这种格式

                                  fileIds.push(res.data.files[0].id);
                                  souceSize += res.data.files[0].fileSize;
                                } else {
                                  _viewDesign.Message.error({
                                    content: res.data.msg,
                                    duration: 0.5
                                  });
                                }
                              }
                            });
                          }).then(function () {
                            spanDom.remove();
                          });
                        };

                        for (i = 0; i < item.pageTextList.length; i++) {
                          _loop2(i);
                        }
                      }

                      _XXData = {
                        "_type": "MultiPng",
                        "arrayPics": arrayPics,
                        "size": souceSize,
                        // base64:base64,
                        "html": item.TextHtml,
                        "curchange_m": item.curchange_m,
                        "height": item.height,
                        "iPicCount": arrayPics.length,
                        "id": thisOutside.createId(),
                        "left": item.left,
                        "name": "MultiPng",
                        "playTime": numtext,
                        "timeSpan": item.inputDuration * arrayPics.length,
                        "top": item.top,
                        "width": item.width
                      };
                      numtext += item.inputDuration;
                      resolve([souceSize, item.inputDuration * arrayPics.length, _XXData]);

                    case 4:
                    case "end":
                      return _context9.stop();
                  }
                }
              });
            }).then(function (res) {
              resolve(res);
            });
          }
        } else {
          if (item.textRadio == "1" && item.curchange == 'static') {
            new Promise(function (resolve, reject) {
              var scrolltext = document.getElementById("scrolltext" + item.selectText);

              _domToImageMore["default"].toPng(scrolltext, {
                bgcolor: "#000000"
              }).then(function (dataurl) {
                // for (var i = 0; i < item.showWndImage.length; i++) {
                imgFile = thisOutside.dataURLtoFile(dataurl); //base64转为图片的实例   上传用这个

                var formData = new FormData(); // file为后台接收参数

                formData.append('files', imgFile);

                _axios["default"].request({
                  url: _httpRequest["default"].adornUrl('/sys/program/file/upload/0'),
                  method: 'post',
                  data: formData,
                  headers: {
                    'Content-Type': 'multipart/form-data',
                    'token': thisOutside.getToken()
                  }
                }).then(function (res) {
                  if (res.data) {
                    if (res.data.code === 0) {
                      souceSize += res.data.files[0].fileSize;
                      arrayPics.push({
                        "effect": "no",
                        "effectSpeed": 0,
                        "id": res.data.files[0].id,
                        "mime": "image/png",
                        "picDuration": 0,
                        "size": res.data.files[0].fileSize
                      }); //取到的数据id  push到数组中以下面这种格式

                      fileIds.push(res.data.files[0].id);
                      _XXData = {
                        "_type": "MultiPng",
                        "arrayPics": arrayPics,
                        "size": souceSize,
                        // base64:base64,
                        "html": item.TextHtml,
                        "curchange": item.curchange,
                        "height": item.height,
                        "iPicCount": arrayPics.length,
                        "id": thisOutside.createId(),
                        "left": item.left,
                        "name": "MultiPng",
                        "playTime": numtext,
                        "timeSpan": item.inputDuration * arrayPics.length,
                        "top": item.top,
                        "width": item.width
                      };
                      console.log(_XXData);
                      resolve([souceSize, item.inputDuration * arrayPics.length, _XXData]);
                    } else {
                      _viewDesign.Message.error({
                        content: res.data.msg,
                        duration: 0.5
                      });
                    }
                  }
                }); // }

              });
            }).then(function (res) {
              resolve(res);
            });
          } else if (item.textRadio == "2" && item.curchange == 'roll') {
            new Promise(function (resolve, reject) {
              var dom = document.getElementById('scrolltext' + item.selectText);

              _domToImageMore["default"].toPng(dom, {
                bgcolor: "#000000"
              }).then(function (dataurl) {
                // for (var i = 0; i < item.showWndImage.length; i++) {
                imgFile = thisOutside.dataURLtoFile(dataurl); //base64转为图片的实例   上传用这个

                var formData = new FormData(); // file为后台接收参数

                formData.append('files', imgFile);

                _axios["default"].request({
                  url: _httpRequest["default"].adornUrl('/sys/program/file/upload/0'),
                  method: 'post',
                  data: formData,
                  async: false,
                  headers: {
                    'Content-Type': 'multipart/form-data',
                    'token': thisOutside.getToken()
                  }
                }).then(function (res) {
                  // 上传成功处理
                  if (res.data) {
                    if (res.data.code === 0) {
                      souceSize += res.data.files[0].fileSize;
                      arrayPics.push({
                        "effect": item.effect,
                        "effectSpeed": item.textSpeed,
                        "id": res.data.files[0].id,
                        "mime": "image/png",
                        "picDuration": 0,
                        "size": res.data.files[0].fileSize
                      }); //取到的数据id  push到数组中以下面这种格式

                      fileIds.push(res.data.files[0].id);
                      _XXData = {
                        "_type": "MultiPng",
                        "arrayPics": arrayPics,
                        "size": souceSize,
                        // base64:base64,
                        "html": item.TextHtml,
                        "curchange": item.curchange,
                        "height": item.height,
                        "iPicCount": arrayPics.length,
                        "id": thisOutside.createId(),
                        "left": item.left,
                        "name": "MultiPng",
                        "playTime": numtext,
                        "timeSpan": item.inputDuration * arrayPics.length,
                        "top": item.top,
                        "width": item.width
                      };
                      resolve([souceSize, item.inputDuration * arrayPics.length, _XXData]);
                    } else {
                      _viewDesign.Message.error({
                        content: res.data.msg,
                        duration: 0.5
                      });
                    }
                  }
                }); // }

              });
            }).then(function (res) {
              resolve(res);
            });
          } else if (item.textRadio == "3" && item.curchange == 'page') {
            new Promise(function _callee3(resolve, reject) {
              var scrolltext, _width, _height, _loop3, i;

              return regeneratorRuntime.async(function _callee3$(_context11) {
                while (1) {
                  switch (_context11.prev = _context11.next) {
                    case 0:
                      if (!(item.pageTextList && item.pageTextList.length > 0)) {
                        _context11.next = 12;
                        break;
                      }

                      scrolltext = (0, _jQuery["default"])("#scrolltext" + item.selectText);
                      _width = scrolltext[0].clientWidth;
                      _height = scrolltext[0].clientHeight;

                      _loop3 = function _loop3(i) {
                        var element, spanDom;
                        return regeneratorRuntime.async(function _loop3$(_context10) {
                          while (1) {
                            switch (_context10.prev = _context10.next) {
                              case 0:
                                element = item.pageTextList[i];
                                spanDom = document.createElement("div");
                                spanDom.style.width = _width + "px";
                                spanDom.style.height = _height + "px";
                                spanDom.style.lineHeight = "normal";
                                spanDom.style.whiteSpace = "normal";
                                spanDom.style.wordBreak = "break-all";
                                spanDom.style.color = "#ffffff";
                                spanDom.style.backgroundColor = "#000000";
                                spanDom.innerHTML = element;
                                document.body.append(spanDom);
                                _context10.next = 13;
                                return regeneratorRuntime.awrap(_domToImageMore["default"].toPng(spanDom, {
                                  bgcolor: "#000000"
                                }).then(function (dataurl) {
                                  imgFile = thisOutside.dataURLtoFile(dataurl); //base64转为图片的实例   上传用这个

                                  var formData = new FormData(); // file为后台接收参数

                                  formData.append('files', imgFile);

                                  _axios["default"].request({
                                    url: _httpRequest["default"].adornUrl('/sys/program/file/upload/0'),
                                    method: 'post',
                                    data: formData,
                                    async: true,
                                    headers: {
                                      'Content-Type': 'multipart/form-data',
                                      'token': thisOutside.getToken()
                                    }
                                  }).then(function (res) {
                                    // 上传成功处理
                                    if (res.data) {
                                      if (res.data.code === 0) {
                                        arrayPics.push({
                                          "effect": "no",
                                          "effectSpeed": 1,
                                          "id": res.data.files[0].id,
                                          "mime": "image/png",
                                          "picDuration": item.inputDuration,
                                          "size": res.data.files[0].fileSize
                                        }); //取到的数据id  push到数组中以下面这种格式

                                        fileIds.push(res.data.files[0].id);
                                        souceSize += res.data.files[0].fileSize;
                                      } else {
                                        _viewDesign.Message.error({
                                          content: res.data.msg,
                                          duration: 0.5
                                        });
                                      }
                                    }
                                  });
                                }).then(function () {
                                  spanDom.remove();
                                }));

                              case 13:
                              case "end":
                                return _context10.stop();
                            }
                          }
                        });
                      };

                      i = 0;

                    case 6:
                      if (!(i < item.pageTextList.length)) {
                        _context11.next = 12;
                        break;
                      }

                      _context11.next = 9;
                      return regeneratorRuntime.awrap(_loop3(i));

                    case 9:
                      i++;
                      _context11.next = 6;
                      break;

                    case 12:
                      _XXData = {
                        "_type": "MultiPng",
                        "arrayPics": arrayPics,
                        "size": souceSize,
                        // base64:base64,
                        "html": item.TextHtml,
                        "curchange": item.curchange,
                        "height": item.height,
                        "iPicCount": arrayPics.length,
                        "id": thisOutside.createId(),
                        "left": item.left,
                        "name": "MultiPng",
                        "playTime": numtext,
                        "timeSpan": item.inputDuration * arrayPics.length,
                        "top": item.top,
                        "width": item.width
                      };
                      numtext += item.inputDuration;
                      resolve([souceSize, item.inputDuration * arrayPics.length, _XXData]);

                    case 15:
                    case "end":
                      return _context11.stop();
                  }
                }
              });
            }).then(function (res) {
              resolve(res);
            });
          }
        }
      }
    }).then(function (data) {
      sourcesBox.push(XXData || data[2]);
      resolve(data);
    });
  },

  /**
   * 保存
   * @param {*} isExit
   */
  preservation: function preservation(isExit) {
    var thisOutside = this;
    this.clearAllTiming();

    if (isExit === true) {
      this.loadingSaveAndExit = true;
    } else {
      this.loadingSave = true;
    }

    var medioDataAll = []; //items数组

    var promiseAllArr = []; //一共多少页

    var fileIds = []; // 预存资源类型id

    this.ALLBoxDataParent[this.currentPage] = JSON.parse(JSON.stringify(this.ALLBoxData));
    var XXData;
    this.ALLBoxDataParent.forEach(function (itemAll, indexAll) {
      var totalSize = 0;
      var promiseArr = [];

      if (itemAll.length > 0) {
        itemAll.forEach(function (item, index) {
          thisOutside.num = 0;
          promiseArr.push(new Promise(function (resolve, reject) {
            var madPromise = [];

            if (item.type == "multiWindow") {
              var addEe = [];
              item.multiWindowData.forEach(function (itemMin, indexMin) {
                var sourcesBox = [];
                madPromise.push(new Promise(function (resolve, reject) {
                  thisOutside.hanDleData(itemMin, itemAll, indexAll, indexMin, XXData, fileIds, resolve, reject, sourcesBox, item);
                }));
                addEe.push(sourcesBox);
              });
              Promise.all(madPromise).then(function (data) {
                data.forEach(function (itm, idx) {
                  totalSize += itm[0];
                });
                var arrC = [];
                addEe.forEach(function (itemC, indexC) {
                  arrC.push(itemC[0]);

                  if (indexC == 0) {
                    itemC[0].playTime = 0;
                  } else {
                    var y = 0;

                    for (var i = 0; i < indexC; i++) {
                      y += data[i][1];
                    }

                    itemC[0].playTime = y;
                  }
                });
                resolve(arrC);
              });
            } else {
              var sourcesBox = [];
              new Promise(function (resolve, reject) {
                thisOutside.hanDleData(item, itemAll, indexAll, index, XXData, fileIds, resolve, reject, sourcesBox, 'single');
              }).then(function (data) {
                totalSize += data[0];
                resolve(sourcesBox);
                sourcesBox = [];
              });
            }
          }));
        });
        promiseAllArr.push(new Promise(function (resolve, reject) {
          Promise.all(promiseArr).then(function (data) {
            var medioData = [];
            data.forEach(function (item, index) {
              medioData.push({
                "repeat": false,
                "sources": item
              });
            });
            var fileIdsStr = "";

            if (fileIds.length > 0) {
              fileIdsStr = fileIds.join(",");
            } //console.log(fileIdsStr)


            var timing = [];

            if (thisOutside.CurrentPageAll[indexAll].isEffective) {
              if (thisOutside.CurrentPageAll[indexAll].DayOfWeekPlay.length > 0) {
                thisOutside.CurrentPageAll[indexAll].DayOfWeekPlay.forEach(function (item) {
                  var obj = {};
                  var weekData = [];
                  item.isWeekB ? weekData.push(1) : "";
                  item.isWeekC ? weekData.push(2) : "";
                  item.isWeekD ? weekData.push(3) : "";
                  item.isWeekE ? weekData.push(4) : "";
                  item.isWeekF ? weekData.push(5) : "";
                  item.isWeekG ? weekData.push(6) : "";
                  item.isWeekA ? weekData.push(7) : "";
                  obj.dateType = "Range";
                  obj.endDate = thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].EndDate) ? thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].EndDate) : null;
                  obj.startDate = thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].StartDate) ? thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].StartDate) : null;
                  obj.startTime = item.timeSchedule[0];
                  obj.endTime = item.timeSchedule[1];
                  obj.timeType = "Range";
                  obj.filterType = "Week";
                  obj.monthFilter = [];
                  obj.weekFilter = weekData;
                  timing.push(obj);
                });
              } else {
                var obj = {};
                obj.endDate = thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].EndDate) ? thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].EndDate) : null;
                obj.startDate = thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].StartDate) ? thisOutside.transformationDate(thisOutside.CurrentPageAll[indexAll].StartDate) : null;
                obj.dateType = "Range";
                obj.timeType = "All";
                obj.filterType = "None";
                timing.push(obj);
              }
            } else {
              if (thisOutside.CurrentPageAll[indexAll].DayOfWeekPlay.length > 0) {
                thisOutside.CurrentPageAll[indexAll].DayOfWeekPlay.forEach(function (item) {
                  var weekData = [];
                  var obj = {};
                  obj.dateType = "All";
                  obj.endDate = null;
                  obj.startDate = null;
                  item.isWeekB ? weekData.push(1) : "";
                  item.isWeekC ? weekData.push(2) : "";
                  item.isWeekD ? weekData.push(3) : "";
                  item.isWeekE ? weekData.push(4) : "";
                  item.isWeekF ? weekData.push(5) : "";
                  item.isWeekG ? weekData.push(6) : "";
                  item.isWeekA ? weekData.push(7) : ""; // obj.endTime=thisOutside.transformationDateTime(item.endSchedule)
                  // obj.startTime=thisOutside.transformationDateTime(item.startSchedule)
                  // obj.endTime = item.endSchedule
                  // obj.startTime = item.startSchedule

                  obj.startTime = item.timeSchedule[0];
                  obj.endTime = item.timeSchedule[1];
                  obj.timeType = "Range";
                  obj.filterType = "Week";
                  obj.monthFilter = [];
                  obj.weekFilter = weekData;
                  timing.push(obj);
                });
              }
            }

            var itemsData = {
              "_id": thisOutside.createId(),
              "_program": {
                "width": thisOutside.canvasWidth,
                "height": thisOutside.canvasHeight,
                "__v": 0,
                "_company": "alahover",
                "_department": "alahover",
                "_id": thisOutside.createId(),
                "_role": "alahover",
                "_user": "alahover",
                "dateCreated": "",
                "sortXBList": JSON.stringify(thisOutside.sortXB[indexAll]),
                // "layers": [  //分层
                //     {
                //         "repeat": false,
                //         "sources": medioData
                //     }
                // ],
                "layers": medioData,
                "name": thisOutside.canvasName,
                "totalSize": totalSize,
                "fileIds": fileIdsStr
              },
              "priority": 0,
              "repeatTimes": thisOutside.CurrentPageAll[indexAll].PlaybackTimes,
              //播放次数
              "schedules": timing,
              "version": 0
            };
            medioData = []; //页面里面东西

            promiseArr = []; //promise.All清空

            resolve(itemsData);
          });
        }));
      }
    });
    Promise.all(promiseAllArr).then(function (data) {
      data.forEach(function (itemP) {
        medioDataAll.push(itemP);
      });
      var totalData = {
        "type": "commandXixunPlayer",
        "_id": thisOutside.createId(),
        "command": {
          "_type": "PlayXixunTask",
          "id": thisOutside.createId(),
          "notificationURL": 'http://192.168.1.108:8080/progress/notification',
          "preDownloadURL": 'http://192.168.1.108:8080/sys/program/file/download/',
          "task": {
            "_department": null,
            "_id": thisOutside.createId(),
            "cmdId": thisOutside.createId(),
            "insert": false,
            "items": medioDataAll,
            "name": thisOutside.canvasName
          }
        }
      };
      var webData = {
        ALLBoxDataParent: thisOutside.ALLBoxDataParent,
        CurrentPageAll: thisOutside.CurrentPageAll,
        currentPage: thisOutside.currentPage,
        canvasWidth: thisOutside.canvasWidth,
        canvasHeight: thisOutside.canvasHeight,
        canvasName: thisOutside.canvasName,
        deviationX: thisOutside.deviationX,
        deviationY: thisOutside.deviationY,
        weatherHtml: thisOutside.weatherHtml,
        weatherCity: thisOutside.weatherCity,
        weatherCode: thisOutside.weatherCode,
        maxHierarchy: thisOutside.maxHierarchy,
        sortXB: thisOutside.sortXB,
        sortIndexXB: thisOutside.sortIndexXB
      }; //console.log(webData);

      var obj = {
        web: JSON.stringify(webData),
        equipment: totalData,
        programId: thisOutside.programId,
        sourceFileIds: thisOutside.sourceFileIds,
        infoId: thisOutside.infoId
      };
      (0, _httpRequest["default"])({
        url: _httpRequest["default"].adornUrl("/screen/program/".concat(obj.programId === "" ? 'save' : 'update')),
        method: 'post',
        data: _httpRequest["default"].adornData(obj, false)
      }).then(function (_ref) {
        var data = _ref.data;

        if (isExit === true) {
          thisOutside.loadingSaveAndExit = false;
        } else {
          thisOutside.loadingSave = false;
        }

        if (data && data.code === 0) {
          if (isExit === true) {
            // 保存并退出
            thisOutside.clearAllDom(); //清空dom所有数据
            // 保存成功跳转到节目管理页面

            _router["default"].replace({
              name: 'screen-program'
            }); //跳转路由

          } else {
            //保存
            thisOutside.programId = data.program.programId;
            thisOutside.sourceFileIds = data.program.sourceFileIds;

            _viewDesign.Message.info("保存成功");
          }
        } else {
          _viewDesign.Message.error(data.msg);

          if (fileIds.length > 0) {
            (0, _httpRequest["default"])({
              url: _httpRequest["default"].adornUrl('/sys/program/file/delete'),
              method: 'post',
              data: _httpRequest["default"].adornData(fileIds, false)
            }).then(function (_ref2) {//   console.log(data)

              var data = _ref2.data;
            });
          }
        }
      });
    })["catch"](function (err) {
      _viewDesign.Message.error("保存过程出现错误"); // 保存错误删除预存图片


      if (fileIds.length > 0) {
        (0, _httpRequest["default"])({
          url: _httpRequest["default"].adornUrl('/sys/program/file/delete'),
          method: 'post',
          data: _httpRequest["default"].adornData(fileIds, false)
        }).then(function (_ref3) {//   console.log(data)

          var data = _ref3.data;
        });
      }
    });
  },

  /**
   * 传入字体和字体大小  出来宽高
   * @param {*} str
   * @param {*} fontSize
   * @returns
   */
  getTextWH: function getTextWH(str, fontSize) {
    var width = 10;
    var height = 10;
    var ele = document.createElement("span");
    ele.innerText = str;
    ele.style.fontSize = fontSize + "px";
    document.documentElement.append(ele);
    width = ele.offsetWidth;
    height = ele.offsetHeight;
    document.documentElement.removeChild(ele);
    return {
      x: width,
      y: height
    };
  },

  /**
   * 添加一列计划时间表
   */
  addTimeFrom: function addTimeFrom() {
    // this.CurrentPageAll[this.currentPage].DayOfWeekPlay.push(
    //     { isWeekA: true, isWeekB: true, isWeekC: true, isWeekD: true, isWeekE: true, isWeekF: true, isWeekG: true, startSchedule: "00:00", endSchedule: "00:59" }
    // )
    this.CurrentPageAll[this.currentPage].DayOfWeekPlay.push({
      isWeekA: true,
      isWeekB: true,
      isWeekC: true,
      isWeekD: true,
      isWeekE: true,
      isWeekF: true,
      isWeekG: true,
      timeSchedule: ["00:00:00", "00:00:00"]
    });
  },

  /**
   * 清空计划时间表
   */
  clearTimeFrom: function clearTimeFrom() {
    // this.CurrentPageAll[this.currentPage].DayOfWeekPlay = [{ isWeekA: true, isWeekB: true, isWeekC: true, isWeekD: true, isWeekE: true, isWeekF: true, isWeekG: true, startSchedule: "00:00", endSchedule: "23:59" }]
    this.CurrentPageAll[this.currentPage].DayOfWeekPlay = [{
      isWeekA: true,
      isWeekB: true,
      isWeekC: true,
      isWeekD: true,
      isWeekE: true,
      isWeekF: true,
      isWeekG: true,
      timeSchedule: ["00:00:00", "00:00:00"]
    }];
  },

  /**
   * 删除当前时间表
   * @param {*} index
   */
  removeIndex: function removeIndex(index) {
    this.CurrentPageAll[this.currentPage].DayOfWeekPlay.splice(index, 1);
  },

  /**
   * 点击修改获取dom数据
   * @param {*} data
   * @param {*} isView 是否预览模式
   */
  modifyAdvertisement: function modifyAdvertisement(data, isView) {
    var _this11 = this;

    if (isView === true) {
      this.isView = true;
    } else {
      this.isView = false;
    } // console.log(data);


    this.infoId = data.infoId;
    this.programId = data.programId;
    this.sourceFileIds = data.sourceFileIds;
    this.ALLBoxDataParent = data.web.ALLBoxDataParent;
    this.CurrentPageAll = data.web.CurrentPageAll;
    this.currentPage = data.web.CurrentPageAll.length - 1;
    this.canvasWidth = data.web.canvasWidth;
    this.canvasHeight = data.web.canvasHeight;
    this.canvasName = data.web.canvasName;
    this.deviationX = data.web.deviationX;
    this.deviationY = data.web.deviationY;
    this.weatherHtml = data.web.weatherHtml;
    this.weatherCity = data.web.weatherCity;
    this.weatherCode = data.web.weatherCode;
    this.maxHierarchy = data.web.maxHierarchy;
    this.sortXB = data.web.sortXB;
    this.sortIndexXB = data.web.sortIndexXB;
    this.selectBox = data.web.ALLBoxDataParent[data.web.ALLBoxDataParent.length - 1].length - 1;
    this.ALLBoxData = data.web.ALLBoxDataParent[data.web.ALLBoxDataParent.length - 1]; // 实现缩略图将所有页面都选择一遍

    if (this.CurrentPageAll.length > 0) {
      var DataTim = setInterval(function () {
        if ((0, _jQuery["default"])(".canvasBox")) {
          clearInterval(DataTim);

          _this11.CurrentPageAll.forEach(function _callee4(item, index) {
            return regeneratorRuntime.async(function _callee4$(_context12) {
              while (1) {
                switch (_context12.prev = _context12.next) {
                  case 0:
                    _context12.next = 2;
                    return regeneratorRuntime.awrap(_this11.clickPage(null, index));

                  case 2:
                  case "end":
                    return _context12.stop();
                }
              }
            });
          });
        }
      }, 100);
    } // if (this.ALLBoxData.length > 0) {
    //     let DataTim = setInterval(() => {
    //         if ($(".canvasBox")) {
    //             clearInterval(DataTim)
    //             this.ALLBoxData.forEach(async (item, index) => {
    //             await this.addDom(item, false)
    //                 if (item.type == "multiWindow") {
    //                     this.selectBox = index
    //                     this.windowPage = item.multiWindowData.length - 1
    //                     await this.addWindowItem(item.multiWindowData[this.windowPage])
    //                 }
    //             })
    //         }
    //     }, 100);
    // }

  },

  /**
   * base64转图片实例方法
   * @param {*} dataurl
   * @param {*} imgName
   * @returns
   */
  dataURLtoFile: function dataURLtoFile(dataurl, imgName) {
    var arr = dataurl.split(',');
    var mime = arr[0].match(/:(.*?);/)[1];
    var suffix = mime.split('/')[1];
    var bstr = atob(arr[1]);
    var n = bstr.length;
    var u8arr = new Uint8Array(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], "".concat(imgName ? imgName : "Unnamed"), {
      type: mime
    });
  },

  /**
   * 清空当前页dom
   */
  emptyDom: function emptyDom() {
    (0, _jQuery["default"])(".canvasBox").html("");
    this.clearAllTiming();
    this.ALLBoxData = [];
    this.selectBox = 0;
    this.assemblyProp = '';
    this.sortXB[this.currentPage] = [];
    this.sortIndexXB[this.currentPage] = 0;
  },

  /**
   * 删除选中dom
   */
  deleteDom: function deleteDom() {
    this.topPing(1);
    this.clearTimingByCurrentPage(this.ALLBoxData[this.selectBox]);
    this.sortXB[this.currentPage].splice(this.sortXB[this.currentPage].indexOf(Math.max.apply(Math, _toConsumableArray(this.sortXB[this.currentPage]))), 1);
    this.sortIndexXB[this.currentPage]--;
    this.maxHierarchy[this.currentPage]--;
    (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).remove();
    this.ALLBoxData.splice([this.selectBox], 1);
    this.assemblyProp = '';
    console.log("deleteDom");
    this.getThumbnail();
  },

  /**
   * 向上一层
   */
  upLayer: function upLayer() {
    if (this.ALLBoxData && this.ALLBoxData.length > 0) {
      var sortMin = this.sortXB[this.currentPage][this.selectBox];

      if (sortMin) {
        var min = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index");

        for (var i = 0; i < (0, _jQuery["default"])(".mediaTZbox").length; i++) {
          if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") == (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index") - 0 + 1) {
            //遍历出比当前选中大一层级的dom
            (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css({
              "z-index": (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index")
            });
            (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css({
              "z-index": min
            });
            break;
          }
        }

        for (var y = 0; y < this.sortXB[this.currentPage].length; y++) {
          if (this.sortXB[this.currentPage][y] == sortMin + 1) {
            var _ref4 = [this.sortXB[this.currentPage][this.selectBox], this.sortXB[this.currentPage][y]];
            this.sortXB[this.currentPage][y] = _ref4[0];
            this.sortXB[this.currentPage][this.selectBox] = _ref4[1];
          }
        }

        for (var _i4 = 0; _i4 < this.sortXB[this.currentPage].length; _i4++) {
          this.ALLBoxData[_i4].z_index = this.sortXB[this.currentPage][_i4];
        }

        console.log('upLayer');
        this.getThumbnail();
      }
    }
  },

  /**
   * 向下一层
   */
  nextFloor: function nextFloor() {
    if (this.ALLBoxData && this.ALLBoxData.length > 0) {
      var sortMin = this.sortXB[this.currentPage][this.selectBox];

      if (sortMin) {
        var min = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index");

        for (var i = 0; i < (0, _jQuery["default"])(".mediaTZbox").length; i++) {
          if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") == (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index") - 1) {
            (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css({
              "z-index": (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index")
            });
            (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css({
              "z-index": min
            });
            break;
          }
        }

        for (var y = 0; y < this.sortXB[this.currentPage].length; y++) {
          if (this.sortXB[this.currentPage][y] == sortMin - 1) {
            var _ref5 = [this.sortXB[this.currentPage][this.selectBox], this.sortXB[this.currentPage][y]];
            this.sortXB[this.currentPage][y] = _ref5[0];
            this.sortXB[this.currentPage][this.selectBox] = _ref5[1];
          }
        }

        for (var _i5 = 0; _i5 < this.sortXB[this.currentPage].length; _i5++) {
          this.ALLBoxData[_i5].z_index = this.sortXB[this.currentPage][_i5];
        }
      }

      console.log('nextFloor');
      this.getThumbnail();
    }
  },

  /**
   * 置顶
   */
  topPing: function topPing(type) {
    if (this.ALLBoxData && this.ALLBoxData.length > 0) {
      var sortMin = this.sortXB[this.currentPage][this.selectBox];

      if (sortMin) {
        if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index") == this.maxHierarchy[this.currentPage] - 1) {} else {
          for (var i = 0; i < (0, _jQuery["default"])(".mediaTZbox").length; i++) {
            if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") != (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index") && (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") > (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index")) {
              (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css({
                "z-index": (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") - 1
              });
            }
          }

          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css({
            "z-index": this.maxHierarchy[this.currentPage] - 1
          });
        }

        if (sortMin != this.sortIndexXB[this.currentPage] - 1) {
          for (var _i6 = 0; _i6 < this.sortXB[this.currentPage].length; _i6++) {
            if (this.sortXB[this.currentPage][_i6] > sortMin) {
              this.sortXB[this.currentPage][_i6]--;
            }
          }

          this.sortXB[this.currentPage][this.selectBox] = this.sortIndexXB[this.currentPage] - 1;

          for (var _i7 = 0; _i7 < this.sortXB[this.currentPage].length; _i7++) {
            this.ALLBoxData[_i7].z_index = this.sortXB[this.currentPage][_i7];
          }
        }

        if (type != 1) {
          console.log('topPing');
          this.getThumbnail();
        }
      }
    }
  },

  /**
   * 置底
   */
  bottomSetting: function bottomSetting() {
    if (this.ALLBoxData && this.ALLBoxData.length > 0) {
      var sortMin = this.sortXB[this.currentPage][this.selectBox];

      if (sortMin) {
        if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index") > 0) {
          for (var i = 0; i < (0, _jQuery["default"])(".mediaTZbox").length; i++) {
            if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") < (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css("z-index")) {
              (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css({
                "z-index": (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[i]).css("z-index") - 0 + 1
              });
            }
          }

          (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[this.selectBox]).css({
            "z-index": 0
          });
        }

        if (sortMin != 0) {
          for (var _i8 = 0; _i8 < this.sortXB[this.currentPage].length; _i8++) {
            if (this.sortXB[this.currentPage][_i8] < sortMin) {
              this.sortXB[this.currentPage][_i8]++;
            }
          }

          this.sortXB[this.currentPage][this.selectBox] = 0;

          for (var _i9 = 0; _i9 < this.sortXB[this.currentPage].length; _i9++) {
            this.ALLBoxData[_i9].z_index = this.sortXB[this.currentPage][_i9];
          }
        }

        console.log('bottomSetting');
        this.getThumbnail();
      }
    }
  },

  /**
   * 清除所有页面元素
   */
  clearAllDom: function clearAllDom() {
    this.clearAllTiming();
    (0, _jQuery["default"])(".canvasBox").html("");
    this.ALLBoxData = [];
    this.selectBox = 0;
    this.assemblyProp = '';
    this.attributeNav = "page";
    this.currentPage = 0;
    this.deviationY = 0;
    this.deviationX = 0;
    this.sortXB = [[]];
    this.sortIndexXB = [0];
    this.CurrentPageAll = [//所有页面属性
    {
      isEffective: false,
      StartDate: new Date(),
      EndDate: new Date(),
      // EndDate:new Date().setDate(new Date().getDate()+30),
      PlaybackTimes: 1,
      DayOfWeekPlay: [],
      name: this.getName()
    }];
    this.ALLBoxDataParent = [[]];
    this.canvasName = this.getName();
  },

  /**
   * 从Cookies中获取token
   *
   */
  getToken: function getToken() {
    var strcookie = document.cookie; //获取cookie字符串

    var arrcookie = strcookie.split("; "); //分割
    //遍历匹配

    for (var i = 0; i < arrcookie.length; i++) {
      var arr = arrcookie[i].split("=");

      if (arr[0] == "token") {
        return arr[1];
      }
    }

    return "";
  },

  /**
   * 松开鼠标按键时重置文本数据
   * @param {*} tempData
   * @param {*} then
   * @param {*} textbig
   * @param {*} textbigImg
   */
  mouseupText: function mouseupText(tempData, then, textbig, textbigImg) {
    if (tempData) {
      if (tempData.textRadio == '3') {
        if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
          // textbig.html(tempData.TextHtml_m);
          then.computePageText(tempData);

          if (tempData.pageTextList && tempData.pageTextList.length > 0) {
            textbig.html(tempData.pageTextList[tempData.ImagePage - 1]);
          }
        } else {
          // textbig.html(tempData.TextHtml);
          then.computePageText(tempData);

          if (tempData.pageTextList && tempData.pageTextList.length > 0) {
            textbig.html(tempData.pageTextList[tempData.ImagePage - 1]);
          }
        }

        textbigImg.empty();
      } else if (tempData.textRadio == '2') {
        tempData.curchange = 'roll';

        if ((0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow").length > 0) {
          then.newText(tempData.TextHtml_m);
        } else {
          then.newText(tempData.TextHtml);
        }
      } else if (tempData.textRadio == '1') {
        tempData.curchange = 'static';
      }
    }
  },

  /**
   * 根据是否多素材窗口获取对应数据
   * @param {*} then
   * @returns
   */
  getDataByMulti: function getDataByMulti(then) {
    var assemblyPropDisplay, tempData, textbig, textbigImg;
    var multiWindow = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).children(".multiWindow");

    if (multiWindow.length > 0) {
      assemblyPropDisplay = multiWindow.find(".assemblyPropDisplay");
      textbig = multiWindow.find(".textbig_m");
      textbigImg = multiWindow.find(".textbig_m img");
      tempData = then.ALLBoxData[then.selectBox].multiWindowData[then.windowPage];
    } else {
      assemblyPropDisplay = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".assemblyPropDisplay");
      textbig = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".textbig");
      textbigImg = (0, _jQuery["default"])((0, _jQuery["default"])(".mediaTZbox")[then.selectBox]).find(".textbig img");
      tempData = then.ALLBoxData[then.selectBox];
    }

    return {
      assemblyPropDisplay: assemblyPropDisplay,
      textbig: textbig,
      textbigImg: textbigImg,
      tempData: tempData
    };
  },

  /**
   * 将图片等比缩放后的宽高
   * @param {*} width 图片的宽度
   * @param {*} height 图片的高度
   * @returns 改变后的宽高
   */
  calculateWidthAndHeight: function calculateWidthAndHeight(width, height) {
    var resWidth, resHeight; // 如果图片的宽高小于画布的宽高

    if (width < this.canvasWidth && height < this.canvasHeight) {
      resWidth = width;
      resHeight = height;
    } else {
      // 如果图片的宽高大于画布的宽高
      var widthScaling = Number(width / this.canvasWidth);
      var heightScaling = Number(height / this.canvasHeight);

      if (widthScaling >= heightScaling) {
        resWidth = Number(width / widthScaling);
        resHeight = Number(height / widthScaling);
      } else {
        resWidth = Number(width / heightScaling);
        resHeight = Number(height / heightScaling);
      }

      if (resWidth > this.canvasWidth) {
        resWidth = this.canvasWidth;
      }

      if (resHeight > this.canvasHeight) {
        resHeight = this.canvasHeight;
      }
    }

    return {
      width: resWidth,
      height: resHeight
    };
  },
  calculateCanvasSize: function calculateCanvasSize(width, height) {
    var resWidth, resHeight; // 如果宽高大于画布的宽高

    if (width > this.canvasWidth && height > this.canvasHeight) {
      resWidth = width;
      resHeight = height;
    } else {
      // 如果宽高小于画布的宽高
      if (this.canvasWidth == this.canvasHeight) {
        resWidth = height;
        resHeight = height;
      } else {
        var heightScaling = height / this.canvasHeight;
        resWidth = Number(this.canvasWidth * heightScaling);

        if (resWidth > width) {
          resHeight = height - (resWidth - width);
          resWidth = width;
        } else {
          resHeight = height;
        }
      }
    }

    return {
      width: resWidth,
      height: resHeight
    };
  }
};
exports["default"] = _default;