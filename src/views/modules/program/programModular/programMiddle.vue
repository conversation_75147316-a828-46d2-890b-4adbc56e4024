<template>
  <div class="middleSurface">
    <div class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in filterProgramTypeList" :key="index" :title="$t(item.title)">
          <li v-if="item.isShow" :style="!programData.isDiscount ? '' : programData.isDiscount && item.isDiscount ? '' : 'cursor: not-allowed;'">
            <div class="opera_list"  @click="addProgram(item.type, item.isDiscount, item.title)">
              <svg width="54px" height="25px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.title)}}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div class="secondNav">
      <div v-for="(item, index) in secondNavList" :key="index">
        <div v-if="item.type==='zoomValue'">
          <span> {{ programData.zoomRate.toFixed(1) }}</span>
        </div>
        <div  v-else :title="$t(item.title)" @click="setDomStyle(item.type)">
          <svg class="iconT" aria-hidden="true">
            <use :xlink:href="'#' + item.icon"></use>
          </svg>
        </div>
      </div>
    </div>
    <el-dialog :title="$t('nav.媒体库')" :visible.sync="mediaVisible" :before-close="cancelMediea" width="570px">
      <Form :inline="true" :model="dataForm">
        <FormItem>
          <Input v-model="dataForm.name" :placeholder="$t('file.name')"></Input>
        </FormItem>
        <FormItem v-if="!isTextTemplate">
          <Select v-model="dataForm.type" filterable clearable
            :placeholder="$t('common.PleaseSelect') + $t('file.type')">
            <Option v-for="item in fileType" :value="item.suffix" :key="item.suffix">{{ item.suffix }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button style="margin-right: 6px" @click="searchmedia">
            <div style="margin: 3px 8px">{{ $t("common.query") }}</div>
          </Button>
        </FormItem>
      </Form>
      <el-table ref="multipleTable" :data="mediaData" tooltip-effect="dark"
        style="width: 100%;overflow:auto;height: 400px;" @selection-change="handleSelectionChange" :infinite-scroll-disabled="false"
        v-infinite-scroll="pagingFn" infinite-scroll-distance="5" @row-click="clickRow" :highlight-current-row="isTextTemplate">
        <el-table-column type="selection" width="55" v-if="!isTextTemplate">
        </el-table-column>
        <el-table-column :label="$t('file.name')" :width="isTextTemplate ? 175 : 120">
          <template slot-scope="scope">
            <div style="line-height:50px;margin-left:6px;width:100px;float:left;" :title="scope.row.fileName">
              {{ isTextTemplate ? scope.row.fileName.slice(0, 10) : scope.row.fileName.slice(0, 6) }}{{scope.row.fileName.length > 6 ? '...' : ''}} </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('file.thumbnail')" width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.suffix === 'mp4'"
              style="width:50px;height:50px;display:block;margin-left:5px;float:left;">
              <svg width="50px" height="50px" aria-hidden="true">
                <use xlink:href="#video"></use>
              </svg>
            </div>
            <!-- <video :src="downloadUrl + item.fileId" v-if="item.suffix==='mp4'" style="width:50px;height:50px;display:block;margin-left:5px;float:left;"></video> -->
            <img src="@/assets/img/audio.png" v-else-if="scope.row.suffix == 'mp3'"
              style="width:45px;height:45px;display:block;margin-left:5px;float:left;">
            <img :src="downloadUrl + scope.row.fileId" v-else
              style="width:50px;height:50px;display:block;margin-left:5px;float:left;">
            <div style="line-height:50px;margin-left:14px;float:left;width:40px;">{{ scope.row.suffix }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('file.size')" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.size / 1024 / 1024 < 1"
              style="line-height:50px;margin-left:14px;float:left;width:70px;">
              {{ (scope.row.fileSize / 1024).toFixed(1) }}KB</div>
            <div v-else style="line-height:50px;margin-left:14px;float:left;width:70px;">
              {{ (scope.row.fileSize / 1024 / 1024).toFixed(2) }}MB</div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <ul class="media_ul">
          <li class="media_li" v-for="(item,index) in mediaData" :key="index" @click="mediaXZ=index" :class="mediaXZ==index?'mediaXZ':''">
             <div style="line-height:50px;margin-left:4px;width:16px;color:red;float:left;"> {{index+1}} </div>
             <div style="line-height:50px;margin-left:6px;width:100px;float:left;" :title="item.fileName"> {{item.fileName.slice(0,8)}}...  </div>
             <div v-if="item.suffix==='mp4'" style="width:50px;height:50px;display:block;margin-left:5px;float:left;">
                <svg width="50px" height="50px" aria-hidden="true" >
                  <use xlink:href="#video"></use>
                </svg>
             </div> -->
      <!-- <video :src="downloadUrl + item.fileId" v-if="item.suffix==='mp4'" style="width:50px;height:50px;display:block;margin-left:5px;float:left;"></video> -->
      <!-- <img src="@/assets/img/audio.png"  v-else-if="item.suffix=='mp3'" style="width:45px;height:45px;display:block;margin-left:5px;float:left;">
             <img :src="downloadUrl + item.fileId" v-else style="width:50px;height:50px;display:block;margin-left:5px;float:left;">

             <div style="line-height:50px;margin-left:14px;float:left;width:40px;">{{item.suffix}}</div>
             <div v-if="item.size/1024/1024 < 1" style="line-height:50px;margin-left:14px;float:left;width:70px;">{{(item.fileSize/1024).toFixed(1)}}KB</div>
             <div v-else style="line-height:50px;margin-left:14px;float:left;width:70px;">{{(item.fileSize/1024/1024).toFixed(2)}}MB</div> -->
      <!-- <div v-if="item.width&&item.height" style="line-height:50px;margin-left:14px;float:left;width:70px;">{{item.width}}*{{item.height}}</div>
             <div v-else style="line-height:50px;margin-left:14px;float:left;"> -- </div> -->
      <!-- </li>
        </ul> -->
      <!-- <el-pagination layout="prev, pager, next" :current-page="pageNum" :total="mediaPaging" @current-change="pagingFn">
      </el-pagination> -->

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelMediea">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" :disabled="modelStatus" @click="addMedia_Fn">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
    <div class="canvasBox_wep" :style="{ maxWidth: maxHeight + 'px' }">
      <div class="canvasBox" id="canvasbox"
        :style="{ height: programData.canvasHeight + 'px', width: programData.canvasWidth + 'px' }"></div>
    </div>
  </div>
</template>

<script>
// import E from 'wangeditor' //wangeditor编辑器
import program from "./program.js"
import EventBus from "../EventBus.js"
import $ from "jQuery"
// import { Row } from 'view-design'

// let editor
export default {
  data() {
    return {
      multipleSelection: [],
      mediaVisible: false,//媒体模态框
      mediaData: [], //媒体数据
      mediaPaging: 0, //媒体数据总条数
      mediaXZ: 0, //选中的媒体
      pageNum: 1,// 当前页
      mediaXZData: {}, //选中媒体数据
      programData: {},//数据对象  和上面引入的是同一个
      maxHeight: "",
      ZoomAndOutValue: 100,
      downloadUrl: this.$http.adornUrl(`/sys/program/file/download/`), // 下载地址前缀
      isWindow: false, //是否为多素材窗口调用此组件
      isTextTemplate: false, // 是否为文字模块调用此组件
      modelStatus: true,
      /**
       * 媒体库查询条件
       */
      dataForm: {
        name: "",
        type: "",
      },
      fileType: "",
      programTypeList: [
        {title: "program.text", type: 'text', icon: "icon-text", isDiscount: false, isShow: true},
        {title: "task.media", type: 'media', icon: "media", isDiscount: true, isShow: true},
        {title: "program.DigitalClock", type: 'DigitalClock', icon: "icon-Digital-AlarmClock", isDiscount: false, isShow: true},
        {title: "program.analogClock", type: 'simulationClock', icon: "icon-shijian3", isDiscount: false, isShow: true},
        {title: "program.EnvironmentalMonitoring", type: 'environmental', icon: "icon-huanjingjiance", isDiscount: false, isShow: true},
        {title: "program.weather", type: 'weather', icon: "icon-tianqi", isDiscount: false, isShow: true},
        {title: "program.streaming", type: 'streaming', icon: "icon-shebeileifuwuqiliumeiti", isDiscount: false, isShow: true},
        {title: "program.Multi-materialWindow", type: 'multiWindow', icon: "window", isDiscount: false, isShow: true},
        {title: "program.html", type: 'WebURL', icon: "HTML5", isDiscount: false, isShow: true},
        // {title: "文字模版", type: 'textTemplate', icon: "textTemplate"},
        {title: "program.flowStatistics", type: 'VistorSource', icon: "VistorSource", isDiscount: false, isShow: true},
        {title: "HDMI", type: 'hdmi', icon: "hdmi", isDiscount: false, isShow: true},
        {title: "program.text-to-speech", type: 'textToSpeech', icon: "text-to-speech", isDiscount: false, isShow: true},
        {title: "program.sensorsShareData", type: 'sensorsShareData', icon: "sensor-shared-data", isDiscount: false, isShow: false},
        {title: "program.negativeIon", type: 'negativeIon', icon: "icon-huanjingjiance", isDiscount: false, isShow: false},
      ],
      programTypeOrder: [1,2,3,4,5,6,7,8,9],
      secondNavList: [
        {title: 'program.zoomOut', type: 'zoomOut', icon: 'zoomOut'},
        {title: '', type: 'zoomValue', icon: ''},
        {title: 'program.zoomIn', type: 'zoomIn', icon: 'zoomIn'},
        {title: 'program.empty', type: 'empty', icon: 'icon-qingkong'},
        {title: 'common.delete', type: 'delete', icon: 'icon-shanchu'},
        {title: 'program.oneLevelUp', type: 'up', icon: 'icon-dashujukeshihuaico-1'},
        {title: 'program.oneLevelDown', type: 'down', icon: 'icon-dashujukeshihuaico-'},
        {title: 'program.layerOnTop', type: 'top', icon: 'icon-control-top'},
        {title: 'program.bottomLayer', type: 'bottom', icon: 'icon-control-bottom'},
        {title: 'program.FullScreen', type: 'fullScreen', icon: 'icon-full-screen'},
      ],
      textTemplateMedia: null
    }
  },
  watch: {
    program: { //监听的对象  this.programData指向program
      handler() {
        this.programData = program
      },
      immediate: true,
    },
    maxHeight: { //监听浏览器宽度
      handler() {
        this.maxHeight = window.innerWidth - 272 - 299
      },
      immediate: true,
    }
  },
  mounted() {
    EventBus.$on("mediaMethod", (type) => {
      this.getMedia(1, type, false)
    });
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
      if (this.multipleSelection.length > 0) {
        this.modelStatus = false
        // 计算出图片宽高
        this.multipleSelection.forEach(item => {
          if (item.suffix == "mp4") {
            $("body").append(`<video id='demoMp4' controls autoplay>
                                <source src="${this.downloadUrl + item.fileId}" type='video/mp4'>
                              </video>`)
            var video = document.getElementById('demoMp4')
              video.addEventListener('canplay', function () {
                item.width = this.videoWidth
                item.height = this.videoHeight
            });
            $(`#demoMp4`).remove()
          } else {
            var img = new Image();
            img.src = this.downloadUrl + item.fileId
            img.onload = function() {
              item.height = img.height
              item.width = img.width
            }
          }

        })
      } else {
        this.modelStatus = true
      }
    },
    // ZoomAndOut() {
    //   program.ZoomAndOutFn(this.ZoomAndOutValue)
    // },
    /**
     * 添加对应类型的dom
     */
    addProgram(type, isDiscount, title) {
      // 如果任务为打折任务
      if (program.isDiscount) {
        if(!isDiscount) {
          this.$Message.warning({
            content: this.$t('common.supportedTip') + this.$t(title) + this.$t('program.segmentation'),
            duration: 2
          })
          return
        }
      }
      if (type == 'media' || type == 'textTemplate') {
        this.getMedia(1, type, false)
      } else if (type == 'hdmi'){
        program.addOrUpdateHDMI()
      } else {
        program.assemblyProp = type
        program.addDom()
      }
    },
    cancelMediea() {
      this.mediaVisible = false;
      this.modelStatus = true
      this.dataForm.name = "";
      this.dataForm.type = "";
      this.textTemplateMedia = null
      this.$refs.multipleTable.clearSelection();
    },
    async addMedia_Fn() { //添加媒体dom到canvas
      this.mediaXZData = this.mediaData[this.mediaXZ]
      this.mediaVisible = false
      this.dataForm.name = "";
      this.dataForm.type = "";
      if (this.isWindow) {
        this.multipleSelection.forEach(item => {
          program.addWindowItem("media", item)
        })
      } else {
        if (this.isTextTemplate) {
          program.assemblyProp = "textTemplate"
          program.addDom(null, false, this.textTemplateMedia)
          // program.addTextTemplateImgDom(this.textTemplateMedia)
        } else {
          program.assemblyProp = "media"
          if (program.isDiscount) {
            if (this.multipleSelection.length > 1) {
              this.$Message.warning({
                content: this.$t('common.supportedTip') + this.$t('program.multi-picture') + this.$t('program.segmentation'),
                duration: 2
              })
              return
            }
            if (this.multipleSelection[0].suffix == 'mp3') {
              this.$Message.warning({
                content: this.$t('program.PleaseSelectPictureVideoSplit'),
                duration: 2
              })
              return
            }
            if (program.ALLBoxData.length > 0) {
              this.$Message.warning({
                content: this.$t('common.supportedTip') + this.$t('program.multi-picture') + this.$t('program.segmentation'),
                duration: 2
              })
              return
            }
          }
          this.multipleSelection.forEach(item => {
            program.addDom(null, false, item)
            // program.addFindDom(item)
          })
        }
      }


    },
    clickRow(row, column, event) {
      if (this.isTextTemplate) {
        var img = new Image();
        img.src = this.downloadUrl + row.fileId
        img.onload = function() {
          row.height = img.height
          row.width = img.width
        }
        this.textTemplateMedia = row
        if (this.textTemplateMedia) {
          this.modelStatus = false
        }
      } else {
        this.$refs.multipleTable.toggleRowSelection(row);
      }
    },
    pagingFn() {  //点击分页更新列表
      this.mediaXZ = 0
      this.pageNum++;
      /* if (this.isTextTemplate) {
        this.getMedia(this.pageNum, "textTemplate", true)
      } else if (this.isWindow) {
        this.getMedia(this.pageNum, "multiWindow", false)
      } else {
        this.getMedia(this.pageNum, "none", true)
      } */
      // if (program.ALLBoxData.length > 0) {
      //   this.getMedia(this.pageNum, program.ALLBoxData[program.selectBox].type, true)
      // } else {
      //   if (this.isTextTemplate) {
      //     this.getMedia(this.pageNum, "textTemplate", true)
      //   } else {
      //     this.getMedia(this.pageNum, "none", true)
      //   }
      // }
      if (this.isWindow) {
        this.getMedia(this.pageNum, "multiWindow", false)
      } else {
        this.getMedia(this.pageNum, "none", true)
      }
      // this.getMedia(this.pageNum,program.ALLBoxData[program.selectBox].type)
    },
    getMedia(x, type, isScroll) { //取媒体库数据multiWidow
      if (type == "multiWindow") {
        this.isWindow = true
      } else {
        this.isWindow = false
      }
      if (type == "textTemplate") {
        this.isTextTemplate = true
      } else {
        this.isTextTemplate = false
      }
      if (x == 1) {
        this.mediaData = [];
      }
      this.pageNum = x;
      if (!isScroll && !this.isTextTemplate) {
        // 文件类型
        this.$http({
          url: this.$http.adornUrl("/sys/file/fileType"),
          method: "get",
          params: this.$http.adornParams(),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.fileType = data.fileType;
          }
        });
      }

      this.$http({
        url: this.$http.adornUrl('/screen/media/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageNum,
          'limit': 5,
          'status': 2,
          'suffix': this.isTextTemplate ? 'jpgOrPng' : this.dataForm.type
        }, true)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.mediaVisible = true
          for (let i = 0; i < data.page.list.length; i++) {
            this.mediaData.push(data.page.list[i]);
          }
          // this.mediaData = data.page.list
          this.mediaPaging = data.page.totalCount
        }
      })
    },
    setDomStyle(test) {
      if (test == "empty") {
        program.emptyDom()
      } else if (test == "delete") {
        program.deleteDom()
      } else if (test == "up") {
        program.upLayer()
      } else if (test == "down") {
        program.nextFloor()
      } else if (test == "top") {
        program.topPing()
      } else if (test == "bottom") {
        program.bottomSetting()
      } else if (test == "fullScreen") {
        program.fullScreen()
      }else if (test=='zoomIn'){
        program.zoomIn()
      }else if (test=='zoomOut'){
        program.zoomOut()
      }

    },

    /**
     * 搜索媒体库
     */
    searchmedia() {
      this.mediaData = [];
      // console.log(this.dataForm);
      this.$http({
        url: this.$http.adornUrl('/screen/media/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 5,
          'status': 2,
          fileName: this.dataForm.name,
          suffix:  this.isTextTemplate ? 'jpgOrPng' : this.dataForm.type,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          for (let i = 0; i < data.page.list.length; i++) {
            this.mediaData.push(data.page.list[i]);
          }
          this.mediaPaging = data.page.totalCount
        }
      })
    },

    // handleClose() {
    //   this.dataForm.name = "";
    //   this.dataForm.type = "";
    //   this.mediaVisible = false;
    // }

  },
  components: {

  },
  computed: {
    filterProgramTypeList() {
      let {programTypeList, programTypeOrder} = this
      programTypeList = programTypeList.filter((item,index) => {
        item.order = programTypeOrder[index]
        return item
      })
      programTypeList = programTypeList.sort(function(p1, p2) {
        return p1.order - p2.order
      })
      return programTypeList
    }
  }
}
</script>
<style>
.el-table::before {
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0px;
}
.w-e-text-container {
  background: #000 !important;
  color: #fff !important;
  z-index: 1 !important;
}
.w-e-toolbar {
  z-index: 10 !important;
}

/* 添加组件全局样式 */
.mediaTZbox {
  border: dashed 1px #fff;
  cursor: move;
  color: #fff;
  position: absolute;
  top: 40%;
  left: 36%;
  z-index: 1;
  overflow: hidden;
}
.hdmibox {
  border: dashed 1px #fff;
  cursor: move;
  color: #fff;
  position: absolute;
  top: 20%;
  left: 26%;
  z-index: 999;
  overflow: hidden;
}

.textDisplay {
  width: 100%;
  height: 100%;
}

.DigitalClock {
  color: red;
  text-align: center;
  pointer-events: none;
}
.negativeIonDisplay {
  color: red;
  text-align: center;
  pointer-events: none;
  width: 100%;
  height: 100%;
  user-select: none;
  position:absolute;
}
.assemblyPropDisplay {
  color: red;
  text-align: center;
  pointer-events: none;
  width: 100%;
  height: 100%;
  user-select: none;
  position:absolute;
  /*
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%); */
  /* display: flex;
  flex-direction: column;
  justify-content: center; */
  /* position: absolute; */
}

/* .assemblyPropDisplay{
  font-size: 18px;color:red;
  text-align: center;
  pointer-events: none;
  justify-content:center;
  user-select:none;
  position:absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
} */
</style>

<style scoped>

.middleSurface {
  width: 100%;
  height: 97%;
  flex: 1;
  border-left: #dce3f3 solid 1px;
}
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 65px;
  overflow: hidden;
  overflow-y: auto;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  text-align: center;
  fill: currentColor;
  color: #68b5fc;
  float: left;
  list-style: none;
  width: 77px;
  height: 65px;
  margin-left: 1px;
  white-space: normal;
  word-break: break-word;
}
.opera_list {
  padding-top: 5px;
  width: 77px;
  height: 65px;
}
.opera_ul li:hover {
  background-color: #dce3f3;
  border-radius: 3%;
  cursor: pointer;
}
.opera_text {
  color: rgb(99, 100, 100);
  font-size: 12px;
}

.iconT {
  width: 25px;
  height: 25px;
  fill: currentColor;
  overflow: hidden;
  cursor: pointer;
  border: #e3dfdf solid 1px;
  padding: 3px;
}
.iconT:hover {
  border: #68b5fc solid 1px;
}
.secondNav {
  border-top: #dce3f3 solid 1px;
  width: 100%;
  height: 45px;
  border-bottom: #dce3f3 solid 1px;
  display: flex;
  overflow-y: hidden;
}

/* //修改overflow滚动条 外边框 */
.secondNav::-webkit-scrollbar {
  width: 2px;
  height: 4px;
  background-color: rgba(240, 240, 240, 1);
}

/* //修改overflow滚动条  滚动条 */
.secondNav::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  background-color: #aeb7cf;
}

.secondNav>div {
  height: 45px;
  width: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.canvasBox_wep {
  width: 100%;
  height: calc(100% - 102px);
  overflow-x: auto;
  overflow-y: auto;
  /* font-family: Arial; */
  /* display: flex;
  justify-content: center;
  align-items: center; */
}

/* //修改overflow滚动条 外边框
.canvasBox_wep::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: rgb(64, 248, 8);;
}
//修改overflow滚动条  滚动条
.canvasBox_wep::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  background-color: #13f70b;
}*/
.canvasBox {
  background: #000;
  overflow: hidden;
  position: relative;
  left: 0;
}

.media_ui {
  width: 100%;
  height: 100%;
}

.media_li {
  width: 100%;
  height: 50px;
  border: solid 1px #dce3f3;
  border-radius: 5px;
  margin-top: 4px;
  cursor: pointer;
}

.media_li:hover {
  background: #dce3f3;
}

.mediaXZ {
  background: #dce3f3;
}
</style>
