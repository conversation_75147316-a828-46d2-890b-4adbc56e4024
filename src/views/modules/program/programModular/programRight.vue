<template>
  <div class="rightSurface">
    <div class="mainBtn">
         <el-button class="preservation" :disabled="programData.isView" size="medium" :loading="programData.loadingSave" @click="preservation(false)">{{$t('common.save')}}</el-button>
         <el-button type="success" :disabled="programData.isView" plain class="preservation" style="right:40px !important;;top:8px !important;;" size="medium" :loading="programData.loadingSaveAndExit" @click="preservation(true)">{{$t('common.saveAndExit')}}</el-button>
    </div>
    <div class="setPaging">
      <div class="tab_pane" @click="programData.attributeNav='assembly'" :class="programData.attributeNav==='assembly'?'XZBack':''">{{$t('program.ComponentProperties')}}</div>
      <div class="tab_pane" @click="programData.attributeNav='page'" :class="programData.attributeNav==='page'?'XZBack':''">{{$t('program.pageProperties')}}</div>
    </div>
    <div class="NavContent" v-if="programData.attributeNav!='page'">
      <setText :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'text'"></setText>
      <setMedia :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'media'"></setMedia>
      <setDigitalClock :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'DigitalClock'"></setDigitalClock>
      <setEnvironmental :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'environmental'"></setEnvironmental>
      <setWeatherMD :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'weather'"></setWeatherMD>
      <setStreaming :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'streaming'"></setStreaming>
      <setSimulationClock :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'simulationClock'"></setSimulationClock>
      <setMultiWindow v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'multiWindow'" :proData="programData.ALLBoxData[programData.selectBox]"></setMultiWindow>
      <setHtml  :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'WebURL'"></setHtml>
      <!-- <setTextTemplate :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav==='assembly'&& (programData.assemblyProp=='textTemplate' || programData.assemblyProp=='templateText')"></setTextTemplate> -->
      <setVistorSource :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&& programData.assemblyProp == 'VistorSource'"></setVistorSource>
      <setHDMI :proData="programData" v-if="programData.attributeNav === 'assembly'&& programData.assemblyProp == 'hdmi'"></setHDMI>
      <setTextToSpeechMD :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'textToSpeech'"></setTextToSpeechMD>
      <setSensorsShareData :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'sensorsShareData'"></setSensorsShareData>
      <setNegativeIon :proData="programData.ALLBoxData[programData.selectBox]" v-if="programData.attributeNav === 'assembly'&&programData.assemblyProp == 'negativeIon'"></setNegativeIon>
    </div>
    <div class="NavContent" v-else>
      <div style="margin-left:5px" v-if="programData.assemblyProp !== 'hdmi'">
        <el-divider content-position="left">{{$t('program.BasicProperties')}}</el-divider>
        <div style="margin:0 0 5px 0;">
          <span style="margin-top:5px;">{{$t('program.name')}}</span> <el-input v-model="programData.CurrentPageAll[programData.currentPage].name" :placeholder="$t('program.name')" style="width:195px;"></el-input>
        </div>
        <el-checkbox v-model="programData.CurrentPageAll[programData.currentPage].isEffective">{{$t('program.effectiveDate')}}</el-checkbox>
        <div class="timeBox" v-show="programData.CurrentPageAll[programData.currentPage].isEffective">
          <span>{{$t('card.startDate')}}：</span>
            <Col span="12">
                <DatePicker type="date" show-week-numbers placeholder="Select date" style="width: 200px" v-model="programData.CurrentPageAll[programData.currentPage].StartDate"></DatePicker>
            </Col>
          <span style="margin-top:5px;">{{$t('card.endDate')}}：</span>
            <Col span="12">
                <DatePicker type="date" show-week-numbers placeholder="Select date" style="width: 200px" v-model="programData.CurrentPageAll[programData.currentPage].EndDate"></DatePicker>
            </Col>
        </div>

        <el-divider content-position="left" style="margin-top:20px;">{{$t('program.PlayProperties')}}</el-divider>
        <div>
          <el-checkbox v-model="programData.CurrentPageAll[programData.currentPage].isMaterialCycle">{{ $t('program.materialCycle') }}</el-checkbox>
        </div>
        <div class="playAtt">
          <span>{{$t('task.plays')}}</span>
          <el-input-number controls-position="right" v-model="programData.CurrentPageAll[programData.currentPage].PlaybackTimes" :min="1" :max="9999" style="margin-left:20px;margin-top: 10px"></el-input-number>
        </div>
        <el-divider content-position="left" style="margin-top:20px;">{{$t('program.planSchedule')}}</el-divider>
        <span class="operation el-icon-plus" @click="addTimeFrom"></span>
        <span class="operation el-icon-refresh-left" @click="clearTimeFrom"></span>
        <div class="TimedPlay">
          <div class="TimedPlay_find" v-for="(item,index) in programData.CurrentPageAll[programData.currentPage].DayOfWeekPlay" :key="index">
            <span>{{index+1}}</span>
            <!-- <Col span="12" style="width: 95px;display:inline-block;">
                <TimePicker type="time" placeholder="Select time" format="HH:mm:ss" style="width: 95px;display:inline-block;" v-model="item.startSchedule" :editable="false"></TimePicker>
            </Col>
            <span>~</span>
            <Col span="12" style="width: 95px;display:inline-block;">
                <TimePicker type="time" placeholder="Select time" format="HH:mm:ss" style="width: 95px;display:inline-block;" v-model="item.endSchedule" :editable="false"></TimePicker>
            </Col> -->
            <TimePicker type = "timerange" placeholder = "select time" placement="bottom-end" format = "HH:mm:ss" style="width: 180px;display: inline-block;" v-model = "item.timeSchedule" :editable="false"></TimePicker>
            <span class="operation el-icon-delete" @click="removeIndex(index)"></span>
            <div class="OisWeek">
                  <div :class="item.isWeekA ? 'yesWeek' : 'noWeek'" @click="item.isWeekA=!item.isWeekA">{{$t('program.sun')}}</div>
                  <div :class="item.isWeekB ? 'yesWeek' : 'noWeek'" @click="item.isWeekB=!item.isWeekB">{{$t('program.one')}}</div>
                  <div :class="item.isWeekC ? 'yesWeek' : 'noWeek'" @click="item.isWeekC=!item.isWeekC">{{$t('program.two')}}</div>
                  <div :class="item.isWeekD ? 'yesWeek' : 'noWeek'" @click="item.isWeekD=!item.isWeekD">{{$t('program.three')}}</div>
                  <div :class="item.isWeekE ? 'yesWeek' : 'noWeek'" @click="item.isWeekE=!item.isWeekE">{{$t('program.four')}}</div>
                  <div :class="item.isWeekF ? 'yesWeek' : 'noWeek'" @click="item.isWeekF=!item.isWeekF">{{$t('program.five')}}</div>
                  <div :class="item.isWeekG ? 'yesWeek' : 'noWeek'" @click="item.isWeekG=!item.isWeekG">{{$t('program.six')}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import program from "./program.js"
import setText from "./components/setTextMD.vue"
import setMedia from "./components/setMediaMD.vue"
import setWeatherMD from "./components/setWeatherMD.vue"
import setStreaming from "./components/setStreaming.vue"
import setDigitalClock from "./components/setDigitalClock.vue"
import setEnvironmental from "./components/setEnvironmental.vue"
import setSimulationClock from "./components/setSimulationClock.vue"
import setMultiWindow from "./components/setMultiWindow.vue"
import setHtml from "./components/setHtml.vue"
// import setTextTemplate from "./components/setTextTemplate.vue"
import setVistorSource from "./components/setVistorSource.vue"
import setHDMI from "./components/setHDMI.vue"
import setTextToSpeechMD from "./components/setTextToSpeechMD.vue"
import setSensorsShareData from "./components/setSensorsShareData.vue"
import setNegativeIon from "./components/setNegativeIon.vue"
export default {
    data(){
      return {
        programData:{},//数据对象  和上面引入的是同一个
      }
    },
    watch:{
      program:{ //监听的对象  this.programData指向program
        handler(){
          this.programData = program
        },
        deep: true,
        immediate: true,
      },
    },
    computed:{

    },
    components:{
      setText,
      setMedia,
      setDigitalClock,
      setStreaming,
      setEnvironmental,
      setWeatherMD,
      setSimulationClock,
      setMultiWindow,
      setHtml,
      // setTextTemplate,
      setVistorSource,
      setHDMI,
      setTextToSpeechMD,
      setSensorsShareData,
      setNegativeIon
    },
    methods:{
      preservation(isExit){
        program.preservation(isExit)
      },
      addTimeFrom(){
        program.addTimeFrom()
      },
      clearTimeFrom(){
        program.clearTimeFrom()
      },
      removeIndex(index){
        program.removeIndex(index)
      }
      // release(){
      //   program.release()
      // }
    }
}
</script>
<style>
/* 更改element样式 */
.el-divider__text{
  color:#68b5fc;
}
.el-tabs__nav{
  height:45px !important;
}
.el-tabs__item{
  height:46px !important;
}
</style>
<style scoped>
.operation{
  width:28px;
  height:28px;
  font-size:14px;
  border:#dce3f3 solid 1px;
  border-radius: 3px;
  text-align: center;
  line-height:28px;
  cursor: pointer;
}
.TimedPlay{
  width:100%;
  margin-top:8px;
  height:calc(100% - 390px);
  overflow: auto;
}
.TimedPlay_find{
  margin-top:10px;
  border:solid 1px #dce3f3;
  padding:3px;
}
.TimedPlay_find:hover{
  background:#dce3f3;
}
.rightSurface{
  width:335px;
  border-left:#dce3f3 solid 1px;
}
.mainBtn{
  width:100%;
  height:65px;
  border-bottom:#dce3f3 solid 1px;
  position:relative;
}
.preservation{
  border-radius: 6px !important;;
  position: absolute !important;;
  right:180px !important;;
  top:8px !important;;
}
.release{
  border-radius: 6px;
  position: absolute;
  right:10px;
  top:8px;
}
.timeBox{
  margin-top:4px;
}
.setPaging{
  width:100%;
  height:46px;
  border-bottom:#dce3f3 solid 1px;
}
.tab_pane{
  float: left;
  width:calc(50% - 0.5px);
  height:46px;
  text-align: center;
  line-height: 46px;
  cursor: pointer;
}
.tab_pane:nth-child(1){
  border-right:#dce3f3 solid 1px;
}
.XZBack{
  background:#9dc9f3;
}
.NavContent{
  width:100%;
  height:calc(100% - 117px);
  overflow:auto;
}
/* //修改overflow滚动条 外边框 */
.NavContent::-webkit-scrollbar {
  width: 2px;
  height: 4px;
  background-color: rgba(240, 240, 240, 1);;
}
/* //修改overflow滚动条  滚动条 */
.NavContent::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  background-color: #aeb7cf;
}

.OisWeek{
  display: flex;
  justify-content: space-between;
  margin-top:5px;
}
.yesWeek{
  width:28px;
  height:28px;
  font-size:14px;
  background:#43a3fb;
  color:#fff;
  border:#dce3f3 solid 1px;
  border-radius: 3px;
  text-align: center;
  line-height:28px;
  cursor: pointer;
}
.noWeek{
  color:#3e3e3e;
  background:#fff;
  width:28px;
  height:28px;
  font-size:14px;
  border:#dce3f3 solid 1px;
  border-radius: 3px;
  text-align: center;
  line-height:28px;
  cursor: pointer;
}
.elementType{
  height: calc(100% - 101px);
  width: 97%;
  margin-left: 8px;
}
</style>
