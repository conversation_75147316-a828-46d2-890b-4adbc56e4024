<template>
  <div class="standard_total">
    <Left :ProgramData="ProgramData"></Left>
    <Minddle></Minddle>
    <Right></Right>
  </div>
</template>

<script>
import Left from "./programModular/programLeft.vue"
import Minddle from "./programModular/programMiddle.vue"
import Right from "./programModular/programRight.vue"
export default {
    data(){
      return {
        ProgramData:{}
      }
    },
    created(){
     this.ProgramData =JSON.parse(localStorage.getItem("ProgramData"))
    },
    components:{
       Left,Minddle,Right
    },
    methods:{
    },
}
</script>
<style scoped>
.standard_total{
  width:100%;
  height:100%;
  display: flex;
}
</style>