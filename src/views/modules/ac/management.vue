<template>
  <div>
    <!-- 查询条件 -->
    <Form :inline="true" :model="dataForm" @keyup.enter.native="gettablelist(null,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('ac.name') + '/IP'"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" size="large" v-on:click="gettablelist(null,1)">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
        <Button v-if="isAuth('monitor:device:save')" style="margin-right:6px" size="large" type="primary"
          v-on:click="getAC()">
          <div style=" margin:3px 8px">{{ $t('common.newlyBuild') }}</div>
        </Button>
        <!-- <Button v-if="isAuth('monitor:device:delete')" style="margin-right:6px" size="large" type="error">
          <div style="margin:3px 8px">{{ $t('common.batchDel') }}</div>
        </Button> -->
      </FormItem>
    </Form>
    <!-- AC表格 -->
    <Table border :columns="dataConlums" :data="dataList" style="width: 100%" ref="selection"
      :loading="dataListLoading">
      <template slot-scope="{ row, index }" slot="number">
        {{ index + 1 }}
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div v-if="row.isOn === 1 ">
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#on-line"></use>
          </svg>
        </div>
        <div v-else>
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#line"></use>
          </svg>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <!-- <Button v-if="isAuth('monitor:device:update')" type="primary" size="small"
                style="margin-right: 5px;font-size: 11px"
                @click="getAC(row.ip)">{{ $t('common.update') }}
        </Button> -->
        <Button type="error" size="small" style="font-size: 11px" @click="deleteAC(row.acIp)">{{ $t('common.delete') }}
        </Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
      show-elevator show-sizer :page-size-opts="[10,20,50,100]" />

    <!-- 新建和修改弹窗 -->
    <Modal v-model="ACmodal" title="AC" width="30%" @on-ok="buildnewAc()">
      <Form :model="ACinfo">
        <FormItem label="AC IP">
          <!-- <Input-number :max="999" :min="0" v-model="ACinfo.ip[0]"></Input-number> -->
          <Input-number v-model="ACinfo.ip[0]" :max="255" :min="0" style="width:100px"></Input-number>
          <span style="font-size: 24px;">.</span>
          <Input-number v-model="ACinfo.ip[1]" :max="255" :min="0" style="width:100px"></Input-number>
          <span style="font-size: 24px;">.</span>
          <Input-number v-model="ACinfo.ip[2]" :max="255" :min="0" style="width:100px"></Input-number>
          <span style="font-size: 24px;">.</span>
          <Input-number v-model="ACinfo.ip[3]" :max="255" :min="0" style="width:100px"></Input-number>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      /**
       * 查询表单条件
       */
      dataForm: {
        key: ""
      },
      /**
       * 表头
       */
      dataConlums: [
        { type: 'selection', width: 100, align: 'center', fixed: 'left', },
        {
          title: this.$t('cardDevice.number'), fixed: 'left', align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('ac.name'), key: 'acName', align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('ac.name'))
          }
        },
        {
          title: "IP", key: "acIp", align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', "IP")
          }
        },
        {
          title: this.$t('ac.macAddress'), key: "acMacAddress", align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('ac.macAddress'))
          }
        },
        // {
        //   title: this.$t('ac.belongArea'), key: "area", align: "center", tooltip: true,
        //   renderHeader: (h) => {
        //     return h('div', this.$t('ac.belongArea'))
        //   }
        // },
        // {
        //   title: this.$t('ac.belongOrganization'), key: "organization", align: 'center', tooltip: true,
        //   renderHeader: (h) => {
        //     return h('div', this.$t('ac.belongOrganization'))
        //   }
        // },
        // {
        //   title: this.$t('ac.belongProject'), key: "project", align: 'center', tooltip: true,
        //   renderHeader: (h) => {
        //     return h('div', this.$t('ac.belongProject'))
        //   }
        // },
        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center', fixed: 'right',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        },
      ],
      /**
       * 表格数据
       */
      dataList: [],
      /**
       * 模态框的显示状态
       */
      ACmodal: false,
      /**
       * 新增和修改的参数
       */
      ACinfo: {
        ip: [0, 0, 0, 0]
      },
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
    }
  },

  activated() {
    this.gettablelist('loading',null);
  },
  methods: {
    /**
     * 获取表格数据
     */
    gettablelist(loading,isQuery) {
      this.pageIndex = 1
      if (loading) {
        this.dataListLoading = true
      }
      if (isQuery){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/ac/device/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key,
        })
      }).then(({ data }) => {
        // console.log(data);
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.gettablelist(loading,isQuery)
          }
          // 设置选中
          // var select = this.$refs.selection.getSelection().map(item => {
          //   return item.deviceId
          // })
          // if (select && select.length !== 0) {
          //   this.dataList.map(item => {
          //     if (select.indexOf(item.deviceId) != -1) {
          //       item._checked = true
          //     } else {
          //       item._checked = false
          //     }
          //   })
          // }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    /**
     * 新建和修改AC
     */
    getAC(ip) {
      this.ACmodal = true;
      // if (ip) {
      //   // this.ACinfo.ip = ip;
      //   this.ACinfo.ip = ip.split(".")
      // } else {
      //   this.ACinfo.ip = [];
      // }
      this.ACinfo.ip = [0, 0, 0, 0]
    },

    /**
     * 新建AC模态框的确定按钮
     */
    buildnewAc() {
      console.log(this.ACinfo.ip[0] + "." + this.ACinfo.ip[1] + "." + this.ACinfo.ip[2] + "." + this.ACinfo.ip[3])
      if (this.ACinfo.ip[0] && this.ACinfo.ip[1] && this.ACinfo.ip[2] && this.ACinfo.ip[3]) {
        let ip = this.ACinfo.ip[0] + "." + this.ACinfo.ip[1] + "." + this.ACinfo.ip[2] + "." + this.ACinfo.ip[3];
        this.$http({
          url: this.$http.adornUrl('/ac/device/add'),
          method: "post",
          params: this.$http.adornParams({ ip: ip })
        }).then(({ data }) => {
          // console.log(data);
          if (data && data.code === 0) {
            this.gettablelist();
          } else {
            this.$Message.error(data.msg);
          }
        })
      } else {
        this.$Message.error("请输入IP地址");
      }

    },

    /**
     * 删除AC
     */
    deleteAC(ip) {
      // console.log(ip);
      this.$http({
        url: this.$http.adornUrl('/ac/device/delete'),
        method: "get",
        params: {
          ip: ip
        }
      }).then(({ data }) => {
        // console.log(data);
        if (data && data.code === 0) {
          this.gettablelist();
        } else {

        }
      })
    }
  }
}
</script>

<style scoped>

</style>
