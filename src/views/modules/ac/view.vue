<template>
    <div>
        <Row>
            <Col span="5">
                <Input size="large" v-model="dataKey" :placeholder="$t('ac.name') + '/IP'" :border="false" @keyup.native="getTablelist(true)"></Input>
                <Card shadow :style="{'height': tableHeight + 140 + 'px'}"> 
                    <!-- <div class="AcEquipment"> -->
                    <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight + 155"> -->
                    <div :style="{'height':tableHeight + 45+ 'px', 'overflow-y': 'auto'}">
                        <ul class="opera_ul" v-if="AcEquipment && AcEquipment.length > 0">
                            <li v-for="(item, index) in AcEquipment" :key="index" @click="changeSelect(item, index)">
                                <div :class="select == index ? 'select': ''"
                                    style="display: flex;justify-content: space-between;padding: 5px;overflow: hidden;"
                                    :title="item.acName">
                                    <span>{{item.acIp}}<span v-if="item.acName">({{ item.acName.slice(0, 8)
                                    }}...)</span></span>
                                    <span v-if="item.isOn === 1" style="float: right">
                                        <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                                            <use xlink:href="#on-line"></use>
                                        </svg>
                                    </span> 
                                    <span v-else style="float: right">
                                        <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                                            <use xlink:href="#line"></use>
                                        </svg>
                                    </span>
                                </div>
                            </li>
                        </ul>
                        <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
                            {{$t('home.temporarilyNoData')}}
                        </div>
                    <!-- </Scroll> -->
                    </div>
                    <Page size="small" :total="totalPage" :current="pageIndex" :page-size="pageSize"
                        show-elevator show-total @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
                </Card>
            </Col>
            <Col span="19">
                <Spin size="large" fix v-if="loadstatus"></Spin>
                <div class="statistics">
                    <Button type="primary" style="margin-bottom:10px;margin-left: 20px;"
                        @click="refresh()">{{$t('ac.refresh')
                        }}</Button>
                    <!-- <Row> -->
                        <!-- <Col span="12" style="padding:0px 15px"> -->
                        <Card :bordered="false" dis-hover style="margin-left: 20px;">
                            <div class="onlineRate">
                                <span><b>{{ $t('ac.apOnlineRate') }}：</b>{{apOnlineRate +"%"}}</span>
                                <div>
                                    <p><b>{{ $t('ac.apOnlineNumber') }}：</b>{{apOnlineTotal}}</p>
                                    <p><b>{{ $t('ac.apSum') }}：</b>{{apTotal}}</p>
                                </div>
                                <div>
                                    <p><b>{{$t('ac.userOnlineCount')}}：</b>{{userOnlineTotal}}</p>
                                    <p><b>{{ $t('ac.acOnlineUserCount') }}：</b>{{acOnlineUser}}</p>
                                </div>
                            </div>
                        </Card>
                        <!-- </Col> -->
                        <!-- <Col span="12" style="padding:0px 15px">
                        <Card :bordered="false" dis-hover>
                            <div class="onlineTerminal">
                                <p><b>{{$t('ac.userOnlineCount')}}：</b>{{userOnlineTotal}}</p>
                                <p><b>{{ $t('ac.acOnlineUserCount') }}：</b>{{acOnlineUser}}</p>
                            </div>
                        </Card> -->
                        <!-- </Col> -->
                    <!-- </Row> -->
                </div>
                <div class="flowChart">
                    <Row v-show="trafficStatisticsChartinfo.month.length != 0">
                        <Col span="12" style="padding: 15px 0px 0px 20px;">
                            <Card :bordered="false" style="overflow-y: auto">
                                <div id="trafficStatistics" ref="trafficStatistics"
                                    :style="{'width': documentClientWidth - 1036 + 'px', 'height': tableHeight -  32  + 'px',}"></div>
                            </Card>
                        </Col>
                        <Col span="12" style="padding: 15px 0px 0px 20px;">
                            <Card :bordered="false" style="overflow-y: auto">
                                <div id="dailyTrafficStatistics" ref="dailyTrafficStatistics"
                                    :style="{'width': documentClientWidth - 1036 + 'px', 'height': tableHeight - 32 + 'px'}"></div>
                            </Card>
                        </Col>
                    </Row>
                    <div v-show="trafficStatisticsChartinfo.month.length == 0" class="no-data">
                        <div>
                        <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#notData"></use>
                        </svg>
                        <div>{{$t('home.temporarilyNoData')}}</div>
                        </div>
                    </div>
                </div>
            </Col>
        </Row>
        <!-- <div v-else style="text-align: center; font-size:24px">{{$t("home.temporarilyNoData")}}</div> -->
    </div>
</template>

<script>
export default {
    data() {
        return {
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataKey: '',
            /**
             * 选择的AC设备
             */
            select: 0,
            /**
             * ap设备在线率
             */
            apOnlineRate: '',
            /**
             * 在线设备数
             */
            apOnlineTotal: '',
            /**
             * 设备总数
             */
            apTotal: '',
            /**
            * 用户上线总次数
            */
            userOnlineTotal: '',
            /**
             * ac所有在线用户数
             */
            acOnlineUser: '',
            /**
             * AC设备列表
             */
            AcEquipment: [],
            /**
             * 流量统计图表
             */
            trafficStatisticsChart: '',
            /**
             * 流量图表数据
             */
            trafficStatisticsChartinfo: {
                month: [],
                upstreamTraffic: [],
                downlinkTraffic: [],
            },
            /**
             * 当日流量统计图表
             */
            dailyTrafficStatisticsChart: "",
            /**
             * 当日流量统计图表数据
             */
            dailyTrafficStatisticsinfo: {
                time: [],
                upstreamTraffic: [],
                downlinkTraffic: [],
            },
            /**
             * 当前选中的ip
             */
            acIp: '',
            /**
             * 刷新状态
             */
            loadstatus: false,
        }
    },
    activated() {
        this.getTablelist();
    },
    methods: {
        // 每页数
        sizeChangeHandle (val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getTablelist()
        },
        // 当前页
        currentChangeHandle (val) {
            this.pageIndex = val
            this.getTablelist()
        },
        /**
         * 获取列表数据
         */
        getTablelist(isQuery) {
            if (isQuery) {
                this.pageIndex = 1
            }
            this.$http({
                url: this.$http.adornUrl('/ac/device/acList'),
                method: 'get',
                params: this.$http.adornParams({
                    'page': this.pageIndex,
                    'limit': this.pageSize,
                    'key': this.dataKey,
                })
            }).then(({ data }) => {
                // console.log(data);
                if (data && data.code === 0) {
                    this.AcEquipment = data.page.list
                    this.totalPage = data.page.totalCount
                    if (this.AcEquipment.length > 0) {
                        // this.acIp = this.AcEquipment[0].acIp;
                        this.select = 0
                        this.changeSelect(this.AcEquipment[0], 0);
                    }
                } else {
                    this.AcEquipment = []
                    this.totalPage = 0
                }
            })
        },
        handleReachBottom () {
            return new Promise(resolve => {
                setTimeout(() => {
                this.pageSize = this.pageSize + 10;
                if (this.pageSize > this.totalCount) {
                    this.$Message.warning(this.$t('common.noMoreData'));
                    return;
                }
                this.getTablelist();
                resolve();
                }, 200);
            });
        },
        /**
         * 切换AC设备
         */
        changeSelect(item, index) {
            // console.log(this.documentClientWidth);
            this.acIp = item.acIp;
            this.trafficStatisticsChartinfo.month = [];
            this.trafficStatisticsChartinfo.downlinkTraffic = [];
            this.trafficStatisticsChartinfo.upstreamTraffic = [];
            this.dailyTrafficStatisticsinfo.time = [];
            this.dailyTrafficStatisticsinfo.downlinkTraffic = [];
            this.dailyTrafficStatisticsinfo.upstreamTraffic = [];
            this.select = index;
            this.$http({
                url: this.$http.adornUrl("/ac/device/info"),
                method: "get",
                params: this.$http.adornParams({
                    ip: this.acIp,
                })
            }).then(({ data }) => {
                // console.log(data);
                if (data && data.code == 0) {
                    this.apOnlineRate = data.data.apOnlineRate;
                    this.apOnlineTotal = data.data.apOnlineTotal;
                    this.apTotal = data.data.apTotal;
                    this.userOnlineTotal = data.data.userOnlineTotal;
                    this.acOnlineUser = data.data.acOnlineUser;
                    if (data.data.monthlyFlow && data.data.dayLyFlow) {
                        data.data.monthlyFlow.forEach(element => {
                            this.trafficStatisticsChartinfo.month.push(element.date);
                            this.trafficStatisticsChartinfo.downlinkTraffic.push(element.downlinkTraffic);
                            this.trafficStatisticsChartinfo.upstreamTraffic.push(element.upstreamTraffic);
                        });
                        data.data.dayLyFlow.forEach(element => {
                            this.dailyTrafficStatisticsinfo.time.push(element.date);
                            this.dailyTrafficStatisticsinfo.downlinkTraffic.push(element.downlinkTraffic);
                            this.dailyTrafficStatisticsinfo.upstreamTraffic.push(element.upstreamTraffic);
                        });
                    }
                    // console.log(this.dailyTrafficStatisticsinfo.downlinkTraffic);
                    this.getCharts();
                } else {
                    this.apOnlineRate = 0;
                    this.apOnlineTotal = 0;
                    this.apTotal = 0;
                    this.userOnlineTotal = 0;
                    this.acOnlineUser = 0;
                    this.trafficStatisticsChartinfo.month = [];
                    this.trafficStatisticsChartinfo.downlinkTraffic = [];
                    this.trafficStatisticsChartinfo.upstreamTraffic = [];
                    this.dailyTrafficStatisticsinfo.time = [];
                    this.dailyTrafficStatisticsinfo.downlinkTraffic = [];
                    this.dailyTrafficStatisticsinfo.upstreamTraffic = [];
                }
            })
        },

        /**
         * 获取图表信息
         */
        getCharts() {
            //流量统计
            if (this.trafficStatisticsChart != null && this.trafficStatisticsChart != "" && this.trafficStatisticsChart != undefined) {
                this.trafficStatisticsChart.dispose();
            }
            this.trafficStatisticsChart = this.$echarts.init(this.$refs.trafficStatistics);
            this.trafficStatisticsChart.setOption(this.getTrafficStatistics);

            //当日流量统计
            if (this.dailyTrafficStatisticsChart != null && this.dailyTrafficStatisticsChart != "" && this.dailyTrafficStatisticsChart != undefined) {
                this.dailyTrafficStatisticsChart.dispose();
            }
            this.dailyTrafficStatisticsChart = this.$echarts.init(this.$refs.dailyTrafficStatistics);
            this.dailyTrafficStatisticsChart.setOption(this.getdailyTrafficStatistics);
        },

        /**
         * 刷新按钮
         */
        refresh() {
            this.loadstatus = true;
            this.trafficStatisticsChartinfo.month = [];
            this.trafficStatisticsChartinfo.downlinkTraffic = [];
            this.trafficStatisticsChartinfo.upstreamTraffic = [];
            this.dailyTrafficStatisticsinfo.time = [];
            this.dailyTrafficStatisticsinfo.downlinkTraffic = [];
            this.dailyTrafficStatisticsinfo.upstreamTraffic = [];
            this.$http({
                url: this.$http.adornUrl("/ac/device/refresh"),
                method: "get",
                params: this.$http.adornParams({
                    ip: this.acIp
                })
            }).then((data) => {
                // console.log(data);
                this.loadstatus = false;
                if (data.data && data.data.code == 0) {
                    this.apOnlineRate = data.data.data.apOnlineRate;
                    this.apOnlineTotal = data.data.data.apOnlineTotal;
                    this.apTotal = data.data.data.apTotal;
                    this.userOnlineTotal = data.data.data.userOnlineTotal;
                    this.acOnlineUser = data.data.data.acOnlineUser;
                    if (data.data.data.monthlyFlow && data.data.data.dayLyFlow) {
                        data.data.data.monthlyFlow.forEach(element => {
                            this.trafficStatisticsChartinfo.month.push(element.date);
                            this.trafficStatisticsChartinfo.downlinkTraffic.push(element.downlinkTraffic);
                            this.trafficStatisticsChartinfo.upstreamTraffic.push(element.upstreamTraffic);
                        });
                        data.data.data.dayLyFlow.forEach(element => {
                            this.dailyTrafficStatisticsinfo.time.push(element.date);
                            this.dailyTrafficStatisticsinfo.downlinkTraffic.push(element.downlinkTraffic);
                            this.dailyTrafficStatisticsinfo.upstreamTraffic.push(element.upstreamTraffic);
                        });
                    }

                    this.getCharts();
                } else {
                    this.apOnlineRate = 0;
                    this.apOnlineTotal = 0;
                    this.apTotal = 0;
                    this.userOnlineTotal = 0;
                    this.acOnlineUser = 0;
                    this.trafficStatisticsChartinfo.month = [];
                    this.trafficStatisticsChartinfo.downlinkTraffic = [];
                    this.trafficStatisticsChartinfo.upstreamTraffic = [];
                    this.dailyTrafficStatisticsinfo.time = [];
                    this.dailyTrafficStatisticsinfo.downlinkTraffic = [];
                    this.dailyTrafficStatisticsinfo.upstreamTraffic = [];
                    this.getCharts();
                }
            })
        }

    },
    watch: {
        'language': function (newVal, OldVal) {
            this.getCharts();
        }
    },
    computed: {
        documentClientWidth: {
            get() { return this.$store.state.common.documentClientWidth },
        },
        language: {
            get() { return this.$store.state.language.language },
        },
        tableHeight: {
            get () { return this.$store.state.common.tableHeight },
        },
        /**
         * 获取流量统计
         */
        getTrafficStatistics: {
            get() {
                return {
                    title: {
                        text: this.$t('ac.flowStatistics')
                    },
                    xAxis: {
                        type: 'category',
                        data: this.trafficStatisticsChartinfo.month
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value} MB'
                        }
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: [this.$t('ac.upstreamTraffic'), this.$t('ac.downlinkTraffic')]
                    },
                    series: [
                        {
                            data: this.trafficStatisticsChartinfo.downlinkTraffic,
                            type: 'line',
                            name: this.$t('ac.upstreamTraffic')
                        },
                        {
                            data: this.trafficStatisticsChartinfo.upstreamTraffic,
                            type: 'line',
                            name: this.$t('ac.downlinkTraffic')
                        }
                    ]
                }
            }
        },

        /**
         * 获取当日流量统计
         */
        getdailyTrafficStatistics: {
            get() {
                return {
                    title: {
                        text: this.$t('ac.flowStatisticsToday')
                    },
                    xAxis: {
                        type: 'category',
                        data: this.dailyTrafficStatisticsinfo.time
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value} MB'
                        }
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: [this.$t('ac.upstreamTraffic'), this.$t('ac.downlinkTraffic')]
                    },
                    series: [
                        {
                            data: this.dailyTrafficStatisticsinfo.downlinkTraffic,
                            type: 'line',
                            name: this.$t('ac.upstreamTraffic')
                        },
                        {
                            data: this.dailyTrafficStatisticsinfo.upstreamTraffic,
                            type: 'line',
                            name: this.$t('ac.downlinkTraffic')
                        }
                    ]
                }
            }
        }
    }

}
</script>

<style scoped>
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
}
.onlineRate {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    font-size: 16px;
}
.onlineTerminal {
    padding: 15px;
    font-size: 16px;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  background-color: rgb(255, 255, 255);
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-bottom: 5px;
  cursor: pointer;
}
.opera_ul li:hover {
  background-color: rgb(204, 204, 204);
  cursor: pointer;
}

.select {
    background-color: rgb(204, 204, 204);
}
</style>
