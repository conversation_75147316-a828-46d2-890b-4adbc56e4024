<template>
  <div>
    <div :style="{height: documentClientHeight - 110 + 'px', overflow: 'hidden'}">
      <span class="userInfo_title">{{$t('nav.订单')}}</span>
      <Divider />
      <div class="admin-order" :style="{height: documentClientHeight - 156 + 'px'}">
        <div class="admin-order-info-header">
          <h4>{{$t('pay.currentVersion')}}
            <span v-if="version == null || version == 0"> {{$t('pay.tips1')}} </span>
              <span v-else-if="version == -1" style="color: #CD7F32">
                <img style="vertical-align: middle;" src="@/assets/img/VIP4.png" width="23" height="23">
                {{$t('pay.superVip')}}</span>
              <span v-else-if="version == 1" style="color: #c0c0c0">
                <img style="vertical-align: middle;" src="@/assets/img/VIP1.png" width="20" height="20">
                {{$t('pay.silverCardVip')}}</span>
              <span v-else-if="version == 2" style="color: #ffd700">
                <img style="vertical-align: middle;" src="@/assets/img/VIP2.png" width="20" height="20">
                {{$t('pay.goldCardVip')}}</span>
              <span v-else-if="version == 3" style="color: #5cadff">
                <img style="vertical-align: middle;" src="@/assets/img/VIP3.png" width="23" height="23">
                {{$t('pay.diamondVip')}}</span>
              <span v-else-if="version == 4" style="color: #CD7F32">
                <img style="vertical-align: middle;" src="@/assets/img/VIP4.png" width="23" height="23">
                {{$t('pay.vip4')}}</span>
              <span v-else></span>
          </h4>
        </div>
        <div class="admin-order-info-field">
          <div class="admin-order-info-field_title">{{$t('pay.numberOfTerminals')}}</div>
          <br/>
          <div class="admin-order-info-field_total">{{deviceTotalCount}}{{$t('home.piece')}}</div>
          <div class="admin-order-info-field_progress"><Progress :percent="percent" :stroke-width="15" :status="percent > 80 ? 'wrong':'active'" hide-info /></div>
          <div class="admin-order-info-field_rest" v-if="version == -1 || version == 4">{{$t('pay.TheRemainingAmount')}} ∞ {{$t('home.piece')}}</div>
          <div class="admin-order-info-field_rest" v-else>{{$t('pay.TheRemainingAmount')}} {{total - deviceTotalCount}} {{$t('home.piece')}}</div>
        </div>
        <div class="admin-order-info-field" v-if="version != -1">
          <div class="admin-order-info-field_title">{{$t('pay.ExpireDate')}}</div>
          <br/>
          <div style="margin-left: 20px;">{{expireDate}}</div>
        </div>
        <div class="title">{{$t('pay.OrderRecord')}}</div>
        <Table border :columns="columns" :data="order" :height="documentClientHeight - 314">
          <template slot-scope="{ row, index }" slot="orderInfo">
            <span v-if="row.vipStatus === 1">{{$t('pay.silverCardVip')}} ({{$t('pay.100ControlCards')}})</span>
            <span v-else-if="row.vipStatus === 2">{{$t('pay.goldCardVip')}} ({{$t('pay.500ControlCards')}})</span>
            <span v-else-if="row.vipStatus === 3">{{$t('pay.diamondVip')}} ({{$t('pay.NumberOfControlCards1500')}})</span>
            <span v-else-if="row.vipStatus === 4">{{$t('pay.vip4')}} (∞)</span>
          </template>
          <template slot-scope="{ row, index }" slot="month">
            {{row.month / 12}} {{$t('program.year')}}
          </template>
          <template slot-scope="{ row, index }" slot="tradeStatus">
            <span v-if="row.tradeStatus === 'CREATE_ORDER'">{{$t('pay.unpaid')}}</span>
            <span v-else-if="row.tradeStatus === 'WAIT_BUYER_PAY'">{{$t('pay.transactionCreation')}}</span>
            <span v-else-if="row.tradeStatus === 'BANK_RECEIPT_ERROR'">{{$t('pay.ErrorUploadingBankReceipt')}}</span>
            <span v-else-if="row.tradeStatus === 'TRADE_CLOSED'">{{$t('pay.UnpaidTransactionTimeoutClosed')}}</span>
            <span v-else-if="row.tradeStatus === 'WAIT_SELLER_CONFIRM'">{{$t('pay.WaitingForSellerToConfirm')}}</span>
            <span v-else-if="row.tradeStatus === 'TRADE_SUCCESS'">{{$t('pay.paymentSuccessful')}}</span>
            <!-- <span v-else-if="row.tradeStatus === 'TRADE_SUCCESS' || row.tradeStatus === 'TRADE_FINISHED'">{{$t('pay.paymentSuccessful')}}</span> -->
          </template>
          <template slot-scope="{ row, index }" slot="totalAmount">
            $ {{row.totalAmount | filterTotalPrice}} （
            <span v-if="row.purchaseStatus == 1">{{$t('pay.newPurchase')}}</span>
            <span v-else-if="row.purchaseStatus == 2">{{$t('pay.Renewal')}}</span>
            <span v-else-if="row.purchaseStatus == 3">{{$t('pay.upgrade')}}</span>
            ）
          </template>
          <template slot-scope="{ row, index }" slot="operation">
            <a href="javascript:void(0)" style="margin-right: 10px;" @click="orderDetailsHander(row.outTradeNo)">{{$t('pay.OrderDetails')}}</a>
            <a href="javascript:void(0)" style="margin-right: 10px;" @click="orderUpdateHander(row.outTradeNo)"
              v-if="row.tradeStatus === 'WAIT_SELLER_CONFIRM' || row.tradeStatus === 'BANK_RECEIPT_ERROR'">{{$t('common.update')}}</a>
            <a href="javascript:void(0)" style="margin-right: 10px;" @click="orderPay(row)" v-if="row.tradeStatus === 'CREATE_ORDER'">{{$t('pay.pay')}}</a>
            <a href="javascript:void(0)" v-if="row.tradeStatus === 'CREATE_ORDER'" @click="cancelOrder">{{$t('sys.close')}}</a>
          </template>
        </Table>
      </div>
    </div>
    <Modal v-model="visibleOrderDetails" width="700" footer-hide>
      <p slot="header" style="text-align:center">
          <span>{{$t('pay.OrderDetails')}}</span>
      </p>
      <Alert show-icon>{{$t('pay.contactOurSalesStaff')}}</Alert>
      <div class="order-detail">
        <div class="order-detail__content">
          <table class="order-detail__table">
            <thead>
              <tr>
                <th style="width: 200px;">{{$t('pay.Serve')}}</th>
                <th style="width: 288px;">{{$t('pay.Optional')}}</th>
                <th style="width: 264px;">{{$t('pay.price')}}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{$t('card.version')}}</td>
                <td>
                  <span v-if="dataForm.vipStatus === 1">{{$t('pay.silverCardVip')}} ({{$t('pay.100ControlCards')}})</span>
                  <span v-else-if="dataForm.vipStatus === 2">{{$t('pay.goldCardVip')}} ({{$t('pay.500ControlCards')}})</span>
                  <span v-else-if="dataForm.vipStatus === 3">{{$t('pay.diamondVip')}} ({{$t('pay.NumberOfControlCards1500')}})</span>
                  <span v-else-if="dataForm.vipStatus === 4">{{$t('pay.vip4')}} (∞)</span>
                </td>
                <td rowspan="2">
                  <span v-if="dataForm.vipStatus === 1">$ 800/ {{$t('program.year')}} * {{dataForm.month / 12}} {{$t('program.year')}} = ￥{{800 * (dataForm.month / 12)}}</span>
                  <span v-else-if="dataForm.vipStatus === 2">$ 1600/ {{$t('program.year')}} * {{dataForm.month / 12}} {{$t('program.year')}} = ￥{{1600 * (dataForm.month / 12)}}</span>
                  <span v-else-if="dataForm.vipStatus === 3">$ 2600/ {{$t('program.year')}} * {{dataForm.month / 12}} {{$t('program.year')}} = ￥{{2600 * (dataForm.month / 12)}}</span>
                  <span v-else-if="dataForm.vipStatus === 4">$ 5000/ {{$t('program.year')}} * {{dataForm.month / 12}} {{$t('program.year')}} = ￥{{5000 * (dataForm.month / 12)}}</span>
                </td>
              </tr>
              <tr>
                <td>{{$t('pay.Years')}}</td>
                <td>{{dataForm.month / 12}} {{$t('program.year')}}
                  <span class="subtext">{{$t('pay.deadline')}}
                    <span class="text-mono">{{dataForm.estimatedTime}}</span>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
          <Timeline style="margin-top: 10px" v-if="dataForm.paymentProcedures && dataForm.paymentProcedures.length > 0">
            <TimelineItem v-for="(item, index) in dataForm.paymentProcedures" :key="index">
                <p class="time">{{item.createTime}}</p>
                <div class="content">
                  <span v-if="item.tradeStatus === 'CREATE_ORDER'">{{$t('pay.unpaid')}}</span>
                  <span v-else-if="item.tradeStatus === 'WAIT_BUYER_PAY'">{{$t('pay.transactionCreation')}}</span>
                  <span v-else-if="item.tradeStatus === 'BANK_RECEIPT_ERROR'">
                    {{$t('pay.ErrorUploadingBankReceipt')}}
                    <Card dis-hover>
                        <template #title>{{$t('file.ApprovalComments')}}</template>
                        <p>{{item.remark}}</p>
                    </Card>
                  </span>
                  <span v-else-if="item.tradeStatus === 'TRADE_CLOSED'">{{$t('pay.UnpaidTransactionTimeoutClosed')}}</span>
                  <span v-else-if="item.tradeStatus === 'WAIT_SELLER_CONFIRM'">{{$t('pay.WaitingForSellerToConfirm')}}</span>
                  <span v-else-if="item.tradeStatus === 'TRADE_SUCCESS'">{{$t('pay.paymentSuccessful')}}</span>
                  <!-- <span v-else-if="item.tradeStatus === 'TRADE_SUCCESS' || item.tradeStatus === 'TRADE_FINISHED'">{{$t('pay.paymentSuccessful')}}</span> -->
                </div>
            </TimelineItem>
          </Timeline>
        </div>
      </div>
    </Modal>
    <Modal v-model="visibleOrderUpdate" width="700">
      <p slot="header" style="text-align:center">
          <span>{{$t('common.update')}}</span>
      </p>
      <Alert show-icon>{{$t('pay.orderUpdateTips')}}</Alert>
      <Form :model="formItem" label-position="top">
        <FormItem>
          <div class="bankImg" @click="imgClickHandler">
            <img v-show="formItem.file" id="img" src="" style="width: 100%; height: 100%"/>
            <img v-show="formItem.fileId" ref="imgId" src="" style="width: 100%; height: 100%"/>
          </div>
          <div style="display:inline-block;">
            <Upload :before-upload="handleUpload" :action="''" accept="image/jpg, image/png">
              <Button icon="ios-cloud-upload-outline">{{$t('screen.selectFile')}}</Button>
            </Upload>
            <span>{{$t('pay.uploadTip')}}</span>
          </div>
        </FormItem>
        <FormItem :label="$t('pay.BankCardNumber')">
            <Input v-model="formItem.cardId" :placeholder="$t('pay.bankCardNumberWhenTransferring')" style="width: 400px"></Input>
        </FormItem>
        <FormItem :label="$t('pay.companyName')">
            <Input v-model="formItem.companyName" :placeholder="$t('pay.companyName')" style="width: 400px"></Input>
        </FormItem>
        <FormItem :label="$t('pay.address')">
            <Input v-model="formItem.address" :placeholder="$t('pay.address')" style="width: 400px"></Input>
        </FormItem>
        <FormItem :label="$t('pay.contactPerson')">
            <Input v-model="formItem.contactPerson" :placeholder="$t('pay.contactPerson')" style="width: 400px"></Input>
        </FormItem>
        <FormItem :label="$t('pay.telephone')">
            <Input v-model="formItem.telephone" :placeholder="$t('pay.telephone')" style="width: 400px"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button size="large" @click="visibleOrderUpdate = false">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" :loading="loadingOrderUpdate" @click="dataFormSubmit()">{{$t('common.update')}}</Button>
      </div>
    </Modal>
    <Modal v-model="dialogVisible" footer-hide>
      <img w-full v-if="formItem.file" id="fullImg" src="" style="width: 100%; height: 100%" alt="Preview Image" />
      <img w-full v-if="formItem.fileId" ref="fullImgFileId" src=""  style="width: 100%; height: 100%" alt="Preview Image" />
    </Modal>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  data () {
    return {
      version: 0,
      total: 10,
      deviceTotalCount: 0,
      percent: 0,
      expireDate: '',
      columns: [
        {
            title: this.$t('pay.orderNumber'),
            key: 'outTradeNo',
            width: '150',
            fixed: 'left',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.orderNumber'))
            }
        },
        {
            title: this.$t('common.createTime'),
            key: 'createTime',
            width: '180',
            renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
            }
        },
        {
            title: this.$t('pay.OrderDetails'),
            key: 'orderInfo',
            slot: 'orderInfo',
            width: '240',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.OrderDetails'))
            }
        },
        {
            title: this.$t('pay.Years'),
            key: 'month',
            slot: 'month',
            width: '100',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.Years'))
            }
        },
        {
            title: this.$t('pay.PaymentStatus'),
            key: 'tradeStatus',
            slot: 'tradeStatus',
            width: '150',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.PaymentStatus'))
            }
        },
        {
            title: this.$t('pay.amount'),
            key: 'totalAmount',
            slot: 'totalAmount',
            width: '170',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.amount'))
            }
        },
        {
            title: this.$t('pay.companyName'),
            key: 'companyName',
            align: 'center',
            width: '180',
            renderHeader:(h)=>{
                return h('div',this.$t('pay.companyName'))
            }
        },
        {
            title: this.$t('pay.address'),
            key: 'address',
            align: 'center',
            width: '180',
            renderHeader:(h)=>{
                return h('div',this.$t('pay.address'))
            }
        },
        {
            title: this.$t('pay.contactPerson'),
            key: 'contactPerson',
            align: 'center',
            width: '180',
            renderHeader:(h)=>{
                return h('div',this.$t('pay.contactPerson'))
            }
        },
        {
            title: this.$t('pay.telephone'),
            key: 'telephone',
            align: 'center',
            width: '180',
            renderHeader:(h)=>{
                return h('div',this.$t('pay.telephone'))
            }
        },
        {
          title: this.$t("common.operation"),
          slot: "operation",
          fixed: 'right',
          align: "center",
          width: 250,
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        }
      ],
      order: [],
      dataForm: {},
      visibleOrderDetails: false,
      loadingOrderDetails: false,
      visibleOrderUpdate: false,
      loadingOrderUpdate: false,
      formItem: {
        file: null,
        fileId: "",
        cardId: "",
        outTradeNo: "",
        companyName: "",
        address: "",
        contactPerson: "",
        telephone: ""
      },
      dialogVisible: false,
      token: this.$cookie.get('token'),
    }
  },
  activated() {
    // 查询基本信息
    this.$http({
      url: this.$http.adornUrl(`/sys/user/orderInfo`),
      method: "get",
      params: this.$http.adornParams(),
    }).then(({ data }) => {
      this.version = data.orderInfo.version
      this.total = data.orderInfo.total
      this.deviceTotalCount = data.orderInfo.deviceTotalCount
      this.percent = this.deviceTotalCount / this.total * 100
      if (this.percent > 100) {
        this.percent = 100
      }
      // 如果当前用户为超级VIP
      if (this.version == -1 || this.version == 4) {
        this.percent = 0
      }
      this.expireDate = data.orderInfo.expireDate
    }).then(() => {
      this.$http({
        url: this.$http.adornUrl("/sys/pay/list"),
        method: "get",
        data: this.$http.adornData(),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.order = data.list
        } else {
          this.$Message.error(data.msg)
        }
      })
    })
  },
  methods: {
    // 取消订单
    cancelOrder() {
      if (this.order && this.order[0]) {
        this.$Modal.confirm({
          title: this.$t('common.tips'),
          content: this.$t('common.delete_current_option'),
          okText: this.$t('common.confirm'),
          cancelText: this.$t('common.cancel'),
          onOk: () => {
            this.$http({
              url: this.$http.adornUrl('/sys/pay/cancelOrder'),
              method: 'get',
              params: this.$http.adornParams({'orderNo': this.order[0].outTradeNo})
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                    this.$router.push({ name: 'orderServer-payServer' })
                    this.menuActiveName = "付费服务";
                    this.subMenuActiveName = "订购服务"
                  }
                })
              } else {
                this.$Message.error(data.msg)
              }
            })
          }
        })
      } else {
        this.$Message.error(this.$t('pay.CancellationLineItemCannotBeEmpty'))
      }
    },
    // 跳转支付
    orderPay(order) {
      this.$router.push({ name: 'orderServer-payServer', params: {'order': order}})
      this.menuActiveName = "付费服务";
      this.subMenuActiveName = "订购服务"
    },
    orderDetailsHander(orderNo) {
      this.visibleOrderDetails = true
      this.$http({
        url: this.$http.adornUrl(`/sys/pay/orderDetails/${orderNo}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm = data.payBills
        }
      })
    },
    orderUpdateHander(orderNo) {
      this.formItem.cardId = null
      this.formItem.fileId = null
      this.formItem.outTradeNo = null
      this.formItem.file = null
      this.visibleOrderUpdate = true
      this.$http({
        url: this.$http.adornUrl(`/sys/pay/info/${orderNo}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.formItem.cardId = data.payBills.bankCardNumber
          this.formItem.fileId = data.payBills.bankReceiptPath
          this.formItem.outTradeNo = data.payBills.outTradeNo
          this.formItem.companyName = data.payBills.companyName
          this.formItem.address = data.payBills.address
          this.formItem.contactPerson = data.payBills.contactPerson
          this.formItem.telephone = data.payBills.telephone
          this.$refs.imgId.src = this.$http.adornUrl(`/sys/order/download/`) + this.formItem.fileId + '?token=' + this.token
        }
      })
    },
    handleUpload(selectFile) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      if(ele === "image" && selectFile.size > (5 * 1024 *1024)){
        this.$Message.error(this.$t('screen.picture')+'：'+selectFile.name +this.$t('screen.sizeMore')+ '5M!')
        this.selectFile = null //超过大小将文件清空
        return false
      }
      //判断当前是否支持使用FileReader
      if(window.FileReader){
        //创建读取文件的对象
        var fr = new FileReader();
        //以读取文件字符串的方式读取文件 但是不能直接读取file
        //因为文件的内容是存在file对象下面的files数组中的
        //该方法结束后图片会以data:URL格式的字符串（base64编码）存储在fr对象的result中
        fr.readAsDataURL(selectFile);
        fr.onloadend = function(){
          document.getElementById("img").src = fr.result;
        }
      }
      this.formItem.file = selectFile
      this.formItem.fileId = null
      return false
    },
    imgClickHandler() {
      if (this.formItem.file) {
        this.dialogVisible = true
        //判断当前是否支持使用FileReader
        if(window.FileReader){
          //创建读取文件的对象
          var fr = new FileReader();
          //以读取文件字符串的方式读取文件 但是不能直接读取file
          //因为文件的内容是存在file对象下面的files数组中的
          //该方法结束后图片会以data:URL格式的字符串（base64编码）存储在fr对象的result中
          fr.readAsDataURL(this.formItem.file);
          fr.onloadend = function(){
            document.getElementById("fullImg").src = fr.result;
          }
        }
      } else if(this.formItem.fileId) {
        this.dialogVisible = true
        this.$refs.fullImgFileId.src = this.$http.adornUrl(`/sys/order/download/`) + this.formItem.fileId + '?token=' + this.token
      }
    },
    dataFormSubmit () {
      if (this.formItem.file == null && this.formItem.fileId == null) {
        this.$Message.error(this.$t('pay.PleaseUploadBankReceipt'))
        return;
      }
      if (this.formItem.cardId == "") {
        this.$Message.error(this.$t('pay.PleaseEnterBankCardNumber'))
        return;
      }
      if (this.formItem.companyName == '') {
        this.$Message.error(this.$t('pay.companyNameTips'))
        return
      }
      if (this.formItem.address == '') {
        this.$Message.error(this.$t('pay.addressTips'))
        return
      }
      if (this.formItem.contactPerson == '') {
        this.$Message.error(this.$t('pay.contactPersonTips'))
        return
      }
      if (this.formItem.telephone == '') {
        this.$Message.error(this.$t('pay.telephoneTips'))
        return 
      }
      this.loadingOrderUpdate = true;
      let formData = new FormData()
      if (this.formItem.fileId) {
        formData.append("fileId",this.formItem.fileId);
      }
      formData.append("file",this.formItem.file);
      formData.append("cardId",this.formItem.cardId);
      formData.append("companyName",this.formItem.companyName);
      formData.append("address",this.formItem.address);
      formData.append("contactPerson",this.formItem.contactPerson);
      formData.append("telephone",this.formItem.telephone);
      formData.append("outTradeNo",this.formItem.outTradeNo)
      axios.request({
        url: this.$http.adornUrl('/sys/pay/uploadBankReceipt'),
        method: 'post',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data', 'token': this.token }
      }).then(res => {
        // 上传成功处理
        if (res.data) {
          if (res.data.code === 0) {
            this.loadingOrderUpdate = false
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                this.visibleOrderUpdate = false
                this.$router.push({ name: 'orderServer-order'})
                this.menuActiveName = "付费服务";
                this.subMenuActiveName = "订单"
              }
            })
          } else {
            this.$Message.error({
              content: res.data.msg,
              duration: 0.5,
              onClose: () => {
                this.visibleOrderUpdate = false
                this.loadingOrderUpdate = false
                this.visible = false
              }
            })
          }

        }
      })
    },
  },
  computed: {
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight;
      },
    },
    menuActiveName: {
      get () { return this.$store.state.common.menuActiveName },
      set (val) { this.$store.commit('common/updateMenuActiveName', val) }
    },
    subMenuActiveName: {
      get () { return this.$store.state.common.subMenuActiveName },
      set (val) { this.$store.commit('common/updatesubMenuActiveName', val) }
    },
  },
  filters: {
    filterTotalPrice(val) {
      return val.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },
  },
}
</script>

<style scoped>
.userInfo_title {
  font-size: 23px;
  font-weight: 600;
}
.admin-order {
  overflow-x: hidden;
  overflow-y: auto;
}
.admin-order-info-header {
  margin-top: 10px;
}
.admin-order-info-header h4 {
  font-size: 18px;
  font-weight: 500!important;
}
.admin-order-info-field {
  font-size: 16px;
  margin-top: 10px;
}
.admin-order-info-field div {
  display: inline-block;
}
.admin-order-info-field_title {
  font-weight: bold;
  width: 155px;
}
.admin-order-info-field_total {
  width: 70px;
  margin-left: 20px;
}
.admin-order-info-field_progress {
  width: 600px;
}
.admin-order-info-field_rest {
  margin-left: 20px;
}
.title {
  font-size: 18px;
  font-weight: 500;
  margin-top: 40px;
  margin-bottom: 20px;
}
.order-detail__table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}
.order-detail__table tr th {
  background-color: #fafbfb;
  color: #909aaa;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  border: 1px solid #ecedf0;
  padding: 8px 16px;
}
.order-detail__table tr {
    display: table-row;
    vertical-align: inherit;
    border-color: inherit;
}
.order-detail__table tr td {
  border: 1px solid #ecedf0;
  padding: 8px 16px;
}
.order-detail__table tr .subtext {
  color: #909aaa;
  font-size: 12px;
  margin-left: 8px;
}
.bankImg {
  display:inline-block;
  width: 120px;
  height: 120px;
  border: 1px solid #dadee3;
}
.time{
    font-size: 14px;
    font-weight: bold;
}
.content{
    padding-left: 5px;
}
</style>
