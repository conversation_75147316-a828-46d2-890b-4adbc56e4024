<template>
  <div class="modules_pay">
    <div :style="{height: documentClientHeight - 110 + 'px', overflow: 'hidden'}">
      <span class="userInfo_title">{{$t('pay.PayForTheOrder')}}</span>
      <!-- <Divider /> -->
      <!-- <div class="admin-order" :style="{height: documentClientHeight - 195 + 'px'}"> -->
      <div class="admin-order" :style="{height: documentClientHeight - 156 + 'px'}">
        <Table border :columns="columns" :data="order">
          <template slot-scope="{ row, index }" slot="orderInfo">
            <span v-if="row.vipStatus === 1">{{$t('pay.silverCardVip')}} ({{$t('pay.100ControlCards')}})</span>
            <span v-else-if="row.vipStatus === 2">{{$t('pay.goldCardVip')}} ({{$t('pay.500ControlCards')}})</span>
            <span v-else-if="row.vipStatus === 3">{{$t('pay.diamondVip')}} ({{$t('pay.NumberOfControlCards1500')}})</span>
            <span v-else-if="row.vipStatus === 4">{{$t('pay.vip4')}} (∞)</span>
          </template>
          <template slot-scope="{ row, index }" slot="month">
            {{row.month / 12}} {{$t('program.year')}}
          </template>
          <template slot-scope="{ row, index }" slot="tradeStatus">
            <span v-if="row.tradeStatus === 'CREATE_ORDER'">{{$t('pay.unpaid')}}</span>
            <span v-else-if="row.tradeStatus === 'WAIT_BUYER_PAY'">{{$t('pay.transactionCreation')}}</span>
            <span v-else-if="row.tradeStatus === 'BANK_RECEIPT_ERROR'">{{$t('pay.ErrorUploadingBankReceipt')}}</span>
            <span v-else-if="row.tradeStatus === 'TRADE_CLOSED'">{{$t('pay.UnpaidTransactionTimeoutClosed')}}</span>
            <span v-else-if="row.tradeStatus === 'WAIT_SELLER_CONFIRM'">{{$t('pay.WaitingForSellerToConfirm')}}</span>
            <span v-else-if="row.tradeStatus === 'TRADE_SUCCESS'">{{$t('pay.paymentSuccessful')}}</span>
            <!-- <span v-else-if="row.tradeStatus === 'TRADE_SUCCESS' || row.tradeStatus === 'TRADE_FINISHED'">{{$t('pay.paymentSuccessful')}}</span> -->
          </template>
          <template slot-scope="{ row, index }" slot="totalAmount">
            $ {{row.totalAmount | filterTotalPrice}} （
            <span v-if="row.purchaseStatus == 1">{{$t('pay.newPurchase')}}</span>
            <span v-else-if="row.purchaseStatus == 2">{{$t('pay.Renewal')}}</span>
            <span v-else-if="row.purchaseStatus == 3">{{$t('pay.upgrade')}}</span>
            ）
          </template>
        </Table>
        <div class="flex-center-between">
          <div>
            <span v-if="order && order[0]" class="text-bold">{{$t('pay.ExpectedPeriod')}} {{order[0].createTime}} {{$t('pay.to')}} {{order[0].estimatedTime}}</span>
            <span class="text-muted">{{$t('pay.ActualDeadlineIsSubjectToPayment')}}</span>
          </div>
          <Button @click="cancelOrder">{{$t('pay.cancelOrder')}}</Button>
        </div>
        <div>{{$t('pay.theOrderWillBeAutomaticallyClosed')}}</div>
        <div>{{$t('pay.AfterOrderIsPaidSuccessfully')}} <a href="javascript:void(0)" @click="orderPage">{{$t('nav.订单')}}</a> </div>
        <!-- {{$t('pay.AfterOrderIsPaidSuccessfully')}} -->
        <h3 class="small_header_h3">{{$t('pay.paymentMethod')}}</h3>
        <Alert class="payAlert">
          {{$t('pay.publicAccount')}}
          <template #desc>
            <div><b>{{$t('pay.companyName')}}</b> {{$t('pay.xixunCompanyValue')}}</div>
            <div><b>{{$t('pay.savingsAccountNumber')}}</b> **********-838</div>
            <div><b>{{$t('pay.bankName')}}</b> {{$t('pay.bankNameValue')}}</div>
            <div><b>{{$t('pay.bankName')}}</b> HSBCHKHHHKH</div>
            <div><b>{{$t('pay.address')}}</b> {{$t('ad.addressValue')}}</div>
            <div><b>{{$t('pay.telephone')}}</b> (852)********</div>
            <div>{{$t('pay.uploadTheBankReceipt')}}</div>
            <div>{{$t('pay.receivedByMajorBanks')}}</div>
            <div>{{$t('pay.notifyYouViaSMS')}}</div>
            <Button style="margin: 20px 0" type="primary" @click="payImmediately">{{$t('pay.UploadBankReceipt')}}</Button>
            <div>{{$t('pay.contactOurSalesStaff')}}</div>
          </template>
        </Alert>
        <Alert type="warning" class="payAlert">
         {{$t('pay.NotesForPublicTransfers')}}
          <template #desc>
            <div>{{$t('pay.submittedForFinancialReview')}}</div>
            <div>{{$t('pay.TransfersFromPersonalAccounts')}}</div>
            <div>{{$t('pay.IfTheCompanyAccountIsTransferred')}}</div>
          </template>
        </Alert>
        <!-- <Tabs :animated="false">
          <TabPane label="第三方支付">
            <div class="Third_party_payment">
              <RadioGroup v-model="payMethod">
                <Radio label="alipay" size="large"><img src="@/assets/img/alipay.png" class="payMethodImg"/></Radio>
                <Radio label="wechatpay" size="large" style="margin-left: 10px"><img src="@/assets/img/wechatpay.png" class="payMethodImg" style="width: 110px;"/></Radio>
              </RadioGroup>
            </div>
          </TabPane>
          <TabPane label="网银">
            暂不支持
          </TabPane>
        </Tabs>
        <Divider />
        <div style="margin-top: 5px">在线支付总金额：<span v-if="order && order[0]" style="color: #de350b!important;">{{order[0].totalAmount | filterTotalPrice}}</span> 元</div>
        <Button style="margin-top: 10px" type="primary" @click="payImmediately">立即支付</Button> -->
      </div>
   </div>
    <Modal v-model="visible" width="600">
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{$t('pay.UploadBankReceipt')}}</span>
      </p>
      <Form :model="formItem" label-position="top">
        <FormItem>
          <div class="bankImg" @click="imgClickHandler">
            <img v-show="formItem.file" ref="img" src="" style="width: 100%; height: 100%"/>
          </div>
          <div style="display:inline-block;">
            <Upload :before-upload="handleUpload" :action="''" accept="image/jpg, image/png">
              <Button icon="ios-cloud-upload-outline">{{$t('screen.selectFile')}}</Button>
            </Upload>
            <span>{{$t('pay.uploadTip')}}</span>
          </div>
        </FormItem>
        <FormItem :label="$t('pay.BankCardNumber')">
            <Input v-model="formItem.cardId" :placeholder="$t('pay.bankCardNumberWhenTransferring')" style="width: 400px"></Input>
        </FormItem>
        <!-- <FormItem :label="$t('pay.transactionHour')">
           <DatePicker type="date" v-model="formItem.time" placeholder="" style="width: 400px" />
        </FormItem> -->
      </Form>
      <div slot="footer">
        <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
        <Button type="primary" :loading="loading" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
    <Modal v-model="dialogVisible" footer-hide>
      <img w-full ref="fullImg" src="" style="width: 100%; height: 100%" alt="Preview Image" />
    </Modal>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  data() {
    return {
      order: [],
      columns: [
        {
            title: this.$t('pay.orderNumber'),
            key: 'outTradeNo',
            width: '150',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.orderNumber'))
            }
        },
        {
            title: this.$t('common.createTime'),
            key: 'createTime',
            width: '180',
            renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
            }
        },
        {
            title: this.$t('pay.OrderDetails'),
            key: 'orderInfo',
            slot: 'orderInfo',
            width: '240',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.OrderDetails'))
            }
        },
        {
            title: this.$t('pay.Years'),
            key: 'month',
            slot: 'month',
            width: '100',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.Years'))
            }
        },
        {
            title: this.$t('pay.PaymentStatus'),
            key: 'tradeStatus',
            slot: 'tradeStatus',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.PaymentStatus'))
            }
        },
        {
            title: this.$t('pay.amount'),
            key: 'totalAmount',
            slot: 'totalAmount',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.amount'))
            }
        },
      ],
      // payMethod: "alipay",
      visible: false,
      loading: false,
      formItem: {
        file: null,
        cardId: "",
        time: new Date()
      },
      dialogVisible: false,
      token: this.$cookie.get('token'),
    }
  },
  methods: {
    init(order) {
      this.order.push(order)
    },
    orderPage () {
      this.$router.push({ name: 'orderServer-order'})
      this.menuActiveName = "付费服务"
      this.subMenuActiveName = "订单"
    },
    // 取消订单
    cancelOrder() {
      if (this.order && this.order[0]) {
        this.$Modal.confirm({
          title: this.$t('common.tips'),
          content: this.$t('common.delete_current_option'),
          okText: this.$t('common.confirm'),
          cancelText: this.$t('common.cancel'),
          onOk: () => {
            this.$http({
              url: this.$http.adornUrl('/sys/pay/cancelOrder'),
              method: 'get',
              params: this.$http.adornParams({'orderNo': this.order[0].outTradeNo})
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                    this.$router.push({ name: 'orderServer-payServer' })
                    this.menuActiveName = "付费服务";
                    this.subMenuActiveName = "订购服务"
                    this.$emit('changeState')
                  }
                })
              } else {
                this.$Message.error(data.msg)
              }
            })
          }
        })
      } else {
        this.$Message.error(this.$t('pay.CancellationLineItemCannotBeEmpty'))
      }
    },
    handleUpload(selectFile) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      if(ele === "image" && selectFile.size > (5 * 1024 *1024)){
        this.$Message.error(this.$t('screen.picture')+'：'+selectFile.name +this.$t('screen.sizeMore')+ '5M!')
        this.selectFile = null //超过大小将文件清空
        return false
      }
      //判断当前是否支持使用FileReader
      if(window.FileReader){
        //创建读取文件的对象
        var fr = new FileReader();
        //以读取文件字符串的方式读取文件 但是不能直接读取file 
        //因为文件的内容是存在file对象下面的files数组中的
        //该方法结束后图片会以data:URL格式的字符串（base64编码）存储在fr对象的result中
        fr.readAsDataURL(selectFile);
        var than = this
        fr.onloadend = function(){
          than.$refs.img.src = fr.result;
        }
      }
      this.formItem.file = selectFile
      return false
    },
    imgClickHandler() {
      if (this.formItem.file) {
        this.dialogVisible = true
        //判断当前是否支持使用FileReader
        if(window.FileReader){
          //创建读取文件的对象
          var fr = new FileReader();
          //以读取文件字符串的方式读取文件 但是不能直接读取file 
          //因为文件的内容是存在file对象下面的files数组中的
          //该方法结束后图片会以data:URL格式的字符串（base64编码）存储在fr对象的result中
          fr.readAsDataURL(this.formItem.file);
          var than = this
          fr.onloadend = function(){
            than.$refs.fullImg.src = fr.result;
          }
        }
      }
      
    },
    // 上传银行回执
    payImmediately () {
      this.visible = true
      this.formItem.file = null
      this.formItem.cardId = null
    },
    dataFormSubmit () {
      if (this.formItem.file == null) {
        this.$Message.error(this.$t('pay.PleaseUploadBankReceipt'))
        return;
      }
      if (this.formItem.cardId == "") {
        this.$Message.error(this.$t('pay.PleaseEnterBankCardNumber'))
        return;
      }
      if (!this.order && !this.order[0]) {
        this.$Message.error(this.$t('pay.NoRelatedOrders'))
        return;
      }
      this.loading = true;
      let formData = new FormData()
      formData.append("file",this.formItem.file);
      formData.append("cardId",this.formItem.cardId);
      formData.append("outTradeNo",this.order[0].outTradeNo)
      axios.request({
        url: this.$http.adornUrl('/sys/pay/uploadBankReceipt'),
        method: 'post',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data', 'token': this.token }
      }).then(res => {
        // 上传成功处理 
        if (res.data) {
          if (res.data.code === 0) {
            this.loading = false
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                this.visible = false
                this.$router.push({ name: 'orderServer-order'})
                this.menuActiveName = "付费服务";
                this.subMenuActiveName = "订单"
              }
            })
          } else {
            this.loading = false
            this.$Message.error({
              content: res.data.msg,
              duration: 0.5,
              onClose: () => {
                this.loading = false
                this.visible = false
              }
            })
          }
        
        }
      })
    },
    // 立即支付
    /* payImmediately() {
      this.$http({
        url: this.$http.adornUrl('/sys/pay/payOrder'),
        method: 'get',
        params: this.$http.adornParams({'orderNo': this.order[0].outTradeNo, 'type': this.payMethod})
      }).then(({data}) => {
        if (data === 'wechatpay') {
          this.$Message.error('暂不支持微信支付')
          return;
        }
        if (data === 'noNeed') {
          this.$Message.success({
            content: "支付成功",
            duration: 0.5,
          })
          this.$router.push({ name: 'orderServer-payServer' })
          this.menuActiveName = "付费服务";
          this.subMenuActiveName = "订购服务"
          this.$emit('changeState')
          return;
        }
        var page = window.open("", "_parent")
        var html = data
        page.document.write(html);
      })
    } */
  },
  computed: {
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight;
      },
    },
    menuActiveName: {
      get () { return this.$store.state.common.menuActiveName },
      set (val) { this.$store.commit('common/updateMenuActiveName', val) }
    },
    subMenuActiveName: {
      get () { return this.$store.state.common.subMenuActiveName },
      set (val) { this.$store.commit('common/updatesubMenuActiveName', val) }
    },
  },
  filters: {
    filterTotalPrice(val) {
      return val.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },
  }
}
</script>

<style scoped>
.userInfo_title {
  font-size: 23px;
  font-weight: 600;
}
.admin-order {
  overflow-x: hidden;
  overflow-y: auto;
}
.flex-center-between {
  margin-bottom: 12px!important;
  margin-top: 15px!important;
  align-items: center;
  justify-content: space-between;
  display: flex!important;
}
.text-bold {
  font-size: 16px!important;
  color: #000 !important;
  font-weight: 700!important;
}
.text-muted {
  margin-left: 4px!important;
  color: #909aaa;
}
.small_header_h3 {
  margin:10px 0px;
}
.Third_party_payment {
  padding: 20px; 
}
.payMethodImg {
  margin-left: 10px;
}
.payAlert div {
  margin: 5px 0
}
.bankImg {
  display:inline-block;
  width: 200px;
  height: 200px;
  margin-right: 15px;
  border: 1px solid #dadee3; 
}
</style>