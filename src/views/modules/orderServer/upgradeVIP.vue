<template>
  <div class="modules_upgradeVIP">
    <div :style="{height: documentClientHeight - 110 + 'px', overflow: 'hidden'}">
      <span class="userInfo_title">{{$t('pay.orderService')}}</span>
      <Divider />
      <div>
        <div class="admin-order" :style="{height: documentClientHeight - 195 + 'px'}">
          <div class="admin-order-info-header">
            <h4>{{$t('pay.currentVersion')}} 
              <span v-if="version == null || version == 0"> {{ $t('pay.tips1') }} </span>
              <span v-else-if="version == -1" style="color: #CD7F32">
                <img style="vertical-align: middle;" src="@/assets/img/VIP4.png" width="23" height="23">
                {{$t('pay.superVip')}}</span>
              <span v-else-if="version == 1" style="color: #c0c0c0">
                <img style="vertical-align: middle;" src="@/assets/img/VIP1.png" width="20" height="20">
                {{$t('pay.silverCardVip')}}</span>
              <span v-else-if="version == 2" style="color: #ffd700">
                <img style="vertical-align: middle;" src="@/assets/img/VIP2.png" width="20" height="20">
                {{$t('pay.goldCardVip')}}</span>
              <span v-else-if="version == 3" style="color: #5cadff">
                <img style="vertical-align: middle;" src="@/assets/img/VIP3.png" width="23" height="23">
                {{$t('pay.diamondVip')}}</span>
              <span v-else-if="version == 4" style="color: #CD7F32">
                <img style="vertical-align: middle;" src="@/assets/img/VIP4.png" width="23" height="23">
                {{$t('pay.vip4')}}</span>
              <span v-else></span>
            </h4>
          </div>
          <div class="admin-order-info-field">
            <!-- <div class="admin-order-info-field_title">{{$t('pay.numberOfTerminals')}}</div> -->
            <!-- <br/> -->
            <!-- <div class="admin-order-info-field_total">{{deviceTotalCount}}{{$t('home.piece')}}</div> -->
            <!-- <div class="admin-order-info-field_progress"><Progress :percent="percent" :stroke-width="15" :status="percent > 80 ? 'wrong':'active'" hide-info /></div> -->
            <!-- <div class="admin-order-info-field_rest" v-if="version == -1">{{$t('pay.TheRemainingAmount')}} ∞ {{$t('home.piece')}}</div> -->
            <!-- <div class="admin-order-info-field_rest" v-else>{{$t('pay.TheRemainingAmount')}} {{total - deviceTotalCount}} {{$t('home.piece')}}</div> -->
          </div>
          <!-- <div class="admin-order-info-field" v-if="version != null && version != -1">
            <div class="admin-order-info-field_title">{{$t('pay.ExpireDate')}}</div>
            {{expireDate}}
          </div> -->
          <div class="form-field_header">
            <span class="number">1</span>
            <span class="title">{{$t('pay.selectVersion')}}</span>
          </div>
          <div class="form-field_content">
            <Table highlight-row ref="currentRowTable" @on-current-change="currentChangeHandler" :border="true" :columns="columns" :data="data">
              <template slot-scope="{ row, index }" slot="version">
                {{$t(row.version)}}
              </template>
              <template slot-scope="{ row, index }" slot="deviceTotal">
                {{row.deviceTotal}} {{$t('home.piece')}}
              </template>
              <template slot-scope="{ row, index }" slot="price">
                $ {{row.price}} / {{$t('program.year')}}
              </template>
            </Table>
          </div>
          <div class="form-field_header">
            <span class="number">2</span>
            <span class="title">{{$t('pay.SelectDuration(years)')}}</span>
          </div>
          <div class="form-field_content">
            <div class="field-box">
              <InputNumber :min="1" v-model="year" style="width: 300px"/>
            </div>
          </div>
          <div class="form-field_header">
            <span class="number">3</span>
            <span class="title">{{$t('pay.contactInformation')}}</span>
          </div>
          <div class="form-field_content">
            <div class="field-box" style="height: 220px">
              <Input prefix="ios-pricetags" size="large" type="text" v-model="companyName" :placeholder="$t('pay.companyName')"></Input>
              <Input style="margin-top: 5px;" prefix="ios-navigate" size="large" type="text" v-model="address" :placeholder="$t('pay.address')"></Input>
              <Input style="margin-top: 5px;" prefix="ios-person" size="large" type="text" v-model="contactPerson" :placeholder="$t('pay.contactPerson')"></Input>
              <Input style="margin-top: 5px;" prefix="ios-phone-portrait" size="large" type="text" v-model="telephone" :placeholder="$t('pay.telephone')"></Input>
            </div>
          </div>
          
        </div>
      </div>
      <div class="footToolBar" :style="{'width': documentClientWidth - navClientWidth + 'px'}">
        <!-- style="width: 84%;" -->
        <div class="footToolBar-left">
          <!-- <a class="ml-4" href="javascript:void(0)" @click="viewOrderInfo">查看明细</a> -->
          <div class="header"><span class="text-w500" style="margin-right: 30px">{{$t('pay.totalOrder')}}</span></div>
          <div class="price">
            <span class="price_icon">$</span><span class="price_num">{{total_price | filterTotalPrice}}</span>
            <span class="price_origin">${{total_price + (total_price / 4) | filterTotalPrice}}</span></div>
        </div>
        <div class="button-right">
          <Button type="primary" :loading="submitOrderLoading" size="large" @click="submitOrder">{{$t('pay.submitOrder')}}</Button>
        </div>
      </div>
      </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      submitOrderLoading: false,
      columns: [
          {
            title: this.$t('card.version'),
            key: 'version',
            align: 'center',
            slot: 'version',
            renderHeader:(h)=>{
              return h('div',this.$t('card.version'))
            }
          },
          {
            title: this.$t('pay.numberOfTerminals'),
            key: 'deviceTotal',
            align: 'center',
            slot: 'deviceTotal',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.numberOfTerminals'))
            }
          },
          {
            title: this.$t('pay.price'),
            key: 'price',
            align: 'center',
            slot: 'price',
            renderHeader:(h)=>{
              return h('div',this.$t('pay.price'))
            }
          }
      ],
      data: [
          {
            version: 'pay.silverCardVip',
            vipStatus: 1,
            deviceTotal: 100,
            price: 800,
            _highlight: true
          },
          {
            version: 'pay.goldCardVip',
            vipStatus: 2,
            deviceTotal: 500,
            price: 1600,
          },
          {
            version: 'pay.diamondVip',
            vipStatus: 3,
            deviceTotal: 1500,
            price: 2600
          },
          {
            version: 'pay.vip4',
            vipStatus: 4,
            deviceTotal: '∞',
            price: 5000
          }
      ],
      year: 1,
      total_price: 0,
      version: 0,
      total: 10,
      deviceTotalCount: 0,
      percent: 0,
      vipStatus: 1,
      expireDate: '',
      companyName: '',
      address: '',
      contactPerson: '',
      telephone: '',
    }
  },
  activated() {
    // 初始化数据
    // this.data[0]["_highlight"] = true;
    this.total_price = this.data[0].price * this.year
    this.percent = this.deviceTotalCount / this.total * 100
    // 查询基本信息
    this.$http({
      url: this.$http.adornUrl(`/sys/user/orderInfo`),
      method: "get",
      params: this.$http.adornParams(),
    }).then(({ data }) => {
      this.version = data.orderInfo.version
      this.total = data.orderInfo.total
      this.deviceTotalCount = data.orderInfo.deviceTotalCount
      this.percent = this.deviceTotalCount / this.total * 100
      if (this.percent > 100) {
        this.percent = 100
      }
      // 如果当前用户为超级VIP
      if (this.version == -1) {
        this.percent = 0
      }
      this.expireDate = data.orderInfo.expireDate
    });
  },
  methods: {
    currentChangeHandler(currentRow, oldCurrentRow) {
      this.total_price = currentRow.price * this.year
      this.vipStatus = currentRow.vipStatus
    },
    // 创建订单
    submitOrder() {
      if (this.companyName == '') {
        this.$Message.error(this.$t('pay.companyNameTips'))
        return
      }
      if (this.address == '') {
        this.$Message.error(this.$t('pay.addressTips'))
        return
      }
      if (this.contactPerson == '') {
        this.$Message.error(this.$t('pay.contactPersonTips'))
        return
      }
      if (this.telephone == '') {
        this.$Message.error(this.$t('pay.telephoneTips'))
        return 
      }
      this.submitOrderLoading = true
      this.$http({
        url: this.$http.adornUrl("/sys/pay/createOrder"),
        method: "post", 
        data: this.$http.adornData({
          vipStatus: this.vipStatus,
          month: this.year * 12,
          companyName: this.companyName,
          address: this.address,
          contactPerson: this.contactPerson,
          telephone: this.telephone,
        }, false),
      }).then(({ data }) => {
        this.submitOrderLoading = false
        if (data && data.code === 0) {
          this.$emit('changeState', data.order)
        } else {
          this.$Message.error(data.msg)
        }
      })
    },
  },
  computed: {
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth },
    },
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight;
      },
    },
    navClientWidth: {
      get () { return this.$store.state.common.navClientWidth },
    },
  },
  filters: {
    filterTotalPrice(val) {
      return val.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    }
  },
  watch: {
    'year': function (newVal, oldVal) {
      this.total_price = (this.total_price / oldVal) * newVal
    }
  },

}
</script>

<style scoped>
.userInfo_title {
  font-size: 23px;
  font-weight: 600;
}
.admin-order {
  overflow-x: hidden;
  overflow-y: auto;
}
.admin-order-info-header {
  margin-top: 10px;
}
.admin-order-info-header h4 {
  font-size: 18px;
  font-weight: 500!important;
}
.admin-order-info-field {
  font-size: 16px;
  margin-top: 10px;
}
.admin-order-info-field div {
  display: inline-block;
}
.admin-order-info-field_title {
  font-weight: bold;
  width: 155px;
}
.admin-order-info-field_total {
  width: 50px;
}
.admin-order-info-field_progress {
  width: 600px;
}
.admin-order-info-field_rest {
  margin-left: 20px;
}
.form-field_header {
  margin-top: 30px;
}
.form-field_header .number {
  background: #fcfcfc;
  border-radius: 50%;
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  height: 24px;
  line-height: 24px;
  text-align: center;
  user-select: none;
  width: 24px;
}
.form-field_header .title {
  font-size: 18px;
  font-weight: 500;
  line-height: 22px;
  margin-left: 5px;
}
.form-field_content {
  margin-top: 30px;
}
.field-box {
  border: 1px solid #dadee3;
  border-radius: 4px;
  padding: 24px 32px;
  width: 100%;
  height: 100px;
}
.footToolBar {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: #fafbfb;
  border-top: 1px solid #e5ecf0;
  padding: 11px 32px 12px;
  width: 84%;
  height: 80px;
}
.footToolBar-left {
  display: inline-block;
}
.price {
  color: #ff5630;
  margin-bottom: -3px;
}
.price_icon {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
}
.price_num {
  font-size: 28px;
  font-weight: 500;
  line-height: 32px;
  margin-left: 8px;
  vertical-align: -2px;
}
.price_origin {
    color: #c1c7d0;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-left: 8px;
    text-decoration: line-through;
}
.button-right {
  float: right;
  line-height: 60px;
}
</style>