<template>
  <div>
    <upgradeVIP v-if="status === 'order'" @changeState="changeState"></upgradeVIP>
    <pay ref="pay" v-else-if="status === 'pay'" @changeState="changeState"></pay>
  </div>
</template>

<script>
import upgradeVIP from './upgradeVIP.vue'
import pay from './pay.vue'
export default {
    data() {
        return {
            status: "order",
        }
    },
    methods: {
      changeState(order) {
        if (order) {
          this.status = 'pay'
          this.$nextTick(() => {
            this.$refs.pay.init(order)
          })
        } else {
          this.status = 'order'
        }
      },
      // 查询当前用户是否有未支付的订单
      checkOrder() {
        this.status = 'order'
        this.$Spin.show();
        this.$http({
          url: this.$http.adornUrl("/sys/pay/checkOrder"),
          method: "post", 
          data: this.$http.adornData(),
        }).then(({ data }) => {
          this.$Spin.hide();
          var order = data.order
          if (order) {
            this.$Modal.info({
              title:  this.$t("pay.ThereAreUnpaidOrders"),
              content: this.$t("pay.ThereIsAnUnpaidOrderPleaseGoToPay"),
              onOk: () => {
                this.status = 'pay'
                this.$nextTick(() => {
                  this.$refs.pay.init(order)
                })
              }
            });
          }
        })
      }
    },
    activated() {
      let order = this.$route.params.order
      if (order) {
        changeState(order)
      }
      this.checkOrder()
    },
    components: {
      upgradeVIP,
      pay
    },
}
</script>

<style>

</style>