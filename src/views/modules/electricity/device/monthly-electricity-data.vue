<template>
  <Modal v-model="visible" width="70%">
    <p slot="header" style="text-align:center">
      {{$t('electricity.monthlyElectricity')}}
    </p>
    <div style="text-align:center" :style="{'width': documentClientWidth - 630 + 'px', 'height': '500px'}">
      <div v-show="list && list.length > 0" ref="sumElectricityData" :style="{'width': documentClientWidth - 630 + 'px', 'height': '500px'}"></div>
      <div v-if="list.length === 0" class="no-data">
        <div>
          <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#notData"></use>
          </svg>
          <div>{{$t('home.temporarilyNoData')}}</div>
        </div>
      </div>
    </div>
    <div slot="footer">
    </div>
  </Modal>
</template>

<script>
export default {
  name: "monthlyElectricityData",

  data(){
    return{
      visible: false,
      sumElectricityEChart:"",
      sumElectricityData:[],
      monthlyDate:[],
      list:[],
    }

  },
  methods: {
    init(id) {
      this.visible = true
      this.sumElectricityData = []
      this.monthlyDate = []
      this.$http({
        url: this.$http.adornUrl(`/electricity/card/monthlyElectricity/${id}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.list = data.data
          // this.startDate = data.values.startDate
          // this.endDate = data.values.endDate
          if (this.list && this.list.length > 0) {
            this.list.forEach(item => {
              // this.date.push((item.time !== '' ? item.time.replace(" ", "\n") : ''));
              this.monthlyDate.push(item.date)
              this.sumElectricityData.push(item.electricity)

            })
            this.myEcharts();
          }
        }
      })

    },
    myEcharts() {

      //电流
      if (this.sumElectricityEChart != null && this.sumElectricityEChart != "" && this.sumElectricityEChart != undefined) {
        this.sumElectricityEChart.dispose();
      }
      this.sumElectricityEChart = this.$echarts.init(this.$refs.sumElectricityData);
      this.sumElectricityEChart.setOption(this.sumElectricityOption);
    }
    },
  watch: {
    'language': function(newVal, OldVal) {
      this.myEcharts();
    }

  },
  computed: {
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth },
    },
    sumElectricityOption: {
      get () {
        return {
          grid: {
            height: 400
          },
          color: ['#9966FF'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('electricity.electricity') + ' {c} kWh'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.monthlyDate,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} kWh'
            },
            boundaryGap: [0, '100%']
          },
          series: [
            {
              name: this.$t('electricity.electricity'),
              type: 'line',
              data: this.sumElectricityData,
            }
          ]
        }
      }
    }

  }
}
</script>

<style scoped>
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
}
</style>
