<template>
  <div class="verify-line">
    <Row>
      <Col span="5">
        <Input size="large" v-model="key" :placeholder="$t('cardDevice.deviceName') + '/ID'" :border="false" @keyup.native="getDataList(true)"></Input>
        <Card shadow :style="{'height': tableHeight + 140 + 'px'}">
          <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight + 155"> -->
          <div :style="{'height':tableHeight + 45+ 'px', 'overflow-y': 'auto'}">
            <ul class="opera_ul" v-if="dataList && dataList.length > 0">
              <li v-for="(item, index) in dataList" :key="index" @click="changeSelect(item, index)">
                <div :class="select === index ? 'select': ''" style="padding-left: 10px;padding-right: 10px;">
                  {{item.deviceId}}
                  <span v-if="item.isOn === 1" style="float: right">
                  <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                    <use xlink:href="#on-line"></use>
                  </svg>
                </span>
                  <span v-else style="float: right">
                  <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                    <use xlink:href="#line"></use>
                  </svg>
                </span>
                </div>
              </li>
            </ul>
            <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
              {{$t('home.temporarilyNoData')}}
            </div>
          </div>
          <!-- </Scroll> -->
          <Page size="small" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-total @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
        </Card>
      </Col>
      <Col span="19">
        <div :style="{'height': tableHeight + 175 + 'px', 'overflow-y': 'auto'}">
          <Button style="margin-left: 5px" type="primary" :disabled="weeklyData.length == 0" @click="getMonthData(cardId)">{{$t('electricity.monthlyElectricity')}}</Button>
          <Button @click="exportMonthData(cardId)" :disabled="weeklyData.length == 0" type="primary" style="float: right">{{$t('electricity.exportElectricity')}}</Button>

          <Button  @click="changeVisible()" type="primary">{{ $t('electricity.setElectricityTime') }}</Button>
          <div v-show="weeklyData && weeklyData.length > 0">
<!--          <div>-->



<!--            <Button @click="clearData=true"> {{$t('electricity.clearData')}}</Button>
            <Modal v-model="clearData">
              <p slot="header" style="color:#f60;text-align:center">
                <Icon type="information-circled"></Icon>
                <span>{{$t('electricity.clearData')}}</span>
              </p>
              <div style="text-align:center">
              <p>{{ $t('electricity.isClear') }}+{{ cardId }}+{{ $t('electricity.electricitySensorData') }}</p>

              <div style="color: red">警告：清除操作无法撤消！请慎重操作！</div>
              <div style="color: red">
              该操作将清除传感器中的数据
              为防止意外，确认继续操作请输入以下内容：
              </div>
              <div>
              清除传感器中的数据
              </div>
              <Input v-model="isClearSensorData" placeholder="请确认清除" style="width: 300px"></Input>
              </div>
              <div slot="footer">
                <Button>返回</Button>
                <Button type="primary" @click="confirm">删除</Button>
              </div>
            </Modal>-->
            <!--电能-->
            <div ref="electricityData" :style="{'width': documentClientWidth - 630 + 'px', 'height': '310px'}"></div>
            <!--电流-->
            <div ref="currentData" :style="{'width': documentClientWidth - 630 + 'px', 'height': '310px'}"></div>
            <!--功率-->
            <div ref="powerData" :style="{'width': documentClientWidth - 630 + 'px', 'height': '310px'}"></div>
            <!--电压-->
            <div ref="voltageData" :style="{'width': documentClientWidth - 630 + 'px', 'height': '310px'}"></div>

          </div>
          <div v-if="weeklyData.length === 0" class="no-data">
            <div>
              <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
                <use xlink:href="#notData"></use>
              </svg>
              <div>{{$t('home.temporarilyNoData')}}</div>
            </div>
          </div>
        </div>
      </Col>
    </Row>
    <Modal v-model="setTimeVisible" width="500">
      <p slot="header" style="text-align:center">
        <span  style="font-size: 18px">{{ $t('electricity.setElectricityTime') }}</span>
      </p>
      <div >
        <Alert type="warning" show-icon ><b class="tip">{{ $t('electricity.tip') }}</b></Alert>
        <div>
          <span style="font-size: 16px">{{ $t('electricity.curElectricityTime') }}:{{curElectricityTime}}</span>
        </div>
        <div>
          <span style="font-size: 16px">{{ $t('electricity.selectTime') }}：</span>
          <Select v-model="electricityTime" style="width:80px">
            <Option v-for="item in dayTime" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div>

      </div>
      <div slot="footer">

        <Button v-if="electricityTime" @click="setElectricityTime(cardId)">{{ $t('common.confirm') }}</Button>
      </div>
    </Modal>

    <monthly-electricity-data v-if="monthlyDataVisible" ref="monthlyElectricityData"></monthly-electricity-data>
  </div>
</template>




<script>
import monthlyElectricityData from "./device/monthly-electricity-data";

export default {
  data () {
    return {
      key: '',
      select: 0,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      startDate: '',
      endDate: '',
      date: [],

      weeklyDate:[],
      weeklyData:[],
      clearData:false,

      //每月数据可视化
      monthlyDataVisible:false,

      //图表数据
      currentData:[],
      powerData:[],
      electricityData:[],
      voltageData:[],
      currentEChart:'',
      powerEChart:'',
      electricityEChart:'',
      voltageEChart:'',

      cardId:'',

      isClearSensorData:'',

      //电能查询时间
      electricityTime:"",
      // dayTime:[
      //   {value:'0',label:'00:00'},
      //   {value:'1',label:'01:00'},
      //   {value:'2',label:'02:00'},
      //   {value:'3',label:'03:00'},
      //   {value:'4',label:'04:00'},
      //   {value:'5',label:'05:00'},
      //   {value:'6',label:'06:00'},
      //   {value:'7',label:'07:00'},
      //   {value:'8',label:'08:00'},
      //   {value:'9',label:'09:00'},
      //   {value:'10',label:'10:00'},
      //   {value:'11',label:'11:00'},
      //   {value:'12',label:'12:00'},
      //   {value:'13',label:'13:00'},
      //   {value:'14',label:'14:00'},
      //   {value:'15',label:'15:00'},
      //   {value:'16',label:'16:00'},
      //   {value:'17',label:'17:00'},
      //   {value:'18',label:'18:00'},
      //   {value:'19',label:'19:00'},
      //   {value:'20',label:'20:00'},
      //   {value:'21',label:'21:00'},
      //   {value:'22',label:'22:00'},
      //   {value:'23',label:'23:00'},
      // ],
      dayTime:[
        {value:'1',label:1},
        {value:'2',label:2},
        {value:'3',label:3},
        {value:'4',label:4},
        {value:'5',label:5},
        {value:'6',label:6},
        {value:'7',label:7},
        {value:'8',label:8},
        {value:'9',label:9},
        {value:'10',label:10},
        {value:'11',label:11},
        {value:'12',label:12},
        {value:'13',label:13},
        {value:'14',label:14},
        {value:'15',label:15},
        {value:'16',label:16},
        {value:'17',label:17},
        {value:'18',label:18},
        {value:'19',label:19},
        {value:'20',label:20},
        {value:'21',label:21},
        {value:'22',label:22},
        {value:'23',label:23},
        {value:'24',label:24},
      ],

      //
      setTimeVisible:false,
      //当前电能时间
      curElectricityTime:null
    }
  },

  components: {
    monthlyElectricityData
  },

  activated () {
    this.electricityTime=""
    this.setTimeVisible=false
    this.getDataList()
  },
  methods: {
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // confirm () {
    //   // this.$Modal.confirm({
    //   //   title: this.$t('electricity.clearData'),
    //   //   content: this.$t('electricity.isClear')+this.cardId+this.$t('electricity.electricitySensorData'),
    //   //   onOk: () => {
    //   console.log(this.isClearSensorData)
    //   if (this.isClearSensorData==='清除传感器中的数据')
    //       this.$http({
    //         url: this.$http.adornUrl(`/electricity/card/cleanElectricity`),
    //         method: "post",
    //         params: this.$http.adornParams({'cardId':this.cardId})
    //       }).then(({data}) => {
    //           if (data && data.code === 0) {
    //             if (data.msg.success){
    //               this.$Message.success({
    //                   content: this.$t('electricity.clearSuccessfully'),
    //                   duration: 0.5,
    //               }
    //               )
    //             }
    //           }else {
    //             this.$Message.error(data.msg)
    //           }
    //         })
    //   //   },
    //   //   onCancel: () => {
    //   //     this.$Message.info({
    //   //       content: this.$t('electricity.cancelClear')
    //   //     })
    //   //
    //   //   }
    //   // });
    // },

    changeSelect(item, index) {
      this.select = index
      this.getCardInfo(item.deviceId)
    },
    handleReachBottom () {
      return new Promise(resolve => {
        setTimeout(() => {
          this.pageSize = this.pageSize + 10;
          if (this.pageSize > this.totalCount) {
            this.$Message.warning(this.$t('common.noMoreData'));
            return;
          }
          this.getDataList();
          resolve();
        }, 200);
      });
    },
    getCardInfo (id) {
      this.currentData=[]
      this.powerData=[]
      this.electricityData=[]
      this.voltageData=[]
      this.date =  []
      this.weeklyDate=[]
      this.cardId=id;
      var url;
      var timezone=Intl.DateTimeFormat().resolvedOptions().timeZone;
      if(timezone !='Etc/GMT-8'){
        url="/electricity/card/weeklyElectricity/"+id+"?timezone="+timezone
      }else {
        url="/electricity/card/weeklyElectricity/"+id
      }
      this.$http({
        url: this.$http.adornUrl(url),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.weeklyData = data.data
          // this.startDate = data.values.startDate
          // this.endDate = data.values.endDate
          if (this.weeklyData && this.weeklyData.length > 0) {
            this.weeklyData.forEach(item => {
              // this.date.push((item.time !== '' ? item.time.replace(" ", "\n") : ''));
              this.weeklyDate.push(item.createTime)
              this.currentData.push(item.current)
              this.powerData.push(item.power)
              this.electricityData.push(item.electricity)
              this.voltageData.push(item.voltage)
            })
            this.myEcharts();
          }
        }
      })
    },
    getDataList (isQuery) {
      if (isQuery) {
         this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/electricity/card/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.key,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataList.length > 0) {
            if (this.dataList[0].deviceId) {
              this.cardId=this.dataList[0].deviceId
              this.select=0
              this.getCardInfo(this.dataList[0].deviceId)
            }
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
      })
    },
    //查询每月电能总量
    getMonthData (id) {
      this.monthlyDataVisible = true
      this.$nextTick(() => {
        this.$refs.monthlyElectricityData.init(id)
      })
    },
    //导出每月数据
    exportMonthData(id){
      window.open(this.$http.adornUrl(`/electricity/card/exportElectricity/${id}?token=${this.$cookie.get('token')}`))
    },
    //设置电能查询时间
    setElectricityTime(cardId){
      this.$http({
        url: this.$http.adornUrl(`/lampPole/card/setElectricityTime`),
        method: 'post',
        data: this.$http.adornData({
          'deviceId': cardId,
          'time': this.electricityTime
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$Message.success(this.$t('common.operationSuccessful'))
          this.setTimeVisible=false
        }else {
          this.$Message.error(data.msg)
        }
      })
    },
    queryElectricity(){
      if (this.cardId&&this.cardId!=''){}
      this.$http({
        url: this.$http.adornUrl('/lampPole/card/electricity/'+this.cardId),
        method: 'get'
      }).then(({data})=>{
        if (data&&data.code===0){
          this.curElectricityTime=data.data
        }
      })
    },

    //电能查询时间弹窗
    changeVisible(){
      this.electricityTime="24"
      this.setTimeVisible=true
      this.queryElectricity()
    },
    //构建图表
    myEcharts(){

      //电流
      if (this.currentEChart!=null&&this.currentEChart!=""&&this.currentEChart!=undefined){
        this.currentEChart.dispose();
      }
      this.currentEChart=this.$echarts.init(this.$refs.currentData);
      this.currentEChart.setOption(this.currentOption);

      //功率
      if (this.powerEChart!=null&&this.powerEChart!=""&&this.powerEChart!=undefined){
        this.powerEChart.dispose();
      }
      this.powerEChart=this.$echarts.init(this.$refs.powerData);
      this.powerEChart.setOption(this.powerOption);

      //电能
      if (this.electricityEChart!=null&&this.electricityEChart!=""&&this.electricityEChart!=undefined){
        this.electricityEChart.dispose();
      }
      this.electricityEChart=this.$echarts.init(this.$refs.electricityData);
      this.electricityEChart.setOption(this.electricityOption);


      //电压
      if (this.voltageEChart!=null&&this.voltageEChart!=""&&this.voltageEChart!=undefined){
        this.voltageEChart.dispose();
      }
      this.voltageEChart=this.$echarts.init(this.$refs.voltageData);
      this.voltageEChart.setOption(this.voltageOption);

    }
  },

  watch: {
    'language': function(newVal, OldVal) {
      this.myEcharts();
    }
  },
  computed: {
    language: {
      get () { return this.$store.state.language.language },
    },
    tableHeight: {
      get () { return this.$store.state.common.tableHeight },
    },
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth },
    },
    currentOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#FFBF00'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('electricity.current') + ' {c} A'
          },
          title: {
            left: 'center',
            text: this.$t('electricity.current'),
            subtext: this.$t('electricity.currentSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.weeklyDate,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} A'
            },
            boundaryGap: [0, '100%']
          },
          // dataZoom: [
          //   {
          //     startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
          //     endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
          //   },
          //   {
          //     type: 'inside',
          //     start: 2,
          //     end: 2
          //   },
          //   {
          //     start: 0,
          //     end: 10
          //   }
          // ],
          series: [
            {
              name: this.$t('electricity.current'),
              type: 'line',
              data: this.currentData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(224, 62, 76)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(255, 191, 0)'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    powerOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#33CCFF'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('electricity.power') + ' {c} W'
          },
          title: {
            left: 'center',
            text: this.$t('electricity.power'),
            subtext: this.$t('electricity.powerSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.weeklyDate,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} W'
            },
            boundaryGap: [0, '100%']
          },
          // dataZoom: [
          //   {
          //     startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
          //     endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
          //   },
          //   {
          //     type: 'inside',
          //     start: 2,
          //     end: 2
          //   },
          //   {
          //     start: 0,
          //     end: 10
          //   }
          // ],
          series: [
            {
              name: this.$t('electricity.power'),
              type: 'line',
              data: this.powerData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(102,51,255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(51,204,255)'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    electricityOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#9966FF'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('electricity.electricity') + ' {c} kWh'
          },
          title: {
            left: 'center',
            text: this.$t('electricity.electricity'),
            subtext: this.$t('electricity.electricitySubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.weeklyDate,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} kWh'
            },
            boundaryGap: [0, '100%']
          },
          // dataZoom: [
          //   {
          //     startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
          //     endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
          //   },
          //   {
          //     type: 'inside',
          //     start: 2,
          //     end: 2
          //   },
          //   {
          //     start: 0,
          //     end: 10
          //   }
          // ],
          series: [
            {
              name: this.$t('electricity.electricity'),
              type: 'line',
              data: this.electricityData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(153,0,153)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(153,102,255)'
                  }
                ])
              },
            }
          ]
        }
      }
    },
    voltageOption: {
      get () {
        return {
          grid: {
            height: 170
          },
          color: ['#99FFCC'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('electricity.voltage') + ' {c} V'
          },
          title: {
            left: 'center',
            text: this.$t('electricity.voltage'),
            subtext: this.$t('electricity.voltageSubText')
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.weeklyDate,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} V'
            },
            boundaryGap: [0, '100%']
          },
          // dataZoom: [
          //   {
          //     startValue: (this.startDate !== '' ? this.startDate.replace(" ", "\n") : ''),
          //     endValue: (this.endDate !== '' ? this.endDate.replace(" ", "\n") : '')
          //   },
          //   {
          //     type: 'inside',
          //     start: 2,
          //     end: 2
          //   },
          //   {
          //     start: 0,
          //     end: 10
          //   }
          // ],
          series: [
            {
              name: this.$t('electricity.voltage'),
              type: 'line',
              data: this.voltageData,
              areaStyle: {
                opacity: 0.8,
                color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(153,204,51)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(153,255,204)'
                  }
                ])
              },
            }
          ]
        }
      }
    }
  }
}
</script>

<style scoped>
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  background-color: rgb(255, 255, 255);
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-bottom: 5px;
  cursor: pointer;
}
.opera_ul li:hover {
  background-color: rgb(204, 204, 204);
  cursor: pointer;
}
.select {
  background-color: rgb(204, 204, 204);
}
</style>
