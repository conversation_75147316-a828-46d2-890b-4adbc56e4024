<template>
  <div class="modiles-controlLog">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem>
        <Input size="large" v-model="dataForm.deviceId" :placeholder="$t('home.cardNumber')"></Input>
      </FormItem>
      <FormItem>
        <Select size="large" v-model="dataForm.status" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('common.state')">
          <Option v-for="item in logStatus" :value="item.value" :key="item.value">{{ $t(item.name) }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button @click="getDataList(1)" size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList" ref="table"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight">
      <template slot-scope="{ row, index }" slot="status">
        <Tag color="blue" v-if="row.status === 0">{{$t('home.successful')}}</Tag>
        <Tag color="red" v-if="row.status === 1">{{$t('home.failure')}}</Tag>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        deviceId: '',
        status: ''
      },
      logStatus: [
        {value: 0, name: 'home.successful'},
        {value: 1, name: 'home.failure'}
      ],
      dataConlums: [
        {title: this.$t('log.commandId'), key: 'LogId', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('log.commandId'))
          }
        },
        {title: this.$t('home.cardNumber'), key: 'deviceId', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('home.cardNumber'))
          }
        },
        {title: this.$t('program.name'), key: 'taskName', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('program.name'))
          }
        },
        {title: this.$t('log.schedule'), key: 'progress', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('log.schedule'))
          }
        },
        {title: this.$t('log.ReasonForFailure'), key: 'reason', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('log.ReasonForFailure'))
          }
        },
        {title: this.$t('common.state'), key: 'status', slot: 'status', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.state'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center', width: 200,
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        }
      ],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataList:[],
    }
  },
  activated () {
    this.initData()
    this.getDataList()
  },
  methods: {
    initData () {
      this.dataForm = {
        deviceId: '',
        status: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
    },
    // 获取数据列表
    getDataList (isQuery) {
      this.dataListLoading = true
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/broadcast/playLog/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'deviceId': this.dataForm.deviceId,
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    }
  }
}
</script>
