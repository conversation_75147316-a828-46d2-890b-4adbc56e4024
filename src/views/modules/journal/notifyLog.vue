<template>
  <div class="modiles-controlLog">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem style="width: 240px;" v-if="userInfo.userId == 1">
        <Input size="large" v-model="dataForm.key" :placeholder="$t('notify.Username/EmailAddress')"></Input>
      </FormItem>
      <FormItem >
        <Select size="large" v-model="dataForm.type" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('menu.type')">
          <Option v-for="item in typeList" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button @click="getDataList(1)" size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList" ref="table"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight">
      <template slot-scope="{ row, index }" slot="type">
        {{ $t(typeFilters(row.type)) }}
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button v-if="isAuth('sys:notifyLog:info')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="infoHandle(row.id, row.type)">{{$t('common.info')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

  <Modal v-model="visible" width="800">
    <p slot="header" style="text-align:center">
      <span>{{$t('common.info')}}</span>
    </p>
    <Table border :columns="dataDetailConlums" :data="dataDetail.detailList" ref="table"
      :loading="dataDetailListLoading" style="width: 100%" :max-height="tableHeight">
    </Table>
    <div slot="footer">
        <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
    </div>
  </Modal>

  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        key: '',
        type: ''
      },
      typeList: [
        {value: 0, label: "notify.OfflineNotify"},
        {value: 1, label: "notify.CardNotWorkingNotification"}
      ],
      dataConlums: [
        {title: this.$t('menu.type'), key: 'type',slot: 'type', align: 'center',width: 260,
          renderHeader:(h)=>{
            return h('div',this.$t('menu.type'))
          }
        },
        {title: this.$t('notify.SendingMailbox'), key: 'sendToEmail', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('notify.SendingMailbox'))
          }
        },
        {title: this.$t('notify.SendingTime'), key: 'sendTime', align: 'center', width: 200,
          renderHeader:(h)=>{
            return h('div', this.$t('notify.SendingTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',width: 200,
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      visible: false,
      dataDetailConlums: [
        {title: 'ID', key: 'deviceId', width: 170, align: 'center'}
      ],
      dataDetail: {},
      dataDetailListLoading: false
    }
  },
  activated () {
    this.initData()
    this.getDataList()
    // 如果是超级管理员显示创建用户
    if (this.userInfo.userId == 1) {
      this.dataConlums.splice(1, 0, {
        title: this.$t('login.username'), key: 'username', width: 100,
        renderHeader:(h)=>{
          return h('div',this.$t('login.username'))
        }
      })
    }
  },
  methods: {
    initData () {
      this.dataForm = {
        key: '',
        type: ''
      }
      this.pageIndex =  1
      this.pageSize =  10
      this.totalPage =  0
    },
    // 获取数据列表
    getDataList (isQuery) {
      if (isQuery===1){
        this.pageIndex=1
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/sys/notify/log/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': String.trim(this.dataForm.key),
          'type': this.dataForm.type
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    infoHandle(id, type) {
      this.visible = true
      // 设备离线
      if (type == 0) {
        this.dataDetailConlums.splice(1, 0, {title: this.$t('notify.OffLineTime'), key: 'abnormalTime', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('notify.OffLineTime'))
          }
        })
      } else if (type == 1) {// 接收卡不工作
        this.dataDetailConlums.splice(1, 2, 
          {title: this.$t('notify.AbnormalTime'), key: 'abnormalTime', width: 170, align: 'center',
            renderHeader:(h)=>{
              return h('div',this.$t('notify.AbnormalTime'))
            }
          },
          {title: this.$t('notify.NumberOfCardsRLastTime'), key: 'originalData', align: 'center',
            renderHeader:(h)=>{
              return h('div',this.$t('notify.NumberOfCardsRLastTime'))
            }
          },
          {title: this.$t('notify.NumberOfCardsReceivedExceptionOccurs'), key: 'latestData', align: 'center',
            renderHeader:(h)=>{
              return h('div',this.$t('notify.NumberOfCardsReceivedExceptionOccurs'))
            }
          },
        )
      }
      if (id) {
        this.dataDetailListLoading = true
        this.$http({
          url: this.$http.adornUrl(`/sys/notify/log/info/${id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataDetail = data.log
          }
          this.dataDetailListLoading = false
        })
      }
    },
    typeFilters (val){
      let res = ""
      switch (val) {
        case 0:
          res = 'notify.OfflineNotify'
          break;
        case 1:
          res = "notify.CardNotWorkingNotification"
          break;
        default:
          break;
      }
      return res
    },
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal == false) {
        this.dataDetailConlums = [
          {title: 'ID', key: 'deviceId', width: 170, align: 'center'}
        ]
      }
    }
  }
}
</script>
