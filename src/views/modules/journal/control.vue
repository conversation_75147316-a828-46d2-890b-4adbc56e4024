<template>
  <div class="modiles-controlLog">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem style="width: 240px;">
        <Input size="large" v-model="dataForm.key" :placeholder="$t('home.cardNumber') +' / '+$t('log.user_action')"></Input>
      </FormItem>
      <FormItem >
        <Select size="large" v-model="dataForm.status" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('common.state')">
          <Option v-for="item in logStatus" :value="item.value" :key="item.value">{{ $t(item.name) }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button @click="getDataList(1)" size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList" ref="table"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight">
      <template slot-scope="{ row, index }" slot="status">
        <Tag color="blue" v-if="row.status === 0">{{row.status | filterStatus(logStatusList)}}</Tag>
        <Tag color="red" v-else>{{row.status | filterStatus(logStatusList)}}</Tag>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        key: '',
        status: ''
      },
      logStatus: [
        {value: -1, name: 'log.RequestTimedOut'},
        {value: 0, name: 'log.requestSucceeded'},
        {value: 1, name: 'log.connectionDoesNotExist'},
        {value: 2, name: 'log.Disconnect'},
        {value: 7, name: 'log.connectionClosed'},
        {value: 8, name: 'log.requestException'}
      ],
      dataConlums: [
        {title: 'ID', key: 'cmdId', align: 'center', tooltip: true},
        {title: this.$t('home.cardNumber'), key: 'cardId', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('home.cardNumber'))
          }
        },
        {title: this.$t('log.commandId'), key: 'commandId', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('log.commandId'))
          }
        },
        {title: this.$t('log.user_action'), key: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('log.user_action'))
          }
        },
        {title: this.$t('log.request_parameters'), key: 'req', align: 'center', tooltip: true, width: 200,
          renderHeader:(h)=>{
            return h('div',this.$t('log.request_parameters'))
          }
        },
        {title: this.$t('log.response_result'), key: 'resp', align: 'center', tooltip: true, width: 150,
          renderHeader:(h)=>{
            return h('div',this.$t('log.response_result'))
          }
        },
        {title: this.$t('common.state'), key: 'status', slot: 'status', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.state'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center', width: 200,
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false
    }
  },
  activated () {
    this.initData()
    this.getDataList()
  },
  methods: {
    initData () {
      this.dataForm = {
        key: '',
        status: ''
      }
      this.pageIndex =  1
      this.pageSize =  10
      this.totalPage =  0
    },
    // 获取数据列表
    getDataList (isQuery) {
      if (isQuery===1){
        this.pageIndex=1
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/controlLog/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': String.trim(this.dataForm.key),
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    },
    logStatusList: {
      get () {
        return [
          {value: -1, name: this.$t('log.RequestTimedOut')},
          {value: 0, name:  this.$t('log.requestSucceeded')},
          {value: 1, name:  this.$t('log.connectionDoesNotExist')},
          {value: 2, name:  this.$t('log.Disconnect')},
          {value: 7, name:  this.$t('log.connectionClosed')},
          {value: 8, name:  this.$t('log.requestException')}
        ]
      }
    }
  },
  filters: {
    filterStatus (val, logStatus) {
      var result = ''
      if (logStatus && logStatus.length > 0) {
        result = logStatus.filter(item => {
          if (item.value === val) {
            return item
          } else {
            return ''
          }
        })
        return result[0].name
      }
    }
  }
}
</script>
