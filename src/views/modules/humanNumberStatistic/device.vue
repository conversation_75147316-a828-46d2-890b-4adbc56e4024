<template>
<div>
  <div class="verify-line">
    <Row>
      <Col span="5">
        <Input size="large" v-model="key" :placeholder="$t('cardDevice.deviceName')" :border="false" @keyup.native="getDeviceList(true)"></Input>
        <Card shadow :style="{'height': tableHeight + 140 + 'px'}">
          <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight + 155"> -->
          <div :style="{'height':tableHeight + 45+ 'px', 'overflow-y': 'auto'}">
            <ul class="opera_ul" v-if="deviceList && deviceList.length > 0">
              <li v-for="(item, index) in deviceList" :key="index" @click="changeSelect(item, index)" >
                <div :class="select === index ? 'select': ''" style="padding-left: 10px;padding-right: 10px;">
                  <!-- <span class="box"></span> -->
                  {{item.alias}}
                  <span style="float: right;">
                    <svg width="25px" height="25px" aria-hidden="true"  style="vertical-align:middle;">
                      <use :xlink:href="item.isOn===1 ? '#on-line' : '#line'"></use>
                    </svg>
                  </span>
                  <!-- <span v-else>
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#line"></use>
                    </svg>
                  </span> -->
                </div>
              </li>
            </ul>
            <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
              {{$t('home.temporarilyNoData')}}
            </div>
          </div>
          <!-- </Scroll> -->
          <Page size="small" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-total @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
        </Card>
      </Col>
      <Col span="19">
        <div :style="{'height': tableHeight + 175 + 'px', 'overflow-y': 'auto'}">
          <Button style="margin-left: 5px" type="primary" @click="getHumanStatisticNow(deviceId)">{{$t('statistic.queryHumanStatisticToday')}}</Button>
          <Button type="primary" @click="getRealplay(deviceId)" style="float: right">{{$t('program.preview')}}</Button>
          <DatePicker type="daterange" placement="bottom-end" :options="humanNumberOptions" v-model="dateRange" :placeholder="$t('broadcast.selectDate')" style="width: 200px"></DatePicker>
          <Button type="primary" @click="getHumanStatisticByDate(deviceId)">{{$t('statistic.selectHistoricalData')}}</Button>
          <Button type="primary" @click="showModal(deviceId)">{{ $t('statistic.sendData') }}</Button>
          <div v-show="dataList && dataList.length > 0" >
<!--            <Button type="primary" @click="enableHumanStatistic(deviceId)">{{$t('statistic.enableHumanStatistic')}}</Button>-->

            <!--客流-->
            <div id="humanStatistic" ref="humanStatistic" :style="{'width': documentClientWidth - 690 + 'px', 'height': tableHeight + 115 + 'px', 'overflow-y': 'auto'}"></div>
          </div>
          <div v-if="dataList.length === 0" class="no-data">
            <div>
              <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
                <use xlink:href="#notData"></use>
              </svg>
              <div>{{$t('home.temporarilyNoData')}}</div>
            </div>
          </div>
        </div>
      </Col>
    </Row>
    <realplay v-if="realplayVisible" ref="Realplay"></realplay>
  </div>
  <Modal v-model="sendDataVisible" width="600">
    <p slot="header" style="text-align:center">
      <span>{{ $t('statistic.sendData') }}</span>
    </p>
    <Alert show-icon style="text-align: left"><b class="tip">{{ $t('statistic.keyTip') }}</b></Alert>
    <div style="height: 50px">
      <span>{{ $t('statistic.isShowHumanNumber') }}：</span>
      <span v-if="isOpen==1">
        {{ $t('common.true') }}
      </span>
      <span v-else>{{ $t('common.false') }}</span>
      <div style="float: right">
        <Button type="primary" @click="enableSendHumanNumber(1)" size="small">{{ $t('sys.open') }}</Button>
        <Button type="success"  @click="enableSendHumanNumber(0)" size="small">{{ $t('sys.close') }}</Button>
      </div>
    </div>
    <div style="height: 50px">
      <span>{{ $t('statistic.dataKey') }}：</span>
      <span v-if="dataKey!=null&&dataKey!=''">{{dataKey}}</span>
      <span v-else>{{ $t('statistic.noDataKey') }}</span>
<!--      <span>{{dataKey}}</span>-->
      <Tooltip :content="this.$t('statistic.clickCopy')">
        <svg width="20px" height="20px" aria-hidden="true" style="vertical-align: middle;" @click="copyKey">
          <use xlink:href="#copy"></use>
        </svg>
      </Tooltip>
    </div>
    <div slot="footer">
    </div>
  </Modal>
</div>

</template>

<script>
import Realplay from "./device/realplay";

export default {
  components: {Realplay},
  data() {
    return {
      key: '',
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      deviceList: [],
      deviceId:"",


      realplayVisible:false,
      select: 0,
      dataList: [],

      /**
       * 日期范围
       */
      dateRange:[],

      //每小时进入人数列表
      enteredList:[],
      //每小时离开人数列表
      exitedList:[],
      //时间
      timeList:[],
      humanNumberEChart:'',
      timeListAll:["00:00","01:00","02:00","03:00","04:00","05:00","06:00","07:00","08:00","09:00","10:00","11:00","12:00",
        "13:00","14:00","15:00","16:00","17:00","18:00","19:00","20:00","21:00","22:00","23:00"],
      humanNumberOptions:{
        disabledDate (date) {
          return  Date.now() -1000*60*60*24*365 >date.valueOf() || date.valueOf()> Date.now()+ 1000*60*60*24;
        }
      },
      //传输数据对话框
      sendDataVisible:false,
      //已绑定的设备
      // ownDevice:"",
      //是否开启传输数据功能
      isOpen:0,
      dataKey:"",
      UpdateDhDeviceFrom:{
        deviceId:'',
        state:0
      }

    }
  },


  // mounted() {
  //
  //   this.$nextTick(function () {
  //     this.getDeviceList()
  //     // this.clearStatisticData()
  //   });
  // },

  activated () {
    var el= document.getElementById('humanStatistic')
    if (el!=null){
      this.$echarts.dispose(el)
    }
    this.dataRange=[]
    this.getDeviceList()
  },

  methods: {
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDeviceList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDeviceList()
    },
    // 获取数据列表
    getDeviceList(isQuery) {
      if (isQuery) {
         this.pageIndex = 1
      }
      this.clearStatisticData()
      this.$http({
        url: this.$http.adornUrl('/monitor/humanNumberStatistic/list'),
        method: 'get',
        params: this.$http.adornParams({
          // 'isOn': "1",
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.key,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          // console.log(data)
          this.deviceList = data.page.list
          this.totalPage = data.page.totalCount
          this.select = 0
          if (this.deviceList && this.deviceList.length > 0) {
            if (this.deviceList[0].deviceId) {
              this.deviceId=this.deviceList[0].deviceId
              this.select=0
              if (this.deviceList[0].isOn){
                this.getHumanStatisticNow(this.deviceId)
              }
              // this.getMonitorDeviceInfo(this.deviceId)
              // this.getHumanStatisticNow(this.deviceId)
            }
          }
        } else {
          this.deviceList = []
          this.totalPage = 0
        }
      })
    },
    handleReachBottom () {
      return new Promise(resolve => {
        setTimeout(() => {
          this.pageSize = this.pageSize + 10;
          if (this.pageSize > this.totalCount) {
            this.$Message.warning(this.$t('common.noMoreData'));
            return;
          }
          this.getDeviceList()
          resolve();
        }, 200);
      });
    },
    //清空数据
    clearStatisticData(){
      this.exitedList=[]
      this.enteredList=[]
      this.timeList=[]
      this.isOpen=0
      this.dataKey=""

    },
    //切换卡号
    changeSelect(item, index) {
      this.select = index
      this.deviceId=item.deviceId
      this.clearStatisticData()
      // this.getMonitorDeviceInfo(this.deviceId)
      var el= document.getElementById('humanStatistic')
      if (el!=null){
        this.$echarts.dispose(el)
      }
      this.dateRange=[]
      if (item.isOn){
        this.getHumanStatisticNow(this.deviceId)
      }
    },
    //开启人流统计
    /*enableHumanStatistic(deviceId){
      this.deviceId=deviceId
      this.$http({
        url: this.$http.adornUrl('/monitor/device/humanNumberStatistic'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': deviceId,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.msg.msg === "success") {
            this.$Message.success(this.$t('common.enable'))
          } else {
            this.$Message.error(data.msg.msg)
            // this.$Message.error("该摄像头不支持人流统计功能")
          }
        } else {
          this.$Message.error(data.msg.msg)
          // this.$Message.error("该摄像头不支持人流统计功能")
        }
      })
    },*/

    // 查询当前的人流统计数据
    getHumanStatisticNow(deviceId){
      this.clearStatisticData()
      this.$http({
        url: this.$http.adornUrl('/monitor/humanNumberStatistic/queryHumanNumber'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': deviceId,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          // console.log(data)
          if (data.msg === "success") {
            this.dataList = data.data
            if (this.dataList && this.dataList.length > 0) {
              this.dataList.forEach(item => {
                this.enteredList.push(item.entered)
                this.exitedList.push(item.exited)
                this.timeList.push(item.eventTime)
              })
              this.myEcharts();
            }
            // else {
            //   this.$Message.success( this.$t('home.temporarilyNoData'))
            // }
          }else {
            this.$Message.error(data.msg.msg)
          }
          //
        }
        // this.$Message.error(data.msg.msg)
        this.clearStatisticData()
      })
    },
    /**
     * 根据选择的日期查询历史数据
     * @param deviceId
     */
    getHumanStatisticByDate(deviceId){

      var startDate = ""
      var endDate = ""
      //如果没有选择日期，则默认查询昨天的数据
      if (this.dateRange.length == 0) {
        endDate = this.DateToStr(new Date())
        var yesterday = new Date()
        yesterday.setTime(new Date().getTime() - 24 * 60 * 60 * 1000);
        startDate = this.DateToStr(yesterday)
      } else {
        startDate = this.DateToStr(this.dateRange[0])
        endDate = this.DateToStr(this.dateRange[1])
      }
      this.clearStatisticData()
      this.$http({
        url: this.$http.adornUrl('/monitor/humanNumberStatistic/queryHumanNumberByRangeDate'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': deviceId,
          'startDate':startDate,
          'endDate':endDate
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.msg === "success") {
            this.dataList = data.data
            if (this.dataList.length > 0) {
              this.dataList.forEach(item => {
                this.enteredList.push(item.entered)
                this.exitedList.push(item.exited)
                this.timeList.push(item.eventTime)
              })
              this.myEcharts();
            }else {
              this.$Message.success(this.$t('home.temporarilyNoData'))
            }
          }else {
            this.$Message.error(data.msg.msg)
          }
        }
        this.clearStatisticData()
      })
    },
// date格式转成yy-MM-dd
    DateToStr(dd) {
      var y = dd.getFullYear();
      var m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
      var d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
      return y + '-' + m + '-' + d;
    },
    //预览
    getRealplay (deviceId) {
      deviceId=this.deviceId
      // console.log(deviceId)
      this.realplayVisible = true
      this.$nextTick(() => {
        this.$refs.Realplay.init(deviceId)
      })
    },

    //构建图表
    myEcharts(){

      //客流
      if (this.humanNumberEChart!=null&&this.humanNumberEChart!=""&&this.humanNumberEChart!=undefined){
        this.humanNumberEChart.dispose();
      }
      this.humanNumberEChart=this.$echarts.init(this.$refs.humanStatistic);
      this.humanNumberEChart.setOption(this.humanNumberOption);
    },
    //发送数据弹窗
    showModal(deviceId){
      this.sendDataVisible=true
      this.getMonitorDeviceInfo(deviceId)

    },
    //查询当前监控设备的信息
    getMonitorDeviceInfo(deviceId){
      this.$http({
        url: this.$http.adornUrl(`/monitor/device/info/${deviceId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          // console.log(data)
          this.isOpen=data.deviceNode.sendHumanNumber
          this.dataKey=data.deviceNode.dataKey
        }else {
          this.$Message.error(data.msg.msg)
        }
      })
    },
    //开启|关闭发送数据到控制卡功能
    enableSendHumanNumber(state){
      this.$http({
        url:this.$http.adornUrl('/monitor/device/updateState'),
        method:'post',
        data: this.$http.adornData({
          'deviceId':this.deviceId,
          'state':state
        })
      }).then(({data})=>{
        if (data&&data.code===0){
          this.getMonitorDeviceInfo(this.deviceId)
          this.$Message.success("success")
        }else {
          this.$Message.error(data.msg.msg)
        }
      })
    },
    //点击复制
    copyKey(){
      //创建一个input标签
      var oInput = document.createElement("input");
      //将要复制的值赋值给input
      if (this.dataKey==null||this.dataKey==""){
        oInput.value=this.$t('statistic.noDataKey')
      }else {
        oInput.value = this.dataKey;
      }
      //在页面中插入
      document.body.appendChild(oInput);
      // 模拟鼠标选中
      oInput.select();
      // 执行浏览器复制命令（相当于ctrl+c）
      document.execCommand("Copy");
      //只是用一下input标签的特性，实际并不需要显示，所以这里要隐藏掉
      oInput.style.display = "none";
      this.$Message.success(this.$t('statistic.copySuccess'));
    }
  },
  deactivated() {
    // this.humanNumberEChart.dispose();
    var el= document.getElementById('humanStatistic')
    if (el!=null){
      this.$echarts.dispose(el)
    }
  },
  watch: {
    'languages': function(newVal, OldVal) {
      if (this.$route.path=="/humanNumberStatistic-device"){
        this.getHumanStatisticByDate(this.deviceId)
      }
    }
  },
  computed:{
    languages: {
      get () { return this.$store.state.language.language },
    },
    tableHeight: {
      get () { return this.$store.state.common.tableHeight },
    },
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth },
    },
    // 控制统计
    humanNumberOption: {
      get() {
        return {
          // width:window.innerWidth - 500,
          title: {
            text: this.$t('operation.passengerFlowStatistics'),
            subtext: this.$t('home.UnitTimes')
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
              label: {
                show: true
              }
            }
          },
          calculable: true,
          grid: {
            left: '1%',
            right: '10%',
            bottom: '5%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: this.timeList
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                formatter: (value) => {
                    return /^-?\d+$/.test(value) ? value : '' // 判断y轴数值为整数的才显示
                }
              }
            }
          ],
          legend: {
            // right: 'right',
            // data:[]
          },
          series: [
            {
              name: this.$t("statistic.enter"),
              type: 'bar',
              data: this.enteredList,
              itemStyle:{
                color: '#90EE90'
              }
            },
            {
              name: this.$t("statistic.exited"),
              type: 'bar',
              data: this.exitedList,
              itemStyle:{
                color: '#FFDEAD'
              }
            }
          ]
        }
      }
    },
  }

}
</script>

<style scoped>
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
}
.box {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 170px;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  background-color: rgb(255, 255, 255);
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-bottom: 5px;
  cursor: pointer;
}
.opera_ul li:hover {
  background-color: rgb(204, 204, 204);
  cursor: pointer;
}
.select {
  background-color: rgb(204, 204, 204);
}
</style>
