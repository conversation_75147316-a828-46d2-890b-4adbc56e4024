<template>
    <div :style="{'height': (tableHeight + 190) + 'px'}">
        <div class="left">
            <Input size="large" v-model="key" :placeholder="$t('cardDevice.deviceName') + '/ID'" :border="false" @keyup.native="getDeviceList(true)"></Input>
            <div class="device-list" v-if="deviceList.length > 0">
                <div v-for="(item, index) in deviceList" :key="index">
                    <div :class="selectDeviceId == item.deviceId ? 'selectDeviceId device-item': 'device-item'" @click="selectDeviceIdClick(index)">
                        <div class="device-name" :title="item.deviceId">
                            <span v-if="item.alias && item.alias != 'noname'">{{item.alias}}</span>
                            <span v-else>{{item.deviceId}}</span>
                            <span style="float: right;">
                                <svg width="25px" height="25px" aria-hidden="true"  style="vertical-align:middle;">
                                    <use :xlink:href="item.isOn===1 ? '#on-line' : '#line'"></use>
                                </svg>
                            </span>    
                        </div>
                    </div>
                </div>
            </div>
            <div class="device-list" style="text-align: center;font-size: 20px;line-height: 200px" v-if="deviceList.length == 0">
                <span>{{$t('home.temporarilyNoData')}}</span>
            </div>
            <Page style="text-align: right;padding-right: 5px;" :current="currentPage" :total="totalPage" :page-size="pageSize"
                simple @on-change="handlePageChange"/>
        </div>
        <div class="right">
            <div class="toolbar">
                <!-- 左侧日期选择 -->
                <div class="left-tool">
                    <RadioGroup v-model="timeType" type="button" @on-change="timeTypeChangeHandler()">
                        <Radio :label="'today'" value="today">{{$t('employee.today')}}</Radio>
                        <Radio :label="'yesterday'" value="yesterday">{{$t('employee.yesterday')}}</Radio>
                        <Radio :label="'7day'" value="7day">{{$t('employee.last7Days')}}</Radio>
                        <Radio :label="'30day'" value="30day">{{$t('employee.last30Days')}}</Radio>
                        <Radio :label="'custom'" value="custom">{{ $t('common.custom') }}</Radio>
                    </RadioGroup>
                    <DatePicker
                        v-if="timeType==='custom'"
                        v-model="timeRange"
                        @on-change="dateRangeChange()"
                        type="daterange"
                        placement="bottom-end"
                        :options="humanNumberOptions"
                        :placeholder="$t('broadcast.selectDate')"
                        style="width: 200px; margin-left: 10px">
                    </DatePicker>
                </div>
                <!-- 右侧图表切换 -->
                <div class="right-tool">
                    <RadioGroup v-model="chartType" type="button">
                        <Radio label="line">{{$t('employee.lineChart')}}</Radio>
                        <Radio label="bar">{{$t('employee.barChart')}}</Radio>
                    </RadioGroup>
                </div>
            </div>
            <!-- ECharts 容器 -->
            <div v-show="deviceList.length > 0" ref="chart" class="chart-container"></div>
            <div v-if="deviceList.length === 0" class="no-data">
                <div>
                    <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#notData"></use>
                    </svg>
                    <div>{{$t('home.temporarilyNoData')}}</div>
                </div>
            </div>
        </div>
    </div>

</template>

<script>
import * as echarts from 'echarts';
export default {
    data() {
        return {
            key: '',
            currentPage: 1,
            pageSize: 10,
            totalPage: 1,
            selectDeviceId: 'y01-111-12345', // 选中的deviceId
            deviceList: [],
            timeType: 'today', // 时间类型
            timeRange: [], // 时间范围
            chartType: 'line', // 图表类型
            chartData: { // 图表数据
                xAxis: [],
                series: [],
                series1: []
            },
            chartInstance: null, // ECharts 实例
            humanNumberOptions:{
                disabledDate (time) {
                    if (!time) return false;
                    let date = new Date()
                    date.setDate(date.getDate() - 30)
                    let now = new Date()
                    return !(time.valueOf() < now.getTime() && time.valueOf() > date.getTime())
                    // return  Date.now() -1000*60*60*24*365 >date.valueOf() || date.valueOf()> Date.now()+ 1000*60*60*24;
                }
            },
        }
    },
    watch: {
        chartType() {
            this.initChart()
        }
    },
    activated() {
        this.initData()
        this.queryDevicePage()
        this.initChart()
        window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize)
        if (this.chartInstance) {
        this.chartInstance.dispose()
        }
    },
    methods: {
        initData(){
            this.deviceList=[]
            this.selectedDeviceId=null
            this.currentPage= 1
            this.pageSize= 5
            this.totalPage=0
            this.timeRange=[]
            this.timeType= 'today' // 时间类型
            this.chartType= 'line' // 图表类型
            this.chartData= { // 图表数据
                xAxis: [],
                // 男
                series: [],
                // 女
                series1: []
            }
        },
        formatterChart(params) {
            // 初始化总和为0
            let sum = 0;
            // 遍历数据项，累加每个系列的值
            params.forEach(function (item) {
                sum += item.data;
            });
            // 通过 `<br/>` 分隔原有提示信息和总和
            return params[0].name +
            '<br/>' + params.map(item => item.seriesName + ' : ' + item.data).join('<br/>') +
            '<br/>'+ this.$t('program.total') +' : ' + sum;
        },
        initChart() {
            if (!this.chartInstance) {
                this.chartInstance = echarts.init(this.$refs.chart);
            }
            const option = {
                title: {
                    subtext: this.$t('employee.unitPerson'),
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: this.formatterChart.bind(this)
                },
                legend: {
                    data: [this.$t('employee.man'), this.$t('employee.woman')]
                },
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '1%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.xAxis,
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    axisLabel: {
                        formatter: (value) => {
                            return this.isInteger(value) ? value : '' // 判断y轴数值为整数的才显示
                        }
                    }
                },
                series: [
                    {
                        name: this.$t('employee.man'),
                        data: this.chartData.series,
                        type: this.chartType,
                        smooth: this.chartType === 'line',
                        itemStyle: {
                            color: '#2d8cf0'
                        },
                        areaStyle: this.chartType === 'line' ? {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(45,140,240,0.6)' },
                                { offset: 1, color: 'rgba(45,140,240,0.0)' }
                            ])
                        } : null
                    },
                    {
                        name: this.$t('employee.woman'),
                        data: this.chartData.series1,
                        type: this.chartType,
                        smooth: this.chartType === 'line',
                        itemStyle: {
                            color: '#19be6b'
                        },
                        areaStyle: this.chartType === 'line' ? {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(25,190,107,0.6)' },
                                { offset: 1, color: 'rgba(25,190,107,0.0)' }
                            ])
                        } : null
                    }
                ]
            }
            this.chartInstance.setOption(option,true)
            this.handleResize()
            // this.chartInstance.setOption(option,true)
        },
        // 判断是否为整数
        isInteger(value) {
            return /^-?\d+$/.test(value);
        },
        handleResize() {
            this.chartInstance.resize()
        },
        timeTypeChangeHandler() {
            if (!this.selectDeviceId) return;
            if(this.timeType == "today") {// 今天
                this.queryOneDayInsideNum('today', this.selectDeviceId )
            } else if (this.timeType == "yesterday") {// 昨天
                this.queryOneDayInsideNum('yesterday',this.selectDeviceId )
            } else if (this.timeType == "7day") {// 近7天
                let date = new Date();
                let startDate = date.toISOString().split('T')[0];
                date.setDate(date.getDate() - 6);
                let endDate = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                this.queryRangeDayInsideNum(this.selectDeviceId,endDate,startDate)
            } else if (this.timeType == "30day") {// 近30天
                let date = new Date();
                let startDate = date.toISOString().split('T')[0];
                date.setDate(date.getDate() - 29);
                let endDate = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                this.queryRangeDayInsideNum(this.selectDeviceId,endDate,startDate)
            } else if (this.timeType == "custom") {// 自定义
                this.dateRangeChange()
            }
        },
        // 切换日期
        dateRangeChange(){
            if (this.timeRange && this.timeRange.length>0){
                this.queryRangeDayInsideNum(this.selectDeviceId,this.handleDateToString(this.timeRange[0]),this.handleDateToString(this.timeRange[1]))
            }
        },
        // 将日期转换为字符串
        handleDateToString(date){
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // getMonth() 返回的是 0 到 11，所以需要加 1
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        // 查询某一天人数
        queryOneDayInsideNum(type, id){
            const url = '/humanNumberStatisticV1/card/humanStatistic/' + id + '/' + type;
            this.$http({
                url: this.$http.adornUrl(url),
                method: 'get',
                params: this.$http.adornParams({})
            }).then(({ data }) => {
                this.chartData.xAxis = data.msg.timeList
                this.chartData.series = data.msg.menNumList
                this.chartData.series1 = data.msg.womenNumList
                this.initChart()
            })
        },
        // 查询范围时间内人数
        queryRangeDayInsideNum(id,startDate,endDate){
            this.$http({
                url: this.$http.adornUrl('/humanNumberStatisticV1/card/rangeHumanStatistic/' + id),
                method: 'get',
                params: this.$http.adornParams({
                    'startDate':startDate,
                    'endDate':endDate
                })
            }).then(({ data }) => {
                this.chartData.xAxis = data.msg.timeList
                this.chartData.series = data.msg.menNumList
                this.chartData.series1 = data.msg.womenNumList
                this.initChart()
            })
        },
        selectDeviceIdClick(index) {
            this.selectDeviceId = this.deviceList[index].deviceId;
            this.queryOneDayInsideNum(this.timeType, this.selectDeviceId)
        },
        handlePageChange(val) {
            this.currentPage = val;
            this.queryDevicePage();
        }, 
        queryDevicePage(isQuery) {
            if (isQuery) {
                this.pageIndex = 1
            }
            // if(this.key == '') {
            //    return;
            // }
            this.$http({
                url: this.$http.adornUrl('/humanNumberStatisticV1/card/list'),
                method: 'get',
                params: this.$http.adornParams({
                    'page': this.pageIndex,
                    'limit': this.pageSize,
                    'key': this.key,
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.deviceList = data.page.list
                    this.totalPage = data.page.totalCount
                    if (this.deviceList.length > 0) {
                        if (this.deviceList[0].deviceId) {
                            this.selectDeviceId = this.deviceList[0].deviceId
                            this.queryOneDayInsideNum(this.timeType, this.deviceList[0].deviceId)
                        }
                    }
                } else {
                    this.deviceList = []
                    this.totalPage = 0
                }
            })
        }
    },
    computed:{
        languages: {
            get () { return this.$store.state.language.language },
        },
        tableHeight: {
            get () { return this.$store.state.common.tableHeight },
        },    
    }
}
</script>
<style scoped>
.left{
    width: 300px;
    float: left;
    height: 100%;
    background-color: #fff;
    border-radius: 10px;
}
.device-list {
    width: 100%;
    padding: 0 8px;
    height: calc(100% - 70px);
    overflow-y: auto;
}
.device-item {
    width: 100%;
    height: 50px;
    line-height: 50px; 
    cursor: pointer;
    margin-top: 5px;
    border-radius: 5px;
}
.selectDeviceId {
    background-color: rgb(204, 204, 204);
    color: #fff; 
}
.device-item:hover {
    background-color: rgb(204, 204, 204);
    color: #fff; 
}
.device-name {
    width: 100%;
    height: 100%;
}
.right{
    float: left;
    width: calc(100% - 300px);
    height: 100%;
}
.toolbar {
    width: 100%;
    height: 50px;
}
.left-tool {
    float: left;
}
.right-tool {
    float: right;
}
.chart-container {
    clear: both;
    width: 100%;
    height: calc(100vh - 140px);
    overflow: hidden;
}
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
}
</style>