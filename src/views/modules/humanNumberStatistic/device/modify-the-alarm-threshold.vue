<template>
  <Modal v-model="visible" width="500">
    <p slot="header" style="text-align:center">
      <span>{{$t('monitor.openTheAlarm')}}</span>
    </p>
    <Alert type="warning" show-icon ><b class="tip">{{$t('monitor.openTheAlarmTip')}} <br/></b></Alert>
    <Form ref="dataForm" :model="dataForm" :label-width="150" style="height: 200px" label-position="left">
      <i-Switch v-model="isOpen" size="large" @on-change="changeOpen"
        true-color="#13ce66" false-color="#ff4949">
          <template #open>
            <span>{{$t('sys.open')}}</span>
          </template>
          <template #close>
            <span>{{$t('sys.close')}}</span>
          </template>
      </i-Switch>
      <div v-if="isOpen">
        <FormItem prop="crowdAlarmThreshold" :label="$t('monitor.crowdAlarmThreshold')">
          <InputNumber size="large" style="width:150px;" v-model="dataForm.crowdAlarmThreshold" :min="1"></InputNumber>
        </FormItem>
        <FormItem prop="crowdAlarmThreshold" :label="$t('monitor.intervalForSendingEmails')">
          <InputNumber size="large" style="width:150px;" :min="1" v-model="dataForm.timeBetweenCrowdAlarms"></InputNumber>
        </FormItem>
      </div>
    </Form>
    <div slot="footer">
      <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
      <Button type="primary" :loading="loading" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      loading: false,
      isOpen: false,
      dataForm: {
        deviceId: '',
        crowdAlarmThreshold: -1,
        timeBetweenCrowdAlarms: 10,
        loginHandle: 0
      },
      crowdAlarmThreshold: -1
    }
  },
  methods: {
    // 初始化
    init(deviceId) {
      this.visible = true
      this.dataForm.deviceId = deviceId || null
      if (this.dataForm.deviceId) {
        this.$http({
          url: this.$http.adornUrl(`/monitor/insideHuman/info/${this.dataForm.deviceId}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataForm.deviceId = data.deviceNode.deviceId
            this.dataForm.crowdAlarmThreshold = data.deviceNode.crowdAlarmThreshold
            this.dataForm.timeBetweenCrowdAlarms = data.deviceNode.timeBetweenCrowdAlarms
            this.dataForm.loginHandle = data.deviceNode.loginHandle
            this.isOpen =  this.dataForm.crowdAlarmThreshold == -1 ? false : true
            this.crowdAlarmThreshold = this.dataForm.crowdAlarmThreshold
          }
        })
      } else {
        this.dataForm.deviceId = ''
        this.dataForm.crowdAlarmThreshold = -1
        this.dataForm.timeBetweenCrowdAlarms = 10
        this.dataForm.loginHandle = 0
      }
    },
    // 改变状态
    changeOpen(isOpen) {
      // 如果关闭功能将阈值设置为-1
      if(isOpen) {
        // 如果之前设置过阈值，将之前的阈值返回
        if (this.crowdAlarmThreshold != -1) {
          this.dataForm.crowdAlarmThreshold = this.crowdAlarmThreshold;
        } else {
          this.dataForm.crowdAlarmThreshold = 20;
        }
      } else {
        this.dataForm.crowdAlarmThreshold = -1;
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl(`/monitor/insideHuman/update`),
        method: 'post',
        data: this.$http.adornData({
          'deviceId': this.dataForm.deviceId,
          'crowdAlarmThreshold': this.dataForm.crowdAlarmThreshold,
          'timeBetweenCrowdAlarms': this.dataForm.timeBetweenCrowdAlarms,
          'loginHandle': this.dataForm.loginHandle,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.loading = false
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 0.5,
            onClose: () => {
              this.visible = false
            }
          })
        } else {
          this.$Message.error(data.msg)
          setTimeout(() => {
            this.loading = false
          }, 500)
        }
      })
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.dataForm.deviceId = ''
        this.dataForm.crowdAlarmThreshold = -1
        this.dataForm.timeBetweenCrowdAlarms = 10
        this.dataForm.loginHandle = 0
      }
    }
  },
}
</script>