<template>
<div>
  <Modal v-model="visible" width="70%" :mask-closable="false">
    <div slot="header" style="text-align: center">
      {{$t('statistic.countingMonitoring')}}
    </div>
    <div style="text-align:center">
      <video  id="realPlay" autoplay  muted="muted" width="100%"  height="100%">
        加载中...
      </video>
    </div>
    <div slot="footer">

    </div>
    <div slot="close" @click="destroyVideo()">
      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
        <use xlink:href="#close"></use>
      </svg>
    </div>
  </Modal>
</div>
</template>

<script>
import flvjs from "flv.js";
import {generateToken} from '@/utils/jwtUtils'
export default {
  name: "realplay",

  data(){
    return{
      visible: false,
      deviceId:"",
      flvPlayer: null,
    }

  },

  methods: {
    init(id) {
      this.deviceId=id
      console.log(this.deviceId)
      this.visible = true
      this.playVideo()
    },
    playVideo() {
      this.lastDecodedFrame = 0
      // 加载前先销毁
      let videoWin = document.getElementById("realPlay");
        if (flvjs.isSupported() ) {
          this.flvPlayer = flvjs.createPlayer({
            type: "flv",// 媒体类型
            isLive: true,//是否是实时流
            hasAudio: false,//是否有音频
            url: window.SITE_CONFIG.rtmp +this.deviceId +"&token="+ generateToken(this.deviceId),// 视频流地址
            stashInitialSize: 128 // 减少首帧显示等待时长
          }, {
            enableWorker: false,// 不启动分离线程
            enableStashBuffer: false,// 关闭IO隐藏缓冲区
            reuseRedirectedURL: true,// 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
            autoCleanupSourceBuffer: true, // 自动清除缓存
            fixAudioTimestampGap: false,// false 音频同步
          });
          // 断流重连
          this.flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
            console.log("errorType:", errorType);
            console.log("errorDetail:", errorDetail);
            console.log("errorInfo:", errorInfo);
            if (this.flvPlayer) {
              this.destroyVideo();
            }
          });
          // 画面卡死重连
          var than = this
          this.flvPlayer.on("statistics_info", function (res) {
            if (than.lastDecodedFrame == 0) {
              than.lastDecodedFrame = res.decodedFrames;
              return;
            }
            if (than.lastDecodedFrame != res.decodedFrames) {
              than.lastDecodedFrame = res.decodedFrames;
            } else {
              than.lastDecodedFrame = 0;
              if (than.flvPlayer) {
                than.destroyVideo();
                than.playVideo()
              }
            }
          });
          this.flvPlayer.attachMediaElement(videoWin);
          this.flvPlayer.load();
          let playPromise =this.flvPlayer.play();
          if (playPromise !== undefined) {
            playPromise.then(() => {
              this.flvPlayer.play()
            }).catch(() => {

            })
          }
        }
    },


    //销毁断流方法
    destroyVideo () {
      if (this.flvPlayer !== null) {
        this.flvPlayer.pause();
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
        // this.visible=false
      }
    },
  },
}
</script>

<style scoped>

</style>
