<template>
  <Modal v-model="visible" width="85%" :mask-closable="false">
    <div slot="header" style="text-align: center">
      <div>
        <span>{{$t('statistic.lineChart')}}</span>
      </div>
    </div>

<!--    <div v-if="dataLoading">-->
<!--      <div :style="{'text-align':'center'}"><span>{{msg}}</span></div>-->
<!--    </div>-->
<!--    <div v-else>-->

    <div>
      <div style="text-align: right">
        <Date-picker v-model="dateRange" format="yyyy-MM-dd" :options="insideNumberOption" type="daterange" placement="bottom-end" :placeholder="$t('card.DateRange')" style="width: 200px"></Date-picker>
        <Button type="primary" @click="queryInsideInfoRange()">{{$t('common.query')}}</Button>
      </div>
      <div v-show="list.length > 0" ref="humanInside" style="width: 100%;height: 750px"></div>
      <div v-if="list.length === 0" class="no-data">
        <div>
          <svg width="100px" height="100px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#notData"></use>
          </svg>
          <div>{{$t('home.temporarilyNoData')}}</div>
        </div>
      </div>
    </div>

<!--    </div>-->
    <div slot="footer">

    </div>
    <div slot="close" @click="destroyEcharts()">
      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
        <use xlink:href="#close"></use>
      </svg>
    </div>
  </Modal>
</template>

<script>

import echarts from "../../../../utils/echarts";

export default {
  name: "insideNumEcharts",


  data() {
    return {
      visible: false,
      deviceId: "",
      insideNumList: [],
      insideDateList: [],
      humanInsideNumberEChart: '',
      dataLoading: true,
      msg: '数据加载中',
      dateRange: [],
      startDate: "",
      endDate: "",
      insideNumberOption:{
        disabledDate (date) {
          return  Date.now() -1000*60*60*24*365 >date.valueOf() || date.valueOf()> Date.now()+ 1000*60*60*24;
        }
      },
      list: [],
    }
  },
  activated() {
    this.dataLoading = true
  },
  methods: {
    init(id) {
      this.deviceId = id
      // console.log(this.deviceId)
      this.dataLoading = true
      this.visible = true
      this.dateRange = []
      this.startDate = ""
      this.startTime = ""
      this.insideNumList=[]
      this.insideDateList=[]
      this.$http({
        url: this.$http.adornUrl(`/monitor/insideHuman/queryNow`),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': id,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.list = data.data
          if (this.list && this.list.length > 0) {
            this.list.forEach(item => {
              this.insideNumList.push(item.insidePeopleNum)
              this.insideDateList.push(item.stuTime)
            })
            this.dataLoading = false
            this.myEcharts();
          } else {
            this.msg = this.$t('home.temporarilyNoData')
          }
        }
      })

    },
    //构建图表
    myEcharts() {
      this.dataLoading = false
      //客流
      if (this.humanInsideNumberEChart != null && this.humanInsideNumberEChart != "" && this.humanInsideNumberEChart != undefined) {
        this.humanInsideNumberEChart.dispose();
      }
      this.humanInsideNumberEChart = this.$echarts.init(this.$refs.humanInside);
      this.humanInsideNumberEChart.setOption(this.humanInsideNumberOption);

    },
    destroyEcharts() {
      if (this.humanInsideNumberEChart != null && this.humanInsideNumberEChart != "" && this.humanInsideNumberEChart != undefined) {
        this.humanInsideNumberEChart.dispose();
      }
    },

    queryInsideInfoRange() {
      var startDate = ""
      var endDate = ""
      //如果未选择日期，直接查询，则默认查询昨天的数据
      if (this.dateRange.length == 0) {
        endDate = this.DateToStr(new Date())
        var yesterday = new Date()
        yesterday.setTime(new Date().getTime() - 24 * 60 * 60 * 1000);
        startDate = this.DateToStr(yesterday)
      } else {
        startDate = this.DateToStr(this.dateRange[0])
        endDate = this.DateToStr(this.dateRange[1])
      }
      this.$http({
        url: this.$http.adornUrl('/monitor/insideHuman/queryList'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': this.deviceId,
          'startDate': startDate,
          'endDate': endDate
        })
      }).then(({ data }) => {
        this.insideDateList = []
        this.insideNumList = []
        if (data && data.code === 0) {
          this.list = data.data
          if (this.list.length > 0) {
            this.list.forEach(item => {
              this.insideNumList.push(item.insidePeopleNum)
              this.insideDateList.push(item.stuTime)
            })
            this.dataLoading = false
            this.myEcharts();
          } else {
            this.msg = this.$t('home.temporarilyNoData')
          }
        }
        this.date = []
        this.startTime = ''
        this.endTime = ''
        // this.deviceId = ''
        // this.fileName=Date.now()


      })
      this.downloadVisible = false

    },
    // date格式转成yy-MM-dd HH:mm:ss
    DateToStr(dd) {
      var y = dd.getFullYear();
      var m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
      var d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
      // var h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
      // var n = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
      // var s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
      // return y + '-' + m + '-' + d + ' ' + h + ':' + n + ':' + s;
      return y + '-' + m + '-' + d;
    },

  },
  computed: {
    humanInsideNumberOption: {
      get() {
        return {
          tooltip: {
            trigger: 'axis',
            position: function (pt) {
              return [pt[0], '10%'];
            }
          },

          toolbox: {
            feature: {
              // saveAsImage: {}
              // dataZoom: {
              //   yAxisIndex: 'none'
              // },
              // restore: {},

            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.insideDateList
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: (value) => {
                  return /^-?\d+$/.test(value) ? value : '' // 判断y轴数值为整数的才显示
              }
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 10
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              name: this.$t('statistic.areaPeopleNum'),
              type: 'line',
              symbol: 'none',
              sampling: 'lttb',
              itemStyle: {
                color: 'rgb(135, 206, 250)'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(0,191,255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(135, 206, 250)'
                  }
                ])
              },
              data: this.insideNumList
            }
          ]
        }
      }
    },

  }
}


</script>

<style scoped>
.no-data{
  display: flex;
  justify-content: center; 
  align-items: center;
  text-align: center;
  height: 300px;
  font-size: 20px;
  font-weight: bold;
}
</style>
