<template>
  <div>
    <div class="verify-line">
      <Row>
        <Col span="5">
          <Input size="large" v-model="key" :placeholder="$t('cardDevice.deviceName')" :border="false" @keyup.native="getDeviceList(true)"></Input>
          <Card shadow :style="{'height': tableHeight + 140 + 'px'}">
            <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight + 155"> -->
            <div :style="{'height':tableHeight + 45+ 'px', 'overflow-y': 'auto'}">
              <ul class="opera_ul" v-if="deviceList && deviceList.length > 0">
                <li v-for="(item, index) in deviceList" :key="index" @click="changeSelect(item, index)">
                  <div :class="select === index ? 'select' : ''" style="padding-left: 10px;padding-right: 10px;">
                    <span class="box">{{item.alias}}</span>
                    <div style="display: inline-block;">
                      <svg width="25px" height="25px" aria-hidden="true">
                        <use :xlink:href="item.isOn===1 ? '#on-line' : '#line'"></use>
                      </svg>
                    </div>
                  </div>
                </li>
              </ul>
              <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
                {{ $t('home.temporarilyNoData') }}
              </div>
            </div>
            <!-- </Scroll> -->
            <Page size="small" :total="totalPage" :current="pageIndex" :page-size="pageSize"
              show-elevator show-total @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
          </Card>
        </Col>
        <Col span="19">
          <div style="height: 100%;overflow-y:auto">
            <div style="display:flex;justify-content: space-between;">
              <div style="font-size: 20px;padding-bottom: 10px;">
                <span style="margin-left: 5px">{{$t('statistic.currentNumber')}}：【{{ this.insideNum }}】</span>
              </div>
              <div>
                <Button type="primary" v-show="deviceList.length>0" @click="modifyTheAlarmThreshold(deviceId)">{{$t('monitor.openTheAlarm')}}</Button>
                <Button type="primary" v-show="deviceList.length>0" @click="getInsideNumEcharts(deviceId)">{{$t('statistic.viewChart')}}</Button>
              </div>
            </div>
            <video id="realPlay" autoplay muted="muted" width="100%" height="90%">
              {{$t('common.Loading')}}
            </video>

          </div>
        </Col>
      </Row>
      <inside-num-echarts v-if="echartsVisible" ref="insideNumEcharts"></inside-num-echarts>
      <modify-the-alarm-threshold v-if="modifyTheAlarmThresholdVisible" ref="modifyTheAlarmThreshold"></modify-the-alarm-threshold>
    </div>
  </div>
</template>

<script>
import insideNumEcharts from "./device/insideNumEcharts";
import modifyTheAlarmThreshold from './device/modify-the-alarm-threshold.vue';
import flvjs from "flv.js";
import {generateToken} from '@/utils/jwtUtils'
export default {
  components: { insideNumEcharts, modifyTheAlarmThreshold },
  data() {
    return {
      key: '',
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      deviceList: [],
      deviceId: "",

      flvPlayer: null,

      //图表可视
      echartsVisible: false,
      //人数变化可视
      numVisible: false,

      select: 0,

      date: [],

      temp: 0,

      insideNum: 0,


      websocket: null,
      token: '',
      modifyTheAlarmThresholdVisible: false,


    }
  },

  activated() {
    this.getDeviceList()
    this.insideNum=0
  },
  methods: {
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDeviceList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDeviceList()
    },
    //连接websocket
    connectWS: function (deviceId) {
      if (typeof WebSocket === "undefined") {
        this.$message({
          type: "warning",
          message: "当前浏览器不支持WebSocket",
          showClose: true,
        });
      } else {
        let token = this.$cookie.get('token')
        // let url = `ws://192.168.1.115:8088/webSocket?deviceId=${deviceId}&token=${token}`;
        // let url = `ws://192.168.1.115:8088/websocket/` + deviceId + `/` + token;
        let url=`${window.SITE_CONFIG.INSIDEURL}/websocket/${deviceId}/${this.$cookie.get("token")}`
        // debugger
        this.websocket = new WebSocket(url);
        this.websocket.onopen = this.open;
        this.websocket.onerror = this.error;
        this.websocket.onmessage = this.message;
        this.websocket.onclose = this.close;
        // this.numVisible = false
      }
    },
    open: function () {
      console.log("WebSocket Connected successfully~");
    },
    error: function (e) {
      console.log("WebSocket Error: " + JSON.stringify(e));
    },


    message: function (msg) {
      // console.log("WebSocket Message: ", msg.data);
      let newData = JSON.parse(msg.data);

      this.insideNum = newData.insidePeopleNum
      // console.log(newData)

    },


    send: function (params) {
      this.websocket.send(params);
    },
    close: function (e) {
      console.log('websocket 断开: ' + e.code + ' ' + e.reason + ' ' + e.wasClean)
      console.log(e)
      // console.log("WebSocket Disconnected!");
      // let url=`${window.SITE_CONFIG.INSIDEURL}/websocket/${deviceId}/${this.$cookie.get("token")}`
      this.websocket.close()
        setTimeout(() => {
          console.log("WebSocket reconnecting...");
        }, 3000);
    },
    closeWebSocket: function () {
      console.log("Closing websocket...");
      this.numVisible = false
      if (this.websocket) {
        this.websocket.close();
      }
    },


    playVideo() {

      this.lastDecodedFrame = 0
      // 加载前先销毁
      let videoWin = document.getElementById("realPlay");
      if (flvjs.isSupported()) {
        this.flvPlayer = flvjs.createPlayer({
          type: "flv",// 媒体类型
          isLive: true,//是否是实时流
          hasAudio: false,//是否有音频
          url: window.SITE_CONFIG.rtmp + this.deviceId +"&token="+ generateToken(this.deviceId),// 视频流地址
          stashInitialSize: 128 // 减少首帧显示等待时长
        }, {
          enableWorker: false,// 不启动分离线程
          enableStashBuffer: false,// 关闭IO隐藏缓冲区
          reuseRedirectedURL: true,// 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
          autoCleanupSourceBuffer: true, // 自动清除缓存
          fixAudioTimestampGap: false,// false 音频同步
        });
        this.numVisible=true
        // 断流重连
        this.flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
          console.log("errorType:", errorType);
          console.log("errorDetail:", errorDetail);
          console.log("errorInfo:", errorInfo);
          if (this.flvPlayer) {
            this.destroyVideo();
          }
        });
        // 画面卡死重连
        var than = this
        this.flvPlayer.on("statistics_info", function (res) {
          if (than.lastDecodedFrame == 0) {
            than.lastDecodedFrame = res.decodedFrames;
            return;
          }
          if (than.lastDecodedFrame != res.decodedFrames) {
            than.lastDecodedFrame = res.decodedFrames;
          } else {
            than.lastDecodedFrame = 0;
            if (than.flvPlayer) {
              this.numVisible = true
              than.destroyVideo();
              than.playVideo()

            }
          }
        });
        this.flvPlayer.attachMediaElement(videoWin);
        this.flvPlayer.load();
        let playPromise = this.flvPlayer.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            this.flvPlayer.play()
            this.numVisible = true
          }).catch(() => {

          })
        }
      }
    },
    //销毁断流方法
    destroyVideo() {
      this.numVisible = false
      if (this.flvPlayer !== null) {
        this.flvPlayer.pause();
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
      }
    },

    // 获取数据列表
    getDeviceList(isQuery) {
      if (isQuery) {
         this.pageIndex = 1
      }
      this.numVisible = false
      this.$http({
        url: this.$http.adornUrl('/monitor/insideHuman/list'),
        method: 'get',
        params: this.$http.adornParams({
          // 'isOn': "1",
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.key,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          // console.log(data)
          this.totalPage = data.page.totalCount
          this.deviceList = data.page.list
          this.select = 0
          if (this.deviceList.length > 0) {
            if (this.deviceList[0].deviceId) {
              this.deviceId = this.deviceList[0].deviceId
              this.select = 0
              this.connectWS(this.deviceId);
              this.playVideo()
            }
          }
        } else {
          this.deviceList = []
          this.totalPage = 0
        }
      })
    },
    handleReachBottom() {
      return new Promise(resolve => {
        setTimeout(() => {
          this.pageSize = this.pageSize + 10;
          if (this.pageSize > this.totalCount) {
            this.$Message.warning(this.$t('common.noMoreData'));
            return;
          }
          this.getDeviceList()
          resolve();
        }, 200);
      });
    },

    clearStatisticData() {
      this.humanNumberEChart = ''
    },
    changeSelect(item, index) {
      this.select = index
      this.closeWebSocket()
      this.deviceId = item.deviceId
      this.destroyVideo()
      this.playVideo()
      this.clearStatisticData()
      this.insideNum=0
      setTimeout(() => {
        // console.log(this.deviceId)
        this.connectWS(this.deviceId);
        // console.log('重新打开');
      }, 100);

    },

    //查看图表
    getInsideNumEcharts(deviceId) {
      deviceId = this.deviceId
      // console.log(deviceId)
      this.echartsVisible = true
      this.$nextTick(() => {
        this.$refs.insideNumEcharts.init(deviceId)
      })
    },

    // 修改报警阈值
    modifyTheAlarmThreshold(deviceId) {
      this.modifyTheAlarmThresholdVisible = true
      this.$nextTick(() => {
        this.$refs.modifyTheAlarmThreshold.init(deviceId)
      })
    },

  },
  beforeUnmount() {
    window.removeEventListener("beforeunload", (e) => this.closeWebSocket(e));
  },

  beforeDestroy: {},
  computed: {
    language: {
      get() {
        return this.$store.state.language.language
      },
    },
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight
      },
    },
    documentClientWidth: {
      get() {
        return this.$store.state.common.documentClientWidth
      },
    },
  },
  //切换页面关闭websocket和video
  deactivated() {
    this.closeWebSocket()
    // console.log("切换页面")
    this.destroyVideo()
  },

}
</script>

<style scoped>
.box {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 170px;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}

.opera_ul li {
  background-color: rgb(255, 255, 255);
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-bottom: 5px;
  cursor: pointer;
}

.opera_ul li:hover {
  background-color: rgb(204, 204, 204);
  cursor: pointer;
}

.select {
  background-color: rgb(204, 204, 204);
}
</style>
