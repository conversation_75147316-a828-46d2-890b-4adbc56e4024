<!-- 智慧物联 -->
<template>
  <div class="modiles-device">
    <device-list-model v-if="deviceListModel" ref="deviceListModel" @changeModel="changeModel"></device-list-model>
    <device-map-model v-if="deviceMapModel" ref="deviceMapModel" @changeModel="changeModel"></device-map-model>
  </div>
</template>

<script>
import deviceListModel from './device-list-model.vue'
import deviceMapModel from './device-map-model.vue'
export default {
    data () {
        return {
          deviceListModel: false,
          deviceMapModel: true,
        }
    },
    components: {
      deviceMapModel,
      deviceListModel
    },
    methods: {
      changeModel(model) {
        if (model === 'map') {
          this.deviceMapModel = true
          this.deviceListModel = false
          this.$nextTick(() => {
            this.$refs.deviceMapModel.initMap()
          })
        } else {
          this.deviceMapModel = false
          this.deviceListModel = true
          this.$nextTick(() => {
            this.$refs.deviceListModel.initData()
          })
        }
      }
    },
    activated () {
      this.deviceListModel = false
      this.deviceMapModel = true
      this.$nextTick(() => {
        this.$refs.deviceMapModel.initMap()
      })
    },
    computed: {

    }
}
</script>

<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 189px;
  background:#eee;
  overflow: hidden;
  overflow-y: auto;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 94px;
  margin-left: 10px;
  margin-right: 10px;
}
.opera_list {
  padding-top: 10px;
  width: 90px;
  height: 90px;
  margin-left: 25%;
}
.opera_list:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}
.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}
.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}
.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
</style>