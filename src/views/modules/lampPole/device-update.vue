<template>
  <div>
    <Modal v-model="visible" width="600" footer-hide>
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{id}}</span>
      </p>
      <Form :model="dataForm" :label-width="75" inline label-colon style="margin-top: 20px;height:400px ">
        <FormItem :label="$t('lamp.poleName')">
           <Row>
            <Col span="23">
              <Input v-model="dataForm.alias" style="width:330px;margin-right: 30px"  placeholder=""></Input>
            </Col>
            <Col span="1">
              <Button type="primary"  :loading="alias_loading" @click="submitUpdate()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
<!--        <FormItem prop="groupId" :label="$t('group.name')">-->
<!--          <Row>-->
<!--            <Col span="23">-->
<!--              <Select style="width:330px;margin-right: 30px;" v-model="dataForm.groupId" filterable clearable transfer :placeholder="$t('common.PleaseSelect') + $t('group.name')">-->
<!--                <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{item.cardCount}})</Option>-->
<!--              </Select>-->
<!--            </Col>-->
<!--            <Col span="1">-->
<!--              <Button type="primary" style="float:right;" :loading="group_loading" @click="submitGroup()">{{$t('common.set')}}</Button>-->
<!--            </Col>-->
<!--          </Row>-->
<!--        </FormItem>-->
        <FormItem prop="groupId" :label="$t('common.selectingGroup')" >
          <Row>
            <Col span="23">
              <div style="width: 330px;margin-right: 30px;text-align: center">
                <span >{{ groupName }}</span>
              </div>
            </Col>
            <Col span="1">
              <Button type="primary"  :loading="group_loading" @click="changeGroup()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
<!--        <FormItem v-if="isAuth('screen:card:deploy')" prop="userId" :label="$t('common.AssignUsers')">
          <Row>
            <Col span="23">
              <Select style="width:330px;margin-right: 30px;"  v-model="dataForm.userId" :placeholder="$t('common.PleaseSelect') + $t('common.AssignUsers')" filterable clearable transfer>
                <Option v-for="item in userList" :value="item.userId" :key="item.username">{{ item.username }}</Option>
              </Select>
            </Col>
            <Col span="1">
              <Button type="primary" :loading="userId_loading" @click="submitUserId()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>-->
        <FormItem :label="$t('lamp.longitude')">
          <Row>
            <Col span="23">
              <InputNumber v-model="dataForm.longitude"  :min="-180" :max="180" style="width:125px;margin-right: 7px" placeholder=""></InputNumber>
              <span style="vertical-align: middle; font-size: 14px; color: #515a6e; line-height: 1;">{{$t('lamp.latitude')}}：</span>
              <InputNumber v-model="dataForm.latitude" :min="-90" :max="90" style="width:125px;margin-right: 30px;" placeholder=""></InputNumber>
              <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;" @click="showMap">
                <use xlink:href="#location"></use>
              </svg>
            </Col>
            <Col span="1">
              <Button type="primary" :loading="lngAndLat_loading" @click="submitLngAndLat()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
        <div v-if="errorMsg" style="height: 50px; color: red; overflow-y: auto;">
          {{errorMsg}}
        </div>
      </Form>
    </Modal>
    <!--分组弹出框-->
    <Modal v-model="selectGroupVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{$t('common.selectGroup')}}</span>
      </p>
      <Alert type="info" show-icon >
        <span>{{this.$t('tips.groupTip')}}</span>
      </Alert>
      <div>
        <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
      </div>
      <div slot="footer">
        <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
    <!-- 显示地图 -->
    <device-map v-if="deviceMap" ref="deviceMap" @selectLocation="selectLocation"></device-map>
  </div>
</template>
<script>
import deviceMap from './device-map.vue'
export default {
  data () {
    return {
      dataForm: {
        alias: '',
        groupId: '',
        userId: '',
        longitude: 0,
        latitude: 0
      },
      errorMsg: '',
      id: '',
      visible: false,
      alias_loading: false,
      groupList: [],
      group_loading: false,
      // userList: [],
      userId_loading: false,
      deviceMap: false,
      lngAndLat_loading: false,
      groupName:"",
      groupCount:0,
      groupOption:{
        value:"",
        label:""
      },
      //选择分组时，分组框是否可见
      selectGroupVisible:false,
      rootNode:null,
      totalNum:0
    }
  },
  components: {
    deviceMap
  },
  methods: {
    // 初始化
    init (id, createUserId,totalNum) {
      this.visible = true
      this.id = id
      this.totalNum=totalNum
      if (this.id !== '') {
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/getSettingItem'),
          method: 'post',
          data: this.id
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.card.selectInfo) {
              this.dataForm.alias = data.card.selectInfo.alias
              this.dataForm.groupId = data.card.selectInfo.group_id + ""
              this.dataForm.userId = data.card.selectInfo.user_id
              this.groupName=data.card.selectInfo.groupName
              var longitude = data.card.selectInfo.longitude
              if (longitude) {
                this.dataForm.longitude = parseFloat(longitude)
              }
              var latitude = data.card.selectInfo.latitude
              if (latitude) {
                this.dataForm.latitude = parseFloat(latitude)
              }
            }
            // this.userList = data.card.userList
            this.groupList = data.card.groups
            this.getUnGroupDevice()
            this.visible = true
            // 1 为超级管理员id
            // if (createUserId !== 1) {
            //   this.dataForm.userId = createUserId
            // }
          } else {
            this.$Message.error(data.msg)
          }
        })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    // 修改别名
    submitUpdate () {
      this.alias_loading = true
      this.$http({
        url: this.$http.adornUrl('/card/set/setCardAlias'),
        method: 'post',
        data: this.$http.adornData({'cardId': this.id, 'alias': this.dataForm.alias}, false)
      }).then(({data}) => {
        this.alias_loading = false
        if (data && data.code !== 0) {
          this.$Message.error(data.msg)
          this.errorMsg = data.msg
        } else {
          this.visible = false
        }
      })
    },
    // 修改分组
    submitGroup () {
      this.group_loading = true
          this.$http({
          url: this.$http.adornUrl(`/lampPole/card/updateGroup`),
          method: 'post',
          data: this.$http.adornData({
              'cardIds': [this.id],
              'group': this.dataForm.groupId
          })
      }).then(({data}) => {
          if (data && data.code === 0) {
              this.group_loading = false
              this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                      this.visible = false
                      this.$emit('refreshDataList')
                  }
              })
          } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                  this.group_loading = false
              }, 500)
          }
      })
    },
    // 分配所属用户
/*    submitUserId () {
      this.userId_loading = true
      var ids=[];
      ids.push(this.id)
      this.$http({
        url: this.$http.adornUrl('/lampPole/card/deploy'),
        method: 'post',
        data: this.$http.adornData({
          'cardIds': ids,
          'userId': this.dataForm.userId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userId_loading = false
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 0.5,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.userId_loading = false
          this.$Message.error(data.msg)
        }
      })
    },*/
    // 设置经纬度信息
    submitLngAndLat () {
      this.lngAndLat_loading = true
      if (this.dataForm.latitude==null||this.dataForm.longitude==null){
        this.lngAndLat_loading=false
        this.$Message.error(this.$t('lamp.latAndLngNotEmpty'))
      }else {
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/setLngAndLat'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.id,
            'longitude': this.dataForm.longitude,
            'latitude': this.dataForm.latitude
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.lngAndLat_loading = false
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.lngAndLat_loading = false
            this.$Message.error(data.msg)
          }
        })
      }
    },
    // 显示地图
    showMap() {
      this.deviceMap = true
      this.$nextTick(() => {
        // 如果经度纬度不为空按照经纬度显示
        this.$refs.deviceMap.init(parseFloat(this.dataForm.longitude), parseFloat(this.dataForm.latitude))
      })
    },
    selectLocation(businessDetail) {
      if (businessDetail && businessDetail.longitude) {
        this.dataForm.longitude = businessDetail.longitude
      }
      if (businessDetail && businessDetail.latitude) {
        this.dataForm.latitude = businessDetail.latitude
      }
    },
    changeGroup(){
      // this.getGroupList()
      this.selectGroupVisible=true
    },
    // 表单提交
    dataFormSubmit () {
      var rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      this.dataForm.groupId=rootNode.id
      this.selectGroupVisible=false
      this.submitGroup()
      this.groupName=rootNode.name
      this.rootNode=null
    },
    cancelSelect(){
      this.selectGroupVisible=false
      // this.dataForm.group=[]
      // this.groupName=this.$t('common.selectGroup')
      this.rootNode=null
      // this.getDataList()
      // this.getGroupList()

    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.dataForm.alias = ''
          this.dataForm.longitude = 0
          this.dataForm.latitude = 0
          this.id = ''
          this.$emit('refreshDataList')
          this.errorMsg = ''
          this.alias_loading=false
          this.group_loading=false
          this.userId_loading=false
          this.lngAndLat_loading=false
        }, 200)
      }
    }
  }
}
</script>
<style scoped>
</style>
