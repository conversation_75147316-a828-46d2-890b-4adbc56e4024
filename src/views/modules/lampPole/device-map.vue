<template>
  <div>
    <Modal v-model="visible" :title="$t('lamp.ChooseTargeting')" @on-ok="ok" @on-cancel="cancel" width="800">
      <div style="margin-bottom: 10px">
        <Form :label-width="40" inline>
          <FormItem :label="$t('lamp.latitude')">
            <Input v-model="mapData.center.lat" placeholder="lat" style="width: 120px" @on-blur="changePosition()" />
          </FormItem>
          <FormItem :label="$t('lamp.longitude')">
            <Input v-model="mapData.center.lng" placeholder="lng" style="width: 120px" @on-blur="changePosition()" />
          </FormItem>
          <FormItem>
            <Button type="primary" style="margin-left: 10px" :loading="cardLocationLoading" @click="getCardLocation()">
              {{ $t('operation.getGspInfo') }}</Button>
          </FormItem>
        </Form>
      </div>
      <div style="height: 400px;position: relative;">
        <div style="height: 400px" id="DMap"></div>
        <Spin fix v-if="spinShow">
          <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
          <div>{{$t('lamp.LoadingPositioning')}}...</div>
        </Spin>
      </div>
    </Modal>
  </div>
</template>

<script>
import loadBMap from '../../../utils/loadMap'
export default {
  data () {
    return {
      visible: false,
      //位置信息(包含坐标信息)
      businessDetail:{},
      //地图数据
      mapData: {
        //中心坐标
        center: { lng: 0, lat: 0 },
        //缩放级别
        zoom: 12
      },
      spinShow: false,
      myMap: null,
      geolocationControl: null,
      deviceId: '',
      cardLocationLoading:false
    }
  },
  methods: {
    //在地图上选择区域
    getLocation(lng,lat,type) {
      this.mapData.center.lng = lng
      this.mapData.center.lat = lat
      if (type===2){
        this.myMap.centerAndZoom(new BMap.Point(this.mapData.center.lng, this.mapData.center.lat),this.mapData.zoom)
      }
      //设置经度
      this.businessDetail.longitude = lng
      //设置纬度
      this.businessDetail.latitude = lat
      //清除地图上所有的覆盖物(保证每次点击只有一个标记)
      this.myMap.clearOverlays()
      //创建定位标记
      let marker = new BMap.Marker(new BMap.Point(lng, lat))
      //将标记添加到地图上
      this.myMap.addOverlay(marker)
      //创建坐标解析对象
      // let geoc = new BMap.Geocoder()
      // //解析当前的坐标成地址
      // geoc.getLocation(e.point, (rs) => {
      //   //获取地址对象
      //   let addressComp = rs.addressComponents
      //   //拼接出详细地址
      //   this.businessDetail.address =
      //     addressComp.province +
      //     addressComp.city +
      //     addressComp.district +
      //     addressComp.street +
      //     addressComp.streetNumber
      // })
    },
    init (longitude, latitude, deviceId) {
      if (deviceId) {
        this.deviceId = deviceId
      }
      this.visible = true
      if (longitude && latitude) {
        //设置经度
        this.mapData.center.lng = this.businessDetail.longitude = longitude
        //设置纬度
        this.mapData.center.lat = this.businessDetail.latitude = latitude
      }
      this.initMap()
    },
    locationError (StatusCode) {
      this.$Message.error($t('lamp.FailedToGetLocationInformation'))
    },
    locationSuccess (lng,lat) {
      //清除地图上所有的覆盖物(保证每次点击只有一个标记)
      this.myMap.clearOverlays()
      //创建定位标记
      let marker = new BMap.Marker(new BMap.Point(lng, lat))
      //将标记添加到地图上
      this.myMap.addOverlay(marker)
    },
    ok () {
      if (this.deviceId) {
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/setLngAndLat'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.deviceId,
            'longitude': this.businessDetail.longitude.toString(),
            'latitude': this.businessDetail.latitude.toString()
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                this.visible = false
                this.$emit('selectLocation')
              }
            })
          } else {
            this.$Message.error(data.msg)
          }
        })
      } else {
        this.visible = false
        this.$emit('selectLocation',this.businessDetail)
      }
    },
    cancel () {
        this.visible = false
    },
    initMap() {
      loadBMap().then(() => {
          // 百度地图API功能
        this.myMap = new BMap.Map("DMap") // 创建Map实例
        var _this = this

        //如果一开始坐标存在(编辑的时候)
        if (this.businessDetail.longitude && this.businessDetail.latitude) {
          this.mapData.center.lng = this.businessDetail.longitude
          this.mapData.center.lat = this.businessDetail.latitude

          this.myMap.clearOverlays()
          //创建定位标记
          let marker = new BMap.Marker(
            new BMap.Point(
              this.businessDetail.longitude,
              this.businessDetail.latitude
            )
          )
          //将标记添加到地图上
          this.myMap.addOverlay(marker)
          this.myMap.centerAndZoom(new BMap.Point(this.mapData.center.lng, this.mapData.center.lat), this.mapData.zoom)
        }else {
          this.spinShow = true
          this.myMap.clearOverlays()
          this.mapData.center.lng = this.businessDetail.longitude = 116.4
          this.mapData.center.lat = this.businessDetail.latitude = 39.9
          //创建定位标记
          let marker = new BMap.Marker(
            new BMap.Point(
              this.businessDetail.longitude,
              this.businessDetail.latitude
            )
          )
          this.myMap.addOverlay(marker)
          this.myMap.centerAndZoom(new BMap.Point(this.mapData.center.lng, this.mapData.center.lat), this.mapData.zoom)
          this.spinShow = false
        }
/*        else {
          this.spinShow = true
          //如果坐标不存在则动态获取当前浏览器坐标（创建的时候）
          let geolocation = new BMap.Geolocation()
          //获取当前的坐标（使用promise 将异步转换为同步）
          geolocation.getCurrentPosition((r) => {
            this.mapData.center.lng = this.businessDetail.longitude = r.point.lng
            this.mapData.center.lat = this.businessDetail.latitude = r.point.lat


            this.myMap.clearOverlays()
            //创建定位标记
            let marker = new BMap.Marker(
              new BMap.Point(
                this.businessDetail.longitude,
                this.businessDetail.latitude
              )
            )
            //将标记添加到地图上
            this.myMap.addOverlay(marker)
            this.myMap.centerAndZoom(new BMap.Point(this.mapData.center.lng, this.mapData.center.lat), this.mapData.zoom)
            this.spinShow = false
          })
        }*/

        this.myMap.enableScrollWheelZoom(true) //开启鼠标滚轮缩放

        // 添加比例尺控件
        var scaleCtrl = new BMap.ScaleControl({
          // 控件的停靠位置（可选，默认左上角）
          anchor: BMAP_ANCHOR_BOTTOM_LEFT,
          // 控件基于停靠位置的偏移量（可选）
          offset: new BMap.Size(10, 10)
        });
        this.myMap.addControl(scaleCtrl);
        // 添加定位控件
        this.geolocationControl = new BMap.GeolocationControl({
            // 控件的停靠位置（可选，默认左上角）
            anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
            // 控件基于停靠位置的偏移量（可选）
            offset: new BMap.Size(10, 10)
        });
        // 将控件添加到地图上
        this.myMap.addControl(this.geolocationControl);


        // 添加定位事件
        this.geolocationControl.addEventListener("locationSuccess", function(e){
           _this.locationSuccess(e.point.lng,e.point.lat)
          _this.getLocation(e.point.lng,e.point.lat,1)
        });
        this.geolocationControl.addEventListener("locationError",function(e){
            _this.locationError(e)
        });

        this.myMap.addEventListener('click', function(e) {
          _this.getLocation(e.point.lng,e.point.lat,1)
        })

        // 添加缩放控件
        var navigationControl = new BMap.NavigationControl({
            anchor: BMAP_ANCHOR_TOP_RIGHT,
            offset: new BMap.Size(10, 10)
        });
        this.myMap.addControl(navigationControl);

        // 创建城市选择控件
        var cityControl = new BMap.CityListControl({
            // 控件的停靠位置（可选，默认左上角）
            anchor: BMAP_ANCHOR_TOP_LEFT,
            // 控件基于停靠位置的偏移量（可选）
            offset: new BMap.Size(10, 5)
        });
        // 将控件添加到地图上
        this.myMap.addControl(cityControl);

      }).catch(err => {
          console.log('地图加载失败')
      })
    },

    // 获取设备的定位
    getCardLocation(){
      if (this.deviceId) {
        this.cardLocationLoading=true
        var ids=[this.deviceId]
        this.$http({
          url: this.$http.adornUrl('/card/query/getGpsInfo'),
          method: 'post',
          data: ids
        }).then(({data}) => {
          if (data && data.code == 0&&data.data[0]._type==='success') {
            this.mapData.center.lng = data.data[0].lng
            this.mapData.center.lat = data.data[0].lat
            this.changePosition()
          } else {
            this.$Message.error(data.data[0].msg)
          }
          this.cardLocationLoading=false
        })
      } else {
        this.visible = false
        this.$emit('selectLocation',this.businessDetail)
      }
    },
    // 修改输入的经纬度时，更新地图上的位置
    changePosition(){
      this.locationSuccess(this.mapData.center.lng,this.mapData.center.lat)
      this.getLocation(this.mapData.center.lng,this.mapData.center.lat,2)
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.businessDetail = {},
          this.mapData.center = { lng: 0, lat: 0 }
          this.mapData.zoom = 12
        }, 200)
      }
    }
  }
}
</script>

<style>
.citylist_popup_main .citylist_ctr_content .city_content_top {
  height: 50px;
}
.anchorBL img{
  display:none;
}
.BMap_cpyCtrl {
  display:none;
  visibility:hidden;
}
/* .info {
  z-index: 999;
  margin-left: 1.25rem;
  margin-top: 15px;
  position: fixed;
  border-radius: .25rem;
  color: #666;
  box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
} */
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
</style>
