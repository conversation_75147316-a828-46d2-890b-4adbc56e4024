<template>
    <Modal v-model="visible"  height="400" width="500">
       <p slot="header" style="text-align:center">
            <span>{{$t('common.deploy')}}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 200px;" :label-width="80" label-position="left" label-colon
        @keyup.enter.native="dataFormSubmit()">
          <FormItem prop="userId" :label="$t('login.username')">
            <Select style="width:300px;"  v-model="dataForm.userId" :placeholder="$t('common.PleaseSelect') + $t('login.username')" filterable clearable transfer>
              <Option v-for="item in userList" :value="item.userId" :key="item.username">{{ item.username }}</Option>
            </Select>
            <Button type="primary" style="float:right;" :loading="modal_loading" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
          </FormItem>
        </Form>
        <div slot="footer" style="text-align: left;">
          <span>
              <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
          </span>
          <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="id in ids" :key="id" style="color: #999;font-weight: normal">{{id}}</BreadcrumbItem>
            </Breadcrumb>
          </div>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      ids: [],
      dataForm: {
        userId: ''
      },
      userList: [],
      modal_loading: false
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          userId: [
          { validator: (rule, value, callback) => {
            if (value === '') {
              callback(new Error(this.$t('common.PleaseSelect') + this.$t('login.username')))
            } else {
              callback()
            }
          }, trigger: 'change' }
        ]
        }
      }
    }
  },
  methods: {
    // 初始化
    init (ids, createUserId) {
      this.ids = ids
      if (this.ids.length > 0) {
        this.$http({
          url: this.$http.adornUrl('/sys/user/select'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.userList = data.userList
            this.visible = true
            // 1 为超级管理员id
            if (this.ids.length === 1 && createUserId !== 1) {
              this.dataForm.userId = createUserId
            }
          }
        })
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.modal_loading = true
          this.$http({
            url: this.$http.adornUrl('/lampPole/card/deploy'),
            method: 'post',
            data: this.$http.adornData({
              'cardIds': this.ids,
              'userId': this.dataForm.userId
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.modal_loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.modal_loading = false
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.dataForm = { userId: '' }
          this.$nextTick(() => {
            this.$refs['dataForm'].resetFields()
          })
        }, 200)
      }
    }
  }
}
</script>
