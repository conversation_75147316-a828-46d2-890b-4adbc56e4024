<template>
  <Modal v-model="visible" width="900">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{$t('lamp.gpsUploadState')}}</span>
    </p>
    <div style="height: 300px;">
      <Form style="height: 70px;" label-position="left" label-colon>
        <FormItem>
          <Button style="margin-left:20px" :loading="open_loading"  type="primary" @click="dataFormSubmit(1)">{{$t('sys.open')}}</Button>
          <Button style="margin-left:20px" :loading="close_loading"  type="primary" @click="dataFormSubmit(0)">{{$t('sys.close')}}</Button>
        </FormItem>
<!--        <div v-if="resultData.length > 0" style="height: 495px;overflow-y: auto">-->
<!--          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>-->
<!--        </div>-->
      </Form>
    </div>
    <div slot="footer" style="text-align: left;">
      <span>
          <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
      </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data () {
    return {
      visible: false,
      ids: [],
      open_loading: false,
      close_loading: false,

    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.open_loading=false
        this.close_loading=false
      }
    },
    // 查詢gps
    dataFormSubmit (state) {
      if (this.ids.length > 0) {
        if (state==1){
          this.open_loading=true
        }else {
          this.close_loading=true
        }
        this.resultData = []
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/gpsUploadState'),
          method: 'post',
          data: this.$http.adornData({'deviceIds': this.ids, 'state': state},false)
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.$Message.success("success")
          } else {
            this.$Message.error(data.msg)
          }
          if (state==1){
            this.open_loading=false
          }else {
            this.close_loading=false
          }
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.open_loading=false
        this.close_loading=false
      }
    }
  }
}
</script>

