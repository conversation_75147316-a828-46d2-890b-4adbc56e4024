<template>
  <Modal v-model="visible" width="900">
    <p slot="header" style="text-align:center;font-size: 18px;">
      <span>{{ $t('operation.lightingControl') }}</span>
    </p>
    <Form label-position="left" label-colon>
      <FormItem >
        <Row>
          <Col span="3"><span style="line-height: 35px;font-size: 16px">{{$t('operation.LightingSwitch')}}</span></Col>
          <Col span="21">
            <Button type="success" :loading="on_loading" @click="screenSwitchSubmit(true)">{{$t('program.open')}}</Button>
            <Button type="warning" :loading="off_loading" @click="screenSwitchSubmit(false)">{{$t('program.close')}}</Button>
          </Col>
        </Row>
        <Row style="margin-top: 10px">
          <Col span="3"><span style="font-size:16px;line-height: 35px">{{$t('operation.LightingLevel')}}:</span></Col>
          <Col span="10"><SliderDrag  style="margin-left: 0px" :value="data.value" @SetOpacityConfig="SetLightingConfig" :key="reloadBrightness"></SliderDrag></Col>
          <Col span="11"><Button  :loading="modal_loading"  type="primary" @click="screenBrightness()">{{$t('common.set')}}</Button></Col>
        </Row>
      </FormItem>
      <FormItem v-if="resultData.length > 0" style="height: 235px;overflow-y: auto">
        <div v-for="(item, index) in resultData" :key="index+item.deviceId">
          <FormItem label="ID">
            <div v-if="item.deviceId">{{item.deviceId}}</div>
            <div v-else-if="item.cardId">{{item.cardId}}</div>
          </FormItem>
          <FormItem :loading="true">
            <div v-if="item.success === true">
              <div style="color: green;">{{$t('setTime.setupSuccess')}}</div>
            </div>
            <!-- <div v-if="item._type === undefined">
              <div style="color: red;">{{item}}</div>
            </div> -->
            <div v-else-if="item._type !== 'success'">
              <div style="color: red;" v-if="item.msg">{{item.msg}}</div>
              <div style="color: red;" v-else-if="item.errorMessage">{{item.errorMessage}}</div>
            </div>
          </FormItem>
          <Divider/>
        </div>
      </FormItem>
    </Form>

    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardNameSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>


<script>
import SliderDrag from '@/utils/SliderDrag'

export default {
  data() {
    return {
      visible: false,
      ids: [],
      names: [],
      data: {
        value: 1
      },
      on_loading: false,
      off_loading: false,
      resultData: [],
      modal_loading: false,
      queryLightingBrightnessLoading: false,
      reloadBrightness: 0,
    }
  },
  components: {
    SliderDrag
  },
  methods: {
    // 初始化
    init(ids, names) {
      this.visible = true
      this.ids = ids
      this.names = names
    },
    // 照明开关
    screenSwitchSubmit(status) {
      this.clearData()
      if (this.ids.length > 0) {
        if (status === true) {
          this.on_loading = true
        } else {
          this.off_loading = true
        }
        var idsLength = this.ids.length
        this.ids.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/lighting/set/lightingSwitch'),
            method: 'post',
            data: this.$http.adornData({'id': item, 'state': status}, false)
          }).then(({data}) => {
            if (data) {
              this.resultData.push(data.msg)
            }
            idsLength--
            if (idsLength === 0) {
              if (status === true) {
                this.on_loading = false
              } else {
                this.off_loading = false
              }
            }
          })
        })
      }
    },
    // 设置照明亮度
    screenBrightness () {
      this.clearData()
      if (this.ids.length > 0) {
        this.modal_loading = true
          this.$http({
            url: this.$http.adornUrl('/lighting/set/lightingValue'),
            method: 'post',
            data: this.$http.adornData({
              'ids': this.ids,
              'value': this.data.value
            })
          }).then(({data}) => {
            if (data&&data.code===0) {
              this.resultData=data.data
            }else {
              this.$Message.error(data.msg)
            }
            this.modal_loading = false
          })
      }
    },
    SetLightingConfig(val){
      this.data.value = val
    },
    clearData () {
      this.resultData = []

    },
  },

  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.names = []
        this.resultData = []
        this.aips4Address = ''
        this.cardAddress = ''
        this.volume = 5
        this.single = false
        this.off_loading=false
        this.on_loading=false
        this.modal_loading=false
        this.queryLightingBrightnessLoading=false
        this.data.brightness = 1
        this.reloadBrightness += 1
      }
    }
  }
}
</script>
<style scoped>

</style>
