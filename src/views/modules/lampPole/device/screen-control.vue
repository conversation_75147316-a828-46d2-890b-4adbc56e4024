<template>
  <div>
    <Modal v-model="visible" width="900">
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{$t('operation.screenControl')}}</span>
      </p>
      <div style="height: 400px">
        <Row>
            <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('setTime.screenSwitch')}}</div></Col>
            <Col span="21">
              <Button type="success" :loading="on_loading" @click="screenSwitchSubmit(true)">{{$t('program.open')}}</Button>
              <Button type="warning" :loading="off_loading" @click="screenSwitchSubmit(false)">{{$t('program.close')}}</Button>
            </Col>
        </Row>
        <Row style="margin-top: 10px">
          <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('cardDevice.brightness')}}</div></Col>
          <Col span="10">
            <SliderDrag style="margin-left: 0px" :value="brightness" @SetOpacityConfig="setOpacityConfig" :key="reloadMe"></SliderDrag>
          </Col>
          <Col span="11">
            <Button  :loading="brightness_loading"  type="primary" @click="screenBrightness()">{{$t('common.set')}}</Button>
          </Col>
        </Row>
        <Row style="margin-top: 10px">
          <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('cardDevice.volume')}}</div></Col>
          <Col span="10">
            <InputNumber :max="15" :min="1" v-model="volume"></InputNumber>
          </Col>
          <Col span="11">
            <Button  :loading="volume_loading"  type="primary" @click="screenVolume()">{{$t('common.set')}}</Button>
          </Col>
        </Row>
      </div>
      <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardNameSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import SliderDrag from '@/utils/SliderDrag'
export default {
  data () {
    return {
      visible: false,
      ids: [],
      names: [],
      screesSwitch: false,
      on_loading: false,
      off_loading: false,
      brightness: 1,
      brightness_loading: false,
      volume: 10,
      volume_loading: false,
      reloadMe: 0
    }
  },
  components: {
    SliderDrag
  },
  methods: {
    // 初始化
    init (ids, names) {
      this.visible = true
      this.ids = ids
      this.names = names
    },
    // 开关屏
    screenSwitchSubmit (status) {
      if (this.ids.length > 0) {
        if (status === true) {
          this.on_loading = true
        } else {
          this.off_loading = true
        }
        this.$http({
          url: this.$http.adornUrl('/card/set/screenSwitch'),
          method: 'post',
          data: this.$http.adornData({'ids': this.ids, 'status': status}, false)
        }).then(({data}) => {
          if (data && data.code !== 0) {
            this.$Message.error(data.msg);
          }
          if (status === true) {
            this.on_loading = false
          } else {
            this.off_loading = false
          }
        })
      }
    },
    // 设置亮度
    setOpacityConfig(val){
       this.brightness = val
    },
    screenBrightness () {
      if (this.ids.length > 0) {
        this.brightness_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/screenBrightness'),
          method: 'post',
          data: this.$http.adornData({
            'ids': this.ids,
            'brightness': this.brightness
          })
        }).then(({data}) => {
          if (data && data.code !== 0) {
            this.$Message.error(data.msg);
          }
          this.brightness_loading = false
        })
      }
    },
    // 设置音量
    screenVolume() {
      if (this.ids.length > 0) {
        this.volume_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/volume'),
          method: 'post',
          data: this.$http.adornData({
            'ids': this.ids,
            'volume': this.volume
          },false)
        }).then(({data}) => {
          if (data && data.code !== 0) {
            this.$Message.error(data.msg);
          }
          this.volume_loading = false
        })
      }
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.names = []
        this.on_loading = false
        this.off_loading = false
        this.brightness_loading = false
        this.brightness = 1
        this.volume_loading = false
        this.volume = 10
        this.reloadMe += 1
      }
    }
  }
}
</script>
<style scoped>
.schedules {
  margin-top: -25px;
}
.schedules ul {
  list-style: none;
  float: left;
}
</style>
