<template>
  <Modal v-model="visible" width="1000" style="height: 500px">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{ $t('operation.broadcastControl') }}</span>
    </p>

    <div style="height: 400px">
      <Form style="height: 300px;" label-position="left" label-colon>
        <FormItem>
          <Input v-model="address" :placeholder="$t('common.PleaseInput') + $t('operation.SIPServerAddress')" style="width: 300px" />
          <span>{{$t('broadcast.multicastAddress')}}</span>
          <Input-number :value="224" :disabled="true"></Input-number>
          <Input-number :value="0"  :disabled="true"></Input-number>
          <Input-number :value="1"  :disabled="true"></Input-number>
          <Input-number :max="255" :min="3" v-model="multicastAddress"></Input-number>
          <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
          <Button style="margin-left:10px" :loading="get_loading"  type="warning" @click="getSIPServerAddress()">{{$t('common.query')}}</Button>
        </FormItem>
        <FormItem v-if="resultData.length > 0">
          <div v-for="(item, index) in resultData" :key="index">

            <FormItem label="ID">
              <div>{{item.deviceId}}</div>
            </FormItem>
            <FormItem :loading="true">
              <div v-if="item._type === undefined">
                <div style="color: red;">{{item}}</div>
              </div>
              <div v-else-if="item._type !== 'success'">
                <div style="color: red;">{{item.msg}}</div>
              </div>
              <div v-else>
                <span>{{ item.message }}</span>

<!--                <div>-->
<!--                  <div v-if="item.sipAddress"><span>{{$t('operation.SIPServerAddress')}}： </span>{{item.sipAddress}}</div>-->
<!--                  <div v-else-if="item.sipAddress === ''"><span>{{$t('operation.SIPServerAddress')}}： </span>{{$t('common.notSet')}}</div>-->
<!--                  <div v-else>{{$t('setTime.setupSuccess')}}</div>-->
<!--                </div>-->
<!--                <div>-->
<!--                  <div v-if="item.multicastAddress"><span>{{$t('broadcast.multicastAddress')}}： </span>{{item.multicastAddress}}</div>-->
<!--                  <div v-else-if="item.multicastAddress === ''"><span>{{$t('broadcast.multicastAddress')}}： </span>{{$t('common.notSet')}}</div>-->
<!--                  <div v-else>{{$t('setTime.setupSuccess')}}</div>-->
<!--                </div>-->
<!--                <div>-->
<!--                  <div v-if="item.radioMAC"><span>{{$t('operation.AlarmEquipmentMacAddress')}}： </span>{{item.radioMAC}}</div>-->
<!--                  <div v-else-if="item.radioMAC === ''"><span>{{$t('operation.AlarmEquipmentMacAddress')}}： </span>{{$t('common.notSet')}}</div>-->
<!--                  <div v-else>{{$t('setTime.setupSuccess')}}</div>-->
<!--                </div>-->
<!--                <div>-->
<!--                  <div v-if="item.macIp"><span>{{$t('operation.AlarmEquipmentIpAddress')}}： </span>{{item.macIp}}</div>-->
<!--                  <div v-else-if="item.macIp === ''"><span>{{$t('operation.AlarmEquipmentIpAddress')}}： </span>{{$t('common.notSet')}}</div>-->
<!--                  <div v-else>{{$t('setTime.setupSuccess')}}</div>-->
<!--                </div>-->
              </div>
            </FormItem>
            <Divider/>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardNameSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>


<script>
import SliderDrag from '@/utils/SliderDrag'

export default {
  data() {
    return {
      visible: false,
      ids: [],
      names: [],
      modal_loading: false,
      resultData: [],

      address: '',
      get_loading: false,
      /**
       * 组播地址最后一位
       */
      multicastAddress:null,
      /**
       * 设置组播等待
       */
      setMulticastIpLoading:false,
      /**
       * 查询组播等待
       */
      getMulticastIpLoading:false,

    }
  },
  components: {
    SliderDrag
  },
  methods: {
    // 初始化
    init(ids, names) {
      this.visible = true
      this.ids = ids
      this.names = names
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.$Modal.confirm({
          title: this.$t('common.tips'),
          content: this.address != '' ?
            this.$t('broadcast.customSIPAddressStart') + this.address + this.$t('broadcast.customSIPAddressEnd') : this.$t('broadcast.SIPAddress'),
          okText: this.$t('common.confirm'),
          cancelText: this.$t('common.cancel'),
          onOk: () => {
            this.modal_loading = true
            this.resultData = []
            var idsLength = this.ids.length
            var multiIp=""
            if (this.multicastAddress===""||this.multicastAddress===null){
              multiIp=""
            }else{
              multiIp="224.0.1."+this.multicastAddress;
            }
            var param= {'cardIds':this.ids,'multicastAddress':multiIp, 'address': this.address}
              this.$http({
                url: this.$http.adornUrl('/broadcast/set/setSipAddress'),
                method: 'post',
                data: param
              }).then(({data}) => {
                if (data&& data.code===0) {
                  this.resultData=data.data
                }else{
                  this.$Message.error(data.msg)
                }
                  this.modal_loading = false
              })
          }
        })
      }
    },

    getSIPServerAddress () {
      if (this.ids.length > 0) {
        this.get_loading = true
        this.resultData = []
          var ids=this.ids.join(",")
          this.$http({
            url: this.$http.adornUrl('/broadcast/get/getSipAddress'),
            method: 'get',
            params: this.$http.adornParams({'cardIds':ids})
          }).then(({data}) => {
            if (data&&data.code===0) {
              this.resultData=data.data
            }else {
              this.$Message.error(data.msg)
            }
              this.get_loading = false
          })
      }
    },
    clearLoading(){
      this.modal_loading=false
      this.get_loading=false
    }

  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
          this.ids = []
          this.names = []
          this.resultData = []
          this.clearLoading()
      }
    }
  }
}
</script>
<style scoped>

</style>
