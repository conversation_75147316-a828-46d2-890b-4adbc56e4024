<template>
  <Modal v-model="visible" width="900">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{ $t('program.EnvironmentalMonitoring') }}</span>
    </p>

    <div style="height: 400px">

    </div>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardNameSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>


<script>
import SliderDrag from '@/utils/SliderDrag'

export default {
  data() {
    return {
      visible: false,
      ids: [],
      names: [],
      modal_loading: false,
      volumeLoading:false,
      resultData: [],
      aips4Address: '',
      cardAddress: '',
      single: false,
      volume:5
    }
  },
  components: {
    SliderDrag
  },
  methods: {
    // 初始化
    init(ids, names) {
      this.visible = true
      this.ids = ids
      this.names = names
    },

  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.names = []
        this.resultData = []
        this.aips4Address = ''
        this.cardAddress = ''
        this.volume = 5
        this.single = false
        this.modal_loading=false
        this.volumeLoading=false
      }
    }
  }
}
</script>
<style scoped>

</style>
