<template>
    <Modal v-model="visible" height="400" width="500">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.group')}}</span>
        </p>
<!--        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 200px;" :label-width="80" label-position="left" label-colon-->
<!--            @keyup.enter.native="dataFormSubmit()">-->
<!--            <FormItem prop="group" :label="$t('group.name')">-->
<!--                <Select style="width:300px;" v-model="dataForm.group" filterable clearable transfer :placeholder="$t('common.PleaseSelect') + $t('group.name')">-->
<!--                    <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{item.cardCount}})</Option>-->
<!--                </Select>-->
<!--                <Button type="primary" style="float:right;" :loading="modal_loading" @click="dataFormSubmit()">{{$t('common.set')}}</Button>-->
<!--            </FormItem>-->
<!--        </Form>-->
      <div style="height: 300px">
        <Tree :data="groupList" :render="renderContent"  ref="groupListTree"></Tree>
        <div>
          <Button type="primary" style="float:right;" :loading="modal_loading" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
        </div>
      </div>

        <div slot="footer" style="text-align: left;">
            <span>
                <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in data.ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
        visible: false,
        data: {
            ids: [],
        },
        groupList: [],
        dataForm: {
            group: ''
        },
        modal_loading: false,
        totalNum:0,
    }
  },
  // computed: {
  //   dataRule: {
  //       get () {
  //           return {
  //               group: [
  //                   { validator: (rule, value, callback) => {
  //                       if (value === '') {
  //                           callback(new Error(this.$t('common.PleaseSelect') + this.$t('group.name')))
  //                       } else {
  //                           callback()
  //                       }
  //                   }, trigger: 'change' }
  //               ]
  //           }
  //       }
  //   },
  // },
  methods: {
    // 初始化
    init (ids,totalNum) {
      if (ids) {
        this.visible = true
        this.data.ids = ids
        this.totalNum=totalNum
        this.getGroupList()
      }
    },
    getGroupList(){
      this.$http({
        url: this.$http.adornUrl('/sys/group/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.groupList = data.group
          this.getUnGroupDevice()
        } else {
          this.groupList = []
        }
      })
    },
    // 提交数据
    dataFormSubmit () {
      var rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      if (rootNode) {
              this.dataForm.group=rootNode.id;
                this.modal_loading = true
                    this.$http({
                    url: this.$http.adornUrl(`/lampPole/card/updateGroup`),
                    method: 'post',
                    data: this.$http.adornData({
                        'cardIds': this.data.ids,
                        'group': this.dataForm.group
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.modal_loading = false
                        this.$Message.success({
                            content: this.$t('common.operationSuccessful'),
                            duration: 0.5,
                            onClose: () => {
                                this.visible = false
                                this.$emit('refreshDataList')
                            }
                        })
                      this.getGroupList()
                    } else {
                        this.$Message.error(data.msg)
                        setTimeout(() => {
                            this.modal_loading = false
                        }, 500)
                    }
                })
            }else {
        this.$Message.error("请选择一个分组")
      }
    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
            this.dataForm = {
                group: ''
            }
            // this.$nextTick(() => {
            //     this.$refs['dataForm'].resetFields()
            // })
        }, 200)
      }
    }
  }
}
</script>
