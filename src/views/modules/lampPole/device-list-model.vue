<template>
  <div class="modiles-device">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
<!--      <FormItem>-->
<!--        <Select size="large" v-model="dataForm.group" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('group.name')" @on-change = "getDataList">-->
<!--          <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{item.cardCount}})</Option>-->
<!--        </Select>-->
<!--      </FormItem>-->

      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(null,1)"  size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button style="margin-right:6px" size="large" @click="changeGroup()">
          <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
        </Button>
        <Button style="margin-right:6px" @click="getTerminalInfo()" :loading="terminalInfoLoading"  type="primary" size="large" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{$t('cardDevice.queryTerminalInfo')}}</div>
        </Button>
<!--        <Button @click="deployHandle()" style="margin-right:6px"  type="warning" size="large" v-if="isAuth('screen:card:deploy')" :disabled="dataListSelections.length <= 0">-->
<!--          <div style="margin:3px 8px">{{$t('common.deploy')}}</div>-->
<!--        </Button>-->
        <Button @click="groupHandle()" style="margin-right:6px"  type="primary" size="large" v-if="isAuth('device:group')" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{$t('operation.group')}}</div>
        </Button>
        <Button v-if="isAuth('lampPole:device:delete')" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
        </Button>
      </FormItem>
      <FormItem style="float: right">
        <Tooltip :content="$t('lamp.mapMode')" placement="bottom-start">
            <svg width="40px" height="40px" aria-hidden="true" @click="mapHandle"
            style="vertical-align:middle;cursor: pointer;">
              <use xlink:href="#map"></use>
            </svg>
        </Tooltip>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
      :loading="dataListLoading" :height="tableHeightData" ref="selection">
      <template slot-scope="{ row, index }" slot="number">
        {{index+1}}
      </template>
      <template slot-scope="{ row, index }" slot="deviceId">
        {{row.deviceId}}
        <span v-if="row.msg === 1">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#send"></use>
          </svg>
        </span>
        <span v-if="row.msg === 2">
          <Poptip placement="right-start" v-if="row.text && row.text !== ''"
          trigger="hover" transfer :title="$t('common.tips')" :content="tipChange(row.text)">
            <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
              <use xlink:href="#fail"></use>
            </svg>
          </Poptip>
          <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#fail"></use>
          </svg>
        </span>
        <span v-if="row.msg === 3">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#success"></use>
          </svg>
        </span>
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div v-if="row.isOn === 1 ">
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#on-line"></use>
            </svg>
         </div>
         <div v-else>
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#line"></use>
            </svg>
         </div>
      </template>
      <template slot-scope="{ row, index }" slot="radioState">
        {{row.radioState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>
      <template slot-scope="{ row, index }" slot="environmentState">
        {{row.environmentState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>
      <template slot-scope="{ row, index }" slot="lightState">
        {{row.lightState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>

      <template slot-scope="{ row, index }" slot="electricityState">
        {{row.electricityState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>

      <template slot-scope="{ row, index }" slot="monitorState">
        {{row.monitorState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>
      <template slot-scope="{ row, index }" slot="trafficState">
        {{row.trafficState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>
      <template slot-scope="{ row, index }" slot="gpsUploadState">
        {{row.gpsUploadState === 0 ? $t('common.NotEnabled'): $t('common.enable')}}
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <!-- <Button v-if="isAuth('screen:card:deploy')" :disabled="row._disabled" type="warning" size="small"
        style="margin-right: 5px;font-size: 11px" @click="deployHandle(row.deviceId, row.createUserId)">{{$t('common.deploy')}}</Button>
        <Button v-if="isAuth('device:group')" :disabled="row._disabled" type="primary" size="small"
        style="margin-right: 5px;font-size: 11px" @click="groupHandle(row.deviceId, row.createUserId)">{{$t('operation.group')}}</Button> -->
        <Button v-if="isAuth('lampPole:device:update')" :disabled="row._disabled" type="primary"
        size="small" style="margin-right: 5px;font-size: 11px" @click="updateHandle(row.deviceId, row.createUserId)">{{$t('common.update')}}</Button>
        <Button v-if="isAuth('screen:card:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.deviceId)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

    <!--分组弹出框-->
    <Modal v-model="selectGroupVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{$t('common.selectGroup')}}</span>
      </p>
      <Alert type="info" show-icon >
        <span>{{this.$t('tips.groupTip')}}</span>
      </Alert>
      <div>
        <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
      </div>
      <div slot="footer">
        <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>

    <!-- 功能菜单-->
    <div style="height: 20px; background:#eee; clear: both;">
      <svg v-if="!isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuixiaohua"></use>
      </svg>
      <svg v-if="isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuidahua"></use>
      </svg>
    </div>
    <div v-if="!isMinimize" class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in operation" :key="index">
          <li v-if="isAuth(item.auth)||!item.auth" >
            <div  class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id ,item.checked)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.text)}}</div>
            </div>
            <div class="opera_list" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.text)}}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div v-if="isMinimize" style="height: 50px;" class="opera_div">
      <ul class="opera_ul1">
        <div v-for="(item, index) in operation" :key="index" :title="$t(item.text)">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list1" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
            <div class="opera_list1" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
          </li>
        </div>
      </ul>
    </div>

    <!-- 分配-->
    <device-deploy v-if="deployVisible" ref="deviceDeploy" @refreshDataList="getDataList()"></device-deploy>
    <!-- 分组 -->
    <device-group v-if="groupVisible" ref="deviceGroup" @refreshDataList="getDataList"></device-group>
    <!-- 修改-->
    <device-update v-if="updateVisible" ref="deviceUpdate" @refreshDataList="getDataList"></device-update>
    <!-- 屏幕控制-->
    <screen-control v-if="screenControl" ref="screenControl"></screen-control>
    <!-- 广播控制-->
    <broadcast-control v-if="broadcastControl" ref="broadcastControl"></broadcast-control>
    <!--环境检测-->
    <environment  v-if="environment" ref="environment"></environment>
    <!--照明控制-->
    <lighting-control v-if="lightingControl" ref="lightingControl"></lighting-control>

    <gps-upload v-if="gpsUploadState" ref="gpsUploadState"></gps-upload>
  </div>
</template>

<script>
import deviceDeploy from './device-deploy'
import deviceGroup from './device-group'
import deviceUpdate from './device-update.vue'
import ScreenControl from './device/screen-control.vue'
import broadcastControl from "./device/broadcast-control"
import environment from "./device/environment";
import lightingControl from "./device/lighting-control"
import GpsUpload from "./device/gps-upload.vue";
export default {
    data () {
        return {
          dataForm: {
              key: '',
              group: []
          },
          dataConlums: [
              {type: 'selection', width: 60,fixed: 'left', align: 'center'},
              {title: this.$t('cardDevice.number'), width: 70,fixed: 'left', align: 'center',slot: 'number', tooltip: true,
                renderHeader:(h)=>{
                  return h('div',this.$t('cardDevice.number'))
                }
              },
              {title: this.$t('lamp.poleName'), fixed: 'left',key: 'alias', width: 130, align: 'center', tooltip: true,
                renderHeader:(h)=>{
                  return h('div',this.$t('lamp.poleName'))
                }
              },
              {title: 'ID', key: 'deviceId',fixed: 'left', width: 170, align: 'center', slot: 'deviceId',},
              {title: this.$t('cardDevice.online'), key: 'isOn', width: 100, slot: 'online', align: 'center',
                renderHeader:(h)=>{
                  return h('div',this.$t('cardDevice.online'))
                }
              },
              {title: this.$t('operation.group'), key: 'groupName', align: 'center', width: 100, tooltip: true,
                renderHeader:(h)=>{
                  return h('div',this.$t('operation.group'))
                }
              },
              {title: this.$t('lamp.broadcast'), key: 'radioState', align: 'center', width: 150, tooltip: true, slot: 'radioState',
                renderHeader:(h)=>{
                  return h('div',this.$t('lamp.broadcast'))
                }
              },
              {title: this.$t('lamp.monitor'), key: 'monitorState', align: 'center', width: 150, tooltip: true, slot: 'monitorState',
                renderHeader:(h)=>{
                  return h('div',this.$t('lamp.monitor'))
                }
              },
              {title: this.$t('lamp.environment'), key: 'environmentState', align: 'center', width: 230, tooltip: true, slot: 'environmentState',
                renderHeader:(h)=>{
                  return h('div',this.$t('lamp.environment'))
                }
              },
              {title: this.$t('lamp.lighting'), key: 'lightState', align: 'center', width: 150, tooltip: true, slot: 'lightState',
                renderHeader:(h)=>{
                  return h('div',this.$t('lamp.lighting'))
                }
              },
              {title: this.$t('nav.电能管理'), key: 'electricityState', align: 'center', width: 150, tooltip: true, slot: 'electricityState',
                renderHeader:(h)=>{
                  return h('div',this.$t('nav.电能管理'))
                }
              },
              {title: this.$t('lamp.Passenger'), key: 'trafficState', align: 'center', width: 210, tooltip: true, slot: 'trafficState',
                renderHeader:(h)=>{
                  return h('div',this.$t('lamp.Passenger'))
                }
              },
            {title: this.$t('lamp.gpsUploadState'), key: 'gpsUploadState', align: 'center', width: 210, tooltip: true, slot: 'gpsUploadState',
              renderHeader:(h)=>{
                return h('div',this.$t('lamp.gpsUploadState'))
              }
            },
              {title: "IP", key: 'realIp', width: 170, align: 'center',
                renderHeader:(h)=>{
                  return h('div','IP')
                }
              },
              {title: this.$t('cardDevice.lastOffline'), key: 'lastOffTime', width: 170, align: 'center',
                renderHeader:(h)=>{
                  return h('div',this.$t('cardDevice.lastOffline'))
                }
              },
              {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', width: 230, align: 'center',
                renderHeader:(h)=>{
                  return h('div',this.$t('common.operation'))
                }
              }
          ],
          dataList: [],
          pageIndex: 1,
          pageSize: 10,
          totalPage: 0,
          dataListLoading: false,
          dataListSelections: [],
          groupList: [],
          deployVisible: false,
          groupVisible: false,
          updateVisible: false,
          device: {},
          operation: [
            {id: 'screenControl', icon: 'pingmu', text: 'operation.screenControl', disable: false, checked: true},
            {id: 'broadcastControl', icon: 'broadcast', text: 'operation.broadcastControl', disable: false,auth: 'lampPole:broadcastControl', checked: true},
            {id: 'monitoringControl', icon: 'monitoring', text: 'operation.monitoringControl', disable: false,auth: 'lampPole:monitoringControl', checked: true},
            // {id: 'environment', icon: 'meteorologicalEnvironment', text: 'operation.meteorologicalEnvironmentControl', disable: true,auth: 'lampPole:meteorologicalEnvironment', checked: true},
            {id: 'passengerFlow', icon: 'passengerFlow', text: 'operation.passengerFlowStatistics', disable: false,auth: 'lampPole:passengerFlowStatistics', checked: true},
            {id: 'lightingControl', icon: 'lighting', text: 'operation.lightingControl', disable: false,auth: 'lampPole:lighting', checked: true},
            {id: 'gpsUploadState', icon: 'gpsUpload', text: 'lamp.gpsUploadState', disable: false,auth: 'device:gpsUploadState', checked: true},
            // {id: 'electricity', icon: 'electricity', text: 'nav.电能管理', disable: true,auth: 'lampPole:po werManagement', checked: true},
          ],
          screenControl: false,
          terminalInfoLoading: false,
          broadcastControl:false,
          environment:false,
          lightingControl:false,
          //选择分组时，分组框是否可见
          selectGroupVisible:false,
          //  分组名
          groupName:"",
          rootNode:null,
          //是否第一次打开该页面
          isFirst:true,
          //总数量
          totalNum:0,
          tableHeightData: 0,
          isMinimize: false,
          gpsUploadState:false,
        }
    },
    components: {
      GpsUpload,
      deviceDeploy,
      deviceGroup,
      deviceUpdate,
      ScreenControl,
      broadcastControl,
      environment,
      lightingControl

    },
    methods: {
      // 初始化部分数据
      initData () {
          this.dataForm = {
              key: '',
              group: []
          }
          this.isFirst=true
          //总数量
          this.totalNum=0
          this.dataList = []
          this.pageIndex = 1
          this.pageSize = 10
          this.totalPage = 0
          this.dataListLoading = false
          this.dataListSelections = []
          this.getDataList('loading',null)
          this.groupName=""
          this.rootNode=null
          this.isFirst=true
          //总数量
          this.totalNum=0,
          this.tableHeightData = this.tableHeight
      },
      // 查询终端信息
      getTerminalInfo () {
        // 判断选中的列表不为空
        if (this.dataListSelections.length > 0) {
          var deviceIds = this.dataListSelections.map(item => {
            return item.deviceId
          })
          this.dataList = this.dataList.filter(data => {
              if (deviceIds.indexOf(data.deviceId) !== -1) {
                data.msg =  1
                data._checked = true
              } else {
                data._checked = false
              }
              return data
          })
          this.terminalInfoLoading = true
          this.$http({
            url: this.$http.adornUrl('/card/query/getCardInformation'),
            method: 'post',
            data: deviceIds
          }).then(({ data }) => {
            if (data && data.cards) {
              for (let i = 0; i < data.cards.length; i++) {
                const element = data.cards[i];
                if (element.card.text) {
                  element.card.msg = 2
                } else {
                  element.card.msg = 3
                }
                element.card._checked = true
                this.dataList.forEach((item, index) => {
                  if (element.card.deviceId.indexOf(item.deviceId) !== -1) {
                    this.dataList.splice(index, 1, element.card)
                  } else {
                    item = item
                  }
                })
              }
            } else {
              this.$Message.error(data.msg)
            }
            this.terminalInfoLoading = false
          });
        }
      },
      // 查询分组列表
      getGroupList () {
        this.$http({
            url: this.$http.adornUrl('/sys/group/select'),
            method: 'get',
            params: this.$http.adornParams()
        }).then(({data}) => {
          // console.log(data);
            if (data && data.code === 0) {
              this.groupList = data.group
              this.getUnGroupDevice()
            } else {
            this.groupList = []
            }
        })
      },
      // 获取数据列表
      getDataList (loading,isQuery) {
        if (loading) {
            this.dataListLoading = true
        }
        if (isQuery===1){
          this.pageIndex=1
        }
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/list'),
          method: 'post',
          data: this.$http.adornData({
            'page': this.pageIndex+"",
            'limit': this.pageSize+"",
            'key': this.dataForm.key,
            'group': this.dataForm.group
            })
        }).then(({data}) => {
            if (data && data.code === 0) {
                this.dataList = data.page.list
                this.totalPage = data.page.totalCount
              if (this.dataForm.group.length===0){
                this.groupName=""
              }
              if (this.isFirst){
                this.totalNum=data.page.totalCount
                this.isFirst=false
              }
              //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
              if (data.page.currPage>this.totalPage&&this.totalPage!==0){
                this.pageIndex=1
                this.getDataList(loading,isQuery)
              }
              // this.dataForm.group=[]
                // 设置选中
                if (this.$refs.selection) {
                  var select = this.$refs.selection.getSelection().map(item => {return item.deviceId})
                  if (select && select.length !== 0) {
                      this.dataList.map(item => {
                      if (select.indexOf(item.deviceId) != -1) {
                          item._checked = true
                      } else {
                          item._checked = false
                      }
                      })
                  }
                }
            } else {
                this.dataList = []
                this.totalPage = 0
            }
            this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.$refs.selection.selectAll(false)
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle () {
        this.dataListSelections = this.$refs.selection.getSelection()
      },
      selectThisRow(data, index) {
        this.$refs.selection.toggleSelect(index);
        this.checkCards()
      },
      updateHandle (deviceId, createUserId) {
        this.updateVisible = true // 修改
        this.$nextTick(() => {
          this.$refs.deviceUpdate.init(deviceId, createUserId,this.totalNum)
        })
      },
      // 删除
      deleteHandle (id) {
        var deviceIds = id ? [id] : this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.$Modal.confirm({
          title: this.$t('common.tips'),
          content: this.$t('common.delete_current_option'),
          okText: this.$t('common.confirm'),
          cancelText: this.$t('common.cancel'),
          onOk: () => {
            this.$http({
              url: this.$http.adornUrl('/lampPole/card/delete'),
              method: 'post',
              data: this.$http.adornData(deviceIds, false)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                    if (this.pageIndex != 1 && this.dataList.length === deviceIds.length) {
                      this.pageIndex--
                    }
                    this.getDataList()
                    this.dataListSelections = []
                  }
                })
              } else {
                this.$Message.error(data.msg)
              }
            })
          }
        })
      },
      // 分配
      deployHandle (id, createUserId) {
        var deviceIds = id ? [id] : this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.deployVisible = true
        this.$nextTick(() => {
          this.$refs.deviceDeploy.init(deviceIds, createUserId)
        })
      },
      // 分组
      groupHandle (id) {
        var deviceIds = id ? [id] : this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.groupVisible = true
        this.$nextTick(() => {
          this.$refs.deviceGroup.init(deviceIds,this.totalNum)
        })
      },
      // 不支持
      operaErrorHandle (text) {
        this.$Message.warning({
          content: this.$t('common.supportedTip') + this.$t(text),
          duration: 2
        })
      },
      // 成功
      operaSuccessHandle (id, checked) {
        if (id) {
          if (checked === true) {
            // 获取已选的卡
            var deviceIds = null
            var deviceNames = null
            if (this.dataListSelections.length > 0) {
              deviceIds = this.dataListSelections.map(item => {
                return item.deviceId
              })
              deviceNames = this.dataListSelections.map(item => {
                return item.alias
              })
            } else {
              this.$Message.warning({
                content: this.$t('common.selectDevice'),
                duration: 2
              })
              return
            }
            if (deviceIds.length >= 1) {
              if (id === 'screenControl') { // 屏幕控制
                this.screenControl = true // 修改
                this.$nextTick(() => {
                  this.$refs.screenControl.init(deviceIds, deviceNames)
                })
              } else if (id === 'broadcastControl') { // 广播控制
                this.broadcastControl = true // 修改
                this.$nextTick(() => {
                  this.$refs.broadcastControl.init(deviceIds, deviceNames)
                })
              } else if (id === 'monitoringControl') { // 监控控制

              } else if (id === 'environment') { // 气象环境监控
                this.environment=true
                this.$nextTick(() => {
                  this.$refs.environment.init(deviceIds, deviceNames)
                })
              } else if (id === 'passengerFlow') { // 客流统计

              } else if (id === 'lightingControl') { // 照明控制
                this.lightingControl=true
                this.$nextTick(() => {
                  this.$refs.lightingControl.init(deviceIds, deviceNames)
                })
              }else if(id==='electricity'){// 电能管理

              }else if (id === 'gpsUploadState') { // gps上报
                this.gpsUploadState=true
                this.$nextTick(() => {
                  this.$refs.gpsUploadState.init(deviceIds, deviceNames)
                })
              }
            }
          } else if(checked === false) {
            // 不需要选中卡

          }
        }
      },
      //检测卡是否可以使用某些功能
      checkCards() {
        if (this.dataListSelections.length > 0) {
           this.dataListSelections.forEach(item => {
              for (let i = 0; i < this.operation.length; i++) {
                const element = this.operation[i];
                if (element.id === 'broadcastControl') { // 智慧广播状态
                  element.disable = item.radioState <= 0
                } else if (element.id === 'monitoringControl') { // 监控状态
                  element.disable = item.monitorState <= 0
                } else if (element.id === 'environment') { // 环境检测状态
                  element.disable = item.environmentState <= 0
                } else if (element.id === 'passengerFlow') { // 客流统计状态
                  element.disable = item.trafficState <= 0
                } else if (element.id === 'lightingControl') {  // 智慧照明状态
                  element.disable = item.lightState <= 0
                } else if (element.id === 'electricity') { //电能管理状态
                  element.disable = item.electricityState <= 0
                }
              }
          })
        }
      },
      changeGroup(){
        this.getGroupList()
        this.selectGroupVisible=true
      },
      //tip国际化
      tipChange(tip){
        if (tip=="控制卡连接已断开"){
          return this.$t('monitor.offLineOrNotExist')
        }else {
          return this.$t('log.connectionClosed')
        }
      },
      // 表单提交
      dataFormSubmit () {
        this.dataForm.group=[]
        this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
        this.getChildrenNodes(this.rootNode)
        this.selectGroupVisible=false
        this.getDataList()
        this.groupName=this.rootNode.name
        this.rootNode=null
      },
      cancelSelect(){
        this.selectGroupVisible=false
        this.dataForm.group=[]
        this.groupName=""
        this.rootNode=null
        this.getDataList()
        this.getGroupList()

      },
      //获取该分组及其子分组的groupId
      getChildrenNodes(rootNode){
        this.dataForm.group.push(rootNode.id)
        var childNode=rootNode.children;
        if (childNode){
          for (var i=0; i<childNode.length; i++) {
            this.getChildrenNodes(childNode[i])
          }
        }
      },
      renderContent (h, { root, node, data }) {
        return h('span', {
          style: {
            display: 'inline-block',
            width: '100%'
          }
        }, [
          h('span', [
            h('span', data.name+"("+data.count+")")
          ])
        ]);
      },
      //获取未分组的设备
      getUnGroupDevice(){
        var groupedNum=0;
        this.unGroupNum=0;
        this.groupList.map(item=>{
          groupedNum+=item.count;
        })
        this.unGroupNum=this.totalNum-groupedNum;
        var unGroupObj={
          "id":-1,
          "name": this.$t('common.unclassified'),
          "count":this.unGroupNum,
          "children":[],
          "expand":true
        }
        this.groupList.push(unGroupObj)
      },

      // 切换地图模式
      mapHandle () {
        this.$emit('changeModel', 'map')
      },

      // 最大化最小化
      handleMinimize (isMinimize) {
        this.isMinimize = !isMinimize
        if (this.isMinimize == true) {
          this.tableHeightData = this.tableHeight + 130
        } else {
          this.tableHeightData = this.tableHeight
        }
      },
    },
    activated () {
      this.initData()
    },
    computed: {
      tableHeight: {
          get () { return this.$store.state.common.tableHeight - 155 }
      }
    },
  watch: {
    'totalNum': function (newVal, OldVal) {
      this.getGroupList();
    },
    'tableHeight': function(newVal, oldVal) {
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    }
  },
}
</script>

<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 180px;
  background:#eee;
  overflow: hidden;
  overflow-y: auto;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 100px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list {
  padding-top: 10px;
  width: 105px;
  height: 105px;
  margin-left: 8%;
}
.opera_ul li:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}
.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}
.opera_ul1 {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul1 li{
  text-align: center;
  float: left;
  list-style: none;
  width: 40px;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list1 {
  padding-top: 5px;
  width: 40px;
  height: 40px;
}
.opera_ul1 li:hover {
  background-color: rgb(210, 174, 245);
  cursor: pointer;
}
.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}
.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
</style>
