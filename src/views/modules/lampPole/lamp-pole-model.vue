<template>
    <div v-if="show" :class="model">
        <div class="title">
            <span style="text-shadow: 5px 5px 20px #000;">{{device != null && device.alias != null && device.device_id != null ? device.alias + '('+ device.device_id +')' : $t('common.set')}}</span>
            <Icon type="ios-close-circle-outline" style="float: right;margin-top: 10px; margin-right: 10px" :size="30" @click="close"/>
            <hr style="width: 670px;margin: 0 auto"/>
        </div>
        <div class="content">
            <ul class="opera_ul">
                <div v-for="(item, index) in operation" :key="index">
                    <li v-if="isAuth(item.auth)||!item.auth">
                        <div class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id)"
                            :style="item.id === selectOpera ? 'background-color: rgb(210, 174, 245);': ''">
                            <svg width="35px" style="vertical-align: middle;" height="35px" aria-hidden="true">
                                <use :xlink:href="'#' + item.icon"></use>
                            </svg>
                        </div>
                        <div class="opera_list" v-else @click="operaErrorHandle(item.text)"
                            :style="item.select === selectOpera ? 'background-color: rgb(210, 174, 245);cursor: not-allowed;': 'cursor: not-allowed;'">
                            <svg width="35px" style="vertical-align: middle;" height="35px" aria-hidden="true">
                                <use :xlink:href="'#' + item.icon"></use>
                            </svg>
                        </div>
                    </li>
                </div>
            </ul>

            <div class="opera">
                <Button style="margin-bottom: 10px" @click="getTerminalInfo()" :loading="terminalInfoLoading"  type="primary" size="large">
                    <div style="margin:3px 8px">{{$t('cardDevice.queryTerminalInfo')}}</div>
                </Button>
<!--              屏幕控制-->
                <div v-if="selectOpera === 'screenControl'">
                    <Row>
                        <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('setTime.screenSwitch')}}</div></Col>
                        <Col span="21">
                            <Button type="success" :loading="on_loading" @click="screenSwitchSubmit(true)">{{$t('program.open')}}</Button>
                            <Button type="warning" :loading="off_loading" @click="screenSwitchSubmit(false)">{{$t('program.close')}}</Button>
                        </Col>
                    </Row>
                    <Row style="margin-top: 10px">
                        <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('cardDevice.brightness')}}</div></Col>
                        <Col span="13">
                            <SliderDrag style="margin-left: 0px" :totalWidth="'230'" :value="brightness" @SetOpacityConfig="setOpacityConfig" :key="reloadMe"></SliderDrag>
                        </Col>
                        <Col span="8">
                            <Button :loading="brightness_loading"  type="primary" @click="screenBrightness()">{{$t('common.set')}}</Button>
                        </Col>
                    </Row>
                    <Row style="margin-top: 10px">
                        <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('cardDevice.volume')}}</div></Col>
                        <Col span="13">
                            <InputNumber :max="15" :min="1" v-model="volume"></InputNumber>
                        </Col>
                        <Col span="8">
                            <Button  :loading="volume_loading"  type="primary" @click="screenVolume()">{{$t('common.set')}}</Button>
                        </Col>
                    </Row>
                    <hr style="width: 670px;margin: 10px auto"/>
                    <div>
                        <Row style="height: 40px">
                            <Col span="6">
                                <span style="margin-right: 5px">{{$t('cardDevice.online')}}</span>
                                <div v-if="device.is_on === 1" style="display: inline-block">
                                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                                        <use xlink:href="#on-line"></use>
                                    </svg>
                                </div>
                                <div v-else style="display: inline-block">
                                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                                    <use xlink:href="#line"></use>
                                    </svg>
                                </div>
                            </Col>
                            <Col span="6">
                                <span style="margin-right: 5px">{{$t('cardDevice.screenStatus')}}</span>
                                <div v-if="device.screen_status === 'on' " style="display: inline-block">
                                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                                        <use xlink:href="#on"></use>
                                    </svg>
                                </div>
                                <div v-else style="display: inline-block">
                                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                                        <use xlink:href="#off"></use>
                                    </svg>
                                </div>
                            </Col>
                            <Col span="6">
                                <span style="margin-right: 5px">{{$t('cardDevice.networkType')}}</span>
                                <div v-if="device.net_type === 'WIFI'" style="display: inline-block">
                                    {{device.net_type}}
                                    <svg v-if="device.rssi >= -50 && device.rssi <= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#WIFI-level4"></use>
                                    </svg>
                                    <svg v-else-if="device.rssi >= -70 && device.rssi < -50" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#WIFI-level3"></use>
                                    </svg>
                                    <svg  v-else-if="device.rssi >= -80 && device.rssi < -70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#WIFI-level2"></use>
                                    </svg>
                                    <svg v-else-if="device.rssi >= -100 && device.rssi < -80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#WIFI-level1"></use>
                                    </svg>
                                </div>
                                <div style="display: inline-block" v-else-if="device.net_type === 'LTE' || device.net_type === 'UMTS' || device.net_type === 'HSPA'
                                || device.net_type === 'HSPA+' || device.net_type === 'EDGE'  || device.net_type === 'GPRS'">
                                    {{device.net_type}}
                                    <svg v-if="device.asu >= 12 && device.asu != 99" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#gsm-0"></use>
                                    </svg>
                                    <svg v-else-if="device.asu >= 8 && device.asu  < 12" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#gsm-1"></use>
                                    </svg>
                                    <svg  v-else-if="device.asu >= 5 && device.asu < 8" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#gsm-2"></use>
                                    </svg>
                                    <svg v-else-if="device.asu >= 3 && device.asu < 5" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                                        <use xlink:href="#gsm-3"></use>
                                    </svg>
                                </div>
                                <div v-else style="display: inline-block">
                                    {{device.net_type}}
                                </div>
                            </Col>
                            <Col span="6">
                                <span style="margin-right: 5px">{{$t('cardDevice.resolvingPower')}}</span>
                                <div style="display: inline-block" v-if="device.width && device.height">{{device.width}} * {{device.height}}</div>
                                <div style="display: inline-block" v-else></div>
                            </Col>
                        </Row>
                        <Row style="height: 40px">
                            <Col span="6">
                                <span style="margin-right: 5px">{{$t('cardDevice.brightness')}}</span>
                                <div style="display: inline-block" v-if="device.brightness_percentage && device.brightness_percentage != -1">{{device.brightness_percentage}}%</div>
                                <div style="display: inline-block" v-else>{{device.brightness}}</div>
                            </Col>
                            <Col span="6">
                                <span style="margin-right: 5px">{{$t('cardDevice.volume')}}</span>
                                {{device.volume}}
                            </Col>
                        </Row>
                    </div>
                </div>
<!--              广播控制-->
                <div v-else-if="selectOpera === 'broadcastControl'">
                  <div>
                    <Row>
                      <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('cardDevice.broadcastTask')}}</div></Col>
                      <Col span="21">
                        <Button style="margin-left:20px" :loading="modalLoading"  type="primary" @click="dataFormSubmit()">{{$t('operation.clearBroadcastTask')}}</Button>
                        <Button style="margin-left:20px" :loading="specialModalLoading"  type="primary" @click="specialDataFormSubmit()">{{$t('task.clearInStream')}}</Button>
                      </Col>
                    </Row>

                    <hr style="width: 670px;margin: 10px auto"/>
                    <Row style="height: 40px">
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.online')}}</span>
                        <div v-if="device.is_on === 1" style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#on-line"></use>
                          </svg>
                        </div>
                        <div v-else style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#line"></use>
                          </svg>
                        </div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.screenStatus')}}</span>
                        <div v-if="device.screen_status === 'on' " style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#on"></use>
                          </svg>
                        </div>
                        <div v-else style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#off"></use>
                          </svg>
                        </div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('lamp.radioState')}}</span>
                        <div v-if="device.radio_state === 1 " style="display: inline-block" >
                          <Tooltip :content="$t('broadcast.radioState1')">
                            <svg style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                              <use xlink:href="#radio4"></use>
                            </svg>
                          </Tooltip>
                        </div>
                        <div v-else-if="device.radio_state===2" style="display: inline-block" >
                          <Tooltip :content="$t('broadcast.radioState2')">
                            <svg style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                              <use xlink:href="#radio3"></use>
                            </svg>
                          </Tooltip>
                        </div>
                        <div v-else-if="device.radio_state===3" style="display: inline-block" >
                          <Tooltip :content="$t('broadcast.radioState3')">
                            <svg style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                              <use xlink:href="#radio2"></use>
                            </svg>
                          </Tooltip>
                        </div>
                        <div v-else-if="device.radio_state===4" style="display: inline-block" >
                          <Tooltip :content="$t('broadcast.radioState4')">
                            <svg style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                              <use xlink:href="#radio1"></use>
                            </svg>
                          </Tooltip>
                        </div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.broadcastTask')}}</span>
                        <div style="display: inline-block" v-if="device.ip_column_program">{{device.ip_column_program}}</div>
<!--                        <div style="display: inline-block" v-else></div>-->
                      </Col>
                    </Row>
                    <Row style="height: 40px">
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.brightness')}}</span>
                        <div style="display: inline-block" v-if="device.brightness_percentage && device.brightness_percentage != -1">{{device.brightness_percentage}}%</div>
                        <div style="display: inline-block" v-else>{{device.brightness}}</div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.volume')}}</span>
                        {{device.volume}}
                      </Col>
                    </Row>
                  </div>
                </div>
<!--              监控控制-->
                <div v-else-if="selectOpera === 'monitoringControl'">
                    monitoringControl
                    <hr style="width: 670px;margin: 10px auto"/>
                </div>
<!--              环境监测-->
                <div v-else-if="selectOpera === 'environment'">
                    <hr style="width: 670px;margin: 10px auto"/>
                    <div v-if="environmentLastData">
                        <div v-if="environmentLastData._type === 'success'">
                            <div v-if="environmentLastData.isSensor485_7 === true">
                                <Row style="height: 40px">
                                    <Col span="6">
                                        <span style="margin-right: 5px">{{$t('cardDevice.temperature')}}</span>
                                        {{environmentLastData.temperature}}  ℃
                                    </Col>
                                    <Col span="6">
                                        <span style="margin-right: 5px">{{$t('card.humidity')}}</span>
                                        {{environmentLastData.humidity}}  RH
                                    </Col>
                                    <Col span="6">
                                        <span style="margin-right: 5px">{{$t('program.noise')}}</span>
                                        {{environmentLastData.noise}}  dB
                                    </Col>
                                    <Col span="6">
                                        <span style="margin-right: 5px">{{$t('program.windSpeed')}}</span>
                                        {{environmentLastData.windSpeed}}  m/s
                                    </Col>
                                </Row>
                                <Row style="height: 40px">
                                    <Col span="6">
                                        <span style="margin-right: 5px">{{$t('program.windDirection')}}</span>
                                        {{environmentLastData.windDirection}} °
                                    </Col>
                                    <Col span="6">
                                        <span style="margin-right: 5px">{{$t('meteorological.Illuminance')}}</span>
                                        {{environmentLastData.sensorBrightness}}
                                    </Col>
                                    <Col span="6">
                                        <span style="margin-right: 5px">PM10</span>
                                        {{environmentLastData.pm10}}  μg/m³
                                    </Col>
                                    <Col span="6">
                                        <span style="margin-right: 5px">pm2.5</span>
                                        {{environmentLastData.pm2_5}}  μg/m³
                                    </Col>
                                </Row>
                                <Row style="height: 40px">
                                    <Col span="24">
                                        <span style="margin-right: 5px">{{$t('lamp.updateTime')}}</span>
                                        {{environmentLastData.time}}
                                    </Col>
                                </Row>
                            </div>
                            <div v-else>
                                <Row style="height: 40px">
                                    <Col span="6">
                                       <span style="color:red">{{$t('lamp.TheSensorIsNot')}}</span>
                                    </Col>
                                    <Col span="18">
                                        <span style="margin-right: 5px">{{$t('lamp.updateTime')}}</span>
                                        {{environmentLastData.time}}
                                    </Col>
                                </Row>
                            </div>
                        </div>
                        <div v-else>
                            <Row style="height: 40px">
                                <Col span="6">
                                    <span style="color:red">{{environmentLastData.msg}}</span>
                                </Col>
                                <Col span="18">
                                    <span style="margin-right: 5px">{{$t('lamp.updateTime')}}</span>
                                    {{environmentLastData.time}}
                                </Col>
                            </Row>
                        </div>
                    </div>
                    <div v-else>{{$t('home.temporarilyNoData')}}</div>
                </div>
<!--              客流统计-->
                <div v-else-if="selectOpera === 'passengerFlow'">
                    passengerFlow
                    <hr style="width: 670px;margin: 10px auto"/>
                </div>
<!--              照明控制-->
                <div v-else-if="selectOpera === 'lighting'">
                  <Row>
                    <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('operation.LightingLevel')}}</div></Col>
                    <Col span="21">
                      <Button type="success" :loading="lightingOpen" @click="lightingSwitchSubmit(true)">{{$t('program.open')}}</Button>
                      <Button type="warning" :loading="lightingClose" @click="lightingSwitchSubmit(false)">{{$t('program.close')}}</Button>
                    </Col>
                  </Row>
                  <Row style="margin-top: 10px">
                    <Col span="3"><div style="font-size: 16px;line-height: 35px">{{$t('operation.LightingLevel')}}:</div></Col>
                    <Col span="10"><SliderDrag  style="margin-left: 0px" :totalWidth="'230'" :value="lightingValue" @SetOpacityConfig="setLightingOpacityConfig"></SliderDrag></Col>
                    <Col span="11"><Button  :loading="lightingLoading"  type="primary" @click="setLightingValue()">{{$t('common.set')}}</Button></Col>
                  </Row>
                  <hr style="width: 670px;margin: 10px auto"/>
                  <div>
                    <Row style="height: 40px">
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.online')}}</span>
                        <div v-if="device.is_on === 1" style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#on-line"></use>
                          </svg>
                        </div>
                        <div v-else style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#line"></use>
                          </svg>
                        </div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.screenStatus')}}</span>
                        <div v-if="device.screen_status === 'on' " style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#on"></use>
                          </svg>
                        </div>
                        <div v-else style="display: inline-block">
                          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                            <use xlink:href="#off"></use>
                          </svg>
                        </div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.networkType')}}</span>
                        <div v-if="device.net_type === 'WIFI'" style="display: inline-block">
                          {{device.net_type}}
                          <svg v-if="device.rssi >= -50 && device.rssi <= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#WIFI-level4"></use>
                          </svg>
                          <svg v-else-if="device.rssi >= -70 && device.rssi < -50" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#WIFI-level3"></use>
                          </svg>
                          <svg  v-else-if="device.rssi >= -80 && device.rssi < -70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#WIFI-level2"></use>
                          </svg>
                          <svg v-else-if="device.rssi >= -100 && device.rssi < -80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#WIFI-level1"></use>
                          </svg>
                        </div>
                        <div style="display: inline-block" v-else-if="device.net_type === 'LTE' || device.net_type === 'UMTS' || device.net_type === 'HSPA'
                                || device.net_type === 'HSPA+' || device.net_type === 'EDGE'  || device.net_type === 'GPRS'">
                          {{device.net_type}}
                          <svg v-if="device.asu >= 12 && device.asu != 99" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#gsm-0"></use>
                          </svg>
                          <svg v-else-if="device.asu >= 8 && device.asu  < 12" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#gsm-1"></use>
                          </svg>
                          <svg  v-else-if="device.asu >= 5 && device.asu < 8" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#gsm-2"></use>
                          </svg>
                          <svg v-else-if="device.asu >= 3 && device.asu < 5" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                            <use xlink:href="#gsm-3"></use>
                          </svg>
                        </div>
                        <div v-else style="display: inline-block">
                          {{device.net_type}}
                        </div>
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.resolvingPower')}}</span>
                        <div style="display: inline-block" v-if="device.width && device.height">{{device.width}} * {{device.height}}</div>
                        <div style="display: inline-block" v-else></div>
                      </Col>
                    </Row>
                    <Row style="height: 40px">
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('operation.LightingLevel')}}</span>
                        <div style="display: inline-block">{{device.light_value}}</div>
<!--                        <div style="display: inline-block" v-if="device.brightness_percentage && device.brightness_percentage != -1">{{device.brightness_percentage}}%</div>-->
<!--                        <div style="display: inline-block" v-else>{{device.brightness}}</div>-->
                      </Col>
                      <Col span="6">
                        <span style="margin-right: 5px">{{$t('cardDevice.volume')}}</span>
                        {{device.volume}}
                      </Col>
                    </Row>
                  </div>
                </div>
<!--              电能管理-->
                <div v-else-if="selectOpera === 'electricity'">
                  <hr style="width: 670px;margin: 10px auto"/>
                  <div v-if="electricityLastData">
<!--                    <div v-if="environmentLastData.isSensor485_7 === true">-->
                      <Row style="height: 40px">
                        <Col span="6">
                          <span style="margin-right: 5px">{{$t('electricity.current')}}</span>
                          {{electricityLastData.current}} A
                        </Col>
                        <Col span="6">
                          <span style="margin-right: 5px">{{$t('electricity.power')}}</span>
                          {{electricityLastData.power}}  W
                        </Col>
                        <Col span="6">
                          <span style="margin-right: 5px">{{$t('electricity.electricity')}}</span>
                          {{electricityLastData.electricity}}  kWh
                        </Col>
                        <Col span="6">
                          <span style="margin-right: 5px">{{$t('electricity.voltage')}}</span>
                          {{electricityLastData.voltage}}  V
                        </Col>
                      </Row>
<!--                    </div>-->
                  </div>
                  <div v-else>
                    <Row style="height: 40px">
                      <Col span="6">
                        <span style="color:red">{{$t('lamp.TheSensorIsNot')}}</span>
                      </Col>
                      <Col span="18">
                        <span style="margin-right: 5px">{{$t('lamp.updateTime')}}</span>
                        {{this.electricityUpdateDate}}
                      </Col>
                    </Row>
                  </div>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import SliderDrag from '@/utils/SliderDrag'
export default {
    data() {
        return {
            show: false,
            model: 'model modelOpen',
            device: {},
            operation: [
                {id: 'screenControl', icon: 'pingmu', text: 'operation.screenControl', disable: false},
                {id: 'broadcastControl', icon: 'broadcast', text: 'operation.broadcastControl', disable: false,auth: 'lampPole:broadcastControl'},
                {id: 'monitoringControl', icon: 'monitoring', text: 'operation.monitoringControl', disable: false,auth: 'lampPole:monitoringControl'},
                {id: 'environment', icon: 'meteorologicalEnvironment', text: 'operation.meteorologicalEnvironmentControl', disable: false,auth: 'lampPole:meteorologicalEnvironment'},
                {id: 'passengerFlow', icon: 'passengerFlow', text: 'operation.passengerFlowStatistics', disable: false,auth: 'lampPole:passengerFlowStatistics'},
                {id: 'lighting', icon: 'lighting', text: 'operation.lightingControl', disable: false,auth: 'lampPole:lighting'},
                {id: 'electricity', icon: 'electricity', text: 'nav.电能管理', disable: false,auth: 'lampPole:powerManagement'},
            ],
            terminalInfoLoading: false,
            selectOpera: 'screenControl',
            on_loading: false,
            off_loading: false,
            brightness: 1,
            brightness_loading: false,
            reloadMe: 0,
            volume: 10,
            volume_loading: false,
            environmentLastData: null,
            electricityLastData: null,
            // ip_column_program:'暂无广播'
            lightingValue:1,
            lightingLoading:false,
            lightingOpen:false,
            lightingClose:false,

            modalLoading:false,
            specialModalLoading:false,
            electricityUpdateDate:""
        }
    },
    methods: {
        // 初始化方法
        init (deviceId) {
            if (deviceId) {
                this.$http({
                url: this.$http.adornUrl(`/lampPole/card/info/${deviceId}`),
                method: 'get',
                params: this.$http.adornParams()
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.device = data.lamp
                        this.show = true
                        this.model = 'model modelOpen'
                        //判断哪些功能可用
                        if (this.device) {
                            for (let i = 0; i < this.operation.length; i++) {
                                const element = this.operation[i];
                                if(element.id === 'broadcastControl') { // 智慧广播状态
                                    element.disable = this.device.radio_state <= 0
                                } else if(element.id === 'monitoringControl') { // 监控状态
                                    element.disable = this.device.monitor_state <= 0
                                } else if(element.id === 'environment') { // 环境检测状态
                                    element.disable = this.device.environment_state <= 0
                                } else if(element.id === 'passengerFlow') { // 客流统计状态
                                    element.disable = this.device.traffic_state <= 0
                                } else if(element.id === 'lighting') {  // 智慧照明状态
                                    element.disable = this.device.light_state <= 0
                                }else if(element.id==='electricity'){ //电能管理状态
                                    element.disable=this.device.electricity_state <= 0
                                }
                            }
                        }
                    } else {
                        this.$Message.error(data.msg)
                    }
                })
            }
        },
        // 查询详情
        getCardInfoByDeviceId (deviceId) {
            this.$http({
                url: this.$http.adornUrl(`/lampPole/card/info/${deviceId}`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.device = data.lamp
                    if (this.device) {
                        for (let i = 0; i < this.operation.length; i++) {
                            const element = this.operation[i];
                            if(element.id === 'broadcastControl') { // 智慧广播状态
                                element.disable = this.device.radio_state <= 0
                            } else if(element.id === 'monitoringControl') { // 监控状态
                                element.disable = this.device.monitor_state <= 0
                            } else if(element.id === 'environment') { // 环境检测状态
                                element.disable = this.device.environment_state <= 0
                            } else if(element.id === 'passengerFlow') { // 客流统计状态
                                element.disable = this.device.traffic_state <= 0
                            } else if(element.id === 'lighting') {  // 智慧照明状态
                                element.disable = this.device.light_state <= 0
                            }else if(element.id==='electricity'){ //电能管理状态
                              element.disable=this.device.electricity_state <= 0
                            }
                        }
                    }
                } else {
                    this.$Message.error(data.msg)
                }
            })
        },
        // 关闭窗口
        close () {
            this.model = 'model modelColse'
            setTimeout(() => {
                this.show = false
                this.selectOpera = 'screenControl'
                this.reloadMe += 1
                this.brightness = 1
                this.$emit('selectLocation')
            }, 1000)
        },
        // 选择功能
        operaSuccessHandle (operationId) {
            this.selectOpera = operationId

            // 查询环境检测最新数据
            if (this.selectOpera === 'environment') {
              var url;
              var timezone=Intl.DateTimeFormat().resolvedOptions().timeZone;
              if(timezone !='Etc/GMT-8'){
                url="/environment/card/lastDate/"+this.device.device_id+"?timezone="+timezone
              }else {
                url="/environment/card/lastDate/"+this.device.device_id
              }
                this.$http({
                    url: this.$http.adornUrl(url),
                    method: 'get',
                    params: this.$http.adornParams()
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.environmentLastData = data.value
                    } else {
                        this.$Message.error(data.msg)
                    }
                })
            }else if (this.selectOpera==='electricity'){
              this.$http({
                url: this.$http.adornUrl(`/electricity/card/todayElectricity/${this.device.device_id}`),
                method:'get',
                params: this.$http.adornParams()
              }).then(({data}) =>{
                if (data && data.code === 0) {
                  if (data.data){
                    this.electricityLastData = data.data
                    this.electricityUpdateDate=data.data.createTime
                  }
                } else {
                  this.$Message.error(data.msg)
                  this.electricityUpdateDate=data.data.createTime

                }
              })
            }else if (this.selectOpera==='broadcastControl'){
              this.getCardInfoByDeviceId(this.device.device_id)
            }
        },
        // 选择不支持的功能
        operaErrorHandle (operationText) {
            this.$Message.warning({
                content: this.$t('common.supportedTip') + this.$t(operationText),
                duration: 2
            })
        },
        // 查询终端信息
        getTerminalInfo () {
            if (this.device.device_id) {
                this.terminalInfoLoading = true
                this.$http({
                    url: this.$http.adornUrl('/card/query/getCardInformation'),
                    method: 'post',
                    data: [this.device.device_id]
                }).then(({ data }) => {
                    if (data && data.cards) {
                      this.getCardInfoByDeviceId(this.device.device_id)
                    } else {
                      this.$Message.error(data.msg)
                    }
                    this.terminalInfoLoading = false
                })
            }
        },
        // 开关屏
        screenSwitchSubmit (status) {
            if (this.device.device_id) {
                if (status === true) {
                this.on_loading = true
                } else {
                this.off_loading = true
                }
                this.$http({
                    url: this.$http.adornUrl('/card/set/screenSwitch'),
                    method: 'post',
                    data: this.$http.adornData({'ids': [this.device.device_id], 'status': status}, false)
                }).then(({data}) => {
                    if (data && data.code === 0) {
                      this.getCardInfoByDeviceId(this.device.device_id)
                      if (data.data) {
                        if (data.data[0]._type != "success") {
                          this.$Message.error(data.data[0].msg)
                        }
                      }
                    } else {
                      this.$Message.error(data.msg)
                    }
                    if (status === true) {
                        this.on_loading = false
                    } else {
                        this.off_loading = false
                    }
                })
            }
        },
        // 设置亮度
        setOpacityConfig(val){
            this.brightness = val
        },
      // 设置照明亮度
      setLightingOpacityConfig(val){
        this.lightingValue = val
      },

      // 设置照明亮度
      setLightingValue () {
        this.lightingLoading= true
        this.$http({
          url: this.$http.adornUrl('/lighting/set/lightingValue'),
          method: 'post',
          data: this.$http.adornData({
            'ids': [this.device.device_id],
            'value': this.lightingValue
          })
        }).then(({data}) => {
          if (data && data._type=="success") {
            this.getCardInfoByDeviceId(this.device.device_id)
          }else {
            this.$Message.error(data.msg)
          }
          this.lightingLoading= false
        })
      },

        //设置亮度
        screenBrightness () {
            if (this.device.device_id) {
                this.brightness_loading = true
                this.$http({
                    url: this.$http.adornUrl('/card/set/screenBrightness'),
                    method: 'post',
                    data: this.$http.adornData({
                    'ids': [this.device.device_id],
                    'brightness': this.brightness
                    })
                }).then(({data}) => {
                    this.brightness_loading = false
                    if (data && data.code === 0) {
                        this.getCardInfoByDeviceId(this.device.device_id)
                        if(data.data) {
                          if (data.data[0]._type != "success") {
                            this.$Message.error(data.data[0].msg)
                          }
                        }
                    } else {
                      this.$Message.error(data.msg)
                    }
                })
            }
        },
        // 设置音量
        screenVolume() {
          if (this.device.device_id) {
              this.volume_loading = true
              this.$http({
                  url: this.$http.adornUrl('/card/set/volume'),
                  method: 'post',
                  data: this.$http.adornData({'ids': [this.device.device_id], 'volume': this.volume},false)
              }).then(({data}) => {
                  this.volume_loading = false
                  if (data && data.code === 0) {
                    this.getCardInfoByDeviceId(this.device.device_id)
                    if(data.data) {
                      if (data.data[0]._type != "success") {
                        this.$Message.error(data.data[0].msg)
                      }
                    }
                  } else {
                    this.$Message.error(data.msg)
                  }
              })
          }
        },

      // 清除所有广播任务
      dataFormSubmit () {
        this.modalLoading=true
            this.$http({
              url: this.$http.adornUrl('/broadcast/set/clearIpColumnService'),
              method: 'post',
              data: [this.device.device_id]
            }).then(({data}) => {
              if (data.data && data.data[0]._type=="success") {
                this.getCardInfoByDeviceId(this.device.device_id)
              }else {
                this.$Message.error(data.data[0].msg)
                // this.$Message.error(data.data[0].message)
              }
              this.modalLoading=false
            })
      },
      // 仅清除插播任务
      specialDataFormSubmit () {
          this.specialModalLoading=true
            this.$http({
              url: this.$http.adornUrl('/broadcast/set/clearSpecialIpColumnService'),
              method: 'post',
              data: [this.device.device_id]
            }).then(({data}) => {
              if (data.data && data.data[0]._type=="success") {
                this.getCardInfoByDeviceId(this.device.device_id)
              }else {
                // this.$Message.error(data.data[0].message)
                this.$Message.error(data.data[0].msg)
              }
              this.specialModalLoading=false

            })
      },
      // 照明开关
      lightingSwitchSubmit(status) {
          if (status === true) {
            this.lightingOpen = true
          } else {
            this.lightingClose = true
          }
            this.$http({
              url: this.$http.adornUrl('/lighting/set/lightingSwitch'),
              method: 'post',
              data: this.$http.adornData({'ids': [this.device.device_id], 'state': status}, false)
            }).then(({data}) => {
              if (data && data.code === 0) {
                  this.getCardInfoByDeviceId(this.device.device_id)
              } else {
                this.$Message.error(data.msg)
              }
              if (status === true) {
                this.lightingOpen = false
              } else {
                this.lightingClose = false
              }
            })


      }


    },
    components: {
        SliderDrag
    },
}
</script>

<style scoped>
.model {
    width: 750px;
    height: 500px;
    background: linear-gradient(to bottom,rgb(212, 212, 212), rgb(248, 248, 248));
    border-radius: 1%;
    border: 1px rgb(211, 211, 211) solid;
    position: fixed;
    opacity: 0.9;
    top: 43%;
    left:50%;
    z-index: 999;
    color: #000;
    box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.2);
    transform: translate(-50%, -50%);
}
.modelOpen {
    animation: moveOpen 1s linear forwards;
    -moz-animation:moveOpen 1s linear forwards;
    -webkit-animation:moveOpen 1s linear forwards;
    -o-animation:moveOpen 1s linear forwards;
}
.modelColse {
    animation: moveColse 1s linear forwards;
    -moz-animation:moveColse 1s linear forwards;
    -webkit-animation:moveColse 1s linear forwards;
    -o-animation:moveColse 1s linear forwards;
}
.title {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
}
.title span {
    margin: 0 auto;
    font-weight: 500;
    font-size: 20px;
    margin-left: 20px;
}
.content {
    width: 748px;
    height: 447px;
    overflow-y: auto;
    overflow-x: hidden;
}
.opera_ul {
  margin: 20px 19px 20px 19px;
  overflow: hidden;
}
.opera_ul li{
  text-align: center;
  float: left;
  list-style: none;
  margin-left: 10px;
  margin-right: 10px;
}
@keyframes shock {
    0% {
        padding-left: 10px;
        padding-right: 10px;
    }
    100% {
        padding-left: 12px;
        padding-right: 8px;
    }
}
.opera_list {
  padding-top: 10px;
  width: 55px;
  height: 55px;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  margin-left: 25%;
}
.opera_list:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 50%;
  cursor: pointer;
  animation-delay: 0s;
  animation-name: shock;
  animation-duration: .1s;
  animation-iteration-count: 3;
  animation-direction: normal;
  animation-timing-function: linear;
}
.opera {
    margin: 0px 39px 0px 39px;
    height: 250px;
}
@keyframes moveColse {
    from {opacity: 0.9;}
    to {opacity: 0;}
}
/* Firefox */
@-moz-keyframes moveColse {
    from {opacity: 0.9;}
    to {opacity: 0;}
}
 /* Safari and Chrome */
@-webkit-keyframes moveColse {
    from {opacity: 0.9;}
    to {opacity: 0;}
}
/* Opera */
@-o-keyframes moveColse {
    from {opacity: 0.9;}
    to {opacity: 0;}
}
@keyframes moveOpen {
    from {top: 32%}
    to {top: 43%}
}
/* Firefox */
@-moz-keyframes moveOpen {
    from {top: 32%}
    to {top: 43%}
}
 /* Safari and Chrome */
@-webkit-keyframes moveOpen {
    from {top: 32%}
    to {top: 43%}
}
/* Opera */
@-o-keyframes moveOpen {
    from {top: 32%}
    to {top: 43%}
}
</style>
