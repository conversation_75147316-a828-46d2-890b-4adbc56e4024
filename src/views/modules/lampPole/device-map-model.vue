<template>
  <div class="modiles-device">
    <Form :inline="true" :model="dataForm">
      <FormItem>
        <Dropdown placement="bottom-start">
          <i class="radius" style="background-color: #00FF00;"></i>
          <div class="radiusText">
            <a href="javascript:void(0)">{{$t('lamp.online')}}  {{onlineList.length}}</a>
          </div>
          <DropdownMenu slot="list" v-if="onlineList && onlineList.length > 0" style="overflow: auto;max-height: 230px;">
            <DropdownItem v-for="(item, index) in onlineList" :key="index" @click.native="clickDevice(item)">
              {{item.alias}}({{item.deviceId}})
            </DropdownItem>
          </DropdownMenu>
          <DropdownMenu slot="list" v-else>
            <DropdownItem>{{$t('home.temporarilyNoData')}}</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </FormItem>
      <FormItem>
        <Dropdown placement="bottom-start">
          <i class="radius" style="background-color: #CCCCCC;"></i>
          <div class="radiusText">
            <a href="javascript:void(0)">{{$t('lamp.offline')}}  {{offlineList.length}}</a>
          </div>
          <DropdownMenu slot="list" v-if="offlineList && offlineList.length > 0" style="overflow: auto;max-height: 230px;">
            <DropdownItem v-for="(item, index) in offlineList" :key="index" @click.native="clickDevice(item)">
              {{item.alias}}({{item.deviceId}})
            </DropdownItem>
          </DropdownMenu>
          <DropdownMenu slot="list" v-else>
            <DropdownItem>{{$t('home.temporarilyNoData')}}</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </FormItem>
      <FormItem>
        <Dropdown placement="bottom-start">
          <i class="radius" style="background-color: #33CC00;"></i>
          <div class="radiusText">
            <a href="javascript:void(0)">{{$t('lamp.targetingIsSet')}}  {{targetingIsSetList.length}}</a>
          </div>
          <DropdownMenu slot="list" v-if="targetingIsSetList && targetingIsSetList.length > 0" style="overflow: auto;max-height: 230px;">
              <DropdownItem v-for="(item, index) in targetingIsSetList" :key="index" @click.native="setDevicePosition(item)">
                {{item.alias}}({{item.deviceId}})
              </DropdownItem>
            </DropdownMenu>
            <DropdownMenu slot="list" v-else>
              <DropdownItem>{{$t('home.temporarilyNoData')}}</DropdownItem>
            </DropdownMenu>
          </Dropdown>
      </FormItem>
      <FormItem>
        <Dropdown placement="bottom-start">
        <i class="radius" style="background-color: #FFFF33;"></i>
        <div class="radiusText">
          <a href="javascript:void(0)" style="color: #FF9999">{{$t('lamp.targetingNotSet')}}  {{targetingNotSetList.length}}</a>
        </div>
        <DropdownMenu slot="list" v-if="targetingNotSetList && targetingNotSetList.length > 0" style="overflow: auto;max-height: 230px;">
            <DropdownItem v-for="(item, index) in targetingNotSetList" :key="index" @click.native="setDevicePosition(item)">
              {{item.alias}}({{item.deviceId}})
            </DropdownItem>
          </DropdownMenu>
          <DropdownMenu slot="list" v-else>
            <DropdownItem>{{$t('home.temporarilyNoData')}}</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </FormItem>
      <FormItem>
        <AutoComplete v-model="searchDeviceId" icon="ios-search" :placeholder="$t('lamp.PleaseEnterThePoleNameOrdeviceID')" style="width:300px" :transfer="true" transfer-class-name="divSearch"
                      @on-search="handleSearchDevice" @on-select="handleSelectDevice">
          <Option v-for="(item, index) in filterDevice" :value="item.deviceId" :key="index">
            <span>{{ item.alias }}</span>
            <span>({{item.deviceId}})</span>
          </Option>
        </AutoComplete>
      </FormItem>
      <FormItem style="float: right">
        <Tooltip :content="$t('lamp.listMode')" placement="bottom-start">
            <svg width="40px" height="40px" aria-hidden="true" @click="mapHandle"
            style="vertical-align:middle;cursor: pointer;">
              <use xlink:href="#list"></use>
            </svg>
        </Tooltip>
      </FormItem>
    </Form>
    <div :style="{'height': tableHeight + 70 + 'px', 'clear': 'both'}">
      <Card dis-hover>
        <div v-if="selectedLanguage==='cn'" :style="{'height': tableHeight + 70 + 'px'}" id="BMap-124"></div>
        <div v-else :style="{'height': tableHeight + 70 + 'px'}" id="BMap-125"></div>
        <!-- <google-com ref="googleMap"></google-com> -->
        <Spin fix v-if="spinShow">
          <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
          <div>{{$t('lamp.LoadingPositioning')}}...</div>
        </Spin>
      </Card>
    </div>
    <!-- 显示地图 -->
    <device-map v-if="deviceMap" ref="deviceMap" @selectLocation="initMap"></device-map>
    <device-google-map v-if="deviceGoogleMap" @selectLocation="initMap" ref="deviceGoogleMap"></device-google-map>
<!--    <model v-if="modalOperation" ref="poleModel" @selectLocation="getDataList"></model>-->
    <model v-if="modalOperation" ref="poleModel"></model>
  </div>
</template>

<script>
import loadBMap from '@/utils/loadMap'
import deviceMap from './device-map.vue'
import model from './lamp-pole-model.vue'
import deviceGoogleMap from "./device-google-map";
import {Loader} from "@googlemaps/js-api-loader";
// import googleCom from '@/components/googlemap'
export default {
  data () {
    return {
      dataForm: {
          key: '',
          group: ''
      },
      spinShow: false,
      myMap: null,
      //位置信息(包含坐标信息)
      businessDetail:{},
      //地图数据
      mapData: {
        //中心坐标
        center: { lng: 105, lat: 38 },
        //缩放级别
        zoom: 12
      },
      dataList: [],
      totalPage: 0,
      // 在线
      onlineList: [],
      // 离线
      offlineList: [],
      // 已设置定位
      targetingIsSetList: [],
      // 未设置定位
      targetingNotSetList: [],
      deviceMap: false,
      deviceGoogleMap:false,
      modalOperation: false,
      searchDeviceId: '',
      filterDevice: [],
      //当前语言
      selectedLanguage:'',
      //谷歌地图的默认zoom
      googleMapZoom:6,
      myMarker:null
    }
  },
  components: {
    deviceMap,
    model,
    deviceGoogleMap
    // googleCom
  },
  methods: {
    // 切换列表模式
    mapHandle () {
      this.$emit('changeModel', 'list')
    },
    // 初始化地图和表数据
    initMap() {
      this.selectedLanguage=localStorage.getItem('language');
     if (this.selectedLanguage==='cn'){
       loadBMap().then(() => {
         // 百度地图API功能
         this.myMap = new BMap.Map("BMap-124") // 创建Map实例
         this.spinShow = true
         //如果坐标不存在则动态获取当前浏览器坐标（创建的时候）
         let geolocation = new BMap.Geolocation()
         // //获取当前的坐标
         geolocation.getCurrentPosition((r) => {
           this.mapData.center.lng = this.businessDetail.longitude = r.point.lng
           this.mapData.center.lat = this.businessDetail.latitude = r.point.lat
           this.myMap.centerAndZoom(new BMap.Point(this.mapData.center.lng,this.mapData.center.lat ), 12)
           this.spinShow = false
         })

         this.myMap.enableScrollWheelZoom(true) //开启鼠标滚轮缩放

         // 添加比例尺控件
         var scaleCtrl = new BMap.ScaleControl({
           // 控件的停靠位置（可选，默认左上角）
           anchor: BMAP_ANCHOR_BOTTOM_LEFT,
           // 控件基于停靠位置的偏移量（可选）
           offset: new BMap.Size(10, 10)
         });
         this.myMap.addControl(scaleCtrl);
         // 添加定位控件
         var geolocationControl = new BMap.GeolocationControl({
             // 控件的停靠位置（可选，默认左上角）
             anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
             // 控件基于停靠位置的偏移量（可选）
             offset: new BMap.Size(10, 10)
         });
         // 将控件添加到地图上
         this.myMap.addControl(geolocationControl);

         // 添加缩放控件
         var navigationControl = new BMap.NavigationControl({
             anchor: BMAP_ANCHOR_TOP_RIGHT,
             offset: new BMap.Size(10, 10)
         });
         this.myMap.addControl(navigationControl);

         // 创建城市选择控件
         var cityControl = new BMap.CityListControl({
             // 控件的停靠位置（可选，默认左上角）
             anchor: BMAP_ANCHOR_TOP_LEFT,
             // 控件基于停靠位置的偏移量（可选）
             offset: new BMap.Size(10, 5)
         });
         // 将控件添加到地图上
         this.myMap.addControl(cityControl);


         this.getDataList()
       }).catch(err => {
         console.log(err)
         console.log('地图加载失败')
       })
     }else {
       const loader = new Loader({
         apiKey: "AIzaSyC7b2_5RxBtvTjnFPmLrzOa_nmXFdALQU8",
         // apiKey: "AIzaSyCpy0fL3DSFkATSiKl44_yF6_RcqsJG_Ys",
         version: "3.34",
       });
       // var latlng = {lat: 49.930663, lng: 9.969829};

       loader.load().then(() => {
         // 49.930663, 9.969829
         //23.092041, 113.261712
         this.myMap = new google.maps.Map(document.getElementById("BMap-125"), {
           center: { lat: 23.092041, lng: 113.261712 },
           //缩放范围
           zoom: 10,
           minZoom: 2,
           maxZoom: 16,
           //限制拖拽范围，防止出现除地图外的灰色区域
           restriction: {
             latLngBounds: {
               north: 85,
               south: -85,
               east: 180,
               west: -180,
             },
           },
           markers:[],
           //禁用键盘控制
           keyboardShortcuts:false,
           //关闭地图类型选择控件
           mapTypeControl:false

         });
         var geocoder = new google.maps.Geocoder();

         var _this=this
         //根据ip来判断来判断是否在中国，如果在中国则无法加载谷歌地图。
         navigator.geolocation.getCurrentPosition(function(position) {
           var latitude = position.coords.latitude;
           var longitude = position.coords.longitude;
           var latlng = {lat: latitude, lng: longitude};
           // console.log(latlng)
           geocoder.geocode({'location': latlng}, function(results, status) {
             if (status === 'OK') {
               if (results[0]) {
                 var country = results[0].address_components[results[0].address_components.length - 1];
                 if (country.short_name !== 'CN') {
                   _this.googlePositionHandle()
                   _this.myMap.setCenter(latlng)
                   _this.myMap.setZoom(12)
                 }
               } else {
                 console.log('No results found.');
               }
             } else {
               console.log('Geocoder failed due to: ' + status);
             }
           });
         });
         this.addYourLocationButton(this.myMap);
         this.getDataList()
       });
     }
    },
    // 获取数据列表
    getDataList () {
      this.$http({
          url: this.$http.adornUrl('/lampPole/card/listAll'),
          method: 'get',
          params: this.$http.adornParams({})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.list
          this.totalPage = data.list.length
          if (this.dataList && this.dataList.length > 0) {
            this.onlineList = this.dataList.filter(item => item.isOn === 1)
            this.offlineList = this.dataList.filter(item => item.isOn === 0)
            this.targetingIsSetList = this.dataList.filter(item => item.latitude !== null && item.longitude != null)
            this.targetingNotSetList = this.dataList.filter(item => item.latitude === null && item.longitude == null)
            // 给所有定位标点
            if (this.myMap && this.myMap != null) {
              if (this.targetingIsSetList && this.targetingIsSetList.length > 0) {
                if (this.selectedLanguage==='cn'){
                  this.positionHandle()
                }else {
                  this.googlePositionHandle()
                }
              }
            }
          }
        } else {
            this.dataList = []
            this.totalPage = 0
        }
      })
    },
    // 给所有的定位标点
    positionHandle () {
      var than = this
      //清除地图上所有的覆盖物
      this.myMap.clearOverlays()
      // 定义自定义覆盖物的构造函数
      function SquareOverlay(center, svgWH, device){
          this._center = center;
          this._svgWH = svgWH;
          this._device = device
      }
      // 继承API的BMap.Overlay
      SquareOverlay.prototype = new BMap.Overlay();

      // 实现初始化方法
      SquareOverlay.prototype.initialize = function(map){
        // 保存map对象实例
        this._map = map;
        // 创建div元素，作为自定义覆盖物的容器
        var div = document.createElement("div");
        div.style.position = "absolute";
        div.style.cursor = "pointer"
        var xlink = 'lampOffline'
        // 可以根据参数设置元素外观
        if (this._device && this._device.isOn != null) {
          if (this._device.isOn === 1) {
            xlink = 'lampOnline'
          } else {
            xlink = 'lampOffline'
          }
        }
        div.innerHTML = '<svg width="'+ this._svgWH +'px" height="'+ this._svgWH +'px" aria-hidden="true"><use xlink:href="#'+ xlink +'"></use></svg>'
        var _this = this
        var showDiv = document.createElement("div");
        div.appendChild(showDiv)
        var style = showDiv.style
        div.onclick = function () {
          than.modalOperation = true
          than.$nextTick(() => {
            than.$refs.poleModel.init(_this._device.deviceId)
          })
        }

          style.backgroundColor = "#fff";
          style.display = "inline-block"
          style.width = "150px";
          style.height = "50px";
          style.position = "relative";
          style.textAlign = "center";
          style.top = "-60px";
          style.left = "60px";
          style.zIndex = "999";
          style.borderRadius = "2%";
          style.boxShadow = "0px 10px 15px rgba(0, 0, 0, 0.2)"
          var htmlText = '<div>'
          if (_this._device) {
            htmlText += '<div>'
            htmlText += _this._device.alias
            htmlText += '</div>'
            htmlText += '<div>'
            htmlText += _this._device.deviceId
            htmlText += '</div>'
          }
          htmlText += '</div>'
          showDiv.innerHTML = htmlText


        // div.onmouseout = function () {
        //   style.display = "none"
        // }


        // 将div添加到覆盖物容器中
        map.getPanes().markerPane.appendChild(div);
        // 保存div实例
        this._div = div;
        // 需要将div元素作为方法的返回值，当调用该覆盖物的show、
        // hide方法，或者对覆盖物进行移除时，API都将操作此元素。
        return div;
      }
      SquareOverlay.prototype.draw = function(){
        var position = this._map.pointToOverlayPixel(this._center);
        this._div.style.left = position.x - this._svgWH / 2 + "px";
        this._div.style.top = position.y - this._svgWH  + "px";
      }

      for (let i = 0; i< this.targetingIsSetList.length; i++) {
        const element = this.targetingIsSetList[i];
        let point = new BMap.Point(element.longitude, element.latitude)
        // 添加自定义覆盖物
        var mySquare = new SquareOverlay(point, 50, element);
        this.myMap.addOverlay(mySquare);
      }
    },
    //谷歌地图标点
    googlePositionHandle(){
        var _this=this
        for (let i = 0; i< this.targetingIsSetList.length; i++) {
          //设备详情
          var positionInfo = this.targetingIsSetList[i];
          var latLng = new google.maps.LatLng(positionInfo.latitude*1,positionInfo.longitude*1 )
          // let latLng = {lat:positionInfo.latitude*1,lng:positionInfo.longitude*1}
          // (positionInfo.latitude*1,positionInfo.longitude*1 )
          //设置Marker自定义图标的属性，size是图标尺寸，该尺寸为显示图标的实际尺寸，origin是切图坐标，该坐标是相对于图片左上角默认为（0,0）的相对像素坐标，anchor是锚点坐标，描述经纬度点对应图标中的位置
          var anchor = new google.maps.Point(0, 40)
          var size = new google.maps.Size(32, 32)
          var origin = new google.maps.Point(0, 0)
          var offlineUrl=require('@/assets/img/lampOffline.png')
          var onlineUrl=require('@/assets/img/lampOnline.png')
          var offlineIcon = new google.maps.MarkerImage(
            offlineUrl,
            size,
            origin,
            anchor,
          );
          var onlineIcon = new google.maps.MarkerImage(
            onlineUrl,
            size,
            origin,
            anchor,
          );
          let deviceId=positionInfo.deviceId
          //设置自定义标记图标
          let marker=new google.maps.Marker({
            position: latLng,
            title:deviceId,
            map: _this.myMap,
            visible: true
          });
          if (positionInfo.isOn===1){
            marker.setIcon(onlineIcon)
          }else {
            marker.setIcon(offlineIcon)
          }

          //标记添加点击事件
          marker.addListener('click', function() {
            // _this.myMap.setCenter(latLng)
            // _this.myMap.setZoom(16)
            // console.log(latLng)
            _this.modalOperation = true
            _this.$nextTick(() => {
              // debugger
              _this.$refs.poleModel.init(deviceId)
            })
          });

          //标记上方的信息框
          var div='<div style="text-align: center">'+
            '<div>' +
            '<div>'+positionInfo.alias+'</div>'+
            '<div>'+positionInfo.deviceId+'</div>'+
            '</div>'+
            '</div>'
          let infoWindow = new google.maps.InfoWindow({
            content: div,
            // position:latLng,
            //停用平移地图，使信息窗口在打开时完全可见。
            disableAutoPan:true
          });
          // infoWindow.setPosition(latLng)
          //打开信息框
          // infoWindow.open()
          infoWindow.open({
            anchor: marker,
            map:_this.myMap,
          });
        }
    },
    // 点击设备获取位置
    clickDevice(item) {
      // 经度
      var longitude = item.longitude
      // 纬度
      var latitude = item.latitude
      if (longitude && latitude) {
        if (this.myMap && this.myMap != null) {
          if (this.selectedLanguage === 'cn') {
            this.myMap.centerAndZoom(new BMap.Point(longitude, latitude), this.mapData.zoom);
          } else {
            let latLng = new google.maps.LatLng(latitude,longitude)
            this.myMap.setCenter(latLng)
            this.myMap.setZoom(this.mapData.zoom)
          }
        } else {
          this.$Message.error(this.$t('lamp.getMapException'))
        }
      } else {
        this.$Message.error(this.$t('lamp.NoLocationData'))
        this.setDevicePosition(item)
      }
    },
    // 给未设置定位的灯杆设置定位或者已设置的修改定位
    setDevicePosition(item) {
      this.selectedLanguage=localStorage.getItem('language');
      if (this.selectedLanguage==="cn"){
        this.deviceMap = true
        this.$nextTick(() => {
          // 如果经度纬度不为空按照经纬度显示
          this.$refs.deviceMap.init(item.longitude, item.latitude, item.deviceId);
        })
      }else {
        this.deviceGoogleMap=true
        this.$nextTick(() => {
          this.$refs.deviceGoogleMap.init(item.longitude, item.latitude, item.deviceId);
        })
      }
    },
    handleSearchDevice(value) {
      if (this.dataList && this.dataList.length > 0) {
        this.filterDevice = this.dataList.filter(item => {
          if (item.alias.indexOf(value) !== -1 || item.deviceId.indexOf(value) != -1) {
            return item
          }
        })
      }
    },
    handleSelectDevice (value) {
      var deviceItem = this.filterDevice.filter(item => {
        if (item.deviceId === value) {
          return item
        }
      })
      if (deviceItem && deviceItem.length > 0) {
        this.clickDevice(deviceItem[0])
      }
    },
    //添加定位按钮
    addYourLocationButton(map) {
    var controlDiv = document.createElement('div');
    var firstChild = document.createElement('button');
    firstChild.style.backgroundColor = '#fff';
    firstChild.style.border = 'none';
    firstChild.style.outline = 'none';
    firstChild.style.width = '40px';
    firstChild.style.height = '40px';
    firstChild.style.borderRadius = '2px';
    firstChild.style.boxShadow = '0 1px 4px rgba(0,0,0,0.3)';
    firstChild.style.cursor = 'pointer';
    firstChild.style.marginRight = '10px';
    firstChild.style.padding = '0px';
    firstChild.title = 'Your Location';
    controlDiv.appendChild(firstChild);

    var secondChild = document.createElement('div');
    secondChild.style.margin = '5px';
    secondChild.style.width = '18px';
    secondChild.style.height = '18px';
    secondChild.style.backgroundImage = 'url(https://maps.gstatic.com/tactile/mylocation/mylocation-sprite-1x.png)';
    secondChild.style.backgroundSize = '180px 18px';
    secondChild.style.backgroundPosition = '0px 0px';
    secondChild.style.backgroundRepeat = 'no-repeat';
    secondChild.style.margin='auto'
    secondChild.id = 'location_img';
    var _this=this
    firstChild.appendChild(secondChild);
    firstChild.addEventListener('click', function() {
      if(navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
          const latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
          map.setCenter(latlng);
          map.setZoom(15)
          if (null!=_this.myMarker){
            _this.myMarker.setMap(null)
          }
          _this.myMarker = new google.maps.Marker({
            animation: google.maps.Animation.DROP,
            position: latlng
          });
          _this.myMarker.setMap(map)
        });
      }
    });
    controlDiv.index = 1;
    map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);
  }


},
  activated () {
    // this.initMap()
  },
  watch: {
    'language': function (newVal, OldVal) {
      if (this.$route.path=="/lampPole-device"){
        this.initMap()
      }
    }
  },
  computed: {
    tableHeight: {
        get () { return this.$store.state.common.tableHeight }
    },
    language: {
      get() { return this.$store.state.language.language },
    },
  }
}
</script>

<style>
/* .ivu-dropdown .ivu-select-dropdown {
  overflow: auto;
  max-height: 230px;
} */
/*防止搜索超出*/
.divSearch{
  height: 300px;
}
.radiusText {
  display:inline-block;
  margin-right: 10px;
  margin-left: 5px;
}
.radiusText a {
  color: #666666;
}
.radius {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  display:inline-block;
}
.citylist_popup_main .citylist_ctr_content .city_content_top {
  height: 50px;
}
.anchorBL img{
  display:none;
}
.BMap_cpyCtrl {
  display:none;
  visibility:hidden;
}
.gmnoprint div div span{
  display:none;
  visibility:hidden;
}
.gmnoprint.gm-style-cc{
  display:none;
  visibility:hidden;
}
.gm-style-cc{
  display:none;
  visibility:hidden;
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
</style>
