<template>
  <div>
    <Modal v-model="visible" :title="$t('lamp.ChooseTargeting')" @on-ok="ok" @on-cancel="cancel" width="800">
      <div style="margin-bottom: 10px">
        <Form :label-width="60" inline>
          <FormItem :label="$t('lamp.latitude')">
            <Input v-model="mapData.center.lat" placeholder="lat" style="width: 160px" @on-blur="changePosition()" />
          </FormItem>
          <FormItem :label="$t('lamp.longitude')">
            <Input v-model="mapData.center.lng" placeholder="lng" style="width: 160px" @on-blur="changePosition()" />
          </FormItem>
          <FormItem>
            <Button type="primary" style="margin-left: 10px" :loading="cardLocationLoading" @click="getCardLocation()">{{ $t('operation.getGspInfo') }}</Button>
          </FormItem>
        </Form>
      </div>
      <div style="height: 400px;position: relative;">
        <div style="height: 400px" id="GMap"></div>
        <Spin fix v-if="spinShow">
          <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
          <div>{{ $t('lamp.LoadingPositioning') }}...</div>
        </Spin>
      </div>
    </Modal>
  </div>
</template>

<script>

import {Loader} from "@googlemaps/js-api-loader"

export default {
  data() {
    return {
      visible: false,
      //位置信息(包含坐标信息)
      businessDetail: {},
      //地图数据
      mapData: {
        //中心坐标
        center: {lng: 0, lat: 0},
        //缩放级别
        zoom: 14
      },
      spinShow: false,
      myMap: null,
      geolocationControl: null,
      deviceId: '',
      googleMarker: null,
      cardLocationLoading:false,
    }
  },
  methods: {

    init(longitude, latitude, deviceId) {
      if (deviceId) {
        this.deviceId = deviceId
      }
      this.visible = true
      if (longitude && latitude) {
        //设置经度
        this.mapData.center.lng = this.businessDetail.longitude = longitude
        //设置纬度
        this.mapData.center.lat = this.businessDetail.latitude = latitude
      }
      this.initMap()
    },

    ok() {
      if (this.deviceId) {
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/setLngAndLat'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.deviceId,
            'longitude': this.businessDetail.longitude.toString(),
            'latitude': this.businessDetail.latitude.toString()
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                this.visible = false
                this.$emit('selectLocation')
              }
            })
          } else {
            this.$Message.error(data.msg)
          }
        })
      } else {
        this.visible = false
        this.$emit('selectLocation', this.businessDetail)
      }
    },
    cancel() {
      this.visible = false

    },
    initMap() {
      const loader = new Loader({
        apiKey: "AIzaSyC7b2_5RxBtvTjnFPmLrzOa_nmXFdALQU8",
        version: "3.34",
      });
      loader.load().then(() => {
        this.myMap = new google.maps.Map(document.getElementById("GMap"), {
          center: {lat: -34.397, lng: 150.644},
          zoom: 8,
          //限制拖拽范围，防止出现除地图外的灰色区域
          restriction: {
            latLngBounds: {
              north: 85,
              south: -85,
              east: 180,
              west: -180,
            },
          },
          markers:[],
          //禁用键盘控制
          keyboardShortcuts:false,
          //关闭地图类型选择控件
          mapTypeControl:false
        });
        this.initPosition()
        this.addYourLocationButton(this.myMap)
        let _this = this
        google.maps.event.addListener(this.myMap, 'click', function (event) {
          const pos = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
          };
          if (null!=_this.googleMarker){
            _this.googleMarker.setMap(null);
          }
          _this.mapData.center.lng = event.latLng.lng()
          _this.mapData.center.lat = event.latLng.lat()
          //设置经度
          _this.businessDetail.longitude = event.latLng.lng()
          //设置纬度
          _this.businessDetail.latitude = event.latLng.lat()
          _this.googleMarker = new google.maps.Marker({
            position: event.latLng,
            map: _this.myMap,
            visible: true
          })
          _this.googleMarker.setPosition(pos)
          _this.googleMarker.setMap(_this.myMap)

        });
      });

    },
    initPosition() {
      if (this.mapData.center.lat && this.mapData.center.lng) {
        this.businessDetail.longitude=this.mapData.center.lng
        this.businessDetail.latitude=this.mapData.center.lat
        if (this.googleMarker){
          this.googleMarker.setMap(null)
        }

        //创建定位标记
        let latLng = new google.maps.LatLng(this.businessDetail.latitude, this.businessDetail.longitude)
        this.googleMarker = new google.maps.Marker({
          position: latLng,
          map: this.myMap,
          visible: true
        });
        this.googleMarker.setMap(latLng)
        this.myMap.setCenter(latLng)
      } else {
        var _this = this
        this.spinShow = true
        this.mapData.center.lng = this.businessDetail.longitude = 2.239367
        this.mapData.center.lat = this.businessDetail.latitude = 49.168296
        let latLng = new google.maps.LatLng(this.businessDetail.latitude, this.businessDetail.longitude)
        //创建定位标记
        _this.googleMarker = new google.maps.Marker({
          position: latLng,
          map: this.myMap,
          visible: true
        })
        _this.myMap.setCenter(latLng)
        _this.myMap.setZoom(8)
        this.spinShow = false
      }
    },
    //添加定位按钮
    addYourLocationButton(map) {
      var controlDiv = document.createElement('div');

      var firstChild = document.createElement('button');
      firstChild.style.backgroundColor = '#fff';
      firstChild.style.border = 'none';
      firstChild.style.outline = 'none';
      firstChild.style.width = '40px';
      firstChild.style.height = '40px';
      firstChild.style.borderRadius = '2px';
      firstChild.style.boxShadow = '0 1px 4px rgba(0,0,0,0.3)';
      firstChild.style.cursor = 'pointer';
      firstChild.style.marginRight = '10px';
      firstChild.style.padding = '0px';
      firstChild.title = 'Your Location';
      controlDiv.appendChild(firstChild);

      var secondChild = document.createElement('div');
      secondChild.style.margin = '5px';
      secondChild.style.width = '18px';
      secondChild.style.height = '18px';
      secondChild.style.backgroundImage = 'url(https://maps.gstatic.com/tactile/mylocation/mylocation-sprite-1x.png)';
      secondChild.style.backgroundSize = '180px 18px';
      secondChild.style.backgroundPosition = '0px 0px';
      secondChild.style.backgroundRepeat = 'no-repeat';
      secondChild.style.margin='auto'
      secondChild.id = 'location_img';
      firstChild.appendChild(secondChild);
      firstChild.addEventListener('click', function() {
        if(navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(function(position) {
            const latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
            map.setCenter(latlng);
            map.setZoom(15)
            // _this.googleMarker = new google.maps.Marker({
            //   animation: google.maps.Animation.DROP,
            //   position: latlng
            // });
            // _this.googleMarker.setMap(map)
          });
        }
      });
      controlDiv.index = 1;
      map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);
    },

    // 获取设备的定位
    getCardLocation(){
      if (this.deviceId) {
        this.cardLocationLoading=true
        var ids=[this.deviceId]
        this.$http({
          url: this.$http.adornUrl('/card/query/getGpsInfo'),
          method: 'post',
          data: ids
        }).then(({data}) => {
          if (data && data.code == 0&&data.data[0]._type==='success') {
            this.mapData.center.lng = this.data.data[0].lng
            this.mapData.center.lat = this.data.data[0].lat
            this.changePosition()
          } else {
            this.$Message.error(data.data[0].msg)
          }
          this.cardLocationLoading=false
        })
      } else {
        this.visible = false
        this.$emit('selectLocation',this.businessDetail)
      }
    },
    changePosition(){
      this.initPosition()
    },

  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.businessDetail = {}
          this.mapData.center = {lng: 0, lat: 0}
          this.mapData.zoom = 14
        }, 200)
      }
    }
  }
}
</script>

<style>
.citylist_popup_main .citylist_ctr_content .city_content_top {
  height: 50px;
}

.anchorBL img {
  display: none;
}

.BMap_cpyCtrl {
  display: none;
  visibility: hidden;
}

/* .info {
  z-index: 999;
  margin-left: 1.25rem;
  margin-top: 15px;
  position: fixed;
  border-radius: .25rem;
  color: #666;
  box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
} */
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
