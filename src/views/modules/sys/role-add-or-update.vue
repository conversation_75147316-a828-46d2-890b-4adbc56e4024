<template>
    <Modal v-model="visible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{!dataForm.id ? $t('common.newlyBuild') : $t('common.update')}}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 400px;" :label-width="80" label-position="left"
        @keyup.enter.native="dataFormSubmit()">
          <FormItem prop="roleName" :label="$t('role.roleName')">
            <Input size="large" v-model="dataForm.roleName"
            :placeholder="$t('common.PleaseInput') + $t('role.roleName')"/>
          </FormItem>
          <FormItem prop="remark" :label="$t('role.remark')">
            <Input size="large" v-model="dataForm.remark"
            :placeholder="$t('common.PleaseInput') + $t('role.remark')"/>
          </FormItem>
          <FormItem :label="$t('role.authorization')">
            <Tree :data="menuList" show-checkbox  expand-node ref="menuListTree"></Tree>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" :loading="loading" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
import { setCheckedNodes } from '@/utils'
import zh from '@/language/zh-CN.json'
export default {
  data () {
    return {
      visible: false,
      menuList: [],
      loading: false,
      dataForm: {
        id: 0,
        roleName: '',
        remark: '',
        menuIdList: [],
        createUserId: ''
      }
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          roleName: [
            { required: true, message: this.$t('validate.roleName_cannot_empty'), trigger: 'blur' }
          ]
        }
      }
    },
  },
  methods: {
    // 初始化
    init (id) {
      this.dataForm.id = id || 0
      this.$http({
        url: this.$http.adornUrl('/sys/menu/list'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        // this.menuList = treeDataTranslate(data, 'menuId')
        // 改title为国际化数据
        this.menuList = data
        if (this.menuList.length > 0) {
          this.menuList = this.changeTitle()
        }
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
        })
      }).then(() => {
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/sys/role/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm.roleName = data.role.roleName
              this.dataForm.remark = data.role.remark
              this.dataForm.createUserId = data.role.createUserId
              var menuIdList = data.role.menuIdList
              this.menuList = setCheckedNodes(this.menuList, menuIdList)
            }
          })
        }
      })
    },
    // 改变tree的标题
    changeTitle() {
      for (let i = 0; i < this.menuList.length; i++) {
        const element = this.menuList[i];
        if (element.menuId != 188 && element.menuId != 189 && element.parentId != 188 && element.parentId != 189) {
          if (element.type == 3) {
            element.title = this.$t(this.getZhKeyByValue(element.title))
          } else {
            element.title = this.$t('nav.' + element.title)
          }
        } 
        if (element.children.length > 0) {
          for (let j = 0; j < element.children.length; j++) {
            const elementJ = element.children[j];
            if (elementJ.menuId != 188 && elementJ.menuId != 189 && elementJ.parentId != 188 && elementJ.parentId != 189) {
              if (elementJ.type == 3) {
                elementJ.title = this.$t(this.getZhKeyByValue(elementJ.title))
              } else {
                elementJ.title = this.$t('nav.' + elementJ.title)
              }
            }
            if (elementJ.children.length > 0) {
              for (let k = 0; k < elementJ.children.length; k++) {
                const elementk = elementJ.children[k];
                if (elementk.menuId != 188 && elementk.menuId != 189 && elementk.parentId != 188 && elementk.parentId != 189) {
                  if (elementk.type == 3) {
                    elementk.title = this.$t(this.getZhKeyByValue(elementk.title))
                  } else {
                    elementk.title = this.$t('nav.' + elementk.title)
                  }
                }
                if (elementk.children.length > 0) {
                   for (let r = 0; r < elementk.children.length; r++) {
                    const elementR = elementk.children[r];
                    if (elementR.type == 3) {
                      elementR.title = this.$t(this.getZhKeyByValue(elementR.title))
                    } else {
                      elementR.title = this.$t('nav.' + elementR.title)
                    }
                  }
                }
              }
            }
          }
        }
      }
      return this.menuList
    },
    // 获取对应的国际化标签
    getZhKeyByValue(val) {
      for(let key in zh) {
        // 判断属性是否是对象自身的属性而非继承的属性
        if (zh.hasOwnProperty(key)) {
          for(let key1 in zh[key]) {
            if (zh[key].hasOwnProperty(key1)) {
              if (zh[key][key1] == val) {
                return key + "." + key1
              }
            }
          }
        }
      }
      return val
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          // 获取菜单ID
          this.$refs.menuListTree.getCheckedAndIndeterminateNodes().forEach(element => {
            this.dataForm.menuIdList.push(element.menuId)
          })
          this.$http({
            url: this.$http.adornUrl(`/sys/role/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'roleId': this.dataForm.id || undefined,
              'roleName': this.dataForm.roleName,
              'remark': this.dataForm.remark,
              'menuIdList': this.dataForm.menuIdList,
              'createUserId': this.dataForm.createUserId
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
            this.loading = false
            this.dataForm.menuIdList = []
          })
        }
      })
    }
  }
}
</script>
