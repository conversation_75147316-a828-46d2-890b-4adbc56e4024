<template>
  <div class="modiles-systemLog">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('log.user_name_user_action')"></Input>
      </FormItem>
      <FormItem>
        <Button @click="getDataList(1)" size="large">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataColumns" :data="dataList" ref="table"
           :loading="dataListLoading" style="width: 100%" :max-height="tableHeight">

      <template slot-scope="{ row, index }" slot="number">
        {{ index + 1 }}
      </template>

      <template slot-scope="{ row, index }" slot="type">
        <div v-if="row.type==1">
          <span>{{ $t('userAuth.company') }}</span>
        </div>
        <div v-else>
          <span>{{ $t('userAuth.personal') }}</span>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="isAuth">
        <div v-if="row.isAuth == 0">
          <span>{{ $t('userAuth.unverified') }}</span>
        </div>
        <div v-else-if="row.isAuth == 1">
          <span>{{ $t('userAuth.pass') }}</span>
        </div>
        <div v-else>
          <span>{{ $t('userAuth.fail') }}</span>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button type="primary" size="small"
                style="margin-right: 5px;font-size: 11px" @click="userAuth(row.id)">
          {{ $t('file.examine') }}
        </Button>

      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <Modal v-model="visible" width="900">
      <p slot="header" style="text-align: center">
        <span>{{ $t('userAuth.certificationAudit') }}</span>
      </p>
      <div v-if="selectUser" style="height: 600px; overflow-y: auto;">
          <Form :model="dataForm">
            <FormItem :label="$t('userAuth.authMode') + ':'">
              {{selectUser.type == 1 ? $t('userAuth.company') : $t('userAuth.personal')}}
            </FormItem>
            <div v-if="selectUser.type == 1">
              <FormItem :label="$t('register.companyName') + ':'">{{ selectUser.companyName }}</FormItem>
              <FormItem :label="$t('register.companyAddress') + ':'">{{ selectUser.companyAddr }}</FormItem>
              <FormItem :label="$t('register.companyPhone') + ':'">{{ selectUser.companyPhone }}</FormItem>
            </div>
            <div v-else>
               <FormItem :label="$t('register.idCardNumber') + ':'">{{ selectUser.idCardNumber }}</FormItem>
            </div>
            <FormItem :label="$t('userAuth.authInfo') + ':'">
              <ul class="screen_ul" v-if="selectUser.pics">
                <li v-if="selectUser.pics.companyLicense">
                  <span>{{ $t('userAuth.enterpriseLicense') }}</span>
                  <div @click="onPreview(selectUser.pics.companyLicense)">
                    <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                                placement="bottom-end">
                      <el-image style="width: 220px;height: 220px;margin-top: 5px" :src="selectUser.pics.companyLicense"
                                :alt="$t('common.clickToEnlarge')"></el-image>
                    </el-tooltip>
                  </div>
                </li>
                <li v-if="selectUser.pics.cachet">
                  <span>{{ $t('userAuth.OfficialSeal') }}</span>
                  <div @click="onPreview(selectUser.pics.cachet)">
                    <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                                placement="bottom-end">
                      <el-image style="width: 220px;height: 220px;margin-top: 5px" :src="selectUser.pics.cachet"
                                :alt="$t('common.clickToEnlarge')"></el-image>
                    </el-tooltip>
                  </div>
                </li>
                <li v-if="selectUser.pics.idCardFront">
                  <span>{{ $t('userAuth.FrontOfIdentityCard') }}</span>
                  <div @click="onPreview(selectUser.pics.idCardFront)">
                    <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                                placement="bottom-end">
                      <el-image style="width: 220px;height: 220px;margin-top: 5px" :src="selectUser.pics.idCardFront"
                                :alt="$t('common.clickToEnlarge')"></el-image>
                    </el-tooltip>
                  </div>
                </li>
                <li v-if="selectUser.pics.idCardReverse">
                  <span>{{ $t('userAuth.ReverseOfIDCard') }}</span>
                  <div @click="onPreview(selectUser.pics.idCardReverse)">
                    <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                                placement="bottom-end">
                      <el-image style="width: 220px;height: 220px;margin-top: 5px" :src="selectUser.pics.idCardReverse"
                                :alt="$t('common.clickToEnlarge')"></el-image>
                    </el-tooltip>
                  </div>
                </li>
              </ul>
              <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]"
                                style="z-index:25000;"/>
            </FormItem>
            <FormItem :label="$t('userAuth.authentication') + ':'">
              <Select v-model="selectUser.isAuth" style="width:200px" transfer>
                <Option v-for="item in authList" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
              </Select>
            </FormItem>
            <FormItem :label="$t('userAuth.reason') + ':'" v-if="selectUser.isAuth == 2">
              <Input style="width:700px"  v-model="selectUser.reason" type="textarea" :autosize="{minRows: 3,maxRows: 5}" maxlength="300"></Input>
            </FormItem>

          </Form>
      </div>

      <div slot="footer">
        <Button size="large" @click="visible = false">{{ this.$t('common.cancel') }}</Button>
        <Button type="primary" size="large" :loading="modal_loading" @click="submit()">{{ this.$t('common.submit') }}</Button>
      </div>
    </Modal>

    <Modal v-model="pwdVisible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{ $t('login.password') }}</span>
        </p>
        <Form style="height: 70px;" :label-width="80" label-position="left"
        @keyup.enter.native="authPwdSubmit()">
          <FormItem :label="$t('login.password')">
            <Input size="large" type="password" v-model="password"
            :placeholder="$t('common.PleaseInput') + $t('login.password')"/>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="pwdVisible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" @click="authPwdSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
  </div>

</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  data() {
    return {
      dataForm: {
        key: ''
      },
      dataColumns: [
        {
          title: this.$t('cardDevice.number'), width: 70, align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('login.username'), key: 'userName', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('login.username'))
          }
        },
        {
          title: this.$t('menu.type'), key: 'type', align: 'center',slot: 'type',
          renderHeader: (h) => {
            return h('div', this.$t('menu.type'))
          }
        },
        {
          title: this.$t('common.state'), key: 'isAuth', align: 'center',slot: 'isAuth',
          renderHeader: (h) => {
            return h('div', this.$t('common.state'))
          }
        },
        {
          title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.createTime'))
          }
        },
        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center', fixed: 'right', width: 160,
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      visible: false,
      modal_loading: false,
      selectUser: null,
      showViewer: false,
      showImg: '',
      authList: [
        {
          value: 1,
          label: 'userAuth.pass'
        },
        {
          value: 2,
          label: 'userAuth.fail'
        }
      ],
      pwdVisible: false,
      password: '',
    }
  },
  components: {
    ElImageViewer,
  },
  activated() {
    this.initData()
  },
  methods: {
    initData() {
      this.dataForm = {
        key: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataList = []
      this.dataListLoading = false
      this.selectUser = null
      this.password = ''
      this.getDataList();
    },
    authPwdSubmit() {
      this.pwdVisible = false;
      if (this.password) {
        this.getDataList()
      }
    },
    // 获取数据列表
    getDataList(isQuery) {
      if (!this.password) {
        this.pwdVisible = true
        return;
      }
      this.dataListLoading = true
      if (isQuery === 1) {
        this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/sys/userAuth/list'),
        method: 'get',
        headers: {
          'pwd': this.$encruption(this.password)
        },
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage > this.totalPage && this.totalPage !== 0) {
            this.pageIndex = 1
            this.getDataList(isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
          if (data.code == 897) {
            this.password = '';
            this.pwdVisible = true
          }
          this.$Message.error(data.msg)
        }
        this.dataListLoading = false
      })
    },
    userAuth(id) {
      if (!this.password) {
        this.pwdVisible = true
        return;
      }
      this.visible = true
      this.$http({
        url: this.$http.adornUrl('/sys/userAuth/info'),
        method: 'get',
        headers: {
          'pwd': this.$encruption(this.password)
        },
        params: this.$http.adornParams({"id": id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.selectUser = data.data
        } else {
          if (data.code == 897) {
            this.password = '';
            this.pwdVisible = true
          }
          this.$Message.error(data.msg)
        }
      })
    },
    onPreview(img) {
      this.showViewer = true
      this.showImg = img;
    },
    closeViewer() {
      this.showViewer = false
      this.showImg = ''
    },
    submit() {
      if (!this.password) {
        this.pwdVisible = true
        return;
      }
      this.modal_loading = true
      this.$http({
        url: this.$http.adornUrl('/sys/userAuth/auth'),
        method: 'post',
        headers: {
          'pwd': this.$encruption(this.password)
        },
        data: this.$http.adornData({"id": this.selectUser.id, "reason": this.selectUser.reason, "isAuth": this.selectUser.isAuth})
      }).then(({data}) => {
        if (data && data.code == 0) {
          this.getDataList()
        } else {
          if (data.code == 897) {
            this.password = '';
            this.pwdVisible = true
          }
          this.$Message.error(data.msg)
        }
        this.modal_loading = false
        this.visible=false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },

  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight
      }
    }
  }
}
</script>

<style>
.screen_ul {
  height: 300px;
  overflow: hidden;
  overflow-y: auto;
}

.screen_ul li {
  list-style: none;
  float: left;
  margin-left: 20px;
  margin-top: 10px;
  width: 250px;
  height: 275px;
  background: #ebeef5;
  text-align: center;
  border-radius: 5%;
}
</style>
