<template>
    <Modal v-model="visible" width="700">
        <p slot="header" style="text-align:center">
            <span>{{!dataForm.id ? $t('common.newlyBuild') : $t('common.update')}}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 400px;" :label-width="90" label-position="left">
          <FormItem prop="type" :label="$t('menu.type')">
            <Select v-model="dataForm.type" size="large">
                <Option v-for="item in typeList" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
            </Select>
          </FormItem>
          <FormItem prop="tactics" :label="$t('notify.tactics')">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">{{ $t('notify.selectAll') }}</el-checkbox>
            <Divider size="small" class="ivu-m-0" />
            <el-checkbox-group v-model="checkedHour" @change="handleCheckedHourChange">
              <el-checkbox border size="mini" v-for="item in hours" style="margin: 0 10px 0 10px"
                :label="item" :key="item">{{item}} h</el-checkbox>
            </el-checkbox-group>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" :loading="loading" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {
        id: 0,
        type: 0,
        tactics:""
      },
      typeList: [
        {value: 0, label: "notify.OfflineNotify"},
        {value: 1, label: "notify.CardNotWorkingNotification"}
      ],
      checkAll: false,
      checkedHour: ['08', '12', '16', '20'],
      hours: this.generatedhours(),
      isIndeterminate: true
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          // roleName: [
          //   { required: true, message: this.$t('validate.roleName_cannot_empty'), trigger: 'blur' }
          // ]
        }
      }
    },
  },
  methods: {
    // 初始化
    init (id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(`/sys/notify/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.type = data.notify.type
            if (data.notify.tactics) {
              this.checkedHour = data.notify.tactics.split(",")
            }
          }
        })
      } else {
        this.checkedHour = ['08', '12', '16', '20']
      }
    },
    generatedhours() {
      let hoursData = []
      for (let i = 1; i <= 24; i++) {
        hoursData.push(i < 10 ? ('0' + i) : (i + ''))
      }
      return hoursData
    },
    handleCheckAllChange(val) {
      this.checkedHour = val ? this.generatedhours() : [];
      this.isIndeterminate = false;
    },
    handleCheckedHourChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.hours.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.hours.length;
    },
    // 表单提交
    dataFormSubmit () {
      if (this.checkedHour.length <= 0) {
        this.$Message.error(this.$t('notify.PleaseSelectNotification'))
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(`/sys/notify/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'type': this.dataForm.type,
              'tactics': this.checkedHour.toString(),
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
