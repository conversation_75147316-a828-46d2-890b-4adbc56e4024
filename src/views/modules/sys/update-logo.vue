<template>
    <Modal v-model="visible" width="350">
        <p slot="header" style="text-align:center">
            <span>{{$t('common.personalSettings')}}</span>
        </p>
        <Alert type="success" show-icon>
                  {{$t("tips.CustomCanUseNetworkAddress")}}
        </Alert>
        <Form ref="dataForm" style="height: 200px; width: 100%">
            <FormItem>
              <RadioGroup v-model="icon">
                <Radio :label="item.icon" v-for="item in validateList" :key="item.icon">
                  <img :src="item.url" style="height: 40px;margin-top:5px;vertical-align: middle;"/>
                </Radio>
                <Radio label="custom">{{$t('common.custom')}}</Radio>
                <Input v-if="icon === 'custom'" v-model="custom" :placeholder="$t('common.PleaseInput')"/>
                <div v-if="custom !== ''" style="margin-top: 10px;height: 50px">
                  LOGO：
                  <img :src="custom" style="height: 40px;width: 100%;vertical-align: middle;"/>
                </div>
              </RadioGroup>
            </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.update')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      icon: 'aips40.png',
      custom: '',
      validateList: [
        // {icon: 'aips40.png', url: require('@/assets/img/aips40.png')},
        // {icon: 'aips40_1.png', url: require('@/assets/img/aips40_1.png')},
        // {icon: 'aips40_2.png', url: require('@/assets/img/aips40_2.png')},
        // {icon: 'aips40_3.png', url: require('@/assets/img/aips40_3.png')},
        // {icon: 'aips40_4.png', url: require('@/assets/img/aips40_4.png')}
      ]
    }
  },
  methods: {
    // 初始化
    init () {
      this.custom = ''
      this.visible = true
      if (this.userInfo.iconUrl) {
        if (this.userInfo.iconUrl.substr(0, 6) === 'aips40') {
          this.icon = this.userInfo.iconUrl
        } else {
          this.icon = 'custom'
          this.custom = this.userInfo.iconUrl
        }
      }
    },
    dataFormSubmit () {
      if (this.icon === 'custom') {
        if (this.custom === '') {
          this.$Message.error(this.$t('tips.networkAddressWhenCustomizing'));
          return
        } else {
          this.icon = this.custom
        }
      }
      this.$http({
        url: this.$http.adornUrl('/sys/user/updateLogo'),
        method: 'post',
        params: this.$http.adornParams({'icon': this.icon})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 1
          })
          this.$emit('refreshData')
            this.visible = false
        } else {
          this.$Message.error(data.msg)
        }
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>
