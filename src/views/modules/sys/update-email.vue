<template>
    <Modal v-model="visible" width="400">
        <p slot="header" style="text-align:center">
            <span>{{$t('login.'+ type)}}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule">
          <FormItem v-if="userInfo.email">
            <b>{{$t('common.original') + $t('register.mailbox')}} : </b>
              {{userInfo.email}}
          </FormItem>
          <FormItem prop="email">
              <Input prefix="ios-mail-outline" size="large" type="email" v-model="dataForm.email"
              :placeholder="$t('common.PleaseInput') + $t('register.mailbox')"/>
          </FormItem>
          <FormItem prop="code">
              <Input prefix="ios-lock-outline" size="large" v-model="dataForm.code"
              :placeholder="$t('common.PleaseInput') + $t('register.code')">
                <div id="sendCode" @click="sendCode()" slot="append">
                  <a style="cursor: pointer">{{$t('register.getCode')}}</a>
                </div>
              </Input>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
          <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.update')}}</Button>
        </div>
    </Modal>
</template>

<script>
import $ from 'jQuery'
import { isEmail, isSixCode } from '@/utils/validate'
export default {
  data () {
    return {
      visible: false,
      countdown: 60,
      timer: null,
      type: '',
      dataForm: {
        email: '',
        code: ''
      }
    }
  },
  methods: {
    // 初始化
    init (type) {
      this.visible = true
      this.type = type
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    // 获取验证码
    sendCode () {
      if (isEmail(this.dataForm.email)) {
        this.$http({
          // url: this.$http.adornUrl(`/sms/email/sendCode?email=${this.dataForm.email}`),
          url: this.$http.adornUrl(`/sms/email/sendCode`),
          method: 'post',
          data: this.$http.adornData({
            "email": this.$encruption(this.dataForm.email)
          })
        }).then(({data}) => {
          if (data && data.code !== 0) {
            this.$Message.error(data.msg)
            this.clearTimer()
          }
        })
        this.timeoutChangeStyle()
      } else {
        this.$Message.error(this.$t('validate.incorrect_email_format'))
      }
    },
    timeoutChangeStyle () {
      // 启动定时器
      this.loading()
      this.timer = setInterval(() => {
        // 创建定时器
        if (this.countdown === 0) {
          this.clearTimer() // 关闭定时器
        } else {
          this.loading()
        }
      }, 1000)
    },
    loading () {
      // 启动定时器
      var str = `<div>${this.$t('register.prependResend')} ${this.countdown} ${this.$t('register.appendResend')} </div>`
      $('#sendCode').html(str)
      this.countdown-- // 定时器减1
    },
    clearTimer () {
      // 清除定时器
      clearInterval(this.timer)
      this.timer = null
      this.countdown = 60
      $('#sendCode').html(`<a style="cursor: pointer">${this.$t('register.getCode')}</a>`)
    },
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/sys/user/updateEmail'),
            method: 'post',
            data: this.$http.adornData({
              'email': this.dataForm.email,
              'code': this.dataForm.code
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 1
              })
              this.$emit('refreshData')
              this.visible = false
              // 清除当前定时器
              this.clearTimer()
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    },
    dataRule: {
      get () {
        return {
          email: [
            { required: true, message: this.$t('validate.email_cannot_empty'), trigger: 'blur' },
            { type: 'email', message: this.$t('validate.incorrect_email_format'), trigger: 'blur' }
          ],
          code: [
            { required: true, message: this.$t('validate.code_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isSixCode(value)) {
                callback(new Error(this.$t('validate.code_format')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ]
        }
      }
      }
  }
}
</script>
