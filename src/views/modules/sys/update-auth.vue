<template>
  <Modal v-model="visible" width="800" :closable="false" :mask-closable="false">
    <p slot="header" style="text-align:center">
      <span>{{isAuth ==  2 ? this.$t('userAuth.updateAuth'): this.$t('userAuth.uploadAuth')}}</span>
    </p>
    <Tabs @on-click="handlerTab" :value="tabName" v-if="!isSubmit">
      <TabPane :label="$t('register.company')" icon="md-home" name="company">
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 500px;overflow: auto">
          <FormItem :label="$t('register.CertifiProgress')" v-if="authInfo">
              <div v-if="authInfo.isAuth == 0">{{$t('register.waitForAdminAuth')}}</div>
              <div style="color:red;" v-else-if="authInfo.isAuth == 2">
                <span>{{authInfo.reason}}</span>
              </div>
          </FormItem>
          <FormItem  prop="companyName">
            <Row>
              <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
              <Col span="23"><Input prefix="ios-pricetags" size="large" type="text" v-model="dataForm.companyName" :placeholder="$t('register.companyName')"></Input></Col>
            </Row>
          </FormItem>
          <FormItem prop="companyAddr">
            <Row>
              <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
              <Col span="23"><Input prefix="ios-home" size="large" type="text" v-model="dataForm.companyAddr" :placeholder="$t('register.companyAddress')"></Input></Col>
            </Row>
          </FormItem>
          <FormItem prop="companyPhone" >
            <Row>
              <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
              <Col span="23"><Input prefix="ios-call" size="large" type="text" v-model="dataForm.companyPhone" :placeholder="$t('register.companyPhone')"></Input></Col>
            </Row>
          </FormItem>

          <FormItem class="upload" label="">
            <span>{{ $t ('register.companyLicense')}}</span>
            <!-- <div v-if="authInfo"> -->
              <div v-if="authInfo.companyLicenseAddr && isBase64(authInfo.companyLicenseAddr)"
                @click="onPreview(authInfo.companyLicenseAddr)" class="pic2">
                <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                            placement="bottom-end">
                  <el-image style="width: 220px;height: 220px;margin-top: 20px" :src="authInfo.companyLicenseAddr"
                            :alt="$t('common.clickToEnlarge')"></el-image>
                </el-tooltip>
              </div>
              <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]"
                               style="z-index:25000;"/>
            <!-- </div> -->
            <Upload :before-upload="function (file) {return handleUpload(file, 1)}" :action="''" type="drag" 
              accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>{{$t('file.attachment1')}}</p>
              </div>
            </Upload>
          </FormItem>
          <FormItem class="upload" label="">
            <span>{{ $t('register.cachet') }}</span>
            <!-- <div v-if="authInfo"> -->
              <div v-if="authInfo.cachetAddr && isBase64(authInfo.cachetAddr)" 
                @click="onPreview(authInfo.cachetAddr)" class="pic2">
                <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                            placement="bottom-end">
                  <el-image style="width: 220px;height: 220px;margin-top: 20px" :src="authInfo.cachetAddr"
                            :alt="$t('common.clickToEnlarge')"></el-image>
                </el-tooltip>
              </div>
              <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]"
                               style="z-index:25000;"/>
            <!-- </div> -->
            <Upload :before-upload="function (file) {return handleUpload(file, 2)}" :action="''" type="drag" accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>{{$t('file.attachment1')}}</p>
              </div>
            </Upload>
          </FormItem>
          <div v-if="dataForm.secondFile != null">
            <span>{{dataForm.secondFile.name}}</span>
            <Icon type="ios-close" size="20" style="float:right;" @click="dataForm.secondFile = null"></Icon>
          </div>
        </Form>
      </TabPane>
      <TabPane :label="$t('register.personal')" icon="ios-person" name="person">
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 500px;overflow: auto">
          <FormItem :label="$t('register.CertifiProgress')" v-if="authInfo">
              <div v-if="authInfo.isAuth == 0">{{$t('register.waitForAdminAuth')}}</div>
              <div style="color:red;" v-else-if="authInfo.isAuth == 2">
                <span>{{authInfo.reason}}</span>
              </div>
          </FormItem>
          <FormItem prop="cardIdNumber" >
            <Row>
              <Col span="1"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical" /></Col>
              <Col span="23"><Input prefix="md-contacts" size="large" type="text" v-model="dataForm.idCardNumber" :placeholder="$t('common.PleaseInput')+$t('register.idCardNumber')"></Input></Col>
            </Row>
          </FormItem>
          <FormItem class="upload" label="">
            <span>{{ $t('register.idCardFront') }}</span>
            <!-- <div v-if="authInfo"> -->
              <div v-if="authInfo.idCardFrontAddr && isBase64(authInfo.idCardFrontAddr)" 
                @click="onPreview(authInfo.idCardFrontAddr)" class="pic2">
                <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                            placement="bottom-end">
                  <el-image style="width: 220px;height: 220px;margin-top: 20px" :src="authInfo.idCardFrontAddr"
                            :alt="$t('common.clickToEnlarge')"></el-image>
                </el-tooltip>
              </div>
              <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]"
                               style="z-index:25000;"/>
            <!-- </div> -->
            <Upload :before-upload="function (file) {return handleUpload(file, 3)}" :action="''" type="drag" accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>{{$t('file.attachment1')}}</p>
              </div>
            </Upload>
          </FormItem>
          <FormItem class="upload" label="">
            <span>{{ $t('register.idCardReverse') }}</span>
            <!-- <div v-if="authInfo"> -->
              <div v-if="authInfo.idCardReverseAddr && isBase64(authInfo.idCardReverseAddr)" 
                @click="onPreview(authInfo.idCardReverseAddr)" class="pic2">
                <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')"
                            placement="bottom-end">
                  <el-image style="width: 220px;height: 220px;margin-top: 20px" :src="authInfo.idCardReverseAddr"
                            :alt="$t('common.clickToEnlarge')"></el-image>
                </el-tooltip>
              </div>
              <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]"
                               style="z-index:25000;"/>
            <!-- </div> -->
            <Upload :before-upload="function (file) {return handleUpload(file, 4)}" :action="''" 
              type="drag" accept="image/jpg,image/jpeg, image/png, application/x-shockwave-flash">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>{{$t('file.attachment1')}}</p>
              </div>
            </Upload>
          </FormItem>
          <div v-if="dataForm.secondFile != null">
            <span>{{dataForm.secondFile.name}}</span>
            <Icon type="ios-close" size="20" style="float:right;" @click="dataForm.secondFile = null"></Icon>
          </div>
        </Form>
      </TabPane>
    </Tabs>
    <div v-else>
      <div style="height:60px;"></div>
      <h2>{{$t('tips.authTip')}}</h2>
      <div style="height:110px;"></div>
      <!-- <a @click="loginPage()"><h3>{{$t('register.clickJumpOr')}} {{timeGoToLogin}} s {{$t('register.loginDisplayed')}}</h3></a> -->
    </div>
    <div slot="footer" v-if="!isSubmit">
      <Button size="large" @click="visible = false">{{ this.$t('common.cancel') }}</Button>
      <Button type="primary" size="large" :loading="loading" @click="submit()">{{ this.$t('common.submit') }}</Button>
    </div>
    <div slot="footer" v-else>
      <Button size="large" @click="visible = false">{{ this.$t('sys.close') }}</Button>
    </div>
  </Modal>
</template>

<script>
import { isEmail, isMobile ,isIdCardNumber} from '@/utils/validate'
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import {clearLoginInfo} from "../../../utils";
import axios from 'axios'
export default {
  data () {
    return {
      visible:false,
      loading: false,
      dataForm: {
        id:null,
        companyId: '',
        companyName: '',
        companyAddr: '',
        companyPhone: '',
        //企业
        companyLicenseFile: null,
        cachetFile: null,
        // 个人
        idCardFrontFile: null,
        idCardReverseFile: null,
        //身份证号
        idCardNumber:'',
        //是公司还是个人，1 公司、2 个人
        type:'1',
      },
      //查询的认证信息
      authInfo:{
        companyLicenseAddr: null,
        cachetAddr: null,
        idCardFrontAddr: null,
        idCardReverseAddr: null
      },
      showViewer: false,
      showImg: '',
      tabName: 'company',
      isSubmit: false,
      timerGoToLogin: null,
      timeGoToLogin: 3,
      isAuth: 0,
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          companyId: [
            { required: true, message: this.$t('validate.company_id_empty'), trigger: 'blur' }
          ],
          companyAddr: [
            { required: true, message: this.$t('validate.company_address_cannot_empty'), trigger: 'blur' }
          ],
          companyName: [
            { required: true, message: this.$t('validate.company_name_cannot_empty'), trigger: 'blur' }
          ],
          companyPhone: [
            { required: true, message: this.$t('validate.company_phone_number_cannot_empty'), trigger: 'blur' }
          ],
          idCardNumber: [
            { required: true, message: this.$t('validate.company_phone_number_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
                if (!isIdCardNumber(value)) {
                  callback(new Error(this.$t('validate.id_card_number_format_wrong')))
                } else {
                  callback()
                }
              }, trigger: 'blur' }
          ]
        }
      }
    }
  },
  components: {
    ElImageViewer
  },
  methods: {
    isBase64(str) {
      if (str.indexOf('data:') != -1 && str.indexOf('base64') != -1) {
        return true;
      } else {
        return false;
      }
    },
    //切换tab
    handlerTab(name){
      if (name==='company'){
        this.dataForm.type = 1;
      }else {
        this.dataForm.type = 2
      }
    },
    // 文件上传前
    handleUpload (selectFile, status) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      if(ele === "image"){
        if(ele === "image" && selectFile.size > (10 * 1024 *1024)){
          this.$Message.error(this.$t('screen.picture')+'：'+selectFile.name +this.$t('screen.sizeMore')+ '10M!')
          if (status == 1) {
            this.dataForm.companyLicenseFile = null
          } else if (status == 2) {
            this.dataForm.cachetFile = null
          } else if (status == 3) {
            this.dataForm.idCardFrontFile = null
          } else if (status == 4){
            this.dataForm.idCardReverseFile = null
          }
          return false
        }
      } else {
        this.$Message.error(this.$t('screen.picturesOrVideos'))
        if (status == 1) {
          this.dataForm.companyLicenseFile = null
        } else if (status == 2) {
          this.dataForm.cachetFile = null
        } else if (status == 3) {
          this.dataForm.idCardFrontFile = null
        } else if (status == 4){
          this.dataForm.idCardReverseFile = null
        }
        return false
      }
      const reader = new FileReader();
      //将文件读取为 DataURL 以data:开头的字符串
      reader.readAsDataURL(selectFile);
      reader.onload = (e) => {
        // 读取到的图片base64 数据编码 将此编码字符串传给后台即可
        const code = e.target.result;
        if (status == 1) {
          this.authInfo.companyLicenseAddr = code;
          this.dataForm.companyLicenseFile = selectFile
        } else if (status == 2) {
          this.authInfo.cachetAddr = code;
          this.dataForm.cachetFile = selectFile
        } else if (status == 3) {
          this.authInfo.idCardFrontAddr = code;
          this.dataForm.idCardFrontFile = selectFile
        } else if (status == 4){
          this.authInfo.idCardReverseAddr = code;
          this.dataForm.idCardReverseFile = selectFile
        }
      };
      // console.log(this.authInfo)
      return false
    },
    timeJS () {
      this.timeGoToLogin -= 1
      if (this.timeGoToLogin <= 0) {
        this.loginPage()
      }
    },
    // 登录页面跳转
    loginPage () {
      this.visible = false
      // this.$router.replace({ name: 'login' })
      clearInterval(this.timerGoToLogin)
      this.isSubmit = false
      // clearLoginInfo()
    },
    //更新、添加认证信息
    submit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
            let formData = new FormData();
            formData.append("userAuth", JSON.stringify(this.dataForm));
            formData.append("companyLicenseFile", this.dataForm.companyLicenseFile);
            formData.append("cachetFile", this.dataForm.cachetFile);
            formData.append("idCardFrontFile", this.dataForm.idCardFrontFile);
            formData.append("idCardReverseFile", this.dataForm.idCardReverseFile);
            this.loading = true
            axios.request({
              url: this.$http.adornUrl('/sys/userAuth/addOrUpdate'),
              method: 'post',
              data: formData,
              headers: { 'Content-Type': 'multipart/form-data', 'token': this.$cookie.get('token') }
            }).then(res => {
              // 上传成功处理
              if (res.data && res.data.code === 0) {
                this.loading = false
                /* this.$Message.success({
                  content: this.$t('tips.authTip'),
                  onClose: () => {
                    setTimeout(() => {
                      this.loading = false
                    }, 8000)
                  }
                }) */
                this.isSubmit = true
                this.timeGoToLogin = 3
                this.timerGoToLogin = setInterval(() => { this.timeJS() }, 1000)
                // clearLoginInfo()
                // this.$router.push({name:'login'})
              } else {
                this.$Message.error({
                  content: res.data.msg,
                  onClose: () => {
                    setTimeout(() => {
                      this.loading = false
                    }, 500)
                  }
                })
              }
            });
            /* this.$http({
              url: this.$http.adornUrl('/sys/userAuth/addOrUpdate'),
              method: 'post',
              data: formData,
              headers: { 'Content-Type': 'multipart/form-data'}
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.loading = false
                this.isSubmit = true
                this.timeGoToLogin = 3
                this.timerGoToLogin = setInterval(() => { this.timeJS() }, 1000)
              } else {
                this.$Message.error({
                  content: data.msg,
                  onClose: () => {
                    setTimeout(() => {
                      this.loading = false
                    }, 500)
                  }
                })
              }
            }) */
        }
      })
    },
    cancel() {
      /* this.visible = false
      clearLoginInfo()
      this.$router.push({name:'login'}) */
    },
    onPreview(img) {
      this.showViewer = true
      this.showImg = img;
    },
    closeViewer() {
      this.showViewer = false
      this.showImg = ''
    },
    init(isAuth){
      this.isAuth = isAuth
      this.dataForm = {
        id:null,
        companyId: '',
        companyName: '',
        companyAddr: '',
        companyPhone: '',
        //企业
        companyLicenseFile: null,
        cachetFile: null,
        // 个人
        idCardFrontFile: null,
        idCardReverseFile: null,
        //身份证号
        idCardNumber:'',
        //是公司还是个人，1 公司、2 个人
        type:'1',
      }
      //查找是否存在用户
      this.$http({
        url: this.$http.adornUrl(`/sys/userAuth/authUserInfo`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.authInfo) {
            this.authInfo = data.authInfo
            if (this.authInfo.type === 1){
              this.tabName = "company"
              this.dataForm.companyName = this.authInfo.companyName
              this.dataForm.companyAddr = this.authInfo.companyAddr
              this.dataForm.companyPhone = this.authInfo.companyPhone
            }else {
              this.tabName = "person"
              this.dataForm.idCardNumber = this.authInfo.idCardNumber
            }
            this.dataForm.type = this.authInfo.type
            this.dataForm.id = this.authInfo.id
          } else {
            this.authInfo = {
              companyLicenseAddr: null,
              cachetAddr: null,
              idCardFrontAddr: null,
              idCardReverseAddr: null
            }
          }
          this.visible = true
        } else {
          this.$Message.error(data.msg)
        }
      })
    }
  },
}
</script>

<style scoped>
.ivu-steps .ivu-steps-title {
  display: inline-block;
  margin-bottom: 4px;
  padding-right: 10px;
  font-size: 14px;
  font-weight: 700;
  color: #666;
  background: rgba(255, 255, 255, 0.719);
}
.ivu-form-item {
  margin-bottom:20px;
}
.login-wrap {
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.719);
  width: 720px;
  box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.2);
  border-radius: 1%;
  overflow-x: hidden;
  overflow-y: auto;
}
.content {
  margin: 0 auto;
  text-align: center;
  width: 80%;
}
.login-title {
  color: #6f7072;
  font-weight: 600;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.719);
  font-size: 26px;
}
.steps {
  text-align: left;
}
.pic2{
  /*list-style: none;*/
  /*float: left;*/
  /*margin-left: 20px;*/
  /*margin-top: 10px;*/
  /*width: 250px;*/
  /*height: 260px;*/
  /*background: #ebeef5;*/
  /*text-align: center;*/
  /*border-radius: 5%;*/
}
</style>
