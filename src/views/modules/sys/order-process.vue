<template>
  <div>
    <Modal v-model="visible" width="700" footer-hide>
      <p slot="header" style="text-align:center">
          <span>{{$t('approval.approvalProcess')}}</span>
      </p>
      <div style="height: 400px;overflow-x:hidden;overflow-y:auto;">
        <Timeline style="margin-top: 10px" v-if="dataList && dataList.length > 0">
          <TimelineItem v-for="(item, index) in dataList" :key="index">
              <p class="time">{{item.createTime}}</p>
              <p class="content">
                <span v-if="item.tradeStatus === 'CREATE_ORDER'">{{$t('pay.unpaid')}}</span>
                <span v-else-if="item.tradeStatus === 'WAIT_BUYER_PAY'">{{$t('pay.transactionCreation')}}</span>
                <span v-else-if="item.tradeStatus === 'BANK_RECEIPT_ERROR'">
                    {{$t('pay.ErrorUploadingBankReceipt')}}
                    <Card dis-hover>
                        <template #title>{{$t('file.ApprovalComments')}}</template>
                        <p>{{item.remark}}</p>
                    </Card>
                </span>
                <span v-else-if="item.tradeStatus === 'TRADE_CLOSED'">{{$t('pay.UnpaidTransactionTimeoutClosed')}}</span>
                <span v-else-if="item.tradeStatus === 'WAIT_SELLER_CONFIRM'">{{$t('pay.WaitingForSellerToConfirm')}}</span>
                <span v-else-if="item.tradeStatus === 'TRADE_SUCCESS'">{{$t('pay.paymentSuccessful')}}</span>
                <!-- <span v-else-if="item.tradeStatus === 'TRADE_SUCCESS' || item.tradeStatus === 'TRADE_FINISHED'">{{$t('pay.paymentSuccessful')}}</span> -->
              </p>
          </TimelineItem>
        </Timeline>
        <div v-else>
          {{$t('home.temporarilyNoData')}}
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      outTradeNo: null,
      visible: false,
      loading: false,
      dataList: [],
    }
  },
  methods: {
    // 初始化
    init (outTradeNo) {
      this.dataList = [],
      this.visible = true
      this.outTradeNo = outTradeNo || null
      if (this.outTradeNo) {
        this.$http({
          url: this.$http.adornUrl(`/sys/order/orderProcess/${this.outTradeNo}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.payBills
          }
        })
      }
    },
  },
  computed: {
  }
}
</script>
<style scoped>
.bankImg {
  display:inline-block;
  width: 120px;
  height: 120px;
  border: 1px solid #dadee3; 
}
.order-detail {
  margin-bottom: 10px;
}
.order-detail__table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}
.titleTh {
  background-color: #fafbfb;
  color: #909aaa;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  border: 1px solid #ecedf0;
  padding: 8px 16px;
}
.order-detail__table tr {
    display: table-row;
    vertical-align: inherit;
    border-color: inherit;
}
.order-detail__table tr td {
  border: 1px solid #ecedf0;
  padding: 8px 16px;
}
.order-detail__table tr .subtext {
    color: #909aaa;
    font-size: 12px;
    margin-left: 8px;
}
</style>