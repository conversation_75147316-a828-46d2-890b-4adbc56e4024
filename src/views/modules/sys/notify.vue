<template>
  <div class="modiles-notify">
    <Row>
        <Col span="22" style="height: 40px;line-height: 40px;">
          <div class="notify-title">{{ $t('notify.NotificationStrategy') }}
            <span class="notify-tip">{{ $t('notify.NotifyTip') }}</span>
          </div>
        </Col>
        <Col span="2">
          <Button size="large" shape="circle" icon="md-add" v-if="isAuth('sys:notify:save')" @click="addOrUpdateHandle()">{{$t('common.newlyBuild')}}</Button>
        </Col>
    </Row>
    <Form :inline="true" :model="dataForm" v-if="userInfo.userId == 1">
      <FormItem style="margin-bottom: 2px">
        <Input size="large" v-model="dataForm.username" :placeholder="$t('login.username')"></Input>
      </FormItem>
      <FormItem style="margin-bottom: 2px">
        <Button style="margin-right:6px" @click="getDataList(1)"  size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table style="margin-top: 10px;width: 100%" border :max-height="tableHeight" :columns="dataColumns" :data="dataList" :loading="dataListLoading"
     @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow" ref="selection">
      <template slot-scope="{ row, index }" slot="type">
          {{ $t(typeFilters(row.type)) }}
      </template>
      <template slot-scope="{ row, index }" slot="tactics">
          {{row.tactics | tacticsFilters}}
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button v-if="isAuth('sys:notify:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
        <Button v-if="isAuth('sys:notify:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
      show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
        @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>
<script>
import AddOrUpdate from './notify-add-or-update'
export default {
  data () {
    return {
      dataColumns: [
        {type: 'selection', width: 60, align: 'center'},
        { title: this.$t('menu.type'), key: 'type', slot: 'type', width: 260,
          renderHeader:(h)=>{
            return h('div',this.$t('menu.type'))
          }
        },
        { title: this.$t('notify.tactics'), key: 'tactics', slot: 'tactics',
          renderHeader:(h)=>{
            return h('div',this.$t('notify.tactics'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',width: 200,
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',width: 200,
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      addOrUpdateVisible: false,
      dataListLoading: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListSelections: [],
      dataForm: {
        username:''
      }
    }
  },
  activated () {
    this.dataColumns = [
      {type: 'selection', width: 60, align: 'center'},
      { title: this.$t('menu.type'), key: 'type', slot: 'type', width: 260,
        renderHeader:(h)=>{
          return h('div',this.$t('menu.type'))
        }
      },
      { title: this.$t('notify.tactics'), key: 'tactics', slot: 'tactics',
        renderHeader:(h)=>{
          return h('div',this.$t('notify.tactics'))
        }
      },
      {title: this.$t('common.createTime'), key: 'createTime', align: 'center',width: 200,
        renderHeader:(h)=>{
          return h('div',this.$t('common.createTime'))
        }
      },
      {title: this.$t('common.operation'), slot: 'operation', align: 'center',width: 200,
        renderHeader:(h)=>{
          return h('div',this.$t('common.operation'))
        }
      },
    ]
    this.getDataList()
    // 如果是超级管理员显示创建用户
    if (this.userInfo.userId == 1) {
      this.dataColumns.splice(3, 0, {
        title: this.$t('home.founder'), key: 'username', width: 100,
        renderHeader:(h)=>{
          return h('div',this.$t('home.founder'))
        }
      })
    }
  },
  methods: {
    // 获取数据列表
    getDataList (isQuery) {
      this.dataListLoading = true
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/sys/notify/list'),
        method: 'get',
        params: this.$http.adornParams(this.dataForm)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    typeFilters (val){
      let res = ""
      switch (val) {
        case 0:
          res = 'notify.OfflineNotify'
          break;
        case 1:
          res = "notify.CardNotWorkingNotification"
          break;
        default:
          break;
      }
      return res
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/sys/notify/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === ids.length) {
                    this.pageIndex--
                  }
                  this.getDataList()
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  components: {
    AddOrUpdate
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  },
  filters: {
    tacticsFilters: function(val) {
      var res = ""
      if (val != null) {
        var temp = val.split(",")
        for (let i = 0; i < temp.length; i++) {
          const element = temp[i];
          res += element + "h"
          if (i != temp.length - 1) {
            res += ","
          }
        }
      }
      return res
    }
  }
}
</script>
<style scoped>
.notify-title {
  font-weight: bold;
  color: #464c5b;
  font-size: 19px;
}
.notify-tip {
  color: #9ea7b4;
  font-size: 16px;
}
</style>
