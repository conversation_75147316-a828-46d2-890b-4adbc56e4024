<template>
  <Modal v-model="visible" width="800" :closable="false" :mask-closable="false">
    <p slot="header" style="text-align:center">
      <span>{{ $t('resetPWD.changePassword') }}</span>
    </p>
    <Form ref="dataForm" :model="dataForm" :rules="dataRule">
      <Alert type="warning"  show-icon style="margin-top: 10px;" v-if="isNotAutoWindow=='false'">
        <span>{{ $t('tips.passwordIsWeak') }}</span>
      </Alert>
      <Alert type="success" show-icon style="margin-top: 10px;">
            <span slot="desc">{{ $t('register.youAreRight') }}<b
              style="color:#ff9900;cursor: default;">&nbsp;{{ this.userName }}&nbsp;</b>
          {{ $t('register.resettingVerification') }}：
            <div style="color:#0000cc;cursor: pointer;" v-if="resData.email && resData.mobile === 0"
                 @click="formCode.flag === 0 ? formCode.flag = 1 : formCode.flag = 0">{{ $t('register.switchingAuthentication') }}</div></span>
      </Alert>
      <FormItem v-if="!isVerify"
        :label="formCode.flag === 0 ?  `${$t('login.clickCodeMailbox')}${resData.email} `: `${$t('login.clickCodePhone')}${resData.mobile}`"
        prop="code">
      </FormItem>
      <FormItem v-if="!isVerify">
        <Row>
          <Col span="16">
            <Input prefix="ios-mail" size="large" v-model="formCode.code" type="text"
                   :placeholder="$t('common.PleaseInput') + $t('register.code')">
            </Input>
          </Col>
          <Col span="6">
            <Button style="margin-left: 5px" size="large" @click="sendCode" id="sendCode">
              <div>{{ $t('register.getCode') }}</div>
            </Button>
          </Col>
        </Row>
      </FormItem>
      <FormItem prop="password" v-if="isVerify">
        <Input prefix="ios-lock-outline" swc ize="large" type="text" v-model="dataForm.password"
               :placeholder="$t('common.PleaseInput') + $t('login.password')"/>
      </FormItem>
      <FormItem prop="newPassword">
        <Input prefix="ios-lock-outline" size="large" type="text" v-model="dataForm.newPassword"
               :placeholder="$t('common.PleaseInput') + $t('login.newPassword')"/>
      </FormItem>
      <FormItem prop="confirmPassword">
        <Input prefix="ios-lock-outline" size="large" type="text" v-model="dataForm.confirmPassword"
               :placeholder="$t('common.PleaseInput') + $t('login.confirmPassword')"/>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button size="large" @click="cancel()">{{ $t('common.cancel') }}</Button>
      <Button type="primary" size="large" @click="dataFormSubmit()">{{ $t('common.update') }}</Button>
    </div>
  </Modal>
</template>

<script>
import {clearLoginInfo} from '@/utils'
import {PWDLenght} from '@/utils/validate'
import $ from "jQuery";

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        password: '',
        newPassword: '',
        confirmPassword: ''
      },
      formCode: {
        code: '',
        flag: 0// 表示使用哪种方式验证 0 邮箱 1 短信 默认邮箱
      },
      resData: {},
      current: 0,
      userName: this.$store.state.user.userInfo.username,
      countdown: 60,
      timer: null,
      //是否为强密码
      isNotAutoWindow: "true",
      isVerify: window.SITE_CONFIG.isVerify,
    }
  },
  methods: {
    // 初始化
    init() {
      this.visible = true
      this.isNotAutoWindow = this.$cookie.get("isStrong");
      this.submitFindUser()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    // 查询用户
    submitFindUser() {
      this.$http({
        url: this.$http.adornUrl(`/sys/findUser/${this.userName}`),
        method: 'get',
        params: this.$http.adornParams({
          'isValidate': this.isVerify,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.resData = data.resData
          this.loading = false
          // 表示使用哪种方式验证 0 邮箱 1 短信 默认邮箱，优先邮箱
          if (this.resData.validate){
            if (this.resData.validate===1){
              this.formCode.flag=0
            }else if (this.resData.validate===2){
              this.formCode.flag=1
            }else {
              if (this.resData.mobile) {
                this.formCode.flag = 1
              } else if (this.resData.email) {
                this.formCode.flag = 0
              }
            }
          }
        } else {
          this.$Message.error(data.msg)
        }
      })
    },
    //发送验证码
    sendCode() {
      this.$http({
        url: this.$http.adornUrl(`/sms/emailOrMobile/sendCode?username=${this.userName}&flag=${this.formCode.flag}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code !== 0) {
          this.$Message.error(data.msg)
          this.clearTimer()
        }
      })
      this.timeoutChangeStyle()
    },

    loadingTime() {
      // 启动定时器
      var str = `<div>${this.$t('register.prependResend')} ${this.countdown} ${this.$t('register.appendResend')} </div>`
      $('#sendCode').html(str)
      this.countdown-- // 定时器减1
    },
    timeoutChangeStyle() {
      // 启动定时器
      this.loadingTime()
      this.timer = setInterval(() => {
        // 创建定时器
        if (this.countdown === 0) {
          this.clearTimer() // 关闭定时器
        } else {
          this.loadingTime()
        }
      }, 1000)
    },
    clearTimer() {
      // 清除定时器
      clearInterval(this.timer)
      this.timer = null
      this.countdown = 60
      $('#sendCode').html(`<div>${this.$t('register.getCode')}</div>`)
    },
    cancel() {
      if (this.isNotAutoWindow && this.isNotAutoWindow != null) {
        clearLoginInfo()
        this.$router.replace({name: 'login'})
      } else {
        this.visible = false
      }
    },
    // 修改密码
    dataFormSubmit() {
      if (!this.isVerify){
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl('/sys/updatePWD'),
              method: 'post',
              data: this.$http.adornData({
                'code': this.formCode.code,
                'password': this.$encruption(this.dataForm.newPassword),
                'username': this.resData.username,
                'flag': this.formCode.flag
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 1,
                  onClose: () => {
                    this.visible = false
                    this.$nextTick(() => {
                      clearLoginInfo()
                      this.clearTimer()
                      this.$router.replace({name: 'login'})
                    })
                  }
                })
              } else {
                this.$Message.error(data.msg)
                this.formCode.code = ''
                this.dataForm.password = ''
              }
            })
          }
        })
      }else {
          this.$refs['dataForm'].validate((valid) => {
            if (valid) {
              this.$http({
                url: this.$http.adornUrl('/sys/user/password'),
                method: 'post',
                data: this.$http.adornData({
                  'password': this.$encruption(this.dataForm.password),
                  'newPassword': this.$encruption(this.dataForm.newPassword)
                })
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.$Message.success({
                    content: this.$t('common.operationSuccessful'),
                    duration: 1,
                    onClose: () => {
                      this.visible = false
                      this.$nextTick(() => {
                        clearLoginInfo()
                        this.clearTimer()
                        this.$router.replace({ name: 'login' })
                      })
                    }
                  })
                } else {
                  this.$Message.error(data.msg)
                }
              })
            }
          })
      }

    },
    // dataFormSubmit () {
    //   this.$refs['dataForm'].validate((valid) => {
    //     if (valid) {
    //       this.$http({
    //         url: this.$http.adornUrl('/sys/user/password'),
    //         method: 'post',
    //         data: this.$http.adornData({
    //           'password': this.dataForm.password,
    //           'newPassword': this.dataForm.newPassword
    //         })
    //       }).then(({data}) => {
    //         if (data && data.code === 0) {
    //           this.$Message.success({
    //             content: this.$t('common.operationSuccessful'),
    //             duration: 1,
    //             onClose: () => {
    //               this.visible = false
    //               this.$nextTick(() => {
    //                 clearLoginInfo()
    //                 this.clearTimer()
    //                 this.$router.replace({ name: 'login' })
    //               })
    //             }
    //           })
    //         } else {
    //           this.$Message.error(data.msg)
    //         }
    //       })
    //     }
    //   })
    // }
  },
  computed: {
    userInfo: {
      get() {
        return this.$store.state.user.userInfo
      }
    },
    dataRule: {
      get() {
        return {
          password: [
            {required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur'}
          ],
          newPassword: [
            {required: true, message: this.$t('validate.new_pwd_cannot_empty'), trigger: 'blur'},
            {
              validator: (rule, value, callback) => {
                if (!PWDLenght(value)) {
                  callback(new Error(this.$t('login.passwordMore8')))
                } else {
                  callback()
                }
              }, trigger: 'blur'
            }
          ],
          confirmPassword: [
            {required: true, message: this.$t('validate.confirm_password_cannot_empty'), trigger: 'blur'},
            {
              validator: (rule, value, callback) => {
                if (this.dataForm.newPassword !== value) {
                  callback(new Error(this.$t('validate.the_new_password_is_inconsistent')))
                } else {
                  callback()
                }
              }, trigger: 'blur'
            }
          ]
        }
      }
    }
  }
}
</script>
