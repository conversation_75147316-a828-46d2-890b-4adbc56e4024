<template>
    <Modal v-model="visible" width="400" :closable="false"  :mask-closable="false">
        <p slot="header" style="text-align:center">
            <span>{{$t('common.personalSettings')}}</span>
        </p>
        <Form ref="dataForm">
          <Alert v-if="validate===0" style="display: inline-block" type="warning" show-icon>
            <p>{{$t('validate.validateTip')}}</p>
          </Alert>
            <FormItem>
              <Select style="width:300px;" v-model="validate" transfer :placeholder="$t('sys.authenticationMode')">
                  <Option v-for="item in validateList" :value="item.id" :key="item.id">{{ $t(item.name) }}</Option>
              </Select>
            </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.update')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      validate: -1,
      validateList: [
        // {id: 0, name: 'screen.null'},
        {id: 1, name: 'sys.emailVerification'},
        {id: 2, name: 'sys.mobileVerification'},
      ]
    }
  },
  methods: {
    // 初始化
    init () {
      this.visible = true
      this.validate = this.userInfo.validate
      // if (this.validate===0){
      //   this.validate=-1
      // }
    },
    dataFormSubmit () {
      if (this.validate===1||this.validate===2){
        this.$http({
          url: this.$http.adornUrl('/sys/user/updateValidate'),
          method: 'post',
          data: this.$http.adornData(this.validate, false)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 1
            })
            this.$emit('refreshData')
            this.visible = false
          } else {
            this.$Message.error(data.msg)
          }
        })
      }else {
        this.$Message.error(this.$t('sys.authenticationMode'))
      }

    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>
