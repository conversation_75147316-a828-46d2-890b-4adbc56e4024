<template>
  <div class="modiles-role">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(true)">
      <FormItem>
        <Input size="large" v-model="dataForm.announcementName" :placeholder="$t('announcement.titleText')"></Input>
      </FormItem>
       <FormItem>
        <Button v-if="isAuth('sys:announcement:list')" style="margin-right:6px" @click="getDataList(true)" size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button v-if="isAuth('sys:announcement:save')" style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
          <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
        </Button>
        <Button v-if="isAuth('sys:announcement:delete')" style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
      <template slot-scope="{ row, index }" slot="operation">
          <Button v-if="isAuth('sys:announcement:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.annoId)">{{$t('common.update')}}</Button>
          <Button v-if="isAuth('sys:announcement:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.annoId)">{{$t('common.delete')}}</Button>
      </template>
<!--      <template slot-scope="{ row, index }" slot="text" v-html="row.text">-->
<!--        <div>{{row.text}}</div>-->
<!--      </template>-->
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './announcement-add-or-update'
export default {
  data () {
    return {
      dataForm: {
        announcementName: ''
      },
      dataConlums: [
        {type: 'selection', width: 60, align: 'center'},
        {title: 'ID', key: 'annoId', width: 80, align: 'center',tooltip: true},
        {title: this.$t('announcement.title'), key: 'title', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('announcement.title'))
          }
        },
        {title: this.$t('announcement.content'), key: 'partContent', align: 'center',width: 400, tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('announcement.content'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.initData()
    this.getDataList()
  },
  methods: {
    initData () {
      this.dataForm = {
        announcementName: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
    },
    // 获取数据列表
    getDataList (flag) {
      if (flag && flag === true) {
        this.pageIndex = 1
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/sys/Announcement/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'titleText': this.dataForm.announcementName
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(flag)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) { //获得id数组或单个id数组
      var ids = id ? [id] : this.dataListSelections.map(item => {// 选中的对象集合
        return item.annoId
      })
        this.$http({
          url: this.$http.adornUrl('/sys/Announcement/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$Message.success({
              content: this.$t('common.operationSuccessful'),
              duration: 0.5,
              onClose: () => {
                if (this.pageIndex != 1 && this.dataList.length === ids.length) {
                  this.pageIndex--
                }
                this.getDataList()
                this.dataListSelections = []
              }
            })
          } else {
            this.$Message.error(data.msg)
          }
        })
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    }
  }
}
</script>

<style>

</style>
