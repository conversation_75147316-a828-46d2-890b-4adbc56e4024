<template>
  <Modal v-model="visible" width=90%>
    <p slot="header" style="text-align:center">
      <span>{{!dataForm.id ? $t('common.newlyBuild') : $t('common.update')}}</span>
    </p>
    <Form ref="dataForm" :model="dataForm" :rules="dataRule" :label-width="80" label-position="left">
      <FormItem prop="title" :label="$t('announcement.title')">
        <Input size="large" v-model="dataForm.title" maxlength="22" :placeholder="$t('announcement.enterTitle')" />
      </FormItem>
      <!-- <FormItem prop="text" :label="$t('announcement.content')"> -->
      <!-- <div ref="wangEditor1" id="wangEditor1"></div>
            <div ref="wangEditor2" id="wangEditor2" ></div> -->
      <div style="border: 1px solid #ccc;">
        <Toolbar style="border-bottom: 1px solid #ccc;position: relative;z-index: 100000;" :editor="editor"
          :defaultConfig="toolbarConfig" :mode="mode" />
        <Editor style="height: 500px !important; overflow-y: hidden;" v-model="html" :defaultConfig="editorConfig" :mode="mode"
          @onCreated="onCreated" @onChange="onChange" />
      </div>
      <!-- </FormItem> -->
    </Form>
    <div slot="footer">
      <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
      <Button type="primary" :loading="loading" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
    </div>
  </Modal>
</template>

<script>
import E from "wangeditor";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
export default {
  components: { Editor, Toolbar },
  data() {
    return {
      // editor: null,
      textMaxLength: 300,
      visible: false,
      menuList: [],
      loading: false,
      content: "",
      dataForm: {
        id: 0,
        title: '',
        text: '',
        partContent: '',
        infoId:''
      },
      picIdArr: [],
      picIdArrAll: [],
      picId: '',
      editor: null,
      html: "",
      toolbarConfig: {
        // toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
        // excludeKeys: [ /* 隐藏哪些菜单 */ ],
      },
      editorConfig: {
        placeholder: "请输入内容...",
        // autoFocus: false,

        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
          uploadImage: this.uploadImage(),
        },
      },
      mode: 'default',
    }
  },
  mounted() {
    // this.setcse();
  },
  methods: {
    // 初始化
    init(id) {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      this.visible = true
      this.dataForm.id = id || 0
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(`/sys/Announcement/one/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataForm.id = data.msg.SysAnn.annoId
            this.dataForm.title = data.msg.SysAnn.title
            this.dataForm.infoId=data.msg.SysAnn.infoId
            // this.dataForm.text = data.msg.SysAnn.text
            // this.editor.txt.html(data.msg.SysAnn.text);
            this.html = data.msg.SysAnn.text;
            // this.picIdArr = data.msg.SysAnn.picIdArr;
            this.picId = data.msg.SysAnn.picId;
            this.picIdArrAll = data.msg.SysAnn.picIdArr;
          }
        })
      } else {
        this.html = "";
        this.picIdArr = [];
        this.picId = "";
        this.picIdArrAll = [];
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.picIdArr = [];
      console.log(this.editor.getElemsByType('image'));
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let imagelist = this.editor.getElemsByType('image');
          if (imagelist) {
            imagelist.forEach(element => {
              this.picIdArr.push(element.alt);
            });
          }
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(`/sys/Announcement/${this.dataForm.id === 0 ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'annoId': this.dataForm.id===0 ? null:this.dataForm.id,
              'title': this.dataForm.title,
              'text': this.dataForm.text,
              'partContent': this.dataForm.partContent,
              "picIdArr": this.picIdArr,
              "picId": this.picId,
              "picIdArrAll": this.picIdArrAll,
              "infoId": this.dataForm.infoId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', this.dataForm.id === 0 ? true : '')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    },

    uploadImage() {
      var that = this;
      var uploadimage = {
        server: this.$http.adornUrl('/sys/Announcement/upload'),
        fieldName: "file",
        headers: { 'token': this.$cookie.get('token') },
        timeout: 20 * 1000,
        customInsert(res, insertFn) {
          console.log(res);
          insertFn(res.data.url, res.data.fileId);
          that.picIdArrAll.push(res.data.fileId);
        }
      }
      return uploadimage;
    },

    onCreated(editor) {
      this.editor = Object.seal(editor) // 【注意】一定要用 Object.seal() 否则会报错
    },
    onChange(editor) {
      this.dataForm.text = editor.getHtml();
      this.dataForm.partContent = editor.getText();
      // console.log(editor.getText());
      // console.log('onChange', editor.getHtml()) // onChange 时获取编辑器最新内容
    },


    setcse: function () {
      let that = this
      //文本编辑器的初始化
      this.editor = new E(this.$refs.wangEditor1);
      // 配置菜单栏，设置不需要的菜单
      this.editor.config.excludeMenus = [
        'backColor',
        'video',
        'todo',
        'code',
        'table'
      ]

      this.editor.config.uploadImgShowBase64 = true
      this.editor.config.uploadImgMaxLength = 1 // 一次最多上传 1 个图片
      this.editor.config.showFullScreen = false;
      this.editor.config.showLinkImg = false
      this.editor.create();
      this.editor.config.onchange = function (newHtml) {
        that.dataForm.text = newHtml
        that.dataForm.partContent = that.editor.txt.text().replace(/&nbsp;/g, " ")
      }

    },
  },
  computed: {
    dataRule: function () {
      return {
        title: [
          { required: true, message: this.$t('announcement.enterTitle'), trigger: 'blur' }
        ],
        text: [
          { required: true, message: this.$t('announcement.enterContent'), trigger: 'blur' }
        ]
      }
    },
  },
  watch: {
    // 'visible': function (newVal, oldVal) {
    //   if (newVal === false) {
    //     this.dataForm.id = 0
    //     this.dataForm.title = ''
    //     this.dataForm.text = ''
    //     this.dataForm.partContent = ''
    //     this.editor.txt.html("");
    //   }
    // }
  },

  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
}
</script>

<style>
#wangEditor2 {
  width: 100%;
  height: 118px;
  padding-bottom: 5px;
}

#wangEditor1 {
  width: 100%;
  height: 118px;
  padding-bottom: 5px;
}

.w-e-text-container {
  background: #000 !important;
  color: #fff !important;
  z-index: 1 !important;
}

/* .w-e-bar-item,
.w-e-bar-item button {
  align-items: center;
  justify-content: center;
}

.w-e-bar-item {
  display: flex;
  height: 40px;
  padding: 4px;
  position: relative;
  text-align: center;
}

.w-e-bar svg {
  fill: var(--w-e-toolbar-color);
  height: 14px;
  width: 14px;
} */
</style>
