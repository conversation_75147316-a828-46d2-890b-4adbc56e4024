<template>
    <div class="modiles-approval">
        <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
            <FormItem>
                <Input size="large" v-model="dataForm.name" :placeholder="$t('approval.auditName')"></Input>
            </FormItem>
            <FormItem>
                <Button style="margin-right:6px" @click="getDataList()"  size="large">
                <div style="margin:3px 8px">{{$t('common.query')}}</div>
                </Button>
                <Button v-if="isAuth('sys:approval:save')" style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
                <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
                </Button>
                <Button v-if="isAuth('sys:approval:delete')" style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
                <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
                </Button>
            </FormItem>
        </Form>
        <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
            :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
            <template slot-scope="{ row, index }" slot="type">
              <div v-if="row.type === 0">{{$t('approval.mediaResources')}}</div>
              <div v-if="row.type === 1">{{$t('approval.ProgramType')}}</div>
              <div v-if="row.type === 2">{{$t('approval.BroadcastMediaResources')}}</div>
              <div v-if="row.type === 3">{{$t('approval.BroadcastTaskResources')}}</div>
            </template>
            <template slot-scope="{ row, index }" slot="status">
              <Tag color="blue" v-if="row.status === 0">{{$t('sys.enable')}}</Tag>
              <Tag color="red" v-if="row.status === 1">{{$t('common.disable')}}</Tag>
            </template>
            <template slot-scope="{ row, index }" slot="operation">
                <Button v-if="isAuth('sys:approval:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.examineId)">{{$t('common.update')}}</Button>
                <Button v-if="isAuth('sys:approval:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.examineId)">{{$t('common.delete')}}</Button>
            </template>
        </Table>
        <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
            @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
</template>

<script>
import AddOrUpdate from './approval-add-or-update'
export default {
  data () {
    return {
      dataForm: {
        name: ''
      },
      dataConlums: [
        {type: 'selection', width: 60, align: 'center'},
        {title: 'ID', key: 'examineId', width: 80, align: 'center'},
        {title: this.$t('approval.auditName'), key: 'examineName', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('approval.auditName'))
          }
        },
        {title: this.$t('approval.auditType'), key: 'type', slot: 'type', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('approval.auditType'))
          }
        },
        {title: this.$t('common.state'), key: 'status', slot: 'status', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.state'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.initData()
    this.getDataList()
  },
  methods: {
    initData () {
      this.dataForm = {
        name: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
    },
    // 查询数据
    getDataList (flag) {
      if (flag && flag === true) {
        this.pageIndex = 1
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/sys/approval/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 添加或修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除与批量删除
    deleteHandle (id) {
      var approvalIds = id ? [id] : this.dataListSelections.map(item => {
        return item.examineId
      })
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/sys/approval/delete'),
            method: 'post',
            data: this.$http.adornData(approvalIds, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === approvalIds.length) {
                    this.pageIndex--
                  }
                  this.getDataList()
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    }
  }
}
</script>
