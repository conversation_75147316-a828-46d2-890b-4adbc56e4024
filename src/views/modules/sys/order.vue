<template>
  <div class="modules_order">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <!-- <FormItem>
        <Input size="large" v-model="dataForm.name" :placeholder="$t('group.name')"></Input>
      </FormItem> -->
        <FormItem>
            <Select size="large"  v-model="dataForm.userId" :placeholder="$t('common.PleaseSelect') + $t('login.username')" filterable clearable transfer>
                <Option v-for="item in userList" :value="item.userId" :key="item.username">{{ item.username }}</Option>
            </Select>
        </FormItem>
        <FormItem>
            <Select size="large"  v-model="dataForm.purchaseStatus" :placeholder="$t('common.PleaseSelect') + $t('common.state')" filterable clearable transfer>
                <Option v-for="item in purchaseStatusList" :value="item.value" :key="item.value">{{ $t(item.name) }}</Option>
            </Select>
        </FormItem>
        <FormItem>
            <Select size="large"  v-model="dataForm.tradeStatus" :placeholder="$t('common.PleaseSelect') + $t('pay.PaymentStatus')" filterable clearable transfer>
                <Option v-for="item in tradeStatusList" :value="item.value" :key="item.value">{{ $t(item.name) }}</Option>
            </Select>
        </FormItem>
        <FormItem>
            <Button style="margin-right:6px" @click="getDataList(1)"  size="large">
                <div style="margin:3px 8px">{{$t('common.query')}}</div>
            </Button>
            <Button v-if="isAuth('sys:order:delete')" style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
                <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
            </Button>
        </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
        <template slot-scope="{ row, index }" slot="tradeStatus">
            <span v-if="row.tradeStatus === 'CREATE_ORDER'">{{$t('pay.unpaid')}}</span>
            <span v-else-if="row.tradeStatus === 'WAIT_BUYER_PAY'">{{$t('pay.transactionCreation')}}</span>
            <span v-else-if="row.tradeStatus === 'BANK_RECEIPT_ERROR'">{{$t('pay.ErrorUploadingBankReceipt')}}</span>
            <span v-else-if="row.tradeStatus === 'TRADE_CLOSED'">{{$t('pay.UnpaidTransactionTimeoutClosed')}}</span>
            <span v-else-if="row.tradeStatus === 'WAIT_SELLER_CONFIRM'">{{$t('pay.WaitingForSellerToConfirm')}}</span>
            <span v-else-if="row.tradeStatus === 'TRADE_SUCCESS'">{{$t('pay.paymentSuccessful')}}</span>
            <!-- <span v-else-if="row.tradeStatus === 'TRADE_SUCCESS' || row.tradeStatus === 'TRADE_FINISHED'">{{$t('pay.paymentSuccessful')}}</span> -->
        </template>
        <template slot-scope="{ row, index }" slot="vipStatus">
            <span v-if="row.vipStatus === 1">{{$t('pay.silverCardVip')}}</span>
            <span v-else-if="row.vipStatus === 2">{{$t('pay.goldCardVip')}}</span>
            <span v-else-if="row.vipStatus === 3">{{$t('pay.diamondVip')}}</span>
            <span v-else-if="row.vipStatus === 4">{{$t('pay.vip4')}}</span>
        </template>
        <template slot-scope="{ row, index }" slot="month">
            {{(row.month / 12).toFixed(2)}} {{$t('program.year')}}
        </template>
        <template slot-scope="{ row, index }" slot="totalAmount">
            $ {{row.totalAmount | filterTotalPrice}} （
            <span v-if="row.purchaseStatus == 1">{{$t('pay.newPurchase')}}</span>
            <span v-else-if="row.purchaseStatus == 2">{{$t('pay.Renewal')}}</span>
            <span v-else-if="row.purchaseStatus == 3">{{$t('pay.upgrade')}}</span>
            ）
          </template>
        <template slot-scope="{ row, index }" slot="operation">
            <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" @click="processHandle(row.outTradeNo)">{{$t('approval.approvalProcess')}}</Button>
            <Button v-if="isAuth('sys:order:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="updateHandle(row.outTradeNo)">{{$t('file.examine')}}</Button>
            <Button v-if="isAuth('sys:order:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.outTradeNo)">{{$t('common.delete')}}</Button>
        </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <order-process v-if="processVisible" ref="process" @refreshDataList="getDataList"></order-process>
  </div>
</template>

<script>
import AddOrUpdate from './order-add-or-update'
import orderProcess from './order-process'
export default {
    data() {
        return {
            dataForm: {
                userId: null,
                purchaseStatus: null,
                tradeStatus: null
            },
            dataConlums: [
                {type: 'selection',fixed: 'left', width: 60, align: 'center'},
                {
                    title: this.$t('pay.orderNumber'),
                    key: 'outTradeNo',
                    width: '150',
                    fixed: 'left',
                    align: 'center',
                    renderHeader:(h)=>{
                    return h('div',this.$t('pay.orderNumber'))
                    }
                },
                {
                    title: this.$t('login.username'),
                    key: 'username',
                    width: '130',
                    align: 'center',
                    renderHeader:(h)=>{
                    return h('div',this.$t('login.username'))
                    }
                },
                {
                    title: this.$t('pay.PaymentStatus'),
                    key: 'tradeStatus',
                    slot: 'tradeStatus',
                    align: 'center',
                    width: '160',
                    renderHeader:(h)=>{
                    return h('div',this.$t('pay.PaymentStatus'))
                    }
                },
                {
                    title: this.$t('common.state'),
                    key: 'vipStatus',
                    slot: 'vipStatus',
                    align: 'center',
                    width: '100',
                    renderHeader:(h)=>{
                    return h('div',this.$t('common.state'))
                    }
                },
                {
                    title: this.$t('pay.Years'),
                    key: 'month',
                    slot: 'month',
                    align: 'center',
                    width: '100',
                    renderHeader:(h)=>{
                        return h('div',this.$t('pay.Years'))
                    }
                },
                {
                    title: this.$t('pay.amount'),
                    key: 'totalAmount',
                    slot: 'totalAmount',
                    width: '180',
                    align: 'center',
                    renderHeader:(h)=>{
                        return h('div',this.$t('pay.amount'))
                    }
                },
                {
                    title: this.$t('common.createTime'),
                    key: 'createTime',
                    align: 'center',
                    width: '180',
                    renderHeader:(h)=>{
                        return h('div',this.$t('common.createTime'))
                    }
                },
                {
                    title: this.$t('pay.companyName'),
                    key: 'companyName',
                    align: 'center',
                    width: '180',
                    renderHeader:(h)=>{
                        return h('div',this.$t('pay.companyName'))
                    }
                },
                {
                    title: this.$t('pay.address'),
                    key: 'address',
                    align: 'center',
                    width: '180',
                    renderHeader:(h)=>{
                        return h('div',this.$t('pay.address'))
                    }
                },
                {
                    title: this.$t('pay.contactPerson'),
                    key: 'contactPerson',
                    align: 'center',
                    width: '180',
                    renderHeader:(h)=>{
                        return h('div',this.$t('pay.contactPerson'))
                    }
                },
                {
                    title: this.$t('pay.telephone'),
                    key: 'telephone',
                    align: 'center',
                    width: '180',
                    renderHeader:(h)=>{
                        return h('div',this.$t('pay.telephone'))
                    }
                },
                {
                    title: this.$t('common.operation'),
                    slot: 'operation',
                    align: 'center',
                    fixed: 'right',
                    width: '280',
                    renderHeader:(h)=>{
                        return h('div',this.$t('common.operation'))
                    }
                },
            ],
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataListSelections: [],
            userList: [],
            purchaseStatusList: [
                {value: 1, name: "pay.newPurchase"},
                {value: 2, name: "pay.Renewal"},
                {value: 3, name: "pay.upgrade"},
            ],
            tradeStatusList: [
                {value: 'CREATE_ORDER', name: "pay.unpaid"},
                {value: 'WAIT_BUYER_PAY', name: "pay.transactionCreation"},
                {value: 'BANK_RECEIPT_ERROR', name: "pay.ErrorUploadingBankReceipt"},
                {value: 'TRADE_CLOSED', name: "pay.UnpaidTransactionTimeoutClosed"},
                {value: 'WAIT_SELLER_CONFIRM', name: "pay.WaitingForSellerToConfirm"},
                {value: 'TRADE_SUCCESS', name: "pay.paymentSuccessful"},
            ],
            addOrUpdateVisible: false,
            processVisible: false
        }
    },
    components: {
        AddOrUpdate,
        orderProcess
    },
    activated () {
        this.initData()
        this.getUserList()
        this.getDataList()
    },
    methods: {
        initData () {
            this.dataForm = {
                userId: null,
                purchaseStatusList: null,
                tradeStatus: null
            }
            this.pageIndex = 1
            this.pageSize = 10
            this.totalPage = 0
            this.dataListSelections = []
        },
        // 获取数据列表
        getDataList (isQuery) {
            this.dataListLoading = true
          if (isQuery===1){
            this.pageIndex=1
          }
            this.$http({
                url: this.$http.adornUrl('/sys/order/list'),
                method: 'get',
                params: this.$http.adornParams({
                    'page': this.pageIndex,
                    'limit': this.pageSize,
                    'userId': this.dataForm.userId,
                    'purchaseStatus': this.dataForm.purchaseStatus,
                    'status': this.dataForm.tradeStatus,
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.page.list
                    this.totalPage = data.page.totalCount
                  //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
                  if (data.page.currPage>this.totalPage&&this.totalPage!==0){
                    this.pageIndex=1
                    this.getDataList(isQuery)
                  }
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListSelections = []
                this.dataListLoading = false
            })
        },
        getUserList () {
            this.$http({
                url: this.$http.adornUrl('/sys/user/select'),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.userList = data.userList
                }
            })
        },
        // 每页数
        sizeChangeHandle (val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle (val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle () {
            this.dataListSelections = this.$refs.selection.getSelection()
        },
        selectThisRow(data, index) {
            this.$refs.selection.toggleSelect(index);
        },
        // 修改
        updateHandle (id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        // 审核流程
        processHandle (id) {
            this.processVisible = true
            this.$nextTick(() => {
                this.$refs.process.init(id)
            })
        },
        // 删除
        deleteHandle (id) {
            var orderNos = id ? [id] : this.dataListSelections.map(item => {
                return item.outTradeNo
            })
            this.$Modal.confirm({
                title: this.$t('common.tips'),
                content: this.$t('common.delete_current_option'),
                okText: this.$t('common.confirm'),
                cancelText: this.$t('common.cancel'),
                onOk: () => {
                    this.$http({
                        url: this.$http.adornUrl('/sys/order/delete'),
                        method: 'post',
                        data: this.$http.adornData(orderNos, false)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                        this.$Message.success({
                            content: this.$t('common.operationSuccessful'),
                            duration: 0.5,
                            onClose: () => {
                            if (this.pageIndex != 1 && this.dataList.length === groupIds.length) {
                                this.pageIndex--
                            }
                            this.getDataList()
                            this.dataListSelections = []
                            }
                        })
                        } else {
                            this.$Message.error(data.msg)
                        }
                    })
                }
            })
        }
    },
    computed: {
        tableHeight: {
        get () { return this.$store.state.common.tableHeight }
        }
    },
    filters: {
        filterTotalPrice(val) {
            return val.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
        },
    }
}
</script>

<style>

</style>
