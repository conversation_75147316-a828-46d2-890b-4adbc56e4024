<template>
    <Modal v-model="visible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{!dataForm.examineId ? $t('common.newlyBuild') : $t('common.update')}}</span>
        </p>
         <Alert type="warning" show-icon closable><b class="tip">{{$t('tips.approval')}}</b></Alert>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 300px;" :label-width="80" label-position="left" inline
        @keyup.enter.native="dataFormSubmit()">
          <FormItem prop="examineName" :label="$t('approval.auditName')">
            <Input size="large" v-model="dataForm.examineName"
            :placeholder="$t('common.PleaseInput') + $t('approval.auditName')"/>
          </FormItem>
          <br/>
          <FormItem :label="$t('approval.approvalProcess')">
            <Button icon="md-add-circle" size="small" type="dashed" @click="headlerAdd()"></Button>
            <div v-if="dataForm.process && dataForm.process.length > 0">
              <span style="margin-right: 130px;">{{$t('approval.Reviewer')}}</span>
              <span style="margin-right: 30px;">{{$t('approval.order')}}</span>
              <span>{{$t('common.operation')}}</span>
            </div>
            <div v-if="dataForm.process && dataForm.process.length > 0" v-for="(item,index) in dataForm.process" :key="index">
              <Select label-in-value v-model="item.userId" size="small" style="width: 150px" transfer :placeholder="$t('common.PleaseSelect') + $t('login.username')">
                <Option :value="user.userId" v-for="(user,index) in showUserList(item.userId)" :key="index">
                  {{user.username}}
                </Option>
              </Select>
              <InputNumber disabled :max="15" :min="1" v-model="item.processOrder" size="small" style="width: 80px"></InputNumber>
              <Button icon="md-trash" shape="circle" size="small" @click="dataForm.process.splice(index,1)"></Button>
            </div>
          </FormItem>
          <br/>
          <FormItem prop="type" :label="$t('approval.auditType')">
            <Select transfer v-model="dataForm.type" size="large" :placeholder="$t('common.PleaseSelect') + $t('approval.auditType')">
              <Option v-for="item in resourceType" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
            </Select>
          </FormItem>
          <br/>
          <FormItem :label="$t('common.state')">
            <RadioGroup v-model="dataForm.status">
              <Radio :label="0"> <span>{{$t('sys.enable')}}</span> </Radio>
              <Radio :label="1"> <span>{{$t('common.disable')}}</span> </Radio>
            </RadioGroup>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" :loading="loading" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      resourceType: [
        { value: '0', label: 'approval.mediaResources' },
        { value: '1', label: 'approval.ProgramType' },
        { value: '2', label: 'approval.BroadcastMediaResources' },
        { value: '3', label: 'approval.BroadcastTaskResources' }
      ],
      dataForm: {
        examineId: 0,
        examineName: '',
        type: '',
        status: 1,
        process: []
      },
      order: 0,
      userList: [],
    }
  },
  methods: {
    // 初始化
    init (examineId) {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      this.$http({
        url: this.$http.adornUrl('/sys/user/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userList = data.userList
          this.visible = true
        }
      }).then(() => {
        this.dataForm.examineId = examineId || 0
        if (this.dataForm.examineId) {
          this.$http({
            url: this.$http.adornUrl(`/sys/approval/info/${this.dataForm.examineId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm.examineName = data.info.examineName
              this.dataForm.type = data.info.type.toString()
              this.dataForm.process = data.info.processList
              this.dataForm.status = data.info.status
              this.order = this.dataForm.process[this.dataForm.process.length - 1].processOrder
            }
          })
        }
      })
    },
    // 添加流程
    headlerAdd () {
      if (this.userList.length > this.dataForm.process.length) {
        this.order++
        const process = {
          userId: '',
          processOrder: this.order,
          username: ''
        }
        this.dataForm.process.push(process)
      } else {
        this.$Message.error(this.$t('approval.InsufficientUsers'))
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.process.length <= 0) {
            this.$Message.error(this.$t('approval.select_at_least_one_reviewer'))
          } else {
            var userId = this.dataForm.process.filter(item => item.userId)
            var processOrder = this.dataForm.process.filter(item => item.processOrder)
            // 如果添加了userId不能为空
            if (userId.length !== this.dataForm.process.length) {
              this.$Message.error(this.$t('approval.approver_cannot_blank'))
            } else if (processOrder.length !== this.dataForm.process.length) { // 如果添加了processOrder不能为空
              this.$Message.error(this.$t('approval.approval_order_cannot_blank'))
            } else {
              this.loading = true
              this.$http({
                url: this.$http.adornUrl(`/sys/approval/${!this.dataForm.examineId ? 'save' : 'update'}`),
                method: 'post',
                data: this.$http.adornData(this.dataForm)
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.loading = false
                  this.$Message.success({
                    content: this.$t('common.operationSuccessful'),
                    duration: 0.5,
                    onClose: () => {
                      this.visible = false
                      this.$emit('refreshDataList', !this.dataForm.examineId ? true : '')
                    }
                  })
                } else {
                  this.$Message.error({
                    content: data.msg,
                    onClose: () => {
                      setTimeout(() => {
                        this.loading = false
                      }, 200)
                    }
                  })
                }
              })
            }
          }
        }
      })
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.dataForm = {
            examineId: 0,
            examineName: '',
            type: '',
            status: 1,
            process: []
          }
          this.order = 0
        }, 200)
      }
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          examineName: [
            { required: true, message: this.$t('validate.audit_name_cannot_empty'), trigger: 'blur' }
          ],
          type: [
            { required: true, message: this.$t('validate.resource_type_cannot_empty'), trigger: 'change' }
          ]
        }
      }
    },
    showUserList () {
      return (val) => {
        // 将option的显示数据进行深拷贝
        let newList = JSON.parse(JSON.stringify(this.userList))

        // 处理dataForm.process数据，返回一个新数组arr
        // arr数组就相当于所有Select选中的数据集合（没有选中的为''，不影响判断），只要在这个集合里面，其他的下拉框就不应该有这个选项
        const arr = this.dataForm.process.map(item => {
          // 将其格式{userId：1}变成[1],方便使用indexOf进行判断
          return (item = item.userId)
        })

        // 过滤出newList里面需要显示的数据
        newList = newList.filter(item => {
          // 当前下拉框的选中的数据需要显示
          // val就是当前下拉框选中的值
          if (val === item.userId) {
            return item
          } else {
            //  再判断在arr这个数组中是不是有这个数据，如果不在，说明是需要显示的
            if (arr.indexOf(item.userId) === -1) {
              return item
            }
          }
        })

        // 返回Options显示数据
        return newList
      }
    }
  }
}
</script>
<style scoped>
.processHead {
  margin-right: 110px;
}
</style>
