<template>
    <Modal v-model="visible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{!dataForm.id ? $t('common.newlyBuild') : $t('common.update')}}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 400px;" :label-width="80"
        label-position="left" @keyup.enter.native="dataFormSubmit()">
          <FormItem prop="type" :label="$t('menu.type')">
            <RadioGroup v-model.number="dataForm.type">
              <Radio v-for="type in dataForm.typeList" :key="type.id" :label="type.id" @click.native="changeType(type.id)">{{$t(type.label)}}</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem prop="name" :label="$t('menu.name')">
            <Input size="large" v-model="dataForm.name"
            :placeholder="$t('common.PleaseInput') + $t('menu.name')"/>
          </FormItem>
          <FormItem prop="parentId" :label="$t('menu.parentName')" v-if="dataForm.type !== 4">
            <Select v-model="dataForm.parentId" size="large" filterable>
              <Option :value="item.menuId" :label="item.name" v-for="item in selectmenuList" :key="item.menuId">
                <span>{{ item.name }}</span>
                <span style="float:right;color:#ccc">{{$t('menu.parentMenuName')}}：{{ (item.parentName == null ? $t('menu.mainMenu') : item.parentName) }}</span>
              </Option>
            </Select>
          </FormItem>
          <FormItem prop="url" :label="$t('menu.url')" v-if="dataForm.type === 1" >
            <Input size="large" v-model="dataForm.url"
            :placeholder="$t('common.PleaseInput') + $t('menu.url')"/>
          </FormItem>
          <FormItem prop="perms" :label="$t('menu.perms')" v-show="dataForm.type === 1 || dataForm.type === 2 || dataForm.type === 3" >
            <Input size="large" v-model="dataForm.perms"
            :placeholder="$t('menu.permsTips')"/>
          </FormItem>
          <FormItem prop="orderNum" :label="$t('menu.orderNum')" v-if="dataForm.type !== 3" >
            <InputNumber size="large" v-model.number="dataForm.orderNum" :min="0"
            :placeholder="$t('common.PleaseInput') + $t('menu.orderNum')"/>
          </FormItem>
          <FormItem prop="icon" :label="$t('menu.icon')" v-show="dataForm.type === 0 || dataForm.type === 4">
            <Poptip trigger="focus" placement="bottom-start">
               <Input size="large" v-model="dataForm.icon"
              :placeholder="$t('common.PleaseInput') + $t('menu.icon')"/>
              <div slot="content" class="icon-inner">
                <div class="icon-list">
                  <Button @click="iconActiveHeadle(item)" style="display: inline-block;" class="icon-button" v-for="(item, index) in iconList" :key="index">
                    <svg class="icon" width="15px" height="15px" aria-hidden="true">
                      <use :xlink:href="'#' + item"></use>
                    </svg>
                  </Button>
                </div>
              </div>
            </Poptip>
              <Alert closable>{{$t('common.use_iconfont')}}：<a target="_blank" href="http://www.iconfont.cn/">iconfont</a></Alert>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
import { selectMenus } from '@/utils'
import Icon from '@/icons'
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {
        id: 0,
        type: 1,
        typeList: [
          {id: 0, label: 'menu.mainMenu'},
          {id: 4, label: 'menu.HomeDirectoryMenu'},
          {id: 1, label: 'menu.menu'},
          {id: 2, label: 'menu.DirectoryMenu'},
          {id: 3, label: 'menu.button'},
        ],
        name: '',
        parentId: '',
        url: '',
        perms: '',
        orderNum: 0,
        icon: ''
      },
      menuList: [],
      selectmenuList: [],
      iconList: []
    }
  },
  created () {
    this.iconList = Icon.getNameList()
  },
  methods: {

    // 初始化
    init (id) {
      this.dataForm.id = id || 0
      this.$http({
        url: this.$http.adornUrl('/sys/menu/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.menuList = data.menuList
          this.selectmenuList = selectMenus(data.menuList, this.dataForm.type)
        }
      }).then(() => {
        if (this.dataForm.id) {
          // 修改
          this.$http({
            url: this.$http.adornUrl(`/sys/menu/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm.id = data.menu.menuId
              this.dataForm.type = data.menu.type
              this.selectmenuList = selectMenus(this.menuList, this.dataForm.type)
              this.dataForm.name = data.menu.name
              this.dataForm.parentId = data.menu.parentId
              this.dataForm.url = data.menu.url
              this.dataForm.perms = data.menu.perms
              this.dataForm.orderNum = data.menu.orderNum
              this.dataForm.icon = data.menu.icon
            }
          })
        }
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
        })
      })
    },
    // 切换类型
    changeType (value) {
      this.selectmenuList = selectMenus(this.menuList, value)
      this.$refs['dataForm'].resetFields()
      if (value === 0 || value === 4) { // 主菜单/主目录菜单
        this.dataForm.parentId = 0
      } else { // 其他情况
        this.dataForm.parentId = ''
      }
      this.dataForm.orderNum = 0
    },
    // 图标选中
    iconActiveHeadle (iconName) {
      this.dataForm.icon = iconName
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(`/sys/menu/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'menuId': this.dataForm.id || undefined,
              'type': this.dataForm.type,
              'name': this.dataForm.name,
              'parentId': this.dataForm.parentId,
              'url': this.dataForm.url,
              'perms': this.dataForm.perms,
              'orderNum': this.dataForm.orderNum,
              'icon': this.dataForm.icon
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    }
  },
  computed: {
    dataRule: function () {
      return {
        name: [
          { required: true, message: this.$t('validate.menu_name_cannot_empty'), trigger: 'blur' }
        ],
        parentId: [
          { validator: (rule, value, callback) => {
            if ((this.dataForm.type !== 0 || this.dataForm.type !== 4) && !/\S/.test(value)) {
              console.log(this.dataForm.type)
              callback(new Error(this.$t('validate.superior_menu_cannot_empty')))
            } else {
              callback()
            }
          }, trigger: 'change' }
        ],
        url: [
          { validator: (rule, value, callback) => {
            if (this.dataForm.type === 1 && !/\S/.test(value)) {
              callback(new Error(this.$t('validate.menuURL_cannot_empty')))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
        if (newVal === false) {
          this.dataForm = {
            id: 0,
            type: 1,
            typeList: [
              {id: 0, label: 'menu.mainMenu'},
              {id: 4, label: 'menu.HomeDirectoryMenu'},
              {id: 1, label: 'menu.menu'},
              {id: 2, label: 'menu.DirectoryMenu'},
              {id: 3, label: 'menu.button'},
            ],
            name: '',
            parentId: '',
            url: '',
            perms: '',
            orderNum: 0,
            icon: ''
          }
        }
    }
  }
}
</script>
<style scoped>
.icon-inner {
  width: 300px;
  max-height: 150px;
}
.icon-list {
  width: 300px;
  height: 150px;
  white-space:normal;
  max-height: 150px;
  /* overflow-x: auto; */
  overflow-x: hidden;
  overflow-y: auto;
}
.icon-button {
  padding: 8px;
  margin: 8px 0 0 8px;
}
</style>
