<template>
  <div class="modiles-user">
    <Form :inline="true" :model="dataForm">
    <!-- <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)"> -->
      <FormItem>
        <Input size="large" v-model="dataForm.username" :placeholder="$t('login.UserNameEmailMobile')"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(1)"  size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button v-if="isAuth('sys:user:save')" style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
          <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
        </Button>
        <Button v-if="isAuth('sys:user:delete')" style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
      <template slot-scope="{ row, index }" slot="status">
          <Tag color="red" v-if="row.status === 0">{{$t('common.disable')}}</Tag>
          <Tag color="blue" v-if="row.status === 1">{{$t('common.normal')}}</Tag>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
          <Button v-if="isAuth('sys:user:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.userId)">{{$t('common.update')}}</Button>
          <Button v-if="isAuth('sys:user:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.userId)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './user-add-or-update'
export default {
  data () {
    return {
      isVerify: window.SITE_CONFIG.isVerify,
      dataForm: {
        username: '',
        group: ''
      },
      dataConlums: [
        {type: 'selection', width: 60, align: 'center'},
        {title: 'ID', key: 'userId', width: 80, align: 'center',tooltip: true},
        {title: this.$t('login.username'), key: 'username', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('login.username'))
          }
        },
        {title: this.$t('register.mailbox'), key: 'email', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('register.mailbox'))
          }
        },
        {title: this.$t('register.mobile'), key: 'mobile', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('register.mobile'))
          }
        },
        {title: this.$t('common.state'), key: 'status', slot: 'status', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.state'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      userDeplopGroupVisble: false,
      pwdVisible: false,
    }
  },
  components: {
    AddOrUpdate,
  },
  activated () {
    this.initData()
  },
  methods: {
    initData () {
      this.dataForm = {
        username: '',
        group: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
      if (this.isVerify && this.dataConlums.length > 6) {
        this.dataConlums.splice(3, 2)
      }
      this.getDataList()
    },
    // 获取数据列表
    getDataList (isQuery) {
      this.dataListLoading = true
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/sys/user/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'username': this.dataForm.username
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
          this.$Message.error(data.msg);
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var userIds = id ? [id] : this.dataListSelections.map(item => {
        return item.userId
      })
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/sys/user/delete'),
            method: 'post',
            data: this.$http.adornData(userIds, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === userIds.length) {
                    this.pageIndex--
                  }
                  this.getDataList()
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    //分配分组
    handleGroup (id) {
      var userIds = id ? [id] : this.dataListSelections.map(item => {
        return item.userId
      })
       this.userDeplopGroupVisble = true
      this.$nextTick(() => {
        this.$refs.userDeployGroup.init(userIds)
      })
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  }
}
</script>

<style>

</style>
