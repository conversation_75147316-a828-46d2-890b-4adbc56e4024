<template>
  <div class="modules_userInfo">
    <Card :style="{ width: '46%', height: documentClientHeight - 110 + 'px', float: 'left', }">
      <span class="userInfo_title">{{ this.$t("common.personalSettings") }}</span>
      <Divider/>
      <div class="userInfo_content">
        <!-- 用户名-->
        <div class="content">
          <div style="width: 100px; display: inline-block">
            <span class="content_title">{{ this.$t("login.username") }} : </span>
          </div>
          <span class="content_text" style="font-size: 20px">{{ userInfo.username }}</span>
        </div>
        <!-- 公司Id-->
        <div class="content">
          <div style="width: 120px; display: inline-block">
            <span class="content_title">{{ this.$t("operation.thecompany") }} ID : </span>
          </div>
          <span class="content_text" style="font-size: 20px">{{ userInfo.companyId }}</span>
        </div>
        <!-- LOGO -->
        <!-- <div class="content"> -->
          <!-- <div style="width: 100px; display: inline-block">
            <span class="content_title">LOGO : </span>
          </div>
          <span class="content_text" style="font-size: 20px"> -->
<!--            <span v-if="this.userInfo.iconUrl && this.userInfo.iconUrl.substr(0, 6) === 'aips40'">-->
<!--              <img :src="this.downloadUrl + this.userInfo.iconUrl + '?token=' + this.token" style="height: 40px;vertical-align: middle;"/>-->
              <!-- <img src="@/assets/img/aips40_2.png" style="height: 40px;vertical-align: middle;"/> -->
<!--            </span>-->
<!--            <span v-else>-->
<!--              <img :src="this.userInfo.iconUrl" style="height: 40px;vertical-align: middle;"/>-->
<!--            </span>-->
          <!-- </span>
          <Divider type="vertical" /> -->
<!--          <a href="javascript:void(0)" class="content_text" @click="updateIconUrlHandle()">-->
<!--            {{this.$t("common.update")}}-->
<!--          </a>-->
        <!-- </div> -->
        <!-- 密码-->
        <div class="content">
          <div style="width: 100px; display: inline-block">
            <span class="content_title"
              >{{ this.$t("login.password") }} :
            </span>
          </div>
          <Alert style="display: inline-block" type="success" show-icon>
            <span class="content_text">{{ this.$t("common.setSuccess") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updatePasswordHandle()">{{ this.$t("common.update") }}</a>
          </Alert>
        </div>
        <!-- 邮箱-->
        <div class="content" v-if="!isVerify">
          <div style="width: 100px; display: inline-block">
            <span class="content_title"
              >{{ this.$t("register.mailbox") }} :
            </span>
          </div>
          <Alert style="display: inline-block" type="success" show-icon v-if="userInfo.email">
            <span class="content_text">{{ this.$t("common.bindingSuccess") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateEmailHandle('updateEmail')">{{ this.$t("common.update") }}</a>
          </Alert>
          <Alert style="display: inline-block" type="warning" show-icon v-else>
            <span class="content_text">{{ this.$t("common.unbound") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateEmailHandle('bindEmail')">{{ this.$t("common.binding") }}</a>
          </Alert>
        </div>
        <!-- 手机号-->
        <div class="content" v-if="!isVerify">
          <div style="width: 100px; display: inline-block">
            <span class="content_title">{{ this.$t("register.mobile") }} : </span>
          </div>
          <Alert style="display: inline-block" type="success" show-icon v-if="userInfo.mobile">
            <span class="content_text">{{ this.$t("common.bindingSuccess") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateMobileHandle('updateMobile')">{{ this.$t("common.update") }}</a>
          </Alert>
          <Alert style="display: inline-block" type="warning" show-icon v-else>
            <span class="content_text">{{ this.$t("common.unbound") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateMobileHandle('bindMobile')">{{ this.$t("common.binding") }}</a>
          </Alert>
        </div>
        <!-- 是否开启验证-->
        <div class="content" v-if="!isVerify">
          <div style="width: 100px; display: inline-block">
            <span class="content_title">{{ this.$t("sys.verify") }}: </span>
          </div>
          <Alert style="display: inline-block" type="success" show-icon v-if="userInfo.validate === 1">
            <span class="content_text">{{ this.$t("sys.emailVerification") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateValidateHandle()">{{ this.$t("common.update") }}</a>
          </Alert>
          <Alert style="display: inline-block" type="success" show-icon v-else-if="userInfo.validate === 2">
            <span class="content_text">{{ this.$t("sys.mobileVerification") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateValidateHandle()">{{ this.$t("common.update") }}</a>
          </Alert>
          <Alert style="display: inline-block" type="warning" show-icon v-else>
            <span class="content_text">{{ this.$t("sys.notOpen") }}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateValidateHandle()">{{ this.$t("common.update") }}</a>
          </Alert>
        </div>
        <div v-if="userInfo.isAuth != 1 && !isVerify">
          <div style="width: 100px; display: inline-block">
            <span class="content_title">{{$t('userAuth.authInfo')}}: </span>
          </div>
          <Alert style="display: inline-block" type="warning" show-icon>
            <span class="content_text">{{userInfo.isAuth ==  2 ? this.$t('userAuth.updateAuth'): this.$t('userAuth.uploadAuth')}}</span>
            <Divider type="vertical" />
            <a href="javascript:void(0)" class="content_text" @click="updateAuthHandle(userInfo.isAuth)">{{ userInfo.isAuth ==  2 ? this.$t("common.update") : this.$t("file.upload") }}</a>
          </Alert>
        </div>
      </div>
    </Card>
    <Card :style="{ width: '52%', height: documentClientHeight - 110 + 'px', float: 'right', }">
      <span class="userInfo_title">{{$t('login.ScopeOfAuthority')}}</span>
      <Divider />
      <div style="padding-left: 25px;margin-top: 20px;">
        <div class="content">
          <div style="width: 110px; display: inline-block">
            <span class="content_title">{{$t('role.roleName')}} : </span>
          </div>
          <div style="display: inline-block">
            <div v-if="userInfo.userId != 1">
              <span class="content_title" v-for="(item, index) in roleIdList" :key="item">
                <span v-if="index == roleIdList.length - 1">{{getRoleName(item)}} </span>
                <span v-else>{{getRoleName(item)}} | </span>
              </span>
            </div>
            <div v-else>
              <span class="content_title">
                {{$t('login.superAdministrator')}}
              </span>
            </div>
          </div>
        </div>
        <div class="content">
          <div style="width: 140px; display: inline-block">
              <span class="content_title">{{$t('login.PermissionDetails')}} : </span>
          </div>
          <div style="height:60vh;overflow: auto;">
            <Tree :data="menuList" expand-node ref="menuListTree"></Tree>
          </div>
        </div>
      </div>
    </Card>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
    <!-- 弹窗，修改邮箱-->
    <update-email v-if="updateEmailVisible" ref="updateEmail"  @refreshData="getUserInfo"></update-email>
    <!-- 弹窗，修改电话-->
    <update-mobile v-if="updateMobileVisible" ref="updateMobile" @refreshData="getUserInfo"></update-mobile>
    <!-- 弹窗，修改验证方式-->
    <update-validate v-if="updateValidateVisible" ref="updateValidate" @refreshData="getUserInfo"></update-validate>

    <UpdateAuth v-if="updateAuthVisible" ref="updateAuth"  @refreshData="getUserInfo"></UpdateAuth>
    <!-- 弹窗，修改LOGO-->
<!--    <update-logo v-if="updateLOGOVisible" ref="updateLOGO" @refreshData="getUserInfo"></update-logo>-->
  </div>
</template>

<script>
import UpdatePassword from "./update-password";
import UpdateEmail from "./update-email";
import UpdateMobile from "./update-mobile";
import UpdateValidate from "./update-validate";
import UpdateAuth from "./update-auth";
import zh from '@/language/zh-CN.json'
// import UpdateLogo from "./update-logo";
// import { treeDataTranslate, setCheckedNodes } from '@/utils'
// import $ from 'jQuery'
export default {
  data() {
    return {
      updatePassowrdVisible: false,
      updateEmailVisible: false,
      updateMobileVisible: false,
      updateValidateVisible: false,
      updateVisible: false,
      updateAuthVisible:false,
      // updateLOGOVisible: false,
      roleIdList: [],
      menuList: [],
      // token: this.$cookie.get('token'),
      // downloadUrl: this.$http.adornUrl(`/sys/user/myIcon/`),
      isVerify: window.SITE_CONFIG.isVerify,
    };
  },
  methods: {
    // updateIconUrlHandle() {
    //   this.updateLOGOVisible = true;
    //   this.$nextTick(() => {
    //     this.$refs.updateLOGO.init();
    //   });
    // },
    // 修改密码
    updatePasswordHandle() {
      this.updatePassowrdVisible = true;
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init();
      });
    },
    updateEmailHandle(type) {
      this.updateEmailVisible = true;
      this.$nextTick(() => {
        this.$refs.updateEmail.init(type);
      });
    },
    updateMobileHandle(type) {
      this.updateMobileVisible = true;
      this.$nextTick(() => {
        this.$refs.updateMobile.init(type);
      });
    },
    updateValidateHandle() {
      this.updateValidateVisible = true;
      this.$nextTick(() => {
        this.$refs.updateValidate.init();
      });
    },
    updateAuthHandle(isAuth){
      this.updateAuthVisible = true;
      this.$nextTick(() => {
        this.$refs.updateAuth.init(isAuth);
      });
    },

    getUserInfo() {
      this.$http({
        url: this.$http.adornUrl("/sys/user/info"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.userInfo = data.user;
          // if (this.userInfo && this.userInfo.iconUrl) {
          //   if (this.userInfo.iconUrl.substr(0, 6) === 'aips40') {
          //     $('#userUrl').prop({'src': `${this.downloadUrl}${this.userInfo.iconUrl}?token=${this.$cookie.get("token")}`})
          //   } else {
          //     $('#userUrl').prop({'src': this.userInfo.iconUrl})
          //   }
          // }
        } else {
          this.$Message.error(data.msg);
        }
      });
    },
    // 改变tree的标题
    changeTitle() {
      for (let i = 0; i < this.menuList.length; i++) {
        const element = this.menuList[i];
        if (element.menuId != 188 && element.menuId != 189 && element.parentId != 188 && element.parentId != 189) {
          if (element.type == 3) {
            element.title = this.$t(this.getZhKeyByValue(element.title))
          } else {
            element.title = this.$t('nav.' + element.title)
          }
        }
        if (element.children.length > 0) {
          for (let j = 0; j < element.children.length; j++) {
            const elementJ = element.children[j];
            if (elementJ.menuId != 188 && elementJ.menuId != 189 && elementJ.parentId != 188 && elementJ.parentId != 189) {
              if (elementJ.type == 3) {
                elementJ.title = this.$t(this.getZhKeyByValue(elementJ.title))
              } else {
                elementJ.title = this.$t('nav.' + elementJ.title)
              }
            }
            if (elementJ.children.length > 0) {
              for (let k = 0; k < elementJ.children.length; k++) {
                const elementk = elementJ.children[k];
                if (elementk.menuId != 188 && elementk.menuId != 189 && elementk.parentId != 188 && elementk.parentId != 189) {
                  if (elementk.type == 3) {
                    elementk.title = this.$t(this.getZhKeyByValue(elementk.title))
                  } else {
                    elementk.title = this.$t('nav.' + elementk.title)
                  }
                }
                if (elementk.children.length > 0) {
                   for (let r = 0; r < elementk.children.length; r++) {
                    const elementR = elementk.children[r];
                    if (elementR.type == 3) {
                      elementR.title = this.$t(this.getZhKeyByValue(elementR.title))
                    } else {
                      elementR.title = this.$t('nav.' + elementR.title)
                    }
                  }
                }
              }
            }
          }
        }
      }
      return this.menuList
    },
    // 获取对应的国际化标签
    getZhKeyByValue(val) {
      for(let key in zh) {
        // 判断属性是否是对象自身的属性而非继承的属性
        if (zh.hasOwnProperty(key)) {
          for(let key1 in zh[key]) {
            if (zh[key].hasOwnProperty(key1)) {
              if (zh[key][key1] == val) {
                return key + "." + key1
              }
            }
          }
        }
      }
      return val
    },

    getRoleName(name){
      if (name==='子管理员'){
        return this.$t('role.subAdmin')
      }else if (name==='普通用户'){
        return this.$t('role.normalUser')
      }
    }
  },
  components: {
    UpdatePassword,
    UpdateEmail,
    UpdateMobile,
    UpdateValidate,
    UpdateAuth
    // UpdateLogo
  },
  activated() {
    var isStrong = this.$cookie.get("isStrong")
    // var isAuth = this.$cookie.get("isAuth")
    // if ((null != isStrong && undefined !== isStrong) | (null != isAuth && undefined !== isAuth)){
    //   this.updateAuthHandle()
    // }else

    if (!this.isVerify) {
      if(null != isStrong && undefined !== isStrong) {
        this.updatePasswordHandle()
      }
    }
    //拥有的角色
    this.$http({
      url: this.$http.adornUrl(`/sys/user/infoMy`),
      method: "get",
      params: this.$http.adornParams(),
    }).then(({ data }) => {
      this.roleIdList = data.myInfo.roleIdList;
      this.menuList = data.myInfo.allyRole
      if (this.menuList.length > 0) {
        this.menuList = this.changeTitle()
      }
    }).then(() => {
      // var menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]');
      // if (menuList) {
      //   console.log(menuList)
      //   this.menuList = treeDataTranslate(menuList, 'menuId')
      // }
      // //拥有的权限/所有权限
      // this.$http({
      //   url: this.$http.adornUrl(`/sys/role/infoMy`),
      //   method: 'get',
      //   params: this.$http.adornParams()
      // }).then(({data}) => {
      //   this.menuList = treeDataTranslate(data.msg.allyRole, 'menuId')
      //   // var menuIdList = []
      //   // if (this.userInfo.userId === 1){
      //   //     data.msg.allyRole.forEach(item => {
      //   //       menuIdList.push(item.menuId)
      //   //     })
      //   // }else{
      //   //   menuIdList = data.msg.myRole
      //   // }
      //   //   setCheckedNodes(this.menuList, menuIdList)
      // })

    })
    // else if (undefined !== isAuth && null != isAuth) {
    //   this.updateAuthHandle()
    // }
    // else {
    //
    // }

  },
  computed: {
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
      set(val) {
        this.$store.commit("user/updateUserInfo", val);
      },
    },
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight;
      },
    },
    auth: {
      get() {
        return JSON.parse(sessionStorage.getItem("permissions"));
      },
    },
  },
};
</script>

<style scoped>
.userInfo_title {
  font-size: 23px;
  font-weight: 600;
}
.userInfo_content {
  width: 900px;
  height: 400px;
  padding-left: 25px;
  margin-top: 20px;
}
.content {
  margin-bottom: 23px;
}
.content_title {
  font-size: 18px;
}
.content_text {
  font-size: 15px;
}
.content_nodes {
  color: #2db7f5;
}
</style>
