<template>
    <Modal v-model="visible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{dataForm.id === 0 ? $t('common.newlyBuild') : $t('common.update')}}</span>
        </p>
        <Alert>{{$t('pay.changeVipStateTip')}}</Alert>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 400px;overflow-x:hidden;overflow-y:auto;" :label-width="80" label-position="left"
        @keyup.enter.native="dataFormSubmit()">
          <FormItem prop="username" :label="$t('login.username')">
            <Input size="large" v-model="dataForm.username"
            :placeholder="$t('common.PleaseInput') + $t('login.username')"/>
          </FormItem>
          <FormItem prop="roleIdList" :label="$t('role.role')">
            <CheckboxGroup v-model="dataForm.roleIdList">
              <Checkbox v-for="role in roleList" :key="role.roleId" :label="role.roleId"><span>{{role.roleName}}</span> </Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem prop="defaultPassword" :label="$t('sys.isDefaultPassword')" v-if="isVerify">
            <i-switch v-model="dataForm.isDefaultPassword" @on-change="changeDefaultPassword"></i-switch>
          </FormItem>
          <FormItem prop="password" :label="$t('login.password')">
            <Input size="large" type="text" v-model="dataForm.password"
            :placeholder="$t('common.PleaseInput') + $t('login.password')" :disabled="passwordDisabled"/>
          </FormItem>
          <FormItem prop="confirmPassword" :label="$t('login.confirmPassword')">
            <Input size="large" type="text" v-model="dataForm.confirmPassword"
            :placeholder="$t('common.PleaseInput') + $t('login.confirmPassword')" :disabled="passwordDisabled"/>
          </FormItem>
          <FormItem prop="email" :label="$t('register.mailbox')" v-if="!isVerify">
            <Input size="large" v-model="dataForm.email"
            :placeholder="$t('common.PleaseInput') + $t('register.mailbox')"/>
          </FormItem>
          <FormItem :label="$t('register.mobile')" v-if="!isVerify">
            <Input size="large" v-model="dataForm.mobile"
            :placeholder="$t('common.PleaseInput') + $t('register.mobile')"/>
          </FormItem>
          <FormItem prop="status" :label="$t('common.state')">
            <RadioGroup v-model="dataForm.status">
              <Radio :label="0"> <span>{{$t('common.disable')}}</span> </Radio>
              <Radio :label="1"> <span>{{$t('common.normal')}}</span> </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem :label="$t('sys.verify')"  v-if="!isVerify">
            <RadioGroup v-model="dataForm.validate">
<!--              <Radio :label="0"> <span>{{$t('screen.null')}}</span> </Radio>-->
              <Radio :label="1"> <span>{{$t('sys.email')}}</span> </Radio>
              <Radio :label="2"> <span>{{$t('sys.mobile')}}</span> </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="VIP"  v-if="!isVerify && userInfo.userId === 1">
            <RadioGroup v-model="dataForm.vipStatus">
              <Radio :label="-1"> <span>{{$t('pay.superVip')}}</span> </Radio>
              <Radio :label="0"> <span>{{$t('pay.freeVersion')}}</span> </Radio>
              <!-- <Radio :label="1"> <span>{{$t('pay.goldCardVip')}}</span> </Radio>
              <Radio :label="2"> <span>{{$t('pay.diamondVip')}}</span> </Radio> -->
            </RadioGroup>
          </FormItem>
          <FormItem :label="$t('sys.whetherAudit')">
            <RadioGroup v-model="dataForm.noExamine">
              <Radio :label="0"> <span>{{$t('sys.open')}}</span> </Radio>
              <Radio :label="1"> <span>{{$t('sys.close')}}</span> </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem prop="companyId" :label="$t('sys.companyId')">
            <Input size="large" v-if="userInfo.userId == 1 && dataForm.id == 0" v-model="dataForm.companyId"
            :placeholder="$t('register.companyId')"/>
            <div v-else> {{dataForm.companyId}} &nbsp;<span style="color: red">{{$t('sys.same')}}</span> </div>
          </FormItem>
          <Divider orientation="left">{{$t('sys.permissionOfTerminalGroups')}}</Divider>
          <FormItem v-if="userInfo.createUserId === 1">
            <!-- <CheckboxGroup v-model="dataForm.groupIds">
              <Checkbox v-for="group in groupList" :key="group.id" :label="group.id"><span>{{group.name}}</span> </Checkbox>
            </CheckboxGroup> -->
            <span v-if="userInfo.userId == 1" style="color: red">
              {{$t('sys.TheSuperAdminDoesNotRestrictUserGroups')}}
            </span>
            <Tree v-else :data="groupList" :render="renderContent" check-strictly check-directly show-checkbox ref="groupTreeRender" class="group-tree-render"></Tree>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
import { isMobile, isNumOrLetter, PWDLenght } from '@/utils/validate'
export default {
  data () {
    return {
      visible: false,
      roleList: [],
      loading: false,
      dataForm: {
        id: 0,
        username: '',
        password: '',
        confirmPassword: '',
        salt: '',
        email: '',
        mobile: '',
        roleIdList: [],
        status: 1,
        validate: 0,
        noExamine: 0,
        companyId: '',
        vipStatus: 0,
        groupIds: [],
        showGroup: '',
        groupIdList: [],
        isDefaultPassword: false,
      },
      isVerify: window.SITE_CONFIG.isVerify,
      groupList: [],
      groupListTotalLength: 0,
      passwordDisabled: false,
    }
  },
  methods: {
    // 初始化
    init (id) {
      this.dataForm.isDefaultPassword = false
      this.dataForm.id = id || 0
      if(this.isVerify){
        this.dataForm.validate = 0
      }else {
        this.dataForm.validate = 1
      }
      this.passwordDisabled = false
      this.$http({
        url: this.$http.adornUrl('/sys/role/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.roleList = data && data.code === 0 ? data.list : []
      }).then(() => {
        if (this.userInfo.userId != 1) {
          this.$http({
            url: this.$http.adornUrl('/sys/group/userSelect'),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.groupList = data.map.list
              this.groupListTotalLength = data.map.totalLength
            } else {
              this.groupList = []
              this.groupListTotalLength = 0
            }
          })
        }
      }).then(() => {
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/sys/user/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.user
              this.dataForm.isDefaultPassword = false
              this.dataForm.groupIdList = []
              if (this.groupList && this.groupList.length > 0) {
                this.groupListHandle(this.groupList)
              }
            }
          })
        }
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
        })
      })
    },
    changeDefaultPassword(status){
      this.dataForm.password = null
      this.dataForm.confirmPassword=null
      this.dataForm.isDefaultPassword = status
      if (status){
        this.passwordDisabled=true
      } else {
        this.passwordDisabled=false
      }
    },

    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.groupList && this.groupList.length > 0) {
            this.$refs.groupTreeRender.getCheckedNodes().forEach(element => {
              this.dataForm.groupIdList.push(element.id)
            })
            // 如果全选了所有分组，表示当前用户可以操作所有控制卡
            if (this.groupListTotalLength != 0 && this.groupListTotalLength == this.dataForm.groupIdList.length) {
              this.dataForm.showGroup = 1
            } else if (this.dataForm.groupIdList.length < 1) {
              this.dataForm.showGroup = 0
            } else {
              this.dataForm.showGroup = 2
            }
          }
          this.loading = true
          if(null!=this.dataForm.password&&null!=this.dataForm.confirmPassword&&!this.dataForm.isDefaultPassword){
            this.dataForm.password=this.$encruption(this.dataForm.password)
            this.dataForm.confirmPassword=this.$encruption(this.dataForm.confirmPassword)
          }
          // console.log(this.dataForm)
          this.$http({
            url: this.$http.adornUrl(`/sys/user/${this.dataForm.id == 0 ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    },
    renderContent (h, { root, node, data }) {
      return h('span', data.name)
    },
    groupListHandle(data) {
      data.forEach(item => {
        const tmp = {...item};
        if (this.dataForm.showGroup == 1) {
          item.checked = true
        } else if (this.dataForm.showGroup == 2) {
          if (this.dataForm.groupIds && this.dataForm.groupIds.length > 0) {
            const current = this.dataForm.groupIds.find(ids => ids == item.id)
            if (current) {
              const index = this.dataForm.groupIds.indexOf(current)
              this.dataForm.groupIds.splice(index, 1);
              item.checked = true
            }
          }
        } else {
            item.checked = false
        }
        if (tmp.children) {
          tmp.children = this.groupListHandle(tmp.children);
        }
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    },
    dataRule: {
      get () {
        return {
          username: [
            { required: true, message: this.$t('validate.account_cannot_empty'), trigger: 'blur' },
            { validator: (rule, value, callback) => {
              if (!isNumOrLetter(value)) {
                callback(new Error(this.$t('login.user4To17')))
              } else {
                callback()
              }
            }, trigger: 'blur' }
          ],
        password: [
          { validator: (rule, value, callback) => {
              if(undefined!==this.dataForm.isDefaultPassword&&!this.dataForm.isDefaultPassword){
                if (value || this.dataForm.id=== 0) {
                  if (!PWDLenght(value)) {
                    callback(new Error(this.$t('login.passwordMore8')))
                  } else {
                    callback()
                  }
                } else {
                  callback()
                }
              }else{
                callback()
              }
            }, trigger: 'blur' }
        ],
          confirmPassword: [
            { validator: (rule, value, callback) => {
                if(undefined!==this.dataForm.isDefaultPassword&&!this.dataForm.isDefaultPassword) {
                  if (this.dataForm.password){
                    if (!value) {
                      callback(new Error(this.$t('validate.confirm_password_cannot_empty')))
                    }
                  }
                  if (value|| this.dataForm.id=== 0) {
                    if ((!/\S/.test(value))) {
                      callback(new Error(this.$t('validate.confirm_password_cannot_empty')))
                    } else if (this.dataForm.password !== value) {
                      callback(new Error(this.$t('validate.the_password_is_inconsistent')))
                    } else {
                      callback()
                    }
                  } else {
                    callback()
                  }
                }else{
                  callback()
                }
            }, trigger: 'blur' }
          ],
          roleIdList: [
            { required: true,validator: (rule, value, callback) => {
              if (!this.dataForm.roleIdList || this.dataForm.roleIdList.length < 1) {
                callback(new Error(this.$t('sys.roleEmpty')))
              } else {
                callback()
              }
            }, trigger: 'change' }
          ],
          // companyId: [
          //   { required: true, message: '公司Id不能为空', trigger: 'blur' },
          // ],
          email: [
            { required: true, message: this.$t('validate.email_cannot_empty'), trigger: 'blur' },
            { type: 'email', message: this.$t('validate.incorrect_email_format'), trigger: 'blur' }
          ],
          // mobile: [
          //   { required: true, message: this.$t('validate.mobile_cannot_empty'), trigger: 'blur' },
          //   { validator: (rule, value, callback) => {
          //     if (!isMobile(value)) {
          //       callback(new Error(this.$t('validate.mobile_format')))
          //     } else {
          //       callback()
          //     }
          //   }, trigger: 'blur' }
          // ]
        }
      }
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.groupList = []
          this.roleList = []
          this.dataForm = {
            id: 0,
            username: '',
            password: '',
            confirmPassword: '',
            salt: '',
            email: '',
            mobile: '',
            roleIdList: [],
            status: 1,
            validate: 0,
            noExamine: 0,
            companyId: '',
            vipStatus: 0,
            groupIds: [],
            showGroup: '',
            groupIdList: [],
            isDefaultPassword: false
          },
          this.groupListTotalLength = 0
        }, 500)
      }
    }
  }
}
</script>
