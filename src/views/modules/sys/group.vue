<template>
  <div class="modiles-group">
    <Button type="primary" @click="append()" :disabled="disabledButton">{{$t('group.addingAGroup')}}</Button>
    <Tree :style="{'height': tableHeight + 171 + 'px'}" :data="dataList" :render="renderContent" class="demo-tree-render"></Tree>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataListLoading: false,
      dataList: [],
      buttonSize: 'small',
      selectName: '',
      disabledButton: false
    }
  },
  components: {
  },
  activated () {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/sys/group/list'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.groups
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    renderContent (h, { root, node, data }) {
        return h('span', {
            style: {
                display: 'inline-block',
                width: '100%'
            }
        }, [
            // 是否为编辑模式
            data.isEditor ? h('span', [
                h('Input',{
                  style: {
                    'font-size': '18px',
                    width: '300px',
                    marginRight: '10px'
                  },
                  props: {
                    value: data.name,
                    size: this.buttonSize
                  },
                  on: {
                    input: (value) => {
                      this.selectName = value
                    }
                  }
                }),
                h('Button',{
                  style: {
                    marginRight: '5px',
                    'font-size': '13px',
                  },
                  props: {
                    type:"info",
                    size: this.buttonSize
                  },
                  on: {
                    click: () => {
                      // this.disabledButton = false
                      if (this.selectName) {
                        this.saveOrUpdate(data.parentId, data.id)
                      } else {
                        this.$Message.error(this.$t('group.pleaseEnterAGroupName'))
                      }
                      // console.log(this.selectName)
                      // console.log(data.parentId)
                      // console.log(data.id)
                    }
                  }
                }, this.$t('common.confirm')),
                h('Button',{
                  style: {
                    marginRight: '5px',
                    'font-size': '13px',
                  },
                  props: {
                    type:"primary",
                    size: this.buttonSize
                  },
                  on: {
                    click: () => {
                      data.isEditor = false
                      this.selectName = ''
                      this.disabledButton = false
                      if (data.isSave) {
                        this.remove(root, node, data)
                      }
                    }
                  }
                }, this.$t('common.cancel')),
            ]) :
            // 不为编辑模式
            h('span', [
                h('span',{
                  style: {
                    'font-size': '18px'
                  }
                }, data.name),
                h('span',{
                  style: {
                    'font-size': '12px',
                    marginLeft: '10px',
                  }
                }, data.createTime),
            ]),
            // 修改，添加，删除按钮
            data.id ? h('span', {
                style: {
                    display: 'inline-block',
                    float: 'right',
                    marginRight: '32px',
                }
            }, [
                h('Button',{
                  style: {
                    marginRight: '5px',
                    'font-size': '13px',
                  },
                  props: {
                    type:"info",
                    size: this.buttonSize,
                    disabled: this.disabledButton
                  },
                  on: {
                    click: () => {
                      data.isEditor = true
                      this.selectName = data.name
                      this.disabledButton = true
                    }
                  }
                }, this.$t('common.update')),
                h('Button',{
                  style: {
                    marginRight: '5px',
                    'font-size': '13px',
                  },
                  props: {
                    type:"primary",
                    size: this.buttonSize,
                    disabled: this.disabledButton
                  },
                  on: {
                    click: () => {
                      this.append(data)
                    }
                  }
                }, this.$t('group.addingSubgroup')),
                h('Button',{
                  style: {
                    'font-size': '13px',
                  },
                  props: {
                    type:"error",
                    size: this.buttonSize,
                    disabled: this.disabledButton
                  },
                  on: {
                    click: () => {
                      if ((data.children && data.children.length <= 0) || data.children == null) {
                        this.deleteHandle(data.id)
                      } else {
                        this.$Message.error(this.$t('group.pleaseDeleteTheSubgroupsFirst'))
                      }
                    }
                  }
                }, this.$t('common.delete'))
            ])
            : ''
        ]);
    },
    append (data) {
      const children = {
          id: 0,
          name: '',
          expand: true,
          isEditor: true,
          isSave: true
      };
      if (data) {
        children.parentId = data.id
        if (data.children == null) {
          data.children = []
        }
        data.children.push(children)
      } else {
        this.dataList.push(children)
      }
      this.disabledButton = true
    },
    remove (root, node, data) {
      const parentKey = root.find(el => el === node).parent;
      if (parentKey != null) {
        const parent = root.find(el => el.nodeKey === parentKey).node;
        const index = parent.children.indexOf(data);
        parent.children.splice(index, 1);
      } else {
        this.dataList = this.dataList.filter(item => item.id !== data.id)
      }
    },
    saveOrUpdate(parentId, id) {
      this.$http({
        url: this.$http.adornUrl(`/sys/group/${!id ? 'save' : 'update'}`),
        method: 'post',
        data: this.$http.adornData({
          'id': id || undefined,
          'parentId': parentId || undefined,
          'name': this.selectName,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 0.5,
            onClose: () => {
              this.disabledButton = false
              this.getDataList();
            }
          })
        } else {
          this.$Message.error(data.msg)
            setTimeout(() => {
              this.disabledButton = false
            }, 500)
          }
      })
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/sys/group/delete'),
            method: 'post',
            data: this.$http.adornData([id], false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight },
    }
  }
}
</script>
<style scoped>
.demo-tree-render {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
<style>
.demo-tree-render .ivu-tree-title{
  width: 60%
}

</style>
