<template>
    <Modal v-model="visible" width="500">
      <p slot="header" style="text-align:center">
          <span>{{!dataForm.id ? $t('common.newlyBuild') : $t('common.update')}}</span>
      </p>
      <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 200px;" :label-width="80" label-position="left"
      @keyup.enter.native="dataFormSubmit()">
        <FormItem prop="name" :label="$t('group.name')">
          <Input size="large" v-model="dataForm.name"
          :placeholder="$t('common.PleaseInput') + $t('group.name')"/>
        </FormItem>
        <FormItem prop="memo" :label="$t('role.remark')">
            <Input v-model="dataForm.memo" type="textarea" :autosize="{minRows: 2,maxRows: 5}" :placeholder="$t('sys.remarks')"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
          <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
          <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {
        id: 0,
        name: ''
      },
    }
  },
  methods: {
    // 初始化
    init (id) {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      this.visible = true
      this.dataForm.id = id || 0
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(`/sys/group/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.name = data.group.name
            this.dataForm.memo = data.group.memo
          }
        })
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(`/sys/group/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'memo': this.dataForm.memo
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
          name: [
            { required: true, message: this.$t('validate.group_name_cannot_empty'), trigger: 'blur' }
          ]
        }
      }
    }
  },
}
</script>
