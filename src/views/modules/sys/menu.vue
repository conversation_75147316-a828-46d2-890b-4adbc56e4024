<template>
   <div class="modiles-menu">
     <Form :inline="true" :model="dataForm">
       <FormItem>
        <Button v-if="isAuth('sys:menu:save')" size="large" type="primary" @click="addOrUpdateHandle()">
          <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" row-key="menuId">
      <template slot-scope="{ row, index }" slot="name">
        <span>{{row.menuId != 188 &&  row.menuId != 189 && row.parentId != 188 &&  row.parentId != 189 ? row.type === 3 ? $t(getZhKeyByValue(row.name)) : $t('nav.' + row.name)  : row.name}}</span>
      </template>
      <template slot-scope="{ row, index }" slot="parentName">
        <span>{{row.parentName ? row.parentId != 188 &&  row.parentId != 189 ?$t('nav.' + row.parentName): row.parentName : ''}}</span>
      </template>
      <template slot-scope="{ row, index }" slot="icon">
        <svg class="icon" width="20px" height="20px" aria-hidden="true">
          <use :xlink:href="'#' + row.icon"></use>
        </svg>
      </template>
      <template slot-scope="{ row, index }" slot="type">
        <Tag color="default" v-if="row.type === 0">{{$t('menu.mainMenu')}}</Tag>
        <Tag color="primary" v-if="row.type === 4">{{$t('menu.HomeDirectoryMenu')}}</Tag>
        <Tag color="success" v-if="row.type === 1">{{$t('menu.menu')}}</Tag>
        <Tag color="warning" v-if="row.type === 2">{{$t('menu.DirectoryMenu')}}</Tag>
        <Tag color="primary" v-if="row.type === 3">{{$t('menu.button')}}</Tag>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
          <Button v-if="isAuth('sys:menu:update')" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.menuId)">{{$t('common.update')}}</Button>
          <Button v-if="isAuth('sys:menu:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.menuId)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
   </div>
</template>

<script>
import zh from '@/language/zh-CN.json'
import AddOrUpdate from './menu-add-or-update'
// import { treeDataTranslate } from '@/utils'
export default {
  data () {
    return {
      dataForm: {},
      dataList: [],
      dataConlums: [
        {title: this.$t('menu.name'), key: 'name', tree: true, width: 200, align: 'center',slot: 'name',
          renderHeader:(h)=>{
            return h('div',this.$t('menu.name'))
          }
        },
        {title: this.$t('menu.parentName'), key: 'parentName', align: 'center',slot: 'parentName',
          renderHeader:(h)=>{
            return h('div',this.$t('menu.parentName'))
          }
        },
        {title: this.$t('menu.icon'), key: 'icon', align: 'center', slot: 'icon',
          renderHeader:(h)=>{
            return h('div',this.$t('menu.icon'))
          }
        },
        {title: this.$t('menu.type'), key: 'type', align: 'center', slot: 'type',
          renderHeader:(h)=>{
            return h('div',this.$t('menu.type'))
          }
        },
        {title: this.$t('menu.orderNum'), key: 'orderNum', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('menu.orderNum'))
          }
        },
        {title: this.$t('menu.url'), key: 'url', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('menu.url'))
          }
        },
        {title: this.$t('menu.perms'), key: 'perms', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('menu.perms'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataListLoading: false,
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/sys/menu/list'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.dataList = data
        // this.dataList = treeDataTranslate(data, 'menuId')
        // console.log(this.dataList)
        this.dataListLoading = false
      })
    },
    // 获取对应的国际化标签
    getZhKeyByValue(val) {
      for(let key in zh) {
        // 判断属性是否是对象自身的属性而非继承的属性
        if (zh.hasOwnProperty(key)) {
          for(let key1 in zh[key]) {
            if (zh[key].hasOwnProperty(key1)) {
              if (zh[key][key1] == val) {
                return key + "." + key1
              }
            }
          }
        }
      }
      return val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      // this.getZhKeyByValue()
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl(`/sys/menu/delete/${id}`),
            method: 'post',
            data: this.$http.adornData()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.dataList.length <= 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    }
  },
}
</script>