<template>
    <Modal v-model="visible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{dataForm.id === 0 ? $t('common.newlyBuild') : $t('common.update')}}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 250px;overflow-x:hidden;overflow-y:auto;" :label-width="80" label-position="left"
        @keyup.enter.native="dataFormSubmit()">
          <FormItem prop="number" :label="$t('file.SerialNumber')">
            <Input size="large" v-model="dataForm.number"
            :placeholder="$t('common.PleaseInput') + $t('file.SerialNumber')"/>
          </FormItem>
          <FormItem prop="type" :label="$t('menu.type')">
            <Select size="large" v-model="dataForm.type" :placeholder="$t('common.PleaseSelect') + $t('menu.type')">
                <Option v-for="item in typeList" :value="item.id" :key="item.id">{{ $t(item.title) }}</Option>
            </Select>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {
        id: 0,
        number: '',
        type: 0,
      },
      typeList: [
        {
          id: 1,
          title: 'common.CAT1' 
        },
        {
          id: 2,
          title: 'common.DaHuaCamera' 
        },
        {
          id: 3,
          title: 'common.GBCamera' 
        }
      ],
    }
  },
  methods: {
    // 初始化
    init (id) {
      this.dataForm.id = id || 0
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(`/sys/deviceWhiteList/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm = data.data
          }
        })
      }
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    validateField(field) {
      this.$refs['dataForm'].validateField(field);
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(`/sys/deviceWhiteList/${this.dataForm.id == 0 ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    },
  },
  computed: {
    dataRule: {
      get () {
        return {
          number: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ],
          type: [
            { validator: (rule, value, callback) => {
              if (!this.dataForm.type) {
                callback(new Error(this.$t('validate.not_empty')))
              } else {
                callback()
              }
            }, trigger: 'change' },
          ],
        }
      }
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.dataForm = {
            id: 0,
            number: '',
            type: 0,
          }
        }, 500)
      }
    }
  }
}
</script>
