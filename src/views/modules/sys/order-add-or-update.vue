<template>
  <div>
    <Modal v-model="visible" width="700">
      <p slot="header" style="text-align:center">
          <span>{{$t('file.examine')}}</span>
      </p>
      <div style="height: 400px;overflow-x:hidden;overflow-y:auto;">
        <Divider orientation="left">{{$t('pay.OrderDetails')}}</Divider>
        <div class="order-detail">
          <div class="order-detail__content">
            <table class="order-detail__table">
              <tbody>
                <tr>
                  <td class="titleTh">{{$t('pay.orderNumber')}}</td>
                  <td>{{dataForm.outTradeNo}}</td>
                  <td class="titleTh">{{$t('login.username')}}</td>
                  <td>{{dataForm.username}}</td>
                </tr>
                <tr>
                  <td class="titleTh">{{$t('common.state')}}</td>
                  <td>
                    <span v-if="dataForm.vipStatus === 1">{{$t('pay.silverCardVip')}}</span>
                    <span v-else-if="dataForm.vipStatus === 2">{{$t('pay.goldCardVip')}}</span>
                    <span v-else-if="dataForm.vipStatus === 3">{{$t('pay.diamondVip')}}</span>
                    <span v-else-if="dataForm.vipStatus === 4">{{$t('pay.VIP4')}}</span>
                  </td>
                  <td class="titleTh">{{$t('pay.Years')}}</td>
                  <td>{{dataForm.month / 12}} {{$t('program.year')}}</td>
                </tr>
                <tr>
                  <td class="titleTh">{{$t('pay.bankReceipt')}}</td>
                  <td>
                    <div class="bankImg" @click="imgClickHandler">
                      <img v-show="dataForm.bankReceiptPath" ref="bankImg"
                      src="" style="width: 100%; height: 100%" />
                    </div>
                  </td>
                  <td class="titleTh">{{$t('pay.BankCardNumber')}}</td>
                  <td>{{dataForm.bankCardNumber}}</td>
                </tr>
                <tr>
                  <td class="titleTh">{{$t('pay.PaymentStatus')}}</td>
                  <td colspan="3">
                    <span v-if="dataForm.tradeStatus === 'CREATE_ORDER'">{{$t('pay.unpaid')}}</span>
                    <span v-else-if="dataForm.tradeStatus === 'WAIT_BUYER_PAY'">{{$t('pay.transactionCreation')}}</span>
                    <span v-else-if="dataForm.tradeStatus === 'BANK_RECEIPT_ERROR'">{{$t('pay.ErrorUploadingBankReceipt')}}</span>
                    <span v-else-if="dataForm.tradeStatus === 'TRADE_CLOSED'">{{$t('pay.UnpaidTransactionTimeoutClosed')}}</span>
                    <span v-else-if="dataForm.tradeStatus === 'WAIT_SELLER_CONFIRM'">{{$t('pay.WaitingForSellerToConfirm')}}</span>
                    <span v-else-if="dataForm.tradeStatus === 'TRADE_SUCCESS'">{{$t('pay.paymentSuccessful')}}</span>
                    <!-- <span v-else-if="dataForm.tradeStatus === 'TRADE_SUCCESS' || dataForm.tradeStatus === 'TRADE_FINISHED'">{{$t('pay.paymentSuccessful')}}</span> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <Divider orientation="left">{{$t('file.examine')}}</Divider>
        <Form ref="dataForm" :model="dataForm" :label-width="80" label-position="left"
        @keyup.enter.native="dataFormSubmit()">
          <FormItem :label="$t('pay.amount')">
            <InputNumber :min="0" v-model="dataForm.totalAmount" style="width: 200px"/>
          </FormItem>
          <FormItem :label="$t('file.examine')">
            <Select size="large"  v-model="dataForm.examineStatus" :placeholder="$t('common.PleaseSelect') + $t('common.state')" filterable clearable transfer>
              <Option v-for="item in tradeStatusList" :value="item.value" :key="item.value">{{ $t(item.name) }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="dataForm.examineStatus == 'BANK_RECEIPT_ERROR'" :label="$t('file.ApprovalComments')">
            <Input v-model="dataForm.remark" type="textarea"
            :autosize="{minRows: 3,maxRows: 5}" :maxlength="100" :placeholder="$t('common.PleaseInput') + $t('file.ApprovalComments')"></Input>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
    <Modal v-model="dialogVisible" footer-hide>
      <img w-full ref="fullImg" src="" style="width: 100%; height: 100%" alt="Preview Image" />
    </Modal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {
        outTradeNo: null,
        tradeStatus: null,
        notifyTime: new Date(),
        vipStatus: 0,
        month: 0,
        purchaseStatus: 0,
        totalAmount: null,
        bankReceiptPath: null,
        bankCardNumber: null,
        username: null,
        examineStatus: null,
        remark: null,
      },
      dialogVisible: false,
      tradeStatusList: [
        {value: 'BANK_RECEIPT_ERROR', name: "pay.ErrorUploadingBankReceipt"},
        {value: 'TRADE_CLOSED', name: "pay.UnpaidTransactionTimeoutClosed"},
        {value: 'TRADE_SUCCESS', name: "pay.paymentSuccessful"},
      ],
    }
  },
  methods: {
    // 初始化
    init (outTradeNo) {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      this.dataForm= {
        outTradeNo: null,
        tradeStatus: null,
        notifyTime: new Date(),
        vipStatus: 0,
        month: 0,
        purchaseStatus: 0,
        totalAmount: null,
        bankReceiptPath: null,
        bankCardNumber: null,
        username: null,
        examineStatus: null,
        remark: null,
      },
      this.visible = true
      this.dataForm.outTradeNo = outTradeNo || null
      if (this.dataForm.outTradeNo) {
        this.$http({
          url: this.$http.adornUrl(`/sys/order/info/${this.dataForm.outTradeNo}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm = data.payBills
            this.dataForm.totalAmount = Number.parseInt(this.dataForm.totalAmount)
            this.$refs.bankImg.src = this.$http.adornUrl(`/sys/order/download/`) + this.dataForm.bankReceiptPath + '?token=' + this.$cookie.get('token')
          }
        })
      }
    },
    imgClickHandler() {
      this.dialogVisible = true
      this.$refs.fullImg.src = this.$http.adornUrl(`/sys/order/download/`) + this.dataForm.bankReceiptPath + '?token=' + this.$cookie.get('token')
    },
    // 表单提交
    dataFormSubmit () {
      if (this.dataForm.examineStatus == 'BANK_RECEIPT_ERROR') {
        if (!this.dataForm.remark) {
          this.$Message.error("请输入审批意见")
          return;
        }
      }
      this.loading = true
      this.$http({
        url: this.$http.adornUrl('/sys/order/update'),
        method: 'post',
        data: this.$http.adornData({
          'outTradeNo': this.dataForm.outTradeNo,
          'totalAmount': this.$encruption(this.dataForm.totalAmount + ""),
          'tradeStatus': this.dataForm.examineStatus,
          'remark': this.dataForm.remark
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loading = false
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 0.5,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$Message.error(data.msg)
          setTimeout(() => {
            this.loading = false
          }, 500)
        }
      })
    }
  },
  computed: {
  }
}
</script>
<style scoped>
.bankImg {
  display:inline-block;
  width: 120px;
  height: 120px;
  border: 1px solid #dadee3;
}
.order-detail {
  margin-bottom: 10px;
}
.order-detail__table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}
.titleTh {
  background-color: #fafbfb;
  color: #909aaa;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  border: 1px solid #ecedf0;
  padding: 8px 16px;
}
.order-detail__table tr {
    display: table-row;
    vertical-align: inherit;
    border-color: inherit;
}
.order-detail__table tr td {
  border: 1px solid #ecedf0;
  padding: 8px 16px;
}
.order-detail__table tr .subtext {
    color: #909aaa;
    font-size: 12px;
    margin-left: 8px;
}
</style>
