<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
      <FormItem>
<!--        <Select size="large" v-model="dataForm.group" filterable clearable-->
<!--                :placeholder="$t('common.PleaseSelect') + $t('group.name')" @on-change = "getDataList">-->
<!--          <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{ item.cardCount }})-->
<!--          </Option>-->
<!--        </Select>-->
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" size="large" @click="getDataList(null,1)">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
        <Button style="margin-right:6px" size="large" @click="changeGroup()">
          <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
        </Button>
        <Button style="margin-right:6px" @click="getTerminalInfo()" :loading="terminalInfoLoading"
                type="primary" size="large" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{ $t('cardDevice.queryTerminalInfo') }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList" @on-selection-change="selectionChangeHandle"
           @on-row-click="selectThisRow" :loading="dataListLoading" :height="tableHeightData" ref="selection">
      <template slot-scope="{ row, index }" slot="number">
        {{ index + 1 }}
      </template>
      <template slot-scope="{ row, index }" slot="deviceId">
        {{ row.deviceId }}
        <span v-if="row.msg === 1">
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
                        <use xlink:href="#send"></use>
                    </svg>
                </span>
        <span v-if="row.msg === 2">
                    <Poptip placement="right-start" v-if="row.text && row.text !== ''" trigger="hover" transfer
                            :title="$t('common.tips')" :content="tipChange(row.text)">
                        <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
                            <use xlink:href="#fail"></use>
                        </svg>
                    </Poptip>
                    <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
                        <use xlink:href="#fail"></use>
                    </svg>
                </span>
        <span v-if="row.msg === 3">
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
                        <use xlink:href="#success"></use>
                    </svg>
                </span>
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div v-if="row.isOn === 1 ">
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#on-line"></use>
          </svg>
        </div>
        <div v-else>
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#line"></use>
          </svg>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="netType">
        <div v-if="row.netType === 'WIFI'">
          {{ row.netType }}
          <svg v-if="row.rssi >= -50 && row.rssi <= 0" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level4"></use>
          </svg>
          <svg v-else-if="row.rssi >= -70 && row.rssi < -50" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level3"></use>
          </svg>
          <svg v-else-if="row.rssi >= -80 && row.rssi < -70" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level2"></use>
          </svg>
          <svg v-else-if="row.rssi >= -100 && row.rssi < -80" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#WIFI-level1"></use>
          </svg>
        </div>
        <div v-else-if="row.netType === 'LTE' || row.netType === 'UMTS' || row.netType === 'HSPA'
                || row.netType === 'HSPA+' || row.netType === 'EDGE'  || row.netType === 'GPRS'">
          {{ row.netType }}
          <svg v-if="row.asu >= 12 && row.asu != 99" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#gsm-0"></use>
          </svg>
          <svg v-else-if="row.asu >= 8 && row.asu  < 12" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#gsm-1"></use>
          </svg>
          <svg v-else-if="row.asu >= 5 && row.asu < 8" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#gsm-2"></use>
          </svg>
          <svg v-else-if="row.asu >= 3 && row.asu < 5" style="vertical-align: middle;" width="24px"
               height="24px" aria-hidden="true">
            <use xlink:href="#gsm-3"></use>
          </svg>
        </div>
        <div v-else>
          {{ row.netType }}
        </div>
      </template>

      <template slot-scope="{ row, index }" slot="radarState">
        <div v-if="row.radarState === 1 ">
          <span>{{ $t('common.true') }}</span>
        </div>
        <div v-else>
          <span>{{ $t('common.false') }}</span>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button v-if="isAuth('radar:radarSpeed:list')" type="primary" size="small"
                style="margin-right: 5px;font-size: 11px" @click="speedCharts(row.deviceId)">
          {{ $t('statistic.viewChart') }}
        </Button>

      </template>
    </Table>
    <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex"
          :page-size="pageSize" show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

    <!-- 功能菜单-->
    <div style="height: 20px; background:#eee; clear: both;">
      <svg v-if="!isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuixiaohua"></use>
      </svg>
      <svg v-if="isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuidahua"></use>
      </svg>
    </div>
    <div v-if="!isMinimize" class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in operation" :key="index">
          <li>
            <div class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{ $t(item.text) }}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div v-if="isMinimize" style="height: 50px;" class="opera_div">
      <ul class="opera_ul1">
        <div v-for="(item, index) in operation" :key="index" :title="$t(item.text)">
          <li>
            <div class="opera_list1" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
            <div class="opera_list1" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
          </li>
        </div>
      </ul>
    </div>


    <!--分组弹出框-->
    <Modal v-model="selectGroupVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{$t('common.selectGroup')}}</span>
      </p>
      <Alert type="info" show-icon >
        <span>{{this.$t('tips.groupTip')}}</span>
      </Alert>
      <div>
        <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
      </div>
      <div slot="footer">
        <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>

    <Modal v-model="visible" width="70%" @on-cancel="chartCancel()">
      <p slot="header" style="text-align:center">
        {{ $t('radar.radarSpeed') }}
      </p>
      <div style="text-align:center">
        <div style="text-align: right">
          <Date-picker v-model="dateRange" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
                       :placeholder="$t('card.DateRange')" style="width: 200px"></Date-picker>
          <Button type="primary" @click="speedCharts()">{{ $t('common.query') }}</Button>
        </div>
        <div v-if="!hasData">
          {{$t('home.temporarilyNoData')}}
        </div>
        <div id="radarSpeedChart" ref="speedData" v-show="hasData"
             :style="{'width': documentClientWidth - 630 + 'px', 'height': '500px'}"></div>


      </div>
      <div slot="footer">
      </div>
    </Modal>

    <device-radar-setting ref="radarSetting" v-if="radarSettingVisible"
                          @refreshDataList="getDataList"></device-radar-setting>
    <device-radar-speed-for-program ref="radarSpeedLimit" v-if="radarSpeedLimitVisible"></device-radar-speed-for-program>
  </div>
</template>

<script>
import deviceRadarSetting from "./device/device-radar-setting";
import deviceRadarSpeedForProgram from "./device/device-radar-speed-for-program";
import echarts from "../../../utils/echarts";

export default {
  data() {
    return {
      dataForm: {
        key: '',
        group: []
      },
      groupList: [],
      dataConlums: [
        {type: 'selection', width: 60, fixed: 'left', align: 'center'},
        {
          title: this.$t('cardDevice.number'), fixed: 'left',width: 70, align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {title: 'ID', key: 'deviceId', fixed: 'left',width: 170, align: 'center', slot: 'deviceId'},
        {
          title: this.$t('cardDevice.deviceName'), key: 'alias', align: 'center', tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.deviceName'))
          }
        },
        {
          title: this.$t('cardDevice.online'), key: 'isOn', slot: 'online', align: 'center', width: 110,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.online'))
          }
        },
        {
          title: this.$t('cardDevice.networkType'), key: 'netType', align: 'center', slot: 'netType', width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.networkType'))
          }
        },
        {
          title: this.$t('radar.isConnect'), key: 'radarState', align: 'center', slot: 'radarState', width: 235,
          renderHeader: (h) => {
            return h('div', this.$t('radar.isConnect'))
          }
        },
        {title: this.$t('cardDevice.fireWare'), key: 'fireware', align: 'center', tooltip: true, width: 245,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.fireWare'))
          }},
        {title: 'CardSystem', key: 'ledsetVersion', align: 'center', tooltip: true, width: 130},
        {title: 'Conn', key: 'connVersion', align: 'center', tooltip: true, width: 130},
        {
          title: this.$t('operation.group'), key: 'groupName', align: 'center', tooltip: true,width: 100,
          renderHeader: (h) => {
            return h('div', this.$t('operation.group'))
          }
        },
        {title: "IP", key: 'realIp', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div','IP')
          }
        },
        {
          title: this.$t('cardDevice.lastOffline'), key: 'lastOffTime', align: 'center', tooltip: true,width: 170,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.lastOffline'))
          }
        },

        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center', fixed: 'right',width: 160,
            renderHeader: (h) => {
              return h('div', this.$t('common.operation'))
            }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      terminalInfoLoading: false,


      operation: [
        {
          id: 'radarSetting',
          icon: 'xitongshezhi',
          text: 'radar.radarSetting',
          disable: false,
          // auth: 'lighting:brightness',
          checked: true
        },
        {
          id: 'radarSpeedLimit',
          icon: 'speedLimit',
          text: 'radar.speedLimitRange',
          disable: false,
          auth: 'radar:speedLimited',
          checked: true
        },
      ],
      /**
       * 雷达设置
       */
      radarSettingVisible: false,


      /**
       * 雷达图表可视化
       */
      visible: false,
      speedEChart: "",
      speedData: [],
      speedDate: [],
      /**
       * 雷达数据日期范围
       */
      dateRange: [],
      deviceId: '',
      hasData: false,
      lightingSwitchVisible: false,
      groupVisible: false,
      //选择分组时，分组框是否可见
      selectGroupVisible:false,
      //  分组名
      groupName:"",
      rootNode:null,
      //是否第一次打开该页面
      isFirst:true,
      //总数量
      totalNum:0,
      tableHeightData: 0,
      isMinimize: false,
      radarSpeedLimitVisible:false,
    }
  },
  activated() {
    this.initData()
    this.getDataList('loading',null)
  },
  components: {
    deviceRadarSetting,
    deviceRadarSpeedForProgram
  },
  methods: {
    // 初始化数据
    initData() {
      this.dataForm = {
        key: '',
        group: []
      }
      this.dataList = []
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListLoading = false
      this.dataListSelections = []
      // this.visible = true
      this.speedData = []
      this.speedDate = []
      this.deviceId = ''
      this.groupName=""
      this.rootNode=null
      this.isFirst=true
      //总数量
      this.totalNum=0
      this.terminalInfoLoading= false
      this.tableHeightData = this.tableHeight
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },

    // 获取数据列表
    getDataList(loading,isQuery) {
      this.pageIndex = 1
      if (loading) {
        this.dataListLoading = true
      }
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/radar/card/list'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex+"",
          'limit': this.pageSize+"",
          'key': this.dataForm.key,
          'group': this.dataForm.group
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataForm.group.length===0){
            this.groupName=""
          }
          if (this.isFirst){
            this.totalNum=data.page.totalCount
            this.isFirst=false
          }
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(loading,isQuery)
          }
          // this.dataForm.group=[]
          // 设置选中
          var select = this.$refs.selection.getSelection().map(item => {
            return item.deviceId
          })
          if (select && select.length !== 0) {
            this.dataList.map(item => {
              if (select.indexOf(item.deviceId) != -1) {
                item._checked = true
              } else {
                item._checked = false
              }
            })
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 查询分组列表
    getGroupList() {
      this.$http({
        url: this.$http.adornUrl('/sys/group/select'),
        method: 'get',
        params: this.$http.adornParams({
          'radarState': 0
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.groupList = data.group
          this.getUnGroupDevice()
        } else {
          this.groupList = []
        }
      })
    },

    // 查询终端信息
    getTerminalInfo() {
      // 判断选中的列表不为空
      if (this.dataListSelections.length > 0) {
        var deviceIds = this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.dataList = this.dataList.filter(data => {
          if (deviceIds.indexOf(data.deviceId) !== -1) {
            data.msg = 1
            data._checked = true
          } else {
            data._checked = false
          }
          return data
        })
        this.terminalInfoLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getCardInformation'),
          method: 'post',
          data: deviceIds
        }).then(({ data }) => {
          if (data && data.cards) {
            for (let i = 0; i < data.cards.length; i++) {
              const element = data.cards[i];
              if (element.card.text) {
                element.card.msg = 2
              } else {
                element.card.msg = 3
              }
              element.card._checked = true
              this.dataList.forEach((item, index) => {
                if (element.card.deviceId.indexOf(item.deviceId) !== -1) {
                  this.dataList.splice(index, 1, element.card)
                } else {
                  item = item
                }
              })
            }
          } else {
            this.$Message.error(data.msg)
          }
          this.terminalInfoLoading = false
        });
      }
    },
    // 不支持
    operaErrorHandle(text) {
      this.$Message.warning({
        content: this.$t('common.supportedTip') + this.$t(text),
        duration: 2
      })
    },
    operaSuccessHandle(id, checked) {
      if (id) {
        if (checked === true) {
          // 获取已选的卡
          var deviceIds = null
          if (this.dataListSelections.length > 0) {
            deviceIds = this.dataListSelections.map(item => {
              return item.deviceId
            })
          } else {
            this.$Message.warning({
              content: this.$t('common.selectDevice'),
              duration: 2
            })
            return
          }
          if (deviceIds.length >= 1) {
            if (id === 'radarSetting') { // 照明亮度
              this.radarSettingVisible = true
              this.$nextTick(() => {
                this.$refs.radarSetting.init(deviceIds)
              })
            }else if (id=== 'radarSpeedLimit'){
              this.radarSpeedLimitVisible=true
              this.$nextTick(()=>{
                this.$refs.radarSpeedLimit.init(deviceIds)
              })
            }
          }
        } else if (checked === false) {
          // 不需要选中卡

        }
      }
    },


    /**
     * 查看图表
     * @param deviceId 卡号
     */
    speedCharts(deviceId) {
      this.visible = true
      this.speedData = []
      this.speedDate = []
      this.hasData = false
      if (deviceId != null) {
        this.deviceId = deviceId
      }
      var startDate = ""
      var endDate = ""
      //如果未选择日期，直接查询，则默认查询昨天的数据
      if (this.dateRange.length == 0) {
        endDate = this.DateToStr(new Date())
        var sixDaysAgo = new Date()
        sixDaysAgo.setTime(new Date().getTime() - 6 * 24 * 60 * 60 * 1000);
        startDate = this.DateToStr(sixDaysAgo)
      } else {
        startDate = this.DateToStr(this.dateRange[0])
        endDate = this.DateToStr(this.dateRange[1])
      }
      this.$http({
        url: this.$http.adornUrl(`/radar/radarSpeed/list`),
        method: 'get',
        params: this.$http.adornParams({
          'cardId': this.deviceId,
          'startDate': startDate,
          'endDate': endDate
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.list = data.data
          if (this.list && this.list.length > 0) {
            this.hasData = true
            this.list.forEach(item => {
              this.speedData.push(item.speed)
              this.speedDate.push(item.createTime)
            })
            this.myEcharts();
          } else {
            this.hasData = false
            this.speedData=[]
            this.speedDate=[]
          }

          // this.dateRange=[]
        }
      })
    },
    //tip国际化
    tipChange(tip){
      if (tip=="控制卡连接已断开"){
        return this.$t('monitor.offLineOrNotExist')
      }else {
        return this.$t('log.connectionClosed')
      }
    },
    /**
     * 取消图表，销毁图表
     */
    chartCancel() {
      // console.log("cancel")
      // debugger
      this.dateRange=[]
      var el = document.getElementById('radarSpeedChart')
      if (el != null) {
        this.$echarts.dispose(el)
      }
    },
    changeGroup(){
      this.selectGroupVisible=true
    },
    // 表单提交
    dataFormSubmit () {
      this.dataForm.group=[]
      this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      this.getChildrenNodes(this.rootNode)
      this.selectGroupVisible=false
      this.getDataList('loading',1)
      this.groupName=this.rootNode.name
      this.rootNode=null
    },
    //取消选择分组
    cancelSelect(){
      this.selectGroupVisible=false
      this.dataForm.group=[]
      this.groupName=""
      this.rootNode=null
      this.getDataList()
      this.getGroupList()

    },
    //获取该分组及其子分组的groupId
    getChildrenNodes(rootNode){
      this.dataForm.group.push(rootNode.id)
      var childNode=rootNode.children;
      if (childNode){
        for (var i=0; i<childNode.length; i++) {
          this.getChildrenNodes(childNode[i])
        }
      }
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },

    // date格式转成yy-MM-dd
    DateToStr(dd) {
      var y = dd.getFullYear();
      var m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
      var d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
      var h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
      var n = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
      var s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
      return y + '-' + m + '-' + d + ' ' + h + ':' + n + ':' + s;
      // return y + '-' + m + '-' + d;
    },


    /**
     * 图表
     */
    myEcharts() {

      //电流
      if (this.speedEChart != null && this.speedEChart != "" && this.speedEChart != undefined) {
        this.speedEChart.dispose();
      }
      this.speedEChart = this.$echarts.init(this.$refs.speedData);
      this.speedEChart.setOption(this.radarSpeedOption);
    },
     // 最大化最小化
    handleMinimize (isMinimize) {
      this.isMinimize = !isMinimize
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    },

  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight - 155
      }
    },
    documentClientWidth: {
      get() {
        return this.$store.state.common.documentClientWidth
      },
    },
    radarSpeedOption: {
      get() {
        return {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
            formatter : '{b} <br/> ' + this.$t('radar.speed') + ' {c} km/h'
          },

          toolbox: {
            feature: {
              // saveAsImage: {}
              // dataZoom: {
              //   yAxisIndex: 'none'
              // },
              // restore: {},

            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.speedDate
          },
          yAxis: {
            type: 'value',
            boundaryGap: [0, '100%'],
            axisLabel: {
              formatter: '{value} km/h'
            },
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 10
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              name: this.$t('radar.speed'),
              type: 'line',
              symbol: 'none',
              sampling: 'lttb',
              itemStyle: {
                color: 'rgb(135, 206, 250)'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(0,191,255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(135, 206, 250)'
                  }
                ])
              },
              data: this.speedData
            }
          ]
        }
      }
    }
  },
  watch: {
    'totalNum': function (newVal, OldVal) {
      this.getGroupList();
    },
    'tableHeight': function(newVal, oldVal) {
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    }
  },
}
</script>

<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 180px;
  background: #eee;
  overflow: hidden;
  overflow-y: auto;
}

.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}

.opera_ul li {
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 94px;
  margin-left: 10px;
  margin-right: 10px;
}

.opera_list {
  padding-top: 10px;
  width: 90px;
  height: 90px;
  margin-left: 25%;
}

.opera_list:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}

.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}

.opera_ul1 li{
  text-align: center;
  float: left;
  list-style: none;
  width: 40px;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list1 {
  padding-top: 5px;
  width: 40px;
  height: 40px;
}
.opera_ul1 li:hover {
  background-color: rgb(210, 174, 245);
  cursor: pointer;
}

.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}

.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
</style>
