<template>
  <Modal width="900" height="500" v-model="visible">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{ $t('radar.speedLimitRange') }}</span>
    </p>

    <div style="overflow:auto;height: 400px">
      <Row style="margin-bottom: 10px">
        <Col span="18"></Col>
        <Col span="6">
          <Button type="success" @click="handlerAdd">{{ $t('common.add') }}</Button>
          <Button type="info" @click="queryRange()" :loading="queryLoading">{{ $t('common.query') }}</Button>
          <Button type="primary" @click="setRange" :loading="setLoading">{{ $t('common.set') }}</Button>
        </Col>
      </Row>

      <div>
        <Form :inline="true" style="height: 200px;overflow: auto;" :label-width="85" label-position="left">
          <Table border :columns="dataColumns" :data="speedList">
            <template slot-scope="{ row, index }" slot="number">
              {{ index + 1 }}
            </template>
            <template slot-scope="{ row, index }" slot="minSpeed">
              <div v-if="index===0">
                <el-input-number :max="999" :min="1" v-model="row.minSpeed"   @change="(val)=>minSpeedChange(val, index)" ></el-input-number>
              </div>
              <div v-else>
                <el-input-number :max="999" :min="speedList[index-1].maxSpeed" v-model="row.minSpeed"   @change="(val)=>minSpeedChange(val, index)" ></el-input-number>
              </div>
            </template>
            <template slot-scope="{ row, index }" slot="maxSpeed">
              <el-input-number :max="999" :min="row.minSpeed" v-model="row.maxSpeed"   @change="(val)=>maxSpeedChange(val, index)" ></el-input-number>
            </template>
          </Table>
        </Form>
      </div>
      <div v-if="setSpeedRangeList.length > 0">
        <cardResult :ids="ids" :resultData="setSpeedRangeList" :cardItemWidth="900 / 2 - 50"
                    :isQuery="false"></cardResult>
      </div>
      <div v-if="querySpeedRangeList.length > 0">
        <cardResult :ids="ids" :resultData="querySpeedRangeList" :cardItemWidth="900 / 2 - 50"
                    :isQuery="true" :resultItem="[{text: $t('radar.speedLimitRange'),name:'speedRange',suffix:':'}]" ></cardResult>
      </div>
    </div>

    <div slot="footer" style="text-align: left;">
          <span>
              <Alert v-if="ids.length === 0" type="error" show-icon>{{ $t('hardware.selectionCard') }}！</Alert>
            <Alert v-else type="success" show-icon
            >{{ $t("tips.cardSelected") }}:</Alert
            >
          </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"

export default {
  components: {
    cardResult
  },
  data() {
    return {
      ids: [],
      visible: false,
      speedList: [],
      dataColumns: [
        {
          title: this.$t('cardDevice.number'), align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('radar.minSpeed'), key: 'minSpeed', align: 'center', slot: 'minSpeed',
          renderHeader: (h) => {
            return h('div', this.$t('radar.minSpeed'))
          }
        },
        {
          title: this.$t('radar.maxSpeed'), key: 'maxSpeed', align: 'center', slot: 'maxSpeed',
          renderHeader: (h) => {
            return h('div', this.$t('radar.maxSpeed'))
          }
        },
        {
          title: this.$t('common.operation'), // 删除操作
          key: 'operation',
          align: 'center',
          width: 110,
          render: (h, {row, index}) => {
            return h('a', {
              on: {
                click: () => {
                  this.speedList.splice(index, 1)
                }
              }
            }, this.$t('common.delete'))
          },
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ],
      setSpeedRangeList: [],
      setLoading:false,
      querySpeedRangeList:[],
      queryLoading:false,

    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.ids = ids
        this.visible = true
        this.speedList = []
        this.setSpeedRangeList = []
        this.setLoading=false
        this.setSpeedRangeList=[]
        this.querySpeedRangeList=[]
        this.queryLoading=false
      }
    },
    handlerAdd() {
      const speed = {
        'minSpeed': 0,
        'maxSpeed': 0
      }
      this.speedList.push(speed)
    },
    setRange() {
      if (this.ids.length > 0) {
        if (this.speedList.length>0){
          this.setSpeedRangeList=[]
          this.querySpeedRangeList=[]
          this.setLoading=true
          this.$http({
            url: this.$http.adornUrl("/radar/set/speedRange"),
            method: 'post',
            data: this.$http.adornData({
              "deviceIds": this.ids,
              "speedRanges": this.speedList
            })
          }).then(({data}) => {
            this.setLoading=false
            if (data && data.code === 0) {
              this.setSpeedRangeList = data.data
            } else {
              this.$Message.error(data.msg)
            }
          })
        }else {
          this.$Message.error(this.$t('radar.addSpeedLimitRange'))
        }
      }
    },
    //查询速度范围
    queryRange(){
      if (this.ids.length > 0) {
          this.setSpeedRangeList=[]
          this.querySpeedRangeList=[]
          this.queryLoading=true
          var ids =  this.ids.join(",");
          this.$http({
            url: this.$http.adornUrl("/radar/get/radarSpeedRange"),
            method: 'get',
            params: this.$http.adornParams({
              "deviceIds": ids,
            })
          }).then(({data}) => {
            this.queryLoading=false
            if (data && data.code === 0) {
              this.querySpeedRangeList = data.data
            } else {
              this.$Message.error(data.msg)
            }
          })
      }
    },
    minSpeedChange(val,index) {
      this.speedList[index].minSpeed = val
    },
    maxSpeedChange(val,index) {
      this.speedList[index].maxSpeed = val
    },

  },
}
</script>

<style scoped>

</style>
