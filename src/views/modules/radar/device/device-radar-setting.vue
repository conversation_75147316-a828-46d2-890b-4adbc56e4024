<template>
  <!-- 设置参数弹窗 -->
  <Modal v-model="setUpParametermodal" width="900">
    <p slot="header" style="text-align:center">
      <span>{{$t('radar.radarSetting')}}</span>
    </p>
    <div>
    <Form :model="setUpParameterForm" style="height: 300px;">

      <FormItem >
        <span style="font-size: 18px;margin-right: 10px">{{$t('menu.type')}}:</span>
        <Select v-model="setUpParameterForm.switchType" size="large"
                @on-change="changesetUpParameterList" style="width: 200px;margin-right: 20px">
          <Option v-for="item in setUpParametersgroupList" :value="item.switchType"
                  :key="item.switchType">{{ $t(item.name) }}
          </Option>
        </Select>
        <span style="font-size: 18px;margin-right: 10px">{{$t('radar.parameter')}}:</span>
        <RadioGroup v-model="setUpParameterForm.setParameter" v-if="setUpParameterForm.switchType == 209">
          <Radio label="300">300ms</Radio>
          <Radio label="600">600ms</Radio>
          <Radio label="900">900ms</Radio>
        </RadioGroup>
        <RadioGroup v-model="setUpParameterForm.setParameter" v-if="setUpParameterForm.switchType == 217">
          <Radio label="1">{{$t('radar.fastestCar')}}</Radio>
          <Radio label="0">{{$t('radar.closestCar')}}</Radio>
        </RadioGroup>
        <el-input-number v-model="setUpParameterForm.setParameter" v-if="setUpParameterForm.switchType == 224" :min="10" :max="200" placeholder="Enter something..." style="width: 200px" />

        <el-input-number v-model="setUpParameterForm.setParameter"  v-if="setUpParameterForm.switchType == 225" :min="1" :max="20" placeholder="Enter something..." style="width: 200px" />

        <el-input-number v-model="setUpParameterForm.setParameter" v-if="setUpParameterForm.switchType == 229" :min="10" :max="350" placeholder="Enter something..." style="width: 200px" />

        <span v-if="setUpParameterForm.switchType == 224||setUpParameterForm.switchType == 229" style="font-size: 20px">km/h</span>
        <Button type="primary"  size="large" :loading="settingLoading" @click="radarSetting()" style="margin-left: 20px">{{$t('common.set')}}</Button>
        <Button type="primary"  size="large" :loading="getSettingLoading" @click="getRadarSetting()">{{$t('common.query')}}</Button>
      </FormItem>
      <div  style="height: 240px;overflow-y: auto;" >
        <div v-if="parametersList.length > 0">
          <cardResult :ids="ids" :resultData="parametersList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
        </div>
        <div v-if="queryData.length">
          <cardResult :ids="ids" :resultData="queryData" :cardItemWidth="900 / 2 - 50" :isQuery="true" :tableHeight="165" :resultHeight="132"
                    :resultItem="[{text: 'radar.setMaxSpeed', name: 'maxSpeed'}, 
                    {text: 'radar.setMinSpeed', name: 'minSpeed'}, 
                    {text: 'radar.setResponseTime', name: 'responseTime'},
                    {text: 'radar.setSensitivity', name: 'sensitivity'},
                    {text: 'radar.setOutputTarget', name: 'outputTarget', 
                      resultSet: [{value: 1, name: 'radar.fastestCar', type: 'i18n'},{value: 0, name: 'radar.closestCar', type: 'i18n'}]},
                    {text: 'radar.isConnect', name: 'isConnect', 
                      resultSet: [{value: false, name: 'common.false', type: 'i18n'},{value: true, name: 'common.true', type: 'i18n'}]}]"></cardResult>
        </div>
      </div>
    </Form>
    </div>
    <div slot="footer" style="text-align: left;">
      <span>
          <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
      </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data() {
    return {
      ids: [],
      /**
       * 设置参数下拉框
       */
      setUpParametersgroupList: [
        {
          switchType: 209,
          name: 'radar.setResponseTime'
        },
        {
          switchType: 217,
          name: 'radar.setOutputTarget'
        },
        {
          switchType: 224,
          name: 'radar.setMinSpeed'
        },
        {
          switchType: 225,
          name: 'radar.setSensitivity'
        },
        {
          switchType: 229,
          name: 'radar.setMaxSpeed'
        }
      ],
      /**
       * 设置参数表单
       */
      setUpParameterForm: {
        switchType: 209,
        setParameter: '300',
      },
      /**
       * 设置参数模态框状态
       */
      setUpParametermodal: false,

      /**
       * 确定按钮等待
       */
      settingLoading:false,

      /**
       * 查询按钮
       */
      getSettingLoading:false,
      parametersList:[],
      dataColumns: [
        {title: this.$t('cardDevice.deviceName'), key: 'deviceId', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.deviceName'))
          }
        },
        {title: this.$t('radar.setMaxSpeed'), key: 'maxSpeed',
          renderHeader:(h)=>{
            return h('div',this.$t('radar.setMaxSpeed'))
          }
        },
        {title: this.$t('radar.setMinSpeed'), key: 'minSpeed',
          renderHeader:(h)=>{
            return h('div',this.$t('radar.setMinSpeed'))
          }
        },
        {title: this.$t('radar.setResponseTime'), key: 'responseTime',
          renderHeader:(h)=>{
            return h('div',this.$t('radar.setResponseTime'))
          }
        },
        {title: this.$t('radar.setSensitivity'), key: 'sensitivity',
          renderHeader:(h)=>{
            return h('div',this.$t('radar.setSensitivity'))
          }
        },
        {title: this.$t('radar.setOutputTarget'), key: 'outputTarget',slot:'outputTarget',
          renderHeader:(h)=>{
            return h('div',this.$t('radar.setOutputTarget'))
          }
        },
        {title: this.$t('radar.isConnect'), key: 'isConnect',slot:'isConnect',
          renderHeader:(h)=>{
            return h('div',this.$t('radar.isConnect'))
          }
        }
      ],
      queryData:[],
    }
  },
  methods: {
    // 初始化
    init (ids) {
      this.setUpParametermodal = true
      this.queryData = []
      this.parametersList = []
      if (ids) {
        this.ids = ids
      }
    },
    // 获取数据列表
    changesetUpParameterList() {
      if(this.setUpParameterForm.switchType == 217){
        this.setUpParameterForm.setParameter = "1";
      }
      if(this.setUpParameterForm.switchType == 209){
        this.setUpParameterForm.setParameter = "300";
      }
    },
    /**
     * 雷达设置
     */
    radarSetting(){
      if (this.ids.length > 0) {
        this.settingLoading = true
        this.parametersList = []
        this.queryData = []
        this.$http({
          url: this.$http.adornUrl('/radar/set/parameters'),
          method: 'post',
          data: this.$http.adornData({
            'deviceIds': this.ids,
            'switchType':this.setUpParameterForm.switchType,
            'setParameter':this.setUpParameterForm.setParameter
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.parametersList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          /* this.parametersList.forEach(item =>{
            if (item._type&&item._type==='error'||item._type==='Error'){
              this.queryFailed.push(item)
            }
            if (item.success&&item.success==true){
              this.querySuccess.push(item)
            }
          }) */
            this.settingLoading = false
        })
      }
    },

    getRadarSetting(){
      if (this.ids.length > 0) {
        this.getSettingLoading = true
        this.parametersList = []
        this.queryData = []
          var ids =  this.ids.join(",");
          this.$http({
            url: this.$http.adornUrl('/radar/get/radarParameters'),
            method: 'get',
            params: this.$http.adornParams({
              'deviceIds': ids
            })
          }).then(({data}) => {
            if (data && data.code == 0) {
              this.queryData = data.data
              /* this.parametersList.forEach(item =>{
                if (item._type&&item._type==='error'||item._type==='Error'){
                    this.queryFailed.push(item)
                }
                if (item.success&&item.success==true){
                  this.querySuccess.push(item)
                }
              }) */
            } else {
              this.$Message.error(data.msg)
            }
            this.getSettingLoading = false
          })
      }
    }
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight - 155
      }
    }
  },
  watch: {
    'setUpParametermodal': function (newVal, oldVal) {
      if (newVal === false) {
        this.getSettingLoading=false
        this.settingLoading=false
        this.setUpParameterForm = {
          switchType: 209,
          setParameter: '300',
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
