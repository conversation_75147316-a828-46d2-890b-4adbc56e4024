<template>
  <div class="modiles-controlLog">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('alarm.call')"></Input>
      </FormItem>
      <FormItem>
        <Button @click="getDataList(1)" size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataColumns" :data="dataList" ref="table"
           :loading="dataListLoading" style="width: 100%" :max-height="tableHeight">
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        key: ''

      },

      key:"",
      dataColumns: [
        {title: "ID", key: 'id', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',"ID")
          }
        },
        {title: this.$t('alarm.call'), key: 'alarmSource', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('alarm.call'))
          }
        },
        {title:  this.$t('alarm.receive'), key: 'alarmDestination', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('alarm.receive'))
          }
        },

        {title: this.$t('sys.alarmTime'), key: 'alarmTime', align: 'center',
          renderHeader:(h)=>{
            return h('div', this.$t('sys.alarmTime'))
          }
        },
        {title:this.$t('login.username'), key: 'alarmUserName', align: 'center',
          renderHeader:(h)=>{
            return h('div', this.$t('login.username'))
          }
        }
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false
    }
  },
  activated () {
    this.initData()
    this.getDataList()
  },
  methods: {
    initData () {
      this.dataForm = {
        key:''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
    },
    // 获取数据列表
    getDataList (isQuery) {
      this.dataListLoading = true
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/sys/sipAlarm/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    }
  }
}
</script>
