<template>
<Modal v-model="visible" width="900">
  <p slot="header" style="text-align:center;font-size: 20px;">
    <span>{{$t('operation.LightingLevel')}}</span>
  </p>
  <Tabs :value="tabVal" @on-click="handlerTab" style="min-height: 500px; max-height: 700px;">
    <TabPane :label="$t('operation.LightingLevel')" name="tab1">
      <div>
        <Form label-position="left" label-colon :label-width="120">
          <FormItem :label="$t('cat1.mainLampBrightness') + ':'">
            <Row>
              <Col span="16">
                <SliderDrag :value="params.value" @SetOpacityConfig="SetOpacityConfig" :key="'mainBright'+reloadBrightness"></SliderDrag>
              </Col>
              <Col span="8" style="padding-left: 10px;">
                <InputNumber :max="100" :min="0" :step="1" v-model="params.value" style="width: 70px;"></InputNumber> %
              </Col>
            </Row>
          </FormItem>
          <FormItem :label="$t('cat1.mainLampColorTemp') + ':'">
            <Row>
              <Col span="16">
                <SliderDrag :value="params.mainColorTempValue" @SetOpacityConfig="SetMainColorTemp" :key="'mainColor'+reloadMainColorTemp"></SliderDrag>
              </Col>
              <Col span="8" style="padding-left: 10px;">
                <InputNumber :max="100" :min="0" :step="1" v-model="params.mainColorTempValue" style="width: 70px;"></InputNumber>
                (0-100)
              </Col>
            </Row>
          </FormItem>
          <FormItem :label="$t('cat1.secondaryLampBrightness') + ':'">
            <Row>
              <Col span="16">
                <SliderDrag :value="params.secondaryLampBrightnessValue" @SetOpacityConfig="SetSecondaryBrightness" :key="'secondaryBright'+reloadSecondaryBrightness"></SliderDrag>
              </Col>
              <Col span="8" style="padding-left: 10px;">
                <InputNumber :max="100" :min="0" :step="1" v-model="params.secondaryLampBrightnessValue" style="width: 70px;"></InputNumber> %
              </Col>
            </Row>
          </FormItem>
          <FormItem :label="$t('cat1.secondaryLampColorTemp') + ':'">
            <Row>
              <Col span="16">
                <SliderDrag :value="params.secondaryLampColorTempValue" @SetOpacityConfig="SetSecondaryColorTemp" :key="'secondaryColor'+reloadSecondaryColorTemp"></SliderDrag>
              </Col>
              <Col span="8" style="padding-left: 10px;">
                <InputNumber :max="100" :min="0" :step="1" v-model="params.secondaryLampColorTempValue" style="width: 70px;"></InputNumber>
                (0-100)
              </Col>
            </Row>
          </FormItem>
          <FormItem>
            <Row>
              <Col span="24" style="text-align: center;">
                <Button :loading="modal_loading" type="primary" @click="setBrightness(1)" style="margin-right: 10px;">{{$t('sys.open')}} / {{$t('common.set')}}</Button>
                <Button :loading="closeLoading" @click="setBrightness(0)" type="error">{{$t('sys.close')}}</Button>
              </Col>
            </Row>
          </FormItem>
          <div v-if="resultData.length > 0" style="max-height: 300px;overflow-y: auto; margin-top:15px;">
            <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </Form>
      </div>
    </TabPane>
    <TabPane :label="$t('operation.timingBrightness')" name="tab2">
      <Form inline label-position="left">
        <FormItem>
          <Alert  type="info" show-icon>{{$t('cat1.scheduleTip')}}</Alert>
        </FormItem>
        <FormItem>
        <Row>
          <Col span="8">
            <Input size="large" v-model="name" :placeholder="$t('common.name')"></Input>
          </Col>
          <Col span="16">
            <Button style="margin-left:6px" @click="getDataList()"  size="large">
              <div style="margin:3px 8px">{{$t('common.query')}}</div>
            </Button>
            <Button style="margin-left:6px" size="large" type="primary" @click="addSchedule()">
              <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
            </Button>
          </Col>
        </Row>
        </FormItem>
        <FormItem>
          <Table border :columns="columns" :data="dataList" :loading="dataListLoading" style="width: 100%"
                 :max-height="300" ref="selection">
            <template slot-scope="{ row, index }" slot="number">
              {{ index + 1 }}
            </template>
            <template slot-scope="{ row, index }" slot="operation">
              <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading"
                      @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
              <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px"
                      @click="updateHandle(row.id)">{{$t('common.update')}}</Button>
              <Button type="error" size="small" style="font-size: 11px"
                      @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
            </template>
          </Table>
          <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
                show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total @on-change="currentChangeHandle"
                @on-page-size-change="sizeChangeHandle" />
        </FormItem>
      </Form>
      <Form style="height: 180px;overflow-y: auto;clear: both;" :label-width="30" label-position="left">
      <div v-if="resultData.length > 0">
          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
      </div>
      </Form>


    </TabPane>
  </Tabs>
  <div slot="footer" style="text-align: left;">
    <div style="overflow-y: auto;max-height:42px;">
      <Breadcrumb>
        <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
      </Breadcrumb>
    </div>
  </div>
<Modal v-model="scheduleVisible" width="1100" height="auto" >
  <p slot="header" style="text-align:center;font-size: 20px;">
    <span>{{ $t('card.timing') }}</span>
  </p>
  <div>
    <Form ref="dataForm" :model="scheduleData" :rules="scheduleRules">
      <FormItem prop="name">
        <Row>
          <Row>
            <Col span="2"><Icon  style="color:#ed4014;font-size:10px;margin-top:10px;" type="ios-medical"/></Col>
            <Col span="22"><Input type="text" v-model="scheduleData.name" :placeholder="$t('common.name')"/></Col>
          </Row>
        </Row>
      </FormItem>
      <FormItem>
        <Table :columns="scheduleColumns" :data="scheduleData.list" border >
          <template slot-scope="{ row, index }" slot="number">
            {{ index + 1 }}
          </template>
          <template slot-scope="{ row, index }" slot="time">
            <TimePicker format="HH:mm:ss" :value="row.time" style="width: 112px" :clearable="false" :editable="false" placement="top-start" @on-change="timeChange($event, index)"></TimePicker>
          </template>
          <template slot-scope="{ row, index }" slot="brightness1">
            <InputNumber :max="100" :min="0" :step="1" v-model="row.brightness1" @on-change="brightness1Change($event, index)"></InputNumber>
          </template>
          <template slot-scope="{ row, index }" slot="colorTemperature1">
            <InputNumber :max="100" :min="0" :step="1" v-model="row.colorTemperature1" @on-change="colorTemperature1Change($event, index)"></InputNumber>
          </template>
          <template slot-scope="{ row, index }" slot="brightness2">
            <InputNumber :max="100" :min="0" :step="1" v-model="row.brightness2" @on-change="brightness2Change($event, index)"></InputNumber>
          </template>
          <template slot-scope="{ row, index }" slot="colorTemperature2">
            <InputNumber :max="100" :min="0" :step="1" v-model="row.colorTemperature2" @on-change="colorTemperature2Change($event, index)"></InputNumber>
          </template>
          <template slot-scope="{ row, index }" slot="linkedBrightness">
            <InputNumber :max="100" :min="0" :step="1" v-model="row.linkedBrightness" @on-change="linkedBrightnessChange($event, index)"></InputNumber>
          </template>
        </Table>
      </FormItem>
    </Form>

  </div>
  <div slot="footer">
    <Button @click="cancelSave()">{{ $t('common.cancel') }}</Button>
    <Button type="primary" @click="saveSchedule()">{{ $t('common.confirm') }}</Button>
  </div>
</Modal>
</Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import SliderDrag from '@/utils/SliderDrag'

export default {
  data () {
    return {
      visible: false,
      ids: [],
      params: {
        //卡号
        deviceIds:[],
        //亮度百分比
        value: 1,
        //打开开关,0关 1开
        isOpen:1,
        mainColorTempValue:0,
        secondaryLampBrightnessValue:0,
        secondaryLampColorTempValue:0
      },
      reloadBrightness: 0,
      reloadSecondaryColorTemp:0,
      reloadColorTemp:0,
      reloadMainColorTemp:0,
      reloadSecondaryBrightness:0,
      resultData: [],
      modal_loading:false,
      closeLoading:false,
      tabVal: '',
      name:'',
      //定时任务
      dataList:[],
      columns:[
        {
          title: this.$t('cardDevice.number'), align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('common.name'),align: 'center',key:'name',
          renderHeader: (h) => {
            return h('div', this.$t('common.name'))
          }
        },
        {
          title:  this.$t('common.createTime'),align: 'center',key:'createTime',
          renderHeader: (h) => {
            return h('div', this.$t('common.createTime'))
          }
        },
        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        },
      ],
      dataListLoading:false,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      //添加时的表格
      scheduleColumns:[
        {
          title: this.$t('cardDevice.number'), align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('card.startTime'), align: 'center', slot: 'time', key:'time',
          renderHeader: (h) => {
            return h('div', this.$t('card.startTime'))
          }
        },
        {
          title: this.$t('cat1.channel')+'1',
          align: 'center',
          children: [
            {
              title: this.$t('cardDevice.brightness'),
              key: 'brightness1',
              align: 'center',
              slot:'brightness1',
              renderHeader: (h) => {
                return h('div',this.$t('cardDevice.brightness'))
              }
            },
            {
              title: this.$t('cat1.colorTemp'),
              key: 'colorTemperature1',
              slot: 'colorTemperature1',
              align: 'center',
              renderHeader: (h) => {
                return h('div',this.$t('cat1.colorTemp'))
              }
            }
          ],
          renderHeader: (h) => {
            return h('div',this.$t('cat1.channel')+'1')
          }
        },
        {
          title: this.$t('cat1.channel')+'2',
          align: 'center',
          children: [
            {
              title: this.$t('cardDevice.brightness'),
              key: 'brightness2',
              slot:'brightness2',
              align: 'center',
              renderHeader: (h) => {
                return h('div',this.$t('cardDevice.brightness'))
              }
            },
            {
              title: this.$t('cat1.colorTemp'),
              key: 'colorTemperature2',
              slot: 'colorTemperature2',
              align: 'center',
              renderHeader: (h) => {
                return h('div',this.$t('cat1.colorTemp'))
              }
            }
          ],
          renderHeader: (h) => {
            return h('div',this.$t('cat1.channel')+'2')
          }
        },
        {
          title: '联动亮度', align: 'center', slot: 'linkedBrightness', key:'linkedBrightness',
          renderHeader: (h) => {
            return h('div', '联动亮度')
          }
        },
      ],
      //添加时的表格数据
      scheduleData: {
        id:'',
        name:'',
        list:[]
      },
      //添加对话框
      scheduleVisible:false,
      scheduleRules: {
        name: [
          {required: true, message:this.$t('validate.not_empty'), trigger: 'blur'}
        ],
      },
      setTimeLoading:false,
    }
  },
  components: {
    cardResult,
    SliderDrag
  },
  methods: {
    init (ids) {
      this.visible = true
      this.ids = ids
      // this.params.deviceIds=ids; // 不再直接使用这个参数
      this.modal_loading = false
      this.resultData = []
      this.closeLoading = false
      // this.setTimeLoading = false // 如果这是全局加载状态，则重置
      this.tabVal = "tab1"
      // 重置参数为默认值
      this.params.value = 50; // 默认主灯亮度
      this.params.mainColorTempValue = 0;
      this.params.secondaryLampBrightnessValue = 0;
      this.params.secondaryLampColorTempValue = 0;
      this.params.isOpen = 1; // 默认为"开/设置"状态

      this.reloadBrightness++;
      this.reloadMainColorTemp++;
      this.reloadSecondaryBrightness++;
      this.reloadSecondaryColorTemp++;
    },

    SetOpacityConfig(val) {
      this.params.value = val
    },
    // 主灯色温滑块值变化
    SetMainColorTemp(val) {
      this.params.mainColorTempValue = val;
    },
    // 副灯亮度滑块值变化
    SetSecondaryBrightness(val) {
      this.params.secondaryLampBrightnessValue = val;
    },
    // 副灯色温滑块值变化
    SetSecondaryColorTemp(val) {
      this.params.secondaryLampColorTempValue = val;
    },
    //开灯设置亮度
    setBrightness(isOpen) {
      if (this.ids && this.ids.length > 0) {
        this.resultData = []
        this.params.isOpen = isOpen;

        let mainLampBrightnessValue = 0;
        let mainColorTempValue = 0;
        let secondaryLampBrightnessValue = 0;
        let secondaryLampColorTempValue = 0;

        if(isOpen === 0) {
          this.closeLoading = true;
          // 关闭时，所有亮度和色温设为0
          mainLampBrightnessValue = 0;
          mainColorTempValue = 0;
          secondaryLampBrightnessValue = 0;
          secondaryLampColorTempValue = 0;
        } else {
          this.modal_loading = true;
          mainLampBrightnessValue = this.params.value;
          mainColorTempValue = this.params.mainColorTempValue;
          secondaryLampBrightnessValue = this.params.secondaryLampBrightnessValue;
          secondaryLampColorTempValue = this.params.secondaryLampColorTempValue;
        }

        const requestPayload = this.ids.map(deviceId => {
          const tgParams = [
            mainLampBrightnessValue,
            mainColorTempValue,
            secondaryLampBrightnessValue,
            secondaryLampColorTempValue,
            -1 // 延时时间 -1 表示永久生效
          ];
          return {
            deviceName: deviceId,
            idi: 'service.method.tg',
            params: {
              tg: tgParams
            }
          };
        });

        this.$http({
          url: this.$http.adornUrl('/cat1/device/api/common/set'),
          method: 'post',
          data: requestPayload
        }).then(({data}) => {
          if (data && data.code === 0 && Array.isArray(data.data)) {
            this.resultData = data.data.map(deviceResult => {
              return {
                deviceId: deviceResult.deviceId,
                setText: deviceResult.code === 0 ? this.$t('home.successful') : (deviceResult.msg || this.$t('common.operationFailed')),
                _type: deviceResult.code === 0 ? "success" : "error",
                code: deviceResult.code,
                msg: deviceResult.msg,
                originalApiResponse: deviceResult
              };
            });
          } else {
            this.$Message.error(data.msg || this.$t('common.operationFailed'));
            this.resultData = this.ids.map(id => ({
              deviceId: id,
              setText: data.msg || this.$t('common.operationFailed'),
              _type: "error",
              code: data.code !== undefined ? data.code : -1
            }));
          }
          if(isOpen === 0) {
            this.closeLoading = false;
          } else {
            this.modal_loading = false;
          }
        }).catch(error => {
          console.error("设置亮度/色温错误:", error);
          this.$Message.error(this.$t('common.requestFailedNetwork'));
          this.resultData = this.ids.map(id => ({
            deviceId: id,
            setText: this.$t('common.requestFailedNetwork'),
            _type: "error",
            code: -1
          }));
          if(isOpen === 0) {
            this.closeLoading = false;
          } else {
            this.modal_loading = false;
          }
        });
      }
    },
    //关灯
    // closeLight(){
    //   if (this.ids.length > 0) {
    //     this.$http({
    //       url: this.$http.adornUrl('/cat1/set/brightness'),
    //       method: 'post',
    //       data: this.$http.adornData(this.params)
    //     }).then(({data}) => {
    //       if (data&&data.code===0) {
    //         this.resultData=data.data
    //         this.resultData.forEach(item=>{
    //           if (item.code==0){
    //             item.setText=this.$t('home.successful')
    //             item._type="success"
    //           }else {
    //             // item.setText=item.msg
    //             item._type="error"
    //           }
    //         })
    //       }else {
    //         this.$Message.error(data.msg)
    //       }
    //       this.closeLoading = false
    //     })
    //   }
    // },
    //切换tab
    handlerTab (name) {
      this.tabVal = name
      if (this.tabVal === 'tab2') {
        this.getDataList()

      }
      this.clearLoading()
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/cat1/schedule/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    //设置定时
    setTimeHandle(id){
      if (this.ids.length > 0) {
        var data={
          deviceIds:this.ids,
          scheduleId:id
        }

        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading=true
          }
        })
        this.$http({
          url: this.$http.adornUrl('/cat1/set/schedule'),
          method: 'post',
          data: this.$http.adornData(data)
        }).then(({data}) => {
          if (data&&data.code===0) {
            this.resultData=data.data
            this.resultData.forEach(item=>{
              if (item.code==0){
                item.setText=this.$t('home.successful')
                item._type="success"
              }else {
                // item.setText=item.msg
                item._type="error"
              }
            })
          }else {
            this.$Message.error(data.msg)
          }
          this.dataList.map(item => {
            if (item.id === id) {
              item.timeLoading = false;
            }
          })
        })
      }
    },
    //修改
   updateHandle(id){
      this.$http({
        url: this.$http.adornUrl('/cat1/schedule/get/'+id),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          console.log(data)
          this.scheduleData.list = data.data.list;
          this.scheduleData.name=data.data.name
          this.scheduleData.id=data.data.id
          this.scheduleVisible=true

        } else {
          this.$Message.error(data.msg)
        }
      })

    },
    //删除定时任务
    deleteHandle(id){
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/cat1/schedule/delete/'+id),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    //添加定时
    addSchedule(){
      this.scheduleVisible=true
      this.initTableData()
    },
    //保存定时
    saveSchedule(){
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/cat1/schedule/${!this.scheduleData.id ? 'save' : 'update'}` ),
            method: 'post',
            data: this.$http.adornData(this.scheduleData)
          }).then(({data}) => {
            if (data&&data.code===0) {
              this.$Message.success("success")
              this.getDataList()
              this.scheduleVisible=false
            }else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    //取消保存
    cancelSave(){
      this.scheduleVisible=false
    },

    timeChange(value,key){
      this.scheduleData.list[key].time=value
    },
    brightness1Change(value,key){
      this.scheduleData.list[key].brightness1=value
    },
    colorTemperature1Change(value,key){
      this.scheduleData.list[key].colorTemperature1=value
    },
    colorTemperature2Change(value,key){
      this.scheduleData.list[key].colorTemperature2=value
    },
    brightness2Change(value,key){
      this.scheduleData.list[key].brightness2=value
    },
    linkedBrightnessChange(value,key){
      this.scheduleData.list[key].linkedBrightness=value
    },
    //添加定时时，初始化表格数据
    initTableData(){
      const data = [];
      for (let i = 0; i < 6; i++) {
        // var time = new Date();
        // time.setHours(0);
        // time.setMinutes(0);
        // console.log(time)
        var time="00:00:00"
        data.push({
          key: i,
          time: time ,
          brightness1: 0,
          colorTemperature1: 0,
          brightness2: 0,
          colorTemperature2: 0,
          linkedBrightness: 0,
        });
      }
      this.scheduleData.list = data;
      this.scheduleData.name=''
    },
    clearLoading(){
      this.setTimeLoading=false
      this.modal_loading=false
      this.dataListLoading=false
      this.closeLoading=false
      this.resultData=[]
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.clearLoading()
        this.$emit('refreshDataList')
      }
    }
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
  },
}
</script>
<style scoped>
.schedules {
  margin-top: -25px;
}
.schedules ul {
  list-style: none;
  float: left;
}
</style>

