<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(null,1)" size="large">
          <div style="margin:3px 8px">{{ $t('common.query') }}</div>
        </Button>
        <Button style="margin-right:6px" size="large" @click="changeGroup()">
          <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
        </Button>
        <Button style="margin-right:6px" @click="getTerminalInfo()" :loading="terminalInfoLoading" type="primary"
                size="large" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{ $t('cardDevice.queryTerminalInfo') }}</div>
        </Button>
        <Button style="margin-right:6px" type="info" size="large" @click="addDevice()">
          <div style="margin:3px 8px">{{ $t('common.add') }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataColumns" :data="dataList" @on-selection-change="selectionChangeHandle"
           @on-row-click="selectThisRow"
           :loading="dataListLoading" :height="tableHeightData" ref="selection">
      <template slot-scope="{ row, index }" slot="number">
        {{ index + 1 }}
      </template>
      <template slot-scope="{ row, index }" slot="deviceId">
        {{ row.deviceId }}
        <span v-if="row.msg === 1">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#send"></use>
          </svg>
        </span>
        <span v-if="row.msg === 2">
          <Poptip placement="right-start" v-if="row.text && row.text !== ''"
                  trigger="hover" transfer :title="$t('common.tips')" :content="tipChange(row.text)">
            <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
              <use xlink:href="#fail"></use>
            </svg>
          </Poptip>
          <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#fail"></use>
          </svg>
        </span>
        <span v-if="row.msg === 3">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#success"></use>
          </svg>
        </span>
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div v-if="row.isOn === 1 ">
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#on-line"></use>
          </svg>
        </div>
        <div v-else>
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#line"></use>
          </svg>
        </div>
      </template>
      <template slot-scope="{row,index}"  slot="brightness">
        <div v-if="row.lamp1Bright!=null||row.lamp2Bright!=null">
          <span>{{ $t('cat1.roadOne') }}：{{ row.lamp1Bright }}</span>
          <Divider />
          <span>{{ $t('cat1.roadTwo') }}：{{ row.lamp2Bright }}</span>
        </div>
      </template>
      <template slot-scope="{row,index}" slot="power">

        <div v-if="row.power1!=null||row.power2!=null" >
          <span>{{ $t('cat1.roadOne') }}：{{ row.power1 }}</span>
          <Divider />
          <span>{{ $t('cat1.roadTwo') }}：{{ row.power2 }}</span>
        </div>
      </template>
      <template slot-scope="{row,index}"  slot="electricity">
        <div v-if="row.electricity1!=null||row.electricity2!=null">
          <span>{{ $t('cat1.roadOne') }}：{{ row.electricity1 }}</span>
          <Divider />
          <span>{{ $t('cat1.roadTwo') }}：{{ row.electricity2 }}</span>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px"
                @click="updateHandle(row.deviceId)">{{$t('common.update')}}</Button>
        <Button type="error" size="small" style="font-size: 11px"
                @click="deleteHandle(row.deviceId)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex"
          :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

    <!--分组弹出框-->
    <Modal v-model="selectGroupVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{$t('common.selectGroup')}}</span>
      </p>
      <Alert type="info" show-icon >
        <span>{{this.$t('tips.groupTip')}}</span>
      </Alert>
      <div>
        <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
      </div>
      <div slot="footer">
        <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>

    <!--添加/修改设备-->
    <Modal v-model="addDeviceVisible" width="500">
      <p slot="header" style="text-align:center">
        <span v-if="deviceInfo.deviceId && deviceInfo.isAdd === 0">{{ $t('common.update') }}</span>
        <span v-else>{{ $t('common.add') }}</span>
      </p>
      <div>
        <Form ref="formValidate" :model="deviceInfo" :rules="ruleValidate" :label-width="80">
          <FormItem :label="$t('monitor.equipment')+'Id:'" prop="deviceId" v-if="deviceInfo.isAdd===1">
            <Input v-model="deviceInfo.deviceId"></Input>
          </FormItem>
          <FormItem :label="$t('operation.thealias')+':'" prop="alias">
            <Input v-model="deviceInfo.alias"></Input>
          </FormItem>
          <FormItem :label="$t('operation.group')+':'">
            <Button style="margin-right:6px"  @click="changeGroup()">
              <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
            </Button>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button size="large" @click="deviceAddCancel()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="deviceAddFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>

    <!-- 功能菜单-->
    <div style="height: 20px; background:#eee; clear: both;">
      <svg v-if="!isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuixiaohua"></use>
      </svg>
      <svg v-if="isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuidahua"></use>
      </svg>
    </div>
    <div v-if="!isMinimize" class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in operation" :key="index">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{ $t(item.text) }}</div>
            </div>
            <div class="opera_list" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{ $t(item.text) }}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div v-if="isMinimize" style="height: 50px;" class="opera_div">
      <ul class="opera_ul1">
        <div v-for="(item, index) in operation" :key="index" :title="$t(item.text)">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list1" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
            <div class="opera_list1" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <!--    照明开关-->
    <device-light-switch v-if="lightSwitchVisible" ref="lightSwitch"  @refreshDataList="getDataList"></device-light-switch>
  </div>
</template>

<script>
import deviceLightSwitch from "./device/device-light-switch";
export default {
  data() {
    return {
      dataForm: {
        key: '',
        group: []
      },
      dataColumns: [
        {type: 'selection', width: 60, fixed: 'left', align: 'center'},
        {
          title: this.$t('cardDevice.number'), width: 70, fixed: 'left', align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {title: 'ID', key: 'deviceId', width: 170, fixed: 'left', align: 'center', slot: 'deviceId'},
        {
          title: this.$t('cardDevice.deviceName'), key: 'alias',  align: 'center', tooltip: true,  width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.deviceName'))
          }
        },
        {
          title: this.$t('cardDevice.online'), key: 'isOn', width: 100, slot: 'online', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.online'))
          }
        },
        {
          title: this.$t('cat1.temp')+"/℃", key: 'temp',  align: 'center', tooltip: true,  width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('cat1.temp')+"/℃")
          }
        },
        {
          title: this.$t('electricity.voltage')+"/V", key: 'voltage',  align: 'center', tooltip: true,  width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('electricity.voltage')+"/V")
          }
        },
        {
          title: this.$t('cat1.electricEnergy')+'/Kwh', key: 'electricEnergy',   align: 'center', tooltip: true,  width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('cat1.electricEnergy')+'/Kwh')
          }
        },

        {
          title: this.$t('cardDevice.brightness')+'/%', slot: 'brightness', align: 'center', tooltip: true,  width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.brightness')+'/%')
          }
        },
        {
          title: this.$t('cat1.power')+'/W',  align: 'center',slot:'power', tooltip: true,  width: 130,
          renderHeader: (h) => {
            return h('div',this.$t('cat1.power')+'/W')
          }
        },
        {
          title: this.$t('electricity.current')+"/A",slot:'electricity', align: 'center',width: 100, tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('electricity.current')+"/A")
          }
        },
        {title: "IP", key: 'ip', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div','IP')
          }
        },
        {
          title: this.$t('group.name'), align: 'center',width: 100, tooltip: true,key:'groupName',
          renderHeader: (h) => {
            return h('div',  this.$t('group.name'))
          }
        },
        {
          title: this.$t('cardDevice.lastOffline'), key: 'lastOffTime', width: 170, align: 'center',tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.lastOffline'))
          }
        },
        {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', width: 150, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        }
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      terminalInfoLoading: false,
      groupList: [],
      operation: [
        {
          id: 'lightSwitch',
          icon: 'liangdu',
          text: 'operation.LightingLevel',
          disable: false,
          auth: 'cat1:device:lightSwitch',
          checked: true
        },

      ],
      groupVisible: false,
      selectGroupVisible:false,
      groupName:'',
      rootNode:null,
      totalNum:0,
      tableHeightData: 0,
      isMinimize: false,
      deviceInfo:{
        deviceId:"",
        alias:"",
        groupId:null,
        isAdd: 1 // 1 for add, 0 for update
      },
      addDeviceVisible:false,
      ruleValidate: {
        deviceId: [
          { required: true, message: this.$t('monitor.deviceIdIsNotEmpty'), trigger: 'blur' }
        ],
      },
      groupId:null,
      lightSwitchVisible:false
    }
  },
  activated() {
    this.initData()
    this.getDataList('loading',null)
  },
  components: {
    deviceLightSwitch
  },
  methods: {
    initData() {
      this.dataForm = {
        key: '',
        group: []
      }
      this.dataList = []
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListLoading = false
      this.dataListSelections = []
      this.groupName=''
      this.rootNode=null
      this.totalNum=0
      this.terminalInfoLoading= false
      this.tableHeightData = this.tableHeight
    },
    getGroupList() {
      this.$http({
        url: this.$http.adornUrl('/sys/group/selectList'),
        method: 'get',
        params: this.$http.adornParams({})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.groupList = data.group
          this.getUnGroupDevice()
        } else {
          this.groupList = []
        }
      })
    },
    getDataList(loading,isQuery) {
      if (loading) {
        this.dataListLoading = true
      }
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/cat1/device/list'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex+"",
          'limit': this.pageSize+"",
          'key': this.dataForm.key,
          'group': this.dataForm.group
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataForm.group.length===0){
            this.groupName=''
          }
          this.totalNum=data.page.totalCount
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(loading,isQuery)
          }
          var select = this.$refs.selection.getSelection().map(item => {
            return item.deviceId
          })
          if (select && select.length !== 0) {
            this.dataList.map(item => {
              if (select.indexOf(item.deviceId) != -1) {
                item._checked = true
              } else {
                item._checked = false
              }
            })
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.getDataList()
    },
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 查询终端信息 (MODIFIED)
    getTerminalInfo() {
      if (this.dataListSelections.length <= 0) {
        this.$Message.warning(this.$t('common.selectDeviceToQuery'));
        return;
      }

      const selectedDeviceIds = this.dataListSelections.map(item => item.deviceId);

      this.dataList = this.dataList.map(item => {
        if (selectedDeviceIds.includes(item.deviceId)) {
          const isChecked = this.$refs.selection.getSelection().some(sel => sel.deviceId === item.deviceId);
          return { ...item, msg: 1, _checked: isChecked };
        }
        return item;
      });

      const requestPayload = this.dataListSelections.map(item => {
        return {
          deviceName: item.deviceId,
          idi: 'service.method.rd'
        };
      });

      this.terminalInfoLoading = true;
      this.$http({
        url: this.$http.adornUrl('/cat1/device/api/common/query'),
        method: 'post',
        data: requestPayload
      }).then(({ data: apiResponse }) => {
        if (apiResponse && apiResponse.code === 0 && Array.isArray(apiResponse.data)) {
          apiResponse.data.forEach(deviceResult => {
            const deviceIdToUpdate = deviceResult.deviceId;
            const dataListItemIndex = this.dataList.findIndex(item => item.deviceId === deviceIdToUpdate);

            if (dataListItemIndex !== -1) {
              const updatedItem = { ...this.dataList[dataListItemIndex] };

              if (deviceResult.code === 0) {
                updatedItem.msg = 3;
              } else {
                updatedItem.msg = 2;
              }
              updatedItem.text = deviceResult.msg;

              if (deviceResult.code === 0 && deviceResult.data) {
                const params = deviceResult.data;
                updatedItem.temp = params.tp !== undefined ? params.tp : updatedItem.temp;
                updatedItem.voltage = params.wv !== undefined ? params.wv : updatedItem.voltage;
                updatedItem.lamp1Bright = params.zl !== undefined ? params.zl : updatedItem.lamp1Bright;
                updatedItem.electricEnergy = params.ze !== undefined ? params.ze : updatedItem.electricEnergy;
                updatedItem.power1 = params.zp !== undefined ? params.zp : updatedItem.power1;
                updatedItem.electricity1 = params.zi !== undefined ? params.zi : updatedItem.electricity1;

              }
              updatedItem._checked = selectedDeviceIds.includes(updatedItem.deviceId);
              this.$set(this.dataList, dataListItemIndex, updatedItem);
            } else {
              console.warn(`Device with ID ${deviceIdToUpdate} from API response not found in local dataList.`);
            }
          });
        } else {
          this.$Message.error(apiResponse.msg || this.$t('common.requestFailedGeneral'));
          this.dataList = this.dataList.map(item => {
            if (selectedDeviceIds.includes(item.deviceId)) {
              return { ...item, msg: 2, text: apiResponse.msg || this.$t('common.requestFailed') };
            }
            return item;
          });
        }
        this.terminalInfoLoading = false;
      }).catch(error => {
        console.error("Error querying terminal info:", error);
        this.$Message.error(this.$t('common.requestFailedNetwork'));
        this.terminalInfoLoading = false;
        this.dataList = this.dataList.map(item => {
          if (selectedDeviceIds.includes(item.deviceId)) {
            return { ...item, msg: 2, text: this.$t('common.requestFailedNetwork') };
          }
          return item;
        });
      });
    },
    operaErrorHandle(text) {
      this.$Message.warning({
        content: this.$t('common.supportedTip') + this.$t(text),
        duration: 2
      })
    },
    operaSuccessHandle(id, checked) {
      if (id) {
        if (checked === true) {
          var deviceIds = null
          if (this.dataListSelections.length > 0) {
            deviceIds = this.dataListSelections.map(item => {
              return item.deviceId
            })
          } else {
            this.$Message.warning({
              content: this.$t('common.selectDevice'),
              duration: 2
            })
            return
          }
          if (deviceIds.length >= 1) {
            if (id === 'lightSwitch') {
              this.lightSwitchVisible = true
              this.$nextTick(() => {
                this.$refs.lightSwitch.init(deviceIds)
              })
            }
          }
        } else if (checked === false) {
          // Logic for operations that don't require selection
        }
      }
    },
    changeGroup(){
      this.getGroupList()
      this.selectGroupVisible=true
    },
    addDevice(){
      this.addDeviceVisible=true
      this.deviceInfo= {
        deviceId: "",
        alias: "",
        groupId: this.groupId,
        isAdd: 1
      }
      if (!this.dataForm.group || this.dataForm.group.length === 0) {
        this.groupName = '';
      }
    },
    deviceAddCancel(){
      this.addDeviceVisible=false
      this.groupName = '';
      this.groupId = null;
    },
    deviceAddFormSubmit(){
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.deviceInfo.groupId = this.groupId
          this.$http({
            url: this.$http.adornUrl(`/cat1/device/${this.deviceInfo.isAdd===1 ? 'add' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.deviceInfo)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success(this.$t('common.operationSuccessful'));
              this.addDeviceVisible=false;
              this.getDataList('loading', this.deviceInfo.isAdd === 1 ? 1 : null);
              this.groupName = '';
              this.groupId = null;
            }else {
              this.$Message.error(data.msg)
            }
          })
        } else {
          this.$Message.error(this.$t('common.validateError'));
        }
      });
    },
    tipChange(tip){
      if (tip=="控制卡连接已断开"){
        return this.$t('monitor.offLineOrNotExist')
      } else if (tip === "操作成功" && this.$i18n.locale !== 'zh-CN') {
        return this.$t('tips.operationSuccess');
      }
      else {
        return tip
      }
    },
    dataFormSubmit () {
      this.dataForm.group=[]
      this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      if (!this.rootNode) {
        this.groupId = null;
        this.groupName = '';
      } else {
        this.groupId = this.rootNode.id;
        this.getChildrenNodes(this.rootNode);
        this.groupName = this.rootNode.name;
      }
      this.selectGroupVisible=false
      this.getDataList('loading',1)
    },
    updateHandle(id){
      this.addDeviceVisible=true
      this.deviceInfo.isAdd = 0;
      this.$http({
        url: this.$http.adornUrl('/cat1/device/get/'+id),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0 && data.data) {
          this.deviceInfo.deviceId = data.data.deviceId;
          this.deviceInfo.alias = data.data.alias;
          this.deviceInfo.groupId = data.data.groupId;
          this.groupId = data.data.groupId;
          this.groupName = this.findGroupNameById(this.groupList, data.data.groupId);
        }else {
          this.$Message.error(data.msg || this.$t('common.requestFailed'));
          this.addDeviceVisible = false;
        }
      }).catch(() => {
        this.$Message.error(this.$t('common.requestFailedNetwork'));
        this.addDeviceVisible = false;
      });
    },
    findGroupNameById(groupList, groupId) {
      if (!groupId) return '';
      for (const group of groupList) {
        if (group.id === groupId) {
          return group.name;
        }
        if (group.children && group.children.length > 0) {
          const foundName = this.findGroupNameById(group.children, groupId);
          if (foundName) return foundName;
        }
      }
      return '';
    },
    deleteHandle(id){
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/cat1/device/delete/'+id),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 1.5,
                onClose: () => {
                  if (this.dataList.length === 1 && this.pageIndex > 1) {
                    this.pageIndex--;
                  }
                  this.getDataList('loading', null);
                  this.dataListSelections = [];
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    //取消选择分组
    cancelSelect(){
      this.selectGroupVisible=false
      this.dataForm.group=[]
      this.groupName=""
      this.rootNode=null
      this.getDataList()
      this.getGroupList()
    },
    //获取该分组及其子分组的groupId
    getChildrenNodes(rootNode){
      if (!rootNode) return;
      this.dataForm.group.push(rootNode.id)
      var childNode=rootNode.children;
      if (childNode){
        for (var i=0; i<childNode.length; i++) {
          this.getChildrenNodes(childNode[i])
        }
      }
    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.groupList.forEach(item=>{
        groupedNum+=item.count;
      })
      let unGroupNum = this.totalNum - groupedNum;
      var unGroupObj={
        "id": -1,
        "name": this.$t('common.unclassified'),
        "count": unGroupNum >= 0 ? unGroupNum : 0,
        "children":[],
        "expand":true
      }
      const unclassifiedExists = this.groupList.some(g => g.id === -1);
      if (!unclassifiedExists) {
        this.groupList.push(unGroupObj);
      } else {
        const existingUnclassified = this.groupList.find(g => g.id === -1);
        if (existingUnclassified) existingUnclassified.count = unGroupNum >=0 ? unGroupNum : 0;
      }
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
    handleMinimize (isMinimize) {
      this.isMinimize = !isMinimize
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    },
  },
  computed: {
    tableHeight: {
      get() {
        const baseHeight = Number(this.$store.state.common.tableHeight) || 600;
        return baseHeight - 155
      }
    }
  },
  watch: {
    'totalNum': function (newVal, OldVal) {
      if (newVal !== OldVal) {
        this.getGroupList();
      }
    },
    'tableHeight': function(newVal, oldVal) {
      if (this.isMinimize == true) {
        this.tableHeightData = newVal + 130
      } else {
        this.tableHeightData = newVal
      }
    }
  },
}
</script>

<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 180px;
  background: #eee;
  overflow: hidden;
  overflow-y: auto;
}

.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}

.opera_ul li {
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 100px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}

.opera_list {
  padding-top: 10px;
  width: 105px;
  height: 105px;
  margin-left: 8%;
}
.opera_ul li:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}

.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}
.opera_ul1 li{
  text-align: center;
  float: left;
  list-style: none;
  width: 40px;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list1 {
  padding-top: 5px;
  width: 40px;
  height: 40px;
}
.opera_ul1 li:hover {
  background-color: rgb(210, 174, 245);
  cursor: pointer;
}
.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}

.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
/deep/
.ivu-table-cell{
  padding-left: 0;
  padding-right: 0;
}
</style>
