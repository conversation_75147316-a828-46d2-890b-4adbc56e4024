<template>
  <div>
    <Modal v-model="visible" width="900">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.LightingLevel')}}</span>
        </p>
        <Tabs :value="tabVal" @on-click="handlerTab" style="height: 650px">
          <TabPane :label="$t('operation.LightingLevel')" name="tab1">
            <!-- <Alert type="warning" show-icon ><b class="tip">{{$t('common.brightnessClears')}}</b></Alert> -->
            <Form label-position="left" label-colon>
              <FormItem >
                <Row>
                    <Col span="1"><span style="font-size:15px;">{{$t('cardDevice.brightness')}}:</span></Col>
                    <Col span="11"><SliderDrag  :value="data.brightness" @SetOpacityConfig="SetOpacityConfig" :key="reloadBrightness"></SliderDrag></Col>
                    <Col span="12"><Button  :loading="modal_loading"  type="primary" @click="screenBrightness()">{{$t('common.set')}}</Button>
                    <Button  :loading="queryScreenBrightnessLoading"  type="success" @click="queryScreenBrightness()">{{$t('common.query')}}</Button>
                    </Col>
                </Row>
              </FormItem>
              <div v-if="resultData.length > 0" style="height: 510px;overflow-y: auto">
                <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
              </div>
              <div v-if="queryScreenBrightnessList.length > 0" style="height: 510px;overflow-y: auto">
                <cardResult :ids="ids" :resultData="queryScreenBrightnessList" :cardItemWidth="900 / 2 - 50" :isQuery="true" 
                :resultItem="[{text: 'cardDevice.brightness', name: 'value', unit: '%'}]"></cardResult>
              </div>
            </Form>
          </TabPane>
          <TabPane :label="$t('operation.timingBrightness')" name="tab2">
            <!-- <Alert type="warning" show-icon >
              <b class="tip">{{$t('tips.timingBrightness')}}</b>
            </Alert> -->
            <Form inline label-position="left">
              <!-- <Button  type="primary" @click="setTimedBrightness()">{{$t('card.setTiming')}}</Button> -->
              <FormItem>
              <Input size="large" v-model="name" :placeholder="$t('common.name')"></Input>
                </FormItem>
                <FormItem>
                  <Button style="margin-right:6px" @click="getDataList()"  size="large">
                    <div style="margin:3px 8px">{{$t('common.query')}}</div>
                  </Button>
                  <Button style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
                    <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
                  </Button>
                </FormItem>
            </Form>
            <Table border :columns="dataConlums" :data="dataList"
            :loading="dataListLoading" style="width: 100%" :max-height="300" ref="selection">
              <template slot-scope="{ row, index }" slot="operation">
                  <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading" @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
                  <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
                  <Button type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
              </template>
            </Table>
            <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
            @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
            <Form style="height: 180px;overflow-y: auto;clear: both;" :label-width="30" label-position="left">
              <div v-if="schedulesResultData.length > 0">
                <cardResult :ids="ids" :resultData="schedulesResultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
              </div>
            </Form>
          </TabPane>
          <TabPane :label="$t('operation.queryTiming')" name="tab3">
            <Form inline :label-width="30" label-position="left">
              <!-- <Button  type="primary" @click="setTimedBrightness()">{{$t('card.setTiming')}}</Button> -->
              <Button :loading="getTimed_loading"  type="success" @click="getTimedBrightness()">{{$t('card.getTiming')}}</Button>
              <div style="height: 530px;overflow-y: auto">
                <div v-if="timedBrightness.length > 0">
                  <cardResult :ids="ids" :resultData="timedBrightness" :cardItemWidth="800" :isQuery="true" 
                    :isTable="true" tableFieldNameLv1='lightingValueTask' tableFieldNameLv2='schedules'
                    :tableFieldOther="[{text: 'card.defaultBrightness', name: 'defaultBrightness', unit: '%'}]"
                    :tableColumns='dataColumns' :tableHeight="200"></cardResult>

                </div>

                <!-- <div v-if="timedBrightness.length > 0" v-for="(item,index) in timedBrightness" :key="index">
                  <FormItem label="ID">
                    <div v-if="item.deviceId">{{item.deviceId}}
                      <span v-if="item.lightingValueTask && item.lightingValueTask !== 'null'"> --- {{$t('card.defaultBrightness')}}: {{item.lightingValueTask.defaultBrightness}}</span>
                    </div>
                    <div v-else-if="item.cardId">{{item.cardId}}
                      <span v-if="item.lightingValueTask && item.lightingValueTask !== 'null'"> --- {{$t('card.defaultBrightness')}}: {{item.lightingValueTask.defaultBrightness}}</span>
                    </div>
                  </FormItem>
                  <br/>
                  <FormItem>
                    <div v-if="item.success === true">
                      <div v-if="item.lightingValueTask === undefined || item.lightingValueTask === null || item.lightingValueTask === 'null'">{{$t('card.noTiming')}}</div>
                      <div v-else v-for="(item,index) in item.lightingValueTask.items" :key="index">
                        <div>{{$t('card.timingBrightness')}}： {{item.brightness}}</div>
                        <Table :columns="dataColumns" :data="item.schedules"></Table>
                      </div>
                    </div>
                    <div v-if="item._type !== 'success'">
                      <div style="color: red;" v-if="item.msg">{{item.msg}}</div>
                      <div style="color: red;" v-else-if="item.errorMessage">{{item.errorMessage}}</div>
                    </div>
                  </FormItem>
                  <Divider/>
                </div> -->
              </div>
            </Form>
          </TabPane>
        </Tabs>
        <div slot="footer" style="text-align: left;">
          <span>
              <!-- <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert> -->
          </span>
          <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
          </Breadcrumb>
          </div>
        </div>
    </Modal>
    <!-- 定时-->
    <device-schedules v-if="setTimingVisible" ref="deviceSchedules" @schedules-result-data="getDataList"></device-schedules>
  </div>
</template>


<script>
import deviceSchedules from './device-schedules'
import SliderDrag from '@/utils/SliderDrag'
import cardResult from "@/utils/cardResult.vue"
export default {
  data () {
    return {
      setTimingVisible: false,
      visible: false,
      tabVal: '',
      data: {
        brightness: 1
      },
      ids: [],
      dataForm: {},
      modal_loading: false,
      queryScreenBrightnessLoading: false,
      queryScreenBrightnessList: [],
      getTimed_loading: false,
      timedBrightness: [],
      dataColumns: [
        {
          title:  this.$t('card.timingBrightness'),
          key: 'brightness',
          align: 'center',
          width: '200',
          render: (h, { row, index }) => {
            return h('span', {}, row.brightness + '%')
          },
          renderHeader: (h) => {
            return h('div', this.$t('card.timingBrightness'))
          },
        },
        {
          title: this.$t('card.DateRange'), // 日期范围
          key: 'schedules.dateType',
          align: 'center',
          width: '200',
          render: (h, {row, index}) => {
            var result = ''
            if (row.schedules.dateType === 'Range') {
              result = [
                h('span', {}, row.schedules.startDate),
                h('span', {}, ' -- '),
                h('span', {}, row.schedules.endDate)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.DateRange'))
          }
        },
        {
          title: this.$t('card.timeFrame'), // 时间范围
          key: 'schedules.timeType',
          align: 'center',
          width: '200',
          render: (h, {row, index}) => {
            var result = ''
            if (row.schedules.timeType === 'Range') {
              result = [
                h('span', {}, row.schedules.startTime),
                h('span', {}, ' -- '),
                h('span', {}, row.schedules.endTime)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.timeFrame'))
          }
        },
        {
          title: this.$t('card.SpecifyWeek'), // 星期范围
          key: 'schedules.filterType',
          align: 'center',
          width: '350',
          tooltip: true,
          render: (h, {row, index}) => {
            var result = ''
            if (row.schedules.filterType === 'Week') {
              result = [
                h('span', {}, row.schedules.week + ',')
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.SpecifyWeek'))
          }
        }
      ],
      resultData: [],
      schedulesResultData: [],
      name: '',
      dataConlums: [
        // {type: 'selection', width: 60, align: 'center'},
        // {title: 'ID', key: 'id', width: 80, align: 'center'},
        {title: this.$t('common.name'), key: 'name', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.name'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      addOrUpdateVisible: false,
      reloadBrightness: 0,
    }
  },
  components: {
    deviceSchedules,
    SliderDrag,
    cardResult
  },
  methods: {
    // 初始化
    init (ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
      }
    },
    handlerTab (name) {
      this.tabVal = name
      if (this.tabVal === 'tab2') {
        this.getDataList()
      }
      this.clearLoading()
    },
    // 查询定时
    getTimedBrightness () {
      this.clearData()
      if (this.ids.length > 0) {
        this.getTimed_loading = true
        var cardIds=this.ids.join(",")
          this.$http({
            url: this.$http.adornUrl('/lighting/query/getTimedBrightness'),
            method: 'get',
            params: this.$http.adornParams({'cardIds':cardIds})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.timedBrightness=data.data
            } else {
              this.$Message.error(data.msg)
            }
            this.getTimed_loading = false
          })
      }
    },
    // 设置定时
    addOrUpdateHandle (id) {
      this.setTimingVisible = true
      this.clearData()
      this.$nextTick(() => {
        // 传入1表示当前是一个定时亮度任务
        this.$refs.deviceSchedules.init(1, id)
      })
    },
    //查询亮度
    queryScreenBrightness() {
      this.clearData();
      this.queryScreenBrightnessLoading = true
      if (this.ids.length>0){
        var ids=this.ids.join(",")
        this.$http({
          url: this.$http.adornUrl("/lighting/query/lightingValue"),
          method:  'get',
          params: this.$http.adornParams({'cardIds':ids})
        }).then(({data}) => {
          if (data&&data.code===0){
            this.queryScreenBrightnessList=data.data
          }else {
            this.$Message.error(data.msg)
          }
          this.queryScreenBrightnessLoading = false
        })
      }
    },
    // 设置亮度
    screenBrightness () {
      this.clearData()
      if (this.ids.length > 0) {
        this.modal_loading = true
          this.$http({
            url: this.$http.adornUrl('/lighting/set/lightingValue'),
            method: 'post',
            data: this.$http.adornData({
              'ids': this.ids,
              'value': this.data.brightness
            })
          }).then(({data}) => {
            if (data&&data.code===0) {
              this.resultData=data.data
            }else {
              this.$Message.error(data.msg)
            }
              this.modal_loading = false
          })
      }
    },
    clearData () {
      this.timedBrightness = []
      this.resultData = []
      this.schedulesResultData = []
      this.queryScreenBrightnessList = []
    },
    SetOpacityConfig(val){
      this.data.brightness = val
    },
     // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/lighting/schedules/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
          'type': 1
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/lighting/schedules/delete'),
            method: 'post',
            data: this.$http.adornData(id, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 设置定时
    setTimeHandle (id) {
      if (this.ids.length > 0) {
        this.schedulesResultData = []
        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading = true;
          }
        })
          this.$http({
            url: this.$http.adornUrl('/lighting/set/schedules'),
            method: 'post',
            data: this.$http.adornData({
              'id': id,
              'deviceIds': this.ids
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.schedulesResultData = data.data
            }else {
              this.$Message.error(data.msg)
            }
              this.dataList.map(item => {
                if (item.id === id) {
                  item.timeLoading = false;
                }
              })
          })
      }
    },
    clearLoading(){
      this.modal_loading=false
      this.getTimed_loading=false
      this.queryScreenBrightnessLoading = false
      this.dataListLoading = false
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.clearData()
        this.clearLoading()
        this.$emit('refreshDataList')
        this.data.brightness = 1
        this.reloadBrightness += 1
      }
    }
  }
}
</script>
