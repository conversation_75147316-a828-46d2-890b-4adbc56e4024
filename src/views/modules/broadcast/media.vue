<template>
   <div class="modiles-media">
     <Tabs type="card" value="my" v-if="userInfo.isExamineUser" @on-click="tabsClick">
        <TabPane :label="$t('screen.my')" name="my">
          <media-query-and-table ref="mediaQueryAndTable"></media-query-and-table>
        </TabPane>
        <TabPane :label="$t('screen.pending')" name="todo">
          <media-query-and-table-todo ref="mediaQueryAndTableTodo"></media-query-and-table-todo>
        </TabPane>
    </Tabs>
    <div v-else>
      <media-query-and-table ref="mediaQueryAndTable"></media-query-and-table>
    </div>
   </div>
</template>

<script>
import mediaQueryAndTable from './media-query-and-table.vue'
import mediaQueryAndTableTodo from './media-query-and-table.vue'
export default {
  data () {
    return {
      istoDo: false
    }
  },
  activated () {
    this.getDataList()
  },
  components: {
    mediaQueryAndTable,
    mediaQueryAndTableTodo
  },
  methods: {
    tabsClick (name) {
      if (name === 'todo') {
        this.istoDo = true
        this.getDataListTodo()
      } else {
        this.istoDo = false
        this.getDataList()
      }
    },
    // 获取数据列表
    getDataList () {
      this.$nextTick(() => {
        this.$refs.mediaQueryAndTable.init(this.istoDo)
      })
    },
    // 获取数据列表
    getDataListTodo () {
      this.$nextTick(() => {
        this.$refs.mediaQueryAndTableTodo.init(this.istoDo)
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>

<style scoped>
</style>
