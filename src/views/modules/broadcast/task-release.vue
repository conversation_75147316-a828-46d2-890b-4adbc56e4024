<template>
    <div>
        <Modal v-model="visible" width="1000">
            <p slot="header" style="text-align:center">
                <span>{{$t('common.release')}}</span>
            </p>
            <div>
              <Alert type="warning" show-icon ><b class="tip">{{$t('tips.broadcastTaskRelease')}}<br/></b></Alert>
                <Form :inline="true" :model="dataForm">
                    <FormItem>
                        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
                    </FormItem>
<!--                    <FormItem>-->
<!--                        <Select size="large" v-model="dataForm.group" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('group.name')">-->
<!--                            <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{item.cardCount}})</Option>-->
<!--                        </Select>-->
<!--                    </FormItem>-->
                    <FormItem>
                        <Button style="margin-right:6px" @click="getDataList()"  size="large">
                            <div style="margin:3px 8px">{{$t('common.query')}}</div>
                        </Button>
                    </FormItem>
                  <FormItem>
                    <Button size="large" @click="changeGroup()">{{groupName}}</Button>
                  </FormItem>
                </Form>
                <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle"  @on-row-click="selectThisRow"
                :loading="dataListLoading" style="width: 100%" :height="300" ref="selection">
                <template slot-scope="{ row, index }" slot="online">
                  <div v-if="row.isOn === 1 ">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#on-line"></use>
                      </svg>
                  </div>
                  <div v-else>
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#line"></use>
                      </svg>
                  </div>
                </template>
                <template slot-scope="{ row, index }" slot="ipColumnProgram">
                  <div v-if="row.ipColumnProgram && row.ipColumnProgram=='无广播'">
                    {{ $t('broadcast.noBroadcast')  }}
                  </div>
                  <div v-else>
                    {{row.ipColumnProgram}}
                  </div>
                </template>
                <template slot-scope="{ row, index }" slot="progress">
                    <div v-if="row.progress && row.progress !== 0" style="height: 20px"><Progress :stroke-width="15" :percent="row.progress" :stroke-color="['#108ee9', '#87d068']" status="active"/></div>
                    <div v-else-if="row.msg">
                        <div v-if="row.isError===true">
                            <Poptip trigger="hover" :title="$t('screen.errorDetails')" width="300" placement="bottom-start" transfer>
                                <div slot="content" class="poptipExplain">{{row.msg}}</div>
                                <Button size="small" type="error">{{$t('screen.errorDetails')}}</Button>
                            </Poptip>
                        </div>
                        <div v-else>{{row.msg}}</div>
                    </div>
                    <div v-else>{{$t('screen.null')}}</div>
                </template>
                </Table>
                <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
                show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
                @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
            </div>
            <div slot="footer">
                <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
                <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()" :disabled="dataListSelections.length <= 0">{{$t('common.confirm')}}</Button>
            </div>
        </Modal>

      <!--分组弹出框-->
      <Modal v-model="selectGroupVisible" width="500">
        <p slot="header" style="text-align:center">
          <span>{{$t('common.selectGroup')}}</span>
        </p>
        <Alert type="info" show-icon >
          <span>{{this.$t('tips.groupTip')}}</span>
        </Alert>
        <div>
          <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
        </div>
        <div slot="footer">
          <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
          <Button type="primary" size="large" @click="groupFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
      </Modal>
    </div>
</template>

<script>
export default {
    data () {
        return {
            visible: false,
            loading: false,
            dataForm: {
                key: '',
                group: []
            },
            dataConlums: [
                {type: 'selection', width: 60, align: 'center'},
                {title: this.$t('cardDevice.deviceName'), key: 'alias', align: 'center',
                    renderHeader:(h)=>{
                        return h('div',this.$t('cardDevice.deviceName'))
                    }
                },
                {title: 'ID', key: 'deviceId', width: 132, align: 'center'},
                // {title: this.$t('cardDevice.programTask'), key: 'currentProgramName', align: 'center', tooltip: true},
                {title: this.$t('cardDevice.online'), key: 'isOn', width: 70, slot: 'online', align: 'center',
                    renderHeader:(h)=>{
                      return h('div',this.$t('cardDevice.online'))
                    }
                },
                // {title: this.$t('cardDevice.networkType'), key: 'netType', align: 'center',
                //     renderHeader:(h)=>{
                //         return h('div',this.$t('cardDevice.networkType'))
                //     }
                // },
                {title: this.$t('cardDevice.broadcastTask'), key: 'ipColumnProgram', width: 200, align: 'center', tooltip: true,slot: 'ipColumnProgram',
                  renderHeader:(h)=>{
                    return h('div',this.$t('cardDevice.broadcastTask'))
                  }
                },
                {title: this.$t('screen.showProgress'), key: 'progress', align: 'center',tooltip: true,slot: 'progress',width: 280,
                    renderHeader:(h)=>{
                        return h('div',this.$t('screen.showProgress'))
                    }
                },
            ],
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataListSelections: [],
            programId: '',
            // timerList: [],
            groupList: [],
          //选择分组时，分组框是否可见
          selectGroupVisible:false,
          //  分组名
          groupName:this.$t('common.selectGroup'),
          rootNode:null,
          //是否第一次打开该页面
          isFirst:true,
          //总数量
          totalNum:0,
          successArr: []
        }
    },
    methods: {
        // 初始化
        init (programId) {
            this.visible = true
            this.loading=false
            this.getDataList()
            this.getGroupList()
            this.programId = programId
            this.groupName=this.$t('common.selectGroup')
            this.rootNode=null
            this.dataForm={
              key: '',
              group: []
            }
            this.isFirst=true
            //总数量
            this.totalNum=0
        },
        // 查询分组列表
        getGroupList () {
            this.$http({
                url: this.$http.adornUrl('/sys/group/select'),
                method: 'get',
                params: this.$http.adornParams({
                  'radioState': 0
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.groupList = data.group
                    this.getUnGroupDevice()
                } else {
                    this.groupList = []
                }
            })
        },
        getDataList () {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/broadcast/task/releaseList'),
                method: 'post',
                data: this.$http.adornData({
                    'page': this.pageIndex+"",
                    'limit': this.pageSize+"",
                    'key': this.dataForm.key,
                    'group':this.dataForm.group,
                    'radioState':0
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.page.list
                    this.totalPage = data.page.totalCount
                  if (this.dataForm.group.length===0){
                    this.groupName=this.$t('common.selectGroup')
                  }
                  if (this.isFirst){
                    this.totalNum=data.page.totalCount
                    this.isFirst=false
                  }
                  // this.dataForm.group=[]
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                    this.dataListSelections = []
                    this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle (val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
            currentChangeHandle (val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle () {
            this.dataListSelections = this.$refs.selection.getSelection()
        },
        selectThisRow(data, index) {
            this.$refs.selection.toggleSelect(index);
        },
        // 发布
        dataFormSubmit () {
            if (this.dataListSelections.length > 0) {
                this.loading = true
                var deviceIds =  this.dataListSelections.map(item => {
                    return item.deviceId
                })
                    this.$http({
                        url: this.$http.adornUrl('/broadcast/task/release'),
                        method: 'post',
                        data: this.$http.adornParams({
                            'deviceIds': deviceIds,
                            'programId': this.programId
                        })
                    }).then(({data}) => {
                      if (data&&data.code===0){
                        if (data.data){
                          var repData=data.data
                        }
                      }else {
                        this.$Message.error(data.msg)
                      }

                      if (repData.length>0){
                        repData.forEach(i=>{
                          this.dataList=this.dataList.filter( item=>{
                            if (item.deviceId===i.deviceId){
                              if (i._type!=="success"){
                                item.msg=i.msg
                                item.isError=true
                                item.progress = 0
                              }else {
                                item.msg =  this.$t('screen.sendSuccess')
                                item.isError = false
                                item.progress = 0
                                if (i.commandId){
                                  var temp = {deviceId: i.deviceId, commandId: i.commandId, timer: null, progress: 0, status: 0, reason: '', totalTime: 0}
                                  this.successArr.push(temp)
                                }
                              }
                            }
                            return item;
                          })
                        })
                      }
                      if (this.successArr.length>0){
                        var _this = this
                        for (var item in this.successArr) {
                          (function (item) {
                              var date = new Date()
                              var min = date.getMinutes()
                              date.setMinutes(min + 5)
                              _this.successArr[item].totalTime=date.getTime()// 设置总时长为五分钟后
                              _this.successArr[item].timer = setInterval(function () {
                                clearInterval(_this.successArr[item].timer)
                                _this.successArr[item].timer = null
                                _this.successArr[item].progress = 0
                                // 当前时间如果与前面设置的时间相等视为超时
                                if (_this.successArr[item].totalTime <= new Date().getTime()) {
                                  _this.dataList = _this.dataList.filter(data => {
                                    if (data.deviceId === _this.successArr[item].deviceId) {
                                      data.msg =  _this.$t('screen.timesOut')
                                      data.progress = _this.successArr[item].progress
                                      data._disabled = false
                                    }
                                    return data
                                  })
                                } else {
                                  _this.$http({
                                    url: _this.$http.adornUrl(`/broadcast/playLog/info/${_this.successArr[item].commandId}`),
                                    method: 'get',
                                    params: _this.$http.adornParams()
                                  }).then(({data}) => {
                                    if (data && data.code === 0) {
                                      _this.successArr[item].progress = data.broadcastTaskPlayLog.progress
                                      _this.successArr[item].commandId = data.broadcastTaskPlayLog.LogId
                                      _this.successArr[item].deviceId = data.broadcastTaskPlayLog.deviceId
                                      _this.successArr[item].status = data.broadcastTaskPlayLog.status
                                      _this.successArr[item].reason = data.broadcastTaskPlayLog.reason
                                      if (_this.successArr[item].status === 0) {
                                        _this.dataList = _this.dataList.filter(data => {
                                          if (data.deviceId === _this.successArr[item].deviceId) {
                                            data.progress = _this.successArr[item].progress === 0 ? _this.successArr[item].progress + 5 : _this.successArr[item].progress
                                          }
                                          return data
                                        })
                                        if (_this.successArr[item].progress === 100) {
                                          setTimeout(() => {
                                            clearInterval(_this.successArr[item].timer)
                                            _this.successArr[item].timer = null
                                            _this.successArr[item].progress = 0
                                          }, 500)
                                        }
                                      } else {
                                        _this.dataList = _this.dataList.filter(data => {
                                          if (data.deviceId === _this.successArr[item].deviceId) {
                                            data.msg =  _this.successArr[item].reason
                                          }
                                          return data
                                        })
                                        _this.successArr[item].timer = null
                                        _this.successArr[item].progress = 0
                                        _this.successArr[item].reason = ''
                                        _this.successArr[item].status = 0
                                      }
                                    } else {
                                      _this.$Message.error(_this.$t('screen.failedProgress'))
                                      _this.successArr[item].timer = null
                                      _this.successArr[item].progress = 0
                                    }
                                  })
                                }
                                if (_this.dataListSelections && _this.dataListSelections.length !== 0) {
                                  var select = _this.dataListSelections.map(item => {return item.deviceId})
                                  if (select && select.length !== 0) {
                                    _this.dataList.map(item => {
                                      if (select.indexOf(item.deviceId) != -1) {
                                        item._checked = true
                                      } else {
                                        item._checked = false
                                      }
                                    })
                                  }
                                }
                              }, 2000)
                          })(item)
                        }
                      }
                      this.loading = false
                      this.dataListSelections = []
                    })
            } else {
                this.$Message.error(this.$t('screen.selectCard'))
            }
        },
        changeGroup(){
          this.getGroupList()
          this.selectGroupVisible=true
        },
        // 表单提交
        groupFormSubmit () {
          this.dataForm.group=[]
          this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
          this.getChildrenNodes(this.rootNode)
          this.selectGroupVisible=false
          this.getDataList()
          this.groupName=this.$t('common.selectingGroup')+":"+this.rootNode.name
          this.rootNode=null
        },
        cancelSelect(){
          this.selectGroupVisible=false
          this.dataForm.group=[]
          this.groupName=this.$t('common.selectGroup')
          this.rootNode=null
          this.getDataList()
          this.getGroupList()

        },
        //获取该分组及其子分组的groupId
        getChildrenNodes(rootNode){
          this.dataForm.group.push(rootNode.id)
          var childNode=rootNode.children;
          if (childNode){
            for (var i=0; i<childNode.length; i++) {
              this.getChildrenNodes(childNode[i])
            }
          }
        },
        renderContent (h, { root, node, data }) {
          return h('span', {
            style: {
              display: 'inline-block',
              width: '100%'
            }
          }, [
            h('span', [
              h('span', data.name+"("+data.count+")")
            ])
          ]);
        },
      //获取未分组的设备
      getUnGroupDevice(){
        var groupedNum=0;
        this.unGroupNum=0;
        this.groupList.map(item=>{
          groupedNum+=item.count;
        })
        this.unGroupNum=this.totalNum-groupedNum;
        var unGroupObj={
          "id":-1,
          "name": this.$t('common.unclassified'),
          "count":this.unGroupNum,
          "children":[],
          "expand":true
        }
        this.groupList.push(unGroupObj)
      },
    },
    watch: {
        'visible': function (newVal, oldVal) {
            if (newVal === false) {
                /* if (this.timerList.length > 0) {
                    this.timerList.forEach(item => {
                        clearInterval(item)
                    })
                }
                this.timerList = [] */
                if (this.successArr.length > 0) {
                    for (let i = 0; i < this.successArr.length; i++) {
                        const element = this.successArr[i];
                        if (element.timer) {
                            clearInterval(element.timer)
                        }
                    }
                }
                this.successArr = []

            }
        }
    }
}
</script>
<style scoped>
.adv {
  position: fixed;
  right: 10px;
  bottom: 10px;
  border-radius: 2%;
  background-color: rgb(255, 255, 255);
  width: 250px;
  height: 150px;
  padding: 5px;
  overflow: hidden;
}
.poptipExplain{
    /*float: right;*/
    /* width: 150px; */
    height: 200px;
    white-space: normal;
    word-break: break-all;
    overflow-x:hidden;
    overflow-y: auto;
    line-height: 25px;
    font-size: 14px;
}
</style>
