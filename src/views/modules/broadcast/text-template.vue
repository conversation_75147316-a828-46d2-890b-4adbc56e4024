<template>
    <Modal v-model="visible" width="900" footer-hide>
      <p slot="header" style="text-align:center;font-size: 20px;">
          <span>{{$t('task.TemplateContent')}}</span>
      </p>
      <div>
        <Collapse v-model="textTemplate" accordion>
          <Panel :name="item.name" v-for="item in textTemplateList" :key="item.name">
            {{item.title}}
            <p slot="content">
              <span>{{item.content}}</span>
              <br/>
              <a href="javascript:void(0)" @click="importHandle(item.content)">{{$t('task.import')}}</a>
            </p>
          </Panel>
        </Collapse>
      </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      textTemplate: '1',
      textTemplateList: [
        {
          name: '1',
          title: '抗疫',
          content: '广大居民朋友，近日，新型冠状病毒感染的肺炎疫情蔓延，为做好疫情防空工作，维护社区居民健康平安，我们倡议：一，出入社区自觉扫码，并主动接受测量体温；二，尽量减少外出活动，不去疾病正在流行的地区，减少去人员密集场所；三，做好个人防护，外出必须佩戴口罩，勤洗手，多通风，防控疫情人人有责，让我们行动起来，为自己，为家人，为他人齐心协力，众志成城，携手抗击疫情。'
        },
        {
          name: '2',
          title: '交通',
          content: '过马路，斑马线，不嬉戏，不逗留；红灯停，绿灯行；乘汽车，安全带；坐电车，头盔戴；要开车，不喝酒；规矩守，行无忧。'
        },
        {
          name: '3',
          title: '消防',
          content: '消防安全不放松，家庭幸福乐融融；预防暗火常警惕，根除隐患无祸殃；易燃杂物日清理，耗电设施标准装；消防演练经常搞，火灾损失能减少。'
        },
        {
          name: '4',
          title: '防溺水',
          content: '安全牢牢记在心，夏天游泳防溺水；河沟水库和池塘，千万不能胡乱行；会游的，别逞能，水深沟底乱草生；千万注意防险情，安全牢牢记在心。'
        },
        {
          name: '5',
          title: '饮食安全',
          content: '饮食卫生记心头，饭前必须要洗手；用餐时间不嬉笑，细嚼慢咽消化好；无证摊贩东西脏，不洁食品不进口；自带午饭要加热，生吃瓜果要洗净。'
        },
      ]
    }
  },
  methods: {
    // 初始化
    init () {
      this.visible = true
    },
    importHandle (content) {
      this.visible = false
      this.$emit('resultData', content)
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.textTemplate= '1'
      }
    }
  }
}
</script>
