<template>
  <div>
    <Modal v-model="visible" width="600" footer-hide>
        <p slot="header" style="text-align:center">
            <span>{{$t('file.examine')}}</span>
        </p>
        <Steps :current="current" status="process" direction="vertical" style="height: 300px">
            <Step v-if="data && data.length > 0" v-for="(item,index) in data" :key="index">
              <div slot="title">{{item.auditTime == null ? item.userName : item.userName + $t('file.auditTime') + item.auditTime}}</div>
              <div slot="content">
                  <div v-if="item.status === 0">{{$t('file.checkPending')}}</div>
                  <div v-if="item.status === 1">{{$t('approval.noAudit')}}</div>
                  <div v-if="item.status === 2">{{$t('approval.auditFailed')}}</div>
                  <div v-if="item.status === 3">{{$t('approval.approved')}}</div>
                  <div>{{item.memo}}</div>
                  <Button size="small" type="success" v-if="current === index && userInfo.userId === item.userId && item.status === 0"  :loading="loading"
                  style="margin-top: 20px;float:right" @click="handleExamine(item.userId)">{{$t('file.examine')}}</Button>
              </div>
            </Step>
        </Steps>
        <Button type="warning"  style="float: right" v-if="userInfo.userId === 1 && oneClickAudit" :loading="loading" @click="handleExamine(1)">{{ $t('approval.clickAudit') }}</Button>
    </Modal>
    <!-- 相关人员审核-->
    <task-to-examine v-if="toExamineVisible" ref="taskToExamine" @refreshData="closeThisModal"></task-to-examine>
  </div>
</template>

<script>
import taskToExamine from './task-to-examine'
export default {
  data () {
    return {
      visible: false,
      data: [],
      current: 0,
      loading: false,
      id: [],
      toExamineVisible: false,
      oneClickAudit: false
    }
  },
  methods: {
    // 初始化
    init (id) {
      if (this.id) {
        this.id = id
        if (id.length>1){
          this.handleExamine()
        }else {
          var tid = id[0]
            this.$http({
              url: this.$http.adornUrl(`/broadcast/task/examine`),
              method: 'get',
              params: this.$http.adornParams({'id': tid})
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.data = data.examine
                this.visible = true
                if (this.data && this.data.length > 0) {
                  // 如果存在已经处理过的流程
                  var current = this.data.filter(item => item.status !== 0)
                  if (current && current.length > 0) {
                    this.current = current.length === 1 ? 1 : current.length - 1
                  }
                  // 如果有未处理的流程
                  var untreated = this.data.filter(item => item.status === 0)
                  if (untreated && untreated.length > 0) {
                    this.oneClickAudit = true
                  } else {
                    this.oneClickAudit = false
                  }
                }
              }
            })
          }
      }
    },
    // 处理审核
    handleExamine (userId) {
      this.toExamineVisible = true
      this.$nextTick(() => {
        // 传入审核人以及审核内容的ID
        this.$refs.taskToExamine.init(userId, this.id)
      })
    },
    // 关闭当前模块
    closeThisModal () {
      this.data = []
      this.current = 0
      this.visible = false
      this.$emit('refreshDataList')
    }
  },
  components: {
    taskToExamine
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.data = []
        this.current = 0
      }
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>
