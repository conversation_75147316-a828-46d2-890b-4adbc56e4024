<!-- 智慧广播 -->
<template>
   <div class="modiles-device">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
<!--      <FormItem>-->
<!--        <Select size="large" v-model="dataForm.group" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('group.name')" @on-change = "getDataList()">-->
<!--          <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{item.cardCount}})</Option>-->
<!--        </Select>-->
<!--      </FormItem>-->
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(null,1)"  size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button style="margin-right:6px" size="large" @click="changeGroup()">
          <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
        </Button>
        <Button style="margin-right:6px" @click="getTerminalInfo()" :loading="terminalInfoLoading"  type="primary" size="large" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{$t('cardDevice.queryTerminalInfo')}}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
      :loading="dataListLoading" :height="tableHeightData" ref="selection">
      <template slot-scope="{ row, index }" slot="number">
        {{index+1}}
      </template>
      <template slot-scope="{ row, index }" slot="deviceId">
        {{row.deviceId}}
        <span v-if="row.msg === 1">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#send"></use>
          </svg>
        </span>
        <span v-if="row.msg === 2">
          <Poptip placement="right-start" v-if="row.text && row.text !== ''"
          trigger="hover" transfer :title="$t('common.tips')" :content="tipChange(row.text)">
            <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
              <use xlink:href="#fail"></use>
            </svg>
          </Poptip>
          <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#fail"></use>
          </svg>
        </span>
        <span v-if="row.msg === 3">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#success"></use>
          </svg>
        </span>
      </template>
      <template slot-scope="{ row, index }" slot="online">
        <div v-if="row.isOn === 1 ">
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#on-line"></use>
            </svg>
         </div>
         <div v-else>
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#line"></use>
            </svg>
         </div>
      </template>
      <template slot-scope="{ row, index }" slot="netType">
         <div v-if="row.netType === 'WIFI'">
            {{row.netType}}
            <svg v-if="row.rssi >= -50 && row.rssi <= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#WIFI-level4"></use>
            </svg>
            <svg v-else-if="row.rssi >= -70 && row.rssi < -50" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#WIFI-level3"></use>
            </svg>
            <svg  v-else-if="row.rssi >= -80 && row.rssi < -70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#WIFI-level2"></use>
            </svg>
            <svg v-else-if="row.rssi >= -100 && row.rssi < -80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#WIFI-level1"></use>
            </svg>
         </div>
         <div v-else-if="row.netType === 'LTE' || row.netType === 'UMTS' || row.netType === 'HSPA'
         || row.netType === 'HSPA+' || row.netType === 'EDGE'  || row.netType === 'GPRS'">
            {{row.netType}}
            <svg v-if="row.asu >= 12 && row.asu != 99" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#gsm-0"></use>
            </svg>
            <svg v-else-if="row.asu >= 8 && row.asu  < 12" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#gsm-1"></use>
            </svg>
            <svg  v-else-if="row.asu >= 5 && row.asu < 8" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#gsm-2"></use>
            </svg>
            <svg v-else-if="row.asu >= 3 && row.asu < 5" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
              <use xlink:href="#gsm-3"></use>
            </svg>
         </div>
         <div v-else>
           {{row.netType}}
         </div>
      </template>
      <template slot-scope="{ row, index }" slot="ipColumnProgram">
        <div v-if="row.ipColumnProgram && row.ipColumnProgram=='无广播'">
          {{ $t('broadcast.noBroadcast')  }}
        </div>
        <div v-else>
          {{row.ipColumnProgram}}
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="radioState">
        <div v-if="row.radioState === 1 ">
          <Tooltip :content="$t('broadcast.radioState1')">
            <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
              <use xlink:href="#radio4"></use>
            </svg>
          </Tooltip>
        </div>
        <div v-else-if="row.radioState===2">
          <Tooltip :content="$t('broadcast.radioState2')">
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#radio3"></use>
          </svg>
          </Tooltip>
        </div>
        <div v-else-if="row.radioState===3">
          <Tooltip :content="$t('broadcast.radioState3')">
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#radio2"></use>
          </svg>
          </Tooltip>
        </div>
        <div v-else-if="row.radioState===4">
          <Tooltip :content="$t('broadcast.radioState4')">
          <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
            <use xlink:href="#radio1"></use>
          </svg>
          </Tooltip>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button  :disabled="row._disabled" type="info" size="small" style="margin-right: 5px;font-size: 11px" @click="broadcastInfo(row.deviceId)">{{$t('common.info')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>

     <!--分组弹出框-->
     <Modal v-model="selectGroupVisible" width="500">
       <p slot="header" style="text-align:center">
         <span>{{$t('common.selectGroup')}}</span>
       </p>
       <Alert type="info" show-icon >
         <span>{{this.$t('tips.groupTip')}}</span>
       </Alert>
       <div>
         <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
       </div>
       <div slot="footer">
         <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
         <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
       </div>
     </Modal>

    <!-- 功能菜单-->
    <div style="height: 20px; background:#eee; clear: both;">
      <svg v-if="!isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuixiaohua"></use>
      </svg>
      <svg v-if="isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuidahua"></use>
      </svg>
    </div>
    <div v-if="!isMinimize" class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in operation" :key="index">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.text)}}</div>
            </div>
            <div class="opera_list" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.text)}}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div v-if="isMinimize" style="height: 50px;" class="opera_div">
      <ul class="opera_ul1">
        <div v-for="(item, index) in operation" :key="index" :title="$t(item.text)">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list1" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
            <div class="opera_list1" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
          </li>
        </div>
      </ul>
    </div>

     <!-- 音量-->
     <device-volume v-if="volumeVisible" ref="deviceVolume" @refreshDataList="getDataList"></device-volume>
     <!-- 详情-->
     <device-info v-if="infoVisible" ref="deviceInfo"></device-info>
    <!-- 清除广播任务 -->
    <device-clear-broadcast-task  v-if="clearBroadcastTask" ref="clearBroadcastTask"></device-clear-broadcast-task>
    <!-- 设置SIP服务器地址 -->
    <device-sip-address v-if="deviceSipAddress" ref="deviceSipAddress"></device-sip-address>
    <!-- 设置一键报警地址 -->
<!--    <device-alarm-address v-if="deviceAlarmAddress" ref="deviceAlarmAddress"></device-alarm-address>-->
    <!--  设置报警音量   -->
<!--    <device-alarm-volume v-if="deviceAlarmVolume" ref="deviceAlarmVolume"></device-alarm-volume>-->
    <!-- 注册SIP账号   -->
    <device-register-sip-user v-if="deviceRegisterSipUser" ref="deviceRegisterSipUser"></device-register-sip-user>
    <!--     查看录音文件-->
     <device-recording-files v-if="deviceRecordingFiles" ref="deviceRecordingFiles"></device-recording-files>
    <!--报警设备网络信息-->
     <device-alarm-network-info v-if="deviceAlarmNetworkInfo" ref="deviceAlarmNetworkInfo"></device-alarm-network-info>
    <!--报警设备通话音量-->
     <device-alarm-call-volume v-if="deviceAlarmCallVolume" ref="deviceAlarmCallVolume"></device-alarm-call-volume>
    <!-- 报警设备一键报警-->
     <device-call-police-address v-if="deviceCallPoliceAddress" ref="deviceCallPoliceAddress"></device-call-police-address>
    <!--报警设备信息-->
     <device-alarm-info v-if="deviceAlarmInfo" ref="deviceAlarmInfo"></device-alarm-info>

  </div>
</template>

<script>

import deviceClearBroadcastTask from './device/device-clear-broadcast-task'
import deviceSipAddress from './device/device-Sip-address.vue'
// import deviceAlarmAddress from './device/device-alarm-address.vue'
// import deviceAlarmVolume from "./device/device-alarm-volume";
import deviceRegisterSipUser from "./device/device-register-sip-user.vue";
import DeviceRecordingFiles from "./device/device-recording-files";
import DeviceAlarmNetworkInfo from "./device/device-alarm-network-info";
import DeviceAlarmCallVolume from "./device/device-alarm-call-volume";
import DeviceCallPoliceAddress from "./device/device-call-police-address";
import DeviceAlarmInfo from "./device/device-alarm-info";
import deviceInfo from "./device-info";
import deviceVolume from "../screen/device/device-volume.vue";
export default {
  data () {
    return {
      dataForm: {
        key: '',
        group: []
      },
      dataConlums: [
        {type: 'selection', width: 60,fixed: 'left', align: 'center'},
        {title: this.$t('cardDevice.number'), width: 70,fixed: 'left', align: 'center',slot: 'number',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.number'))
          }
        },
        {title: 'ID', key: 'deviceId',fixed: 'left', width: 170, align: 'center', slot: 'deviceId'},
        {title: this.$t('cardDevice.deviceName'), key: 'alias', width: 130, align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.deviceName'))
          }
        },
        {title: this.$t('cardDevice.online'), key: 'isOn', width: 100, slot: 'online', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.online'))
          }
        },
        {title: this.$t('cardDevice.networkType'), key: 'netType', width: 130,align: 'center', slot: 'netType',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.networkType'))
          }
        },{title: this.$t('lamp.radioState'), key: 'radioState', width: 130,align: 'center', slot: 'radioState',
          renderHeader:(h)=>{
            return h('div',this.$t('lamp.radioState'))
          }
        },

        {title: this.$t('cardDevice.broadcastTask'), key: 'ipColumnProgram', width: 200, align: 'center', tooltip: true,slot: 'ipColumnProgram',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.broadcastTask'))
          }
        },
        {title: this.$t('cardDevice.volume'), key: 'volume', width: 130, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.volume'))
          }
        },
        {title: this.$t('cardDevice.fireWare'), key: 'fireware', width: 245, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.fireWare'))
          }
        },
        // {title: this.$t('cardDevice.fireWare'), key: 'fireware',width: 245, align: 'center', tooltip: true},
        {title: 'CardSystem', key: 'ledsetVersion',width: 130, align: 'center', tooltip: true},
        {title: 'Conn', key: 'connVersion',width: 130, align: 'center', tooltip: true},
        {title: 'Player', key: 'playerVersion',width: 130, align: 'center', tooltip: true},
        // {title: 'starterVersion', key: 'starterVersion',width: 140, align: 'center', tooltip: true},
        // {title: 'displayVersion', key: 'displayVersion',width: 140, align: 'center', tooltip: true},
        {title: this.$t('operation.group'), key: 'groupName', align: 'center', width: 100, tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('operation.group'))
          }
        },
        {title: "IP", key: 'realIp', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div','IP')
          }
        },
        {title: this.$t('cardDevice.lastOffline'), key: 'lastOffTime', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.lastOffline'))
          }
        },
        {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', width: 150, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        }
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      terminalInfoLoading: false,
      groupList: [],
      operation: [
        {id: 'volumeControl', icon: 'yinliang', text: 'operation.volumeControl', disable: false,auth: 'broadcast:card:volumeControl', checked: true},
        {id: 'clearIpColumnService', icon: 'clear', text: 'operation.clearTask', disable: false,auth: 'broadcast:card:clear', checked: true},
        {id: 'SIPAddress', icon: 'SIPAddress', text: 'operation.callAddress', disable: false,auth: 'broadcast:card:SIPAddress', checked: true},
        // {id: 'alarmAddress', icon: 'alarmAddress', text: 'operation.alarmConfig', disable: false,auth: 'broadcast:card:alarmConfig', checked: true},
        // {id: 'deviceAlarmVolume', icon: 'volumeControl', text: 'operation.AlarmVolume', disable: false,auth: 'broadcast:card:alarmVolumeControl', checked: true},
        {id: 'registerSIPUser', icon: 'registerSIP', text: 'operation.Register_SIP_account', disable: false,auth: 'broadcast:card:registerSIPUser', checked: false},
        {id: 'deviceAlarmInfo', icon: 'alarmDevice', text: 'alarm.alarmDeviceInfo', disable: false,auth: 'broadcast:card:alarmDeviceInfo', checked: true},
        {id: 'deviceCallPoliceAddress', icon: 'alarmAddress', text: 'operation.AlarmAddress', disable: false,auth: 'broadcast:card:alarmAddress', checked: true},
        {id: 'deviceAlarmNetworkInfo', icon: 'alarmNetwork', text: 'alarm.alarmDeviceNetwork', disable: false,auth: 'broadcast:card:alarmNetwork', checked: true},
        {id: 'deviceAlarmCallVolume', icon: 'volumeControl', text: 'alarm.callingVolume', disable: false,auth: 'broadcast:card:alarmVolumeControl', checked: true},
        {id: 'deviceRecordingFiles', icon: 'recording', text: 'operation.recordingFile', disable: false,auth: 'broadcast:card:recordingFile', checked: true},

      ],
      clearBroadcastTask: false,
      deviceSipAddress: false,
      // deviceAlarmAddress: false,
      // deviceAlarmVolume: false,
      deviceRegisterSipUser: false,
      deviceRecordingFiles:false,
      deviceAlarmNetworkInfo:false,
      deviceAlarmCallVolume:false,
      deviceCallPoliceAddress:false,
      deviceAlarmInfo:false,
      infoVisible: false,
      //选择分组时，分组框是否可见
      selectGroupVisible:false,
      //  分组名
      groupName:"",
      rootNode:null,
      //是否第一次打开该页面
      isFirst:true,
      //总数量
      totalNum:0,
      tableHeightData: 0,
      isMinimize: false,
      volumeVisible: false
    }
  },
  activated () {
    this.initData()
    this.getDataList('loading',null)
  },
  methods: {
    // 初始化数据
    initData() {
      this.dataForm = {
        key: '',
        group: []
      }
      this.dataList = []
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListLoading = false
      this.dataListSelections = []
      this.groupName=""
      this.rootNode=null
      this.isFirst=true
      //总数量
      this.totalNum=0
      this.terminalInfoLoading= false
      this.tableHeightData = this.tableHeight
    },
    // 查询分组列表
    getGroupList () {
      this.$http({
        url: this.$http.adornUrl('/sys/group/select'),
        method: 'get',
        params: this.$http.adornParams({
          'radioState': 0
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.groupList = data.group
          this.getUnGroupDevice()
        } else {
          this.groupList = []
        }
      })
    },
    // 获取数据列表
    getDataList (loading,isQuery) {
      // this.pageIndex = 1
      if (loading) {
        this.dataListLoading = true
      }
      if (isQuery){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl('/broadcast/card/list'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex+"",
          'limit': this.pageSize+"",
          'key': this.dataForm.key,
          'group': this.dataForm.group
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.dataForm.group.length===0){
            this.groupName=""
          }
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(loading,isQuery)
          }
          if (this.isFirst){
            this.totalNum = data.page.totalCount
            this.isFirst=false
          }
          // this.dataForm.group=[]
          // 设置选中
          var select = this.$refs.selection.getSelection().map(item => {return item.deviceId})
          if (select && select.length !== 0) {
            this.dataList.map(item => {
              if (select.indexOf(item.deviceId) != -1) {
                item._checked = true
              } else {
                item._checked = false
              }
            })
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 查询终端信息
    getTerminalInfo () {
      // 判断选中的列表不为空
      if (this.dataListSelections.length > 0) {
        var deviceIds = this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.dataList = this.dataList.filter(data => {
            if (deviceIds.indexOf(data.deviceId) !== -1) {
              data.msg =  1
              data._checked = true
            } else {
              data._checked = false
            }
            return data
        })
        this.terminalInfoLoading = true
        this.$http({
            url: this.$http.adornUrl('/card/query/getCardInformation'),
            method: 'post',
            data: deviceIds
          }).then(({ data }) => {
            if (data && data.cards) {
              for (let i = 0; i < data.cards.length; i++) {
                const element = data.cards[i];
                if (element.card.text) {
                  element.card.msg = 2
                } else {
                  element.card.msg = 3
                }
                element.card._checked = true
                this.dataList.forEach((item, index) => {
                  if (element.card.deviceId.indexOf(item.deviceId) !== -1) {
                    this.dataList.splice(index, 1, element.card)
                  } else {
                    item = item
                  }
                })
              }
            } else {
              this.$Message.error(data.msg)
            }
            this.terminalInfoLoading = false
          });
      }
    },
    // 不支持
    operaErrorHandle (text) {
      this.$Message.warning({
        content: this.$t('common.supportedTip') + this.$t(text),
        duration: 2
      })
    },
    // 成功
    operaSuccessHandle (id, checked) {
      if (id) {
        if (checked === true) {
          // 获取已选的卡
          var deviceIds = null
          if (this.dataListSelections.length > 0) {
            deviceIds = this.dataListSelections.map(item => {
              return item.deviceId
            })
          } else {
            this.$Message.warning({
              content: this.$t('common.selectDevice'),
              duration: 2
            })
            return
          }
          if (deviceIds.length >= 1) {
            if (id === 'clearIpColumnService') { // 清除任务
              this.clearBroadcastTask = true
              this.$nextTick(() => {
                this.$refs.clearBroadcastTask.init(deviceIds)
              })
            } else if (id === 'SIPAddress') { // 设置SIP服务器地址
              this.deviceSipAddress = true
              this.$nextTick(() => {
                this.$refs.deviceSipAddress.init(deviceIds)
              })
            } /*else if (id === 'alarmAddress') { // 报警配置
              this.deviceAlarmAddress = true
              this.$nextTick(() => {
                this.$refs.deviceAlarmAddress.init(deviceIds)
              })
            } else if (id === 'deviceAlarmVolume') { // 报警音量控制
              this.deviceAlarmVolume = true
              this.$nextTick(() => {
                this.$refs.deviceAlarmVolume.init(deviceIds)
              })
            }*/else if (id==='deviceRecordingFiles'){ //录音文件
              this.deviceRecordingFiles = true
              this.$nextTick(() => {
                this.$refs.deviceRecordingFiles.init(deviceIds)
              })
            }else if (id==='deviceAlarmNetworkInfo'){ //报警设备网络信息
              if (deviceIds.length>1){
                this.$Message.error("该功能不支持批量操作，请选择一张卡")
              }else {
                this.deviceAlarmNetworkInfo = true
                this.$nextTick(() => {
                  this.$refs.deviceAlarmNetworkInfo.init(deviceIds)
                })
              }
            }else if (id==='deviceAlarmCallVolume'){ //报警通话音量
              if (deviceIds.length>1){
                this.$Message.error("该功能不支持批量操作，请选择一张卡")
              }else {
                this.deviceAlarmCallVolume = true
                this.$nextTick(() => {
                  this.$refs.deviceAlarmCallVolume.init(deviceIds)
                })
              }
            }else if (id==='deviceCallPoliceAddress'){ //报警通话音量
              if (deviceIds.length>1){
                this.$Message.error("该功能不支持批量操作，请选择一张卡")
              }else {
                this.deviceCallPoliceAddress = true
                this.$nextTick(() => {
                  this.$refs.deviceCallPoliceAddress.init(deviceIds)
                })
              }
            }else if (id==='deviceAlarmInfo'){ //报警设备信息
              if (deviceIds.length>1){
                this.$Message.error("该功能不支持批量操作，请选择一张卡")
              }else {
                this.deviceAlarmInfo = true
                this.$nextTick(() => {
                  this.$refs.deviceAlarmInfo.init(deviceIds)
                })
              }
            }else if (id === 'volumeControl') { // 音量
              this.volumeVisible = true
              this.$nextTick(() => {
                this.$refs.deviceVolume.init(deviceIds)
              })
            }
          }
        } else if(checked === false) {
          // 不需要选中卡
          if (id === 'registerSIPUser') { // 注册sip账号
            this.deviceRegisterSipUser = true
            this.$nextTick(() => {
              this.$refs.deviceRegisterSipUser.init()
            })
          }
        }
      }
    },
    // 查询广播详情
    broadcastInfo (id) {
      this.infoVisible = true
      this.$nextTick(() => {
        this.$refs.deviceInfo.init(id)
      })
    },
    changeGroup(){
      this.selectGroupVisible=true
    },
    // 表单提交
    dataFormSubmit () {
      this.dataForm.group=[]
      this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      this.getChildrenNodes(this.rootNode)
      this.selectGroupVisible=false
      this.getDataList('loading',1)
      this.groupName=this.rootNode.name
      this.rootNode=null
    },
    cancelSelect(){
      this.selectGroupVisible=false
      this.dataForm.group=[]
      this.groupName=""
      this.rootNode=null
      this.getDataList()
      this.getGroupList()
    },
    //tip国际化
    tipChange(tip){
      if (tip=="控制卡连接已断开"){
        return this.$t('monitor.offLineOrNotExist')
      }else {
        return this.$t('log.connectionClosed')
      }
    },
    //获取该分组及其子分组的groupId
    getChildrenNodes(rootNode){
      this.dataForm.group.push(rootNode.id)
      var childNode=rootNode.children;
      if (childNode){
        for (var i=0; i<childNode.length; i++) {
          this.getChildrenNodes(childNode[i])
        }
      }
    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
    // 最大化最小化
    handleMinimize (isMinimize) {
      this.isMinimize = !isMinimize
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    },
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight - 155 }
    }
  },
  watch: {
    'totalNum': function (newVal, OldVal) {
      this.getGroupList();
    },
    'tableHeight': function(newVal, oldVal) {
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 130
      } else {
        this.tableHeightData = this.tableHeight
      }
    }
  },
  components: {
    deviceVolume,
    DeviceAlarmInfo,
    DeviceCallPoliceAddress,
    DeviceAlarmCallVolume,
    DeviceAlarmNetworkInfo,
    DeviceRecordingFiles,
    // deviceDemo,
    deviceClearBroadcastTask,
    deviceSipAddress,
    // deviceAlarmAddress,
    // deviceAlarmVolume,
    deviceRegisterSipUser,
    deviceInfo
  },
}
</script>

<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 180px;
  background:#eee;
  overflow: hidden;
  overflow-y: auto;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 100px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list {
  padding-top: 10px;
  width: 105px;
  height: 105px;
  margin-left: 8%;
}
.opera_ul li:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}
.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}
.opera_ul1 {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul1 li{
  text-align: center;
  float: left;
  list-style: none;
  width: 40px;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list1 {
  padding-top: 5px;
  width: 40px;
  height: 40px;
}
.opera_ul1 li:hover {
  background-color: rgb(210, 174, 245);
  cursor: pointer;
}
.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}
.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
</style>
