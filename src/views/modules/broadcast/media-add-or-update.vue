<template>
  <Modal v-model="visible" width="500">
    <p slot="header" style="text-align:center">
        <span>{{$t('common.update')}}</span>
    </p>
    <Form :model="uploadForm" style="height: 310px" :label-width="80" label-position="left">
      <Alert type="success" show-icon>{{$t('screen.fileTips1')}}</Alert>
      <Loading :loadBoolValue="load"></Loading>
      <FormItem class="upload" :label="$t('screen.uploadFile')">
        <Upload
          multiple
          :before-upload="handleUpload"
          :action="''"
          type="drag"
          accept="audio/*">
          <!-- accept="video/mp4,video/3gp, audio/*, image/gif, image/jpeg, image/png, application/x-shockwave-flash" -->
          <!-- <Button icon="ios-cloud-upload-outline">{{$t('file.attachment')}}</Button> -->
          <div style="padding: 20px 0">
            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
            <p>{{$t('file.attachment1')}}</p>
          </div>
        </Upload>
        <div>
          <ul class="file-list" v-for="(list,index) in uploadForm.dispalyFile" :key="index">
              <li>{{$t('file.name')}}: <span style="font-size:15px;">{{ list.name }}</span> 
              <Icon type="ios-close" size="20" style="float:right;" @click="uploadForm.dispalyFile.splice(index,1)"></Icon></li>
          </ul>
        </div>
      </FormItem>
    </Form>
    <div slot="footer">
        <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
        <Button type="primary" @click="uploadFormSubmit">{{$t('file.upload')}}</Button>
    </div>
  </Modal>
</template>

<script>
import Loading from '@/utils/loading'
import axios from 'axios'
export default {
  data () {
    return {
      visible: false,
      load: false,
      token: this.$cookie.get('token'),
      uploadForm: {
        dispalyFile: [] // 临时数组，同时用于显示在页面
      }
    }
  },
  methods: {
    // 初始化
    init () {
      this.visible = true
    },
    // 文件上传前
    handleUpload (selectFile) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      if(ele === "audio"){
          if(ele === "audio" && selectFile.size > (20 * 1024 *1024)){
              this.$Message.error(this.$t('file.audio') +'：'+selectFile.name +this.$t('screen.sizeMore')+ '20M!')
              this.selectFile = null //超过大小将文件清空
              return false
          } 
      } else {
        this.$Message.error(this.$t('screen.picturesOrVideos'))
              this.selectFile = null //将文件清空
              return false
      }
      if (this.uploadForm.dispalyFile.length >= 5) {
        this.$Message.error(this.$t('file.YouCanOnlyUploadUpTo5Files'))
        return false
      }
      // 临时数组，同时用于显示在页面
      // this.uploadForm.dispalyFile = selectFile
      // this.uploadFormSubmit()
      this.uploadForm.dispalyFile.push(selectFile)
      return false
    },
    // 新增文件上传
    uploadFormSubmit () {
      if (this.uploadForm.dispalyFile.length > 0) {
        this.load = true
        let formData = new FormData()
        // file为后台接收参数
        // formData.append('file', this.uploadForm.dispalyFile)
        //多个文件上传
        for(var i=0; i< this.uploadForm.dispalyFile.length; i++){  
          formData.append("file",this.uploadForm.dispalyFile[i]);   // 文件对象    
        } 
        axios.request({
          url: this.$http.adornUrl('/broadcast/media/upload'),
          method: 'post',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data', 'token': this.token }
        }).then(res => {
          // 上传成功处理
          if (res.data) {
            if (res.data.code === 0) {
              this.load = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.load = false
              this.$Message.error({
                content: res.data.msg,
                duration: 0.5,
                onClose: () => {
                  this.load = false
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }
          
          }
        })
      }
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.uploadForm = {
          name: '',
          resourceType: 0,
          size: '',
          dispalyFile: []
        }
      }
    }
  },
  components: { 
    Loading 
  }

}
</script>
<style scoped>
</style>
