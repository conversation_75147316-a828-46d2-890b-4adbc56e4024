<template>
  <Modal v-model="visible" :title="$t('operation.AlarmAddress')" width="80" style="text-align:center">
    <p slot="header" style="text-align:center">
      <span>{{  $t('operation.AlarmAddress')  }}</span>
    </p>

    <div style="text-align:center">

      <div style="text-align:left;margin-bottom: 15px;">
        <Button type="primary" :disabled="dataListSelections.length <= 0" style="margin-right: 5px;"
          @click="setCallPoliceAddressList()">{{  $t('alarm.batchSettings')  }}
        </Button>
        <Alert show-icon style="text-align: left"><b class="tip">{{ $t('alarm.alarmAddressTip') }}</b></Alert>
      </div>
      <Table border :loading="dataListLoading" :columns="netWorkInfoColumns" :data="netWorkInfoList"
        @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow" ref="selection">
        <template slot-scope="{ row }" slot="name">
          <strong>{{  row.name  }}</strong>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <Button type="primary" size="small" style="margin-right: 5px;margin-top: 5px"
            @click="setAlarmAddress(row, index)">
            {{  $t('operation.SetAlarmAddress')  }}
          </Button>
        </template>
      </Table>
    </div>
    <div slot="footer">
    </div>
    <Modal v-model="setAlarmAddressVisible" :title="$t('operation.SetAlarmAddress')" @on-ok="uploadAlarmAddress"
      @on-cancel="cancelUploadAlarmAddress">
      <Alert show-icon style="text-align: left"><b class="tip">{{ $t('alarm.alarmAddressSetTip') }}</b></Alert>
      <Form :model="setAlarmAddressDefault" :label-width="80">
        <FormItem :label="$t('common.state')">
          <RadioGroup v-model="setAlarmAddressDefault.state" @on-change="changeStatus()">
            <Radio label="0">{{ $t('alarm.defaultState') }}</Radio>
            <Radio label="1">{{ $t('alarm.custom') }}</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
      <!--      <Form :model="setAlarmAddressformItem" v-if="isDefault" :rules="ruleInline" :label-width="100"  ref="setAlarmAddressformItem" >
        <FormItem :label="$t('operation.AlarmAddress')" prop="OutgoingCallUrl">
          <Input v-model="setAlarmAddressformItem.OutgoingCallUrl"></Input>
        </FormItem>
        <FormItem :label="$t('alarm.calledPhoneNumber')" prop="OutPhoneNumber">
          <Input v-model="setAlarmAddressformItem.OutPhoneNumber" type="text"></Input>
        </FormItem>
      </Form>-->
      <Form :model="setAlarmAddressformItem" :rules="ruleInline" :label-width="100" ref="setAlarmAddressformItem">
        <FormItem :label="$t('operation.AlarmAddress')" v-if="!isDefault" prop="OutgoingCallUrl">
          <Input v-model="setAlarmAddressformItem.OutgoingCallUrl"></Input>
        </FormItem>
        <!--        <FormItem :label="$t('operation.AlarmAddress')" prop="OutgoingCallUrl" >-->
        <!--          <Input v-model="setAlarmAddressformItem.OutgoingCallUrl" type="text"></Input>-->
        <!--        </FormItem>-->
        <FormItem :label="$t('alarm.calledPhoneNumber')" prop="OutPhoneNumber">
          <Input v-model="setAlarmAddressformItem.OutPhoneNumber" type="text"></Input>
        </FormItem>

        <FormItem :label="$t('alarm.backupCalledPhoneNumber')">
          <Input v-model="setAlarmAddressformItem.BackupPhoneNumber" type="text"></Input>
        </FormItem>
      </Form>
    </Modal>
  </Modal>
</template>

<script>
export default {
  name: "device-call-police-address",
  data() {
    return {
      visible: false,
      ids: [],
      setAlarmAddressDefault: {
        /**
         * 为0时，是默认地址，为1时，自定义地址。
         */
        state: "0"
      },
      /**
       * 报警地址是否为默认地址
       */
      isDefault: true,

      dataListLoading: false,
      /**
       *多选
       */
      dataListSelections: [],
      listIndex: "",

      /**
       * 表格数据
       */
      netWorkInfoList: [],
      /**
       * 表头
       */
      netWorkInfoColumns: [
        { type: 'selection', width: 60, fixed: 'left', align: 'center' },
        {
          title: 'MAC',
          key: 'mac',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', "MAC")
          }
        },
        {
          title: 'IP',
          key: 'ip',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', "IP")
          }
        },
        {
          title: this.$t('operation.thealias'),
          key: 'DisplayName',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('operation.thealias'))
          }
        },
        {
          title: this.$t('operation.AlarmAddress'),
          key: 'OutgoingCallUrl',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('operation.AlarmAddress'))
          }
        },
        {
          title: this.$t('alarm.calledPhoneNumber'),
          key: 'OutPhoneNumber',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.calledPhoneNumber'))
          }
        },
        {
          title: this.$t('alarm.backupCalledPhoneNumber'),
          key: 'BackupPhoneNumber',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.backupCalledPhoneNumber'))
          }
        },
        {
          title: this.$t('alarm.myPhoneNumber'),
          key: 'MePhoneNumber',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.myPhoneNumber'))
          }
        },
        {
          title: this.$t('common.operation'),
          slot: 'action',
          width: 150,
          align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ],

      /**
       * 设置报警地址弹窗状态
       */
      setAlarmAddressVisible: false,
      /**
       * 设置报警地址弹窗表单信息
       */
      setAlarmAddressformItem: {
        OutgoingCallUrl: '',
        MePhoneNumber: '',
        OutPhoneNumber: '',
        BackupPhoneNumber: '',
        mac: '',
        ip: '',
        address: '',
        deviceId: ''
      },
      /**
       * 多选时的数组
       */
      setAlarmAddressformItemList: [],
      userInfo:null,

    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids;
        this.getAlarmAddress();
        this.setAlarmAddressformItem = {}
        this.dataListSelections = []
        this.setAlarmAddressDefault.state = "0"
        this.isDefault = true
      }
    },

    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },

    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },

    /**
     * 切换状态
     */
    changeStatus() {
      if (this.setAlarmAddressDefault.state == '0') {

        this.isDefault = true;
      } else {
        this.isDefault = false;
      }

    },

    /**
     * 获取表格数据
     */
    getAlarmAddress() {
      this.dataListLoading = true
      this.ids.forEach(element => {
        this.$http({
          url: this.$http.adornUrl('/broadcast/get/callPoliceService'),
          method: 'get',
          params: this.$http.adornParams({
            deviceId: element
          })
        }).then(({ data }) => {
          this.dataListLoading = false;
          this.netWorkInfoList = [];
          if (data.code == 0 && data.data) {
            data.data.forEach(item => {
              var numberArr = item.sipInfo.OutPhoneNumber.replace(/(\s*$)/g, "").split(",");
              this.netWorkInfoList.push({
                mac: item.mac,
                ip: item.ip,
                address: item.address,
                deviceId: element,
                OutgoingCallUrl: item.sipInfo.OutgoingCallUrl.replace(/(\s*$)/g, ""),
                DisplayName: item.sipInfo.DisplayName.replace(/(\s*$)/g, ""),
                MePhoneNumber: item.sipInfo.MePhoneNumber.replace(/(\s*$)/g, ""),
                OutPhoneNumber: numberArr[0],
                BackupPhoneNumber: numberArr.length==2 ? numberArr[1] : '',
              })
            })
          } else {
            this.$Message.error(data.msg.msg);
          }
        })
      });
    },
    /**
     * 设置报警地址
     */
    setAlarmAddress(item, index) {
      this.setAlarmAddressVisible = true;
      this.setAlarmAddressformItem.OutgoingCallUrl = item.OutgoingCallUrl;
      this.setAlarmAddressformItem.address = item.address;
      this.setAlarmAddressformItem.deviceId = item.deviceId;
      this.setAlarmAddressformItem.MePhoneNumber = item.MePhoneNumber;
      this.setAlarmAddressformItem.OutPhoneNumber = item.OutPhoneNumber;
      this.setAlarmAddressformItem.BackupPhoneNumber = item.BackupPhoneNumber;
      this.listIndex = index;
      this.isDefault = true
      this.setAlarmAddressDefault.state = "0"
    }
    ,

    /**
     * 选择批量修改时
     */
    setCallPoliceAddressList() {
      this.setAlarmAddressVisible = true;
      this.setAlarmAddressDefault.state = "0"
      this.isDefault = true

    },

    cancelUploadAlarmAddress() {
      this.setAlarmAddressformItem = {}
    },

    /**
     * 设置报警地址弹窗确定按钮
     */
    uploadAlarmAddress() {
      this.userInfo=this.$store.state.user.userInfo
      this.setAlarmAddressformItemList = []
      //如果是批量操作
      if (this.dataListSelections.length > 0) {
        this.dataListSelections.forEach(data => {
          //如果是选择默认地址，则给出我们平台地址为报警地址
          if (this.isDefault) {
            // data.OutgoingCallUrl = "http://*************:8080/call/Police?json*" + data.MePhoneNumber + "-" + this.setAlarmAddressformItem.OutPhoneNumber
            data.OutgoingCallUrl = this.$http.adornUrl('/call/Police?json*' + data.MePhoneNumber + "-" +
              this.setAlarmAddressformItem.OutPhoneNumber+"-"+this.userInfo.username+"_"+this.userInfo.userId);
          }else {
            data.OutgoingCallUrl = this.setAlarmAddressformItem.OutgoingCallUrl;
          }
          if (this.setAlarmAddressformItem.BackupPhoneNumber==''){
            data.OutPhoneNumber = this.setAlarmAddressformItem.OutPhoneNumber;
          }else {
            data.OutPhoneNumber = this.setAlarmAddressformItem.OutPhoneNumber + "," + this.setAlarmAddressformItem.BackupPhoneNumber;
          }
          this.setAlarmAddressformItemList.push(data)
        })
      } else {
        if (this.isDefault) {
          // this.setAlarmAddressformItem.OutgoingCallUrl = "http://*************:8080/call/Police?json*" + this.setAlarmAddressformItem.MePhoneNumber + "-" + this.setAlarmAddressformItem.OutPhoneNumber
          this.setAlarmAddressformItem.OutgoingCallUrl=this.$http.adornUrl('/call/Police?json*'+ this.setAlarmAddressformItem.MePhoneNumber + "-" +
            this.setAlarmAddressformItem.OutPhoneNumber+"-"+this.userInfo.username+"_"+this.userInfo.userId);
        }
        this.setAlarmAddressformItem.OutPhoneNumber = this.setAlarmAddressformItem.OutPhoneNumber + "," + this.setAlarmAddressformItem.BackupPhoneNumber;
        this.setAlarmAddressformItemList.push(this.setAlarmAddressformItem)
      }
      this.$http({
        url: this.$http.adornUrl('/broadcast/set/callPoliceService'),
        method: 'post',
        data: this.setAlarmAddressformItemList
      }).then(({ data }) => {
        if (data.code == 0) {
          this.setAlarmAddressVisible = false;
          // this.netWorkInfoList[this.listIndex].OutgoingCallUrl = this.setAlarmAddressformItem.OutgoingCallUrl
          // this.netWorkInfoList[this.listIndex].PhoneNumber = this.setAlarmAddressformItem.PhoneNumber
          //数据回显
          for (let i = 0; i < this.netWorkInfoList.length; i++) {
            for (let j = 0; j < this.setAlarmAddressformItemList.length; j++) {
              if (this.netWorkInfoList[i].mac == this.setAlarmAddressformItemList[j].mac) {
                var temp=this.setAlarmAddressformItemList[j].OutPhoneNumber.split(",")
                this.netWorkInfoList[i].OutgoingCallUrl = this.setAlarmAddressformItemList[j].OutgoingCallUrl
                this.netWorkInfoList[i].OutPhoneNumber = temp[0]
                this.netWorkInfoList[i].BackupPhoneNumber = temp.length==2 ? temp[1] : ''
              }
            }
          }
        }
      })
    }
  },
  computed: {

    ruleInline: {
      get() {
        return {
          OutgoingCallUrl: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },

          ],
          OutPhoneNumber: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ]
        }
      }
    },
  }
}
</script>

<style scoped>
</style>
