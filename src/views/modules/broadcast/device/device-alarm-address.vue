<template>
    <Modal v-model="visible" width="900">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.alarmConfig')}}</span>
        </p>
        <Alert type="warning" show-icon ><b class="tip">{{$t('tips.alarmConfig')}} <br/></b></Alert>
        <Form style="height: 300px;" label-position="left" label-colon>
          <FormItem>
            <Input v-model="aips4Address" :disabled="!single" :placeholder="$t('common.PleaseInput') + $t('operation.AlarmAddress')" style="width: 250px" />
            <Checkbox v-model="single">{{$t('common.custom')}}</Checkbox>
            <Input v-model="cardAddress" :placeholder="$t('common.PleaseInput') + $t('operation.CallAccount')" style="width: 250px" />
            <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('operation.SetAlarmAddress')}}</Button>
            <Button style="margin-left:20px" :loading="get_loading"  type="warning" @click="getAlarmAddress()">{{$t('operation.GetAlarmAddress')}}</Button>
          </FormItem>
          <FormItem v-if="resultData.length > 0">
            <div v-for="(item, index) in resultData" :key="index">
              <FormItem label="ID">
              <div>{{item.deviceId}}</div>
              </FormItem>
              <FormItem :loading="true">
                <div v-if="item._type === undefined">
                  <div style="color: red;">{{item}}</div>
                </div>
                <div v-else-if="item._type !== 'success'">
                  <div style="color: red;">{{item.msg}}</div>
                </div>
                <div v-else>
                  <div v-if="item.aips4Address || item.cardAddress">
                    <span>{{$t('operation.AlarmAddress')}}： </span>{{item.aips4Address}}<br/>
                    <span>{{$t('operation.CallAccount')}}： </span>{{item.cardAddress}}<br/>
                  </div>
                  <div v-else-if="item.aips4Address === '' || item.cardAddress === ''">
                    <span>{{$t('operation.AlarmAddress')}} ： </span>{{item.aips4Address === '' ? $t('common.notSet') : item.aips4Address}}<br/>
                    <span>{{$t('operation.CallAccount')}}： </span>{{item.cardAddress === '' ?  $t('common.notSet') : item.cardAddress}}<br/>
                  </div>
                  <div v-else>{{$t('setTime.setupSuccess')}}</div>
                </div>
              </FormItem>
              <Divider/>
            </div>
          </FormItem>
        </Form>
        <div slot="footer" style="text-align: left;">
            <span>
                <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
        </div>
      </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      ids: [],
      modal_loading: false,
      resultData: [],
      aips4Address: '',
      cardAddress: '',
      get_loading: false,
      single: false
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
      }
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        if ((this.aips4Address === '' && this.single === true) || this.cardAddress === '') {
          this.$Message.warning(this.$t('validate.alarmAddress_not_empty'));
          return
        }
        this.modal_loading = true
        this.resultData = []
        var idsLength = this.ids.length
        this.ids.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/broadcast/set/setAlarmAddress'),
            method: 'post',
            params: this.$http.adornParams({'cardId':item, 'aips4Address': this.aips4Address, 'cardAddress': this.cardAddress})
          }).then(({data}) => {
            if (data) {
              this.resultData.push(data.msg)
            }
            idsLength--
            if(idsLength === 0) {
              this.modal_loading = false
            }
          })
        })
      }
    },
    getAlarmAddress () {
      if (this.ids.length > 0) {
        this.get_loading = true
        this.resultData = []
        var idsLength = this.ids.length
        this.ids.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/broadcast/get/getAlarmAddress'),
            method: 'post',
            params: this.$http.adornParams({'cardId':item})
          }).then(({data}) => {
            if (data) {
              this.resultData.push(data.msg)
            }
            idsLength--
            if(idsLength === 0) {
              this.get_loading = false
            }
          })
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.resultData = [],
        this.aips4Address = '',
        this.cardAddress = '',
        this.single = false
      }
    }
  }
}
</script>
