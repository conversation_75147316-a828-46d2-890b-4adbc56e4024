<template>
  <Modal v-model="visible" width="1000">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{ $t('operation.recordingFile') }}</span>
    </p>
    <Row>
      <Col span="24">
        <span>{{ $t('broadcast.selectDate') }}:</span>
        <Date-picker type="date" v-model="date" style="width: 200px"></Date-picker>
        <Button type="primary" @click="queryRecording()">{{ $t('common.query') }}</Button>
      </Col>
      <Table :columns="columns" :data="fileList"
             style=" overflow-y: auto;  width: 100%;table-layout: fixed;margin-top: 10px" height="300">
        <template slot-scope="{ row, index }" slot="operation">
          <!-- :href="downloadUrl + row.id" -->
          <Button type="primary" size="small" style="font-size: 11px" @click="downloadRecording(row.cardId,row.name)">
            {{ $t('file.download') }}
          </Button>

        </template>
      </Table>
    </Row>
    <div slot="footer" style="text-align: left;">
    </div>
  </Modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        user: '',
        pwd: ''
      },
      modal_loading: false,
      resultData: [],
      ids: [],
      date: new Date(),
      fileList: [],
      columns: [
        {
          title: this.$t('cardDevice.deviceName'),
          key: 'cardId',
          renderHeader: (h) => {
            return h('div',this.$t('cardDevice.deviceName'))
          }
        },
        {
          title: this.$t('file.name'),
          key: 'name',
          renderHeader: (h) => {
            return h('div', this.$t('file.name'))
          }
        },

        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ]
    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.fileList = []
        this.ids = ids
        this.date = new Date()
      }
    },
    queryRecording() {
      // console.log(this.date)
      var year = this.date.getFullYear();
      var month = this.date.getMonth() + 1;
      var day = this.date.getDate();
      if (month < 10) {
        month = '0' + month
      }
      var formatDate = year + '-' + month + '-' + day
      // var idsLength = ['y70-421-00035']
      // this.ids.push('y70-421-00035')
      this.modal_loading = true
      // this.ids.forEach(item => {
      this.$http({
        url: this.$http.adornUrl('/sys/callRecord/recordingList'),
        method: 'post',
        data: this.$http.adornData({
          'cardIds': this.ids,
          "time": formatDate

        })
      }).then(({data}) => {
        if (data) {
          this.fileList = data.data
        }
        this.modal_loading = false
      })
      // })
    },

    downloadRecording(cardId, name) {
      var year = this.date.getFullYear();
      var month = this.date.getMonth() + 1;
      var day = this.date.getDate();
      if (month < 10) {
        month = '0' + month
      }
      var formatDate = year + '-' + month + '-' + day
      // var idsLength = this.ids.length
      console.log(name)
      window.open(this.$http.adornUrl(`/sys/callRecord/download?token=${this.$cookie.get('token')}&cardId=${cardId}&fileName=${name}&time=${formatDate}`))
    }
  },

}
</script>

<style scoped>

</style>
