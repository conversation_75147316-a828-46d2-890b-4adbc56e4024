<template>
  <Modal v-model="visible"  width="600">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{$t('operation.Register_SIP_account')}}</span>
    </p>
    <div style="height: 300px;overflow: hidden;">
      <Alert type="warning" show-icon ><b class="tip">{{$t('tips.SIPTips')}}</b></Alert>
      <Form  :label-width="100">
        <FormItem :label="$t('alarm.accountType')">
          <RadioGroup v-model="accountType" @on-change="changeType()">
            <Radio label="0">{{$t('alarm.registerAlarmAccount')}}</Radio>
            <Radio label="1">{{$t('alarm.registerPhoneNumber')}}</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
      <Form style="height: 150px;" :rules="alarmAccountRule"  ref="dataForm" :model="dataForm" v-if="deviceAccountVisible">
        <FormItem prop="user">
            <Input type="text" v-model="dataForm.user" :placeholder="$t('alarm.account')">
<!--              <Icon type="ios-person-outline" slot="prepend"></Icon>-->
              <span slot="prepend">B</span>
            </Input>
        </FormItem>
        <FormItem prop="pwd">
            <Input type="text" v-model="dataForm.pwd" :placeholder="$t('login.password')">
                <Icon type="ios-lock-outline" slot="prepend"></Icon>
            </Input>
        </FormItem>
        <FormItem style="float: right">
          <Button type="primary" :loading="modal_loading" @click="dataFormSubmit()"> {{ !dataForm.id ? $t('register.register') : $t('common.update') }} </Button>
        </FormItem>
      </Form>
      <Form style="height: 150px;" :rules="accountRule"  ref="dataForm" :model="dataForm" v-else>
        <FormItem prop="user">
          <Input type="text" v-model="dataForm.user" :placeholder="$t('alarm.account')">
<!--            <Icon type="ios-person-outline" slot="prepend"></Icon>-->
            <span slot="prepend">xixun</span>
          </Input>
        </FormItem>
        <FormItem prop="pwd">
          <Input type="text" v-model="dataForm.pwd" :placeholder="$t('login.password')">
            <Icon type="ios-lock-outline" slot="prepend"></Icon>
          </Input>
        </FormItem>
        <FormItem style="float: right">
          <Button type="primary" :loading="modal_loading" @click="dataFormSubmit()"> {{ !dataForm.id ? $t('register.register') : $t('common.update') }} </Button>
        </FormItem>
      </Form>
    </div>
    <div slot="footer" style="text-align: left;">
    </div>
  </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: 0,
        user: '',
        pwd: ''
      },
      modal_loading: false,
      resultData: [],
      /**
       * 账号类型 0为报警设备账号 1为话机账号
       */
      accountType:"0",

      deviceAccountVisible:true
    }
  },
  methods: {
    // 初始化
    init () {
      this.visible = true
      this.dataForm.user = ""
      this.dataForm.pwd = ""
      this.dataForm.id = ""
      this.accountType="0"
      // this.querySip()
      // this.changeType()
      this.$refs['dataForm'].resetFields();

    },
    // 提交数据
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.modal_loading = true
            // debugger
            if (this.accountType=='0'){
              this.dataForm.user='B'+this.dataForm.user
            }else {
              this.dataForm.user='xixun'+this.dataForm.user
            }
            // debugger
            this.$http({

              url: this.$http.adornUrl(`/broadcast/set/registerSIPUser`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.modal_loading = false
                this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                    this.visible = false
                  }
                })
              } else {
                this.$Message.error(data.msg)
                setTimeout(() => {
                  this.modal_loading = false
                }, 500)
              }
            })
          }
      })
    },
    querySip(){
      this.$http({
        url: this.$http.adornUrl(`/broadcast/get/getSIPUser`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm.user = data.info.sipName
          this.dataForm.pwd = data.info.pwd
          this.dataForm.id = data.info.id
        }
      })
    },
    changeType(){
      if (this.accountType==0){
        this.deviceAccountVisible=true
      }else {
        this.deviceAccountVisible=false
      }
      // this.$refs['dataForm'].resetFields();
    }

  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.resultData = []
      }
    }
  },
  computed: {
    alarmAccountRule: {
      get () {
        return {
          user: [
              { required: true, message: this.$t('alarm.accountRule'), trigger: 'blur',pattern:'^[a-zA-Z0-9]{11}$'},


          ],
          pwd: [
              { required: true, message: this.$t('validate.password_cannot_empty') , trigger: 'blur' },
          ]
        }
      }
    },
    accountRule: {
      get () {
        return {
          user: [
            { required: true, message: this.$t('broadcast.SIP_account') + this.$t('validate.not_empty'), trigger: 'blur' },

          ],
          pwd: [
            { required: true, message: this.$t('validate.password_cannot_empty'), trigger: 'blur' },
          ]
        }
      }
    },
  }
}
</script>
