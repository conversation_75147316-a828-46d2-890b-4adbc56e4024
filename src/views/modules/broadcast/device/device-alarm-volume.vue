<template>
  <Modal v-model="visible"  width="900">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{$t('operation.AlarmVolume')}}</span>
    </p>
    <!-- <Tabs :value="tabVal" @on-click="handlerTab">
      <TabPane :label="$t('operation.volumeControl')" name="tab1"> -->
    <div style="height: 400px;overflow: hidden;">
      <Alert type="warning" show-icon ><b class="tip">{{$t('tips.alarmVolume')}}</b></Alert>
      <Divider />
      <Form style="height: 150px;" :label-width="50" label-position="left" label-colon>
        <FormItem  :label="$t('cardDevice.volume')">
          <InputNumber :max="9" :min="1" v-model="volume"></InputNumber>
          <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
        </FormItem>
        <FormItem v-if="resultData.length > 0" style="height: 298px;overflow-y: auto">
          <div v-for="(item, index) in resultData" :key="index">
            <FormItem label="ID">
              <div>{{item.deviceId}}</div>
            </FormItem>
            <FormItem :loading="true">
              <div v-if="item._type === undefined">
                <div style="color: red;">{{item}}</div>
              </div>
              <div v-else-if="item._type !== 'success'">
                <div style="color: red;">{{item.msg}}</div>
              </div>
              <div v-else>
                <div style="color:green;">{{$t('setTime.setupSuccess')}}</div>
              </div>
            </FormItem>
            <Divider/>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer" style="text-align: left;">
            <span>
                <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      ids: [],
      // tabVal: '',
      volume: 5,
      dataForm: {},
      modal_loading: false,
      resultData: []
    }
  },
  methods: {
    // 初始化
    init (ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
      }
    },
    // handlerTab (name) {
    //   this.tabVal = name
    // },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.resultData = []
        this.modal_loading = true
        var idsLength = this.ids.length
        this.ids.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/broadcast/set/setAlarmVolume'),
            method: 'post',
            data: this.$http.adornData({'id': item, 'volume': this.volume},false)
          }).then(({data}) => {
            if (data) {
              this.resultData.push(data.msg)
            }
            idsLength--
            if (idsLength === 0) {
              this.modal_loading = false
            }
          })
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        // this.tabVal = 'tab1'
        this.resultData = []
        this.volume = 5
        this.$emit('refreshDataList')
      }
    }
  }
}
</script>
