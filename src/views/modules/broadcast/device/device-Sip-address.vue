<template>
    <Modal v-model="visible" width="1000">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.callAddress')}}</span>
        </p>
        <Alert type="warning" show-icon ><b class="tip">{{$t('tips.SIPAddress')}} <br/> {{$t('tips.SIPAddress1')}}</b></Alert>
        <Form style="height: 300px;" label-position="left" label-colon>
          <FormItem>
            <Input v-model="address" :placeholder="$t('common.PleaseInput') + $t('operation.SIPServerAddress')" style="width: 300px" />
            <span>{{$t('broadcast.multicastAddress')}}</span>
            <Input-number :value="224" :disabled="true"></Input-number>
            <Input-number :value="0"  :disabled="true"></Input-number>
            <Input-number :value="1"  :disabled="true"></Input-number>
            <Input-number :max="255" :min="3" v-model="multicastAddress"></Input-number>
            <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
            <Button style="margin-left:10px" :loading="get_loading"  type="warning" @click="getSIPServerAddress()">{{$t('common.query')}}</Button>
          </FormItem>
          <div v-if="resultData.length > 0">
            <div style="height: 255px;overflow-y: auto;">
              <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
          </div>
          <div v-if="queryData.length > 0">
            <div style="height: 255px;overflow-y: auto;">
              <cardResult :ids="ids" :resultData="queryData" :cardItemWidth="900 / 2 - 50" :isQuery="true" :tableHeight="165" :resultHeight="135"
                    :resultItem="[{text: 'operation.AlarmEquipmentMacAddress', name: 'radioMAC', suffix: ':'},
                    {text: 'broadcast.multicastAddress', name: 'multicastAddress', suffix: ':'},
                    {text: 'operation.SIPServerAddress', name: 'sipAddress', suffix: ':'},
                    {text: 'macIp', name: 'macIp', suffix: ':'},
                    {text: 'cardMAC', name: 'cardMAC', suffix: ':'},
                    {text: 'sipRegister', name: 'sipRegister', suffix: ':'}]"></cardResult>
            </div>
          </div>
            </div>
          </FormItem>
        </Form>
        <div slot="footer" style="text-align: left;">
            <span>
                <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
        </div>
      </div>
    </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data () {
    return {
      visible: false,
      ids: [],
      modal_loading: false,
      resultData: [],
      queryData: [],
      address: '',
      get_loading: false,
      //组播地址
      multicastAddress:null,
      // setMulticastIpLoading:false,
      // getMulticastIpLoading:false,
      inputNumDisable:true
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
      }
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.$Modal.confirm({
          title: this.$t('common.tips'),
          content: this.address != '' ?
          this.$t('broadcast.customSIPAddressStart') + this.address + this.$t('broadcast.customSIPAddressEnd') : this.$t('broadcast.SIPAddress'),
          okText: this.$t('common.confirm'),
          cancelText: this.$t('common.cancel'),
          onOk: () => {
            this.modal_loading = true
            this.queryData = []
            this.resultData = []
            var multiIp=""
            if (this.multicastAddress===""||this.multicastAddress===null){
              multiIp=""
            }else{
              multiIp="224.0.1."+this.multicastAddress;
            }
             var param= {'cardIds':this.ids,'multicastAddress':multiIp, 'address': this.address}
              this.$http({
                url: this.$http.adornUrl('/broadcast/set/setSipAddress'),
                method: 'post',
                data: param
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.resultData=data.data
                }else {
                  this.$Message.error(data.msg)
                }
                this.modal_loading = false
              })
          }
        })
      }
    },
    getSIPServerAddress () {
      if (this.ids.length > 0) {
       var ids = this.ids.join(',')
        this.get_loading = true
        this.queryData = []
        this.resultData = []
          this.$http({
            url: this.$http.adornUrl('/broadcast/get/getSipAddress'),
            method: 'get',
            params: this.$http.adornParams({'cardIds':ids})
          }).then(({data}) => {
            if (data && data.code == 0) {
              this.queryData=data.data
            }else {
              this.$Message.error(data.msg)
            }
            this.get_loading = false
          })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.resultData = []
        this.queryData = []
        this.address = ''
        this.get_loading=false
        this.modal_loading=false
      }
    }
  }
}
</script>
