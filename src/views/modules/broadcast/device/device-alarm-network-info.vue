<template>
  <div>
    <Modal v-model="visible" :title="$t('alarm.alarmDeviceNetwork')" width="80" style="text-align:center">
      <p slot="header" style="text-align:center">
        <span>{{ $t('alarm.alarmDeviceNetwork') }}</span>
      </p>
      <div style="text-align:center">
        <Alert show-icon style="text-align: left"><b class="tip">{{ $t('alarm.alarmNetworkTip') }}</b></Alert>
        <Table border :loading="dataListLoading" :columns="netWorkInfoColumns" :data="netWorkInfoList">
          <template slot-scope="{ row }" slot="name">
            <strong>{{ row.name }}</strong>
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Button type="primary" size="small" style="margin-right: 5px;margin-top: 5px"
              @click="setNetworkStatus(row, index)">
              {{ $t('alarm.setNetwork') }}
            </Button>
          </template>
          <template slot-scope="{ row, index }" slot="dhcp">
            <span v-if="row.DHCP == '0'">{{ $t('alarm.static') }}</span>
            <span v-if="row.DHCP == '1'">{{ $t('alarm.dynamic') }}</span>
          </template>
        </Table>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <Modal v-model="setNetworkStatusVisible" :title="$t('alarm.setNetwork')" @on-ok="uploadSipNetWork">
      <Form :model="setNetworkStatusformItem" :label-width="80">
        <FormItem label="DHCP">
          <RadioGroup v-model="setNetworkStatusformItem.DHCP" @on-change="changeStatus()">
            <Radio label="0">{{ $t('alarm.static') }}</Radio>
            <Radio label="1">{{ $t('alarm.dynamic') }}</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
      <Form v-if="netWorkStatus" :label-width="80" :rules="ruleInline" :model="setNetworkStatusformItem">
        <FormItem label="IP" prop="IP">
          <Input v-model="setNetworkStatusformItem.IP" type="text"></Input>
        </FormItem>
        <FormItem :label="$t('alarm.gateway')" prop="Gateway">
          <Input v-model="setNetworkStatusformItem.Gateway" type="text"></Input>
        </FormItem>
        <FormItem label="DNS" prop="DNS">
          <Input v-model="setNetworkStatusformItem.DNS" type="text"></Input>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      ids: [],
      dataListLoading: false,

      listIndex: "",

      /**
       * 表格数据
       */
      netWorkInfoList: [],
      /**
       * 表头
       */
      netWorkInfoColumns: [
        {
          title: 'MAC',
          key: 'mac',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', "MAC")
          }
        },
        {
          title: this.$t('operation.thealias'),
          key: 'DisplayName',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('operation.thealias'))
          }
        },
        {
          title: 'IP',
          key: 'ip',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.dynamic') + 'IP')
          }
        },

        {
          title: 'IP',
          key: 'IP',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.static') + 'IP')
          }
        },
        {
          title: 'DNS',
          key: 'DNS',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', 'DNS')
          }
        },

        {
          title: this.$t('alarm.gateway'),
          key: 'Gateway',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.gateway'))
          }
        },

        {
          title: this.$t('alarm.subnetMask'),
          key: 'SubnetMask',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.subnetMask'))
          }
        },
        {
          title: 'DHCP',
          key: 'DHCP',
          align: 'center',
          slot: 'dhcp',
          renderHeader: (h) => {
            return h('div', 'DHCP')
          }
        },

        {
          title: this.$t('common.operation'),
          slot: 'action',
          width: 150,
          align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ],
      /**
       * 设置网络状态弹窗状态
       */
      setNetworkStatusVisible: false,
      /**
       * 设置网络状态表单信息
       */
      setNetworkStatusformItem: {
        mac: '',
        ip: '',
        DHCP: '',
        IP: '',
        SubnetMask: '',
        Gateway: '',
        DNS: '',
        address: ''
      },
      /**
       * 设置网络状态
       */
      netWorkStatus: false,
    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids;
        this.getSipNetworkStatus();
      }
    },

    /**
     * 获取报警设备网络状态
     */
    getSipNetworkStatus() {
      this.dataListLoading = true
      this.ids.forEach(element => {
        this.$http({
          url: this.$http.adornUrl('/broadcast/get/sipNetworkStatus'),
          method: 'get',
          params: this.$http.adornParams({
            deviceId: element
          })
        }).then(({ data }) => {
          this.dataListLoading = false;
          this.netWorkInfoList = [];
          if (data.code == 0 && data.data) {
            data.data.forEach(item => {
              this.netWorkInfoList.push({
                mac: item.mac,
                ip: item.ip,
                address: item.address,
                deviceId: element,
                DHCP: item.sipInfo.DHCP.replace(/(\s*$)/g, ""),
                DNS: item.sipInfo.DNS.replace(/(\s*$)/g, ""),
                Gateway: item.sipInfo.Gateway.replace(/(\s*$)/g, ""),
                IP: item.sipInfo.IP.replace(/(\s*$)/g, ""),
                SubnetMask: item.sipInfo.SubnetMask.replace(/(\s*$)/g, ""),
                DisplayName: item.sipInfo.DisplayName.replace(/(\s*$)/g, ""),

              })
            })
          } else {
            this.$Message.error(data.msg.msg);
          }
        })
      });
    },
    /**
     * 设置网络状态
     */
    uploadSipNetWork() {
      this.$http({
        url: this.$http.adornUrl('/broadcast/set/sipNetworkStatus'),
        method: 'post',
        data: this.$http.adornData(this.setNetworkStatusformItem)
      }).then(({ data }) => {
        if (data.code == 0) {
          this.setSipInfoVisible = false;
          this.netWorkInfoList[this.listIndex].DHCP = this.setNetworkStatusformItem.DHCP
          this.netWorkInfoList[this.listIndex].IP = this.setNetworkStatusformItem.IP
          this.netWorkInfoList[this.listIndex].Gateway = this.setNetworkStatusformItem.Gateway
          this.netWorkInfoList[this.listIndex].DNS = this.setNetworkStatusformItem.DNS
          // this.getTableList();
        }
      })
    },

    /**
     * 打开设置网络状态弹窗
     */
    setNetworkStatus(item, index) {
      this.setNetworkStatusformItem = {
        deviceId: item.deviceId,
        address: item.address,
        DHCP: item.DHCP.replace(/\s+/g, ""),
        IP: item.IP,
        SubnetMask: item.SubnetMask,
        Gateway: item.Gateway,
        DNS: item.DNS,
      }
      this.setNetworkStatusVisible = true;
      this.listIndex = index;
      if (this.setNetworkStatusformItem.DHCP == 0) {
        this.netWorkStatus = true;
      } else {
        this.netWorkStatus = false;
      }
    }
    ,

    /**
     * 切换网络状态
     */
    changeStatus() {
      if (this.setNetworkStatusformItem.DHCP == 0) {
        this.netWorkStatus = true;

      } else {
        this.netWorkStatus = false;
      }

    }
  },
  computed: {

    ruleInline: {
      get() {
        return {
          IP: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },

          ],
          Gateway: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ],
          DNS: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ]
        }
      }
    },
  }
}
</script>

<style scoped>
</style>
