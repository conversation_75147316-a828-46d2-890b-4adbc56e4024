<template>
    <Modal v-model="visible" width="900">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.clearBroadcastTask')}}</span>
        </p>
        <div style="height: 300px;float:left">
          <Form style="height: 300px;" label-position="left" label-colon>
            <FormItem>
              <Button style="margin-left:20px" :loading="pauseModal_loading"  type="primary" @click="stopIpColumn()">{{$t('broadcast.pauseOrOpenBroadcast')}}</Button>
              <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('operation.clearBroadcastTask')}}</Button>
              <Button style="margin-left:20px" :loading="specialModal_loading"  type="primary" @click="specialDataFormSubmit()">{{$t('task.clearInStream')}}</Button>
            </FormItem>
            <div v-if="resultData.length > 0">
              <div style="height: 255px;overflow-y: auto;">
                <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
              </div>
            </div>
          </Form>
        </div>
        <div slot="footer" style="text-align: left;">
            <span>
                <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
        </div>
      </div>
    </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data () {
    return {
      visible: false,
      ids: [],
      modal_loading: false,
      specialModal_loading: false,
      pauseModal_loading:false,
      resultData: []
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
      }
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.resultData = []
        this.$http({
          url: this.$http.adornUrl('/broadcast/set/clearIpColumnService'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData=data.data
          }else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false

        })
      }
    },
    // 仅清除插播任务
    specialDataFormSubmit () {
      if (this.ids.length > 0) {
        this.specialModal_loading = true
        this.resultData = []
          this.$http({
            url: this.$http.adornUrl('/broadcast/set/clearSpecialIpColumnService'),
            method: 'post',
            data: this.ids
          }).then(({data}) => {
            if (data && data.code == 0) {
              this.resultData=data.data
            }else {
              this.$Message.error(data.msg)
            }
            this.specialModal_loading = false
          })

      }
    },
    stopIpColumn(){
      if (this.ids.length > 0) {
        this.pauseModal_loading = true
        this.resultData = []
        var ids=this.ids.join(",")
          this.$http({
            url: this.$http.adornUrl('/broadcast/get/stopIpColumn'),
            method: 'get',
            params: this.$http.adornParams({'deviceIds':ids})
          }).then(({data}) => {
            if (data && data.code == 0) {
              this.resultData=data.data
            }else {
              this.$Message.error(data.msg)
            }
              this.pauseModal_loading = false

          })
      }
    },
    clearLoading(){
      this.modal_loading= false
      this.specialModal_loading= false
      this.pauseModal_loading=false
    },

  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.resultData = []
        this.clearLoading()
      }
    }
  }
}
</script>
