<template>
  <Modal v-model="visible" :title="$t('alarm.callingVolume')" width="80" style="text-align:center">
    <p slot="header" style="text-align:center">
      <span>{{ $t('alarm.callingVolume') }}</span>
    </p>
    <div style="text-align: center">
      <div style="text-align:left;margin-bottom: 15px;">
         <Button type="primary" :disabled="dataListSelections.length <= 0"
         style="margin-right: 5px;" @click="setCallingVolumeList()">{{ $t('alarm.batchSettings') }}</Button>
       </div>
      <Table border :loading="dataListLoading" :columns="netWorkInfoColumns" :data="netWorkInfoList"
             @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow" ref="selection">
        <template slot-scope="{ row }" slot="name">
          <strong>{{ row.name }}</strong>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <Button type="primary" size="small" style="margin-right: 5px;margin-top: 5px"
                  @click="settingCallVolume(row,index)">{{ $t('alarm.setCallingVolume') }}
          </Button>
        </template>
      </Table>
    </div>
    <div slot="footer">
    </div>
    <Modal v-model="setCallVolumeVisible" :title="$t('alarm.setCallingVolume')" @on-ok="uploadCallVolume" @on-cancel="cancelUploadCallVolume">
      <Form :model="setCallVolumeformItem" :label-width="80">
        <FormItem :label="$t('alarm.callingVolume')" prop="HandFreeVol">
          <Input-number :min="1" :max="9" v-model="setCallVolumeformItem.HandFreeVol" ></Input-number>
        </FormItem>
      </Form>
    </Modal>
  </Modal>

</template>

<script>
export default {

  data() {
    return {
      visible: false,
      dataListLoading: false,

      indexArr:[],
      /**
       *多选数据
       */
      dataListSelections: [],
      listIndex: "",

      /**
       * 表格数据
       */
      netWorkInfoList: [],
      /**
       * 表头
       */
      netWorkInfoColumns: [
        {type: 'selection', width: 60, fixed: 'left', align: 'center'},
        {
          title: 'MAC',
          key: 'mac',
          align: 'center',
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',"MAC")
          }
        },
        {
          title: this.$t('operation.thealias'),
          key: 'DisplayName',
          align: 'center',
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('operation.thealias'))
          }
        },
        {
          title: 'IP',
          key: 'ip',
          align: 'center',
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',"IP")
          }
        },
        {
          title: this.$t('alarm.callingVolume'),
          key: 'HandFreeVol',
          align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('alarm.callingVolume'))
          }
        },
        {
          title: this.$t('common.operation'),
          slot: 'action',
          width: 150,
          align: 'center',
          renderHeader:(h)=>{
            return h('div', this.$t('common.operation'))
          }
        }
      ],

      /**
       * 设置通话音量弹窗状态
       */
      setCallVolumeVisible: false,
      setCallVolumeformItemList: [],

      /**
       * 设置通话音量表单信息
       */
      setCallVolumeformItem: {
        HandFreeVol: 1,
        deviceId: '',
        address: '',
        mac: '',
        ip: ''
      },
      ids: [],


    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids;
        this.broadcastVolumeService();
        this.dataListSelections = []
        this.setCallVolumeformItem={}

        // this.setCallVolumeformItemList=[]
        this.indexArr=[]
      }
    },

    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection()
    },

    selectThisRow(data, index) {

      this.$refs.selection.toggleSelect(index);
    },
    /**
     * 获取通话音量
     */
    broadcastVolumeService() {
      this.dataListLoading = true
      this.ids.forEach(element => {
        this.$http({
          url: this.$http.adornUrl('/broadcast/get/broadcastVolumeService'),
          method: 'get',
          params: this.$http.adornParams({
            deviceId: element
          })
        }).then(({data}) => {
          this.dataListLoading = false;
          this.netWorkInfoList = [];
          if (data.code == 0 && data.data) {
            data.data.forEach(item => {
              this.netWorkInfoList.push({
                mac: item.mac,
                ip: item.ip,
                address: item.address,
                deviceId: element,
                HandFreeVol: Number(item.sipInfo.HandFreeVol),
                DisplayName: item.sipInfo.DisplayName.replace(/(\s*$)/g, ""),
              })
            })
          } else {
            this.$Message.error(data.msg.msg);
          }
        })
      });
    },

    /**
     * 设置通话音量
     */
    settingCallVolume(item, index) {
      this.setCallVolumeVisible = true;
      this.setCallVolumeformItem.HandFreeVol = item.HandFreeVol;
      this.setCallVolumeformItem.address = item.address;
      this.setCallVolumeformItem.deviceId = item.deviceId;
      this.listIndex = index;

    },
    /**
     * 多选时，批量设置
     */
    setCallingVolumeList() {
      this.setCallVolumeVisible = true
      this.setCallVolumeformItem.HandFreeVol=1
    },

    /**
     * 取消设置音量
     */
    cancelUploadCallVolume(){
      this.setCallVolumeformItem={}
    },

    /**
     * 设置通话音量确定按钮
     */
    uploadCallVolume() {
      this.setCallVolumeformItemList = []
      if (this.dataListSelections.length > 0) {

        this.dataListSelections.forEach(data => {

          data.HandFreeVol=this.setCallVolumeformItem.HandFreeVol
          this.setCallVolumeformItemList.push(data)
        })
      } else {
        this.setCallVolumeformItemList.push(this.setCallVolumeformItem)
      }
      this.$http({
        url: this.$http.adornUrl('/broadcast/set/broadcastVolumeService'),
        method: 'post',
        data: this.setCallVolumeformItemList
      }).then(({data}) => {
        if (data.code == 0) {
          // debugger
          this.setCallVolumeVisible = false;
          // this.netWorkInfoList[this.listIndex].HandFreeVol = this.setCallVolumeformItem.HandFreeVol
          for (let i = 0; i < this.netWorkInfoList.length; i++) {
            for (let j = 0; j < this.setCallVolumeformItemList.length; j++) {
              if (this.netWorkInfoList[i].mac == this.setCallVolumeformItemList[j].mac) {
                this.netWorkInfoList[i].HandFreeVol = this.setCallVolumeformItemList[j].HandFreeVol;
              }
            }
          }
        }
      })
    }
  },

}
</script>

<style scoped>

</style>
