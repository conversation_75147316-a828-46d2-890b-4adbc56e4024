<template>
  <Modal v-model="visible" :title="$t('alarm.alarmDeviceInfo')" width="80" style="text-align:center">
    <p slot="header" style="text-align:center">
      <span>{{ $t('alarm.alarmDeviceInfo') }}</span>
    </p>
    <div style="text-align:center">
      <div style="text-align:left;margin-bottom: 15px;">
        <Alert show-icon style="text-align: left"><b class="tip">{{ $t('alarm.alarmInfoTip') }}</b></Alert>
      </div>
      <Table border :loading="dataListLoading" :columns="netWorkInfoColumns" :data="netWorkInfoList">
        <template slot-scope="{ row }" slot="name">
          <strong>{{ row.name }}</strong>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <Button type="primary" size="small" style="margin-right: 5px;margin-top: 5px" @click="setSipInfo(row, index)">
            {{ $t('alarm.setAlarmInfo') }}
          </Button>
        </template>
        <template slot-scope="{ row, index }" slot="RegisterCode">
          <span v-if="row.RegisterCode == '0'">{{ $t('alarm.notEnable') }}</span>
          <span v-else-if="row.RegisterCode == '200'">{{ $t('register.register') }}</span>
          <span v-else>{{ $t('screen.unknownState') }}</span>
        </template>
      </Table>
    </div>
    <div slot="footer">
    </div>
    <Modal v-model="setSipInfoVisible" :title="$t('alarm.setAlarmInfo')" @on-ok="uploadSipInfo">
      <Form :model="setSipInfoformItem" :label-width="120" :rules="ruleInline">
        <FormItem :label="$t('alarm.sipAccount')" prop="PhoneNumber">
          <Input v-model="setSipInfoformItem.PhoneNumber"></Input>
        </FormItem>
        <FormItem :label="$t('login.password')" prop="RegPswd">
          <Input v-model="setSipInfoformItem.RegPswd"></Input>
        </FormItem>
        <FormItem :label="$t('operation.thealias')" prop="DisplayName">
          <Input v-model="setSipInfoformItem.DisplayName"></Input>
        </FormItem>
        <FormItem :label="$t('alarm.sipServiceAddress')" prop="RegAddr">
          <Input v-model="setSipInfoformItem.RegAddr"></Input>
        </FormItem>
        <FormItem :label="$t('alarm.sipServicePort')" prop="RegPort">
          <Input v-model="setSipInfoformItem.RegPort"></Input>
        </FormItem>
      </Form>
    </Modal>
  </Modal>
</template>

<script>
export default {
  name: "device-alarm-info",
  data() {
    return {
      visible: false,
      ids: [],
      dataListLoading: false,

      listIndex: "",

      /**
       * 表格数据
       */
      netWorkInfoList: [],
      /**
       * 表头
       */
      netWorkInfoColumns: [
        {
          title: 'MAC',
          key: 'mac',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', "MAC")
          }
        },
        {
          title: this.$t('operation.thealias'),
          key: 'DisplayName',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('operation.thealias'))
          }
        },
        {
          title: 'IP',
          key: 'ip',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', "IP")
          }
        },

        {
          title: this.$t('alarm.sipAccount'),
          key: 'PhoneNumber',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.sipAccount'))
          }
        },
        {
          title: this.$t('alarm.sipServiceAddress'),
          key: 'RegAddr',
          align: 'center',
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('alarm.sipServiceAddress'))
          }
        },
        {
          title: this.$t('alarm.sipServicePort'),
          key: 'RegPort',
          align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('alarm.sipServicePort'))
          }
        },
        {
          title: this.$t('login.password'),
          key: 'RegPswd',
          align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('login.password'))
          }
        },
        {
          title: this.$t('alarm.accountState'),
          key: 'RegisterCode',
          align: 'center',
          slot: 'RegisterCode',
          renderHeader: (h) => {
            return h('div', this.$t('alarm.accountState'))
          }
        },
        {
          title: this.$t('common.operation'),
          slot: 'action',
          width: 150,
          align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ],
      /**
       * 设置SIP信息的弹窗状态
       */
      setSipInfoVisible: false,
      /**
       * 设置SIP信息表单信息
       */
      setSipInfoformItem: {
        mac: '',
        ip: '',
        PhoneNumber: '',
        RegPswd: '',
        DisplayName: '',
        RegAddr: '',
        RegPort: '',
        address: ''
      },

    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids;
        this.getNetworkSip();
      }
    },

    /**
     * 查询局域网内报警设备sip信息
     */
    getNetworkSip() {
      this.dataListLoading = true
      this.ids.forEach(element => {
        this.$http({
          url: this.$http.adornUrl('/broadcast/get/networkSip'),
          method: 'get',
          params: this.$http.adornParams({
            deviceId: element
          })
        }).then(({ data }) => {
          this.dataListLoading = false;
          this.netWorkInfoList = [];
          if (data.code == 0 && data.data) {
            data.data.forEach(item => {
              this.netWorkInfoList.push({
                address: item.address,
                mac: item.mac,
                ip: item.ip,
                deviceId: element,
                DisplayName: item.sipInfo.DisplayName.replace(/(\s*$)/g, ""),
                PhoneNumber: item.sipInfo.PhoneNumber.replace(/(\s*$)/g, ""),
                RegAddr: item.sipInfo.RegAddr.replace(/(\s*$)/g, ""),
                RegPort: item.sipInfo.RegPort.replace(/(\s*$)/g, ""),
                RegPswd: item.sipInfo.RegPswd.replace(/(\s*$)/g, ""),
                RegisterCode: item.sipInfo.RegisterCode.replace(/(\s*$)/g, "")
              })
            })
          } else {
            this.$Message.error(data.msg.msg);
          }
        })
      });
    },

    /**
     * 打开设置SIP信息弹窗
     */
    setSipInfo(item, index) {
      // debugger
      this.setSipInfoVisible = true;
      this.setSipInfoformItem.address = item.address;
      this.setSipInfoformItem.deviceId = item.deviceId;
      this.setSipInfoformItem.PhoneNumber = item.PhoneNumber;
      this.setSipInfoformItem.RegPswd = item.RegPswd;
      this.setSipInfoformItem.DisplayName = item.DisplayName;
      this.setSipInfoformItem.RegAddr = item.RegAddr === "" ? "*************" : item.RegAddr.replace(/(\s*$)/g, "");
      this.setSipInfoformItem.RegPort = item.RegPort === "5060" ? "5062" : item.RegPort;
      this.listIndex = index;
    },

    /**
     * 设置SIP信息的确定按钮
     */
    uploadSipInfo() {
      this.$http({
        url: this.$http.adornUrl('/broadcast/set/networkSip'),
        method: 'post',
        data: this.$http.adornData(this.setSipInfoformItem)
      }).then(({ data }) => {
        // debugger
        if (data.code == 0) {
          this.setSipInfoVisible = false;
          // this.getTableList();
          this.netWorkInfoList[this.listIndex].address = this.setSipInfoformItem.address
          this.netWorkInfoList[this.listIndex].PhoneNumber = this.setSipInfoformItem.PhoneNumber
          this.netWorkInfoList[this.listIndex].RegPswd = this.setSipInfoformItem.RegPswd
          this.netWorkInfoList[this.listIndex].DisplayName = this.setSipInfoformItem.DisplayName
          this.netWorkInfoList[this.listIndex].RegAddr = this.setSipInfoformItem.RegAddr
          this.netWorkInfoList[this.listIndex].RegPort = this.setSipInfoformItem.RegPort
        } else {
          // debugger
          this.$Message.error(data.msg)
        }
      })
    },
  },
  computed: {

    ruleInline: {
      get() {
        return {
          PhoneNumber: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },

          ],
          RegPswd: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ],
          DisplayName: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ],
          RegAddr: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ],
          RegPort: [
            { required: true, message: this.$t('validate.not_empty'), trigger: 'blur' },
          ],
        }
      }
    },
  }
}
</script>

<style scoped>
</style>
