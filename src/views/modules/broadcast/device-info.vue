<!-- 智慧屏幕表格列表详情 -->
<template>
  <Modal v-model="visible" width="500" :loading="infoLoading">
    <p slot="header" style="text-align:center">
      <span>{{ $t('broadcast.broadcastInfo') }}</span>
    </p>
    <div class="loading"  v-if="infoLoading">
      <Spin class="img">
        <Icon type="ios-loading" class="demo-spin-icon-load"/>
        <div>{{ $t('common.Loading') }}...</div>
      </Spin>
    </div>
    <div v-if="cardInfo" style="height: 200px">
      <div class="info_div">
        <div class="info_title">{{ $t('home.cardNumber') }}：</div>{{cardInfo.deviceId}}
        <br/>
        <div class="info_title" >{{ $t('broadcast.broadcastProgramState') }}：</div>
          <span v-if="cardInfo.lock==true">{{ $t('broadcast.paused') }}</span>
          <span v-else-if="cardInfo.lock==false&&cardInfo.realTimeMapObject.have==0">{{ $t('broadcast.noProgram') }}</span>
          <span v-else-if="cardInfo.lock==false&&cardInfo.realTimeMapObject.have==1">{{ $t('broadcast.playing') }}</span>
        <br/>
        <div class="info_title">{{ $t('broadcast.haveProgram') }}： </div>
          <span v-if="cardInfo.realTimeMapObject.have==1">{{ $t('common.true') }}</span>
          <span v-else-if="cardInfo.realTimeMapObject.have==0">{{ $t('common.false') }}</span>
        <br/>
        <div class="info_title">{{ $t('broadcast.playMode') }}：</div>
          <span v-if="cardInfo.realTimeMapObject.playWay==0">{{ $t('screen.null') }}</span>
          <span v-else-if="cardInfo.realTimeMapObject.playWay==1">{{ $t('task.plays') }}</span>
          <span v-else-if="cardInfo.realTimeMapObject.playWay==2">{{ $t('program.PlayTime') }}</span>
        <br/>
        <div class="info_title">{{ $t('broadcast.focusMode') }}：</div>
          <span v-if="cardInfo.realTimeMapObject.playTypes==0">{{ $t('screen.null') }}</span>
          <span v-else-if="cardInfo.realTimeMapObject.playTypes==1">{{ $t('broadcast.fallingTone') }}</span>
          <span v-else-if="cardInfo.realTimeMapObject.playTypes==2">{{ $t('broadcast.mute') }}</span>
        <br/>
      </div>
    </div>
    <div v-else  style="height: 200px;text-align: center">
      <span style="font-size: 18px;color: red">{{msg}}</span>
    </div>
    <div slot="footer">
    </div>
  </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      cardInfo: {
        nameArr:[],
        realTimeMapObject:{
          have:null,
          playTypes:null,
          playWay:null
        },
        json:{},
        lock:null,
        callWaitingMap:{},
        propagandaMap:{},
        programArr:[],
        timingMap:{},
        deviceId:""
      },
      // cardInfo:{},
      infoLoading:false,
      msg:"",
      cardId:""
    }
  },
  methods: {
    // 初始化
    init: function (id) {
      this.cardId=id
      this.cardInfo=null
      this.msg=""
      this.infoLoading=true
      this.visible = true
      if (id) {
        this.$http({
          url: this.$http.adornUrl(`/broadcast/get/ipColumnState/`),
          method: 'get',
          params: this.$http.adornParams({
            "deviceId": this.cardId
          })
        }).then(({data}) => {
          this.infoLoading=false
          if (data && data.code === 0) {
            if (data.msg._type === "success") {
              this.cardInfo = data.msg
              this.visible = true
            }else {
              // this.$Message.error()
              this.msg=data.msg.msg
              this.cardInfo = null
            }
          }else {
            // this.$Message.error(data.msg.msg)
            this.msg=data.msg.msg

            this.cardInfo = null
          }
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.cardInfo={
            nameArr:[],
              realTimeMapObject:{
              have:null,
                playTypes:null,
                playWay:null
            },
            json:{},
            lock:null,
              callWaitingMap:{},
            propagandaMap:{},
            programArr:[],
              timingMap:{},
            deviceId:""
          }
        },200)
      }
    }
  }
}
</script>
<style scoped>
.info_div {
  margin-bottom: 10px;
  height: 200px;
  overflow-x: hidden;
  overflow-y: auto;
}
.info_title {
  font-size: 15px;
  font-weight: 600;
  display: inline-block;
  width: 180px;
  margin-top: 5px;
}
.loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.loading .img {
  display: block;
  width: 200px;
  height: 100px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  font-size: 14px;
}
/*加载中icon样式*/
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
