<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="init(isTodo,1)">
      <FormItem>
        <Input
          size="large"
          v-model="dataForm.name"
          :placeholder="$t('task.name')"
        ></Input>
      </FormItem>
      <FormItem v-if="isTodo === false">
        <Select
          size="large"
          v-model="dataForm.status"
          v-if="isTodo === false"
          filterable
          clearable
          :placeholder="$t('common.PleaseSelect') + $t('common.state')"
        >
          <Option
            v-for="item in fileStatus"
            :value="item.value"
            :key="item.value"
            >{{ $t(item.label) }}</Option
          >
        </Select>
      </FormItem>
      <FormItem>
        <Button style="margin-right: 6px" @click="init(isTodo,1)" size="large">
          <div style="margin: 3px 8px">{{ $t("common.query") }}</div>
        </Button>
        <Button
          v-if="isAuth('broadcast:task:save') && isTodo === false"
          style="margin-right: 6px"
          size="large"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          <div style="margin: 3px 8px">{{ $t("common.newlyBuild") }}</div>
        </Button>
        <Button type="warning" size="large" style="margin-right: 6px" v-if="isTodo === true" :disabled="dataListSelections.length <= 0" @click="auditFlowHandle()">{{$t('approval.batchReview')}}</Button>
        <Button
          v-if="isAuth('broadcast:task:delete') && isTodo === false"
          style="margin-right: 6px"
          size="large"
          type="error"
          :disabled="dataListSelections.length <= 0"
          @click="deleteHandle()"
        >
          <div style="margin: 3px 8px">{{ $t("common.batchDel") }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table
      border
      :columns="dataConlums"
      :data="dataList"
      @on-selection-change="selectionChangeHandle"
      :loading="dataListLoading"
      style="width: 100%"
      :max-height="tableHeight"
      ref="selection"
      @on-row-click="selectThisRow"
    >
      <template slot-scope="{ row, index }" slot="timing">
        <span v-if="row.timing === true">
          {{$t('common.true')}}
        </span>
        <span v-else>
          {{$t('common.false')}}
        </span>
      </template>


      <template slot-scope="{ row, index }" slot="type">
        <span v-if="row.type === 'setCallWaiting'">
          {{$t('task.inStreamTask')}}
        </span>
        <span v-else>
          {{$t('task.normalTask')}}
        </span>
      </template>
<!--      <template slot-scope="{ row, index }" slot="totalSize">
        <span>{{ row.totalSize | filterType }}</span>
      </template>-->
      <template slot-scope="{ row, index }" slot="programPlays">
        <div v-if="row.programPlays && row.programPlays === 'cycle'">{{$t('task.InfiniteLoop')}}</div>
        <div v-else>{{row.programPlays}}</div>
      </template>
      <template slot-scope="{ row, index }" slot="status">
        <Tag color="orange" v-if="row.status === 0">{{
          $t("file.checkPending")
        }}</Tag>
        <Tag color="cyan" v-if="row.status === 1">{{
          $t("file.under_review")
        }}</Tag>
        <Tag color="blue" v-if="row.status === 2">{{
          $t("file.approved")
        }}</Tag>
        <Tag color="volcano" v-if="row.status === 3">{{
          $t("file.auditFailed")
        }}</Tag>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button
          type="warning"
          size="small"
          v-if="userInfo.noExamine === 0 && isTodo === false"
          style="margin-right: 5px; font-size: 11px"
          @click="auditFlowHandle(row.id)"
          >{{ $t("screen.reviewDetails") }}</Button
        >
        <Button type="info" size="small" style="margin-right: 5px;font-size: 11px"  v-if="isTodo === false" @click="releaseHandle(row.id)" :disabled="row.status !== 2">{{$t('common.release')}}</Button>
        <Button v-if="isAuth('broadcast:task:update') && isTodo === false" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
        <Button
          v-if="isAuth('broadcast:task:delete') && isTodo === false"
          type="error"
          size="small"
          style="font-size: 11px"
          @click="deleteHandle(row.id)"
          >{{ $t("common.delete") }}</Button
        >
        <Button
          type="warning"
          size="small"
          v-if="isTodo === true"
          style="margin-right: 5px; font-size: 11px"
          @click="auditFlowHandle(row.id)"
          >{{ $t("file.examine") }}</Button
        >
      </template>
    </Table>
    <Page
      style="float: right; margin-top: 20px"
      :total="totalPage"
      :current="pageIndex"
      :page-size="pageSize"
      show-elevator
      show-sizer
      :page-size-opts="[10, 20, 50, 100]"
      show-total
      @on-change="currentChangeHandle"
      @on-page-size-change="sizeChangeHandle"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="init(isTodo)"
    ></add-or-update>
    <!-- 审批流 -->
    <audit-flow
      v-if="auditFlowVisible"
      ref="auditFlow"
      @refreshDataList="init(isTodo)"
    ></audit-flow>
      <!-- 发布 -->
    <task-release v-if="releaseVisible" ref="taskRelease"></task-release>
  </div>
</template>

<script>
import AddOrUpdate from "./task-add-or-update";
import auditFlow from "./task-audit-flow";
import taskRelease from './task-release.vue'
export default {
  data() {
    return {
      dataForm: {
        name: "",
        status: "",
      },
      fileStatus: [
        { value: "0", label: "file.checkPending" },
        { value: "1", label: "file.under_review" },
        { value: "2", label: "file.approved" },
        { value: "3", label: "file.auditFailed" },
      ],
      dataConlums: [
        { type: "selection", width: 60, align: "center" },
        { title: this.$t("task.name"), key: "name", align: "center", tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('task.name'))
          }
        },
        // { title: this.$t("task.isCycle"), key: "timing",slot: "timing", align: "center",
        //   renderHeader:(h)=>{
        //     return h('div',this.$t('task.isCycle'))
        //   }
        // },

        { title: this.$t("task.type"),  key: "type",slot: "type", align: "center",
          renderHeader:(h)=>{
            return h('div',this.$t("task.type"))
          }
        },

        { title: this.$t("task.cycleIndex"), key: "programPlays", slot: "programPlays", align: "center",
          renderHeader:(h)=>{
            return h('div',this.$t('task.cycleIndex'))
          }
        },
/*        { title: this.$t("program.totalSize"), key: "totalSize", slot: "totalSize", align: "center",
          renderHeader:(h)=>{
            return h('div',this.$t('program.totalSize'))
          }
        },*/
        { title: this.$t("common.state"), key: "status", slot: "status", align: "center",
          renderHeader:(h)=>{
            return h('div',this.$t('common.state'))
          }
        },

        { title: this.$t("common.createTime"),  key: "createTime", align: "center",
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        { title: this.$t("common.operation"), slot: "operation", align: "center", width: 250,
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      auditFlowVisible: false,
      isTodo: false,
      releaseVisible: false
    };
  },
  activated () {
    this.initData()
  },
  methods: {
    initData(){
      this.dataForm =  {
        name: "",
        status: "",
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
    },
    init(isTodo,isQuery) {
      this.isTodo = isTodo;
      this.dataListLoading = true;
      if (isQuery===1){
        this.pageIndex=1
      }
      this.$http({
        url: this.$http.adornUrl("/broadcast/task/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          name: this.dataForm.name,
          status: this.dataForm.status,
          todo: isTodo === true ? 1 : "",
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.init(isTodo,isQuery)
          }
        }
        this.dataListSelections = [];
        this.dataListLoading = false;
      });
    },
    //判断是否为插播任务
    judgeNormalTask(){

    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.init(this.isTodo);
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.init(this.isTodo);
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection();
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 删除
    deleteHandle(id) {
      var taskIds = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id;
          });
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl("/broadcast/task/delete"),
            method: "post",
            data: this.$http.adornData(taskIds, false),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t("common.operationSuccessful"),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === taskIds.length) {
                    this.pageIndex--
                  }
                  this.init(this.isTodo);
                  this.dataListSelections = [];
                },
              });
            } else {
              this.$Message.error(data.msg);
            }
          });
        },
      });
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id);
      });
    },
    // 查询审核流
    auditFlowHandle(id) {
      var taskIds = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.id;
        });
      this.auditFlowVisible = true;
      this.$nextTick(() => {
        this.$refs.auditFlow.init(taskIds);
      });
    },
    // 发布任务
    releaseHandle (id) {
      this.releaseVisible = true
      this.$nextTick(() => {
        this.$refs.taskRelease.init(id)
      })
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },
  components: {
    AddOrUpdate,
    auditFlow,
    taskRelease
  },
  watch: {
  }
};
</script>

<style>
</style>
