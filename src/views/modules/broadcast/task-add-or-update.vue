<template>
  <div>
    <Modal v-model="visible" width="900">
      <p slot="header" style="text-align:center">
        <span>{{ !dataForm.id ? $t('common.newlyBuild') : $t('common.update') }}</span>
      </p>
      <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 450px;" label-position="top"
            @keyup.enter.native="dataFormSubmit()">

        <FormItem prop="name" :label="$t('task.name')">
          <Input size="large" v-model="dataForm.name"
                 :placeholder="$t('common.PleaseInput') + $t('task.name')"/>
        </FormItem>

        <!--        任务类型-->
        <FormItem prop="playTypes" :label="$t('task.type')">
          <RadioGroup v-model="dataForm.type">
            <Radio label="ipColumnService" :disabled="normalDisable">{{ $t('task.normalTask') }}</Radio>
            <Radio label="setCallWaiting" :disabled="inStreamDisable">{{ $t('task.inStreamTask') }}</Radio>
          </RadioGroup>
        </FormItem>
        <!--        <FormItem :label="$t('task.cycleIndex')">
                  <RadioGroup v-model="dataForm.programPlaysFlag">
                    <Radio label="true">{{$t('task.InfiniteLoop')}}</Radio>
                    <Radio label="false">{{$t('task.cycleIndex')}}</Radio>
                  </RadioGroup>
                  <span v-if="dataForm.programPlaysFlag === 'false'"><InputNumber v-model="dataForm.programPlaysNum" :min="1" :max="100"/></span>
                </FormItem>-->
        <FormItem prop="programs" long>
          <ButtonGroup style="width: 100%">
            <Button type="success" style="width: 50%" @click="headlerAddTask()">
              {{ $t('common.add') }}{{ $t('task.task') }}
            </Button>
            <Button type="error" style="width: 50%" @click="taskDeleteHandle()" :disabled="taskSelections.length <= 0">
              {{ $t('common.batchDel') }}
            </Button>
          </ButtonGroup>
          <div style="height: 180px;overflow: auto;">
            <Table border ref="selectionTask" @on-selection-change="programsSelectionChangeHandle"
                   :columns="programsColumns" :data="dataForm.programs">
            </Table>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button size="large" @click="visible = false">{{ $t('common.cancel') }}</Button>
        <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{ $t('common.confirm') }}
        </Button>
      </div>
    </Modal>
    <!-- 任务窗口 -->
    <Modal v-model="taskVisible" width="900">
      <p slot="header" style="text-align:center">
        <span>{{ !taskDataForm.addId ? $t('common.newlyBuild') : $t('common.update') }}{{ $t('task.task') }}</span>
      </p>
      <Form ref="taskDataForm" :model="taskDataForm" :rules="taskDataRule" style="height: 390px;" label-position="left"
            :label-width="80">
        <FormItem prop="name" :label="$t('task.name')">
          <Input size="large" v-model="taskDataForm.name"
                 :placeholder="$t('common.PleaseInput') + $t('task.name')"/>
        </FormItem>
        <FormItem prop="materialType" :label="$t('task.type')">
          <RadioGroup v-model="taskDataForm.materialType">
            <Radio :label="item.value" v-for="item in materialTypes" :key="item.value">{{ $t(item.name) }}</Radio>
          </RadioGroup>
          <!-- <Select size="large" v-model="taskDataForm.materialType" transfer>
            <Option v-for="item in materialTypes" :key="item.value" :value="item.value" :label="item.name"></Option>
          </Select> -->
        </FormItem>
        <div v-if="taskDataForm.materialType === 'text'">
          <FormItem prop="text" :label="$t('task.text')">
            <Input size="large" v-model="taskDataForm.text" type="textarea" maxlength="200"
                   :autosize="{minRows: 2,maxRows: 5}"
                   :placeholder="$t('common.PleaseInput') + $t('task.text') + ' '+$t('common.MaximumNumberOfWords200')"/>
            <a href="javascript:void(0)" @click="textTemplateHandle">{{ $t('task.ImportTemplate') }}</a>
          </FormItem>
          <!--          <FormItem prop="voiceName" :label="$t('task.voiceName')">
                      <RadioGroup v-model="taskDataForm.voiceName">
                        <Radio label="xiaoyan">{{$t('task.femaleVoice')}}</Radio>
                        <Radio label="aisjiuxu">{{$t('task.maleVoice')}}</Radio>
                      </RadioGroup>
                    </FormItem>-->

          <!--          语速方案1-->
          <!--          <FormItem prop="speed" :label="$t('task.speed')">
                      <Slider v-model="taskDataForm.speed" :min="1" :max="60" style="width: 300px"
                      :tip-format="speedTipFormat"></Slider>
                    </FormItem>-->

          <!--          语速方案2-->
          <FormItem prop="speed" :label="$t('task.speed')">
            <RadioGroup v-model="taskDataForm.speed">
              <Radio label="1">{{ $t('task.normal') }}</Radio>
              <Radio label="1.5">{{ $t('task.faster') }}</Radio>
              <Radio label="2">{{ $t('task.fast') }}</Radio>
            </RadioGroup>
          </FormItem>


          <FormItem prop="playTypes" :label="$t('task.playTypes')">
            <RadioGroup v-model="taskDataForm.playWay">
              <!--              <Radio label="plays">{{$t('task.InfiniteLoop')}}</Radio>-->
              <Radio label="plays">{{ $t('task.specifyPlays') }}</Radio>
              <!--              <Radio label="playTime">{{$t('task.specifyPlayTime')}}</Radio>-->
              <Radio label="cycle" v-if="this.taskDataForm.schedules.length>0">{{ $t('task.allTime') }}</Radio>


            </RadioGroup>
          </FormItem>

          <!--          当选中播放次数时，需要有一个指定次数-->
          <div v-if="taskDataForm.playWay==='plays'">
            <FormItem prop="plays" :label="$t('task.plays')">
              <InputNumber v-model="taskDataForm.plays" :min="1"/>
            </FormItem>
          </div>

          <!--          <div v-else>-->
          <!--            <FormItem prop="plays" :label="$t('program.PlayTime')">-->
          <!--              <InputNumber v-model="taskDataForm.playTime" :min="1" /> {{$t('program.Second')}}-->
          <!--            </FormItem>-->
          <!--          </div>-->

          <!--          <FormItem prop="pitch" :label="$t('task.pitch')">
                      <Slider v-model="taskDataForm.pitch" :min="1" :max="100" style="width: 300px"
                      :tip-format="pitchTipFormat"></Slider>
                      &lt;!&ndash; <InputNumber v-model="taskDataForm.pitch" :min="1" :max="100"/> &ndash;&gt;
                    </FormItem>-->
        </div>
        <div v-if="taskDataForm.materialType === 'mp3'">
          <FormItem prop="id">
            <Button :loading="mediaLoading" @click="mediaDataForm()">{{ $t('task.selectMedia') }}</Button>
            <div v-if="Object.keys(taskDataForm.mediaInfo).length !== 0">
              <a href="javascript:void(0)"
                 @click="setPicture(taskDataForm.mediaInfo.id,2)">{{ taskDataForm.mediaInfo.name }}.{{ taskDataForm.mediaInfo.suffix }}</a>
              &nbsp;&nbsp;{{ taskDataForm.mediaInfo.fileSize | filterType }}
            </div>
          </FormItem>
          <FormItem prop="playTypes" :label="$t('task.playTypes')">
            <RadioGroup v-model="taskDataForm.playWay">
              <Radio label="plays">{{ $t('task.specifyPlays') }}</Radio>
              <Radio label="cycle" v-if="this.taskDataForm.schedules.length>0">{{ $t('task.allTime') }}</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem prop="plays" :label="$t('task.plays')">
            <InputNumber v-model="taskDataForm.plays" :min="1" :max="100"/>
          </FormItem>
        </div>
        <div v-if="dataForm.type==='ipColumnService'">
          <ButtonGroup style="width: 100%">
            <Button type="success" style="width: 50%" @click="headlerAddTiming()">
              {{ $t('common.add') }}{{ $t('card.timing') }}
            </Button>
            <Button type="error" style="width: 50%" @click="schedulesDeleteHandle()"
                    :disabled="schedulesSelections.length <= 0">{{ $t('common.batchDel') }}
            </Button>
          </ButtonGroup>
          <div style="height: 180px;overflow: auto;">
            <Table border ref="selection" @on-selection-change="timingSelectionChangeHandle" :columns="timingColumns"
                   :data="taskDataForm.schedules">
              <template slot-scope="{ row, index }" slot="dateRange">
                <DatePicker type="daterange" @on-change="dateRangeChangeHandle($event, index)"
                            @on-clear="dateRangeClearHandle(index)"
                            :placeholder="$t('common.PleaseSelect')" v-model="row.date" transfer format="yyyy-MM-dd">
                </DatePicker>
              </template>
              <template slot-scope="{ row, index }" slot="timeFrame">
                <TimePicker type="timerange" @on-change="timeFrameChangeHandle($event, index)"
                            @on-clear="timeFrameClearHandle(index)"
                            :placeholder="$t('common.PleaseSelect')" v-model="row.time" transfer format="HH:mm">
                </TimePicker>
              </template>
              <template slot-scope="{ row, index }" slot="specifyWeek">
                <Select v-model="row.weekFilter" transfer multiple @on-select="specifyWeekSelect($event, index)">
                  <Option v-for="item in weekOptions" :key="item.value" :value="item.value"
                          :label="$t(item.name)"></Option>
                </Select>
              </template>
            </Table>
          </div>
        </div>
      </Form>
      <div slot="footer">
        <Button size="large" @click="taskVisible = false">{{ $t('common.cancel') }}</Button>
        <Button type="primary" size="large" @click="taskDataFormSubmit()">{{ $t('common.confirm') }}</Button>
      </div>
    </Modal>
    <Modal v-model="mediaVisible" width="700">
      <p slot="header" style="text-align:center">
        <span>{{ $t('task.task') }}</span>
      </p>
      <Table highlight-row ref="currentRowTable" :columns="mediaListColumns" :data="mediaList"
             @on-current-change="mediaCurrentChangeHandle">
        <template slot-scope="{ row, index }" slot="filePath">
          <div v-if="row.suffix === 'mp3'">
            <div @click="setPicture(row.id,2)">
              <img
                src="@/assets/img/audio.png"
                style="margin-top: 5px"
                height="50px"
                width="50px"
              />
            </div>
          </div>
        </template>
        <template slot-scope="{ row, index }" slot="fileSize">
          <span>{{ row.fileSize | filterType }}</span>
        </template>
      </Table>
      <div slot="footer">
        <Button size="large" @click="mediaVisible = false">{{ $t('common.cancel') }}</Button>
        <Button type="primary" size="large" @click="mediaVisible = false">{{ $t('common.confirm') }}</Button>
      </div>
    </Modal>
    <!-- 文字转语音模版 -->
    <text-template v-if="textTemplate" ref="textTemplate" @resultData="textResultData"></text-template>
  </div>
</template>

<script>
import textTemplate from './text-template'

export default {
  data() {
    return {
      //插播任务类型是否禁用
      inStreamDisable: false,
      //普通任务类型是否禁用
      normalDisable: false,
      textTemplate: false,
      visible: false,
      loading: false,
      mediaLoading: false,
      dataForm: {
        id: 0,
        name: '',

        type: 'ipColumnService',

        timing: 0,
        // totalSize: 0,
        // programPlaysFlag: 'true',
        programPlaysNum: 1,
        programPlays: "cycle",// 默认无限循环
        programs: [],
        schedules: [],
        infoId: 0
      },


      // //状态文字
      // stateText:"",

      schedulesSelections: [],
      weekOptions: [
        {value: 1, name: 'common.Monday'},
        {value: 2, name: 'common.Tuesday'},
        {value: 3, name: 'common.Wednesday'},
        {value: 4, name: 'common.Thursday'},
        {value: 5, name: 'common.Friday'},
        {value: 6, name: 'common.Saturday'},
        {value: 7, name: 'common.Sunday'}
      ],
      timingColumns: [
        {type: 'selection', width: 60, align: 'center'},
        {
          title: this.$t('card.DateRange'), key: 'dateRange', align: 'center', slot: 'dateRange',
          renderHeader: (h) => {
            return h('div', this.$t('card.DateRange'))
          }
        }, // 日期范围
        {
          title: this.$t('card.timeFrame'), key: 'timeFrame', align: 'center', slot: 'timeFrame',
          renderHeader: (h) => {
            return h('div', this.$t('card.timeFrame'))
          }
        },// 时间范围
        {
          title: this.$t('card.WeekRange'), key: 'specifyWeek', align: 'center', slot: 'specifyWeek',
          renderHeader: (h) => {
            return h('div', this.$t('card.WeekRange'))
          }
        },// 星期范围
        {
          title: this.$t('common.operation'), // 删除操作
          key: 'operation',
          align: 'center',
          width: 80,
          render: (h, {row, index}) => {
            return h('a', {
              on: {
                click: () => {
                  this.taskDataForm.schedules.splice(index, 1)
                }
              }
            }, this.$t('common.delete'))
          },
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ],
      taskVisible: false,
      taskDataForm: {
        id: '',
        name: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
        materialType: 'text',
        md5: '',
        playWay: 'plays',
        // playTime:1,

        stateText: '',
        schedulesState: '',
        plays: 1,
        size: 0,
        text: '',
        speed: "1",// 语速
        /*        voiceName: 'aisjiuxu',
                pitch: 78,// 语调
                volume: 100, // 音量*/

        mediaInfo: {},// 媒体对象
        schedules: []
      },
      taskSelections: [],
      materialTypes: [
        {value: 'text', name: 'task.textToLanguage'},
        {value: 'mp3', name: 'task.media'}
      ],
      mediaList: [],
      mediaTotalPage: 0,
      mediaPageIndex: 1,
      mediaVisible: false,
      mediaListColumns: [
        {
          title: this.$t("file.thumbnail"), key: "filePath", slot: "filePath", align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('file.thumbnail'))
          }
        },
        {
          title: this.$t("file.name"), key: "fileName", align: "center", tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('file.name'))
          }
        },
        {
          title: this.$t("file.size"), key: "fileSize", slot: "fileSize", align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('file.size'))
          }
        },
      ],
      programsColumns: [
        {type: 'selection', width: 60, align: 'center'},
        {
          title: this.$t('task.name'), key: 'name', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('task.name'))
          }
        }, // 任务名称
        {
          title: this.$t('task.type'), key: 'materialType', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('task.type'))
          }
        },// 任务类型
        {
          title: this.$t('task.text'), key: 'text', align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('task.text'))
          }
        },// 文本
        {
          title: this.$t('common.state'), key: 'stateText', align: 'center', tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('common.state'))
          }
        },
        {
          title: this.$t('task.isTiming'), key: 'schedulesState', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('task.isTiming'))
          }
        },
        {
          title: this.$t('common.operation'), // 删除修改操作
          key: 'operation',
          align: 'center',
          width: 160,
          render: (h, {row, index}) => {
            return h('div', [
              h('a', {
                style: {
                  'margin-right': '15px'
                },
                on: {
                  click: () => {
                    this.dataForm.programs[index].addId = index + 1
                    this.taskDataForm = this.dataForm.programs[index]
                    this.taskVisible = true
                  }
                }
              }, this.$t('common.update')),
              h('a', {
                on: {
                  click: () => {
                    this.dataForm.programs.splice(index, 1)
                    if (this.dataForm.programs.length===0){
                      this.inStreamDisable=false
                      this.normalDisable=false
                    }
                  }
                }
              }, this.$t('common.delete'))
            ])
          },
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ]
    }
  },
  computed: {
    dataRule: {
      get() {
        return {
          name: [
            {required: true, message: this.$t('task.name') + this.$t('validate.not_empty'), trigger: 'blur'}
          ],
          programs: [
            {
              validator: (rule, value, callback) => {
                if (this.dataForm.programs.length === 0) {
                  callback(new Error(this.$t('task.task') + this.$t('validate.not_empty')));
                } else {
                  callback();
                }
              }, trigger: 'blur'
            }
          ]
        }
      }
    },
    taskDataRule: {
      get() {
        return {
          name: [
            {required: true, message: this.$t('task.name') + this.$t('validate.not_empty'), trigger: 'blur'}
          ],
          materialType: [
            {required: true, message: this.$t('task.type') + this.$t('validate.not_empty'), trigger: 'change'}
          ],
          text: [
            {
              validator: (rule, value, callback) => {
                if (this.taskDataForm.materialType === 'text' && value === '') {
                  callback(new Error(this.$t('task.text') + this.$t('validate.not_empty')));
                } else {
                  callback();
                }
              }, trigger: 'blur'
            }
          ],
          id: [
            {
              validator: (rule, value, callback) => {
                if (this.taskDataForm.materialType === 'mp3' && this.taskDataForm.id === '') {
                  callback(new Error(this.$t('task.media') + this.$t('validate.not_empty')));
                } else {
                  callback();
                }
              }, trigger: 'blur'
            }
          ]
        }
      }
    },
  },
  methods: {
    // 初始化
    init(id) {
      this.visible = true
      this.normalDisable = false
      this.inStreamDisable = false
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      this.dataForm.id = id || 0
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(`/broadcast/task/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.broadcast) {
              this.dataForm.id = data.broadcast.id
              this.dataForm.name = data.broadcast.name
              this.dataForm.timing = data.broadcast.timing
              this.dataForm.type = data.broadcast.type
              // this.dataForm.totalSize = data.broadcast.totalSize
              this.dataForm.programs = data.broadcast.programsList
              // this.dataForm.schedules = data.broadcast.schedulesList
              if (this.dataForm.type === 'setCallWaiting') {
                data.broadcast.programPlays = '1';
                this.dataForm.programPlays = data.broadcast.programPlays
              } else {
                this.dataForm.programPlays = data.broadcast.programPlays
              }

              this.dataForm.infoId = data.broadcast.infoId

              // if (data.broadcast.programPlays === 'cycle') {
              //   this.dataForm.programPlays = data.broadcast.programPlays
              // } else {
              //   this.dataForm.programPlaysFlag = 'false'
              //   this.dataForm.programPlaysNum = parseInt(data.broadcast.programPlays)
              // }
            }
          }
        })
      }
    },
    //显示在节目表格中的播放次数或时间
    textChange() {
      if (this.taskDataForm.playWay === 'plays') {
        this.taskDataForm.stateText = this.$t('program.play') + this.taskDataForm.plays + this.$t('program.times')
      } else {
        this.taskDataForm.stateText = this.$t('task.allTime')
      }
      // }else if(this.taskDataForm.playWay==='playTime'){
      //   this.taskDataForm.stateText="播放"+this.taskDataForm.playTime+"秒"
      // }
    },
    //显示在节目表格中的定时状态
    schedulesStateChange() {
      if (this.taskDataForm.schedules.length > 0) {
        this.taskDataForm.schedulesState = this.$t('common.true')
      } else {
        this.taskDataForm.schedulesState = this.$t('common.false')
      }
    },

    textResultData(content) {
      this.taskDataForm.text = content
    },
    textTemplateHandle() {
      this.textTemplate = true
      this.$nextTick(() => {
        this.$refs.textTemplate.init()
      })
    },
    // 添加定时
    headlerAddTiming() {
      var id = 1
      if (this.taskDataForm.schedules.length > 0) {
        id = this.taskDataForm.schedules[this.taskDataForm.schedules.length - 1].id + 1
      }
      const addSchedules = {
        id: id,
        dateType: 'All',
        date: '',
        startDate: '',
        endDate: '',
        timeType: 'All',
        time: '',
        startTime: '',
        endTime: '',
        filterType: 'None',
        weekFilter: []
      }
      this.taskDataForm.schedules.push(addSchedules)
    },
    // 定时改变选中
    timingSelectionChangeHandle() {
      this.schedulesSelections = this.$refs.selection.getSelection();
    },
    // 定时开始日期与结束日期
    dateRangeChangeHandle(date, index) {
      if (date != null && date.length === 2) {
        this.taskDataForm.schedules[index].dateType = 'Range'
        this.taskDataForm.schedules[index].date = date
        // 数组中第一个值为startDate，第二个值为endDate
        if (date[0]) {
          this.taskDataForm.schedules[index].startDate = date[0]
        } else {
          this.taskDataForm.schedules[index].startDate = ''
        }
        if (date[1]) {
          this.taskDataForm.schedules[index].endDate = date[1]
        } else {
          this.taskDataForm.schedules[index].endDate = ''
        }
      } else {
        this.taskDataForm.schedules[index].dateType = 'All'
        this.taskDataForm.schedules[index].date = []
      }
    },
    dateRangeClearHandle(index) {
      this.taskDataForm.schedules[index].dateType = 'All'
      this.taskDataForm.schedules[index].date = []
    },
    // 定时开始时间与结束时间
    timeFrameChangeHandle(time, index) {
      if (time && time.length === 2) {
        this.taskDataForm.schedules[index].timeType = "Range"
        this.taskDataForm.schedules[index].time = time
        // 数组中第一个值为startTime，第二个值为endTime
        if (time[0]) {
          this.taskDataForm.schedules[index].startTime = time[0]
        } else {
          this.taskDataForm.schedules[index].startTime = ''
        }
        if (time[1]) {
          this.taskDataForm.schedules[index].endTime = time[1]
        } else {
          this.taskDataForm.schedules[index].endTime = ''
        }
      } else {
        this.taskDataForm.schedules[index].timeType = "All"
        this.taskDataForm.schedules[index].time = []
      }
    },
    timeFrameClearHandle(index) {
      this.taskDataForm.schedules[index].timeType = "All"
      this.taskDataForm.schedules[index].time = []
    },
    // 定时周几
    specifyWeekSelect(week, index) {
      if (this.taskDataForm.schedules[index].weekFilter.indexOf(week.value) == -1) {
        this.taskDataForm.schedules[index].weekFilter.push(week.value)
      } else {
        this.taskDataForm.schedules[index].weekFilter.some((item, i) => {
          if (item === week.value) {
            this.taskDataForm.schedules[index].weekFilter.splice(i, 1)
            return true
          }
        })
      }
      if (this.taskDataForm.schedules[index].weekFilter.length > 0) {
        this.taskDataForm.schedules[index].filterType = "Week"
      } else {
        this.taskDataForm.schedules[index].filterType = "None"
      }
    },
    // 删除定时
    schedulesDeleteHandle() {
      var ids = this.schedulesSelections.map(item => {
        return item.id;
      });
      ids.forEach(item => {
        this.taskDataForm.schedules.forEach((schedule, index) => {
          if (item === this.taskDataForm.schedules[index].id) {
            this.taskDataForm.schedules.splice(index, 1)
          }
        })
      })
      this.schedulesSelections = []
    },
    // 添加任务窗口
    headlerAddTask() {

      this.taskVisible = true
      // this.taskDataForm = {}
      this.taskDataForm = {
        id: '',
        name: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
        materialType: 'text',
        md5: '',
        plays: 1,
        // playTime: 1,
        size: 0,
        text: '',
        playWay: 'plays',
        speed: "1",// 语速
        mediaInfo: {},
        schedules: [],
      }
      // this.$refs['taskDataForm'].resetFields()
    },
    setPicture(id, type) {
      if (type === 2) {
        var page = window.open()
        var html = `<body style='background:black'>`
        html += `<div style='margin:0 auto;'>`
        html += `<audio controls="controls" autoplay style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; max-height: 100%; max-width: 100%; margin:auto">`
        html += `<source src="${this.$http.adornUrl(`/broadcast/media/download/${id}?inline=true`)}" type="audio/mp3"/>`
        html += `</audio>`
        html += `</div>`
        html += `</body>`
        page.document.write(html);
      }
    },
    programsSelectionChangeHandle() {
      this.taskSelections = this.$refs.selectionTask.getSelection();
    },
    // 查询媒体
    mediaDataForm() {
      this.mediaLoading = true
      this.$http({
        url: this.$http.adornUrl("/broadcast/media/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.mediaPageIndex,
          limit: 10,
          status: 2
        }),
      }).then(({data}) => {
        this.mediaList = data.page.list;
        this.mediaTotalPage = data.page.totalCount;
        this.mediaVisible = true
        this.mediaLoading = false
      })
    },
    // 选择媒体资源
    mediaCurrentChangeHandle(currentRow) {
      this.taskDataForm.id = currentRow.id
      this.taskDataForm.md5 = currentRow.md5
      this.taskDataForm.size = currentRow.fileSize
      this.taskDataForm.mediaInfo.name = currentRow.fileName
      this.taskDataForm.mediaInfo.suffix = currentRow.suffix
      this.taskDataForm.mediaInfo.fileSize = currentRow.fileSize
      this.taskDataForm.mediaInfo.id = currentRow.id
      this.$refs.taskDataForm.fields[2].validateState = ''
      this.$refs.taskDataForm.fields[2].validateMessage = ''
    },
    // 删除任务
    taskDeleteHandle() {
      // debugger
      var ids = this.taskSelections.map(item => {
        return item.tempId;
      });
      var len = this.dataForm.programs.length
      ids.forEach(item => {
        this.dataForm.programs.forEach((program, index) => {
          if (item === this.dataForm.programs[index].tempId) {
            this.dataForm.programs.splice(index, 1)
            len--;
          }
        })
      })
      //当批量删除所有任务时，恢复任务类型的选择
      if (len === 0) {
        this.normalDisable = false
        this.inStreamDisable = false
      }
      // this.taskSelections = []
    },
    // 添加任务
    taskDataFormSubmit() {
      if (this.dataForm.type === 'setCallWaiting') {
        this.normalDisable = true
      } else {
        this.inStreamDisable = true
      }
      this.$refs['taskDataForm'].validate((valid) => {
        if (valid) {
          var id = this.dataForm.programs.length
          const program = {
            tempId: id,
            priority: id,
            name: this.taskDataForm.name,
            materialType: this.taskDataForm.materialType,

            playWay: this.taskDataForm.playWay,//播放方式
            // playTime: this.taskDataForm.playTime,//播放时长


            plays: this.taskDataForm.plays,//播放次数
            id: '',
            md5: '',
            size: 0,
            // schedules: JSON.stringify(this.taskDataForm.schedules),
            schedules: this.taskDataForm.schedules,
            text: '',
            speed: "1",// 语速
            /*voiceName: 'aisjiuxu',
              pitch: 78,// 语调
              volume: 100, // 音量*/
            mediaInfo: {},// 媒体对象
            schedulesState: '',
            stateText: '',
          }

          this.textChange()
          this.schedulesStateChange()
          program.stateText = this.taskDataForm.stateText
          program.schedulesState = this.taskDataForm.schedulesState
          if (this.taskDataForm.materialType === 'text') {
            program.text = this.taskDataForm.text
            program.speed = this.taskDataForm.speed
            // if (program.playWay==='playTime'){
            //   program.playTime=this.taskDataForm.playTime
            //   this.$delete(program,'plays')
            // }else {
            //   program.plays=this.taskDataForm.plays
            //  delete(program.playTime)
            //   this.$delete(program,'playTime')
            // }
            if (program.playWay === 'cycle') {
              this.$delete(program, 'plays')
            } else {
              program.plays = this.taskDataForm.plays
            }
          } else if (this.taskDataForm.materialType === 'mp3') {
            program.id = this.taskDataForm.id
            program.md5 = this.taskDataForm.md5
            program.size = this.taskDataForm.size
            program.mediaInfo = this.taskDataForm.mediaInfo
          }
          if (this.taskDataForm.addId) {
            this.dataForm.programs = this.dataForm.programs.map(item => {
              if (this.taskDataForm.addId === item.addId) {
                item = program
              }
              return item
            })
          } else {
            // console.log(program)
            this.dataForm.programs.push(program)
          }
          this.taskVisible = false

        }
      })

    },
    // 表单提交
    dataFormSubmit: function () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          // 计算总大小
          /*          this.dataForm.totalSize = 0
                    if (this.dataForm.programs.length > 0) {
                      this.dataForm.programs.forEach(item => {
                        if (item.materialType === "mp3") {
                          this.dataForm.totalSize += item.size
                        }
                      })
                    }*/
          if (this.dataForm.programs.length > 0) {
            for (let i = 0; i < this.dataForm.programs.length; i++) {
              const element = this.dataForm.programs[i];
              element.priority = i + 1;
            }
          }
          // 总循环次数
          // if (this.dataForm.programPlaysFlag === 'false') {
          //   this.dataForm.programPlays = this.dataForm.programPlaysNum
          //   this.dataForm.timing = 0
          // } else {
          //   this.dataForm.programPlays = 'cycle'
          //   this.dataForm.timing = 1
          // }

          if (this.dataForm.type === 'setCallWaiting') {
            this.dataForm.programPlays = '1'
          } else {
            this.dataForm.programPlays = 'cycle'
          }
          this.dataForm.timing = 1
          // var pragarms=this.dataForm.programs
          // console.log(typeof pragarms)
          // var programList=JSON.stringify(this.dataForm.programs);
          // console.log(this.dataForm.programs)
          // console.log(typeof programList)
          // console.log(programList)


          this.$http({
            url: this.$http.adornUrl(`/broadcast/task/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'timing': this.dataForm.timing,
              'type': this.dataForm.type,
              // 'totalSize': this.dataForm.totalSize,
              'programPlays': this.dataForm.programPlays,
              'programs': JSON.stringify(this.dataForm.programs),
              'infoId': this.dataForm.infoId
              // 'schedules': JSON.stringify(this.dataForm.schedules)
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    }
  },
  watch: {
    'taskDataForm.materialType': function (newVal, oldVal) {
      // 下标2为 MP3 选择媒体 与 文字转语音 文本内容
      this.$refs.taskDataForm.fields[2].validateState = ''
      this.$refs.taskDataForm.fields[2].validateMessage = ''
    },
    'dataForm.programs': function (newVal, oldVal) {
      // 下标2为 MP3 选择媒体 与 文字转语音 文本内容
      if (newVal.length > 0) {
        this.$refs.dataForm.fields[1].validateState = ''
        this.$refs.dataForm.fields[1].validateMessage = ''
      }
    },
/*    'taskVisible': function (newVal, oldVal) {
      if (newVal === false) {
        console.log("改变了")
        this.taskDataForm = {
          id: '',
          name: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
          materialType: 'mp3',
          md5: '',
          plays: 1,
          // playTime: 1,
          size: 0,
          text: '',
          playWay: 'plays',
          speed: "1",// 语速
          /!*          voiceName: 'aisjiuxu',
                    pitch: 78,// 语调
                    volume: 100, // 音量*!/
          mediaInfo: {},
          schedules: [],
        }
      }
    },*/
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.dataForm = {
          id: 0,
          name: '',
          timing: 0,// 默认无定时
          type: 'ipColumnService',
          // totalSize: 0,
          // programPlaysFlag: 'true',
          programPlaysNum: 1,
          programPlays: "cycle",// 默认无限循环
          programs: [],
          // schedules: []
          infoId: 0
        }
      }
    },
    // taskVisible:{
    //   handler(){
    //     this.taskDataForm = {
    //       id: '',
    //       name: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
    //       materialType: 'text',
    //       md5: '',
    //       plays: 1,
    //       // playTime: 1,
    //       size: 0,
    //       text: '',
    //       playWay: 'plays',
    //       speed: "1",// 语速
    //       mediaInfo: {},
    //       schedules: [],
    //   }
    //   }
    // }
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },
  components: {
    textTemplate
  }
}
</script>
