<template>
   <div class="modiles-task">
     <Tabs type="card" value="my" v-if="userInfo.isExamineUser" @on-click="tabsClick">
        <TabPane :label="$t('screen.my')" name="my">
          <task-query-and-table ref="taskQueryAndTable"></task-query-and-table>
        </TabPane>
        <TabPane :label="$t('screen.pending')" name="todo">
          <task-query-and-table-todo ref="taskQueryAndTableTodo"></task-query-and-table-todo>
        </TabPane>
    </Tabs>
    <div v-else>
      <task-query-and-table ref="taskQueryAndTable"></task-query-and-table>
    </div>
   </div>
</template>

<script>
import taskQueryAndTable from './task-query-and-table.vue'
import taskQueryAndTableTodo from './task-query-and-table.vue'
export default {
  data () {
    return {
      istoDo: false
    }
  },
  activated () {
    this.getDataList()
  },
  components: {
    taskQueryAndTable,
    taskQueryAndTableTodo
  },
  methods: {
    tabsClick (name) {
      if (name === 'todo') {
        this.istoDo = true
        this.getDataListTodo()
      } else {
        this.istoDo = false
        this.getDataList()
      }
    },
    // 获取数据列表
    getDataList () {
      this.$nextTick(() => {
        this.$refs.taskQueryAndTable.init(this.istoDo)
      })
    },
    // 获取数据列表
    getDataListTodo () {
      this.$nextTick(() => {
        this.$refs.taskQueryAndTableTodo.init(this.istoDo)
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>

<style scoped>
</style>
