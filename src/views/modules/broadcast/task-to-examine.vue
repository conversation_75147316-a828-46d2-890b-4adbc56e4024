<template>
  <Modal v-model="visible" width="500">
    <p slot="header" style="text-align:center">
      <span v-if="id.length>1">{{$t('approval.batchReview')}}</span>
      <span v-else>{{$t('file.examine')}}</span>
    </p>
    <Form ref="dataForm" :model="dataForm" :rules="dataRule" style="height: 300px;" :label-width="3" label-position="left"
      @keyup.enter.native="dataFormSubmit()">
        <FormItem prop="status">
            <Select size="large" v-model="dataForm.status" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('common.state')">
                <Option v-for="item in fileStatus" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
            </Select>
        </FormItem>
        <FormItem v-if="dataForm.status===2">
            <Input v-model="dataForm.memo" type="textarea" :autosize="{minRows: 3,maxRows: 5}" :maxlength="100" :placeholder="$t('common.PleaseInput') + $t('file.ApprovalComments')"></Input>
        </FormItem>
    </Form>
    <div slot="footer">
        <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      id: [],
      userId: '',
      loading: false,
      dataForm: {
        status: '',
        memo: this.$t('approval.auditMemo')
      },
      fileStatus: [
        {value: 2, label: 'file.auditFailed'},
        {value: 3, label: 'file.approved'}
      ]
    }
  },
  computed: {
    dataRule: {
      get () {
        return {
            status: [
            { validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error(this.$t('validate.status_cannot_be_empty')))
              } else {
                callback()
              }
            }, trigger: 'change' }
          ]
        }
      }
    }
  },
  methods: {
    // 初始化
    init (userId, id) {
      this.visible = true
      this.id = id
      this.userId = userId
      this.dataForm= {
        status: '',
        memo: this.$t('approval.auditMemo')
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.dataForm.status===3){
            this.dataForm.memo=''
          }
          this.$http({
            url: this.$http.adornUrl(`/broadcast/task/toExamine`),
            method: 'post',
            data: this.$http.adornData({
              'correlationId': this.id,
              'userId': this.userId,
              'status': this.dataForm.status,
              'memo': this.dataForm.memo
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.loading = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshData')
                }
              })
            } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                this.loading = false
              }, 500)
            }
          })
        }
      })
    }
  }
}
</script>
