<template>
    <Modal v-model="visible" width="900" footer-hide>
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('common.programmeDetails')}}</span>
        </p>
        <div style="height: 300px;overflow: hidden;overflow-y: auto;">
            <div v-if="program && program._program">
                <ul style="margin-bottom: 10px">
                    <li style="float: left;">
                        <b style="margin-right: 3px;margin-left: 10px">{{$t('program.name')}}：</b><span><a href="javascript:void(0)" @click="viewInfo(program._program.name)">{{program._program.name}}</a></span></li>
                    <li style="float: left;"><b style="margin-right: 3px;margin-left: 10px">{{$t('common.showWidth')}}：</b><span>{{program._program.width}}</span></li>
                    <li><b style="margin-right: 3px;margin-left: 10px">{{$t('common.showHigh')}}：</b><span>{{program._program.height}}</span></li>
                </ul>
                <div v-if="program.schedules">
                    <Table :columns="dataColumns" :data="program.schedules" width="750"></Table>
                </div>
                <div v-else-if="!program.schedules && !program">{{$t('card.noTiming')}}</div>
            </div>
            <div v-else>{{$t('common.notOnPlatform')}}</div>
        </div>
    </Modal>
</template>

<script>
import program1 from '../../program/programModular/program.js'
export default {
  data () {
    return {
        visible: false,
        program: [],
        dataColumns: [
            {
                title: this.$t('card.DateRange'), // 日期范围
                key: 'dateType',
                align: 'center',
                width: '200',
                render: (h, {row, index}) => {
                    var result = ''
                    if (row.dateType === 'Range') {
                    result = [
                        h('span', {}, row.startDate),
                        h('span', {}, ' -- '),
                        h('span', {}, row.endDate)
                    ]
                    } else {
                    result = h('span', {}, this.$t('card.notSpecified'))
                    }
                    return result
                },
                renderHeader:(h)=>{
                    return h('div',this.$t('card.DateRange'))
                }
            },
            {
                title: this.$t('card.timeFrame'), // 时间范围
                key: 'timeType',
                align: 'center',
                width: '200',
                render: (h, {row, index}) => {
                    var result = ''
                    if (row.timeType === 'Range') {
                    result = [
                        h('span', {}, row.startTime),
                        h('span', {}, ' -- '),
                        h('span', {}, row.endTime)
                    ]
                    } else {
                    result = h('span', {}, this.$t('card.notSpecified'))
                    }
                    return result
                },
                renderHeader:(h)=>{
                    return h('div',this.$t('card.timeFrame'))
                }
            },
            {
                title: this.$t('card.SpecifyWeek'), // 星期范围
                key: 'filterType',
                align: 'center',
                width: '350',
                tooltip: true,
                render: (h, {row, index}) => {
                    var result = ''
                    if (row.filterType === 'Week') {
                    result = [
                        h('span', {}, row.week + ',')
                    ]
                    } else {
                    result = h('span', {}, this.$t('card.notSpecified'))
                    }
                    return result
                },
                renderHeader:(h)=>{
                    return h('div',this.$t('card.SpecifyWeek'))
                }
            }
        ],
    }
  },
  methods: {
    // 初始化
    init (name) {
        this.visible = true
        this.$http({
            url: this.$http.adornUrl(`/screen/program/programInfo/${name}`),
            method: 'get',
            data: this.$http.adornData()
        }).then(({data}) => {
            if (data && data.code === 0) {
                this.program = data.program
            } else {
                this.program = []
            }
        })
    },
    viewInfo(name) {
        this.$http({
            url: this.$http.adornUrl(`/screen/program/info/${this.program._program.programId}`),
            method: 'get',
            data: this.$http.adornData()
        }).then(({data}) => {
            if (data && data.code === 0) {
                this.$router.replace({ name: 'standard', params: {backRouter: 'screen-device'} })
                program1.modifyAdvertisement(data.program, true)
            } else {
                this.$Message.error(data.msg)
            }
        })
        // this.$Message.warning(this.$t('common.supportedTip'));
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.program = []
      }
    },
    program1:{ //监听的对象  this.programData指向program
        handler(){
          this.programData=program1
        },
      immediate:true,
    }
  }
}
</script>