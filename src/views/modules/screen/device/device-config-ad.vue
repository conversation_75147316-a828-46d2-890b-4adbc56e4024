<template>
<Modal width="500" v-model="visible" >
  <p slot="header" style="text-align:center">
    {{$t('operation.ThirdPartyAdvertising')}}
  </p>
  <div style="height: 100px">
    <Alert type="warning" show-icon>{{$t('tips.configAdTip')}}<br>{{$t('tips.configAdTip3')}}</Alert>
   <span>
    {{$t('tips.configAdTip1')}}
  </span>
    <i-switch size="large" v-model="enableAdConfig" @on-change="change">
      <span slot="open">ON</span>
      <span slot="close">OFF</span>
    </i-switch>
  </div>
  <!-- <div>
    <span>点击查看</span>
    <a style="font-weight: bold" href="javascript:void(0)" @click="handleAgreement">《风险须知》</a>
  </div> -->
  <Modal width="300" v-model="modalVisible" :closable="false" :mask-closable="false">
    <p slot="header" style="text-align:center">
    </p>
    <div style="text-align:center">
      <p> {{$t('tips.configAdTip2')}}</p>
    </div>
    <div slot="footer">
      <Button type="primary"  @click="enableAd(1)">{{ $t('common.confirm') }}</Button>
      <Button type="error"   @click="disableAd">{{ $t('common.cancel') }}</Button>
    </div>
  </Modal>
  <div slot="footer" style="text-align: left;">
        <span>
            <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
    <div style="overflow-y: auto;max-height:42px;">
      <Breadcrumb>
        <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
      </Breadcrumb>
    </div>
  </div>
</Modal>
</template>

<script>

export default {
  name: "device-config-ad",
  data () {
    return {
      visible: false,
      ids: [],
      //开关开启状态
      enableAdConfig:false,
      //二次确认对话框
      modalVisible:false,
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.enableAdConfig=false
        this.modalVisible=false
      }
    },
    //打开、关闭开关
    change (status) {
      if (status===true){
        this.modalVisible=true
      }else {
        this.enableAd(0)
      }
    },
    //确定开启广告
    enableAd(status){
      this.$http({
        url: this.$http.adornUrl('/lampPole/card/enableTpa/'+ status),
        method: 'post',
        data: this.ids
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.modalVisible = false
          this.$Message.success(this.$t('setTime.setupSuccess'))
        } else {
          this.$Message.error(data.msg)
        }
      })

    },
    //关闭弹窗
    disableAd(){
      this.modalVisible=false
      this.enableAdConfig=false
    },
    // 跳转到风险须知页面
    handleAgreement () {
      let routeUrl = this.$router.resolve({name: 'riskReminder'})
      window.open(routeUrl.href, '_blank');
    }
  },
}
</script>

<style scoped>

</style>
