<template>
  <div  class="cell-player">
    <div v-if="nameAndUrl.length !== 0" :class="getClassName(index)" v-for="(item,i) in nameAndUrl" :key="i">
        <span style="display: block;position: absolute;font-size: 20px;color: #ff6600;z-index: 1;">{{item.device_id}}</span>
      <div :id="'videoElement'+i" style="position: relative"></div>
    </div>
    <div v-if="nameAndUrl.length===0" class="msgVideoText">{{ $t('home.temporarilyNoData') }}</div>
  </div>
</template>
<script>
import Player from 'xgplayer'
import FlvPlugin from 'xgplayer-flv'
// import "xgplayer/dist/index.min.css"
import {generateToken} from '@/utils/jwtUtils'
export default {
  data () {
    return {
      //卡号
      ids: [],
      //地址数组
      addressArr:[],
      //播放地址的名字
      names:[],
      lastDecodedFrame: 0,
      //传入的地址数
      index:0,
      //包含所有的卡号、推流地址、推流名
      nameAndUrl:[],
      //西瓜视频国际化
      xgLang:'zh-cn',

    }
  },

  methods: {
    // 初始化
    init () {
      this.visible = true
      this.ids=JSON.parse(this.$route.query.deviceIds || '[]')
      this.getMonitorAddress(this.ids)
    },
    getClassName(val){
      switch (val) {
        case 1:
          return ['cell-player-1']
        case 2:
        case 3:
        case 4:
          return ['cell-player-4']
        case 5:
        case 6:
          return ['cell-player-6']
        case 9:
          return ['cell-player-9']
        case 16:
          return ['cell-player-16']
        default:
          break;
      }
    },

    getMonitorAddress(val){
      this.$http({
        url:this.$http.adornUrl('/screen/card/getMonitorAddress'),
        method: 'post',
        data:this.$http.adornData(val, false)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.nameAndUrl = data.data
          this.index=this.nameAndUrl.length
          if (this.index>0){
            this.$nextTick(() => {
              for (let i = 0; i < this.index; i++)
              {
                var name= this.nameAndUrl[i].name
                var url=this.nameAndUrl[i].monitor_address
                this.addressArr.push(url)
                this.names.push(name)
                try {
                  this.playXGFLV(window.SITE_CONFIG.rtmp+name +"&token="+ generateToken(name), i)
                } catch (error) {
                    throw new Error('Error in catch block: ' + error);
                }
              }
            })
          }
        } else {
          this.$Message.error(data.msg)
        }
      })
    },
    playXGFLV(address,num){
      let lang=localStorage.getItem('language');
      console.log(lang)
      if (lang=='us'){
        this.xgLang='en'
      }else {
        this.xgLang='zh-cn'
      }
      let _this=this
      new Player({
        id: 'videoElement'+num,
        url: address,
        fluid: true, //流式布局
        isLive: true,
        autoplay: true, //自动播放
        autoplayMuted: true, //自动播放静音，设置自动播放参数必要参数
        plugins: [FlvPlugin],  // 以xgplayer的插件形式传入并挂载
        lang:_this.xgLang, //国际化
        volume:0,
        preloadTime: 10,
        minCachedTime: 5,
        cors: true,
        screenShot: {
          saveImg: true,
          quality: 0.92,
          type: 'image/png',
          format: '.png'
        },
      })
    },
    /**
     * 在关闭/刷新页面前发送的请求，如果使用axios方式，由于是异步的，在执行刷新/关闭操作时，存在请求还未发送到服务端就被浏览器cancel掉。
     * Fetch API提供了一套健壮的与服务器端交互的方式，提供了跨越不同平台 API 的一致接口。它提供了一个keepalive属性，保证不管发送请求的页面关闭与否，请求都会持续直到结束。
     * 不过上传数据的限制是64 KB。
     */
    closeFfmpeg(){
      let url=this.$http.adornUrl('/screen/card/programClose');
      fetch( url, {
        method: 'post',
        headers:{
          'Accept': 'application/json',
          'Content-Type': 'application/json; charset=UTF-8',
          'token': this.$cookie.get("token") // getToken 返回 token
        },
        body: this.$http.adornData(this.addressArr, false),
        keepalive: true,
      });
    }

  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight
      },
    }
  },
  created() {
    this.init()
    window.addEventListener('beforeunload', () => this.closeFfmpeg())
  },
  destroyed() {
    // 关闭浏览器执行退出接口
    window.removeEventListener('beforeunload', () => this.closeFfmpeg())
  },
}
</script>

<style scoped>
.cell-player {
  height: 100%;
  width: 100%;
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.cell-player-1 {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}
.cell-player-2 {
  height: 100%;
  width: 50%;
  justify-content: space-between;
}
.cell-player-4 {
  display: flex;

  width: 50% !important;
  box-sizing: border-box;
  justify-content: space-between;
}
.cell-player-6 {
  width: 33.33%;
  height: 50% !important;
  box-sizing: border-box;
}

.cell {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.select {
  background-color: rgb(204, 204, 204);
}

.player {
  background-color: black;
  height: 100%;
  width: 100%;
  border: 1px solid white;
  color: white;
  text-align: center;
}
.videoText {
  color: red;
}
.msgVideoText {
  width: 100%;
  margin-top: 30px;
  color: #ff0000;
  font-size: 20px;
  font-weight: 500;
  text-align: center;
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
</style>
