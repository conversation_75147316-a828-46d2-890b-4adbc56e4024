<template>
  <Modal width="900" v-model="visible">
    <p slot="header" style="text-align:center">
      {{ $t('operation.curFlow') }}
    </p>
    <div>
<!--      <Alert type="info" show-icon>{{ $t('operation.curFlowTip') }}</Alert>-->
      <Form ref="curFlow" :model="curFlowForm" :label-width="150">
        <FormItem :label="$t('operation.isEnableCurFlow')" prop="isOpen">
          <i-switch v-model="curFlowForm.isOpen" ></i-switch>
        </FormItem>
        <div v-if="curFlowForm.isOpen">
          <FormItem :label="$t('common.selectDevice')" prop="cameraList">
            <Select v-model="curFlowForm.url" style="width:200px">
              <Option v-for="item in cameraList" :value="item" :key="item">{{ item }}</Option>
            </Select>
          </FormItem>
          <FormItem :label="$t('program.fontColor')" prop="fontColor">
            <el-color-picker v-model="curFlowForm.fontColor"></el-color-picker>
          </FormItem>
          <FormItem :label="$t('program.fontSize')" prop="fontSize">
            <InputNumber v-model="curFlowForm.fontSize" :min="1" :max="500" ></InputNumber>
          </FormItem>
          <FormItem :label="$t('program.refreshCycle')" prop="refreshTime">
            <InputNumber v-model="curFlowForm.refreshTime" :min="1" ></InputNumber><span>  {{$t('program.minute')}}</span>
          </FormItem>
          <FormItem :label="$t('operation.showLocation')" prop="orientation" >
            <Select v-model="curFlowForm.orientation" style="width:100px">
              <Option value="leftTop">{{ $t('operation.leftTop') }}</Option>
              <Option value="rightTop">{{ $t('operation.rightTop') }}</Option>
              <Option value="leftBottom">{{ $t('operation.leftBottom') }}</Option>
              <Option value="rightBottom">{{ $t('operation.rightBottom') }}</Option>
            </Select>
          </FormItem>
          <FormItem :label="$t('operation.showPrefix')" prop="flowText">
            <Input v-model="curFlowForm.flowText"  style="width: 300px"></Input>
          </FormItem>
        </div>

        <FormItem>
        </FormItem>
      </Form>
      <div style="float: right">
        <Button @click="enableCurFlow" type="primary" :loading="setLoading">{{ $t('common.set') }}</Button>
      </div>
      <div style="height: 300px">
        <div style="height:240px;overflow-y: auto;" v-if="resultData.length > 0">
          <cardResult :ids="curFlowForm.deviceIds" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
      </div>
    </div>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in curFlowForm.deviceIds" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  name: "device-current-flow",
  data() {
    return {
      visible: false,
      ids: [],
      cameraList:[],
      curFlowForm:{
        deviceIds: [],
        url:'',
        fontColor: '#ffffff',
        fontSize: 16,
        refreshTime: 1,
        orientation:'leftTop',
        flowText:this.$t('operation.curFlow')+":",
        isOpen: false,
      },

      //结果集
      resultData:[],
      setLoading:false,

    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.resultData=[]
        this.cameraList=[]
        this.setLoading=false
        this.curFlowForm={
          deviceIds:ids,
          url:'',
          fontColor: '#ffffff',
          fontSize: 16,
          refreshTime: 1,
          orientation:'leftTop',
          flowText:this.$t('operation.curFlow')+":",
          isOpen: false,
        }
        this.queryIntelligentCameraList()

      }
    },

    // 查询摄像头id
    queryIntelligentCameraList(){
      this.$http({
        url: this.$http.adornUrl(`/card/query/intelligentCameraList`),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.cameraList=data.data
        } else {
          this.$Message.error(data.msg)
        }
      })
    },


    //确定开启人流量
    enableCurFlow() {
      if (this.curFlowForm.isOpen&&this.curFlowForm.url===''){
        return this.$Message.error(this.$t('common.selectDevice'))
      }
      this.resultData=[]
          if (this.curFlowForm.deviceIds.length>0){
            this.setLoading = true
            this.$http({
              url: this.$http.adornUrl('/card/set/curFlow'),
              method: 'post',
              data: this.curFlowForm
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.resultData = data.data
              } else {
                this.$Message.error(data.msg)
              }
              this.setLoading = false
            })
          }
    },

  },
}
</script>

<style scoped>

</style>
