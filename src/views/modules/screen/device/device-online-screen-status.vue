<template>
  <div>
    <Modal v-model="visible" width="900">
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{$t('operation.oneClickOperateScreen')}}</span>
      </p>
      <Form :label-width="30" label-position="left">
        <Alert type="warning" show-icon ><b class="tip">{{ $t('tips.oneClickOperateScreenTip')}}</b></Alert>
        <FormItem>
<!--          <Button type="success" :loading="on_loading" @click="screenSwitchSubmit(true)">{{$t('program.open')}}</Button>-->
          <Button type="warning" :loading="off_loading" @click="screenSwitchSubmit()">{{$t('program.close')}}</Button>
        </FormItem>
      </Form>
      <div slot="footer" style="text-align: left;">
      </div>
    </Modal>

  </div>
</template>
<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  data () {
    return {
      visible: false,
      on_loading: false,
      off_loading: false,
      dataList: [],
    }
  },
  components: {
    cardResult,
  },
  methods: {

    // 初始化
    init () {
      this.visible = true
      this.on_loading = false
      this.off_loading = false
    },
    // 提交数据
    screenSwitchSubmit () {
        this.off_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/onlineScreenStatus'),
          method: 'post',
          data: this.$http.adornData()
        }).then(({data}) => {
          console.log(data)
          if (data && data.code == 0) {
            this.$Message.success(data.msg);
          } else {
            this.$Message.error(data.msg);
          }
            this.off_loading = false
        })
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.$emit('refreshDataList')
      }
    }
  },
}
</script>
<style scoped>
</style>
