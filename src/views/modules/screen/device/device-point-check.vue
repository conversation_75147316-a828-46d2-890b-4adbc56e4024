<template>
  <Modal v-model="visible" fullscreen :mask-closable="false">
    <p slot="header" style="text-align:center">
      <span>{{ $t('operation.BadPointDetection') }}</span>
    </p>
    <Tabs :value="tabsName" @on-click="handlerTab">
      <TabPane :label="$t('operation.BadPointDetection')" name="name1">
        <Alert type="warning" show-icon>
          {{ $t('tips.pointCheckCard') }}
          <br>
          {{ $t('tips.pointCheckTip') }}
        </Alert>
        <div>
          <span>{{ $t('common.PleaseSelect') }}{{ $t('screen.networkPort') }}：</span>
          <Select v-model="port" style="width:200px">
            <Option v-for="item in netInterface" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <el-button type="primary" @click="startCheck" size="small" :loading="checkLoading"  v-loading.fullscreen.lock="fullscreenLoading" :element-loading-text="$t('operation.detectingBadPixels')"> {{ $t('screen.clickToCheck') }}</el-button>
          <el-button type="primary"  :disabled="isCheck" @click="isHaveTable" size="small" :loading="infoLoading"> {{ $t('screen.getPointCheckInfo') }}</el-button>
          <!--          <Button type="primary" :loading="checkLoading" @click="startCheck()">{{ $t('screen.clickToCheck') }}</Button>-->
          <!--          <Button type="primary" :disabled="isCheck" :loading="infoLoading" @click="isHaveTable()"> {{ $t('screen.getPointCheckInfo') }} </Button>-->
        </div>
        <!--      <div v-if="boxVisible"  :style="{width: boxWidth + 'px',height:boxHeight+'px',backgroundColor:color}"></div>-->
        <Row style="margin-top: 10px" v-show="!isRGB">
          <Col span="5">
            <div style="overflow: auto;height: 858px;" v-if="tableVisible">
              <span style="font-weight: bold;">{{ $t('screen.badPointPosition') }}</span>
              <div v-if="redPosition && redPosition.length > 0">
                <ul  v-for="(item,index) in redPosition" :key="index">
                  <li> <span style="font-size:15px;">{{ $t('screen.abscissa') }}：{{ item.x }} {{ $t('screen.ordinate') }}：{{ item.y }}</span></li>
                </ul>
              </div>
              <div v-else>
                <span>{{ $t('screen.noBadPoint')}}</span>
              </div>
            </div>
          </Col>
          <Col span="19">
            <div v-if="boxVisible" style="margin-bottom: 10px;font-weight: bold;">
              <span>
                {{ $t('screen.imageSize') }}：
                {{ boxWidth }}*{{ boxHeight }}; {{ $t('screen.badPointNum') }}：<span style="color: red;">{{ redPosition.length }}</span>
              </span>
            </div>
            <div style="overflow: auto;height: 858px">
              <canvas id="singleCanvas"></canvas>
            </div>
          </Col>
        </Row>
        <Row style="margin-top: 20px" v-show="isRGB">
          <Col span="5">
            <div style="height: 858px;" v-if="tableVisible">
              <div style="overflow: auto;height: 286px;">
                <span style="font-weight: bold;">{{ $t('screen.redBadPointPosition') }}</span>
                <div v-if="redPosition && redPosition.length > 0">
                  <ul  v-for="(item,index) in redPosition" :key="index">
                    <li> <span style="font-size:15px;">{{ $t('screen.abscissa') }}：{{ item.x }} {{ $t('screen.ordinate') }}：{{ item.y }}</span></li>
                  </ul>
                </div>
                <div v-else>
                  <span>{{ $t('screen.noBadPoint')}}</span>
                </div>
              </div>
              <div style="overflow: auto;height: 286px;">
                <span style="font-weight: bold;">{{ $t('screen.greenBadPointPosition') }}</span>
                <div v-if="greenPosition && greenPosition.length > 0">
                  <ul  v-for="(item,index) in greenPosition" :key="index">
                    <li> <span style="font-size:15px;">{{ $t('screen.abscissa') }}：{{ item.x }} {{ $t('screen.ordinate') }}：{{ item.y }}</span></li>
                  </ul>
                </div>
                <div v-else>
                  <span>{{ $t('screen.noBadPoint')}}</span>
                </div>
              </div>
              <div style="overflow: auto;height: 286px;">
                <span style="font-weight: bold;">{{ $t('screen.blueBadPointPosition') }}</span>
                <div v-if="bluePosition && bluePosition.length > 0">
                  <ul  v-for="(item,index) in bluePosition" :key="index">
                    <li> <span style="font-size:15px;">{{ $t('screen.abscissa') }}：{{ item.x }} {{ $t('screen.ordinate') }}：{{ item.y }}</span></li>
                  </ul>
                </div>
                <div v-else>
                  <span>{{ $t('screen.noBadPoint')}}</span>
                </div>
              </div>
            </div>
          </Col>
          <Col span="19">
            <div v-if="boxVisible" style="margin-bottom: 10px;font-weight: bold;">
              <span> {{ $t('screen.imageSize') }}: {{ boxWidth }}*{{ boxHeight }};  </span>
              <span> {{ $t('screen.totalNumberOfBadPoints') }}：<span style="color: red;">{{ redPosition.length + greenPosition.length + bluePosition.length }} </span>;</span>
              <span> {{ $t('screen.redBeadNumberPoints') }}：<span style="color: red;">{{ redPosition.length }}  </span>;</span>
              <span> {{ $t('screen.greenBeadNumberPoints') }}：<span style="color: red;">{{ greenPosition.length }}  </span>;</span>
              <span> {{ $t('screen.blueBeadNumberPoints') }}：<span style="color: red;">{{ bluePosition.length }}  </span></span>
              <i-Switch size="large" v-model="isShowRGB" @on-change="showRGBHandler">
                  <template #open>
                    <span>{{ $t('screen.fullColor') }}</span>
                  </template>
                  <template #close>
                    <span>{{ $t('screen.monochrome') }}</span>
                  </template>
              </i-Switch>
            </div>
            <div v-show="!isShowRGB">
              <div style="overflow: auto;height: 286px">
                <canvas id="redCanvas"></canvas>
              </div>
              <div style="overflow: auto;height: 286px">
                <canvas id="greenCanvas"></canvas>
              </div>
              <div style="overflow: auto;height: 286px">
                <canvas id="blueCanvas"></canvas>
              </div>
            </div>
            <div v-show="isShowRGB">
              <div style="overflow: auto;height: 858px">
                <canvas id="RGBCanvas"></canvas>
              </div>
            </div>
          </Col>
        </Row>

      </TabPane>
      <TabPane :label="$t('screen.pointTable')"  name="name2">
        <Alert type="info" show-icon>{{ $t('screen.pointTableTips') }}</Alert>
<!--        <Row>-->
<!--          <Col span="5">-->
            <Input v-model="fileName" :placeholder="$t('common.PleaseInput')+$t('file.name')" style="width: 300px" />
<!--          </Col>-->
<!--          <Col span="2">-->
            <Button type="success" icon="ios-search-outline" @click="queryPointTable()">{{$t('common.query')}}</Button>
<!--          </Col>-->
<!--          <Col span="4">-->
            <Button type="primary" icon="ios-cloud-upload-outline"  @click="uploadPointTable()">{{ $t("file.upload") }}{{ $t('screen.pointTable') }}</Button>
            <device-point-check-table-add v-if="uploadPointTableVisible" @uploadPointTable="handlerTab" ref="uploadPointTable"></device-point-check-table-add>
<!--          </Col>-->
<!--          <Col span="10">-->
<!--          </Col>-->
<!--          <Col span="3">-->
            <Button type="primary"  style="float: right" icon="ios-cloud-download">
              <a style="color:white;" target="_blank" :href="downloadUrl">{{ $t('file.download') }}{{ $t('screen.pointTableTemplate') }}</a>
            </Button>
<!--          </Col>-->
<!--        </Row>/-->
        <div style="height:10px;"></div>
          <Table stripe :columns="dataColumns" :data="pageLists" :max-height="tableHeight" :loading="dataListLoading">
            <template slot-scope="{ row }" slot="fileSize">
              <span>{{ row.fileSize | filterType }}</span>
            </template>
            <template slot-scope="{ row, index }" slot="operation">
              <Button type="primary" size="small" style="margin-right: 5px" :loading="setPointTableLoading" @click="setPointCheckTable(row.fileId)">{{$t('common.set')}}</Button>
              <Button type="error" size="small" style="margin-left: 5px" @click="toDelete(row)">{{ $t("common.delete") }}</Button>
            </template>
          </Table>
        <Row>
          <Col span="24">
            <Page style="float: right" :total="page.totalCount" :current="page.currPage" :page-size="page.pageSize"
                  show-elevator show-sizer :page-size-opts="[5]" show-total @on-change="currentChangeHandle"
                  @on-page-size-change="sizeChangeHandle"/>
          </Col>
        </Row>
      </TabPane>
      <TabPane :label="$t('screen.receivingCard')" name="name3">
        <div>
          <i-Switch size="large" v-model="relaySwitch">
              <template #open>
                <span>{{$t('program.open')}}</span>
              </template>
              <template #close>
                <span>{{$t('program.close')}}</span>
              </template>
          </i-Switch>
          <Button type="warning" :loading="modal_switch_loading" @click="setRecCardRelaySwitch()"> {{ $t('screen.setRecCardRelaySwitch') }} </Button>
          <Button type="primary" :loading="modal_recCard_loading" @click="getRecCardInfo()"> {{ $t('screen.getRecCardSensorData') }} </Button>
        </div>
        <div v-if="switchResult.length > 0" style="height: 100%;overflow-y: hidden">
          <cardResult :ids="[deviceId]" :resultData="switchResult" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
        <div v-if="recCard && recCard.length > 0" v-for="(item, index) in recCard" :key="index">
          <div v-if="item._type === undefined || item._type !== 'success'">
            <div style="background-color: #f0ad4e;padding-left: 5px" >
              <span v-if="item.deviceId">{{item.deviceId}}</span>
              <span v-else-if="item.cardId">{{item.cardId}}</span>
              <Icon type="md-close"/>
            </div>
            <div style="border: 1px solid #ccc;height: 50px;padding: 5px">
              <div style="color: red;" v-if="item.msg">{{item.msg}}</div>
              <div style="color: red;" v-else-if="item.errorMessage">{{item.errorMessage}}</div>
            </div>
          </div>
          <div v-else>
            <div style="margin-top: 5px; background-color: #5cb85c;padding-left: 5px">
              <span v-if="item.deviceId">{{item.deviceId}}</span>
              <span v-else-if="item.cardId">{{item.cardId}}</span>
              <Icon type="md-checkmark"/>
            </div>
            <div v-if="item.cardInfoList && item.cardInfoList.length > 0">
              <div v-for="(cardInfo, i) in item.cardInfoList" :key="i">
                <div style="border: 1px solid #ccc;height: 380px;padding: 5px" v-if="cardInfo.isShow != undefined && cardInfo.isShow == 1 || cardInfo.isShow == '1'">
                  <Row style="padding: 5px">
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#temperature"></use>
                      </svg>
                      {{$t('card.temperature')}}： {{cardInfo.temperature}} ℃
                    </Col>
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#voltage"></use>
                      </svg>
                      {{$t('electricity.voltage')}}： {{cardInfo.voltage}} V
                    </Col>
                  </Row>
                </div>
                <div style="border: 1px solid #ccc;height: 380px;padding: 5px" v-else>
                  <Row style="padding: 5px">
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#temperature"></use>
                      </svg>
                      {{$t('card.temperature')}}： {{cardInfo.temperature}} ℃
                    </Col>
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#humidity"></use>
                      </svg>
                      {{$t('card.humidity')}}： {{cardInfo.humidity}} ％
                    </Col>
                  </Row>
                  <Row style="padding: 5px">
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#voltage"></use>
                      </svg>
                      {{$t('electricity.voltage')}}： {{cardInfo.voltage}} V
                    </Col>
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use xlink:href="#light"></use>
                      </svg>
                      {{$t('operation.autoBrightness')}}： {{cardInfo.light}}
                    </Col>
                  </Row>
                  <Divider />
                  <Row style="padding: 5px">
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.smoke > 1000 ? '#isSmoke_true' : '#isSmoke_false'"></use>
                      </svg>
                      {{$t('screen.smoke')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.smoke > 1000 ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                      {{cardInfo.smoke > 1000 ? $t('screen.smoke') : $t('screen.smokeless')}}
                    </Col>
                    <Col span="12">
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh6 == '1' ? '#isOpen_true' : '#isOpen_false'"></use>
                      </svg>
                      {{$t('screen.openCloseDoor')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh6 == '1' ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                      {{cardInfo.isHigh6 == '1' ? $t('screen.openDoor') : $t('screen.closeDoor')}}
                    </Col>
                  </Row>
                  <Row style="padding: 5px">
                    <Col span="12">
                      <!-- byte类型的0xA5 == 165 -->
                      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.switchState == 165 ? '#relaySwitch_true' : '#relaySwitch_false'"></use>
                      </svg>
                      {{$t('screen.relaySwitch')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.switchState == 165 ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                      {{cardInfo.switchState == 165 ? $t('program.open') : $t('program.close')}}
                    </Col>
                    <Col span="12">
                    </Col>
                  </Row>
                  <Divider orientation="left">{{$t('screen.levelDetection')}}</Divider>
                  <Alert>
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#green_circle"></use>
                    </svg>
                    {{$t('screen.accessHighLevel')}}
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#red_circle"></use>
                    </svg>
                    {{$t('screen.noDeviceConnected')}}
                  </Alert>
                  <Row style="padding: 5px">
                    <Col span="12">
                      {{$t('screen.firstRoad')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh1 == '1' ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                    </Col>
                    <Col span="12">
                      {{$t('screen.secondWay')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh2 == '1' ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                    </Col>
                  </Row>
                  <Row style="padding: 5px">
                    <Col span="12">
                      {{$t('screen.thirdWay')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh3 == '1' ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                    </Col>
                    <Col span="12">
                      {{$t('screen.fourthWay')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh4 == '1' ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                    </Col>
                  </Row>
                  <Row style="padding: 5px">
                    <Col span="12">
                      {{$t('screen.theFifthRoad')}}：
                      <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="cardInfo.isHigh5 == '1' ? '#green_circle' : '#red_circle'"></use>
                      </svg>
                    </Col>
                    <Col span="12">
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
            <div v-else>
              <div style="border: 1px solid #ccc;height: 380px;padding: 5px" v-if="item.isShow != undefined && item.isShow == false || item.isShow == 'false'">
                <Row style="padding: 5px">
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#temperature"></use>
                    </svg>
                    {{$t('card.temperature')}}： {{item.temperature}} ℃
                  </Col>
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#voltage"></use>
                    </svg>
                    {{$t('electricity.voltage')}}： {{item.voltage}} V
                  </Col>
                </Row>
              </div>
              <div style="border: 1px solid #ccc;height: 380px;padding: 5px" v-else>
                <Row style="padding: 5px">
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#temperature"></use>
                    </svg>
                    {{$t('card.temperature')}}： {{item.temperature}} ℃
                  </Col>
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#humidity"></use>
                    </svg>
                    {{$t('card.humidity')}}： {{item.humidity}} ％
                  </Col>
                </Row>
                <Row style="padding: 5px">
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#voltage"></use>
                    </svg>
                    {{$t('electricity.voltage')}}： {{item.voltage}} V
                  </Col>
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use xlink:href="#light"></use>
                    </svg>
                    {{$t('operation.autoBrightness')}}： {{item.light}}
                  </Col>
                </Row>
                <Divider />
                <Row style="padding: 5px">
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.isSmoke ? '#isSmoke_true' : '#isSmoke_false'"></use>
                    </svg>
                    {{$t('screen.smoke')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="!item.isSmoke ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                    {{!item.isSmoke ? $t('screen.smokeless') : $t('screen.smoke')}}
                  </Col>
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.isOpen ? '#isOpen_true' : '#isOpen_false'"></use>
                    </svg>
                    {{$t('screen.openCloseDoor')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="!item.isOpen ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                    {{!item.isOpen ? $t('screen.closeDoor') : $t('screen.openDoor')}}
                  </Col>
                </Row>
                <Row style="padding: 5px">
                  <Col span="12">
                    <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.relaySwitch ? '#relaySwitch_true' : '#relaySwitch_false'"></use>
                    </svg>
                    {{$t('screen.relaySwitch')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.relaySwitch ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                    {{item.relaySwitch ? $t('program.open') : $t('program.close')}}
                  </Col>
                  <Col span="12">
                  </Col>
                </Row>
                <Divider orientation="left">{{$t('screen.levelDetection')}}</Divider>
                <Alert>
                  <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                    <use xlink:href="#green_circle"></use>
                  </svg>
                  {{$t('screen.accessHighLevel')}}
                  <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                    <use xlink:href="#red_circle"></use>
                  </svg>
                  {{$t('screen.noDeviceConnected')}}
                </Alert>
                <Row style="padding: 5px">
                  <Col span="12">
                    {{$t('screen.firstRoad')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.power1 ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                  </Col>
                  <Col span="12">
                    {{$t('screen.secondWay')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.power2 ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                  </Col>
                </Row>
                <Row style="padding: 5px">
                  <Col span="12">
                    {{$t('screen.thirdWay')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.power3 ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                  </Col>
                  <Col span="12">
                    {{$t('screen.fourthWay')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.power4 ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                  </Col>
                </Row>
                <Row style="padding: 5px">
                  <Col span="12">
                    {{$t('screen.theFifthRoad')}}：
                    <svg width="15px" height="15px" aria-hidden="true" style="vertical-align: middle;">
                      <use :xlink:href="item.power5 ? '#green_circle' : '#red_circle'"></use>
                    </svg>
                  </Col>
                  <Col span="12">
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
          <!-- {{recCard}} -->
      </TabPane>
    </Tabs>
    <div slot="footer">
    </div>
    <div slot="close" @click="destroyCanvas()">
      <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
        <use xlink:href="#close"></use>
      </svg>
    </div>
  </Modal>
</template>


<script>
import cardResult from "@/utils/cardResult.vue"
import devicePointCheckTableAdd from "./device-point-check-table-add";
export default {
  components: {
    cardResult,
    devicePointCheckTableAdd
  },
  name: "device-point-check",
  data() {
    return {
      tabsName: "name1",
      visible: false,
      deviceId: '',
      boxWidth: 0,
      boxHeight: 0,
      boxVisible: false,
      color: 'black',
      port: 0,
      ids: [],
      netInterface: [
        {
          value: 0,
          label: this.$t('screen.networkPort') + ' 1'
        },
        {
          value: 1,
          label: this.$t('screen.networkPort') + ' 2'
        },],
      redPointArr: null,
      greenPointArr: null,
      bluePointArr: null,
      checkLoading: false,
      infoLoading: false,
      // badPointNum: 0,
      isCheck: true,
      canvas: null,
      columns: [
        {
          title: this.$t('cardDevice.number'), width: 70, fixed: 'left', align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('screen.abscissa'), key: 'y', fixed: 'left',align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('screen.abscissa'))
          }
        },
        {
          title: this.$t('screen.ordinate'), key: 'x', fixed: 'left',  align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('screen.ordinate'))
          }
        },
      ],
      redPosition: [],
      greenPosition: [],
      bluePosition: [],
      tableVisible: false,
      modal_recCard_loading: false,
      recCard: [],
      modal_switch_loading: false,
      switchResult: [],
      relaySwitch: false,
      isRGB: false,
      isShowRGB: false,
      isHavaTable:false,
      uploadPointTableVisible:false,
      fileName:'',
      pageLists: [],
      page:{
        totalPage: 0, // 总页数
        currPage: 1, //  当前页
        pageSize: 5, // 页容量
        totalCount: 0, // 总条数
      },
      dataListLoading: false,
      dataColumns: [
        {
          title: this.$t('file.name'),
          key: "fileName",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('file.name'))
          }
        },
        {
          title: this.$t("file.TheSize"),
          key: "fileSize",
          slot: "fileSize",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('file.TheSize'))
          }
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        }
      ],
      downloadUrl: this.$http.adornUrl(`/card/query/downloadTableTemp?token=${this.$cookie.get('token')}`),
      setPointTableLoading:false,
      // 全局loading
      fullscreenLoading:false,
    }
  },
  methods: {
    // 切换单灯全彩模式
    showRGBHandler(status){
      this.isShowRGB = status
      if (this.isShowRGB) {
        this.drawRGBBadPoint()
      }else {
        this.drawBadPoint('redCanvas', 'red')
        this.drawBadPoint('greenCanvas', 'green')
        this.drawBadPoint('blueCanvas', 'blue')
      }
    },
    handlerTab (name) {
      this.tabsName = name
      if (name==='name2'){
        this.dataListLoading=false
        this.setPointTableLoading=false
        this.uploadPointTableVisible=false
        this.fileName=''
        this.pageLists= []
        this.page={
          totalPage: 0, // 总页数
          currPage: 1, //  当前页
          pageSize: 5, // 页容量
          totalCount: 0, // 总条数
        }
        this.queryPointTable()
      }
    },
    // 初始化
    init(ids) {
      if (ids) {
        this.tabsName = "name1"
        this.visible = true
        this.deviceId = ids[0]
        // this.ids = ids
        this.boxWidth = 0
        this.boxHeight = 0
        this.redPointArr = null
        this.greenPointArr = null
        this.bluePointArr = null
        this.port = 0
        this.checkLoading = false
        this.infoLoading = false
        this.isCheck = true
        // this.badPointNum = 0
        this.canvas = null
        this.redPosition = []
        this.greenPosition = []
        this.bluePosition = []
        this.boxVisible = false
        this.tableVisible = false
        this.recCard = []
        this.modal_recCard_loading = false
        this.isRGB = false
        this.isShowRGB = false
        // this.$Loading.destroy()
        this.fullscreenLoading=false
        this.isHavaTable=false
        this.dataListLoading=false
        this.setPointTableLoading=false
        this.uploadPointTableVisible=false
        this.pageLists= []
        this.page={
          totalPage: 0, // 总页数
            currPage: 1, //  当前页
            pageSize: 5, // 页容量
            totalCount: 0, // 总条数
        }
      }
    },
    // 全屏loading效果
    openFullScreen() {
      this.fullscreenLoading = true;
      setTimeout(() => {
        this.fullscreenLoading = false;
        this.isCheck = false
      }, 10000 * 6);
    },
    // 获取接收卡传感器数据
    /**
     * 控制接收卡继电器开关
     */
    setRecCardRelaySwitch() {
      this.switchResult = []
      this.recCard = []
      this.modal_switch_loading = true
      this.$http({
        url: this.$http.adornUrl('/card/set/setRecCardRelaySwitch'),
        method: 'post',
        data: this.$http.adornData({'ids': [this.deviceId], 'status': this.relaySwitch}, false)
      }).then(({data}) => {
        if (data && data.code == 0) {
          this.switchResult = data.data
        } else {
          this.$Message.error(data.msg);
        }
        this.modal_switch_loading = false
      })
  },
    /**
     * 查询接收卡数量
     */
    getRecCardInfo () {
      this.switchResult = []
      this.recCard = []
      // 批量查询
      if (this.deviceId) {
        this.modal_recCard_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getRecCardInfo'),
          method: 'post',
          data: [this.deviceId]
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.recCard = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_recCard_loading = false
        })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    // 点击检测
    startCheck() {
      this.checkLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/pointCheck/' + this.deviceId + '/' + this.port),
        method: 'get',
        params: this.$http.adornParams({})
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.data.success === true) {
            this.boxWidth = data.data.width
            this.boxHeight = data.data.height
            this.boxVisible = true
            this.draw('singleCanvas')
            this.draw('redCanvas')
            this.draw('greenCanvas')
            this.draw('blueCanvas')
            this.draw('RGBCanvas')
            this.openFullScreen()
            // this.$Loading.start();
            // setTimeout(() => {
            //   this.$Loading.finish();
            //   this.isCheck = false
            // }, 15000)
          } else {
            if (data.msg.error) {
              this.$Message.error({
                content: data.msg.error,
                duration: 3,
              })
            } else if (data.msg.msg) {
              this.$Message.error({
                content: data.msg.msg,
                duration: 3,
              })
            }
          }
        } else {
          if (data.msg.error) {
            this.$Message.error({
              content: data.msg.error,
              duration: 3,
            })
          } else if (data.msg.msg) {
            this.$Message.error({
              content: data.msg.msg,
              duration: 3,
            })
          } else if (data.msg.errorMessage) {
            this.$Message.error({
              content: data.msg.errorMessage,
              duration: 3,
            })
          } else {
            this.$Message.error({
              content: data.msg,
              duration: 3,
            })
          }
        }
        this.checkLoading = false
      })
    },
    draw(id) {
      var canvas = document.getElementById(id);
      let ctx = canvas.getContext("2d");
      // let myArray = this.pointArr;
      let cellSize; // 每个单元格的大小

      if (this.boxHeight < 100 && this.boxWidth < 100) {
        cellSize = 20
      } else if (this.boxHeight < 300 && this.boxWidth < 300){
        cellSize = 15
      } else {
        cellSize = 10
      }
      // else if ((this.boxHeight >200 && this.boxHeight <=512 )&& (this.boxWidth > 200&&this.boxWidth <=512)){
      //   cellSize = 10
      // }
      let numRows = this.boxHeight + 1;
      let numCols = this.boxWidth + 1;


      canvas.width = numCols * cellSize;
      canvas.height = numRows * cellSize;


      // 绘制背景
      ctx.fillStyle = "#e8eaec";
      ctx.fillRect(cellSize, cellSize, canvas.width, canvas.height);
      ctx.stroke();
      // 3. 采用遍历的方式，绘画x轴的线条
      var xLineTotals = Math.floor(canvas.height / cellSize); // 计算需要绘画的x轴条数
      for (var i = 0; i < xLineTotals; i++) {
        ctx.beginPath(); // 开启路径，设置不同的样式
        ctx.moveTo(0, cellSize * i - 0.5); // -0.5是为了解决像素模糊问题
        ctx.lineTo(canvas.width, cellSize * i - 0.5);
        ctx.strokeStyle = "#fff"; // 设置每个线条的颜色
        ctx.stroke();
      }

      // 4.采用遍历的方式，绘画y轴的线条
      var yLineTotals = Math.floor(canvas.width / cellSize); // 计算需要绘画y轴的条数
      for (var j = 0; j < yLineTotals; j++) {
        ctx.beginPath(); // 开启路径，设置不同的样式
        ctx.moveTo(cellSize * j, 0);
        ctx.lineTo(cellSize * j, canvas.height);
        ctx.strokeStyle = "#fff"; // 设置每个线条的颜色
        ctx.stroke();
      }

      //给外围添加序号
      ctx.fillStyle = "#000";
      if (this.boxHeight < 100 && this.boxWidth < 100) {
        ctx.font = '12px Arial';
      } else if (this.boxHeight < 300 && this.boxWidth < 300){
        ctx.font = '8px Arial';
      } else {
        ctx.font = '5px Arial';
      }

      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      for (let row = 1; row < numRows; row++) {
        const x = cellSize / 2;
        const y = row * cellSize - cellSize / 2;
        const num = row;
        ctx.fillText(num.toString(), x, y + cellSize);
      }
      for (let col = 1; col < numCols; col++) {
        const x = col * cellSize - cellSize / 2;
        const y = cellSize / 2;
        const num = col;
        // ctx.clearRect(x,y,cellSize,cellSize)
        ctx.fillText(num.toString(), x + cellSize, y);
      }

    },
    // 绘制全彩坏点图像
    drawRGBBadPoint() {
      var canvas = document.getElementById("RGBCanvas");
      let ctx = canvas.getContext("2d");
      let cellSize; // 每个单元格的大小

      if (this.boxHeight < 100 && this.boxWidth < 100) {
        cellSize = 20
      } else if (this.boxHeight < 300 && this.boxWidth < 300){
        cellSize = 15
      } else {
        cellSize = 10
      }
      let numRows = this.redPointArr.length + 1;
      let numCols = this.redPointArr[0].length + 1;
      let tempI = 0;
      // 绘制红色单元格
      for (let i = 1; i < numRows; i++) {
        let tempJ = 0;
        for (let j = 1; j < numCols; j++) {
          ctx.beginPath();
          ctx.arc(cellSize * j + cellSize / 2 - 0.5,  cellSize * i + cellSize / 2 / 2 - 0.5, cellSize / 2 / 2, 0, 2*Math.PI);
          if (this.redPointArr[tempI][tempJ] === 1) {
            ctx.fillStyle="black";
          } else {
            ctx.fillStyle="red";
          }
          ctx.fill();
          tempJ++;
        }
        tempI++;
      }
      // 绘制绿色单元格
      tempI = 0;
      for (let i = 1; i < numRows; i++) {
        let tempJ = 0;
        for (let j = 1; j < numCols; j++) {
          ctx.beginPath();
          ctx.arc(cellSize * j + cellSize / 2 / 2 - 0.5, cellSize * i + cellSize / 2 / 2 + cellSize / 2 - 0.5, cellSize / 2 / 2, 0,2*Math.PI);
          if (this.greenPointArr[tempI][tempJ] === 1) {
            ctx.fillStyle="black";
          } else {
            ctx.fillStyle="green";
          }
          ctx.fill();
          tempJ++;
        }
        tempI++;
      }
      // 绘制蓝色单元格
      tempI = 0;
      for (let i = 1; i < numRows; i++) {
        let tempJ = 0;
        for (let j = 1; j < numCols; j++) {
          ctx.beginPath();
          ctx.arc(cellSize * j + cellSize / 2 / 2 + cellSize / 2 - 0.5, cellSize * i + cellSize / 2 / 2 + cellSize / 2 - 0.5, cellSize / 2 / 2, 0,2*Math.PI);
          if (this.bluePointArr[tempI][tempJ] === 1) {
            ctx.fillStyle="black";
          } else {
            ctx.fillStyle="blue";
          }
          ctx.fill();
          tempJ++;
        }
        tempI++;
      }
    },
    // 绘制坏点
    drawBadPoint(id, color) {
      var canvas = document.getElementById(id);
      let ctx = canvas.getContext("2d");
      let myArray = this.redPointArr;
      if (color == "green") {
        myArray = this.greenPointArr;
      } else if (color == "blue") {
        myArray = this.bluePointArr;
      }
      let cellSize; // 每个单元格的大小

      if (this.boxHeight < 100 && this.boxWidth < 100) {
        cellSize = 20
      } else if (this.boxHeight < 300 && this.boxWidth < 300){
        cellSize = 15
      } else {
        cellSize = 10
      }
      let numRows = myArray.length + 1;
      let numCols = myArray[0].length + 1;
      let tempI = 0;
      // 绘制单元格
      for (let i = 1; i < numRows; i++) {
        let tempJ = 0;
        for (let j = 1; j < numCols; j++) {
          if (myArray[tempI][tempJ] === 1) {
            ctx.fillStyle = color;
            ctx.fillRect(j * cellSize, i * cellSize, cellSize, cellSize);
          }
          tempJ++;
        }
        tempI++;
      }
    },
    destroyCanvas() {
      this.destroyCanvasById('singleCanvas')
      this.destroyCanvasById('redCanvas')
      this.destroyCanvasById('greenCanvas')
      this.destroyCanvasById('blueCanvas')

    },
    destroyCanvasById(id) {
      var canvas = document.getElementById(id);
      //销毁等待效果
      if (this.$Loading){
        this.$Loading.destroy()
      }
      if (canvas != null) {
        let ctx = canvas.getContext("2d");
        ctx.clearRect(0, 0, canvas.width, canvas.height) // 清空矩形
      }


    },

    isHaveTable(){
      this.infoLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/tableState/'+this.deviceId),
        method: 'get',
        params: this.$http.adornParams({})
      }).then(({data})=>{
        if (data && data.code===0){
            if (data.data._type == "success") {
              if (data.data.isHave==true || data.data.isHave=="true"){
                this.getSpotCheckData()
              }else {
                this.$Message.error({
                  content: this.$t('screen.pleaseUploadPointTable'),
                  duration: 3
                })
              }
            } else {
              this.$Message.error({
                content: data.data.msg,
                duration: 3
              })
            }
        }else {
          this.$Message.error(data.msg)
        }
        this.infoLoading = false
      })
    },

    getSpotCheckData() {
        this.$http({
          url: this.$http.adornUrl('/card/query/spotCheckData/' + this.deviceId + '/' + this.boxWidth + '/' + this.boxHeight),
          method: 'get',
          params: this.$http.adornParams({})
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.data.success === true) {
              this.redPointArr = data.data.redArr
              this.greenPointArr = data.data.greenArr
              this.bluePointArr = data.data.blueArr
              // this.badPointNum = data.data.badPointNum
              this.redPosition = data.data.redPointPosition
              this.greenPosition = data.data.greenPointPosition
              this.bluePosition = data.data.bluePointPosition
              this.isRGB = data.data.isRGB
              this.tableVisible = true
              if (this.isRGB) {
                if (this.isShowRGB) {
                  this.drawRGBBadPoint()
                } else {
                  this.drawBadPoint('redCanvas', 'red')
                  this.drawBadPoint('greenCanvas', 'green')
                  this.drawBadPoint('blueCanvas', 'blue')
                }
              } else {
                this.drawBadPoint('singleCanvas', 'red')
              }
            } else {
              if (data.msg.error) {
                this.$Message.error(data.msg.error)
              } else if (data.msg.msg) {
                this.$Message.error(data.msg.msg)
              }
            }
          } else {
            if (data.msg.error) {
              this.$Message.error(data.msg.error)
            } else if (data.msg.msg) {
              this.$Message.error(data.msg.msg)
            } else if (data.msg.errorMessage) {
              this.$Message.error(data.msg.errorMessage)
            } else {
              this.$Message.error(data.msg)
            }
          }
        })
    },

    //上传点检表文件
    uploadPointTable(){
      this.uploadPointTableVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadPointTable.init()
      });

    },
    //查询点检表文件列表
    queryPointTable(){
      this.pageLists = []
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/card/query/pointCheckTable"),
        method: "post",
        params: this.$http.adornParams({
          fileName:this.fileName,
          type: 10,
          page: this.page.currPage,
          limit: this.page.pageSize,
        }),
      }).then(({ data }) => {
        this.page = data.page;
        this.pageLists = data.page.list;
        this.dataListLoading = false;
      });
    },
    // 当前页
    currentChangeHandle(val) {
      this.page.currPage = val;
      this.queryPointTable();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.page.pageSize = val;
      this.queryPointTable();
    },
    //设置走点表
    setPointCheckTable(fileId){
      this.setPointTableLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/set/pointTable"),
        method: "post",
        data: this.$http.adornData(
          {
            deviceId: this.deviceId,
            fileId: fileId
          },
        ),
      }).then(({ data }) => {
        if (data && data.code===0){
          this.$Message.success("success")
        }else {
          this.$Message.error(data.msg)
        }
        this.setPointTableLoading = false
      });
    },
    //删除
    toDelete(row) {
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          // 删除
          this.$http({
            url: this.$http.adornUrl("/screen/media/delete"),
            method: "post",
            data: this.$http.adornData([row.fileId], false),
          }).then(({ data }) => {
            if(data && data.code===0){
              this.queryPointTable()
            }else {
              this.$Message.error(data.msg)
            }
          });
        },
      });
    },
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
  }
}

</script>

<style scoped>
</style>
