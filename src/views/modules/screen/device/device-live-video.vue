<template>
    <Modal v-model="visible" width="900">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.liveVideo')}}</span>
        </p>
        <Alert type="warning" show-icon >
            <b class="tip" style="margin-bottom:5px">{{$t('common.note')}}：{{$t('tips.liveVideo')}}</b><br/>
            <b class="tip">{{$t('tips.liveVideo1')}}</b><br/>
            <span>{{$t('tips.liveVideo2')}}：</span>
            <div v-for="(item,index) in testAddr" :key="index">{{item}}</div>
        </Alert>
        <div style="height: 300px;width:50%;float:left">
          <Form style="height: 300px;" label-position="left" label-colon>
            <FormItem :label="$t('common.operation')">
              <i-Switch size="large" v-model="data.flag">
                  <span slot="open">ON</span>
                  <span slot="close">OFF</span>
              </i-Switch>
              <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
            </FormItem>
            <div v-if="data.flag">
                <FormItem label="URL">
                    <Input v-model="data.url" :placeholder="$t('common.PleaseInput') + 'URL'" style="width: 400px" /><br>
                </FormItem>
                <FormItem :label="$t('cardDevice.width')">
                    <InputNumber v-model="data.width" :placeholder="$t('common.PleaseInput') + $t('cardDevice.width')" style="width: 100px" />
                </FormItem>
                <FormItem :label="$t('cardDevice.height')">
                    <InputNumber v-model="data.height" :placeholder="$t('common.PleaseInput') + $t('cardDevice.height')" style="width: 100px" />
                </FormItem>
            </div>
          </Form>
        </div>
        <div style="height: 300px;width:50%;float:left;overflow-y: auto;" v-if="resultData.length > 0">
          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
        </div>
        <div slot="footer" style="text-align: left;">
          <span>
              <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
          </span>
          <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data () {
    return {
      visible: false,
      ids: [],
      data: {
        id: '',
        flag: false,
        url: 'rtsp://**************/vod/mp4://BigBuckBunny_175k.mov',
        width: 64,
        height: 64
      },
      dataForm: {},
      modal_loading: false,
      testAddr: ['rtmp://live.hkstv.hk.lxdns.com/live/hks', 'rtmp://************:1935/livetv/hunantv',
        'rtsp://**************/vod/mp4://BigBuckBunny_175k.mov'],
      resultData: []
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.modal_loading=false
      }
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.resultData = []
        this.data.ids = this.ids
        this.$http({
          url: this.$http.adornUrl('/card/set/liveVideo'),
          method: 'post',
          data: this.$http.adornData(this.data, false)
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg);
          }
          this.modal_loading = false
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.data = {
          id: '',
          flag: false,
          url: 'rtsp://**************/vod/mp4://BigBuckBunny_175k.mov',
          width: 64,
          height: 64
        }
        this.ids = []
        this.resultData = []
      }
    }
  }
}
</script>
