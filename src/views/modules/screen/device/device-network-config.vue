<template>
  <div>
    <Modal v-model="visible" width="1000">
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{ $t('screen.networkConfig') }}</span>
      </p>
      <Alert v-if="dataForm.type===1" type="warning" show-icon><b class="tip">{{ $t('screen.wifiTip') }}</b></Alert>
      <Alert v-if="dataForm.type===2" type="warning" show-icon><b class="tip">{{ $t('screen.apTip') }}</b></Alert>
          <Form :label-width="100" label-position="left"  label-colon ref="dataForm" :model="dataForm"  :rules="networkRules">
            <FormItem :label="$t('menu.type')" prop="type">
              <Select v-model="dataForm.type"  transfer  style="width:200px" @on-change="switchType()">
                <Option :value="1">WIFI</Option>
                <Option :value="2">{{ $t('screen.hotspot') }}</Option>
              </Select>
            </FormItem>
            <FormItem :label="$t('common.name')" prop="name" v-if="dataForm.type==1">
              <Input v-if="wifiList.length == 0" v-model="dataForm.name" :placeholder="$t('screen.selectWifi')" type="text" style="width: 300px"></Input>
              <Select v-else filterable v-model="dataForm.name" style="width:300px" :placeholder="$t('screen.selectWifi')">
                <Option v-for="item in wifiList" :value="item.ssid" :key="item.ssid">{{ item.ssid }}
                    <svg v-if="item.signal <= 50 && item.signal >= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                      <use xlink:href="#WIFI-level1"></use>
                    </svg>
                    <svg v-else-if="item.signal >= 50 && item.signal < 70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                      <use xlink:href="#WIFI-level2"></use>
                    </svg>
                    <svg  v-else-if="item.signal >= 70 && item.signal < 80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                      <use xlink:href="#WIFI-level3"></use>
                    </svg>
                    <svg v-else-if="item.signal >= 80 && item.signal < 100" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                      <use xlink:href="#WIFI-level4"></use>
                    </svg>
                </Option>
              </Select>
              <Button type="primary" :loading="queryLoading" @click="getWifiList()">{{ $t('screen.wifiList') }}</Button>
            </FormItem>
            <FormItem :label="$t('login.password')" prop="password">
              <Input   v-model="dataForm.password" type="text" :placeholder="$t('common.PleaseInput')+$t('login.password')" style="width: 300px"></Input>
            </FormItem>
            <FormItem :label="$t('login.confirmPassword')" prop="confirmPassword" v-if="dataForm.type==2">
              <Input   v-model="dataForm.confirmPassword" :placeholder="$t('login.confirmPassword')" type="text" style="width: 300px"></Input>
            </FormItem>
            <FormItem>
              <Button type="primary" :loading="setLoading" @click="setNetwork()">{{ $t('common.set') }}</Button>
            </FormItem>
            <div v-if="resultData.length > 0" style="height: 495px;overflow-y: auto">
              <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
          </Form>

      <div slot="footer" style="text-align: left;">
        <span>
          <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}
            </BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
  </div>
</template>


<script>
import SliderDrag from '@/utils/SliderDrag'
import cardResult from "@/utils/cardResult.vue"
import axios from "axios";
export default {
  data() {
    return {
      setTimingVisible: false,
      visible: false,
      tabVal: '',
      ids: [],
      dataForm:{
        type:1,
        name:'',
        password:'',
        confirmPassword:'',
        deviceIds:[]
      },
      setLoading:false,
      resultData:[],
      dataList: [],
      wifiList:[],
      queryLoading: false,
    }
  },
  computed: {
    networkRules:{
      get(){
        return{
          name: [
            {required: true, message:this.$t('validate.not_empty'), trigger: 'blur'}
          ],
          password: [
            {required: true, message:this.$t('validate.not_empty'), trigger: 'blur'},
            {
              validator: (rule, value, callback) => {
                if (value.length<8){
                  callback(new Error(this.$t('login.pwdNotes')))
                }else {
                  callback()
                }
              }, trigger: 'blur'
            }
          ],
          confirmPassword:[
            {required: true, message: this.$t('validate.confirm_password_cannot_empty'), trigger: 'blur'},
            {
              validator: (rule, value, callback) => {
                if (value.length<8){
                  callback(new Error(this.$t('login.pwdNotes')))
                }
                if (this.dataForm.password !== value) {
                  callback(new Error(this.$t('validate.the_new_password_is_inconsistent')))
                } else {
                  callback()
                }
              }, trigger: 'blur'
            }
          ]
        }
      }
    }
  },

  components: {
    SliderDrag,
    cardResult
  },
  methods: {
    // 初始化
    init(ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
        this.setLoading=false
        this.dataForm={
          type:1,
          name:'',
          password:'',
          confirmPassword: '',
          deviceIds:ids
        }
        this.resultData=[]
        this.wifiList=[]
        this.queryLoading=false
      }
    },
    // 查询wifi列表
    getWifiList(){
      this.queryLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/wifiList/'+this.ids[0]),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code == 0) {
          if (data.data._type!='success'){
            this.$Message.error(data.data.msg);
          }
          if (data.data.wifiList){
            this.wifiList = data.data.wifiList
            this.getPortableHotSpot()
          }
        } else {
          this.$Message.error(data.msg);
        }
        this.queryLoading = false
      })
    },
    // 查询wifi名字
    getPortableHotSpot(){
      this.queryLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/wifiOrAp/'+this.ids[0]),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code == 0) {
          if (data.data.success&&data.data.wifi){
            this.dataForm.name=data.data.wifi
          }
        } else {
          this.$Message.error(data.msg);
        }
        this.queryLoading = false
      })
    },
    switchType(){
      this.setLoading=false
      this.resultData=[]
      this.dataForm.name=''
      this.dataForm.password=''
      this.dataForm.confirmPassword=''
    },
    setNetwork(){
      if (this.ids.length > 0) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.setLoading=true
            this.$http({
              url: this.$http.adornUrl('/card/set/network'),
              method: 'post',
              data: this.dataForm
            }).then(({ data }) => {
              if (data && data.code == 0) {
                this.resultData = data.data
              } else {
                this.$Message.error(data.msg);
              }
              this.setLoading = false
            })
          }
        })

      }

    },
  },
}
</script>
