<template>
  <div>
    <Modal height="500" v-model="visible" width="900">
      <p slot="header" style="text-align:center;font-size: 20px;">
          <span>{{$t('operation.timingConfig')}}</span>
      </p>
        <div style="height:450px;width:880px;">
            <Tabs ref="tabValue" @on-click="handlerTab">
                <TabPane :label="$t('setTime.timeZone')" name="tab1">
                    <Alert type="warning" show-icon ><b class="tip">{{$t('common.networkNTP')}}：ntp1.aliyun.com</b></Alert>
                     <Form label-colon>
                        <FormItem :label="$t('setTime.theTimeZone')">
                            <Row>
                                <Col span="12">
                                    <Select filterable clearable  v-model="timezone" style="width:200px" >
                                        <Option v-for="item in timezoneS" :value="item.value" :key="item.value">{{ item.name }}</Option>
                                    </Select>
                                    <Button type="primary" :loading="setTimeZoneLoading"  @click="setTimeZone()">{{$t('setTime.setUpThe')}}</Button>
                                    <Button type="success" :loading="queryTimeZoneLoading" @click="queryTimeZone()">{{$t('setTime.query')}}</Button>
                                </Col>
                                <Col span="12">
                                    <Button type="success" :loading="backReadLoading" @click="backRead()">{{$t('setTime.ledTime')}}</Button>
                                </Col>
                            </Row>
                        </FormItem>
                        <Form style="height: 280px;overflow: auto;">
                        <div>
                          <!-- 批量时区展示 -->
                          <div v-if="timeZoneList.length > 0">
                            <cardResult :ids="ids" :resultData="timeZoneList" :cardItemWidth="900 / 2 - 50" :isQuery="true"
                              :resultItem="[{text: 'setTime.deviceTimeZone', name: 'timezone'}]"></cardResult>
                          </div>
                          <!-- 设置批量时区展示 -->
                          <div v-if="setTimeZoneList.length > 0">
                            <cardResult :ids="ids" :resultData="setTimeZoneList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
                          </div>
                          <!-- 查询时间展示 -->
                          <div v-if="backReadList.length > 0">
                            <cardResult :ids="ids" :resultData="backReadList" :cardItemWidth="900 / 2 - 50" :isQuery="true"
                              :resultItem="[{name: 'date'}]"></cardResult>
                          </div>
                        </div>
                        </Form>
                    </Form>
                </TabPane>
                <TabPane :label="$t('setTime.synchronizationSettings')" name="tab2">
                  <Alert type="success" show-icon >{{$t('common.Settings')}}</Alert>
                  <Row>
                    <Col span="12">
                      <div v-if="synchronousModel=='模式'"  style="height:360px;width:440px;border:1px dashed #0099FF">
                        <Form ref="LoraFrom" :model="LoraFrom" label-colon>
                          <FormItem style="margin-left:10px;">
                            <RadioGroup v-model="LoraFrom.type">
                              <Radio label="Lora"></Radio>
                              <Radio label="GPS"></Radio>
                              <Radio label="NTP"></Radio>
                            </RadioGroup>
                          </FormItem>
                          <FormItem  v-if="LoraFrom.type=='Lora'">
                            <Row>
                              <Col span="13">
                                <RadioGroup v-model="attribution">
                                  <Radio label="true" style="margin-left:10px;">
                                    <span>{{$t('setTime.main')}}</span>
                                  </Radio>
                                  <Radio label="false" style="margin-left:10px;">
                                    <span>{{$t('setTime.from')}}</span>
                                  </Radio>
                                </RadioGroup>
                              </Col>
                              <Col span="11">
                                <Button type="primary" :loading="setAttributionLoading" @click="setAttribution()">{{$t('setTime.setUpThe')}}</Button>
                                <Button type="success" :loading="queryAttributionLoading" @click="queryAttribution()">{{$t('setTime.query')}}</Button>
                              </Col>
                            </Row>
                          </FormItem>
                          <FormItem v-if="LoraFrom.type=='Lora'" :label="$t('setTime.IdentificationCode')" style="margin-top:-20px;margin-left:10px;">
                              <Input style="width:200px;" type="text" v-model="LoraFrom.identificationCode"></Input>
                          </FormItem>
                          <FormItem v-if="LoraFrom.type=='Lora'" :label="$t('setTime.timeOffset')" style="margin-top:-10px;margin-left:10px;">
                              <Input style="width:150px;" type="text" v-model="LoraFrom.delaySync"></Input>
                          </FormItem>
                          <FormItem v-if="LoraFrom.type=='Lora'" style="margin-top:-15px;">
                              <CheckboxGroup v-model="LoraFrom.screen" style="margin-left:30px;">
                                <Checkbox label="brightness">
                                    <span>{{$t('setTime.screenBrightness')}}</span>
                                </Checkbox>
                                <Checkbox label="volume">
                                    <span>{{$t('setTime.volume')}}</span>
                                </Checkbox>
                                <Checkbox label="screenSwitch">
                                    <span>{{$t('setTime.screenSwitch')}}</span>
                                </Checkbox>
                              </CheckboxGroup>
                          </FormItem>
                          <FormItem v-if="LoraFrom.type=='Lora'||LoraFrom.type=='NTP'" :label="$t('setTime.synchronizationInterval')" style="margin-left:10px;margin-top:-15px;" >
                              <Input style="width:130px;" type="text" v-model="LoraFrom.checkNtpTime" ></Input>{{$t('setTime.minTime')}}
                          </FormItem>
                          <FormItem style="margin-left:10px;">
                            <Row>
                              <Col span="9"><Button type="primary" :loading="setLoraLoading" @click="setLora('Lora')">{{$t('setTime.setUpThe')}}</Button></Col>
                              <Col span="10"><Button type="success" :loading="queryLoraLoading" @click="queryLora()">{{$t('setTime.query')}}</Button></Col>
                          </Row>
                          </FormItem>
                        </Form>
                      </div>
                    </Col>
                    <Col span="12">
                       <Form style="height: 340px;overflow: auto;margin-left: 20px" >
                         <div v-if="queryAttributionList.length > 0">
                          <cardResult :ids="ids" :resultData="queryAttributionList" :cardItemWidth="850 / 2 - 50" :isQuery="true"
                            :resultItem="[{name: 'result', text: 'setTime.masterSlaveMode',
                            resultSet: [{value: true, name: 'setTime.main', type: 'i18n'},
                            {value: false, name: 'setTime.from', type: 'i18n'}]}]"></cardResult>
                        </div>
                        <div v-if="setAttributionList.length > 0">
                          <cardResult :ids="ids" :resultData="setAttributionList" :cardItemWidth="850 / 2 - 50" :isQuery="false"></cardResult>
                        </div>
                        <div v-if="loraList.length > 0">
                          <cardResult :ids="ids" :resultData="loraList" :cardItemWidth="850 / 2 - 50" :isQuery="true" :tableHeight="205" :resultHeight="175"
                            :resultItem="[{name: 'time', text: 'common.SynchronousMode',suffix: ':', replace: 'SERIAL', replaceValue: 'Lora'},
                            {name: 'identificationCode',suffix: ':', text: 'setTime.IdentificationCode'},
                            {name: 'delaySync',suffix: ':', text: 'setTime.timeOffset'},
                            {name: 'brightness', suffix: ':',text: 'setTime.screenBrightness'},
                            {name: 'volume',suffix: ':', text: 'setTime.volume'},
                            {name: 'screenSwitch',suffix: ':', text: 'setTime.screenSwitch'},
                            {name: 'checkNtpTime', suffix: ':',text: 'setTime.synchronizationInterval'},
                            {name: 'lastSynchronousDate', suffix: ':',text: 'setTime.lastSynchronousTime'}]">
                          </cardResult>
                        </div>
                        <div v-if="setloraList.length > 0">
                          <cardResult :ids="ids" :resultData="setloraList" :cardItemWidth="850 / 2 - 50" :isQuery="false"></cardResult>
                        </div>
                       </Form>
                    </Col>
                  </Row>
                </TabPane>
                <TabPane label="NTP" name="tab3">
                  <Alert type="warning" show-icon ><b class="tip">{{$t('setTime.y60Channels')}}</b></Alert>
                  <Form ref="LoraFrom" :model="LoraFrom" label-colon>
                      <FormItem :label="$t('setTime.serverAddress')">
                        <Row>
                          <Col span="11">
                            <Input type="text" style="width:200px;" v-model="ntpURL" ></Input>
                            <Button type="primary" :loading="setNtpURLLoading" @click="setNtp()">{{$t('setTime.setUpThe')}}</Button>
                            <Button type="success" :loading="queryNtpURLLoading" @click="queryNtp()">{{$t('setTime.query')}}</Button>
                          </Col>
                          <Col span="13">
                            <Alert type="warning" show-icon ><b class="tip">{{$t('setTime.NTPpathNull')}}</b></Alert>
                          </Col>
                        </Row>
                      </FormItem>
                    </Form>
                  <Form ref="LoraFrom" :model="LoraFrom" label-colon style="height: 280px;overflow: auto;">
                    <div v-if="setNtpURLList.length > 0">
                      <cardResult :ids="ids" :resultData="setNtpURLList" :cardItemWidth="850 / 2 - 50" :isQuery="false"></cardResult>
                    </div>
                    <div v-if="queryNtpURLList.length > 0">
                      <cardResult :ids="ids" :resultData="queryNtpURLList" :cardItemWidth="850 / 2 - 50" :isQuery="true"
                        :resultItem="[{name: 'ntpServer', text: 'setTime.serverAddress'}]">
                      </cardResult>
                    </div>
                  </Form>
                </TabPane>
            </Tabs>
        </div>
        <div slot="footer" style="text-align: left;">
            <span>
                <Alert v-if="ids.length === 0" type="error" show-icon>{{$t('tips.uninstallFeatureSelectCard')}}</Alert>
                <Alert v-else type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
    </Modal>
  </div>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import timeZoneData from '@/assets/json/timeZone.json'
export default {
  components: {
    cardResult
  },
  data () {
    return {
      ntpURL: '', // NTP路径
      LoraFrom: {
        identificationCode: '',
        delaySync: '',
        screen: [],
        checkNtpTime: '',
        type: 'Lora'
      },
      attribution: '', // 主从
      timeZoneList: [], // 展示时区集合
      setTimeZoneList: [], // 设置时区结果集
      backReadList: [], // 查询时间结果集
      loraList: [], // 设置结果集
      setloraList: [], // 查询结果集
      setAttributionList: [], // 主从设置结果集
      queryAttributionList: [], // 查询主从设置结果集
      setNtpURLList: [], // 设置ntp地址结果集
      queryNtpURLList: [], // 查询ntp地址结果集
      queryTimeZoneLoading: false,
      setTimeZoneLoading: false,
      backReadLoading: false,
      queryLoraLoading: false,
      setLoraLoading: false,
      setAttributionLoading: false,
      queryAttributionLoading: false,
      setNtpURLLoading: false,
      queryNtpURLLoading: false,
      tabValue: 'tab1',
      ids: [],
      visible: false,
      timezoneS: [],
      timezone: '',
      synchronousModel: '模式'
    }
  },
  methods: {
    init (ids) {
      this.visible = true
      this.ids = ids
      this.loadTimeZone()
      this.timezone = ''
      this.clearLoading()
    },
    loadTimeZone () { // 失效
      this.timezoneS = timeZoneData
    },
    queryNtp () { // 查询NTP地址
      this.clearAllNTP()
      this.queryNtpURLLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/queryNtpURL'),
        method: 'post',
        data: this.ids
      }).then(({data}) => {
        if (data && data.code == 0) {
          this.queryNtpURLList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.queryNtpURLLoading = false
      })

    },
    setNtp () { // 设置NTP地址
      if(this.ids.length > 0) {
        this.clearAllNTP()
        this.setNtpURLLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/setNtpURL'),
          method: 'post',
          data: this.$http.adornData({'cardIds': this.ids, 'ntpURL': this.ntpURL})
        }).then(({data}) => {
          this.setNtpURLLoading = false
          if (data && data.code == 0) {
            this.setNtpURLList = data.data
          } else {
            this.$Message.error(data.msg);
          }
        })
      }
    },
    queryAttribution () { // 查询主从模式
      this.clearAllModel()
      this.queryAttributionLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/queryMasterSlaveSwitch'),
        method: 'post',
        data: this.ids
      }).then(({data}) => {
        if (data && data.code == 0) {
          this.queryAttributionList = data.data
        } else {
          this.$Message.error(data.msg);
        }
        this.queryAttributionLoading = false
      })
    },
    setAttribution () { // 设置主从模式
      if(this.ids.length > 0) {
        if (this.attribution !== '') {
          this.clearAllModel()
          this.setAttributionLoading = true
          this.$http({
            url: this.$http.adornUrl('/card/set/setMasterSlaveSwitch'),
            method: 'post',
            data: this.$http.adornData({'cardIds': this.ids, 'master': this.attribution})
          }).then(({data}) => {
            this.setAttributionLoading = false
            if (data && data.code == 0) {
              this.setAttributionList = data.data
            } else {
              this.$Message.error(data.msg)
            }
          })
        } else {
          this.$Message.error(this.$t('setTime.selectA'))
        }
      }
    },
    synchronousModelClick (model) { // 同步模式切换
      this.synchronousModel = model
      this.clearAllModel()
    },
    setLora () { // 设置模式
      this.clearAllModel()
      if(this.ids.length > 0) {
        this.setLoraLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/setPair'),
          method: 'post',
          data: this.$http.adornData({
            'cardIds': this.ids,
            'time': this.LoraFrom.type,
            'identificationCode': this.LoraFrom.identificationCode,
            'delaySync': this.LoraFrom.delaySync,
            'screen': this.LoraFrom.screen,
            'checkNtpTime': this.LoraFrom.checkNtpTime
          }, false)
        }).then(({data}) => {
          this.setLoraLoading = false
          if (data && data.code == 0) {
            this.setloraList = data.data
          } else {
            this.$Message.error(data.msg);
          }
        })
      }
    },
    queryLora () { // 查询模式
      this.clearAllModel()
      this.queryLoraLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/queryPair'),
        method: 'post',
        data: this.ids
      }).then(({data}) => {
        if (data && data.code == 0) {
          if (data.data && data.data.length > 0) {
            for (let i = 0; i < data.data.length; i++) {
              const element = data.data[i];
              if (element.lastSynchronousTime) {
                element.lastSynchronousDate = this.convertMillisecondsToDateTime(element.lastSynchronousTime)
              }
            }
          }
          this.loraList = data.data
        } else {
          this.$Message.error(data.msg);
        }
        this.queryLoraLoading = false
      })

    },
    convertMillisecondsToDateTime(milliseconds) {
      var date = new Date(milliseconds);
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      month < 10 ? month= "0" + month : month = month
      var day = date.getDate();
      day < 10 ? day= "0" + day : day = day
      var hour = date.getHours();
      hour < 10 ? hour= "0" + hour : hour = hour
      var minute = date.getMinutes();
      minute < 10 ? minute= "0" + minute : minute = minute
      var second = date.getSeconds();
      second < 10 ? second= "0" + second : second = second
      
      return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
    },
    backRead () { // 查询时间
      this.clearAll()
      this.backReadLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/queryBackRead'),
        method: 'post',
        data: this.ids
      }).then(({data}) => {
        if (data && data.code == 0) {
          this.backReadList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.backReadLoading = false
      })
    },
    setTimeZone () { // 批量设置时区
    if(this.timezone.length === 0){
      this.$Message.error(this.$t('common.zoneEmpty'))
      return;
    }
      this.clearAll()
      if (this.ids.length > 0) {
        this.setTimeZoneLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/setTimeZone'),
          method: 'post',
          data: this.$http.adornData({'cardIds': this.ids, 'timezone': this.timezone})
        }).then(({data}) => {
          this.setTimeZoneLoading = false
          if (data && data.code == 0) {
            this.setTimeZoneList = data.data
          } else {
            this.$Message.error(data.msg)
          }
        })
      } else {
        this.$Message.error(this.$t('setTime.selectATime'))
      }
    },
    queryTimeZone () { // 批量查询时区
      this.clearAll ()
      this.queryTimeZoneLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/queryTimeZone'),
        method: 'post',
        data: this.ids
      }).then(({data}) => {
        if (data && data.code == 0) {
          this.timeZoneList = data.data
        } else {
          this.$Message.error(data.msg)
        }
          this.queryTimeZoneLoading = false
      })
    },
    clearAll () {
      this.timeZoneList = [] // 展示时区集合
      this.setTimeZoneList = [] // 设置时区结果集
      this.backReadList = [] // 查询时间结果集
    },
    clearAllModel () {
      this.loraList = [] // 设置结果集
      this.setloraList = [] // 查询结果集
      this.setAttributionList = [] // 主从设置结果集
      this.queryAttributionList = [] // 查询主从设置结果集
    },
    clearAllNTP () {
      this.setNtpURLList = [] // NTP设置结果集
      this.queryNtpURLList = [] // 查询NTP设置结果集
    },
    clearLoading(){
      this.queryTimeZoneLoading=false
      this.setTimeZoneLoading= false
      this.backReadLoading= false
      this.queryLoraLoading= false
      this.setLoraLoading= false
      this.setAttributionLoading= false
      this.queryAttributionLoading= false
      this.setNtpURLLoading= false
      this.queryNtpURLLoading= false
    },
    //切换tab时清除数据
    handlerTab(){
      this.clearAll()
      this.clearAllNTP()
      this.clearAllModel()
      this.clearLoading()
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        this.ntpURL = ''
        this.attribution = ''
        this.$refs.tabValue.activeKey = 'tab1'
        this.clearAll()
        this.clearAllNTP()
        this.clearAllModel()
        this.clearLoading()
        this.loraList = [] // 设置结果集
        this.setloraList = [] // 查询结果集
        this.timezone = ''
        this.synchronousModel = '模式'
        this.LoraFrom = {
          identificationCode: '',
          delaySync: '',
          screen: [],
          checkNtpTime: '',
          type: 'Lora'
        }
      }
    }
  }
}
</script>
<style >
</style>
