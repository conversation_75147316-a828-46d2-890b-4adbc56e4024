 <template>
  <div>
    <Modal height="400" v-model="visible" width="900">
      <p slot="header" style="text-align:center;font-size: 20px;">
          <span>{{$t('operation.backgroundPlayback')}}</span>
      </p>
      <div style="height:420px;overflow: hidden;">
        <Alert type="warning" show-icon ><b class="tip">{{$t('setTime.y60Channels')}} {{$t('tips.backgroundTips')}}</b></Alert>
        <Form label-colon>
          <FormItem :label="$t('file.thumbnail')">
            <div style="width:200px;display: inline-block" v-if="selectItem">
              <div v-if="selectItem.suffix === 'mp4'">
                <svg width="50px" height="50px" style="margin-top: 5px" aria-hidden="true">
                  <use xlink:href="#video"></use>
                </svg>
              </div>
              <div v-else-if="selectItem.suffix === 'mp3'">
                <img src="@/assets/img/audio.png" style="margin-top: 5px" height="50px"  width="50px" />
              </div>
              <div v-else>
                <img :src="downloadUrl + selectItem.fileId" style="margin-top: 5px" height="50px" width="50px"/>
              </div>
            </div>
            <div style="width:200px;display: inline-block" v-else>
              {{$t('common.PleaseSelect')}}
            </div>
            <Button style="margin-left:20px;" type="info" @click="openMaterial()">{{$t('common.selectMaterial')}}</Button>
          </FormItem>

          <FormItem>
            <Button style="margin-left:20px;" type="primary" :loading="getLoading" @click="getBackground">{{$t('common.query')}}</Button>
            <Button style="margin-right:20px;float: right" type="primary" :loading="setLoading" @click="setBackground(true)">{{$t('common.set')}}</Button>
            <Button style="margin-right:20px;float: right" type="warning" :loading="removeLoading" @click="setBackground(false)">{{$t('operation.remove')}}</Button>
          </FormItem>

          <FormItem style="height:240px;overflow: auto;" v-if="getList.length > 0">
            <ul class="screen_ul" v-if="getList.length > 0">
              <li style="float: left;width: 350px;margin: 0 5px;" v-for="(item, index) in getList" :key="index">
                <div v-if="item._type === 'error' || item._type === 'Error'">
                  <div class="card-top" style="background-color: #f0ad4e;" v-if="item.deviceId">{{ item.deviceId }} <Icon type="md-close"/></div>
                  <div class="card-top" style="background-color: #f0ad4e;" v-else-if="item.cardId">{{ item.cardId }} <Icon type="md-close"/></div>
                  <div class="scree_view card-bottom">
                    <span style="color:red;font-size:15px;" v-if="item.msg">{{ item.msg }}</span>
                    <span v-if="item.errorMessage" style="color:red;font-size:15px;">{{ item.errorMessage }}</span>
                  </div>
                </div>
                <div v-else>
                  <div @click="onPreview(item.result)">
                    <div class="card-top" style="background-color: #5cb85c;" v-if="item.deviceId">{{ item.deviceId }} <Icon type="md-checkmark"/></div>
                    <div class="card-top" style="background-color: #5cb85c;" v-else-if="item.cardId">{{ item.cardId }} <Icon type="md-checkmark"/></div>
                    <div class="scree_view">
                      <el-image style="width: 150px;height: 150px;margin-top: 3px" :src="item.result"></el-image>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </FormItem>
          <div style="height:240px;overflow: auto;" v-if="setList.length > 0">
            <cardResult :ids="ids" :resultData="setList" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </Form>
      </div>
      <device-material v-if="sysMaterial" ref="deviceMaterial" @setURL="setURL"></device-material>
      <div slot="footer" style="text-align: left;">
        <span>
            <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import deviceMaterial from '../device-material.vue'
export default {
  data () {
    return {
      selectItem: '',
      visible: false,
      ids: [],
      sysMaterial: false,
      setLoading: false,
      setList: [],
      getLoading: false,
      getList: [],
      token: this.$cookie.get('token'),
      removeLoading:false,
      downloadUrl: this.$http.adornUrl(`/sys/program/file/download/`),
      isSet:true
    }
  },
  components: {
    deviceMaterial,
    cardResult
  },
  methods: {
    init (ids) {
      this.visible = true
      this.ids = ids
    },
    clearData(){
      this.getList=[]
      this.setList=[]
    },
    //查询播放器背景
    getBackground(){
      this.clearData()
      if(this.ids.length > 0){
        this.getLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getBackground'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.data.length > 0) {
                this.getList = this.ids.map(data => {
                    return {deviceId: data, loading: true}
                })
                for (let i = 0; i < data.data.length; i++) {
                    let element = data.data[i];
                    this.getList.forEach((item, index) => {
                        if (element.deviceId == item.deviceId) {
                            element.loading = false
                            this.getList.splice(index, 1, element)
                        }
                    })
                }
            }
            // this.getList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.getLoading = false
        })
      }
    },
    setBackground(isSet) { // 设置背景图片
      this.clearData()
      if(this.ids.length > 0){
        //判断是设置背景还是清除背景
        if (!isSet){
          this.removeLoading = true
          this.$http({
            url: this.$http.adornUrl('/card/set/setBackground'),
            method: 'post',
            data: this.$http.adornData({'cardIds': this.ids, 'url': ""})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.setList = data.data
            } else {
              this.$Message.error(data.msg)
            }
            this.removeLoading = false
          })
        }else {
          if (this.selectItem.fileId) {
            this.setLoading=true
            this.$http({
              url: this.$http.adornUrl('/card/set/setBackground'),
              method: 'post',
              data: this.$http.adornData({'cardIds': this.ids, 'url': this.selectItem.fileId})
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.setList = data.data
              } else {
                this.$Message.error(data.msg)
              }
              this.setLoading = false
            })
          } else {
            this.$Message.error(this.$t('common.PleaseSelect') + this.$t('screen.picture'))
          }
        }
      }
    },
    onPreview(img) {
      this.showViewer = true
      this.showImg = img
    },
    openMaterial () { // 打开素材库
      this.sysMaterial = true // 播放器背景
      this.$nextTick(() => {
        this.$refs.deviceMaterial.init(this.ids)
      })
    },
    setURL (selectItem) { // 打开素材选择库后返回路径
      if (selectItem !="") {
        // this.selectItem = window.SITE_CONFIG.FileURL + 'sys/program/file/download/' + url
        this.selectItem = selectItem
      }
    },
    clearLoading(){
      this.setLoading=false
      this.getLoading=false
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.getList=[]
        this.setList = []
        this.url = ''
        this.selectItem = ''
        this.clearLoading()
        this.clearData()
      }
    }
  }
}
</script>

<style scoped>
.screen_ul {
  height: auto;
  overflow: hidden;
  overflow-y: auto;
}
.scree_view {
  margin-bottom: 5px;
}
.card-top{
  width: 180px;
  border: 1px #fff;
  border-radius: 10%;
  text-align: center;
}
.card-bottom {
  margin-top: 5px;
  border: dashed #000000 1px;
  background-color: #f8f8f9;
  height: 75px;
  padding: 2px;
  overflow-y: auto;
}
</style>
