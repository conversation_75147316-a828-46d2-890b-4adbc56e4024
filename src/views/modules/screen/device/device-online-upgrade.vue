<template>
  <div>
    <Modal height="500" v-model="visible" width="1050">
      <p slot="header" style="text-align: center; font-size: 20px">
        <span>{{ $t("operation.onlineUpdate") }}</span>
      </p>
      <div class="schedules">
        <Loading :loadBoolValue="loadBool"></Loading>
        <Tabs @on-click="oldNamePD" ref="tabs">
          <TabPane :label="$t('card.genericVersion')" name="tab1">
            <Alert type="success" show-icon>
              {{ $t("tips.progressBar") }}
            </Alert>
            <Row>
              <Col span="9">
                <Input v-model="fuzzy0" :placeholder="$t('common.enterPackage')" style="width: 300px"/></Col>
              <Col span="11">
                <Button type="success" icon="ios-search-outline" @click="queryType('tab1')">{{$t('common.query')}}</Button>
              </Col>
              <Col span="4">
                <Button v-if="isAuth('screen:upgrade:getGeneric')" type="primary" icon="ios-cloud-upload-outline" @click="ZIP()">{{ $t("file.upload") }}ZIP</Button>
                <device-zip v-if="visible1" ref="deviceZip" @queryType="oldNamePD"></device-zip>
              </Col>
            </Row>
            <div style="height: 10px"></div>
            <Table stripe :columns="dataConlums" :data="pageLists" :max-height="tableHeight" :loading="dataListLoading">
              <template slot-scope="{ row }" slot="filePath">
                <Button type="primary" size="small" icon="ios-cloud-download">
                  <a style="color: white" target="_blank" :href="downloadUrl + row.fileId">{{ $t("file.download") }}</a>
                </Button>
              </template>
              <template slot-scope="{ row }" slot="fileId">
                <Button v-if="formItem.ids.length === 0" type="primary" disabled size="small">
                  <Icon type="md-refresh" />{{ $t("card.notChosenCard") }}</Button>
                <Button v-else type="primary" size="small" @click="online(row)">
                  <Icon type="md-refresh" />{{ $t("file.update") }}
                </Button>
              </template>
              <template slot-scope="{ row }" slot="fileSize">
                <span>{{ row.fileSize | filterType }}</span>
              </template>
              <template slot-scope="{ row }" slot="memo">
                <Button v-if="userInfo.userId == 1" type="primary" size="small" @click="toView(row)">
                  <Icon type="md-create" />{{ $t("common.update") }}</Button>
                <Button v-else type="primary" size="small" @click="toView(row)">
                  <Icon type="ios-search" />{{ $t("file.toView") }}</Button>
              </template>
              <template slot-scope="{ row }" slot="deleteFile">
                <Button v-if="isAuth('screen:upgrade:deleteGeneric')" type="error" size="small" @click="toDelete(row)">
                  <Icon type="md-close" />{{ $t("common.delete") }}</Button>
                <Button v-else disabled size="small">{{ $t("file.WithoutPermission") }}</Button>
              </template>
            </Table>
            <div class="schedules4"></div>
            <Modal v-model="modalMeMo" :title="memoTitle">
              <div class="schedules3">
                <Alert type="success" show-icon><h3>{{ memoHeader }}</h3></Alert>
                <Input maxlength="300" show-word-limit type="textarea"
                :autosize="{ minRows: 12, maxRows: 12 }" v-model="memoText"
                :placeholder="$t('announcement.enterContent')"></Input>
              </div>
              <div slot="footer">
                <Button type="primary" v-if="userInfo.userId == 1"
                  @click="updateVersionDetails()">
                  {{ $t("common.confirm") }}
                </Button>
              </div>
            </Modal>
            <Row>
              <Col span="24">
                <Page style="float: right" :total="page.totalCount" :current="page.currPage" :page-size="page.pageSize"
                  show-elevator show-sizer :page-size-opts="[5]" show-total
                  @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
              </Col>
            </Row>
            <div class="schedules4"></div>
          </TabPane>
          <TabPane :label="$t('card.TestVersion')" name="tab2">
            <Alert type="success" show-icon>
              {{ $t("tips.progressBar") }}
            </Alert>
            <Row>
              <Col span="9">
                <Input v-model="fuzzy2" :placeholder="$t('common.enterPackage')" style="width: 300px"/>
              </Col>
              <Col span="11">
                <Button type="success" icon="ios-search-outline" @click="queryType('tab2')">{{$t('common.query')}}</Button>
              </Col>
              <Col span="4">
                <Button type="primary" icon="ios-cloud-upload-outline"
                  @click="ZIP()">{{ $t("file.upload") }}
                  ZIP
                </Button>
                <device-zip v-if="visible1" ref="deviceZip" @queryType="oldNamePD"></device-zip>
              </Col>
            </Row>
            <div style="height: 10px"></div>
            <Table stripe :columns="dataConlums"
              :data="pageLists" :max-height="tableHeight" :loading="dataListLoading" >
              <template slot-scope="{ row }" slot="filePath">
                <Button type="primary" size="small" icon="ios-cloud-download">
                  <a style="color: white" target="_blank"
                    :href="downloadUrl + row.fileId">
                    {{ $t("file.download") }}</a></Button>
              </template>
              <template slot-scope="{ row }" slot="fileId">
                <Button v-if="formItem.ids.length === 0" type="primary"
                  disabled size="small" @click="online(row)">
                  <Icon type="md-refresh" />{{ $t("card.notChosenCard") }}
                </Button>
                <Button v-else type="primary" size="small" @click="online(row)">
                  <Icon type="md-refresh" />{{ $t("file.update") }}
                </Button>
              </template>
              <template slot-scope="{ row }" slot="fileSize">
                <span>{{ row.fileSize | filterType }}</span>
              </template>
               <template slot-scope="{ row }" slot="memo">
                <Button v-if="userInfo.userId == 1" type="primary"
                  size="small" @click="toView(row)">
                  <Icon type="md-create" />{{ $t("common.update") }}
                </Button>
                <Button v-else type="primary" size="small" @click="toView(row)">
                  <Icon type="ios-search" />{{ $t("file.toView") }}
                </Button>
              </template>
              <template slot-scope="{ row }" slot="deleteFile">
                <Button type="error" size="small"
                  style="" @click="toDelete(row)">
                  <Icon type="md-close" />{{ $t("common.delete") }}
                </Button>
              </template>
            </Table>
            <div class="schedules4"></div>
            <Row>
              <Col span="24">
                <Page style="float: right" :total="page.totalCount" :current="page.currPage"  :page-size="page.pageSize"
                  show-elevator show-sizer :page-size-opts="[5]" show-total @on-change="currentChangeHandle"
                  @on-page-size-change="sizeChangeHandle"/>
              </Col>
            </Row>
            <div class="schedules4"></div>
          </TabPane>
          <TabPane :label="$t('file.uninstall')" name="tab3">
            <div class="schedules5">
              <Form ref="uninstallForm">
                <FormItem>
                  <Input v-if="custom" v-model="uninstallForm.uninstallModel" style="width: 300px;"/>
                  <Select v-else v-model="uninstallForm.uninstallModel" style="width: 300px"
                    filterable>
                    <Option v-for="item in uninstallList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                  </Select>
                  <Checkbox v-model="custom">{{$t('common.custom')}}</Checkbox>
                  <Button v-if="formItem.ids.length === 0" disabled type="primary">{{ $t("file.uninstall") }}</Button>
                  <Button v-else type="primary" :loading="UninstLoading"
                    @click="selectUninstall">
                    {{ $t("file.uninstall") }}
                  </Button>
                  <!-- <Button @click="clearSelectUninstall">{{ $t("operation.remove") }}</Button> -->
                </FormItem>
                <div style="height: 320px; overflow: auto" v-if="returnUninstalls.length > 0">
                  <cardResult :ids="formItem.ids" :resultData="returnUninstalls" :cardItemWidth="1000 / 2 - 50"
                    :isQuery="false" setText="tips.UninstalledSuccessfully"></cardResult>
                </div>
              </Form>
            </div>
          </TabPane>
          <TabPane :label="$t('common.versionQuery')" name="tab4">
            <div class="schedules5" v-if="formItem.ids.length > 0">
              <div v-if="versionList.length > 0" style="height: 400px; overflow: auto">
                <cardResult :ids="formItem.ids" :resultData="versionList" :cardItemWidth="950" :isQuery="true"
                  :isTable="true" tableFieldNameLv1='apps' :tableColumns='versionConlums' :tableHeight="400"></cardResult>
              </div>
            </div>
            <div v-else>
              <div type="error" show-icon>{{$t('common.uninstallCard')}}！</div>
            </div>
          </TabPane>
        </Tabs>
      </div>
      <div slot="footer" style="text-align: left">
        <span>
          <Alert v-if="formItem.ids.length === 0" type="error" show-icon> {{ $t("tips.upgradeFeatureSelectCard") }} </Alert>
          <Alert v-else type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto; max-height: 42px">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in formItem.ids" :key="item" style="color: #999; font-weight: normal">{{ item }}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>

    <div v-if="visibleBar">
      <div class="adv" v-if="!isMinimize">
        <!--  最大化 -->
        <svg style="cursor: pointer; vertical-align: middle" @click="handleMinimize(isMinimize)"
          width="20px" height="20px" aria-hidden="true">
          <use xlink:href="#zuixiaohua"></use>
        </svg>
        <span style="text-align: center">{{ $t("file.Onlineupgrade") }}</span>
        <Divider />
        <div style="margin-top: 10px; overflow-y: auto; max-height: 200px">
          <div id="progress" v-html="protocol">
            <!-- html注入进度条界面 -->
          </div>
        </div>
      </div>
      <!--  最小化 -->
      <div class="adv" v-if="isMinimize" style="width: 250px; height: 40px">
        <svg style="cursor: pointer; vertical-align: middle" width="20px"
          @click="handleMinimize(isMinimize)" height="20px" aria-hidden="true">
          <use xlink:href="#zuidahua"></use>
        </svg>
        <span style="text-align: center">{{ $t("file.Waitingupdates") }}</span>
        <Divider />
      </div>
    </div>
  </div>
</template>

<script>
// import $ from 'jQuery'
import deviceZip from "../device-zip.vue";
import Loading from "@/utils/loading";
import cardResult from "@/utils/cardResult.vue"
export default {
  data() {
    return {
      custom: false,
      fuzzy0: "",
      fuzzy2: "",
      fileId: "",
      downloadUrl: this.$http.adornUrl("/sys/file/download/"),
      onlineLoading: false,
      loadBool: false,
      versionList: [],
      versionConlums: [
        {
          title: this.$t('menu.name'),
          key: "appName",
          align: "center",
          renderHeader:(h)=>{
              return h('div',this.$t('menu.name'))
          }
        },
        {
          title: this.$t('common.packageName'),
          key: "packageName",
          align: "center",
          renderHeader:(h)=>{
              return h('div',this.$t('common.packageName'))
          }
        },
        {
          title: this.$t('common.versionNumber'),
          key: "versionName",
          align: "center",
          renderHeader:(h)=>{
              return h('div',this.$t('common.versionNumber'))
          }
        },
        {
          title: this.$t('common.versionIdentifiers'),
          key: "versionCode",
          align: "center",
          renderHeader:(h)=>{
              return h('div',this.$t('common.versionIdentifiers'))
          }
        },
      ],
      cardHeight: 280,
      cardLoading: false,
      cardList: [],
      UninstLoading: false,
      protocol: "",
      dataListLoading: false,
      websocket: null,
      isMinimize: false,
      oldName: "",
      version: "1",
      uninstallForm: {
        uninstallModel: "",
      },
      uninstall: {
        value: "",
        label: "",
      },
      CardsoftwareMap: {},
      uninstallList: [
        {
          value: "com.xixun.xixunplayer",
          label: "Player",
        },
        {
          value: "com.xixun.display",
          label: "Display",
        },
        {
          value: "net.sysolution.taxiapp",
          label: "TaxiApp",
        },
        {
          value: "net.sysolution.starter",
          label: "Starter",
        },
        {
          value: "com.xixun.xy.xwalk",
          label: "Xwalk",
        },
        {
          value: "com.xixun.xy.live",
          label: "Live",
        },
        {
          value: "com.xixun.joey.systemcore",
          label: "SystemCore",
        },
        {
          value: "com.xixun.joey.cardsystem",
          label: "CardSystem",
        },
        {
          value: "com.xixun.xy.conn",
          label: "Conn",
        },
      ],
      timer: "",
      time: 120,
      returnUninstalls: [],
      modalMeMo: false,
      memoTitle: "",
      memoText: "",
      memoHeader: "",
      page: {
        totalPage: 0, // 总页数
        currPage: 1, //  当前页
        pageSize: 5, // 页容量
        totalCount: 0, // 总条数
      },
      visible1: false,
      visibleBar: false,
      dataConlums: [
        {
          title: this.$t("file.download"),
          key: "filePath",
          slot: "filePath",
          align: "center",
          width: 120,
          renderHeader:(h)=>{
              return h('div',this.$t('file.download'))
          }
        },
        {
          title: this.$t("file.OnlineUpdate"),
          key: "fileId",
          slot: "fileId",
          align: "center",
          width: 130,
          renderHeader:(h)=>{
              return h('div',this.$t('file.OnlineUpdate'))
          }
        },
        {
          title: this.$t("file.name"),
          key: "fileName",
          align: "center",
          width: 226,
          tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('file.name'))
          }
        },
        {
          title: this.$t("file.TheSize"),
          key: "fileSize",
          slot: "fileSize",
          align: "center",
          width: 100,
          tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('file.TheSize'))
          }
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          width: 173,
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
        {
          title: this.$t("file.VersionLog"),
          key: "memo",
          slot: "memo",
          align: "center",
          width: 120,
          renderHeader:(h)=>{
              return h('div',this.$t('file.VersionLog'))
          }
        },
        {
          title: this.$t("common.delete"),
          key: "fileId",
          slot: "deleteFile",
          align: "center",
          renderHeader:(h)=>{
              return h('div',this.$t('common.delete'))
          }
        },
      ],
      formItem: {
        ids: [],
      },
      pageLists: [],
      visible: false,
      updateForm: {
        id: 0,
        name: "",
      },
      uploadForm: {
        memo: "",
        name: "",
        type: "",
        resourceType: 0,
        size: "",
        dispalyFile: [], // 临时数组，同时用于显示在页面
      },
      // 上传配置
      upload: {
        fileProgressShow: false, // 进度条
        fileProgress: 0, // 进度条进度
      },
      eventData: {
        progress: "",
        speed: "",
        unzipped: "",
        commandId: "",
      },
      BoolClear: true, //关闭界面时是否关闭websocket
    };
  },
  components: {
    deviceZip,
    Loading,
    cardResult
  },
  methods: {
    // 初始化
    init(ids) {
      this.BoolClear = true;
      this.timer = "";
      this.visible = true;
      this.formItem.ids = ids;
      this.oldNamePD("tab1");
      if ("WebSocket" in window) {
        this.websocket = new WebSocket(
          `${window.SITE_CONFIG.WSURL}/websocket/${this.$cookie.get("token")}`
        );
      }
      this.websocket.onopen = function () {
        // console.log('连接成功')
      };
      this.websocket.onerror = function () {
        // console.log('连接出错')
      };
      this.websocket.onclose = function () {
        // console.log('退出连接')
      };
    },
    // 最大化最小化
    handleMinimize(isMinimize) {
      this.isMinimize = !isMinimize;
    },
    online(row) {
      var _successTemp = [];
      var errorTemp = [];
      this.loadBool = true;
      this.onlineLoading = true;
      // 在线更新
      this.$http({
        url: this.$http.adornUrl("/card/set/setUpgrade"),
        method: "post",
        data: this.$http.adornData({
          fileId: row.fileId,
          cardIds: this.formItem.ids,
        }),
      }).then(({ data }) => {
        if (data && data.code == 0) {
          if (data.data) {
            data.data.forEach(item => {
              var temp;
              if (item._type === "success") {
                temp = {
                  progress: 0,
                  speed: 0,
                  unzipped: false,
                  commandId: item.deviceId,
                  error: "",
                  state: "",
                };
                _successTemp.push(temp);
              } else {
                errorTemp.push(item)
              }
            })
          }
        } else {
          this.$Message.error(data.msg)
        }
        this.onlineLoading = false;
        this.loadBool = false;
        if (errorTemp.length > 0) {
          var errorHtml = '<div style="height: 400px; overflow-y: auto;">';
          for (let i = 0; i < errorTemp.length; i++) {
            const element = errorTemp[i];
            errorHtml += '<div style="margin-bottom: 5px">';
            errorHtml += '<span style="display:inline-block;width: 130px;font-weight:bold;">' + element.deviceId + '</span>';
            errorHtml += '<span style="color: red">' + element.msg +'</span>';
            errorHtml += '</div>';
          }
          errorHtml += '</div>';
          // 返回失败的卡 不放入进度条
          this.$Notice.error({
            title: this.$t("common.tips"),
            desc: errorHtml,
            duration: 0,
          });
        }
        if (_successTemp.length > 0) {
          var text = "";
          for (var i in _successTemp) {
            text += '<div style="margin-bottom: 3px">';
            text += _successTemp[i].commandId;
            text += "<div>";
            text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-success">';
            text += '<span class="ivu-alert-message">' + this.$t("file.ReadyDownload") + '</span>'
            text += '<span class="ivu-alert-desc"></span>'
            text +=  "</div>";
            text += "</div>";
            text += "</div>";
          }
          this.protocol = text;
          this.ProgressBarStatus(_successTemp);
        // } else {
        //   this.$Message.error(
        //     this.$t('common.upgradeWrong')
        //   );
        }
      });
    },
    timeJS() {
      this.time--;
      if (this.time == 0) {
        this.$Message.info({
          content: this.$t('common.cardsNotReturn'),
          duration: 10,
          closable: true
        });
        this.clearProgress();
      }
    },
    ProgressBarStatus(successTemp) {
      this.time = 120;
      this.timer = setInterval(() => {
        this.timeJS();
      }, 1000);
      var _this = this;
      var _successTemp = successTemp;
      // 生成进度条
      this.BoolClear = false;
      this.visible = false;
      this.visibleBar = true;
      this.websocket.onmessage = function (event) {
        _this.time = 120;
        var json = JSON.parse(event.data);
        for (var i in _successTemp) {
          if (_successTemp[i].commandId === json.commandId.split("_")[0]) {
            if (json.state === "Success") {
              //成功删除
              _this.$Notice.success({
                title: _this.$t("common.tips"),
                desc: _successTemp[i].commandId + ": " + _this.$t('common.updateComplete'),
                duration: 3,
              });
              _successTemp.splice(i, 1);
            } else if (json.state === "Error" || json.error === "unknown") {
              _this.$Notice.error({
                title: _this.$t("common.tips"),
                desc: _successTemp[i].commandId + ": "+ _this.$t('file.UpdateFailed'),
                duration: 0,
              });
              _successTemp.splice(i, 1);
            } else {
              //替换
              var temp = {
                progress: json.progress,
                speed: json.speed,
                unzipped: json.unzipped,
                commandId: json.commandId.split("_")[0],
                error: json.error,
                state: json.state,
              };
              _successTemp[i] = temp;
            }
            if (_successTemp.length === 0) {
                _this.$Notice.success({
                  title: _this.$t("common.tips"),
                  desc: _this.$t('file.ThreeSeconds'),
                  duration: 3,
                });
                setTimeout(_this.clearProgress(), 3000);
                window.clearTimeout(_this.timer)
              }
            break;
          }
        }
        var text = "";
        for (var j in _successTemp) {
          // 分发到进度条
          text += '<div  style="margin-bottom: 3px">' + _successTemp[j].commandId;
          text += "<div>";
          if (_successTemp[j].error !== "unknown") {
            if (_successTemp[j].progress === 100) {
              text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-warning">'
              text += '<span class="ivu-alert-message">' + _this.$t("file.DownloadComplete") + "。</span>"
              text += '<span class="ivu-alert-desc"></span>'
              text += "</div>";
            } else if (_successTemp[j].progress > 0) {
              text += '<div data-v-575dc1bf="" class="ivu-progress ivu-progress-active"><div class="ivu-progress-outer"><div class="ivu-progress-inner">'
              text += '<div class="ivu-progress-bg" style="width: ' +  _successTemp[j].progress + '%; height: 20px;">'
              text += '<div class="ivu-progress-inner-text">' + _successTemp[j].progress + "%</div>"
              text += "</div>"
              text += '<div class="ivu-progress-success-bg" style="width: 0%; height: 20px;"></div>'
              text +=  "</div></div></div>";
            } else if (_successTemp[j].progress < 0) {
              // text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-warning">';
              // text += '<span class="ivu-alert-message">' + _this.$t("file.NotSupported") + "</span>";
              // text += '<span class="ivu-alert-desc"></span>'
              // text += "</div>";
              text += '<div data-v-575dc1bf="" class="ivu-progress ivu-progress-active"><div class="ivu-progress-outer"><div class="ivu-progress-inner">'
              text += '<div class="ivu-progress-bg" style="width: ' +  5 + '%; height: 20px;">'
              text += '<div class="ivu-progress-inner-text">' + 5 + "%</div>"
              text += "</div>"
              text += '<div class="ivu-progress-success-bg" style="width: 0%; height: 20px;"></div>'
              text +=  "</div></div></div>";
            } else {
              if (_successTemp[j].state === "Success") {
                text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-success">'
                text += '<span class="ivu-alert-message">' +  _this.$t("file.UpdateSuccessful") +  "</span>"
                text += '<span class="ivu-alert-desc"></span>'
                text += "</div>";
              } else if (_successTemp[j].state === "Error") {
                text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-error">'
                text += '<span class="ivu-alert-message">' +  _this.$t("file.UpdateFailed") + "</span>"
                text += '<span class="ivu-alert-desc"></span>'
                text += "</div>";
              } else {
                text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-success">'
                text += '<span class="ivu-alert-message">' + _this.$t("file.ReadyDownload") + "</span>"
                text += '<span class="ivu-alert-desc"></span>'
                text += "</div>";
              }
            }
          } else {
            text += '<div data-v-e81752bc="" class="ivu-alert ivu-alert-error">'
            text += '<span class="ivu-alert-message">' +  _this.$t("file.ConnectionFailed") + "</span>"
            text += '<span class="ivu-alert-desc"></span>'
            text += "</div>";
          }
          text += "</div>";
          text += "</div>";
        }
        _this.protocol = text;
      };
    },
    clearProgress() {
      this.websocket.close();
      this.visibleBar = false;
    },
    toView(row) {
      this.modalMeMo = true;
      this.memoTitle = this.$t("file.LogDetails");
      this.memoHeader = row.fileName;
      this.memoText = row.memo;
      this.fileId = row.fileId;
    },
    toDelete(row) {
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          // 删除
          this.$http({
            url: this.$http.adornUrl("/screen/upgrade/deleteGeneric"),
            method: "post",
            data: this.$http.adornData([row.fileId], false),
          }).then(({ data }) => {
            if (this.version === "0") {
              this.queryType("tab1");
            }
            if (this.version === "2") {
              this.queryType("tab2");
            }
          });
        },
      });
    },
    clearSelectUninstall() {
      this.returnUninstalls = [];
    },
    selectUninstall() {
      this.clearSelectUninstall();
      //卸载
      if (this.uninstallForm.uninstallModel.length !== 0) {
          this.UninstLoading = true;
          this.$http({
            url: this.$http.adornUrl("/card/set/selectUninstall"),
            method: "post",
            data: this.$http.adornData({
              pkg: this.uninstallForm.uninstallModel,
              cardIds: this.formItem.ids,
            }),
          }).then(({ data }) => {
            this.UninstLoading = false;
            if (data && data.code == 0) {
              // var msg = {
              //   deviceId: data.msg.deviceId,
              //   timestamp: this.timestampToTime(data.msg.timestamp),
              //   msg: data.msg.msg,
              //   _type: data.msg._type,
              // };
              this.returnUninstalls = data.data;
            }
          });
        this.UninstLoading = false;
      } else {
        this.$Message.error(this.$t("tips.SelectUninstall"));
      }
    },
    ModelSaveclose() {
      this.updateForm = { id: 0, name: "" };
      this.uploadForm = {
        memo: "",
        name: "",
        type: "",
        resourceType: 0,
        size: "",
        dispalyFile: [],
      };
      this.upload = { fileProgressShow: false, fileProgress: 0 };
      this.page = {
        totalPage: 0, // 总页数
        currPage: 1, //  当前页
        pageSize: 5, // 页容量
        totalCount: 0, // 总条数
      };
      this.pageLists = [];
      this.cardList = [];
      this.returnUninstalls = [];
      this.uninstallForm = {
        uninstallModel: "",
      };
    },
    oldNamePD(name) {
      if (this.oldName === name) {
        this.queryType(name);
      } else {
        this.page.currPage = 1;
        this.queryType(name);
        this.oldName = name;
      }
    },
    queryType(name) {
      //查找tab1,tab2,tab3，tab4
      (this.versionList = []), (this.pageLists = []);
      this.cardList = [];
      this.CardsoftwareMap = {};
      var fuzzy = "";
      if (name === "tab1" || name === "tab2") {
        var typeUrl = "/screen/upgrade/getGeneric";
        if (name === "tab1") {
          this.version = "0";
          fuzzy = this.fuzzy0;
        } else if (name === "tab2") {
          this.version = "2";
          fuzzy = this.fuzzy2;
        }
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl(typeUrl),
          method: "post",
          params: this.$http.adornParams({
            type: this.version,
            page: this.page.currPage,
            limit: this.page.pageSize,
            fileName: fuzzy,
          }),
        }).then(({ data }) => {
          this.page = data.page;
          this.pageLists = data.page.list;
          this.dataListLoading = false;
        });
      } else if (name === "tab4") {
        if(this.formItem.ids.length > 0){
          this.loadBool = true;
          this.$http({
            url: this.$http.adornUrl("/card/query/queryVersion"),
            method: "post",
            data: this.formItem.ids,
          }).then(({ data }) => {
            if (data && data.code == 0) {
              this.versionList = data.data
            } else {
              this.$Message.error(data.msg)
            }
            this.loadBool = false;
          });
        }
      }
    },
    // 每页数
    sizeChangeHandle(val) {
      this.page.pageSize = val;
      this.oldNamePD(this.oldName);
    },
    // 当前页
    currentChangeHandle(val) {
      this.page.currPage = val;
      this.oldNamePD(this.oldName);
    },
    ZIP() {
      this.visible1 = true;
      this.$nextTick(() => {
        this.$refs.deviceZip.init(this.version);
      });
    },
    updateVersionDetails() {
      this.$http({
        url: this.$http.adornUrl("/sys/file/updateVersionDetails"),
        method: "post",
        params: this.$http.adornParams({
          fileId: this.fileId,
          Text: this.memoText,
        }),
      }).then(({ data }) => {
        if (data.code === 0) {
          this.queryType("tab1");
          this.modalMeMo = false;
        } else {
          this.$Message.error(data.msg);
        }
      });
    },
  },
  mounted() {
    // this.timer = setInterval(this.timeJS(), 1000)
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        this.fuzzy0 = "";
        this.fuzzy2 = "";
        this.$refs.tabs.activeKey = "tab1";
        this.ModelSaveclose();
        this.$emit("refreshDataList");
        if (this.BoolClear) {
          this.websocket.close();
        }
      }
    },
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
    formatDate: function (value) {//调用时间戳为日期显示
      let date = new Date(value)
      let y = date.getFullYear()  //获取年份
      let m = date.getMonth() + 1  //获取月份
      m = m < 10 ? "0" + m : m  //月份不满10天显示前加0
      let d = date.getDate()  //获取日期
      d = d < 10 ? "0" + d : d  //日期不满10天显示前加0
      let h = date.getHours(); //小时
      h = h < 10 ? "0" + h : h  //不满10显示前加0
      let mi = date.getMinutes(); //分
      mi = mi < 10 ? "0" + mi : mi  //不满10显示前加0
      let s = date.getSeconds(); //秒
      s = s < 10 ? "0" + s : s  //不满10显示前加0
      return y + "-" + m + "-" + d + " " + h + ":" + mi + ':' + s
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  },
};
</script>
<style scoped>
/* .schedules {
  height: 480px;
} */
.schedules2 {
  height: 400px;
}
.schedules3 {
  height: 300px;
}
.schedules4 {
  height: 10px;
}
.schedules5 {
  height: 380px;
}
.adv {
  position: fixed;
  right: 10px;
  bottom: 10px;
  border-radius: 2%;
  background-color: rgb(255, 255, 255);
  width: 250px;
  height: 250px;
  padding: 5px;
  overflow-x: hidden;
  overflow-y: auto;
}
.loadClass {
  height: 50px;
  width: 50px;
  background-color: transparent;
  position: absolute;
  z-index: 9;
  left: 45%;
  top: 45%;
}
#loading3 {
  position: relative;
  width: 50px;
  height: 50px;
}
.demo3 {
  width: 4px;
  height: 4px;
  border-radius: 2px;
  background: #28bef5;
  position: absolute;
  animation: demo3 linear 0.8s infinite;
  -webkit-animation: demo3 linear 0.8s infinite;
}
.demo3:nth-child(1) {
  left: 24px;
  top: 2px;
  animation-delay: 0s;
}
.demo3:nth-child(2) {
  left: 40px;
  top: 8px;
  animation-delay: 0.1s;
}
.demo3:nth-child(3) {
  left: 47px;
  top: 24px;
  animation-delay: 0.1s;
}
.demo3:nth-child(4) {
  left: 40px;
  top: 40px;
  animation-delay: 0.2s;
}
.demo3:nth-child(5) {
  left: 24px;
  top: 47px;
  animation-delay: 0.4s;
}
.demo3:nth-child(6) {
  left: 8px;
  top: 40px;
  animation-delay: 0.5s;
}
.demo3:nth-child(7) {
  left: 2px;
  top: 24px;
  animation-delay: 0.6s;
}
.demo3:nth-child(8) {
  left: 8px;
  top: 8px;
  animation-delay: 0.7s;
}

@keyframes demo3 {
  0%,
  40%,
  100% {
    transform: scale(1);
  }
  20% {
    transform: scale(3);
  }
}
@-webkit-keyframes demo3 {
  0%,
  40%,
  100% {
    transform: scale(1);
  }
  20% {
    transform: scale(3);
  }
}
</style>
