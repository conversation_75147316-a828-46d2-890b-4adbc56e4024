<style>
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
</style>
<template>
  <Modal v-model="visible" width="900" height="500" footer-hide>
    <!-- <Spin fix v-if="showspin" size="large">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>加载中...</div>
    </Spin> -->
    <div slot="header" class="headerClass">
      <p>
        <span>{{ $t('operation.screenshot') }}</span>
      </p>
    </div>
    <div>
       <Button style="margin-left:20px" type="primary" @click="screenshot()">{{ $t('operation.screenshot') }}</Button>
       <Button style="margin-left:20px" type="info" @click="screenshotFull()">{{ $t('operation.fullSizeScreenshotOfAndroid') }}</Button>
    </div>
    <ul class="screen_ul">
      <li v-for="(item, index) in devices" :key="index">
        <div v-if="item.loading === true">
          <span style="font-size:15px;">{{ item.deviceId }}</span>
          <div class="demo-spin-container">
            <Spin size="large" fix :show="item.loading"></Spin>
          </div>
        </div>
        <div v-else>
          <div v-if="item._type === 'error'">
            <span style="font-size:15px;">{{ item.deviceId }}</span>
            <div class="scree_view"><span style="color:red;font-size:20px;">{{ item.msg }}</span></div>
          </div>
          <div v-else>
            <div @click="onPreview(item.result)">
              <span style="font-size:15px;">{{ item.deviceId }}</span>
              <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')" placement="bottom-end">
                <el-image style="width: 220px;height: 220px;margin-top: 3px" :src="item.result"
                  :alt="$t('common.clickToEnlarge')"></el-image>
              </el-tooltip>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]" style="z-index:25000;" />
  </Modal>
</template>

<script>
import $ from 'jQuery'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      visible: false,
      devices: [],
      showViewer: false,
      showImg: '',
      // showspin: false,
      ids: []
      // imgarr: []
    }
  },
  methods: {
    // 初始化
    init(ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
      } else {
        this.ids = []
      }
    },
    // 屏幕全尺寸截图
    screenshotFull() {
      if (this.ids.length > 0) {
        this.devices = []
        this.devices = this.ids.map(data => {
          return {deviceId: data, loading: true}
        })
        this.$http({
          url: this.$http.adornUrl('/card/query/screenshotFull'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
              for (let i = 0; i < data.data.length; i++) {
                let element = data.data[i];
                this.devices.forEach((item, index) => {
                  if (element.deviceId == item.deviceId) {
                    element.loading = false
                    this.devices.splice(index, 1, element)
                  }
                })
              }
          } else {
            this.$Message.error(data.msg)
          }
        });
      }
    },
    // 屏幕截图
    screenshot() {
      // 群发改单发
      if (this.ids.length > 0) {
        this.devices = []
        this.devices = this.ids.map(data => {
          return {deviceId: data, loading: true}
        })
        this.ids.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/card/query/screenshot'),
            method: 'post',
            data: [item]
          }).then(({data}) => {
            if (data && data.code === 0) {
              for (let i = 0; i < data.data.length; i++) {
                let element = data.data[i];
                this.devices.forEach((item, index) => {
                  if (element.deviceId == item.deviceId) {
                    element.loading = false
                    this.devices.splice(index, 1, element)
                  }
                })
              }
            } else {
              // this.$Message.error(data.msg)
            }
          })
        })
        
        /* this.$http({
          url: this.$http.adornUrl('/card/query/screenshot'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            for (let i = 0; i < data.data.length; i++) {
              let element = data.data[i];
              this.devices.forEach((item, index) => {
                if (element.deviceId == item.deviceId) {
                  element.loading = false
                  this.devices.splice(index, 1, element)
                }
              })
            }
          } else {
            this.$Message.error(data.msg)
          }
        }) */
      }
    },
    onPreview(img) {
      this.showViewer = true
      this.showImg = img;
      // this.imgarr = [];
      // this.imgarr.push(img);
      // console.log(imgarr);
    },
    closeViewer() {
      this.showViewer = false
      this.showImg = ''
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.devices = []
        $('#dialog-bg').hide()
        this.showViewer = false
      }
    }
  }
}
</script>
<style scoped>
.screen_ul {
  height: 500px;
  overflow: hidden;
  overflow-y: auto;
}

.screen_ul li {
  list-style: none;
  float: left;
  margin-left: 20px;
  margin-top: 10px;
  width: 250px;
  height: 260px;
  background: linear-gradient(to bottom, rgb(200, 241, 255), rgb(255, 200, 200));
  text-align: center;
  border-radius: 5%;
}

.scree_view {
  width: 220px;
  height: 220px;
  margin: 3px auto 5px;
  background: rgb(255, 255, 255);
  padding-top: 10px;
}

.demo-spin-container {
  width: 220px;
  height: 220px;
  position: relative;
  /* margin: 3px auto 5px; */
  display: inline-block;
  background: rgb(0, 0, 0);
}

.headerClass {
  text-align: center;
  font-size: 20px;
}
</style>
