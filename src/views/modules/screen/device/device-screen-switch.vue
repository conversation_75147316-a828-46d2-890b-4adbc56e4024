<template>
  <div>
    <Modal v-model="visible" width="900">
      <p slot="header" style="text-align:center;font-size: 20px;">
          <span>{{$t('operation.screenSwitch')}}</span>
      </p>
      <Tabs :value="tabVal" @on-click="handlerTab" style="height: 650px">
        <TabPane :label="$t('operation.screenSwitch')" name="tab1">
          <Alert type="warning" show-icon ><b class="tip">{{$t('tips.screenSwitch')}}</b></Alert>
          <Form :label-width="30" label-position="left">
            <FormItem>
              <Button type="success" :loading="on_loading" @click="screenSwitchSubmit(true)">{{$t('program.open')}}</Button>
              <Button type="warning" :loading="off_loading" @click="screenSwitchSubmit(false)">{{$t('program.close')}}</Button>
            </FormItem>
            <div v-if="resultData.length > 0" style="height: 495px;overflow-y: auto">
              <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
            </div>
          </Form>
        </TabPane>
        <TabPane :label="$t('operation.timingSwitch')" name="tab2">
          <Alert type="warning" show-icon><b class="tip">{{$t('common.screenDuring')}}</b></Alert>
          <Form :inline="true" @keyup.enter.native="getDataList()">
            <FormItem style="margin-bottom: 10px">
              <Input v-model="name" :placeholder="$t('common.name')"></Input>
            </FormItem>
            <FormItem style="margin-bottom: 10px">
              <Button style="margin-right:6px" @click="getDataList()" >
                <div>{{$t('common.query')}}</div>
              </Button>
              <Button style="margin-right:6px" type="primary" @click="addOrUpdateHandle()">
                <div>{{$t('common.newlyBuild')}}</div>
              </Button>
            </FormItem>
          </Form>
          <Table border :columns="dataConlums" :data="dataList"
            :loading="dataListLoading" style="width: 100%" :max-height="300" ref="selection">
            <template slot-scope="{ row, index }" slot="operation">
                <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading" @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
                <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
                <Button type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
            </template>
          </Table>
          <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
            <div style="height: 150px;overflow-y: auto;clear: both;" v-if="schedulesResultData.length > 0">
              <cardResult :ids="ids" :resultData="schedulesResultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
            </div>
        </TabPane>
        <TabPane :label="$t('operation.queryOrClearTiming')" name="tab3">
          <Form style="overflow: hidden;" :label-width="30" label-position="left">
            <FormItem>
              <!-- <Button  type="primary" @click="timedScreening()">{{$t('card.setTiming')}}</Button> -->
              <Button :loading="modal_loading"  type="success" @click="getTimedScreening()">{{$t('card.getTiming')}}</Button>
              <Button type="warning" :loading="clear_loading" @click="clearTime()">{{$t('common.removeTiming')}}</Button>
            </FormItem>
            <div style="height: 535px;overflow-y: auto">
              <div v-if="timedScreen.length > 0">
                <cardResult :ids="ids" :resultData="timedScreen" :cardItemWidth="800" :isQuery="true" :tableHeight="200"
                  :isTable="true" tableFieldNameLv1='task' tableFieldNameLv2='schedules' :tableColumns='schedulesColumns'></cardResult>
              </div>
              <div v-if="clearTimeList.length > 0">
                <cardResult :ids="ids" :resultData="clearTimeList" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
              </div>
            </div>
          </Form>
        </TabPane>
      </Tabs>
      <div slot="footer" style="text-align: left;">
        <span>
            <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
    <!-- 定时-->
    <device-schedules v-if="setTimingVisible" ref="deviceSchedules" @schedules-result-data="getDataList"></device-schedules>

  </div>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import deviceSchedules from './device-schedules'
export default {
  data () {
    return {
      visible: false,
      clear_loading: false,
      clearTimeList: [],
      modal_loading: false,
      on_loading: false,
      off_loading: false,
      tabVal: '',
      ids: [],
      timedScreen: [],
      setTimingVisible: false,
      resultData: [],
      schedulesResultData: [],
      name: '',
      dataConlums: [
        // {type: 'selection', width: 60, align: 'center'},
        // {title: 'ID', key: 'id', width: 80, align: 'center'},
        {title: this.$t('common.name'), key: 'name', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.name'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.operation'))
          }
        },
      ],
      schedulesColumns: [
        {
          title: this.$t('card.DateRange'), // 日期范围
          key: 'dateType',
          align: 'center',
          width: '200',
          render: (h, {row, index}) => {
              var result = ''
              if (row.dateType === 'Range') {
              result = [
                  h('span', {}, row.startDate),
                  h('span', {}, ' -- '),
                  h('span', {}, row.endDate)
              ]
              } else {
              result = h('span', {}, this.$t('card.notSpecified'))
              }
              return result
          },
          renderHeader:(h)=>{
            return h('div',this.$t('card.DateRange'))
          }
        },
        {
          title: this.$t('card.timeFrame'), // 时间范围
          key: 'timeType',
          align: 'center',
          render: (h, {row, index}) => {
              var result = ''
              if (row.timeType === 'Range') {
              result = [
                  h('span', {}, row.startTime),
                  h('span', {}, ' -- '),
                  h('span', {}, row.endTime)
              ]
              } else {
              result = h('span', {}, this.$t('card.notSpecified'))
              }
              return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.timeFrame'))
          }
        },
        {
          title: this.$t('card.SpecifyWeek'), // 星期范围
          key: 'filterType',
          align: 'center',
          width: '350',
          tooltip: true,
          render: (h, {row, index}) => {
              var result = ''
              if (row.filterType === 'Week') {
              result = [
                  h('span', {}, this.getWeekDay(row.weekFilter))
              ]
              } else {
              result = h('span', {}, this.$t('card.notSpecified'))
              }
              return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.SpecifyWeek'))
          }
        },
    ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false
    }
  },
  components: {
    cardResult,
    deviceSchedules
  },
  methods: {
    handlerTab (name) {
      this.tabVal = name
      if (this.tabVal === 'tab2') {
        this.getDataList()
      }
      this.clearLoading()
      this.clearData()
    },
    // 初始化
    init (ids) {
      this.visible = true
      this.ids = ids
    },
    // 提交数据
    screenSwitchSubmit (status) {
      this.clearData()
      if (this.ids.length > 0) {
        if (status === true) {
          this.on_loading = true
        } else {
          this.off_loading = true
        }
        this.resultData = []
        this.resultData = this.ids.map(data => {
          return {deviceId: data, loading: true, _type:"loading"}
        })
        this.ids.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/card/set/screenSwitch'),
            method: 'post',
            data: this.$http.adornData({'ids': [item], 'status': status}, false)
          }).then(({data}) => {
            if (data && data.code == 0) {
              // this.resultData = data.data
              this.$nextTick(() => {
                let element = data.data[0];
                this.resultData.forEach((item, index) => {
                  if (element.deviceId == item.deviceId) {
                    element.loading = false
                    this.$set(this.resultData, index, element)
                    // this.resultData.splice(index, 1, element)
                  }
                })
              })
              // this.resultData.push(data.data[0])
            } else {
              // this.$Message.error(data.msg);
            }
          })
        })
        if (status === true) {
          this.on_loading = false
        } else {
          this.off_loading = false
        }
        /* this.$http({
          url: this.$http.adornUrl('/card/set/screenSwitch'),
          method: 'post',
          data: this.$http.adornData({'ids': this.ids, 'status': status}, false)
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg);
          }
          if (status === true) {
            this.on_loading = false
          } else {
            this.off_loading = false
          }
        }) */
      }
    },
    // 查询定时
    getTimedScreening () {
      this.clearData()
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getTimedScreening'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
            if (data && data.code == 0) {
              this.timedScreen = data.data
            } else {
              this.$Message.error(data.msg)
            }
            this.modal_loading = false
        })
      }
    },
    //清除定时
    clearTime(){
      this.clearData()
      if (this.ids.length > 0) {
        this.clear_loading = true
        this.$http({
          url: this.$http.adornUrl("/card/set/clearTimeSwitch"),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.clearTimeList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.clear_loading = false
        })
      }
    },
    // 添加保存定时
    addOrUpdateHandle (id) {
      this.setTimingVisible = true
      this.clearData()
      this.$nextTick(() => {
        // 传入0表示当前是一个定时开关任务
        this.$refs.deviceSchedules.init(0, id)
      })
    },
    clearData () {
      this.timedScreen = []
      this.resultData = []
      this.schedulesResultData = []
      this.clearTimeList = []
    },
    clearLoading() {
      this.modal_loading=false
      this.on_loading=false
      this.off_loading=false
      this.clear_loading=false
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/schedules/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
          'type': 0
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/screen/schedules/delete'),
            method: 'post',
            data: this.$http.adornData(id, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 设置定时
    setTimeHandle (id) {
      if (this.ids.length > 0) {
        this.schedulesResultData = []
        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading = true;
          }
        })
        this.$http({
          url: this.$http.adornUrl('/card/set/schedules'),
          method: 'post',
          data: this.$http.adornData({
            'id': id,
            'deviceIds': this.ids
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.schedulesResultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.dataList.map(item => {
            if (item.id === id) {
              item.timeLoading = false;
            }
          })
        })
      }
    },
    /**
     * 根据星期数字获取对应的国际化,返回字符串
     * 
     */
     getWeekDay(week) {
  
        let weelStr = ''
        if (week.length > 0) {
          week.forEach(item => {
            switch (item) {
          case 1:
            weelStr+=this.$t('common.Monday')+','
            break
          case 2:
            weelStr+=this.$t('common.Tuesday')+','
            break
          case 3:
            weelStr+=this.$t('common.Wednesday')+','
            break
          case 4:
            weelStr+=this.$t('common.Thursday')+','
            break
          case 5:
            weelStr+=this.$t('common.Friday')+','
            break
          case 6:
            weelStr+=this.$t('common.Saturday')+','
            break
          case 7:
            weelStr+=this.$t('common.Sunday')
            break
          default:
            weelStr=''
        }
          })
        
          weelStr=weelStr.substring(0,weelStr.length-1)
        }
        return weelStr
  }

  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.clearData()
        this.clearLoading()
        this.$emit('refreshDataList')
      }
    }
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
  },
}
</script>
<style scoped>
.schedules {
  margin-top: -25px;
}
.schedules ul {
  list-style: none;
  float: left;
}
</style>
