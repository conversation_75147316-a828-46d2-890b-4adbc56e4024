<template>
  <div>
    <Modal height="400" v-model="visible" width="1000">
      <p slot="header" style="text-align: center;font-size: 20px;">
        <span>{{ $t("operation.hardwareStatus") }}</span>
      </p>
      <div class="schedules">
        <Loading :loadBoolValue="loadBool"></Loading>
      <Tabs @on-click="oldNamePD" ref="tabs">
        <TabPane :label="$t('hardware.restoreParam')" name="tab3">
          <Alert type="success" show-icon> {{$t("hardware.timeQuery")}}  </Alert>
            <Row>
              <Col span="9">
                <Input v-model="fuzzy" :placeholder="$t('common.enterCardPackage')" style="width: 300px" />
              </Col>
              <Col span="11">
                <Button type="success" icon="ios-search-outline" @click="queryType()">{{$t('common.query')}}</Button>
              </Col>
              <Col span="4">
                <Button type="primary" icon="ios-cloud-upload-outline"  @click="ZIP()">{{ $t("file.upload") }}ZIP</Button>
                <device-zip v-if="visible1" ref="deviceZip" @queryType="oldNamePD"></device-zip>
              </Col>
            </Row>
            <div style="height:10px;"></div>
            <Table stripe :columns="dataColumns" :data="pageLists" :max-height="tableHeight" :loading="dataListLoading">
              <template slot-scope="{ row }" slot="filePath">
                <Button type="primary" size="small" icon="ios-cloud-download">
                  <a style="color:white;" target="_blank" :href="downloadUrl + row.fileId">{{$t('file.download')}}</a>
                </Button>
              </template>
              <template slot-scope="{ row }" slot="fileId">
                <Button v-if="ids.length === 0" type="primary" disabled size="small"><Icon type="md-refresh" />
                  {{ $t("card.notChosenCard") }}</Button>
                <Button v-else type="primary" size="small" @click="restore(row)"><Icon type="md-refresh" />{{$t('hardware.restore')}}</Button>
              </template>
              <template slot-scope="{ row }" slot="fileSize">
                <span>{{ row.fileSize | filterType }}</span>
              </template>
              <template slot-scope="{ row }" slot="deleteFile">
                <Button type="error" size="small" style="" @click="toDelete(row)"><Icon type="md-close" />{{ $t("common.delete") }}</Button>
              </template>
            </Table>
            <div class="schedules4"></div>
            <Row>
              <Col span="24">
                <Page style="float: right" :total="page.totalCount" :current="page.currPage" :page-size="page.pageSize"
                  show-elevator show-sizer :page-size-opts="[5]" show-total @on-change="currentChangeHandle"
                  @on-page-size-change="sizeChangeHandle"/>
              </Col>
            </Row>
            <div class="schedules4"></div>

        </TabPane>
        <TabPane :label="$t('hardware.backupParameter')" name="tab2">
            <Button :loading="backup_loading" type="success" @click="PostCardSystemConfig()">{{$t('hardware.akeyBackup')}}</Button>
            <Divider style="margin-top: 5px" />
            <div style="height:380px;overflow-y: auto;" >
            <div  v-for="(item, index) in backupList" :key="index">
              <div v-if="item.deviceId">{{ item.deviceId }}</div>
              <div v-else-if="item.cardId">{{ item.cardId }}</div>
              <div style="color:green" v-if="item._type==='DataCallback' || item._type==='success'">
                  <Alert type="success" show-icon>{{$t('hardware.BackingUp')}}！！！</Alert>
              </div>
              <div style="color:green" v-else-if="item._type==='Received'">
                  <Alert type="success" show-icon>{{$t('hardware.BackingUp')}}, {{$t('hardware.backupSuccessful1')}}！！！</Alert>
              </div>
              <div v-else-if="item._type==='ok'" style="color:green">
                  <Alert type="success" show-icon>{{$t('hardware.backupSuccessful')}}！！！</Alert>
              </div>
              <div style="text-align:center;" v-else-if="item._type !== 'success'">
                <Alert type="error" show-icon>{{ item.msg }}</Alert>
              </div>
              <Divider />
            </div>
          </div>
        </TabPane>
        <TabPane :label="$t('hardware.hardwareStatus')" name="tab1">
          <div style="overflow: hidden;">
            <Button :loading="modal_loading" type="success" @click="getTimedScreening()">{{ $t("hardware.hardwareStatus") }}</Button>
            <Button :loading="modal_recCardNum_loading" type="success" @click="getRecCardNum()">{{ $t("operation.NumberOfCardsReceived") }}</Button>
            <Divider style="margin-top: 5px" />
          </div>
          <div v-if="timedScreen.length > 0">
            <cardResult :ids="ids" :resultData="timedScreen" :cardItemWidth="900" :isQuery="true" :tableHeight="200"
                  :isTable="true" tableFieldNameLv1='result' :tableColumns='dataHardWareColumns'></cardResult>
          </div>
          <div v-if="recCardNum.length > 0">
            <cardResult :ids="ids" :resultData="recCardNum" :cardItemWidth="900" :isQuery="true"
              :resultItem="[{text: 'operation.NumberOfCardsReceived', name: 'receCardNum'}]"></cardResult>
          </div>
        </TabPane>
      </Tabs>
      </div>
     <div slot="footer" style="text-align: left;">
            <span>
                <Alert v-if="ids.length === 0" type="error" show-icon>{{$t('hardware.selectionCard')}}！</Alert>
              <Alert v-else type="success" show-icon
                >{{ $t("tips.cardSelected") }}:</Alert
              >
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
    </Modal>
    <div v-if="visibleBar">
          <div class="adv" v-if="!isMinimize">
            <!--  最大化 -->
            <svg style="cursor: pointer; vertical-align: middle" @click="handleMinimize(isMinimize)" width="20px"
              height="20px" aria-hidden="true">
              <use xlink:href="#zuixiaohua"></use>
            </svg>
            <span style="text-align: center">{{$t('hardware.parameterRecovery')}}</span
            >
            <Divider />
            <div style="margin-top: 10px;overflow-y: auto;max-height:200px;">
              <div id="progress" v-html="protocol">
                <!-- html注入进度条界面 -->
              </div>
            </div>
          </div>
          <!--  最小化 -->
          <div class="adv" v-if="isMinimize" style="width: 250px; height: 40px">
            <svg style="cursor: pointer; vertical-align: middle" width="20px"
              @click="handleMinimize(isMinimize)" height="20px" aria-hidden="true">
              <use xlink:href="#zuidahua"></use>
            </svg>
            <span style="text-align: center">{{$t('hardware.waitingRecover')}}</span>
            <Divider />
          </div>
        </div>
  </div>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import Loading from '@/utils/loading';
import deviceZip from "../device-zip.vue";
export default {
  data () {
    return {
      restoreLoading: false,
      fuzzy: "", //模糊查询
      downloadUrl: this.$http.adornUrl("/sys/file/download/"),
      ids: [],
      dataHardWareColumns: [
        {title: this.$t('card.version'), key: 'version', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.version'))
            }
        },
        {title: this.$t('card.cardVoltage'), key: 'cardVoltage', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.cardVoltage'))
            }
        },
        {title: this.$t('card.externalVoltage1'), key: 'externalVoltage1', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.externalVoltage1'))
            }
        },
        {title: this.$t('card.externalVoltage2'), key: 'externalVoltage2', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.externalVoltage2'))
            }
        },
        {title: this.$t('card.externalVoltage3'), key: 'externalVoltage3', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.externalVoltage3'))
            }
        },
        {title: this.$t('card.externalVoltage4'), key: 'externalVoltage4', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.externalVoltage4'))
            }
        },
        {title: this.$t('card.humidity'), key: 'humidity', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.humidity'))
            }
        },
        {title: this.$t('card.temperature'), key: 'temperature', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.temperature'))
            }
        },
        {title: this.$t('card.smokeWarning'), key: 'smoke', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.smokeWarning'))
            }
        },
        {title: this.$t('card.doorOpen'), key: 'doorOpened', width: 120,
            renderHeader:(h)=>{
                return h('div',this.$t('card.doorOpen'))
            }
        },
      ],
      modal_loading: false,
      visible: false,
      timedScreen: [],
      backup_loading: false,
      backupList: [],
      visible1: false,
      dataListLoading: false, //查询所有备份参数
      pageLists: [],
      page:{
        totalPage: 0, // 总页数
        currPage: 1, //  当前页
        pageSize: 5, // 页容量
        totalCount: 0, // 总条数
      },
      dataColumns: [
        {
          title: this.$t("file.download"),
          key: "filePath",
          slot: "filePath",
          align: "center",
          width: 120,
          renderHeader:(h)=>{
              return h('div',this.$t('file.download'))
          }
        },
        {
          title: this.$t('hardware.restore'),
          key: "fileId",
          slot: "fileId",
          align: "center",
          width: 170,
          renderHeader:(h)=>{
              return h('div',this.$t('hardware.restoreParam'))
          }
        },
        {
          title: this.$t('hardware.namePackage'),
          key: "fileName",
          align: "center",
          width: 226,
          tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('hardware.namePackage'))
          }
        },
        {
          title: this.$t("file.TheSize"),
          key: "fileSize",
          slot: "fileSize",
          align: "center",
          width: 120,
          tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('file.TheSize'))
          }
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          width: 173,
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
        {
          title: this.$t("common.delete"),
          key: "fileId",
          slot: "deleteFile",
          align: "center",
          renderHeader:(h)=>{
              return h('div',this.$t('common.delete'))
          }
        },
      ],
      websocket: "",
      BoolClear: true,  //关闭界面时是否关闭websocket
      loadBool: false,
      protocol: "",
      visibleBar: false,
      isMinimize: false,
      timer: "",
      time: 60,
      modal_recCardNum_loading: false,
      recCardNum: []
    }
  },
  components: {
    deviceZip,
    Loading,
    cardResult
  },
  methods: {
    // 初始化
    init (ids) {
      this.BoolClear= true
      this.timer = ""
      this.visible = true
      this.ids = ids
      this.oldNamePD("tab3");
      if ("WebSocket" in window) {
        this.websocket = new WebSocket(
          `${window.SITE_CONFIG.WSURL}/websocket/${this.$cookie.get('token')}`
        );
      }
      this.websocket.onopen = function () {
         //console.log('连接成功')
      };
      this.websocket.onerror = function () {
         //console.log('连接出错')
      };
      this.websocket.onclose = function () {
         //console.log('退出连接')
      };
    },
    oldNamePD (name){
      if(name === "tab3"){
          this.queryType()
      }
      this.backup_loading = false
      this.modal_loading = false
      this.modal_RS5_loading = false
    },
    restore(row) { //恢复屏参
      this.timer = ""
      this.restoreLoading = true
      var _successTemp = [];
      var receivedTemp = [];
      var errorTemp = [];
      this.loadBool = true
      this.$http({
        url: this.$http.adornUrl("/card/set/DownloadCardSystemConfig"),
        method: "post",
        data: this.$http.adornData({"cardIds": this.ids, "fileId": row.fileId})
      }).then(({data}) => {
          for (let i = 0; i < data.data.length; i++) {
            const element = data.data[i];
            if (element.success === true) {
              var temp = {
                commandId: element.deviceId,
                type: "",
                progress: ""
              };
              _successTemp.push(temp);
            } else if (element._type === 'Received') {
              receivedTemp.push(element)
              /* this.$Notice.open({
                  title: this.$t("common.tips"),
                  desc: this.$t('hardware.backupSuccessful1'),
                  duration: 3,
              }); */
            } else { // 返回失败的卡 不放入进度条
              errorTemp.push(element)
              /* this.$Notice.error({
                title: this.$t("common.tips"),
                desc: data.msg.cardId + ": "+this.$t('hardware.restoreCancel'),
                duration: 3,
              }); */
            }
          }
          this.restoreLoading = false
          this.loadBool = false
          if (receivedTemp.length > 0) {
            var errorHtml = '<div style="height: 400px; overflow-y: auto;">';
            for (let i = 0; i < receivedTemp.length; i++) {
              const element = receivedTemp[i];
              errorHtml += '<div style="margin-bottom: 5px">';
              errorHtml += '<span style="display:inline-block;width: 130px;font-weight:bold;">' + element.deviceId + '</span>';
              errorHtml += '<span style="color: green">' + this.$t('hardware.backupSuccessful1') +'</span>';
              errorHtml += '</div>';
            }
            errorHtml += '</div>';
            // 返回失败的卡 不放入进度条
            this.$Notice.open({
              title: this.$t("common.tips"),
              desc: errorHtml,
              duration: 0,
            });
          }
          if (errorTemp.length > 0) {
            var errorHtml = '<div style="height: 400px; overflow-y: auto;">';
            for (let i = 0; i < errorTemp.length; i++) {
              const element = errorTemp[i];
              errorHtml += '<div style="margin-bottom: 5px">';
              errorHtml += '<span style="display:inline-block;width: 130px;font-weight:bold;">' + element.deviceId + '</span>';
              errorHtml += '<span style="color: red">' + element.msg +'</span>';
              errorHtml += '</div>';
            }
            errorHtml += '</div>';
            // 返回失败的卡 不放入进度条
            this.$Notice.error({
              title: this.$t("common.tips"),
              desc: errorHtml,
              duration: 0,
            });
          }
          if(_successTemp.length > 0){
            var text = "";
              for (var i in _successTemp) {
                text +=
                  '<div  style="margin-bottom: 3px">' + _successTemp[i].commandId;
                text += "<div>";
                text +=
                  '<div data-v-e81752bc="" class="ivu-alert ivu-alert-success">' +
                  '<span class="ivu-alert-message">' +
                  this.$t('hardware.readyRecovery')  +
                  "</span>" +
                  '<span class="ivu-alert-desc"></span>' +
                  "</div>";

                text += "</div>";
                text += "</div>";
              }
            this.protocol = text;
            this.ProgressBarStatus( _successTemp);
          // else{
          //   this.$Message.error(this.$t('hardware.tryAgain'));
          // }
        }
      })
    },
    ProgressBarStatus( successTemp) {
      this.time = 60;
      this.timer = setInterval(() => {
        this.timeJS();
      }, 1000);
      var _this = this;
      var _successTemp = successTemp;
      // 生成进度条
      this.BoolClear = false
      this.visible = false;
      this.visibleBar = true;
      this.websocket.onmessage = function (event) {
        _this.time = 60;
        var json = JSON.parse(event.data);
        for (var i in _successTemp) {
          if (_successTemp[i].commandId === json.commandId.split("_")[0]) {
            if (json.type === "Success") {//成功删除
                _this.$Notice.success({
                        title: _this.$t("common.tips"),
                        desc: _successTemp[i].commandId + ": "+_this.$t('hardware.recoveryComplete'),
                        duration: 3,
                      });
                _successTemp.splice(i, 1)
            }else{ //替换
              var temp = {
                commandId: json.commandId.split("_")[0],
                type: json.type,
                progress: json.progress
              };
                _successTemp[i] = temp
              }
              if (_successTemp.length === 0) {
                _this.$Notice.success({
                  title: _this.$t("common.tips"),
                  desc: _this.$t('hardware.afterRecovery'),
                  duration: 3,
                });
                setTimeout(_this.clearProgress(), 3000);
                window.clearTimeout(_this.timer)
              }
              break;
          }
        }
        var text = "";
              for (var j in _successTemp) {// 分发到进度条
              text +=
                '<div  style="margin-bottom: 3px">' + _successTemp[j].commandId;
              text += "<div>";
                  text +=
                    '<div data-v-e81752bc="" class="ivu-alert ivu-alert-warning">' +
                    '<span class="ivu-alert-message">' +
                    _this.$t('hardware.Recovering')  +
                    "</span>" +
                    '<span class="ivu-alert-desc"></span>' +
                    "</div>";
              text += "</div>";
              text += "</div>";
            }
            _this.protocol = text;
      };
    },
    timeJS() {
      this.time--;
      if (this.time == 0) {
        this.$Message.success(this.$t('hardware.timesOut'));
        this.clearProgress();
      }
    },
    clearProgress() {
      this.websocket.close();
      this.visibleBar = false;
    },
    // 最大化最小化
    handleMinimize(isMinimize) {
      this.isMinimize = !isMinimize;
    },
    queryType() { //查询所有备份参数
      this.pageLists = []
      this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl("/screen/upgrade/getScreenParameters"),
          method: "post",
          params: this.$http.adornParams({
            fileName:this.fuzzy,
            type: 3,
            page: this.page.currPage,
            limit: this.page.pageSize,
          }),
        }).then(({ data }) => {
          this.page = data.page;
          this.pageLists = data.page.list;
          this.dataListLoading = false;
        });
    },
    // 当前页
    currentChangeHandle(val) {
      this.page.currPage = val;
      this.queryType();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.page.pageSize = val;
      this.queryType();
    },
    ZIP() {
      this.visible1 = true;
      this.$nextTick(() => {
        this.$refs.deviceZip.init(3);
      });
    },
    PostCardSystemConfig(){ //一键备份
      if (this.ids.length > 0) {
        this.backupList  = []
        this.backup_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/PostCardSystemConfig'),
          method: 'post',
          data: this.ids
        }).then(({ data }) => {
          if (data && data.code == 0) {
            this.backupList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.backup_loading = false
          this.BackupReply()
        })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    BackupReply(){//备份回复
      var _this = this
      this.websocket.onmessage = function (event) {
        var json = JSON.parse(event.data);
        for (var i in _this.backupList) {
          if (_this.backupList[i].commandId === json.commandId) {
            if(json.progress === 100  || json.progress === "100"){
              _this.backupList[i]._type = "ok"
            }
              break;
          }
        }

      }
    },
    /**
     * 查询硬件状态
     */
    getTimedScreening () {
      this.timedScreen = []
      // 批量查询
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getHardware'),
          method: 'post',
          data: this.ids
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.timedScreen = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false
        })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    /**
     * 查询接收卡数量
     */
    getRecCardNum () {
      this.recCardNum = []
      // 批量查询
      if (this.ids.length > 0) {
        this.modal_recCardNum_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getRecCardNum'),
          method: 'post',
          data: this.ids
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.recCardNum = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_recCardNum_loading = false
        })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    toDelete(row) {
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          // 删除
          this.$http({
            url: this.$http.adornUrl("/screen/upgrade/deleteGeneric"),
            method: "post",
            data: this.$http.adornData([row.fileId], false),
          }).then(({ data }) => {
              this.queryType("tab1");
          });
        },
      });
    },
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.$refs.tabs.activeKey = "tab3";
          this.timedScreen = []
          this.fuzzy = ""
          this.modal_loading = false
          this.modal_RS5_loading = false
          this.backupList = []
          this.backup_loading = false
          if(this.BoolClear){
            this.websocket.close();
          }
          this.$emit('refreshDataList')
        }, 200);
      }
    }
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  }
}
</script>
<style scoped>
.schedules {
  height: 480px;
  overflow-x: hidden;
  overflow-y: auto;
}
.schedules4 {
  height: 10px;
}
.adv {
  position: fixed;
  right: 10px;
  bottom: 10px;
  border-radius: 2%;
  background-color: rgb(255, 255, 255);
  width: 250px;
  height: 250px;
  padding: 5px;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
