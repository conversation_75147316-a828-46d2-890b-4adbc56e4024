<template>
  <div>
    <Modal v-model="visible" width="900" height="500">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{$t('operation.customSwitch')}}</span>
    </p>
      <Alert type="warning" show-icon ><b class="tip">{{$t('tips.customSwitchTip')}}</b></Alert>
    <Tabs :value="tabVal" @on-click="handlerTab">
      <TabPane :label="$t('operation.screenSwitch')" name="tab1">
        <div class="functionSwitch">
          <Button type="success"  :loading="function_on_loading" @click="enableCustomSwitchFunction(true)">{{$t('program.open')}}</Button>
          <Button type="warning"  :loading="function_off_loading" @click="enableCustomSwitchFunction(false)">{{$t('program.close')}}</Button>
          <Button type="primary"  :loading="function_query_loading" @click="queryCustomSwitchFunction()">{{$t('common.query')}}</Button>
        </div>
        <Form :label-width="20" label-position="left">
          <FormItem>
            <RadioGroup v-model="switchType"   @on-change="changeOption">
              <Radio label="1">
                <span>{{ $t('operation.screenSwitch') }}</span>
              </Radio>
              <Radio label="2">
                <span>{{ $t('screen.otherSwitch') }}</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem>
            <Button type="success"  :loading="on_loading" @click="customSwitchSubmit(true)">{{$t('program.open')}}</Button>
            <Button type="warning" style="margin-left: 20px" :loading="off_loading" @click="customSwitchSubmit(false)">{{$t('program.close')}}</Button>
            <Button type="primary" style="margin-left: 20px" :loading="query_loading" @click="queryCustomSwitch()">{{$t('common.query')}}</Button>
          </FormItem>
          <div v-if="enableCustomSetData.length > 0" style="height: 235px;overflow-y: auto">
            <cardResult :ids="ids" :resultData="enableCustomSetData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
          </div>
          <div v-if="customSwitchResultData.length > 0" style="height: 235px;overflow-y: auto">
            <cardResult :ids="ids" :resultData="customSwitchResultData" :cardItemWidth="850 / 2 - 50" :isQuery="true"
                :resultItem="[{text: 'screen.customSwitchFunction',name: 'screenSwitch', suffix: ':',
                resultSet: [{value: true, name: 'sys.enable', type: 'i18n'}, {value: false, name: 'sys.notOpen', type: 'i18n'}]},
                {name: 'screenOn', suffix: ':',text: switchType, 
                textCond:[{value: 1, text: 'operation.screenSwitch'}, {value: 2, text: 'operation.customSwitch'}],
                resultSet: [{value: true, name: 'sys.open', type: 'i18n'}, {value: false, name: 'sys.close', type: 'i18n'}],
                }]"/>
          </div>
        </Form>
      </TabPane>
      <TabPane :label="$t('operation.timingSwitch')" name="tab2">
        <Alert type="warning" show-icon><b class="tip">{{$t('common.screenDuring')}}</b></Alert>
        <Form :inline="true" @keyup.enter.native="getDataList()">
          <FormItem>
            <Input size="large" v-model="name" :placeholder="$t('common.name')"></Input>
          </FormItem>
          <FormItem>
            <Button style="margin-right:6px" @click="getDataList()"  size="large">
              <div style="margin:3px 8px">{{$t('common.query')}}</div>
            </Button>
            <Button style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
              <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
            </Button>
            <RadioGroup v-model="switchType" type="button" size="large"  style="float: right">
              <Radio label="1">
                <span>{{ $t('operation.screenSwitch') }}</span>
              </Radio>
              <Radio label="2" >
                <span>{{ $t('screen.otherSwitch') }}</span>
              </Radio>
            </RadioGroup>
          </FormItem>
        </Form>
        <Table border :columns="dataConlums" :data="dataList"
               :loading="dataListLoading" style="width: 100%" :max-height="300" ref="selection">
          <template slot-scope="{ row, index }" slot="operation">
            <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading" @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
            <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
            <Button type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
          </template>
        </Table>
        <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
              show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
              @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
        <div style="height: 95px;overflow-y: auto;clear: both;">
          <div v-if="schedulesResultData.length > 0">
            <cardResult :ids="ids" :resultData="schedulesResultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </div>
      </TabPane>
      <TabPane :label="$t('operation.queryOrClearTiming')" name="tab3">
        <Form style="overflow: hidden;" :label-width="30" label-position="left">
          <FormItem>
            <RadioGroup v-model="switchType" @on-change="changeOption">
              <Radio label="1">
                <span>{{ $t('operation.screenSwitch') }}</span>
              </Radio>
              <Radio label="2">
                <span>{{ $t('screen.otherSwitch') }}</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem>
            <!-- <Button  type="primary" @click="timedScreening()">{{$t('card.setTiming')}}</Button> -->
            <Button :loading="modal_loading"  type="success" @click="getTimedScreening()">{{$t('card.getTiming')}}</Button>
            <Button type="warning" :loading="clear_loading" @click="clearTime()">{{$t('common.removeTiming')}}</Button>
          </FormItem>
          <div style="height: 235px;overflow-y: auto">
            <div v-if="timedScreen.length > 0">
              <cardResult :ids="ids" :resultData="timedScreen" :cardItemWidth="800" :isQuery="true" :tableHeight="200"
                  :isTable="true" tableFieldNameLv1='screenTask' tableFieldNameLv2='schedules' :tableColumns='dataColumns'></cardResult>
            </div>
            <div v-if="clearTimeList.length > 0">
              <cardResult :ids="ids" :resultData="clearTimeList" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
            </div>
          </div>
        </Form>
      </TabPane>
    </Tabs>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
    <!-- 定时-->
    <device-schedules v-if="setTimingVisible" ref="deviceSchedules" @schedules-result-data="getDataList"></device-schedules>
  </div>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import deviceSchedules from './device-schedules'
export default {
  data () {
    return {
      visible: false,
      clear_loading: false,
      clearTimeList: [],
      modal_loading: false,
      on_loading: false,
      off_loading: false,
      tabVal: '',
      ids: [],
      timedScreen: [],
      setTimingVisible: false,
      dataColumns: [
        {
          title: this.$t('card.DateRange'), // 日期范围
          key: 'dateType',
          align: 'center',
          width: '200',
          render: (h, {row, index}) => {
            var result = ''
            if (row.dateType === 'Range') {
              result = [
                h('span', {}, row.startDate),
                h('span', {}, ' -- '),
                h('span', {}, row.endDate)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
            return h('div',this.$t('card.DateRange'))
          }
        },
        {
          title: this.$t('card.timeFrame'), // 时间范围
          key: 'timeType',
          align: 'center',
          render: (h, {row, index}) => {
            var result = ''
            if (row.timeType === 'Range') {
              result = [
                h('span', {}, row.startTime),
                h('span', {}, ' -- '),
                h('span', {}, row.endTime)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
            return h('div',this.$t('card.timeFrame'))
          }
        },
        {
          title: this.$t('card.SpecifyWeek'), // 星期范围
          key: 'filterType',
          align: 'center',
          width: '350',
          tooltip: true,
          render: (h, {row, index}) => {
            var result = ''
            if (row.filterType === 'Week') {
              var week=this.numberToWeek(row.weekFilter)
              result = [
                h('span', {},week + ' ')
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
            return h('div',this.$t('card.SpecifyWeek'))
          }
        },
      ],
      resultData: [],
      schedulesResultData: [],
      name: '',
      dataConlums: [
        // {type: 'selection', width: 60, align: 'center'},
        // {title: 'ID', key: 'id', width: 80, align: 'center'},
        {title: this.$t('common.name'), key: 'name', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.name'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      //打开定制开关功能
      function_on_loading:false,
      //关闭定制开关功能
      function_off_loading:false,
      //查询定制开关功能
      function_query_loading:false,
      //开关类型
      switchType:"1",
      //查询开关
      query_loading:false,
      weekDay:[],
      // 查询是否开启定制开关功能返回
      customSwitchResultData: [],
      // 设置定制开关
      enableCustomSetData: []
    }
  },
  components: {
    deviceSchedules,
    cardResult
  },
  methods: {
    handlerTab (name) {
      this.tabVal = name
      this.clearData()
      this.clearLoading()
      if (this.tabVal === 'tab2') {
        this.getDataList()
      }
    },
    // 初始化
    init (ids) {
      this.visible = true
      this.ids = ids
    },
    //开启定制开关功能
    enableCustomSwitchFunction(val){
      this.clearData()
      if (this.ids.length > 0) {
        if (val===true){
          this.function_on_loading=true
        }else {
          // this.customSwitchSubmit(false)
          this.function_off_loading=true
        }
        this.$http({
          url: this.$http.adornUrl('/card/set/enableCustomSwitchFunction'),
          method: 'post',
          data: this.$http.adornData({'cardIds': this.ids, 'enable': val}, false)
        }).then(({data}) => {
          if (data && data.code == 0){
            this.enableCustomSetData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          if (val===true){
            this.function_on_loading=false
          }else {
            this.function_off_loading=false
          }
        })

      }
    },
    //查询是否开启定制开关功能
    queryCustomSwitchFunction(){
      this.clearData()
      if (this.ids.length > 0) {
        this.function_query_loading=true
        this.$http({
          url: this.$http.adornUrl('/card/query/customSwitchFunctionState'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.customSwitchResultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.function_query_loading=false
        })

      }
    },

    //查询是否开启定制开关
    queryCustomSwitch(){
      this.clearData()
      if (this.ids.length > 0) {
        this.query_loading=true
        this.$http({
          url: this.$http.adornUrl('/card/query/customSwitchState'),
          method: 'post',
          data: this.$http.adornData({"cardIds": this.ids, "switchType":this.switchType})
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.customSwitchResultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
            this.query_loading=false
        })
      }
    },

    // 开启定制开关
    customSwitchSubmit (status) {
      this.clearData()
      if (this.ids.length > 0) {
        if (status === true) {
          this.on_loading = true
        } else {
          this.off_loading = true
        }
        this.$http({
          url: this.$http.adornUrl('/card/set/enableCustomSwitch'),
          method: 'post',
          data: this.$http.adornData({'cardIds': this.ids, 'enable': status,'switchType':this.switchType}, false)
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.enableCustomSetData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          if (status === true) {
            this.on_loading = false
          } else {
            this.off_loading = false
          }
        })
      }
    },
    // 查询定时
    getTimedScreening () {
      this.clearData()
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/customSwitchSchedules'),
          method: 'post',
          data: this.$http.adornData({"cardIds": this.ids,"switchType":this.switchType})
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.timedScreen = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false
        })
      }
    },

    // 清空定时
    clearTime (id) {
      this.clearData()
      if (this.ids.length > 0) {
        this.schedulesResultData = []
        this.dataList.map(item => {
          if (item.id === id) {
            this.clear_loading=true
          }
        })
        this.$http({
          url: this.$http.adornUrl('/card/set/customSwitchSchedules'),
          method: 'post',
          data: this.$http.adornData({
            'id': -1,
            'cardIds': this.ids,
            'switchType':this.switchType
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.clearTimeList = data.data;
          } else {
            this.$Message.error(data.msg)
          }
          this.clear_loading = false
        })
      }
    },
    // 添加保存定时
    addOrUpdateHandle (id) {
      this.setTimingVisible = true
      this.clearData()
      this.$nextTick(() => {
        // 传入4表示当前是一个定制开关定时任务
        this.$refs.deviceSchedules.init(4, id)
      })
    },
    clearData () {
      this.enableCustomSetData = []
      this.customSwitchResultData = []
      this.timedScreen = []
      this.resultData = []
      this.schedulesResultData = []
      this.clearTimeList = []
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/schedules/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
          'type': 4
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/screen/schedules/delete'),
            method: 'post',
            data: this.$http.adornData(id, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 设置定时
    setTimeHandle (id) {
      if (this.ids.length > 0) {
        this.schedulesResultData = []
        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading = true;
          }
        })
        this.$http({
          url: this.$http.adornUrl('/card/set/customSwitchSchedules'),
          method: 'post',
          data: this.$http.adornData({
            'id': id,
            'cardIds': this.ids,
            'switchType':this.switchType
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.schedulesResultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.dataList.map(item => {
            if (item.id === id) {
              item.timeLoading = false;
            }
          })
        })
      }
    },
    numberToWeek(val){
      // this.weekDay=[]
      var weekDay=[]
      for (let i = 0; i < val.length; i++) {
        switch (val[i]){
          case 1:
            weekDay.push("星期一");
            break;
          case 2:
            weekDay.push("星期二");
            break;
          case 3:
            weekDay.push("星期三");
            break;
          case 4:
            weekDay.push("星期四");
            break;
          case 5:
            weekDay.push("星期五");
            break;
          case 6:
            weekDay.push("星期六");
            break;
          case 7:
            weekDay.push("星期日");
            break;
        }
      }
      return weekDay;
    },
    changeOption(){
      this.clearData()
    },
    clearLoading(){
      this.query_loading=false
      this.off_loading=false
      this.on_loading=false
      this.clear_loading=false
      this.modal_loading=false
      this.function_on_loading=false
      this.function_off_loading=false
      this.function_query_loading=false
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.switchType = "1"
        this.clearData()
        this.clearLoading()
        this.$emit('refreshDataList')
      }
    }
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
  },
}
</script>

<style scoped>
.functionSwitch {
  height: 50px;
}
.functionSwitch button{
  margin-left: 20px;
}
.schedules {
  margin-top: -25px;
}
.schedules ul {
  list-style: none;
  float: left;
}
</style>
