<template>
  <Modal width="900" v-model="visible">
    <p slot="header" style="text-align:center">
      {{ $t('nav.客流统计V1') }}
    </p>
    <div>
      <Alert type="warning" show-icon> {{$t('employee.tips')}} </Alert>
      <Form ref="humanNumberStatisticV1Ref" :model="humanNumberStatisticV1" :label-width="80">
        <FormItem :label="this.$t('common.state')" prop="interval">
          <i-Switch v-model="humanNumberStatisticV1.enable" size="large"  true-color="#13ce66" false-color="#ff4949">
              <template #open>
                <span>{{$t('sys.open')}}</span>
              </template>
              <template #close>
                <span>{{$t('sys.close')}}</span>
              </template>
          </i-Switch>
        </FormItem>
        <FormItem>
          <Button @click="set" type="success" :loading="setLoading">{{ $t('common.set') }}</Button>
        </FormItem>
      </Form>
      <div style="height: 300px">
        <div style="height:240px;overflow-y: auto;" v-if="resultData.length > 0">
          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
      </div>


    </div>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data() {
    return {
      visible: false,
      ids: [],
      humanNumberStatisticV1:{
        enable: false,
      },
      //结果集
      resultData:[],
      setLoading:false,
    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.resultData=[]
        this.setLoading=false
        this.humanNumberStatisticV1= {
          enable: false,
        }

      }
    },
    //设置
    set() {
      this.resultData=[]
      this.$refs['humanNumberStatisticV1Ref'].validate((valid) => {
        if (valid){
          if (this.ids.length > 0){
            this.setLoading = true
            this.$http({
              url: this.$http.adornUrl('/card/set/setHumanNumberStatisticV1'),
              method: 'post',
              data: this.$http.adornData({
                'deviceIds': this.ids,
                'enable': this.humanNumberStatisticV1.enable,
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.resultData = data.data
              } else {
                this.$Message.error(data.msg)
              }
              this.setLoading = false
            })
          }
        }

      })

    },

  },
}
</script>

<style scoped>

</style>
