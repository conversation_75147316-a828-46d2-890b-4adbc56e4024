<template>
  <div>
    <Modal height="400" v-model="visible" width="900">
      <p slot="header" style="text-align:center;font-size: 20px;">
          <span>{{$t('operation.syncAndAsyncConfig')}}</span>
      </p>
      <Tabs :value="tabVal" @on-click="handlerTab" style="height: 650px">
        <TabPane :label="$t('operation.manualconfigurationorquery')" name="tab1">
          <Alert type="warning" show-icon ><b class="tip">{{$t('tips.manualconfigurationorquery1')}}</b></Alert>
              <Form>
                <FormItem style="margin-bottom: 10px">
                  <RadioGroup v-model="isAsync">
                    <Radio label="true">{{$t('cardDevice.synchronous')}}</Radio>
                    <Radio label="false">{{$t('cardDevice.asynchronous')}}</Radio>
                  </RadioGroup>
                </FormItem>
                <FormItem style="margin-bottom: 10px" v-if="isAsync=='true'">
                  <RadioGroup v-model="hdmiNumber">
                    <Radio :label="1">HDMI1</Radio>
                    <Radio :label="2">HDMI2</Radio>
                  </RadioGroup>
                </FormItem>
                <FormItem>
                  <Button type="success" :loading="AsynLoading" @click="getAsyn()">{{$t('common.query')}}</Button>
                  <Button type="primary" :loading="off_loading" @click="setAsyn()">{{$t('common.set')}}</Button>
                </FormItem>
            </Form>
            <Divider style="margin-top:-10px;"/>
          <div style="height: 400px;overflow: auto;">
           <div v-if="setAsynList.length > 0">
              <cardResult :ids="ids" :resultData="setAsynList" :cardItemWidth="850 / 2 - 50" :isQuery="false"></cardResult>
            </div>
            <div v-if="AsynList.length > 0" >
              <cardResult :ids="ids" :resultData="AsynList" :cardItemWidth="850 / 2 - 50" :isQuery="true"
                :resultItem="[{name: 'result', text: 'operation.syncAndAsyncConfig',  
                  resultSet: [{value: true , text: 'cardDevice.synchronous', type: 'i18n'},  {value: false , text: 'cardDevice.asynchronous', type: 'i18n'}]}, 
                  {name: 'number', 
                  resultSet: [{value: 1, text: 'HDML1', otherCond: {name: 'result', value: true } }, {value: 2, text: 'HDML2', otherCond: {name: 'result', value: true } }]}]">
              </cardResult>
            </div>
          </div>
        </TabPane>
        <TabPane :label="$t('operation.scheduledconfigurationorquery')" name="tab2">
          <Row>
              <Col span="24"><Alert type="warning" show-icon ><b class="tip">{{$t('tips.manualconfigurationorquery1')}}  {{$t('common.synchronousState')}}</b></Alert></Col>
          </Row>
          <Form :inline="true" @keyup.enter.native="getDataList()">
            <FormItem style="margin-bottom: 10px">
              <Input v-model="name" :placeholder="$t('common.name')"></Input>
            </FormItem>
            <FormItem style="margin-bottom: 10px">
              <Button style="margin-right:6px" @click="getDataList()">
                <div style="margin:3px 8px">{{$t('common.query')}}</div>
              </Button>
              <Button style="margin-right:6px" type="primary" @click="setTimedAsyn()">
                <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
              </Button>
            </FormItem>
          </Form>
          <Table border :columns="dataConlums" :data="dataList"
            :loading="dataListLoading" style="width: 100%" :max-height="300" ref="selection">
            <template slot-scope="{ row, index }" slot="operation">
                <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading" @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
                <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="setTimedAsyn(row.id)">{{$t('common.update')}}</Button>
                <Button type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
            </template>
          </Table>
          <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
          show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
          @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
          <div style="height: 150px;overflow-y: auto;clear: both;" v-if="setTimeAsynList.length > 0">
            <cardResult :ids="ids" :resultData="setTimeAsynList" :cardItemWidth="850 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </TabPane>
        <TabPane :label="$t('operation.queryTiming')" name="tab3">
          <Form>
            <Button  type="success" :loading="modal_loading" @click="getTimedAsyn()">{{$t('card.getTiming')}}</Button>
            <div style="height:10px;"></div>
            <Divider/>
          </Form>
            <div style="height: 520px;overflow: auto;" v-if="getTimeAsynList.length > 0">
              <cardResult :ids="ids" :resultData="getTimeAsynList" :cardItemWidth="800" :isQuery="true" :tableHeight="200"
                  :isTable="true" tableFieldNameLv1='creenTask' tableFieldNameLv2='schedules' :tableColumns='dataColumns'></cardResult>
            </div>
        </TabPane>
        <TabPane :label="$t('card.picture-in-picture')" name="tab4">
          <Alert type="warning" show-icon ><b class="tip">{{$t('card.pictureTip')}}</b></Alert>
          <Form  :label-width="120" label-position="left">
            <FormItem style="margin-bottom: 10px" :label="$t('card.picture-in-picture')">
              <i-Switch size="large" v-model="picture.switchOn">
                <template #open>
                  <span>{{$t('sys.open')}}</span>
                </template>
                <template #close>
                  <span>{{$t('sys.close')}}</span>
                </template>
              </i-Switch>
            </FormItem>
            <div v-if="picture.switchOn">
              <FormItem :label="$t('card.coordinate')" style="margin-bottom: 10px">
                <Row>
                  <Col span="2">
                    <span>X</span>
                  </Col>
                  <Col span="10">
                    <InputNumber class="pictureInputNum" v-model="picture.x"/>
                  </Col>
                  <Col span="2">
                    <span>Y</span>
                  </Col>
                  <Col span="10">
                    <InputNumber class="pictureInputNum" v-model="picture.y"/>
                  </Col>
                </Row>
              </FormItem>
              <FormItem style="margin-bottom: 10px" :label="$t('card.pictureSize')">
                <Row>
                  <Col span="2">
                    <span>{{$t('program.width')}}</span>
                  </Col>
                  <Col span="10">
                    <InputNumber class="pictureInputNum" v-model="picture.width"/>
                  </Col>
                  <Col span="2">
                    <span>{{$t('program.height')}}</span>
                  </Col>
                  <Col span="10">
                    <InputNumber class="pictureInputNum" v-model="picture.height"/>
                  </Col>
                </Row>
              </FormItem>
            </div>
            <FormItem>
                <Button type="success" :loading="pictureQueryLoading" @click="getPicture()">{{$t('common.query')}}</Button>
                <Button type="primary" :loading="pictureSetLoading" @click="setPicture()">{{$t('common.set')}}</Button>
            </FormItem>
          </Form>
          <Divider style="margin-top:-10px;"/>
          <Form  style="height: 430px;overflow: auto;" :label-width="30" label-position="left">
            <div v-if="pictureSetList.length > 0">
               <cardResult :ids="ids" :resultData="pictureSetList" :cardItemWidth="850 / 2 - 50" :isQuery="false"></cardResult>
            </div>
            <div v-if="pictureQueryList.length > 0">
              <cardResult :ids="ids" :resultData="pictureQueryList" :cardItemWidth="850 / 2 - 50" :isQuery="true"
                :resultItem="[{name: 'switchOn',
                  resultSet: [{value: true , text: 'card.picture-in-picture', type: 'i18n'}, {value: false , text: 'cardDevice.asynchronous', type: 'i18n'}]}]">
              </cardResult>
              <!-- <FormItem label="ID" style="margin-bottom: 10px">
                <div v-if="timed.deviceId">{{timed.deviceId}}</div>
                <div v-else-if="timed.cardId">{{timed.cardId}}</div>
              </FormItem>
              <FormItem style="margin-bottom: 10px" :loading="true" v-if="timed.success === true">
                <div style="color: green;">
                  {{timed.switchOn ? $t('card.picture-in-picture') :  $t('cardDevice.asynchronous')}}
                </div>
              </FormItem>
              <FormItem v-else-if="timed._type !== 'success'" style="margin-bottom: 10px">
                <div style="color: red;" v-if="timed.msg">{{timed.msg}}！</div>
                <div style="color: red;" v-else-if="timed.errorMessage">{{timed.errorMessage}}！</div>
              </FormItem>
              <Divider/> -->
            </div>
          </Form>
        </TabPane>
      </Tabs>
     <div slot="footer" style="text-align: left;">
            <span>
                <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
    </Modal>
    <!-- 定时-->
    <device-schedules v-if="setTimingVisible" ref="deviceSchedules" @schedules-result-data="getDataList"></device-schedules>
  </div>
</template>

<script>
import deviceSchedules from './device-schedules'
import cardResult from "@/utils/cardResult.vue"
export default {
  data () {
    return {
      picture: {
        switchOn: false,// true：画中画；false：异步
        x: 0,//画面显示x坐标
        y: 0,//画面显示y坐标
        width: 128,//显示画面宽度大小
        height: 256,//显示画面高度大小
        type: 1 //区分是画中画还是同异步  调用该接口时 始终传1
      },
      pictureQueryLoading: false,
      pictureSetLoading: false,
      pictureQueryList: [],
      pictureSetList: [],
      AsynLoading: false,
      visible: false,
      modal_loading: false,
      on_loading: false,
      off_loading: false,
      AsynList: [],
      setAsynList: [],
      setTimeAsynList: [],
      getTimeAsynList: [],
      tabVal: '',
      ids: [],
      setTimingVisible: false,
      dataColumns: [
        {
          title: this.$t('card.DateRange'), // 日期范围
          key: 'dateType',
          align: 'center',
          width: '190',
          render: (h, {row, index}) => {
            var result = ''
            if (row.dateType === 'Range') {
              result = [
                h('span', {}, row.startDate),
                h('span', {}, ' -- '),
                h('span', {}, row.endDate)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.DateRange'))
          }
        },
        {
          title: this.$t('card.timeFrame'), // 时间范围
          key: 'timeType',
          align: 'center',
          render: (h, {row, index}) => {
            var result = ''
            if (row.timeType === 'Range') {
              result = [
                h('span', {}, row.startTime),
                h('span', {}, ' -- '),
                h('span', {}, row.endTime)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.timeFrame'))
          }
        },
        {
          title: this.$t('card.SpecifyWeek'), // 星期范围
          key: 'filterType',
          align: 'center',
          width: '350',
          tooltip: true,
          render: (h, {row, index}) => {
            var result = ''
            if (row.filterType === 'Week') {
              result = [
                h('span', {}, row.week + ',')
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.SpecifyWeek'))
          }
        }
      ],
      name: '',
      dataConlums: [
        // {type: 'selection', width: 60, align: 'center'},
        // {title: 'ID', key: 'id', width: 80, align: 'center'},
        {title: this.$t('common.name'), key: 'name', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.name'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.operation'))
          }
        }
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      //是否是同步
      isAsync:"true",
      //hdmi口
      hdmiNumber:1,
    }
  },
  components: {
    deviceSchedules,
    cardResult
  },
  methods: {
    // 设置画中画
    setPicture () {
      this.clearTime()
      if (this.ids.length > 0) {
        this.pictureSetLoading = true
          this.picture.ids = this.ids;
          this.$http({
            url: this.$http.adornUrl('/card/set/setPictureSwitch'),
            method: 'post',
            data: this.$http.adornData(this.picture)
          }).then(({data}) => {
            this.pictureSetLoading = false
            if (data && data.code == 0) {
              this.pictureSetList = data.data
            } else {
              this.$Message.error(data.msg)
            }
          })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    // 查询画中画
    getPicture () {
      this.clearTime()
      if (this.ids.length > 0) {
        this.pictureQueryLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getPictureSwitch'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.pictureQueryList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.pictureQueryLoading = false
        })
      }
    },
    handlerTab (name) {
      this.tabVal = name
      if (this.tabVal === 'tab2') {
        this.getDataList()
      }
      this.clearLoading()
    },
    // 初始化
    init (ids) {
      this.visible = true
      this.ids = ids
    },
    setTimedAsyn (id) { // 设置定时同异步
      this.setTimingVisible = true
      this.$nextTick(() => {
        // 传入3表示当前是一个定时同异步任务
        this.$refs.deviceSchedules.init(3, id)
      })
    },
    clearTime () {
      this.setTimeAsynList = []
      this.getTimeAsynList = []
      this.pictureSetList = []
      this.pictureQueryList = []
    },
    // 查询定时同异步
    getTimedAsyn () {
      this.clearTime()
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getTimedAsynchronous'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.getTimeAsynList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false
        })
      }
    },
    // 设置同异步
    setAsyn () {
      this.clearAsyn()
      if (this.ids.length > 0) {
        // if (status === true) {
        //   this.on_loading = true
        // } else {
        //   this.off_loading = true
        // }
        this.off_loading=true
        this.$http({
          url: this.$http.adornUrl('/card/set/setAsynchronous'),
          method: 'post',
          data: this.$http.adornData({'cardIds': this.ids, 'status': this.isAsync,'number':this.hdmiNumber})
        }).then(({data}) => {
            // this.on_loading = false
          this.off_loading = false
          if (data && data.code === 0) {
            this.setAsynList = data.data
          } else {
            this.$Message.error(data.msg)
          }
        })
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    // 查询同异步
    getAsyn () {
      this.clearAsyn()
      if (this.ids.length > 0) {
        this.AsynLoading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getAsynchronous'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.AsynList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.AsynLoading = false
        })
      }
    },
    clearAsyn () {
      this.AsynList = []
      this.setAsynList = []
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/schedules/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
          'type': 3
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/screen/schedules/delete'),
            method: 'post',
            data: this.$http.adornData(id, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 设置定时
    setTimeHandle (id) {
      if (this.ids.length > 0) {
        this.setTimeAsynList = []
        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading = true;
          }
        })
        this.$http({
          url: this.$http.adornUrl('/card/set/schedules'),
          method: 'post',
          data: this.$http.adornData({
            'id': id,
            'deviceIds': this.ids
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.setTimeAsynList = data.data
          } else {
            this.$Message.error(data.msg);
          }
          this.dataList.map(item => {
            if (item.id === id) {
              item.timeLoading = false;
            }
          })
        })
      }
    },
    clearLoading(){
        this.AsynLoading= false
        this.modal_loading= false
        this.on_loading= false
        this.off_loading= false
        this.picture= {
          switchOn: false,
          x: 0,
          y: 0,
          width: 128,
          height: 256,
          type: 1
        }
        this.pictureQueryLoading = false
        this.pictureSetLoading = false
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.clearTime()
        this.clearAsyn()
        this.clearLoading()
        this.$emit('refreshDataList')
      }
    }
  }
}
</script>
<style scoped>
.pictureInputNum {
  margin-left: 5px;
  width: 150px;
}
.schedules {
  margin-top: -25px;
}
.schedules ul {
  list-style: none;
  float: left;
}
</style>
