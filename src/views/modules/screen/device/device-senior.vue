<template>
  <div>
    <Modal height="400" v-model="visible" width="900">
      <p slot="header" style="text-align: center; font-size: 20px">
        <span>{{ $t("operation.connConfig") }}</span>
      </p>
      <Tabs :value="tabVal" @on-click="handlerTab" style="height: 420px">
        <TabPane :label="$t('operation.volumeset')" name="tab1">
          <Alert type="warning" show-icon><b class="tip">{{ $t("common.notEnteredNotModified") }}</b></Alert>
          <Form :model="formItem" :label-width="160">
            <FormItem style="margin-bottom: 10px" :label="$t('operation.webServerAddress')">
              <Input v-model="formItem.serverURL" :placeholder="$t('common.PleaseInput')" @input="inputHeadler()"></Input>
            </FormItem>
            <FormItem style="margin-bottom: 10px" :label="$t('sys.companyId')">
              <Input v-model="formItem.companyId" :placeholder="$t('common.PleaseInput')" @input="inputHeadler()"></Input>
            </FormItem>
            <FormItem style="margin-bottom: 10px" v-if="isAuth('device:senior:realTime')" :label="$t('operation.realtimeaddress')">
              <Select v-if="!isCustom" v-model="formItem.realtimeURL" @input="inputHeadler()" style="width: 638px;display: inline-block;">
                <Option value="www.ledokcloud.com/realtime" key="www.ledokcloud.com/realtime">www.ledokcloud.com/realtime</Option>
              </Select>
              <Input style="width: 638px;display: inline-block;" v-else v-model="formItem.realtimeURL" :placeholder="$t('common.PleaseInput')" @input="inputHeadler()"></Input>
              <Checkbox style="display: inline-block;margin-right: 0px" v-model="isCustom">{{$t('alarm.custom')}}</Checkbox>
            </FormItem>
            <FormItem style="margin-bottom: 10px">
              <Button style="float: right" type="primary" @click="subSet()" :disabled="disabledSet" :loading="modal_loading">{{ $t("common.set") }}</Button>
            </FormItem>
            <div v-if="setSeniorRes.length > 0" style="height: 145px;overflow-y: auto">
              <cardResult :ids="formItem.ids" :resultData="setSeniorRes" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
            </div>
          </Form>
        </TabPane>
        <TabPane :label="$t('operation.batchquery')" name="tab2">
          <Form :label-width="30" label-position="left">
            <FormItem>
              <Button :loading="modal_loading2" type="success" @click="getTimedScreening()">{{ $t("operation.batchquery") }}</Button>
              <!-- <Button style="margin-left: 8px" @click="rem2()">{{ $t("operation.remove") }}</Button> -->
            </FormItem>
            <div style="height: 300px; overflow: auto"  v-if="timedScreen.length > 0">
             <cardResult :ids="formItem.ids" :resultData="timedScreen" :cardItemWidth="850 / 2 - 50" :isQuery="true"
                :resultItem="[{name: 'ServerURL',text: 'operation.webServerAddress'}
                ,{name: 'CompanyID',text: 'operation.thecompany', suffix: 'ID'},
                {name: 'RealtimeServer',text: 'operation.realtimeaddress'}]">
             </cardResult>
            </div>
          </Form>
        </TabPane>
        <TabPane :label="$t('operation.connectionLog')" name="tab3">
          <Button :loading="connLog_loading" type="success" @click="getConnLog()">{{ $t("operation.batchquery") }}</Button>
            <ul class="screen_ul">
              <li class="screen_ul_li" v-for="(item, index) in connLogList" :key="index">
                <div v-if="item.loading === true">
                  <p style="font-size:15px;">{{ item.deviceId }}</p>
                  <div class="demo-spin-container">
                    <Spin size="large" fix :show="item.loading"></Spin>
                  </div>
                </div>
                <div v-else>
                  <div v-if="item._type === 'error'">
                    <span style="font-size:15px;">{{ item.deviceId }}</span>
                    <div class="scree_view"><span style="color:red;font-size:20px;">{{ item.msg }}</span></div>
                  </div>
                  <div v-else>
                    <span style="font-size:15px;">{{ item.deviceId }}</span>
                    <div v-if="item.result" class="scree_view">
                      <Timeline v-for="(resItem, resIndex) in item.result" :key="resIndex">
                        <TimelineItem>
                            <p class="time">{{resItem.timeStamp | formatDate}}</p>
                            <p class="content"><b>{{ $t("operation.settingMode") }}: </b>{{resItem.source}}</p>
                            <div style="border: dashed #c0c0c0 0.5px; margin: 2px">
                              <p class="content" style="margin-left: 5px">
                                <Row>
                                  <Col span="9"><b>realtime </b></Col>
                                  <Col span="15">{{resItem.realtimeURL}}</Col>
                                </Row>
                              </p>
                              <p class="content" style="margin-left: 5px">
                                <Row>
                                  <Col span="9"><b>web</b></Col>
                                  <Col span="15">{{resItem.serverURL}}</Col>
                                </Row>
                              </p>
                              <p class="content" style="margin-left: 5px">
                                <Row>
                                  <Col span="9"><b>companyId</b></Col>
                                  <Col span="15">{{resItem.companyId}}</Col>
                                </Row>
                              </p>
                            </div>
                        </TimelineItem>
                      </Timeline>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
        </TabPane>
      </Tabs>
      <div slot="footer" style="text-align: left">
        <span>
          <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto; max-height: 42px">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in formItem.ids" :key="item" style="color: #999; font-weight: normal">{{ item }}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
import deviceSchedules from "./device-schedules";
export default {
  filters: {//配置过滤器
    formatDate: function (value) {//调用时间戳为日期显示
      let date = new Date(value)
      let y = date.getFullYear()  //获取年份
      let m = date.getMonth() + 1  //获取月份
      m = m < 10 ? "0" + m : m  //月份不满10天显示前加0
      let d = date.getDate()  //获取日期
      d = d < 10 ? "0" + d : d  //日期不满10天显示前加0
      let h = date.getHours(); //小时
      h = h < 10 ? "0" + h : h  //不满10显示前加0
      let mi = date.getMinutes(); //分
      mi = mi < 10 ? "0" + mi : mi  //不满10显示前加0
      let s = date.getSeconds(); //秒
      s = s < 10 ? "0" + s : s  //不满10显示前加0
      return y + "-" + m + "-" + d + " " + h + ":" + mi + ':' + s
    },
  },
  data() {
    return {
      tabVal: "",
      color1: "white",
      formItem: {
        serverURL: "",
        companyId: "",
        realtimeURL: "",
        ids: [],
        id: "",
      },
      setSeniorRes: [],
      disabledSet: true,
      modal_loading: false,
      modal_loading2: false,
      visible: false,
      timedScreen: [],
      model1: "",
      connLog_loading: false,
      connLogList: [],
      isCustom: false
    };
  },
  components: {
    deviceSchedules,
    cardResult
  },
  methods: {
    // 初始化
    init(ids) {
      this.tabVal = "tab1";
      this.visible = true;
      this.formItem.ids = ids;
    },
    inputHeadler() {
      if (this.formItem.serverURL.length > 0 || this.formItem.companyId.length > 0 
            || this.formItem.realtimeURL.length > 0) {
        this.disabledSet = false;
      } else {
        this.disabledSet = true;
      }
    },
    /**
     * 查询连接日志
     */
    getConnLog() {
      // 批量查询
      if (this.formItem.ids.length > 0) {
        this.connLogList = []
        if (this.formItem.ids.length == 1) {
          this.connLogList.push({deviceId: this.formItem.ids[0], loading: true})
        } else {
          this.formItem.ids.forEach(item => {
            this.connLogList.push({deviceId: item, loading: true})
          })
        }
        this.$http({
          url: this.$http.adornUrl('/card/query/getConnLog'),
          method: 'post',
          data: this.formItem.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            for (let i = 0; i < data.data.length; i++) {
              let element = data.data[i];
              this.connLogList.forEach((item, index) => {
                if (element.deviceId == item.deviceId) {
                  element.loading = false
                  this.connLogList.splice(index, 1, element)
                }
              })
            }
          } else {
            this.$Message.error(data.msg)
          }
        })
      } else {
        this.$Message.error(this.$t("tips.numberEmpty"));
      }
    },
    getTimedScreening() {
      this.timedScreen = []
      // 批量查询
      if (this.formItem.ids.length > 0) {
        this.modal_loading2 = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getSenior'),
          method: 'post',
          data: this.formItem.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.timedScreen = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading2 = false
        })
      } else {
        this.$Message.error(this.$t("tips.numberEmpty"));
      }
    },
    setSenior () {
      this.setSeniorRes = []
      this.modal_loading = true;
      this.$http({
        url: this.$http.adornUrl("/card/set/setSenior"),
        method: "post",
        data: this.$http.adornData(this.formItem),
      }).then(({ data }) => {
        if (data && data.code == 0) {
          this.setSeniorRes = data.data
        } else {
            this.$Message.error(data.msg)
        }
        this.modal_loading = false;
      });
    },
    /**
     * 设置web地址、公司ID、realtime地址
     */
    subSet() {
      // 批量提交
      if (this.formItem.ids.length > 0) {
        if(this.formItem.companyId.length > 0 || this.formItem.realtimeURL.length > 0){
          this.$Modal.confirm({
            title: this.$t("common.tips"),
            content: this.$t("common.Changing"),
            okText: this.$t("common.confirm"),
            cancelText: this.$t("common.cancel"),
            onOk: () => {
              this.setSenior();
            },
          });
        } else {
            this.setSenior();
        }
      } else {
        this.$Message.error(this.$t("tips.numberEmpty"));
      }
    },
    clearSet() {
      this.formItem.serverURL = "";
      this.formItem.companyId = "";
      this.formItem.realtimeURL = "";
    },
    /* rem2() {
      this.timedScreen = [];
    }, */
    handlerTab(name) {
      this.tabVal = name;
    },
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = "tab1";
        this.setSeniorRes = []
        this.timedScreen = [];
        this.timedScreen2 = [];
        this.disabledSet = true;
        this.modal_loading = false;
        this.modal_loading2 = false;
        this.clearSet();
        this.$emit("refreshDataList");
        this.connLogList = []
        this.connLog_loading = false;
      }
    },
  },
};
</script>
<style scoped>
.time{
  font-size: 14px;
  font-weight: bold;
  text-align: left;
}
.content{
  text-align: left;
  padding-left: 5px;
}
.screen_ul {
  height: 330px;
  overflow: hidden;
  overflow-y: auto;
}

.screen_ul_li {
  list-style: none;
  float: left;
  /* overflow: hidden;
  overflow-y: auto; */
  margin-left: 15px;
  margin-top: 10px;
  width: 400px;
  height: 300px;
  border: solid #c0c0c0 0.5px;
  background: rgb(255, 255, 255);
  text-align: center;
}

.scree_view {
  overflow: hidden;
  overflow-y: auto;
  text-align: left;
  width: 394px;
  height: 274px;
  margin: 3px 0px 5px 5px;
  padding-top: 10px;
}

.demo-spin-container {
  position: relative;
  top:120px;
  display: inline-block;
}
</style>
