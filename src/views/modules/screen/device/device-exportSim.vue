<template>
    <Modal v-model="visible" height="400" width="500">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('common.exportingSIM')}}</span>
        </p>
<!--        <Form ref="dataForm" style="height: 200px;" :label-width="80" label-position="left" label-colon-->
<!--            @keyup.enter.native="dataFormSubmit()">-->
<!--            <Alert type="warning" show-icon ><b class="tip">{{$t('common.notSelectedectMaterial')}}</b></Alert>-->
<!--            <FormItem :label="$t('group.name')">-->
<!--                <Select style="width:250px;" v-model="group" filterable clearable transfer :placeholder="$t('common.PleaseSelect') + $t('group.name')">-->
<!--                    <Option v-for="item in groupList" :value="item.id.toString()" :key="item.id">{{ item.name }} ({{item.count}})</Option>-->
<!--                </Select>-->
<!--                <Button type="primary" style="float:right;" :loading="modal_loading" @click="dataFormSubmit()">{{$t('common.exportingSIM')}}</Button>-->
<!--            </FormItem>-->
<!--        </Form>-->
        <div>
          <Alert type="warning" show-icon ><b class="tip">{{$t('common.notSelectedectMaterial')}}</b></Alert>
          <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
        </div>
        <div slot="footer">
          <Button type="primary" :loading="modal_loading" @click="dataFormSubmit()">{{$t('common.exportingSIM')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
        visible: false,
        groupList: [],
        group: '',
        modal_loading: false,
        selectGroupIds:[],
        selectGroupNodes:[],
        totalNum:0,
    }
  },
  methods: {
    // 初始化
    init (totalNum) {
        this.visible = true
        this.totalNum=totalNum
        this.selectGroupIds=[]
        this.selectGroupNodes=[]
        this.$http({
            url: this.$http.adornUrl('/sys/group/select'),
            method: 'get',
            params: this.$http.adornParams({})
        }).then(({data}) => {
            if (data && data.code === 0) {
                this.groupList = data.group
                this.getUnGroupDevice()
            } else {
                this.groupList = []
            }
        })
    },
    // 提交数据
    dataFormSubmit () {
        // this.group = this.group === undefined ? '' : this.group

        if (this.groupList.length > 0) {

          if (this.$refs.groupListTree.getSelectedNodes()[0]==null){
            this.selectGroupIds=[]
          }else {
            this.selectGroupIds=[]
            this.selectGroupNodes=[]
            var cardCount = ''
            var rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
            this.getChildrenNodes(rootNode)
            this.selectGroupNodes.map(item => {
              cardCount += item.count
            })
            //判断选中分组有没有设备
            if (cardCount !== '' && cardCount === 0) {
              this.$Message.error({
                content: this.$t('common.exportingSIMTips'),
                duration: 5
              })
              return
            }
          }

        }
        console.log(this.selectGroupIds)
        window.open(this.$http.adornUrl(`/screen/card/exportSIM?lang=${this.$i18n.locale}&token=${this.$cookie.get('token')}&group=${this.selectGroupIds}`))
    },
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
    //获取该分组及其子分组的groupId
    getChildrenNodes(rootNode){
      this.selectGroupIds.push(rootNode.id)
      this.selectGroupNodes.push(rootNode)
      var childNode=rootNode.children;
      if (childNode){
        for (var i=0; i<childNode.length; i++) {
          this.getChildrenNodes(childNode[i])
        }
      }
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
            this.group = ''
        }, 200)
      }
    }
  }
}
</script>
