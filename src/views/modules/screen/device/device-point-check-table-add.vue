<template>
  <Modal v-model="visible" width="500">
    <p slot="header" style="text-align:center">
      <span>{{ $t('screen.pointTable') }}</span>
    </p>
    <Form :model="uploadForm" style="height: 310px" >
      <Loading :loadBoolValue="load"></Loading>
      <FormItem>
        <Upload
          multiple
          :before-upload="handleUpload"
          :action="''"
          type="drag"
          accept=".xml">
          <div style="padding: 20px 0">
            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
            <p>{{$t('file.attachment1')}}</p>
          </div>
        </Upload>
        <div>
          <ul class="file-list" v-for="(list,index) in uploadForm.displayFile" :key="index">
            <li>{{$t('file.name')}}: <span style="font-size:15px;">{{ list.name }}</span>
              <Icon type="ios-close" size="20" style="float:right;" @click="uploadForm.displayFile.splice(index,1)"></Icon></li>
          </ul>
        </div>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
      <Button type="primary" @click="uploadFormSubmit">{{$t('file.upload')}}</Button>
    </div>
  </Modal>
</template>

<script>
import Loading from '@/utils/loading'
import axios from 'axios'
export default {
  data () {
    return {
      visible: false,
      load: false,
      token: this.$cookie.get('token'),
      uploadForm: {
        resourceType: 10,
        displayFile: [] // 临时数组，同时用于显示在页面
      }
    }
  },
  methods: {
    // 初始化
    init () {
      this.visible = true
    },
    // 文件上传前
    handleUpload (selectFile) {
      var type =  selectFile.type
      var ele = type.substring(0,type.lastIndexOf('/'))
      console.log(type)
      if(ele === "text"){
        if(selectFile.size > (10 * 1024 *1024)){
          this.$Message.error(this.$t('file.file')+'：'+selectFile.name + this.$t('screen.sizeMore')+ '10M!')
          this.selectFile = null //超过大小将文件清空
          return false
        }
      } else {
        this.$Message.error(this.$t('screen.picturesOrVideos'))
        this.selectFile = null //将文件清空
        return false
      }
      if (this.uploadForm.displayFile.length >= 5) {
        this.$Message.error(this.$t('file.YouCanOnlyUploadUpTo5Files'))
        return false
      }
      // 临时数组，同时用于显示在页面
      this.uploadForm.displayFile.push(selectFile)
      // this.uploadForm.displayFile = selectFile
      // this.uploadFormSubmit()
      return false
    },
    // 新增文件上传
    uploadFormSubmit () {
      if (this.uploadForm.displayFile.length > 0) {
        this.load = true
        let formData = new FormData()
        // file为后台接收参数
        // formData.append('file', this.uploadForm.displayFile)
        formData.append('type', this.uploadForm.resourceType)
        //多个文件上传
        for(var i=0; i< this.uploadForm.displayFile.length; i++){
          formData.append("file",this.uploadForm.displayFile[i]);   // 文件对象
        }

        axios.request({
          url: this.$http.adornUrl('/sys/file/upload'),
          method: 'post',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data', 'token': this.token }
        }).then(res => {
          // 上传成功处理
          if (res.data) {
            if (res.data.code === 0) {
              this.load = false
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.load = false
              this.$Message.error({
                content: res.data.msg,
                duration: 0.5,
                onClose: () => {
                  this.load = false
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }
          }
        })
      }
    },
  },
  watch: {
  'visible': function (newVal, oldVal) {
    if (newVal === false) {
      this.uploadForm = {
        name: '',
        type: '',
        resourceType: 1,
        size: '',
        displayFile: []
      }
      this.$emit('uploadPointTable', 'name2')
    }
  }
},
  components: {
    Loading
  }

}
</script>
<style scoped>
</style>
