<template>
    <Modal v-model="visible" width="800">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.clearProgram')}}</span>
        </p>
        <Form style="height: 300px;" label-position="left" label-colon>
          <FormItem>
            <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('operation.clearProgram')}}</Button>
          </FormItem>
          <div v-if="resultData.length > 0"  style="height: 235px;overflow-y: auto">
            <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </Form>
        <div slot="footer" style="text-align: left;">
            <span>
                <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
        </div>
      </div>
    </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult,
  },
  data () {
    return {
      visible: false,
      ids: [],
      //清除节目loading
      modal_loading: false,
      resultData: [],
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.modal_loading=false
        this.resultData=[]
      }
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.resultData = []
        this.$http({
          url: this.$http.adornUrl('/card/set/clearProgram'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.resultData = []
      }
    }
  }
}
</script>