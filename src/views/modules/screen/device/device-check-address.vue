<template>
  <Modal width="900" v-model="visible">
    <p slot="header" style="text-align:center">
      {{ $t('operation.checkAddress') }}
    </p>
    <div>
      <Tabs :value="tabValue" @on-click="handlerTab">
        <TabPane :label="$t('card.mediaContentReview')" name="media">
          <Alert type="warning" show-icon> {{$t('card.checkAddressTip')}} </Alert>
          <Form ref="checkAddressRef" :model="checkAddress" :label-width="80">
            <FormItem label="url">
              <Input v-model="checkAddress.url" :placeholder="this.$t('common.PleaseInput')+ 'URL'" style="width: 300px"></Input>
            </FormItem>
            <FormItem :label="this.$t('common.state')" prop="interval">
              <i-Switch v-model="checkAddress.enable" size="large"  true-color="#13ce66" false-color="#ff4949">
                  <template #open>
                    <span>{{$t('sys.open')}}</span>
                  </template>
                  <template #close>
                    <span>{{$t('sys.close')}}</span>
                  </template>
              </i-Switch>
            </FormItem>
            <FormItem>
              <Button @click="query" type="info" :loading="queryLoading">{{ $t('common.query') }}</Button>
              <Button @click="set" type="success" :loading="setLoading">{{ $t('common.set') }}</Button>
            </FormItem>
          </Form>
        </TabPane>
        <TabPane :label="$t('card.realtimeReview')" name="realtime">
          <Alert type="warning" show-icon> {{ $t('card.realtimeReviewTips') }} </Alert>
          <Form ref="realtimeCheckAddressRef" :model="realtimeCheckAddress" :label-width="80">
            <FormItem :label="this.$t('card.interval')">
              <InputNumber :min="1" v-model="realtimeCheckAddress.interval" :formatter="value => `${value}s`" :parser="value => value.replace('s', '')" />
            </FormItem>
            <FormItem :label="this.$t('common.state')">
              <i-Switch v-model="realtimeCheckAddress.enable" size="large"  true-color="#13ce66" false-color="#ff4949">
                  <template #open>
                    <span>{{$t('sys.open')}}</span>
                  </template>
                  <template #close>
                    <span>{{$t('sys.close')}}</span>
                  </template>
              </i-Switch>
            </FormItem>
            <FormItem>
              <Button @click="queryRealtime" type="info" :loading="queryRealtimeLoading">{{ $t('common.query') }}</Button>
              <Button @click="setRealtime" type="success" :loading="setRealtimeLoading">{{ $t('common.set') }}</Button>
              <Button @click="clearPrompt" type="success" :loading="clearPromptLoading">{{ $t('card.clearPrompt') }}</Button>
            </FormItem>
          </Form>
        </TabPane>
      </Tabs>
      <div style="height: 300px">
        <div style="height:240px;overflow-y: auto;" v-if="resultData.length > 0">
          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
        <div v-if="queryResultData.length>0" style="height: 240px;overflow-y: auto">
          <cardResult :ids="ids" :resultData="queryResultData" :cardItemWidth="800 / 2 - 50"
                      :isQuery="true" :resultItem="[{text:'url',name:'url',suffix:':'},
                  {text:$t('common.state'),name:'enable',suffix:':'}]" ></cardResult>
        </div>
        <div style="height:240px;overflow-y: auto;" v-if="resultRealtimeData.length > 0">
          <cardResult :ids="ids" :resultData="resultRealtimeData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
        <div v-if="queryRealtimeResultData.length>0" style="height: 240px;overflow-y: auto">
          <cardResult :ids="ids" :resultData="queryRealtimeResultData" :cardItemWidth="800 / 2 - 50"
                      :isQuery="true" :resultItem="[{text:'interval',name:'interval',suffix:':', unit: 's'},
                  {text:$t('common.state'),name:'enable',suffix:':'}]" ></cardResult>
        </div>
        <div v-if="clearPromptResultData.length>0" style="height: 240px;overflow-y: auto">
          <cardResult :ids="ids" :resultData="clearPromptResultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
      </div>


    </div>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  name: "device-check-address",
  data() {
    return {
      visible: false,
      ids: [],
      checkAddress:{
        url:'',
        enable: false,
      },
      realtimeCheckAddress: {
        interval: 60,
        enable: false,
      },
      //结果集
      resultData:[],
      setLoading:false,
      queryLoading:false,
      queryResultData:[],
      resultRealtimeData:[],
      setRealtimeLoading:false,
      queryRealtimeLoading:false,
      queryRealtimeResultData:[],
      clearPromptLoading:false,
      clearPromptResultData:[],
      tabValue:"media",
    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.tabValue='media'
        this.resultData=[]
        this.queryResultData=[]
        this.queryLoading=false
        this.setLoading=false
        this.checkAddress= {
          url: '',
          enable: false,
        }
        this.realtimeCheckAddress= {
          interval: 60,
            enable: false,
        }
        this.resultRealtimeData=[]
        this.setRealtimeLoading=false
        this.queryRealtimeLoading=false
        this.queryRealtimeResultData=[]
        this.clearPromptLoading=false
        this.clearPromptResultData=[]


      }
    },
    handlerTab (val) {
      if (val==='media'){

        this.clearPromptResultData=[]
        this.queryRealtimeResultData=[]
        this.resultRealtimeData=[]
        this.setRealtimeLoading=false
        this.queryRealtimeLoading=false
        this.clearPromptLoading=false

        this.checkAddress= {
          url: '',
          enable: false,
        }
      }else {
        this.resultData=[]
        this.queryResultData=[]
        this.realtimeCheckAddress= {
          interval: 60,
          enable: false,
        }
        this.queryLoading=false
        this.setLoading=false
      }
    },
    //设置
    set() {
      this.resultData=[]
      this.queryResultData=[]
      this.$refs['checkAddressRef'].validate((valid) => {
        if (valid){
          if (this.ids.length>0){
            this.setLoading = true
            this.$http({
              url: this.$http.adornUrl('/card/set/setCheckAddress'),
              method: 'post',
              data: this.$http.adornData(
                {
                  'deviceIds': this.ids,
                  'url': this.checkAddress.url,
                  'enable': this.checkAddress.enable,
                })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.resultData = data.data
              } else {
                this.$Message.error(data.msg)
              }
              this.setLoading = false
            })
          }
        }

      })

    },
    query(){
      this.resultData = []
      this.queryResultData = []
      this.queryLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/getCheckAddress'),
        method: 'post',
        data: this.ids,
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.queryResultData = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.queryLoading=false
      })
    },
    queryRealtime(){
      this.resultRealtimeData = []
      this.queryRealtimeResultData = []
      this.clearPromptResultData = []
      this.queryRealtimeLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/query/getRealtimeCheckAddress'),
        method: 'post',
        data: this.ids,
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.queryRealtimeResultData = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.queryRealtimeLoading=false
      })
    },
    //设置
    setRealtime() {
      this.resultRealtimeData = []
      this.queryRealtimeResultData = []
      this.clearPromptResultData = []
      this.$refs['realtimeCheckAddressRef'].validate((valid) => {
        if (valid){
          if (this.ids.length>0){
            this.setRealtimeLoading = true
            this.$http({
              url: this.$http.adornUrl('/card/set/setRealtimeCheckAddress'),
              method: 'post',
              data: this.$http.adornData(
                {
                  'deviceIds': this.ids,
                  'interval': this.realtimeCheckAddress.interval,
                  'enable': this.realtimeCheckAddress.enable,
                })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.resultRealtimeData = data.data
              } else {
                this.$Message.error(data.msg)
              }
              this.setRealtimeLoading = false
            })
          }
        }

      })

    },
    clearPrompt(){
      this.resultRealtimeData = []
      this.queryRealtimeResultData = []
      this.clearPromptResultData = []
      this.clearPromptLoading = true
      this.$http({
        url: this.$http.adornUrl('/card/set/clearPrompt'),
        method: 'post',
        data: this.ids,
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.clearPromptResultData = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.clearPromptLoading = false
      })
    },

  },
}
</script>

<style scoped>

</style>
