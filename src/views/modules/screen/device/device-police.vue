<template>
  <div>
    <Modal height="500" v-model="visible" width="950">
      <p slot="header" style="text-align: center;font-size: 20px;">
        <span>{{$t("operation.alarmSwitch")}}</span>
      </p>
      <div style="height: 380px;">
        <Alert type="warning" show-icon ><b class="tip">{{$t('setTime.y60Channels')}}</b></Alert>
        <Alert type="success">{{$t("police.notOpenSettingsNull")}}</Alert>
        <Form :model="thresholdFrom" label-colon >
          <FormItem>
            <Row>
              <Col span="6">{{$t('police.monitoringItems')}}</Col>
              <Col span="4">{{$t('police.hasBeenOpen')}}</Col>
              <Col span="7">{{$t('police.lower')}}</Col>
              <Col span="7">{{$t('police.ceiling')}}</Col>
            </Row>
            <Divider />
          </FormItem>
          <FormItem class="formItemClass">
            <Row>
              <Col span="6">{{$t('card.temperature')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.temperature"></Checkbox></Col>
              <Col span="7">
                <div v-if="thresholdFrom.temperature"><Input  size="small" class="inputClass" type="text" v-model="thresholdFrom.temperatureRange[0]"></Input>℃</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
              <Col span="7">
                <div v-if="thresholdFrom.temperature"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.temperatureRange[1]"></Input>℃</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
            </Row>
          </FormItem>
          <FormItem class="formItemClass">
            <Row>
              <Col span="6">{{$t('card.humidity')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.humidity"></Checkbox></Col>
              <Col span="7">
                <div v-if="thresholdFrom.humidity"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.humidityRange[0]"></Input>%</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
              <Col span="7">
                <div v-if="thresholdFrom.humidity"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.humidityRange[1]"></Input>%</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
            </Row>
          </FormItem>
          <FormItem  class="formItemClass">
            <Row>
              <Col span="6">{{$t('card.cardVoltage')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.voltage"></Checkbox></Col>
              <Col span="7">
                <div v-if="thresholdFrom.voltage"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.voltageRange[0]"></Input>V</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
              <Col span="7">
                <div v-if="thresholdFrom.voltage"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.voltageRange[1]"></Input>V</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
            </Row>
          </FormItem>
          <FormItem  class="formItemClass">
            <Row>
              <Col span="6">{{$t('card.externalVoltage1')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.voltage1"></Checkbox></Col>
              <Col span="7">
                <div v-if="thresholdFrom.voltage1"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.voltage1Range[0]"></Input>V</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
              <Col span="7">
                <div v-if="thresholdFrom.voltage1"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.voltage1Range[1]"></Input>V</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
            </Row>
          </FormItem>
          <FormItem  class="formItemClass">
            <Row>
              <Col span="6">{{$t('card.externalVoltage2')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.voltage2"></Checkbox></Col>
              <Col span="7">
                <div v-if="thresholdFrom.voltage2"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.voltage2Range[0]"></Input>V</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
              <Col span="7">
                <div v-if="thresholdFrom.voltage2"><Input size="small" class="inputClass" type="text" v-model="thresholdFrom.voltage2Range[1]"></Input>V</div>
                <Icon v-else type="ios-close-circle-outline" />
              </Col>
            </Row>
          </FormItem>
          <FormItem  class="formItemClass">
            <Row>
              <Col span="6">{{$t('card.doorOpen')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.doorOpened"></Checkbox></Col>
              <Col span="14">
                <div v-if="thresholdFrom.doorOpened"><Checkbox v-model="thresholdFrom.doorOpenedBool">{{$t('police.openDoorAlarm')}}</Checkbox></div>
                <span v-else>————————</span>
              </Col>
            </Row>
          </FormItem>
          <FormItem  class="formItemClass">
            <Row>
              <Col span="6">{{$t('police.haveSmoke')}}:</Col>
              <Col span="4"><Checkbox v-model="thresholdFrom.smoke"></Checkbox></Col>
              <Col span="14">
                <div v-if="thresholdFrom.smoke"><Checkbox v-model="thresholdFrom.smokeBool">{{$t('police.turnSmokeAlarm')}}</Checkbox></div>
                <span v-else>————————</span>
              </Col>
            </Row>
            <Divider />
          </FormItem>
          <FormItem  class="formItemClass">
            <Button :loading="setLoading" type="primary" @click="thresholdSet">{{$t('common.set')}}</Button>
            <Button :loading="queryLoading" type="success" @click="thresholdQuery">{{$t('common.query')}}</Button>
            <Button @click="clearList">{{$t('operation.remove')}}</Button>
          </FormItem>
        </Form>

        <div class="thresholdRead">
          <!-- 查询报警阈值 -->
          <div v-if="thresholdList.length > 0">
            <cardResult :ids="deviceIds" :resultData="thresholdList" :tableHeight="180" :resultHeight="152"
              :cardItemWidth="900 / 2 - 50" :isQuery="true"
              :resultItem="[{name: 'temperatureRange', text: 'card.temperature', suffix: ':',
                nameLv2: [{name: 'floor', text: 'police.lower'}, {name: 'ceiling', text: 'police.ceiling'}]},
                {name: 'humidityRange', text: 'card.humidity', suffix: ':',
                nameLv2: [{name: 'floor', text: 'police.lower'}, {name: 'ceiling', text: 'police.ceiling'}]},
                {name: 'voltageRange', text: 'card.cardVoltage', suffix: ':',
                nameLv2: [{name: 'floor', text: 'police.lower'}, {name: 'ceiling', text: 'police.ceiling'}]},
                {name: 'voltage1Range', text: 'card.externalVoltage1', suffix: ':',
                nameLv2: [{name: 'floor', text: 'police.lower'}, {name: 'ceiling', text: 'police.ceiling'}]},
                {name: 'voltage2Range', text: 'card.externalVoltage2', suffix: ':',
                nameLv2: [{name: 'floor', text: 'police.lower'}, {name: 'ceiling', text: 'police.ceiling'}]},
                {name: 'doorOpened', text: 'card.doorOpen', suffix: ':'},
                {name: 'smoke', text: 'police.haveSmoke', suffix: ':'}]"></cardResult>
          </div>
          <!-- 设置报警阈值 -->
          <div v-if="setThresholdList.length > 0">
            <cardResult :ids="deviceIds" :resultData="setThresholdList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
          </div>
        </div>
      </div>
      <div slot="footer" style="text-align: left;">
            <span>
                <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in deviceIds" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
    </Modal>
  </div>
</template>
<script>
import cardResult from "@/utils/cardResult.vue"
export default {
    components: {
      cardResult,
    },
  data() {
    return {
      setLoading: false,
      queryLoading:false,
      tabs: "",
      visible: false,
      deviceIds: [],
      alarmAddressFrom: { // 报警地址
        address: "",
      },
      thresholdList: [],
      setThresholdList: [],
      thresholdFrom: {  // 报警阈值
        cardIds: "",
        temperature: false,
        temperatureRange: ["",""],
        humidity: false,
        humidityRange: ["",""],
        voltage: false,
        voltageRange: ["",""],
        voltage1: false,
        voltage1Range: ["",""],
        voltage2: false,
        voltage2Range: ["",""],
        doorOpened: false,
        doorOpenedBool: false,
        smoke: false,
        smokeBool: false
      }
    };
  },
  methods: {
    init(ids) {
      this.deviceIds = ids;
      this.visible = true;
    },
    thresholdQuery () { // 阈值查询
      this.queryLoading=true
      this.clearList2()
      this.$http({
        url: this.$http.adornUrl("/card/query/queryThreshold"),
        method: "post",
        data: this.deviceIds,
      }).then(({data}) => {
        if (data && data.code == 0) {
          if (data.data.length > 0) {
            this.thresholdList = this.deviceIds.map(data => {
              return {deviceId: data, loading: true}
            })
            for (let i = 0; i < data.data.length; i++) {
                let element = data.data[i];
                this.thresholdList.forEach((item, index) => {
                    if (element.deviceId == item.deviceId) {
                        element.loading = false
                        this.thresholdList.splice(index, 1, element)
                    }
                })
            }
          }
          // this.thresholdList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.queryLoading=false
      })
    },
    thresholdSet () { //阈值设置
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("police.sureToSetAlarmThresholds"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
         this.setThresholdSet()
        },
      });

    },
    setThresholdSet(){
       var thr = this.thresholdFrom
      this.clearList2()
      //当点击开启时 1.非空判断 2.非数字判断 3.上下限判断
      if (thr.temperature) {
        if (thr.temperatureRange[0] =="" || thr.temperatureRange[1] =="" ) {
          this.$Message.error(this.$t("police.upperAndLowerEmpty")); return;
          } else {
            if (isNaN(thr.temperatureRange[0]) || isNaN(thr.temperatureRange[1])) {
              this.$Message.error(this.$t("police.numEmpty")); return;
              } else {
                    if (parseFloat(thr.temperatureRange[0]) >= parseFloat(thr.temperatureRange[1])) { //下限>上限 返回true 就return
                      this.$Message.error(this.$t("police.upperGreaterLower")); return;
                    }
                }
            }
      }
      if (thr.humidity) {
        if (thr.humidityRange[0] =="" || thr.humidityRange[1] =="" ) {
          this.$Message.error(this.$t("police.upperAndLowerEmpty")); return;
          } else {
            if (isNaN(thr.humidityRange[0]) || isNaN(thr.humidityRange[1])) {
              this.$Message.error(this.$t("police.numEmpty")); return;
              } else {
                    if (parseFloat(thr.humidityRange[0]) >= parseFloat(thr.humidityRange[1])) {
                      this.$Message.error(this.$t("police.upperGreaterLower")); return;
                    }
                }
            }
      }
      if (thr.voltage) {
        if (thr.voltageRange[0] =="" || thr.voltageRange[1] =="" ) {
          this.$Message.error(this.$t("police.upperAndLowerEmpty")); return;
          } else {
            if (isNaN(thr.voltageRange[0]) || isNaN(thr.voltageRange[1])) {
              this.$Message.error(this.$t("police.numEmpty")); return;
              } else {
                    if (parseFloat(thr.voltageRange[0]) >= parseFloat(thr.voltageRange[1])) {
                    this.$Message.error(this.$t("police.upperGreaterLower")); return;
                    }
                }
            }
      }
      if (thr.voltage1) {
        if (thr.voltage1Range[0] =="" || thr.voltage1Range[1] =="" ) {
          this.$Message.error(this.$t("police.upperAndLowerEmpty")); return;
          } else {
            if (isNaN(thr.voltage1Range[0]) || isNaN(thr.voltage1Range[1])) {
              this.$Message.error(this.$t("police.numEmpty")); return;
              } else {
                    if (parseFloat(thr.voltage1Range[0]) >= parseFloat(thr.voltage1Range[1])) {
                      this.$Message.error(this.$t("police.upperGreaterLower")); return;
                    }
                }
            }
      }
      if (thr.voltage2) {
        if (thr.voltage2Range[0] =="" || thr.voltage2Range[1] =="" ) {
          this.$Message.error(this.$t("police.upperAndLowerEmpty")); return;
          } else {
            if (isNaN(thr.voltage2Range[0]) || isNaN(thr.voltage2Range[1])) {
              this.$Message.error(this.$t("police.numEmpty")); return;
              } else {
                    if (parseFloat(thr.voltage2Range[0]) >= parseFloat(thr.voltage2Range[1])) {
                      this.$Message.error(this.$t("police.upperGreaterLower")); return;
                    }
                }
            }
      }
      this.setLoading = true
      if(this.deviceIds.length > 0){
        this.thresholdFrom.cardIds = this.deviceIds
          this.$http({
          url: this.$http.adornUrl("/card/set/setThreshold"),
          method: "post",
          data: this.$http.adornData(this.thresholdFrom, false)
        }).then(({data}) => {
          this.setLoading = false;
          if (data && data.code === 0) {
            this.setThresholdList = data.data
          } else {
            this.$Message.error(data.msg)
          }
        })
      }
    },
    clearList() {
      this.thresholdFrom = {  // 报警阈值
        temperature: false,
        temperatureRange: ["",""],
        humidity: false,
        humidityRange: ["",""],
        voltage: false,
        voltageRange: ["",""],
        voltage1: false,
        voltage1Range: ["",""],
        voltage2: false,
        voltage2Range: ["",""],
        doorOpened: false,
        doorOpenedBool: false,
        smoke: false,
        smokeBool: false
      }
    },
    clearList2() {
      this.thresholdList = [];
      this.setThresholdList = [];
    },
    clearLoading(){
      this.queryLoading=false
      this.setLoading=false
    },
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        this.clearList();
        this.clearList2();
        this.clearLoading()
        this.alarmAddressFrom = {
          address: "",
        };
        this.thresholdFrom = {  // 报警阈值
        temperature: false,
        temperatureRange: ["",""],
        humidity: false,
        humidityRange: ["",""],
        voltage: false,
        voltageRange: ["",""],
        voltage1: false,
        voltage1Range: ["",""],
        voltage2: false,
        voltage2Range: ["",""],
        doorOpened: false,
        smoke: false
      }
      }
    },
  },
};
</script>
      <style scoped>
      .inputClass {
        width: 50px;
      }
      .formItemClass {
        margin-top:-25px;
      }
      .tableClass{
        width:320px;
        border-collapse: collapse;
        background-color:rgb(183, 227, 248);
      }
      .thresholdLine{
        float: left;
        border-left:3px solid rgb(204, 196, 196);
        height:350px;
      }
      .thresholdRead{
        margin-left: 30px;
        float: left;
        height:120px;
        width:900px;
        overflow: auto;
      }
</style>
