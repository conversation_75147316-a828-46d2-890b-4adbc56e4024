<template>
  <Modal v-model="visible" width="1100">
    <p slot="header" style="text-align:center">
      <span>{{ $t('nav.播放日志') }}</span>
    </p>
    <div>
      <div style="margin-bottom: 10px">
        <Alert  type="info" show-icon>{{ $t('tips.playLogsExportTip') }}</Alert>
        <DatePicker type="daterange" :placeholder="$t('broadcast.selectDate')" v-model="dateRange" style="width: 200px"></DatePicker>
        <Select v-model="selectTag" style="width:200px">
          <Option v-for="item in tagList" :value="item.value" :key="item.value" >{{ item.label }}</Option>
        </Select>
        <Button type="primary" :loading="queryLogsLoading" @click="queryLogs()">{{ $t('common.query') }}</Button>
<!--        <Button @click="exportEchartsImg">导出图片</Button>-->

<!--        <Button type="primary" :loading="queryPlayLogLoading" @click="queryPlayLog(isClick)">{{ $t('screen.programPlayDetail') }}</Button>-->
<!--        <Button type="primary" :loading="queryPlayLogEchartsLoading" @click="queryPlayLogEcharts(isClick)">{{ $t('screen.programPlayStatistic') }}</Button>-->
<!--        <Button type="primary" :loading="querySumLogsLoading" @click="querySumLogs(isClick)">{{ $t('screen.programPlaySum') }}</Button>-->
        <Button type="warning" :loading="exportExcelLoading" @click="exportExcel()">{{ $t('screen.exportPlayLog') }}</Button>
      </div>

      <div v-if="detailVisible" >
        <div style="height: 550px;overflow: auto">
          <Table border :columns="dataColumns" :data="paginatedData" :loading="dataListLoading"   ref="selection">
            <template slot-scope="{ row, index }" slot="number">
              {{ index + 1 }}
            </template>
          </Table>
        </div>
        <Page style="float:right;margin-top:10px;margin-bottom:20px" :total="totalPage" :current="pageIndex"
              :page-size="pageSize"
              show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
              @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
      </div>


      <div v-if="statisticVisible" style="height: 600px">
        <div v-show="isEchartsVisible">
          <div ref="logStatistic"  id="logStatistic" style="height: 600px;width: 1000px;overflow: auto"></div>
        </div>
        <div v-if="!isEchartsVisible" style="text-align: center;font-size: 18px;line-height: 100px">
          {{$t('home.temporarilyNoData')}}
        </div>
      </div>

      <div v-if="sumVisible" >
        <div style="height: 550px;overflow: auto">
        <Table border :columns="sumDataColumn" :data="paginatedStatisticData" :loading="sumDataLoading" ref="selection">
          <template slot-scope="{ row, index }" slot="number">
            {{ index + 1 }}
          </template>
        </Table>
        <Page style="float:right;margin-top:10px;margin-bottom:20px" :total="statisticTotalPage"
              :current="statisticPageIndex"
              :page-size="statisticPageSize"
              show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
              @on-change="statisticCurrentChangeHandle" @on-page-size-change="statisticSizeChangeHandle"/>
        </div>
      </div>



<!--      <Tabs :value="tabVal" @on-click="tabsClick">-->
<!--        <TabPane :label="$t('screen.programPlayDetail')" name="detail">-->
<!--          <Table border :columns="dataColumns" :data="paginatedData" :loading="dataListLoading" ref="selection">-->
<!--            <template slot-scope="{ row, index }" slot="number">-->
<!--              {{ index + 1 }}-->
<!--            </template>-->
<!--          </Table>-->
<!--          <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex"-->
<!--                :page-size="pageSize"-->
<!--                show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total-->
<!--                @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>-->
<!--        </TabPane>-->
<!--        <TabPane :label="$t('screen.programPlayStatistic')" name="echarts">-->
<!--          <div ref="logStatistic"  id="logStatistic" style="height: 650px;width: 1000px;overflow: auto"></div>-->
<!--        </TabPane>-->
<!--        <TabPane :label="$t('screen.programPlaySum')" name="sum">-->
<!--          <Table border :columns="sumDataColumn" :data="paginatedStatisticData" :loading="sumDataLoading" ref="selection">-->
<!--            <template slot-scope="{ row, index }" slot="number">-->
<!--              {{ index + 1 }}-->
<!--            </template>-->
<!--          </Table>-->
<!--          <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="statisticTotalPage"-->
<!--                :current="statisticPageIndex"-->
<!--                :page-size="statisticPageSize"-->
<!--                show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total-->
<!--                @on-change="statisticCurrentChangeHandle" @on-page-size-change="statisticSizeChangeHandle"/>-->
<!--        </TabPane>-->
<!--      </Tabs>-->
    </div>
    <div slot="footer">
    </div>
  </Modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      startDate: "",
      endDate: "",
      deviceId: "",
      token: this.$cookie.get('token'),
      dataColumns: [
        {
          title: this.$t('cardDevice.number'), align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('program.name'), key: 'name', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('program.name'))
          }
        },
        {
          title: this.$t('card.startTime'), key: 'beginAt', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('card.startTime'))
          }
        },
        {
          title: this.$t('card.endTime'), key: 'endAt', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('card.endTime'))
          }
        },
        {
          title: this.$t('program.PlayTime'), key: 'duration', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('program.PlayTime'))
          }
        },
      ],
      //节目详情
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      // dataListLoading:false,
      //每日统计
      logsStatistic: [],
      chartOption: null,
      logsStatisticEcharts: '',
      tabVal: '',
      //选择的日期
      dateRange: [],
      //
      sumDataColumn: [
        {
          title: this.$t('cardDevice.number'), align: 'center', slot: 'number',
          renderHeader: (h) => {
            return h('div', this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t('program.name'), key: 'name', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('program.name'))
          }
        },
        {
          title: this.$t('screen.programPlaySum'), key: 'sum', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('screen.programPlaySum'))
          }
        }
      ],
      //总数统计
      sumData: [],
      //总数页码
      statisticPageIndex: 1,
      //总数一页数量
      statisticPageSize: 10,
      //总数页数
      statisticTotalPage: 0,
      //详情等待
      dataListLoading:false,
      //总数等待
      sumDataLoading:false,
      exportExcelLoading:false,
      //默认日志详情
      selectTag:'detail',
      //tag对应的功能
      tagList: [
        {
          value: 'detail',
          label: this.$t('screen.programPlayDetail')
        },
        {
          value: 'statistic',
          label: this.$t('screen.programPlayStatistic')
        },
        {
          value: 'sum',
          label: this.$t('screen.programPlaySum')
        }
      ],
      //不同tag对应的图表
      detailVisible:true,
      statisticVisible:false,
      sumVisible:false,
      queryLogsLoading:false,
      //日志统计是否有数据
      isEchartsVisible:false
    }
  },
  methods: {
    // 初始化,默认查询当天的日志
    init(id) {
      this.deviceId = id
      this.visible = true
      this.startDate = null
      this.endDate = null
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage=0
      this.statisticPageIndex=1
      //总数一页数量
      this.statisticPageSize=10
      //总数页数
      this.statisticTotalPage=0
      this.dataList = []
      this.logsStatistic = []
      this.chartOption = null
      this.dateRange=[]
      this.logsStatisticEcharts = ''
      this.selectTag = 'detail'
      this.dataListLoading = false
      this.sumDataLoading=false
      this.exportExcelLoading=false
      this.detailVisible=true
      this.statisticVisible=false
      this.sumVisible=false
      this.queryLogsLoading=false
      if (id) {
        this.queryPlayLog()
      }
    },
    //点击tab时
    // tabsClick(name) {
    //   this.tabVal = name
    //   this.dateRange = []
    //   if (this.tabVal === 'echarts') {
    //     //切换到echarts图时，先销毁
    //     var el= document.getElementById('logStatistic')
    //     if (el!=null){
    //       this.$echarts.dispose(el)
    //     }
    //     this.queryPlayLogEcharts()
    //   } else if (this.tabVal === 'detail') {
    //     this.queryPlayLog()
    //   } else {
    //     this.querySumLogs()
    //   }
    // },

    queryLogs(){
      this.queryLogsLoading=true
      if (this.selectTag === 'statistic') {
        this.statisticVisible=true
        this.sumVisible=false
        this.detailVisible=false
        //切换到echarts图时，先销毁
        var el= document.getElementById('logStatistic')
        if (el!=null){
          this.$echarts.dispose(el)
        }
        this.queryPlayLogEcharts()
      } else if (this.selectTag === 'detail') {
        this.statisticVisible=false
        this.sumVisible=false
        this.detailVisible=true
        this.queryPlayLog()
      } else {
        this.statisticVisible=false
        this.sumVisible=true
        this.detailVisible=false
        this.querySumLogs()
      }
      this.queryLogsLoading=false
    },
    //查询日志详情
    queryPlayLog() {
      this.dataList = []
      this.totalPage = 0
      this.dataListLoading=true
      this.isDateRange()
      this.$http({
        url: this.$http.adornUrl(`/screen/playLogs/list`),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': this.deviceId,
          'startTime': this.startDate,
          'endTime': this.endDate
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.data) {
            this.dataList = data.data
            this.totalPage = data.data.length
          } else {
            this.dataList = []
            this.totalPage = 0
          }
        } else {
          this.$Message.error(data.msg)
        }
        this.dataListLoading=false
      })
    },
    //查询图表统计数据
    queryPlayLogEcharts() {
      this.logsStatistic = []
      this.chartOption = null
      this.logsStatisticEcharts = ''
      this.isDateRange()
      this.$http({
        url: this.$http.adornUrl(`/screen/playLogs/echarts`),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': this.deviceId,
          'startTime': this.startDate,
          'endTime': this.endDate,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.data) {
            if (data.data.logs.length===0){
              this.isEchartsVisible=false
            }else {
              this.isEchartsVisible=true
              this.logsStatistic = data.data
              this.chartOption = {
                title: {
                  text: this.$t('task.plays'),
                  subtext: this.$t('home.UnitTimes')
                },
                tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                    type: 'shadow',
                    label: {
                      show: true
                    }
                  }
                },
                legend: {
                  right: 'top',
                },
                xAxis: {
                  data: this.logsStatistic.date
                },
                yAxis: {},
                series: []
              };
              this.logsStatistic.logs.forEach(item => {
                var series = {
                  name: item.name,
                  type: 'bar',
                  data: item.nums
                }
                this.chartOption.series.push(series)
              })
              this.myEcharts()
            }
          }
        } else {
          this.$Message.error(data.msg)
        }
      })
    },
    //导出excel
    exportExcel() {
      this.exportExcelLoading=true
      this.isDateRange()
      this.$http({
        url: this.$http.adornUrl(`/screen/playLogs/playLogExcel/?deviceId=${this.deviceId}&startTime=${this.startDate}&endTime=${this.endDate}&token=${this.token}`),
        method: 'GET',
        responseType: 'blob'
      }).then(response => {
          // 创建一个链接元素，将 Blob 对象作为其 href 属性，并设置下载属性
          var link = document.createElement('a');
          link.href = window.URL.createObjectURL(response.data);
          link.download = this.deviceId+"的PlayLog"+'.xlsx';
          // 将链接元素添加到文档中，并触发其点击事件
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.exportExcelLoading=false
      });
      // window.open(this.$http.adornUrl(`/screen/playLogs/playLogExcel/?deviceId=${this.deviceId}&startTime=${this.startDate}&endTime=${this.endDate}&token=${this.token}`), '_blank', 'noopener,noreferrer')
    },
    //导出图片
    exportEchartsImg(){
      // debugger
      let myChart=this.$echarts.getInstanceByDom(document.getElementById('logStatistic'));
      if (!myChart){
        myChart=this.$echarts.init(document.getElementById('logStatistic'))
      }
   let picInfo=   myChart.getDataURL({
        type:'jpeg',
        pixelRatio:1.5,
        backgroundColor:"white",
      });
     const link= document.createElement("a");
      link.download="test.jpeg"
      link.style.display="none"
      link.href=picInfo
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)

    },
    //查询日志的总和
    querySumLogs() {
      this.sumDataLoading=true
      this.isDateRange()
      this.sumData = []
      this.statisticTotalPage = 0
      this.$http({
        url: this.$http.adornUrl(`/screen/playLogs/statistic`),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': this.deviceId,
          'startTime': this.startDate,
          'endTime': this.endDate,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.data) {
            this.sumData = data.data
            this.statisticTotalPage = this.sumData.length
          } else {
            this.sumData = []
            this.totalPage = 0
          }
        } else {
          this.$Message.error(data.msg)
        }
        this.sumDataLoading=false
      })
    },
    //构建图表
    myEcharts() {
      //客流
      if (this.logsStatisticEcharts != null && this.logsStatisticEcharts != "" && this.logsStatisticEcharts != undefined) {
        this.logsStatisticEcharts.dispose();
      }
      // console.log(this.chartOption)
      this.logsStatisticEcharts = this.$echarts.init(this.$refs.logStatistic);
      this.logsStatisticEcharts.setOption(this.chartOption);
    },
    // 详情每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
    },
    // 详情当前页
    currentChangeHandle(val) {
      this.pageIndex = val
    },
    // 播放总数每页数
    statisticSizeChangeHandle(val) {
      this.statisticPageSize = val
      this.statisticPageIndex = 1
      this.$refs.selection.selectAll(false)
    },
    // 播放总数当前页
    statisticCurrentChangeHandle(val) {
      this.statisticPageIndex = val
    },

    // date格式转成yy-MM-dd 00:00:00
    DateToStr(dd) {
      var y = dd.getFullYear();
      var m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
      var d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
      return y + '-' + m + '-' + d + " 00:00:00";

    },
    //判断dataRange是否合法
    isDateRange(){
      if (this.dateRange.length!==0){
        if (this.dateRange[0].length!==0&&this.dateRange[1].length!==0){
          this.startDate = this.DateToStr(this.dateRange[0])
          this.endDate = this.DateToStr(this.dateRange[1])
        }else {
          var today1 = new Date();
          this.startDate = this.DateToStr(today1)
          today1.setTime(new Date().getTime() + 24 * 60 * 60 * 1000);
          this.endDate=this.DateToStr(today1)
        }
      }else {
        var today2 = new Date();
        this.startDate = this.DateToStr(today2)
        today2.setTime(new Date().getTime() + 24 * 60 * 60 * 1000);
        this.endDate=this.DateToStr(today2)
      }
    }
  },
  computed: {
    paginatedData() {
      const startIndex = (this.pageIndex - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      return this.dataList.slice(startIndex, endIndex)
    },
    paginatedStatisticData() {
      const startIndex = (this.statisticPageIndex - 1) * this.statisticPageSize
      const endIndex = startIndex + this.statisticPageSize
      return this.sumData.slice(startIndex, endIndex)
    },
  },
}
</script>
<style scoped>

</style>
