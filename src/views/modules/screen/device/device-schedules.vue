<template>
  <div>
    <Modal height="400" width="1200" v-model="visible" :mask-closable="false">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('card.timing')}}</span>
        </p>
        <Row style="margin-bottom:5px">
          <Col span="2"><Button type="success" @click="headlerAdd">{{$t('common.add')}}</Button></Col>
          <Col span="5" style="margin-right: 10px;"><Input v-model="name" :placeholder="$t('common.PleaseInput') + $t('common.name')"/></Col>
          <Col span="16">
            <span v-if="type === 1">
              <Row>
                  <Col span="1.5"><div style="line-height:35px;margin-right:5px">{{$t('card.defaultBrightness')}}: </div></Col>
                  <Col span="20"><InputNumber width="200" :max="100" :min="1" v-model="defaultValue"></InputNumber>%</Col>
              </Row>
                  <!-- <SliderDrag  :value="defaultValue" @SetOpacityConfig="SetOpacityConfig"></SliderDrag> -->
            </span>
            <span v-else-if="type === 2">
              <Row>
                  <Col span="1.5"><div style="line-height:35px;margin-right:5px">{{$t('card.defaultVolume')}}: </div></Col>
                  <Col span="20"><InputNumber width="200" :max="15" :min="1" v-model="defaultValue"></InputNumber></Col>
              </Row>
                  <!-- <SliderDrag  :value="defaultValue" @SetOpacityConfig="SetOpacityConfig"></SliderDrag> -->
            </span>

          </Col>
        </Row>
        <Form :inline="true" style="height: 180px;overflow: auto;" :label-width="85" label-position="left"
        @keyup.enter.native="schedulesSubmit()">
        <Table border :columns="dataColumns" :data="schedules">
          <template slot-scope="{ row, index }" slot="dateRange">
            <DatePicker type="daterange" @on-change="dateRangeChangeHandle($event, index)" :placeholder="$t('common.PleaseSelect')" v-model="row.date" transfer format="yyyy-MM-dd">
            </DatePicker>
          </template>
          <template slot-scope="{ row, index }" slot="timeFrame">
            <TimePicker type="timerange" @on-change="timeFrameChangeHandle($event, index)" :placeholder="$t('common.PleaseSelect')" v-model="row.time" transfer format="HH:mm">
            </TimePicker>
          </template>
          <template slot-scope="{ row, index }" slot="specifyWeek">
            <Select v-model="row.weekFilter" transfer multiple @on-select="specifyWeekSelect($event, index)">
              <Option v-for="item in options" :key="item.value" :value="item.value" :label="$t(item.name)"></Option>
            </Select>
          </template>
        </Table>
        </Form>
        <div slot="footer">
            <div v-if="this.schedules.length > 0">
              <Row>
                  <Col span="9"><Alert type="success">{{$t("common.allIntervals")}}！</Alert></Col>
                  <Col span="15">
                    <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
                    <Button type="primary" size="large" :loading="modal_loading" @click="schedulesSubmit()">{{$t('common.confirm')}}</Button>
                  </Col>
              </Row>
            </div>
            <div v-else>
              <Row>
                  <Col span="9"><Alert type="success">{{$t("common.allIntervals")}}！</Alert></Col>
                  <Col span="15">
                    <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
                    <Button type="primary" size="large" disabled >{{$t('common.addTiming')}}</Button>
                  </Col>
              </Row>


            </div>
        </div>
    </Modal>
  </div>
</template>
<script>
import SliderDrag from '@/utils/SliderDrag'
export default {
  data () {
    return {
      visible: false,
      type: '',
      defaultValue: 1,
      timeValue: 200,
      name: '',
      id: '',
      infoId: 0,
      // ids: [],
      modal_loading: false,
      dataColumns: [
        { title: this.$t('card.DateRange'), key: 'dateRange', align: 'center', slot: 'dateRange',
          renderHeader:(h)=>{
              return h('div',this.$t('card.DateRange'))
          }
        }, // 日期范围
        { title: this.$t('card.timeFrame'), key: 'timeFrame', align: 'center', slot: 'timeFrame',
          renderHeader:(h)=>{
              return h('div',this.$t('card.timeFrame'))
          }
        }, // 时间范围
        { title: this.$t('card.WeekRange'), key: 'specifyWeek', align: 'center', slot: 'specifyWeek',
          renderHeader:(h)=>{
              return h('div',this.$t('card.WeekRange'))
          }
        }, // 星期范围
        {
          title: this.$t('common.operation'), // 删除操作
          key: 'operation',
          align: 'center',
          width: 110,
          render: (h, {row, index}) => {
            return h('a', {on: {
              click: () => {
                this.schedules.splice(index, 1)
              }
            }}, this.$t('common.delete'))
          },
          renderHeader:(h)=>{
              return h('div',this.$t('common.operation'))
          }
        },
      ],
      schedules: [],
      options: [
        {value: 1, name:'common.Monday'},
        {value: 2, name:'common.Tuesday'},
        {value: 3, name:'common.Wednesday'},
        {value: 4, name:'common.Thursday'},
        {value: 5, name:'common.Friday'},
        {value: 6, name:'common.Saturday'},
        {value: 7, name:'common.Sunday'}
      ],
      resultData: []
    }
  },
  methods: {
    // 初始化
    init (type, id) {
      this.visible = true
      this.type = type
      this.id = id
      this.resultData = []
      if (this.type === 1) {
        this.dataColumns.unshift({
          title: this.$t('card.timingBrightness'),
          key: 'timeValue',
          width: '130',
          align: 'left',
          render: (h, {row, index}) => {
            return  h('div', [
              h('InputNumber', {
              props: {
                width: '70',
                max: 100,
                min: 1,
                value:  row.timeValue
              },
              on: {
                input: val => {
                  this.schedules[index].timeValue = val
                }
              }
            }),
            h('span', {
                style: {
                  color: 'green',
                }
              }, "%")
            ])
          }
        })
      } else if (this.type === 2) {
        this.dataColumns.unshift({
          title: this.$t('card.timedVolume'),
          key: 'timeValue',
          width: '130',
          align: 'left',
          render: (h, {row, index}) => {
            return  h('div', [
              h('InputNumber', {
                props: {
                  width: '70',
                  max: 15,
                  min: 1,
                  value:  row.timeValue
                },
                on: {
                  input: val => {
                    this.schedules[index].timeValue = val
                  }
                }
              })
            ])
          }
        })
      }
      if (id) {
        this.$http({
          url: this.$http.adornUrl(`/screen/schedules/info/${id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.name = data.schedules.name
            this.schedules =  JSON.parse(data.schedules.schedules)
            this.defaultValue = data.schedules.defaultValue
            this.infoId = data.schedules.infoId
          }
        })
      }
    },
    dateRangeChangeHandle (date, index) {
      if (date != null && date.length === 2) {
          this.schedules[index].dateType = 'Range'
          this.schedules[index].date = date
          // 数组中第一个值为startDate，第二个值为endDate
          if (date[0]) {
            this.schedules[index].startDate = date[0]
          } else {
            this.schedules[index].startDate = ''
          }
          if (date[1]) {
            this.schedules[index].endDate = date[1]
          } else {
            this.schedules[index].endDate = ''
          }
        } else {
          this.schedules[index].dateType = 'All'
          this.schedules[index].date = []
      }
    },
    timeFrameChangeHandle (time, index) {
      if (time && time.length === 2) {
        this.schedules[index].timeType = "Range"
        this.schedules[index].time = time
        // 数组中第一个值为startTime，第二个值为endTime
        if (time[0]) {
          this.schedules[index].startTime = time[0]
        } else {
          this.schedules[index].startTime = ''
        }
        if (time[1]) {
          this.schedules[index].endTime = time[1]
        } else {
          this.schedules[index].endTime = ''
        }
      } else {
        this.schedules[index].timeType = "All"
        this.schedules[index].time = []
      }
    },
    specifyWeekSelect (week, index) {
      if (this.schedules[index].weekFilter.indexOf(week.value) == -1) {
        this.schedules[index].weekFilter.push(week.value)
      } else {
        this.schedules[index].weekFilter.some((item ,i) => {
          if (item === week.value) {
            this.schedules[index].weekFilter.splice(i, 1)
            return true
          }
        })
      }
      if (this.schedules[index].weekFilter.length > 0) {
        this.schedules[index].filterType = "Week"
      } else {
        this.schedules[index].filterType = "None"
      }
    },
    headlerAdd () {
      const addSchedules = {
        timeValue: 1,
        dateType: 'All',
        date: '',
        startDate: '',
        endDate: '',
        timeType: 'All',
        time:'',
        startTime: '',
        endTime: '',
        filterType: 'None',
        weekFilter: []
      }
      this.schedules.push(addSchedules)
    },
    // 提交数据
    schedulesSubmit () {
      // if (this.ids) {
        if (this.type === '') {
          this.$Message.error(this.$t('card.PleaseScheduledTask'))
        } else {
          if (this.schedules.length > 0) {
            if (this.name === '') {
              this.$Message.error(this.$t('common.name') + this.$t('validate.not_empty'))
              return;
            }
            this.modal_loading = true
              this.$http({
                url: this.$http.adornUrl(`/screen/schedules/${!this.id ? 'save' : 'update'}`),
                method: 'post',
                data: this.$http.adornData({
                  'id': this.id,
                  'name': this.name,
                  'schedules': JSON.stringify(this.schedules),
                  'type': this.type,
                  'defaultValue': this.defaultValue,
                  'infoId': this.infoId
                })
              }).then(({data}) => {
                // console.log(data)
                if (data && data.code === 0) {
                  this.visible = false
                  this.modal_loading = false
                  this.$emit('schedules-result-data')
                } else {
                  this.$Message.error(data.msg)
                }
              })
          }
        }
      // }
    },
    SetOpacityConfig (val) {
      this.defaultValue = val
    }
  },
  components:{
    SliderDrag
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.name = ''
          this.defaultValue = 1
          this.schedules = []
          // 删除动态表
          if (this.dataColumns.length > 4) {
            var len = this.dataColumns.length - 4
            this.dataColumns.splice(0, len)
          }
        }, 500)
      }
    }
  }
}
</script>
