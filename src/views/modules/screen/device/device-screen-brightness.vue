<template>
  <div>
    <Modal v-model="visible" width="1000">
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{$t('operation.screenBrightness')}}</span>
      </p>
      <Tabs :value="tabVal" @on-click="handlerTab" style="height: 650px">
        <TabPane :label="$t('operation.screenBrightness')" name="tab1">
          <Alert type="warning" show-icon><b class="tip">{{$t('common.brightnessClears')}}</b></Alert>
          <Divider />
          <Form label-position="left" label-colon>
            <FormItem>
              <Row>
                <Col span="1"><span style="font-size:15px;">{{$t('cardDevice.brightness')}}:</span></Col>
                <Col span="11">
                  <SliderDrag :value="data.brightness" @SetOpacityConfig="SetOpacityConfig" :key="reloadBrightness">
                  </SliderDrag>
                </Col>
                <Col span="12"><Button :loading="modal_loading" type="primary"
                  @click="screenBrightness()">{{$t('common.set')}}</Button>
                <Button :loading="queryScreenBrightnessLoading" type="success"
                  @click="queryScreenBrightness()">{{$t('common.query')}}</Button>
                </Col>
              </Row>
            </FormItem>
            <div v-if="resultData.length > 0" style="height: 495px;overflow-y: auto">
              <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
            <div v-if="queryScreenBrightnessList.length > 0" style="height: 495px;overflow-y: auto">
              <cardResult :ids="ids" :resultData="queryScreenBrightnessList" :cardItemWidth="900 / 2 - 50" :isQuery="true"
                :resultItem="[{text: 'cardDevice.brightness', name: 'brightnessPercentage', unit: '%'}, 
                {text: 'cardDevice.brightness', name: 'brightness'}]"></cardResult>
            </div>
          </Form>
        </TabPane>
        <TabPane :label="$t('operation.autoBrightness')" name="tab2">
          <Alert type="warning" show-icon>
            <b class="tip" style="margin-bottom:5px">{{$t('tips.autoBrightness')}}</b>
            <br />
            <b class="tip" style="margin-bottom:5px">{{$t('tips.autoBrightness1')}}</b>
            <br />
            <b class="tip">{{$t('tips.autoBrightness2')}}</b>
          </Alert>
          <div>
            <!-- 灵敏度 -->
            <Row>
              <Col span="1.5"><span style="font-size:15px;float:right;"><span
                  v-html="'\u00a0\u00a0\u00a0'"></span>{{$t('card.sensitivity')}}:</span></Col>
              <Col span="11">
                <div style="margin-left:2px;">
                  <SliderDrag :value="autoData.sensitivity" @SetOpacityConfig="SetSensitivity"
                    :key="reloadSensitivity"></SliderDrag>
                </div>
              </Col>
              <Col span="11.5">
                <Button style="margin-left:-7px;" :loading="setSensitivityLoading" type="primary"
                  @click="setSensitivity()">{{$t('common.set')}}
                </Button>
                <Button :loading="getSensitivityLoading" type="success"
                  @click="getSensitivity()">{{$t('common.query')}}
                </Button>
              </Col>
            </Row>
            <!-- 最小亮度 -->
            <Row style="margin-top:5px">
              <Col span="1.5"><span style="font-size:15px;float:right;">{{$t('card.Minbrightness')}}:</span></Col>
              <Col span="11">
                <SliderDrag :value="autoData.minBrightness" @SetOpacityConfig="SetMinBrightness"
                  :key="reloadMinBrightness"></SliderDrag>
              </Col>
              <Col span="11.5">
                <Button style="margin-left:-10px;" :loading="setMinBrightnessLoading" type="primary"
                  @click="setMinBrightness()">{{$t('common.set')}}</Button>
                <Button :loading="getMinBrightnessLoading" type="success"
                  @click="getMinBrightness()">{{$t('common.query')}}</Button>
              </Col>
            </Row>
            <!-- 最大亮度 -->
            <Row style="margin-top:5px">
              <Col span="1.5">
                <span style="font-size:15px;float:right;">{{$t('common.fullBrightness')}}:</span>
              </Col>
              <Col span="11">
                <SliderDrag :value="autoData.maxBrightness" @SetOpacityConfig="SetMaxBrightness"
                  :key="reloadMaxBrightness"></SliderDrag>
              </Col>
              <Col span="11.5">
                <Button style="margin-left:-10px;" :loading="setMaxBrightnessLoading" type="primary"
                  @click="setMaxBrightness()">{{$t('common.set')}}</Button>
                <Button :loading="getMaxBrightnessLoading" type="success"
                  @click="getMaxBrightness()">{{$t('common.query')}}</Button>
              </Col>
            </Row>
          </div>
          <div style="height: 415px;overflow: auto;">
            <div v-if="setSensitivityList.length > 0">
              <cardResult :ids="ids" :resultData="setSensitivityList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
            <div v-if="getSensitivityList.length > 0">
              <cardResult :ids="ids" :resultData="getSensitivityList" :cardItemWidth="900 / 2 - 50" :isQuery="true" 
                :resultItem="[{text: 'card.sensitivity', name: 'sensitivity', unit: '%'}]"></cardResult>
            </div>
            <div v-if="setMinBrightnessList.length > 0">
              <cardResult :ids="ids" :resultData="setMinBrightnessList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
            <div v-if="getMinBrightnessList.length > 0">
              <cardResult :ids="ids" :resultData="getMinBrightnessList" :cardItemWidth="900 / 2 - 50" :isQuery="true" 
                :resultItem="[{text: 'card.Minbrightness', name: 'minBrightnessPercentage', unit: '%'}, 
                {text: 'card.Minbrightness', name: 'brightness'}]"></cardResult>
            </div>
            <div v-if="setMaxBrightnessList.length > 0">
              <cardResult :ids="ids" :resultData="setMaxBrightnessList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
            <div v-if="getMaxBrightnessList.length > 0" >
              <cardResult :ids="ids" :resultData="getMaxBrightnessList" :cardItemWidth="900 / 2 - 50" :isQuery="true" 
                :resultItem="[{text: 'common.fullBrightness', name: 'maxBrightnessPercentage', unit: '%'}, 
                {text: 'common.fullBrightness', name: 'brightness'}]"></cardResult>
            </div>
          </div>
        </TabPane>
        <TabPane :label="$t('operation.timingBrightness')" name="tab3">
          <Alert type="warning" show-icon>
            <b class="tip">{{$t('tips.timingBrightness')}}</b>
          </Alert>
          <Form inline label-position="left">
            <FormItem style="margin-bottom: 10px">
              <Input v-model="name" :placeholder="$t('common.name')"></Input>
            </FormItem>
            <FormItem style="margin-bottom: 10px">
              <Button style="margin-right:6px" @click="getDataList()">
                <div style="margin:3px 8px">{{$t('common.query')}}</div>
              </Button>
              <Button style="margin-right:6px" type="primary" @click="addOrUpdateHandle()">
                <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
              </Button>
            </FormItem>
          </Form>
          <Table border :columns="dataConlums" :data="dataList" :loading="dataListLoading" style="width: 100%"
            :max-height="300" ref="selection">
            <template slot-scope="{ row, index }" slot="operation">
              <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading"
                @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
              <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px"
                @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
              <Button type="error" size="small" style="font-size: 11px"
                @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
            </template>
          </Table>
          <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total @on-change="currentChangeHandle"
            @on-page-size-change="sizeChangeHandle" />
            <div style="height: 130px;overflow-y: auto;clear: both;" v-if="schedulesResultData.length > 0">
              <cardResult :ids="ids" :resultData="schedulesResultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
            </div>
        </TabPane>
        <TabPane :label="$t('operation.queryTiming')" name="tab4">
          <Form inline :label-width="30" label-position="left">
            <Button :loading="getTimed_loading" type="success"
              @click="getTimedBrightness()">{{$t('card.getTiming')}}</Button>
            <div style="height: 520px;overflow-y: auto">
              <div v-if="timedBrightness.length > 0">
                <cardResult :ids="ids" :resultData="timedBrightness" :cardItemWidth="900" :isQuery="true" 
                  :isTable="true" tableFieldNameLv1='taskBrightness' tableFieldNameLv2='items' 
                  :tableFieldOther="[{text: 'card.defaultBrightness', name: 'defaultBrightness', unit: '%'}]"
                  :tableColumns='dataColumns' :tableHeight="200"></cardResult>
              </div>
            </div>
          </Form>
        </TabPane>
        <TabPane :label="this.$t('screen.autoBrightnessTable')" name="tab5">
          <Alert type="warning" show-icon>
            <b class="tip">{{$t('tips.senorBrightnessTable')}}</b>
          </Alert>
          <Form inline :label-width="30" label-position="left">
            <span>{{$t('screen.senorType')}}</span>
            <RadioGroup v-model="sensorType">
              <Radio label="RL1/RL2"></Radio>
              <Radio label="R60/R67/RL3"></Radio>
            </RadioGroup>
            <div style="margin-top: 10px">
              <Button  type="primary" @click="setBrightnessTableVisible()">
                {{ $t('screen.uploadFile') }}</Button>
              <Button :loading="getBrightnessTableLoading" type="success" @click="queryBrightnessTable()">{{ $t('common.query') }}</Button>
              <div style="margin-top: 10px;height: 460px;overflow-y: auto">
                <div  v-if="brightnessTableList.length > 0">
                  <cardResult :ids="ids" :resultData="brightnessTableList" :cardItemWidth="900 / 2 - 50" :isQuery="true" 
                    :resultItem="[{text: 'setTime.connectTo485', name: 'is485'}, 
                    {name: 'value', resultSet: [{value: 1, name: 'RL1/RL2'},{value: 2, name: 'R60/R67/RL3'}]}]"></cardResult>
                </div>
                <div  v-if="setBrightnessTableList.length > 0">
                  <cardResult :ids="ids" :resultData="setBrightnessTableList" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
                </div>
              </div>
            </div>
            <Modal v-model="brightnessTableVisible" :closable="false" :mask-closable="false" width="360">
              <p slot="header" style="text-align:center">
                <span>{{ $t('screen.setAutoBrightnessTable') }}</span>
              </p>
              <div style="text-align:center">
                <RadioGroup v-model="brightnessTableType" @on-change="changeSelect()">
                  <Radio label="defaultTable">{{ $t('screen.default255BrightnessTable') }}</Radio>
                  <Radio label="customizeTable">{{ $t('screen.customizeBrightnessTable') }}</Radio>
                </RadioGroup>
              </div>
              <div v-if="brightnessTableType=='customizeTable'">
                <Upload type="drag" accept=".xlsx,.xls" :show-upload-list="true" :before-upload="handleUpload"
                  :action="''">
                  <div style="padding: 20px 0">
                    <Icon type="ios-cloud-upload" size="26" style="color: #3399ff"></Icon>
                    <p>{{$t('file.attachment1')}}</p>
                  </div>
                </Upload>
                <div>
                  <ul class="file-list" v-for="(list,index) in uploadForm.dispalyFile" :key="index">
                    <li>{{$t('file.name')}}: <span style="font-size:15px;">{{ list.name }}</span>
                      <Icon type="ios-close" size="20" style="float:right;"
                        @click="uploadForm.dispalyFile.splice(index,1)"></Icon>
                    </li>
                  </ul>
                </div>
              </div>
              <div slot="footer">
                <Button type="dashed" size="large" @click="brightnessTableCancel">{{$t('common.cancel')}}</Button>
                <Button type="primary" size="large" :loading="brightnessTableModalLoading"
                  @click="setBrightnessTable">{{$t('common.confirm')}}</Button>
              </div>
            </Modal>
          </Form>
        </TabPane>
      </Tabs>
      <div slot="footer" style="text-align: left;">
        <span>
          <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto;max-height:42px;">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}
            </BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
    <!-- 定时-->
    <device-schedules v-if="setTimingVisible" ref="deviceSchedules" @schedules-result-data="getDataList">
    </device-schedules>
  </div>
</template>


<script>
import deviceSchedules from './device-schedules'
import SliderDrag from '@/utils/SliderDrag'
import cardResult from "@/utils/cardResult.vue"
import axios from "axios";
export default {
  data() {
    return {
      setTimingVisible: false,
      visible: false,
      tabVal: '',
      data: {
        brightness: 1
      },
      autoData: {
        sensitivity: 1,
        minBrightness: 1,
        maxBrightness: 1,
      },
      ids: [],
      dataForm: {},
      modal_loading: false,
      queryScreenBrightnessLoading: false,
      queryScreenBrightnessList: [],
      auto_loading: false,
      getTimed_loading: false,
      timedBrightness: [],
      dataColumns: [
        {
          title:  this.$t('card.timingBrightness'),
          key: 'brightness',
          align: 'center',
          width: '200',
          render: (h, { row, index }) => {
            return h('span', {}, row.brightness + '%')
          },
          renderHeader: (h) => {
            return h('div', this.$t('card.timingBrightness'))
          },
        },
        {
          title: this.$t('card.DateRange'), // 日期范围
          key: 'schedule.dateType',
          align: 'center',
          width: '200',
          render: (h, { row, index }) => {
            var result = ''
            if (row.schedule.dateType === 'Range') {
              result = [
                h('span', {}, row.schedule.startDate),
                h('span', {}, ' -- '),
                h('span', {}, row.schedule.endDate)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader: (h) => {
            return h('div', this.$t('card.DateRange'))
          }
        },
        {
          title: this.$t('card.timeFrame'), // 时间范围
          key: 'schedule.timeType',
          align: 'center',
          width: '200',
          render: (h, { row, index }) => {
            var result = ''
            if (row.schedule.timeType === 'Range') {
              result = [
                h('span', {}, row.schedule.startTime),
                h('span', {}, ' -- '),
                h('span', {}, row.schedule.endTime)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader: (h) => {
            return h('div', this.$t('card.timeFrame'))
          }
        },
        {
          title: this.$t('card.SpecifyWeek'), // 星期范围
          key: 'schedule.filterType',
          align: 'center',
          width: '350',
          tooltip: true,
          render: (h, { row, index }) => {
            var result = ''
            if (row.schedule.filterType === 'Week') {
              result = [
                h('span', {}, this.getWeekDay(row.schedule.weekFilter))
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader: (h) => {
            return h('div', this.$t('card.SpecifyWeek'))
          }
        }
      ],
      resultData: [],
      autoResultData: [],
      schedulesResultData: [],
      setSensitivityList: [], //设置灵敏度集合
      setSensitivityLoading: false,
      getSensitivityList: [], //查询灵敏度集合
      getSensitivityLoading: false,
      setMinBrightnessList: [], //设置最小亮度集合
      setMinBrightnessLoading: false,
      getMinBrightnessList: [], //查询最小亮度集合
      getMinBrightnessLoading: false,
      setMaxBrightnessList: [], //设置最大度集合
      setMaxBrightnessLoading: false,
      getMaxBrightnessList: [], //查询最大度集合
      getMaxBrightnessLoading: false,
      name: '',
      dataConlums: [
        // {type: 'selection', width: 60, align: 'center'},
        // {title: 'ID', key: 'id', width: 80, align: 'center'},
        {
          title: this.$t('common.name'), key: 'name', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.name'))
          }
        },
        {
          title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.createTime'))
          }
        },
        {
          title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      addOrUpdateVisible: false,
      reloadBrightness: 0,
      reloadSensitivity: 0,
      reloadMinBrightness: 0,
      reloadMaxBrightness: 0,
      /**
       * 查询亮度表loading
       */
      getBrightnessTableLoading: false,
      /**
       * 传感器类型
       */
      sensorType: "RL1/RL2",
      /**
       * 自动亮度表设置弹窗可视
       */
      brightnessTableVisible: false,

      /**
       * 亮度表类型
       */
      brightnessTableType: "defaultTable",
      /**
       * 上传文件
       */
      brightnessTableFile: null,
      brightnessTableModalLoading: false,
      token: this.$cookie.get('token'),
      /**
       * 下载列表
       */
      uploadForm: {
        resourceType: 1,
        dispalyFile: [] // 临时数组，同时用于显示在页面
      },
      /**
       * 传感器亮度值
       */
      brightnessTableList: [],
      setBrightnessTableList: [],

    }
  },
  components: {
    deviceSchedules,
    SliderDrag,
    cardResult
  },
  methods: {
    // 初始化
    init(ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
      }
    },
    handlerTab(name) {
      this.tabVal = name
      if (this.tabVal === 'tab3') {
        this.getDataList()
      }
      this.clearLoading()
    },
    clearAutomatic() {
      this.setSensitivityList = []
      this.getSensitivityList = []
      this.setMinBrightnessList = []
      this.getMinBrightnessList = []
      this.setMaxBrightnessList = []
      this.getMaxBrightnessList = []
    },
    getMaxBrightness() { //查询最大亮度
      this.clearAutomatic();
      this.getMaxBrightnessLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/query/getMaxBrightness"),
        method: 'post',
        data: this.ids
      }).then(({ data }) => {
        if (data && data.code == 0) {
          this.getMaxBrightnessList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.getMaxBrightnessLoading = false
      })
    },
    setMaxBrightness() { //设置最大亮度
      this.clearAutomatic();
      this.setMaxBrightnessLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/set/setMaxBrightness"),
        method: 'post',
        data: this.$http.adornData({ 'cardIds': this.ids, 'brightnessPercentage': this.autoData.maxBrightness })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.setMaxBrightnessList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.setMaxBrightnessLoading = false
      })
    },
    getMinBrightness() { //查询最小亮度
      this.clearAutomatic();
      this.getMinBrightnessLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/query/getMinBrightness"),
        method: 'post',
        data: this.ids
      }).then(({ data }) => {
        if (data && data.code == 0) {
          this.getMinBrightnessList = data.data
        } else {
          this.$Message.error(data.msg)
        }
          this.getMinBrightnessLoading = false
      })
    },
    setMinBrightness() { //设置最小亮度
      this.clearAutomatic();
      this.setMinBrightnessLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/set/setMinBrightness"),
        method: 'post',
        data: this.$http.adornData({ 'cardIds': this.ids, 'brightnessPercentage': this.autoData.minBrightness })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.setMinBrightnessList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.setMinBrightnessLoading = false
      })
    },
    getSensitivity() { //查询自动亮度灵敏度
      this.clearAutomatic();
      this.getSensitivityLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/query/getSensitivity"),
        method: 'post',
        data: this.ids
      }).then(({ data }) => {
        if (data && data.code == 0) {
            this.getSensitivityList = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.getSensitivityLoading = false
      })
    },
    setSensitivity() { // 设置自动亮度灵敏度
      this.clearAutomatic();
      this.setSensitivityLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/set/setSensitivity"),
        method: 'post',
        data: this.$http.adornData({ 'cardIds': this.ids, 'sensitivity': this.autoData.sensitivity })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.setSensitivityList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.setSensitivityLoading = false
      })
    },
    // 查询定时
    getTimedBrightness() {
      this.clearData()
      if (this.ids.length > 0) {
        this.getTimed_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getTimedBrightness'),
          method: 'post',
          data: this.ids
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.timedBrightness = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.getTimed_loading = false
        })
      }
    },
    // 设置定时
    addOrUpdateHandle(id) {
      this.setTimingVisible = true
      this.clearData()
      this.$nextTick(() => {
        // 传入1表示当前是一个定时亮度任务
        this.$refs.deviceSchedules.init(1, id)
      })
    },
    //查询亮度
    queryScreenBrightness() {
      this.clearData();
      this.queryScreenBrightnessLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/query/getScreenBrightness"),
        method: 'post',
        data: this.ids
      }).then(({ data }) => {
        if (data && data.code == 0) {
          this.queryScreenBrightnessList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.queryScreenBrightnessLoading = false
      })
    },
    // 设置亮度
    screenBrightness() {
      this.clearData()
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/screenBrightness'),
          method: 'post',
          data: this.$http.adornData({
            'ids': this.ids,
            'brightness': this.data.brightness
          })
        }).then(({ data }) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg);
          }
          this.modal_loading = false
        })
      }
    },
    clearData() {
      this.timedBrightness = []
      this.resultData = []
      this.autoResultData = []
      this.schedulesResultData = []
      this.queryScreenBrightnessList = []
      this.brightnessTableList = []
      this.brightnessTableFile = null
      this.setBrightnessTableList = []
    },
    SetOpacityConfig(val) {
      this.data.brightness = val
    },
    SetSensitivity(val) {
      this.autoData.sensitivity = val
    },
    SetMinBrightness(val) {
      this.autoData.minBrightness = val
    },
    SetMaxBrightness(val) {
      this.autoData.maxBrightness = val
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/schedules/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
          'type': 1
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    deleteHandle(id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/screen/schedules/delete'),
            method: 'post',
            data: this.$http.adornData(id, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 设置定时
    setTimeHandle(id) {
      if (this.ids.length > 0) {
        this.schedulesResultData = []
        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading = true;
          }
        })
        this.$http({
          url: this.$http.adornUrl('/card/set/schedules'),
          method: 'post',
          data: this.$http.adornData({
            'id': id,
            'deviceIds': this.ids
          })
        }).then(({ data }) => {
          if (data && data.code == 0) {
            this.schedulesResultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.dataList.map(item => {
            if (item.id === id) {
              item.timeLoading = false;
            }
          })
        })
      }
    },

    /**
     * 设置传感器亮度
     */
    setBrightnessTable() {
      this.clearData()
      if (this.ids.length > 0) {
        this.brightnessTableModalLoading = true
        let formData = new FormData()
        formData.append("file", this.brightnessTableFile);
        formData.append("cardIds", this.ids);
        formData.append("type", this.sensorType)
        axios.request({
          url: this.$http.adornUrl('/card/set/setSensorBrightnessTable'),
          method: 'post',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data', 'token': this.token }
        }).then(({ data }) => {
          if (data && data.code == 0) {
            this.setBrightnessTableList = data.data
            // this.$Message.success(data.msg._type)
          } else {
            this.$Message.error(data.msg)
          }
          this.uploadForm = {
            resourceType: 1,
            dispalyFile: []
          }
          this.brightnessTableModalLoading = false
          this.brightnessTableVisible = false
          this.brightnessTableFile = null
        })
      }
    },
    /**
     * 打开亮度表设置弹窗
     */
    setBrightnessTableVisible() {
      this.brightnessTableVisible = true
    },
    handleSuccess(res, file) {
      this.brightnessTableFile = file
    },
    /**
     * 上传之前
     * @param selectFile
     * @returns {boolean}
     */
    handleUpload(selectFile) {
      console.log(selectFile)
      var type = selectFile.type
      console.log(type)
      /*      var ele = type.substring(type.lastIndexOf('.') + 1, type.length)
            if (ele === "document") {
              if (ele === "document" && selectFile.size > (50 * 1024 * 1024)) {
                this.$Message.error({
                  content: this.$t("file.fileOverSize")
                })
                this.selectFile = null //超过大小将文件清空
                return false
              }
            } else {
              this.$Message.error({
                content:this.$t("file.fileLimit")
              })
              this.selectFile = null //将文件清空
              return false
            }
            if (this.uploadForm.manualFile.length >= 5) {
              this.$Message.error(this.$t('file.YouCanOnlyUploadUpTo5Files'))
              return false
            }*/
      this.uploadForm.dispalyFile.push(selectFile)

      this.brightnessTableFile = selectFile
      return false
    },
    /**
     * 弹窗取消按钮
     */
    brightnessTableCancel() {
      this.brightnessTableType = "defaultTable"
      this.brightnessTableFile = null
      this.brightnessTableVisible = false

      this.uploadForm = {
        resourceType: 1,
        dispalyFile: []
      }
      console.log(this.uploadForm.dispalyFile);
    },
    /**
     * 查询传感器值
     */
    queryBrightnessTable() {
      this.clearData();
      this.getBrightnessTableLoading = true
      this.$http({
        url: this.$http.adornUrl("/card/query/sensorBrightness"),
        method: 'post',
        data: this.ids
      }).then(({ data }) => {
        if (data && data.code == 0) {
          this.brightnessTableList = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.getBrightnessTableLoading = false
      })
    },
    changeSelect() {
      this.brightnessTableFile = null
    },
    /**
     * 清除loading效果
     */
    clearLoading(){
        this.modal_loading= false
        this.queryScreenBrightnessLoading= false
        this.auto_loading= false
        this.getTimed_loading=false
        this.setSensitivityLoading= false
        this.getSensitivityLoading= false
        this.setMinBrightnessLoading=false
        this.getMinBrightnessLoading=false
        this.setMaxBrightnessLoading=false
        this.getMaxBrightnessLoading=false
        this.getBrightnessTableLoading= false
    },
    /**
     * 根据星期数字获取对应的国际化,返回字符串
     * 
     */
    getWeekDay(week) {
  
      let weelStr = ''
      if (week.length > 0) {
        week.forEach(item => {
          switch (item) {
        case 1:
          weelStr+=this.$t('common.Monday')+','
          break
        case 2:
          weelStr+=this.$t('common.Tuesday')+','
          break
        case 3:
          weelStr+=this.$t('common.Wednesday')+','
          break
        case 4:
          weelStr+=this.$t('common.Thursday')+','
          break
        case 5:
          weelStr+=this.$t('common.Friday')+','
          break
        case 6:
          weelStr+=this.$t('common.Saturday')+','
          break
        case 7:
          weelStr+=this.$t('common.Sunday')
          break
        default:
          weelStr=''
      }
        })
       
        weelStr=weelStr.substring(0,weelStr.length-1)
      }
      return weelStr
     
    }


  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.clearData()
        this.clearLoading()
        this.clearAutomatic()
        this.$emit('refreshDataList')
        this.data.brightness = 1
        this.autoData.sensitivity = 1
        this.autoData.minBrightness = 1
        this.autoData.maxBrightness = 1
        this.reloadBrightness += 1
        this.reloadSensitivity += 1
        this.reloadMinBrightness += 1
        this.reloadMaxBrightness += 1
      }
    }
  }
}
</script>
