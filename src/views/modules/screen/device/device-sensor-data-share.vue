<template>
  <Modal v-model="visible" width="900">
    <p slot="header" style="text-align:center">
      <span>{{$t('operation.sensorDataShare')}}</span>
    </p>

    <Form :label-width="100" style="height: 300px;" inline>
      <FormItem :label="$t('operation.dataShare')">
        <i-switch v-model="formData.isShare" @on-change="handleSwitchChange()"  size="large">
          <span slot="open">{{ $t('sys.open') }}</span>
          <span slot="close">{{ $t('sys.close') }}</span>
        </i-switch>
      </FormItem>
      <FormItem :label="$t('operation.dataRefreshCycle')" v-if="formData.isShare">
        <InputNumber :max="10" :min="1" v-model="formData.cycle"></InputNumber>
        <span>{{ $t('screen.hour') }}</span>
      </FormItem>
      <FormItem :label="$t('operation.sharedDataKey')" v-if="formData.isShare">
        <div style="height: 50px">
          <span v-if="formData.dataKey!=null&&formData.dataKey!=''">{{ formData.dataKey }}</span>
          <span v-else>{{ $t('statistic.noDataKey') }}</span>
          <Tooltip :content="this.$t('statistic.clickCopy')">
            <svg width="20px" height="20px" aria-hidden="true" style="vertical-align: middle;" @click="copyKey">
              <use xlink:href="#copy"></use>
            </svg>
          </Tooltip>
        </div>
      </FormItem>
      <FormItem>
        <Button type="primary"  :loading="setLoading" @click="setDataShare()">{{ $t('common.set') }}</Button>
      </FormItem>
    </Form>

    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
export default {
  name: "device-sensor-data-share",
  data() {
    return {
      visible: false,
      formData: {
        isShare: false,
        cycle: 1,
        dataKey: "",
        deviceId:""
      },
      setLoading: false,
      ids: []
    }
  },
  methods: {

    // 初始化
    init(ids) {
      if (ids) {
        this.ids = ids
        this.visible = true
        this.formData.deviceId=ids[0]
        this.setLoading=false
        this.queryDataShareDevice()

      }
    },
    setDataShare() {
      this.setLoading=true
      this.$http({
        url:this.$http.adornUrl('/lampPole/card/enableDataShare'),
        method:'post',
        data: this.formData
      }).then(({data})=>{
        if (data&&data.code===0){
          this.$Message.success("success")
        }else {
          this.$Message.error(data.msg.msg)
        }
        this.setLoading=false
      })
    },

    //查询共享数据设备
    queryDataShareDevice(){
      this.$http({
        url: this.$http.adornUrl(`/lampPole/card/dataShareDevice/`+this.formData.deviceId),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.formData.dataKey=data.data.dataKey
          this.formData.isShare=data.data.isShare
          this.formData.cycle=data.data.cycle
        }
      })
    },

    //开关切换
    handleSwitchChange(){
      // if (this.formData.dataKey==""||this.formData.dataKey==null){
      if (this.formData.isShare) {
        this.formData.dataKey=this.generateRandomString(16)
      }
    },
    //生成指定位数的字符串
    generateRandomString(length) {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let randomString = '';
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        randomString += characters.charAt(randomIndex);
      }

      return randomString;
    },
    //点击复制
    copyKey() {
      //创建一个input标签
      var oInput = document.createElement("input");
      //将要复制的值赋值给input
      if (this.formData.dataKey == null || this.formData.dataKey == "") {
        oInput.value = this.$t('statistic.noDataKey')
      } else {
        oInput.value = this.formData.dataKey;
      }
      //在页面中插入
      document.body.appendChild(oInput);
      // 模拟鼠标选中
      oInput.select();
      // 执行浏览器复制命令（相当于ctrl+c）
      document.execCommand("Copy");
      //只是用一下input标签的特性，实际并不需要显示，所以这里要隐藏掉
      oInput.style.display = "none";
      this.$Message.success(this.$t('statistic.copySuccess'));
    }
  },
}
</script>

<style scoped>

</style>
