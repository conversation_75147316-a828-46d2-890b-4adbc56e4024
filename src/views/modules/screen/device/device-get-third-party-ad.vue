<template>
  <Modal width="900" v-model="visible">
    <p slot="header" style="text-align:center">
      {{ $t('thirdPartyAd.enabledThirdPartyAd') }}
    </p>
    <div>
      <Alert type="warning" show-icon>{{ $t('tips.configAdTip') }}<br>{{ $t('tips.configAdTip3') }}</Alert>
      <Form ref="thirdPartyAd" :model="thirdPartyAd" :rules="thirdPartyAdRuleValidate" :label-width="150">
        <FormItem :label="$t('thirdPartyAd.thirdPartyUrl')" prop="url">
          <Input v-model="thirdPartyAd.url" :placeholder="$t('common.PleaseInput')+$t('thirdPartyAd.thirdPartyUrl')" style="width: 300px"></Input>
        </FormItem>
        <FormItem :label="this.$t('broadcast.playMode')" prop="playMode">
          <Select v-model="thirdPartyAd.playMode" :placeholder="$t('common.PleaseSelect')+$t('broadcast.playMode')" style="width: 300px">
            <Option value="loop">loop</Option>
            <Option value="once">once</Option>
            <Option value="mutex">mutex</Option>
          </Select>
          <div v-if="thirdPartyAd.playMode=='loop'">
            <span>loop：{{ $t('thirdPartyAd.loopTip') }}</span>
          </div>
          <div v-else-if="thirdPartyAd.playMode=='once'">
            <span>once：{{ $t('thirdPartyAd.onceTip') }}</span>
          </div>
          <div v-else>
            <span>mutex：{{ $t('thirdPartyAd.mutexTip') }}</span>
          </div>
        </FormItem>
        <FormItem :label="$t('thirdPartyAd.adInterval')" prop="interval">
          <InputNumber v-model="thirdPartyAd.interval" :min="1" ></InputNumber><span>  {{$t('program.minute')}}</span>
        </FormItem>
        <FormItem>
          <Button @click="queryAd" type="info" :loading="queryLoading">{{ $t('thirdPartyAd.queryThirdPartyAd') }}</Button>
          <Button @click="enableAd" type="success" :loading="setLoading">{{ $t('thirdPartyAd.enabledThirdPartyAd') }}</Button>
        </FormItem>
      </Form>
      <div style="height: 300px">
        <div style="height:240px;overflow-y: auto;" v-if="resultData.length > 0">
          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="800 / 2 - 50" :isQuery="false"></cardResult>
        </div>
        <div v-if="queryResultData.length>0" style="height: 240px;overflow-y: auto">
          <cardResult :ids="ids" :resultData="queryResultData" :cardItemWidth="800 / 2 - 50"
                      :isQuery="true" :resultItem="[{text:'thirdPartyAd.thirdPartyUrl',name:'thirdPartyUrl',suffix:':'},
                  {text:'broadcast.playMode',name:'playMode',suffix:':'},
                  {text:'thirdPartyAd.adInterval',name:'adInterval',suffix:':'}]" ></cardResult>
        </div>
      </div>


    </div>
    <div slot="footer" style="text-align: left;">
        <span>
            <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{ item }}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  name: "device-get-third-party-ad",
  data() {
    return {
      visible: false,
      ids: [],

      thirdPartyAd:{
        url:'',
        interval: 60,
        playMode:'loop'
      },
      //校验规则
      thirdPartyAdRuleValidate:{
        url: [
          { required: true, message:'url'+this.$t('validate.not_empty'), trigger: 'blur' }
        ],
      },
      //结果集
      resultData:[],
      setLoading:false,
      queryLoading:false,
      queryResultData:[],
      dataColumns: [
        {title: 'id', key: 'recordId ', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div','id')
          }
        },
        {title: this.$t('home.cardNumber'), key: 'deviceId', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('home.cardNumber'))
          }
        },
        {title: 'mac', key: 'mac', align: 'center',
          renderHeader:(h)=>{
            return h('div','mac')
          }
        },
        {title: this.$t('file.file')+'Id', key: 'materialId', align: 'center',
          renderHeader:(h)=>{
            return h('div', this.$t('file.file')+'Id')
          }
        },
        {title: this.$t('thirdPartyAd.downloadUrl'), key: 'materialUrl', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('thirdPartyAd.downloadUrl'))
          }
        },
        {title: this.$t('thirdPartyAd.impression'), key: 'impression1', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('thirdPartyAd.impression'))
          }
        },{title: this.$t('program.PlayTime'), key: 'playSeconds', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('program.PlayTime'))
          }
        },
        {title: this.$t('program.play')+this.$t('home.date'), key: 'playDate', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('program.play')+this.$t('home.date'))
          }
        },
        {title: this.$t('thirdPartyAd.playHour'), key: 'playHour', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('thirdPartyAd.playHour'))
          }
        },
        {title: this.$t('thirdPartyAd.playCount'), key: 'playCount', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('thirdPartyAd.playCount'))
          }
        },
      ]
    }
  },
  methods: {
    // 初始化
    init(ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.resultData=[]
        this.queryResultData=[]
        this.queryLoading=false
        this.setLoading=false
        this.thirdPartyAd= {
          url: '',
          //周期
          interval: 60,
          //播放模式
          playMode: 'loop'
        }

      }
    },

    //确定开启广告
    enableAd() {
      this.resultData=[]
      this.queryResultData=[]
      this.$refs['thirdPartyAd'].validate((valid) => {
        if (valid){
          if (this.ids.length>0){
            this.setLoading = true
            this.$http({
              url: this.$http.adornUrl('/card/set/advertisement'),
              method: 'post',
              data: this.$http.adornData(
                {
                  'deviceIds': this.ids,
                  'url': this.thirdPartyAd.url,
                  'interval': this.thirdPartyAd.interval,
                  'playMode': this.thirdPartyAd.playMode,
                })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.resultData = data.data
              } else {
                this.$Message.error(data.msg)
              }
              this.setLoading = false
            })
          }
        }

      })

    },
    queryAd(){
      this.resultData=[]
      this.queryResultData=[]
      this.queryLoading=true
      var ids =  this.ids.join(",");
      this.$http({
        url: this.$http.adornUrl('/card/query/advertisement'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceIds': ids
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.queryResultData = data.data
        } else {
          this.$Message.error(data.msg)
        }
        this.queryLoading=false
      })
    },

  },
}
</script>

<style scoped>

</style>
