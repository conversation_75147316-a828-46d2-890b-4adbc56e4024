<template>
  <div>
    <Modal height="200" v-model="visible" width="700" footer-hide>
      <p slot="header" style="text-align:center;font-size: 20px;">
        <span>{{id}}</span>
      </p>
      <!-- <Alert type="warning" show-icon><b>{{$t('tips.ModifyTerminal')}}</b></Alert> -->
      <Form  :model="formItem" :label-width="150" inline label-colon style="margin-top: 20px;height:400px ">
        <FormItem :label="$t('lamp.poleName')">
          <Row>
            <Col span="23">
              <Input v-model="formItem.alias" style="width:352px;margin-right: 30px;"  placeholder=""></Input>
            </Col>
            <Col span="1">
              <Button type="primary" style="float:right;" :loading="alias_loading" @click="aliasUpdate()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
<!--        <FormItem prop="groupId" :label="$t('group.name')">-->
<!--          <Row>-->
<!--            <Col span="23">-->
<!--              <Select  :value="groupOption.value" style="width:352px;margin-right: 30px;" filterable clearable transfer :placeholder="$t('common.PleaseSelect') + $t('group.name')">-->
<!--                <Option :value="groupOption.value"  >{{groupOption.label}}</Option>-->
<!--                <Tree :data="groupList" :render="renderContent" style="height: 200px" ref="groupListTree" @on-select-change="groupChange">{{groupName}}</Tree>-->
<!--              </Select>-->
<!--            </Col>-->
<!--            <Col span="1">-->
<!--              <Button type="primary" style="float:right;" :loading="group_loading" @click="submitGroup()">{{$t('common.set')}}</Button>-->
<!--            </Col>-->
<!--          </Row>-->
<!--        </FormItem>-->
        <FormItem prop="groupId" :label="$t('common.selectingGroup')" >
          <Row>
            <Col span="23">
              <div style="width: 352px;margin-right: 30px;text-align: center">
                <span >{{ groupName }}</span>
              </div>
            </Col>
            <Col span="1">
              <Button type="primary" style="float:right;" :loading="group_loading" @click="changeGroup()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
<!--        <FormItem v-if="isAuth('screen:card:deploy')" prop="userId" :label="$t('common.AssignUsers')">-->
<!--          <Row>-->
<!--            <Col span="23">-->
<!--              <Select style="width:352px;margin-right: 30px;"  v-model="formItem.userId" :placeholder="$t('common.PleaseSelect') + $t('common.AssignUsers')" filterable clearable transfer>-->
<!--                <Option v-for="item in userList" :value="item.userId" :key="item.username">{{ item.username }}</Option>-->
<!--              </Select>-->
<!--            </Col>-->
<!--            <Col span="1">-->
<!--              <Button type="primary" style="float:right;" :loading="userId_loading" @click="submitUserId()">{{$t('common.set')}}</Button>-->
<!--            </Col>-->
<!--          </Row>-->
<!--        </FormItem>-->

        <FormItem :label="$t('card.screenWidth')">
          <Row>
            <Col span="23">
              <InputNumber v-model="formItem.width" placeholder="" :min="1" style="width: 95px;margin-right: 7px"></InputNumber>
              <span style="vertical-align: middle; font-size: 14px; color: #515a6e; line-height: 1;">{{$t('card.screenHeight')}}：</span>
              <InputNumber v-model="formItem.height" placeholder="" :min="1" style="width: 95px;margin-right: 30px;"></InputNumber>
            </Col>
            <Col span="1">
              <Button type="primary" style="float:right;" :loading="modal_loading" @click="submitUpdate()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
        <FormItem :label="$t('bigScreen.VideoSurveillance')">
          <Row>
            <Col span="23">
              <Input v-model="formItem.monitorAddress" style="width:352px;margin-right: 30px;"  placeholder=""></Input>
            </Col>
            <Col span="1">
              <Button type="primary" style="float:right;" :loading="address_loading" @click="monitorAddressUpdate()">{{$t('common.set')}}</Button>
            </Col>
          </Row>
        </FormItem>
        <div v-if="errorMsg" style="height: 50px; color: red; overflow-y: auto;">
          {{errorMsg}}
        </div>
      </Form>
    </Modal>
    <!--分组弹出框-->
    <Modal v-model="selectGroupVisible" width="500">
      <p slot="header" style="text-align:center">
        <span>{{$t('common.selectGroup')}}</span>
      </p>
      <Alert type="info" show-icon >
        <span>{{this.$t('tips.groupTip')}}</span>
      </Alert>
      <div>
        <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
      </div>
      <div slot="footer">
        <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
        <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      formItem: {
        alias: '',
        groupId: 0,
        userId: 0,
        width: 0,
        height: 0,
        monitorAddress:'',
      },
      errorMsg: '',
      id: '',
      visible: false,
      alias_loading: false,
      modal_loading: false,
      groupList: [],
      group_loading: false,
      // userList: [],
      userId_loading: false,
      groupName:"",
      groupCount:0,
      groupOption:{
          value:"",
          label:""
      },
      //选择分组时，分组框是否可见
      selectGroupVisible:false,
      rootNode:null,
      totalNum:0,
      //监控地址
      address_loading:false,
      addressArr:[],
    }
  },
  methods: {
    // 初始化
    init (id,totalNum) {
      // this.groupOption={
      //   value:"",
      //   label:""
      // }
      this.id = id
      this.totalNum=totalNum
      if (this.id !== '') {
        this.$http({
          url: this.$http.adornUrl('/lampPole/card/getSettingItem'),
          method: 'post',
          data: this.id
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.formItem.alias = data.card.selectInfo.alias
            this.formItem.groupId = data.card.selectInfo.group_id + ""
            this.formItem.userId = data.card.selectInfo.user_id
            this.formItem.width = data.card.selectInfo.width
            this.formItem.height = data.card.selectInfo.height
            // this.userList = data.card.userList
            this.groupList = data.card.groups
            this.groupName=data.card.selectInfo.groupName
            this.formItem.monitorAddress=data.card.selectInfo.monitor_address
            this.getUnGroupDevice()
          } else {
            this.$Message.error(data.msg)
          }
        })
        this.visible = true
      } else {
        this.$Message.error(this.$t('tips.numberEmpty'))
      }
    },
    // 修改别名
    aliasUpdate () {
      this.alias_loading = true
      this.$http({
        url: this.$http.adornUrl('/card/set/setCardAlias'),
        method: 'post',
        data: this.$http.adornData({'cardId': this.id, 'alias': this.formItem.alias}, false)
      }).then(({data}) => {
        this.alias_loading = false
        if (data && data.code !== 0) {
          // this.$Message.error(data.msg)
          this.errorMsg = data.msg
        } else {
          this.visible = false
        }
      })
    },
    // 修改分组
    submitGroup () {
      this.group_loading = true
          this.$http({
          url: this.$http.adornUrl(`/lampPole/card/updateGroup`),
          method: 'post',
          data: this.$http.adornData({
              'cardIds': [this.id],
              'group': this.formItem.groupId
          })
      }).then(({data}) => {
          if (data && data.code === 0) {
              this.group_loading = false
              this.$Message.success({
                  content: this.$t('common.operationSuccessful'),
                  duration: 0.5,
                  onClose: () => {
                      this.visible = false
                      this.$emit('refreshDataList')
                  }
              })
          } else {
              this.$Message.error(data.msg)
              setTimeout(() => {
                  this.group_loading = false
              }, 500)
          }
      })
    },
    // 分配所属用户
/*    submitUserId () {
      this.userId_loading = true
      var ids=[];
      ids.push(this.id)
      this.$http({
        url: this.$http.adornUrl('/lampPole/card/deploy'),
        method: 'post',
        data: this.$http.adornData({
          'cardIds': ids,
          'userId': this.formItem.userId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userId_loading = false
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 0.5,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.userId_loading = false
          this.$Message.error(data.msg)
        }
      })
    },*/
    submitUpdate () {
      if(this.formItem.width <=0 || this.formItem.width.length ===0
        || this.formItem.height <=0 || this.formItem.width.height ===0) {
        this.$Message.error(this.$t('common.beEmpty'))
      return
      }
      this.modal_loading = true
      this.$http({
        url: this.$http.adornUrl('/card/set/setCardWidHei'),
        method: 'post',
        data: this.$http.adornData({'cardId': this.id, 'width': this.formItem.width, 'height': this.formItem.height}, false)
      }).then(({data}) => {
        this.modal_loading = false
        if (data && data.code !== 0) {
          // this.$Message.error(data.msg)
          this.errorMsg = data.msg
        } else {
          this.visible = false
        }
      })
    },
    changeGroup(){
      // this.getGroupList()
      this.selectGroupVisible=true
    },
    // 表单提交
    dataFormSubmit () {
      var rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      this.formItem.groupId=rootNode.id
      this.selectGroupVisible=false
      this.submitGroup()
      this.groupName=rootNode.name
      this.rootNode=null
    },
    cancelSelect(){
      this.selectGroupVisible=false
      // this.dataForm.group=[]
      // this.groupList=[]
      // this.groupName=this.$t('common.selectGroup')
      this.rootNode=null
      // this.getGroupList()

    },
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },

    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
    monitorAddressUpdate(){
      this.address_loading = true
      this.$http({
        url: this.$http.adornUrl('/screen/card/setMonitorAddress'),
        method: 'post',
        data: this.$http.adornData({'deviceId': this.id, 'url': this.formItem.monitorAddress}, false)
      }).then(({data}) => {
        this.address_loading = false
        if (data && data.code !== 0) {
          this.$Message.error(data.msg)
          this.errorMsg = data.msg
        } else {
          this.$Message.success(data.msg)
          this.visible = false
        }
      })
    },
    clearLoading(){
        this.alias_loading= false
        this.modal_loading= false
        this.group_loading= false
        this.userId_loading= false
        this.address_loading=false
    },
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.clearLoading()
        setTimeout(() => {
          this.formItem._type = ''
          this.formItem.width = 0
          this.formItem.height = 0
          this.id = ''
          this.$emit('refreshDataList')
          this.errorMsg = ''
        }, 200)
      }
    }
  }
}
</script>
<style scoped>
</style>
