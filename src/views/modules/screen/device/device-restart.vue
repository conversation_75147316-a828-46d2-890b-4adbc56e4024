<template>
  <div>
    <Modal v-model="visible" width="500">
      <p slot="header" style="text-align:center;font-size: 20px;">
          <span>{{$t('operation.restartSys')}}</span>
      </p>
      <div style="height:280px;overflow: hidden;">
        <RadioGroup v-model="isTime">
          <Radio label="false">
              <span>{{$t('card.rebootNow')}}</span>
          </Radio>
          <Radio label="true">
              <span>{{$t('card.timing')}}</span>
          </Radio>
        </RadioGroup>
        <div style="margin-top: 5px" v-if="isTime === 'true'">
          <TimePicker format="HH:mm" :value="time" @on-change="handleChange" @on-clear="handleClear" :placeholder="$t('common.PleaseSelect') + $t('card.timing')"></TimePicker>
          <Button :loading="setTime_loading"  type="primary" @click="setTimeReboot()">{{$t('common.set')}}</Button>
          <Button :loading="queryTime_loading"  type="success" @click="queryTimeReboot()">{{$t('common.query')}}</Button>
          <Button :loading="clearTime_loading"  type="warning" @click="clearTimeReboot()">{{$t('el.colorpicker.clear')}}</Button>
          <div style="height: 300px;width:100%;float:left;">
            <Form label-position="left" label-colon>
              <FormItem v-if="timeResultData.length > 0"  style="height: 300px;overflow-y: auto">
                <div v-for="(item, index) in timeResultData" :key="index">
                  <FormItem label="ID">
                  <div>{{item.deviceId}}</div>
                  </FormItem>
                  <FormItem :loading="true">
                    {{item.result}}
                    <div v-if="item._type === undefined">
                      <div style="color: red;">{{item}}</div>
                    </div>
                    <div v-else-if="item._type !== 'success'">
                      <div style="color: red;">{{item.msg}}</div>
                    </div>
                    <div v-else-if="item.time || item.time === ''">
                      <div v-if="item.time === ''">
                        {{$t('card.noTiming')}}
                      </div>
                      <div v-else>{{$t('card.timing')}}：{{item.time}}</div>
                    </div>
                    <div v-else>
                      <div style="color:green;">{{$t('setTime.setupSuccess')}}</div>
                    </div>
                  </FormItem>
                  <Divider/>
                </div>
              </FormItem>
            </Form>
          </div>
        </div>
        <div v-else>
          <svg @click="dataFormSubmit()" style="height:120px;width:120px;margin-left:175px;margin-top:40px;cursor: pointer;">
            <use xlink:href="#processRestart"></use>
          </svg>
        </div>
      </div>
            <!-- <a style="margin-left:95px;" @click="dataFormSubmit()">↑点击重启↑</a> -->
      <div slot="footer" style="text-align: left;">
            <span>
                <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
    </Modal>
    <div v-if="showProgress">
      <div class="adv" v-if="!isMinimize">
        <svg style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
          <use xlink:href="#zuixiaohua"></use>
        </svg>
        <span style="text-align:center;">{{$t('common.WaitingForRestart')}}</span>
        <Divider />
        <div style="margin-top: 10px;overflow-x: hidden;overflow-y: auto;height:110px" v-if="successTemp.length > 0">
          <div v-for="(item,index) in successTemp" :key="index">
            <div v-if="item" style="margin-bottom: 3px">{{item.deviceId}}
              <Progress :percent="item.progress" :stroke-width="20" status="active" text-inside v-if="!item.restartFlag"/>
              <div style="color: red;" v-else>{{$t('common.RestartTimeout')}}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="isMinimize" class="adv" style="width: 250px;height: 40px;">
        <svg style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
          <use xlink:href="#zuidahua"></use>
        </svg>
        <span style="text-align:center;">{{$t('common.WaitingForRestart')}}</span>
        <Divider />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      ids: [],
      modal_loading: false,
      successTemp: [],
      successTempLenght: 0,
      showProgress: false,
      isMinimize: false,
      resultData: [],
      isTime: "false",
      time: '10:50',
      setTime_loading: false,
      queryTime_loading: false,
      clearTime_loading: false,
      timeResultData: [],
    }
  },
  methods: {
    // 初始化
    init (ids) {
      this.isTime = "false"
      this.time = '10:50'
      this.visible = true
      this.timeResultData = []
      if (ids) {
        this.ids = ids
      } else {
        this.ids = []
      }
    },
    handleChange(time) {
      this.time = time
    },
    handleClear () {
      this.time = '';
    },
    // 清除定时
    clearTimeReboot() {
      if (this.ids.length > 0) {
        this.clearTime_loading = true
        this.timeResultData = []
        this.$http({
          url: this.$http.adornUrl('/card/set/setTimingReboot'),
          method: 'post',
          data: this.$http.adornData({
            'ids': this.ids,
            'time': ''
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.timeResultData = data.data;
          } else {
            this.$Message.error(data.msg);
          }
          this.clearTime_loading = false
        })
      }
    },
    // 设置定时重启
    setTimeReboot() {
      if (this.ids.length > 0 && this.time !== '') {
        this.setTime_loading = true
        this.timeResultData = []
        this.$http({
          url: this.$http.adornUrl('/card/set/setTimingReboot'),
          method: 'post',
          data: this.$http.adornData({
            'ids': this.ids,
            'time': this.time
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.timeResultData = data.data;
          } else {
            this.$Message.error(data.msg);
          }
          this.setTime_loading = false
        })
      }
    },
    // 查询定时重启
    queryTimeReboot() {
      if (this.ids.length > 0) {
        this.queryTime_loading = true
        this.timeResultData = []
        this.$http({
            url: this.$http.adornUrl('/card/query/getTimingReboot'),
            method: 'post',
            data: this.ids
          }).then(({data}) => {
            if (data && data.code == 0) {
              this.timeResultData = data.data
            } else {
              this.$Message.error(data.msg)
            }
            this.queryTime_loading = false
          })
      }
    },
    // 最大化最小化
    handleMinimize (isMinimize) {
      this.isMinimize = !isMinimize
    },
    // 提交数据
    dataFormSubmit () {
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t('common.restartCards'),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          if (this.ids.length > 0) {
            this.resultData = []
            this.modal_loading = true
            // var idsLength = this.ids.length
            // this.ids.forEach(item => {
              this.$http({
                url: this.$http.adornUrl('/card/set/restart'),
                method: 'post',
                data: this.ids
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.resultData = data.data
                } else {
                  this.$Message.error(data.msg)
                  setTimeout(() => {
                    this.visible = false
                    this.modal_loading = false
                    this.$emit('refreshDataList')
                  }, 500)
                }
                // idsLength--
                // if (idsLength === 0) {
                  if (this.resultData.length > 0) {
                    var errorTemp = ''
                    for (var i = 0; i < this.resultData.length; i++) {
                      if (this.resultData[i]._type === 'success') {
                        var temp = {deviceId: this.resultData[i].deviceId, progress: 0, timer: null, flag: true, totalTime: 0, restartFlag: false}
                        this.successTemp.push(temp)
                      } else {
                        errorTemp += `<div>${this.resultData[i].deviceId} ${this.resultData[i].msg}</div>`
                      }
                    }
                    if (errorTemp) {
                      this.$Notice.warning({
                        title: this.$t('common.tips'),
                        desc: errorTemp,
                        duration: 2,
                        onClose: () => {
                          setTimeout(() => {
                            this.visible = false
                            this.modal_loading = false
                            if (this.successTemp.length > 0) { // 批量操作时并不是所有卡都操作失败
                              this.showProgress = true
                            }
                            this.$emit('refreshDataList')
                          }, 500)
                        }
                      })
                    }
                    // 重启命令发送成功，尝试获取卡信息
                    if (this.successTemp.length > 0) {
                      this.successTempLenght = this.successTemp.length
                      var _this = this
                      for (var item in this.successTemp) {
                        (function (item) {
                          var date = new Date()
                          var min = date.getMinutes()
                          date.setMinutes(min + 5)
                          _this.successTemp[item].totalTime = date.getTime()// 设置总时长为五分钟后
                          _this.successTemp[item].timer = setInterval(function () {
                            if (_this.successTemp[item].progress === 100) {
                              clearInterval(_this.successTemp[item].timer)
                              _this.successTemp[item].timer = null
                              _this.successTemp[item].progress = 0
                              _this.successTemp[item] = null
                              _this.successTempLenght--
                            } else if (_this.successTemp[item].progress > 60 && _this.successTemp[item].progress < 90) { // 进度在60-90之间时
                              if (_this.successTemp[item].flag === true) { // 尝试获取连接是否断开
                                _this.successTemp[item].flag = false
                                _this.$http({
                                  url: _this.$http.adornUrl(`/card/query/tryConn/${_this.successTemp[item].deviceId}`),
                                  method: 'get',
                                  data: _this.$http.adornParams()
                                }).then(({data}) => {
                                  if (data && data.code === 0) {
                                    if (data.msg._type === 'error') { // 说明是断开的
                                      _this.successTemp[item].progress += 1
                                    } else { // 能够正常通讯直接将进度设置为100
                                      _this.successTemp[item].progress = 100
                                    }
                                  }
                                })
                              } else {
                                _this.successTemp[item].progress += Math.floor(Math.random() * 3 + 1)
                                if (_this.successTemp[item].progress > 80 && _this.successTemp[item].progress < 85) { // 进度为80以上时再次尝试连接是否断开
                                  _this.successTemp[item].flag = true
                                }
                              }
                            } else if (_this.successTemp[item].progress >= 90) {
                              if (_this.successTemp[item].flag === true) { // 尝试获取连接是否断开
                                _this.successTemp[item].flag = false
                                _this.$http({
                                  url: _this.$http.adornUrl(`/card/query/tryConn/${_this.successTemp[item].deviceId}`),
                                  method: 'get',
                                  data: _this.$http.adornParams()
                                }).then(({data}) => {
                                  if (data && data.code === 0) {
                                    if (data.msg._type === 'error') { // 说明是断开的
                                      if (_this.successTemp[item].progress < 98) {
                                        _this.successTemp[item].progress += 1
                                      }
                                    } else { // 能够正常通讯直接将进度设置为100
                                      _this.successTemp[item].progress = 100
                                    }
                                  }
                                })
                              } else {
                                if (_this.successTemp[item].progress <= 98) {
                                  _this.successTemp[item].progress += 1
                                }
                                if (_this.successTemp[item].progress >= 93) {
                                  _this.successTemp[item].flag = true
                                }
                              }
                            } else {
                              _this.successTemp[item].progress += Math.floor(Math.random() * 8 + 1)
                            }
                            // 当前时间如果与前面设置的时间相等视为超时
                            if (_this.successTemp[item] && _this.successTemp[item].totalTime <= new Date().getTime()) {
                              _this.successTemp[item].restartFlag = true
                              _this.$Notice.warning({
                                title: _this.$t('common.tips'),
                                desc: _this.successTemp[item].deviceId + _this.$t('common.RestartTimeout'),
                                duration: 2,
                                onClose: () => {
                                  setTimeout(() => {
                                    clearInterval(_this.successTemp[item].timer)
                                    _this.successTemp[item].timer = null
                                    _this.successTemp[item].progress = 0
                                    _this.successTemp[item] = null
                                    _this.successTempLenght--
                                  }, 500)
                                }
                              })
                            }
                          }, 3000)
                        })(item)
                      }
                      if (!errorTemp) {
                        setTimeout(() => {
                          this.visible = false
                          this.modal_loading = false
                          this.showProgress = true
                          this.$emit('refreshDataList')
                        }, 500)
                      }
                    }
                  }
                // }
              })
            // })
          }
        }
      });


    }
  },
  watch: {
    'successTempLenght': function (newVal, oldVal) {
      // 当重启完成一张卡时刷新列表
      if (oldVal > newVal) {
        this.$emit('refreshDataList')
      }
      if (newVal === 0 && oldVal === 1) { // 重启完成所有卡关闭进度条页面
        this.showProgress = false
        this.successTemp = []
        this.resultData = []
      }
    }
  }
}
</script>
<style scoped>
.adv {
  position: fixed;
  right: 10px;
  bottom: 10px;
  border-radius: 2%;
  background-color: rgb(255, 255, 255);
  width: 250px;
  height: 150px;
  padding: 5px;
  overflow: hidden;
}
</style>
