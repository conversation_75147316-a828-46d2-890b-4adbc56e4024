<template>
  <Modal v-model="visible" width="900">
    <p slot="header" style="text-align:center;font-size: 20px;">
      <span>{{$t('operation.gpsInfo')}}</span>
    </p>
    <div style="height: 300px;">
      <Form style="height: 70px;" label-position="left" label-colon>
        <FormItem>
          <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('operation.getGspInfo')}}</Button>
        </FormItem>
        <div v-if="resultData.length > 0" style="height: 230px;overflow-y: auto;">
          <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="850 / 2 - 50" :isQuery="true" :tableHeight="124" :resultHeight="96"
                :resultItem="[{text: 'lamp.longitude',name: 'lat'}, {text: 'lamp.latitude',name: 'lng'},
                {text: 'program.speed',name: 'speed', unit: ' km/h'}, {text: 'screen.satelliteNumber',name: 'satelliteNumber'}]"/>
        </div>
      </Form>
    </div>
    <div slot="footer" style="text-align: left;">
      <span>
          <Alert type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
      </span>
      <div style="overflow-y: auto;max-height:42px;">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
        </Breadcrumb>
      </div>
    </div>
  </Modal>
</template>

<script>
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    cardResult
  },
  data () {
    return {
      visible: false,
      ids: [],
      modal_loading: false,
      resultData: [],
      gpsInformation:''
    }
  },
  methods: {
    // 初始化
    init (ids) {
      if (ids) {
        this.visible = true
        this.ids = ids
        this.resultData=[]
        this.modal_loading=false
      }
    },
    // 查詢gps
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.modal_loading = true
        this.resultData = []
        this.$http({
          url: this.$http.adornUrl('/card/query/getGpsInfo'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.ids = []
        this.resultData = []
        this.modal_loading=false
      }
    }
  }
}
</script>
