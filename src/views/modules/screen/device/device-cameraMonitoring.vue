<template>
    <div style="background-color: #fff; width: 100%; height: 100%;">
        <div v-if="ids.length === 1" class="cell-player">
            <div :class="cellClass(1)">
                <div class="player">
                    {{ ids[0] }}
                    <video id="elementId" autoplay muted="muted" width="100%" :height="documentClientHeight - 30">
                        {{$t('common.Loading')}}
                    </video>
                </div>
            </div>
        </div>
        <div v-else class="cell-player">
            <div v-if="ids.length > 0" :class="cellClass(ids.length)" v-for="(item, i) in ids" :key="i">
                <div class="player">
                    {{ item }}
                    <video :id="'videoElement' + i"  autoplay muted="muted" width="100%" style="height:calc(100% - 30px)">
                        {{$t('common.Loading')}}
                    </video>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import flvjs from "flv.js";
import {generateToken} from '@/utils/jwtUtils'
export default {
    data() {
        return {
            // 卡号
            ids: [],
            documentClientHeight: 0,
            lastDecodedFrame: 0,
            elementId: '',
            //flvPlayer数组，方便销毁
            flvPlayerArr:[],
        }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.ids = JSON.parse(this.$route.query.deviceIds || []);
            this.documentClientHeight = document.documentElement['clientHeight'];
            this.$nextTick(()=>{
                this.changeMultiVideo()
            });
        },
        cellClass(val){
            switch (val) {
                case 1:
                    return ['cell-player-1']
                case 2:
                    return ['cell-player-2']
                case 3:
                    return ['cell-player-4']
                case 4:
                    return ['cell-player-4']
                case 5:
                    return ['cell-player-6']
                case 6:
                    return ['cell-player-6']
                // case 7:
                //     return ['cell-player-9']
                // case 8:
                //     return ['cell-player-9']
                // case 9:
                //     return ['cell-player-9']
                // case 16:
                //     return ['cell-player-16']
                default:
                    return ['cell-player-6']
            }
        },
        //不同的播放策略
        changeMultiVideo(){
            this.destroyVideo()
            this.$nextTick(()=>{
                //单个监控则不需要额外的video和id
                if (this.ids.length === 1){
                    this.playVideo(this.ids[0], "")
                } else {
                    for (let i = 0; i < this.ids.length; i++) {
                        this.playVideo(this.ids[i],i)
                    }
                }
            });
        },
        //销毁断流方法
        destroyVideo() {
            let _this = this;
            for(let i = 0;i < _this.flvPlayerArr.length;i++){//卸载已经加载的视频资源
                if (_this.flvPlayerArr[i] != null){
                    _this.flvPlayerArr[i].pause();
                    _this.flvPlayerArr[i].unload();
                    _this.flvPlayerArr[i].detachMediaElement();
                    _this.flvPlayerArr[i].destroy();
                    _this.flvPlayerArr[i] = null;
                }
            }
        },
        playVideo(device, id) {
            let _this = this;
            var deviceId = device
            _this.lastDecodedFrame = 0
            _this.elementId = deviceId
            let videoWin = ""
            if (_this.ids.length === 1) {
                videoWin = document.getElementById("elementId");
            } else {
                _this.elementId = id
                videoWin = document.getElementById('videoElement' + id);
            }
            // 加载前先销毁
            if (flvjs.isSupported()) {
                let flvPlayer = flvjs.createPlayer({
                    type: "flv",// 媒体类型
                    isLive: true,//是否是实时流
                    hasAudio: false,//是否有音频
                    url: window.SITE_CONFIG.rtmp + deviceId +"&token="+ generateToken(deviceId),// 视频流地址
                    stashInitialSize: 128 // 减少首帧显示等待时长
                }, {
                    enableWorker: false,// 不启动分离线程
                    enableStashBuffer: false,// 关闭IO隐藏缓冲区
                    reuseRedirectedURL: true,// 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
                    autoCleanupSourceBuffer: true, // 自动清除缓存
                    fixAudioTimestampGap: false,// false 音频同步
                });
                // 断流重连
                flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
                    console.log("errorType:", errorType);
                    console.log("errorDetail:", errorDetail);
                    console.log("errorInfo:", errorInfo);
                if (flvPlayer) {
                    this.destroyVideo();
                }
                });
                // 画面卡死重连
                flvPlayer.on("statistics_info", function (res) {
                if (_this.lastDecodedFrame == 0) {
                    _this.lastDecodedFrame = res.decodedFrames;
                    return;
                }
                if (_this.lastDecodedFrame != res.decodedFrames) {
                    _this.lastDecodedFrame = res.decodedFrames;
                } else {
                    _this.lastDecodedFrame = 0;
                    if (flvPlayer) {
                    // _this.destroyVideo();
                    // _this.playVideo(device,deviceId)
                    _this.changeMultiVideo()
                    }
                }
                });
                flvPlayer.attachMediaElement(videoWin);
                flvPlayer.load();
                _this.flvPlayerArr.push(flvPlayer)
            }
        },
    }
}
</script>
<style scoped>
.player {
  background-color: #000;
  height: 100%;
  border: 1px solid white;
  color: white;
  text-align: center;
}
.cell-player {
  height: 100%;
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.cell-player-1 {
  width: 100%;
  box-sizing: border-box;
}
.cell-player-2 {
  width: 50%;
  box-sizing: border-box;
}
.cell-player-4 {
  width: 50%;
  height: 50% !important;
  box-sizing: border-box;
}
.cell-player-6 {
  width: 33.33%;
  height: 50% !important;
  box-sizing: border-box;
}

.cell-player-9 {
  width: 33.33%;
  height: 33.33% !important;
  box-sizing: border-box;
}

.cell-player-16 {
  width: 25%;
  height: 25% !important;
  box-sizing: border-box;
}
</style>