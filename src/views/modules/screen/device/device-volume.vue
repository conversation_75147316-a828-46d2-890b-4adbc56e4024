<template>
    <Modal v-model="visible"  width="900">
        <p slot="header" style="text-align:center;font-size: 20px;">
            <span>{{$t('operation.volumeControl')}}</span>
        </p>
        <Tabs :value="tabVal" @on-click="handlerTab" style="height: 650px">
          <TabPane :label="$t('operation.volumeControl')" name="tab1">
            <Alert type="warning" show-icon ><b class="tip">{{$t('tips.volume')}}</b></Alert>
            <Form :label-width="50" label-position="left" label-colon>
              <FormItem  :label="$t('cardDevice.volume')">
                <InputNumber :max="15" :min="1" v-model="volume"></InputNumber>
                <Button style="margin-left:20px" :loading="modal_loading"  type="primary" @click="dataFormSubmit()">{{$t('common.set')}}</Button>
              </FormItem>
              <div v-if="resultData.length > 0" style="height: 495px;overflow-y: auto">
                <cardResult :ids="ids" :resultData="resultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
              </div>
            </Form>
          </TabPane>
          <TabPane :label="$t('card.timedVolume')" name="tab2">
            <Form inline label-position="left">
              <FormItem style="margin-bottom: 10px">
                <Input v-model="name" :placeholder="$t('common.name')"></Input>
              </FormItem>
              <FormItem style="margin-bottom: 10px">
                <Button style="margin-right:6px" @click="getDataList()">
                  <div style="margin:3px 8px">{{$t('common.query')}}</div>
                </Button>
                <Button style="margin-right:6px" type="primary" @click="addOrUpdateHandle()">
                  <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
                </Button>
              </FormItem>
            </Form>
            <Table border :columns="dataConlums" :data="dataList"
            :loading="dataListLoading" style="width: 100%" :max-height="300" ref="selection">
              <template slot-scope="{ row, index }" slot="operation">
                  <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" :loading="row.timeLoading" @click="setTimeHandle(row.id)">{{$t('card.setTiming')}}</Button>
                  <Button type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.id)">{{$t('common.update')}}</Button>
                  <Button type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.id)">{{$t('common.delete')}}</Button>
              </template>
            </Table>
            <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
            @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
            <Form style="height: 180px;overflow-y: auto;clear: both;" :label-width="30" label-position="left">
              <div v-if="schedulesResultData.length > 0">
                <cardResult :ids="ids" :resultData="schedulesResultData" :cardItemWidth="900 / 2 - 50" :isQuery="false"></cardResult>
              </div>
            </Form>
          </TabPane>
          <TabPane :label="$t('operation.queryTiming')" name="tab3">
            <Form inline :label-width="30" label-position="left">
              <Button :loading="getTimed_loading"  type="success" @click="getTimedVolume()">{{$t('card.getTiming')}}</Button>
              <div style="height: 520px;overflow-y: auto">
                <div v-if="timedVolume.length > 0">
                  <cardResult :ids="ids" :resultData="timedVolume" :cardItemWidth="800" :isQuery="true" :tableHeight="200"
                  :isTable="true" tableFieldNameLv1='taskVolume' tableFieldNameLv2='items' 
                  :tableFieldOther="[{text: 'card.defaultVolume', name: 'defaultVolume'}]"
                  :tableColumns='dataColumns'></cardResult>
                </div>
              </div>
            </Form>
          </TabPane>
        </Tabs>
         <div slot="footer" style="text-align: left;">
            <span>
                <Alert  type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
            </span>
            <div style="overflow-y: auto;max-height:42px;">
            <Breadcrumb>
              <BreadcrumbItem v-for="item in ids" :key="item" style="color: #999;font-weight: normal">{{item}}</BreadcrumbItem>
            </Breadcrumb>
            </div>
        </div>
        <!-- 定时-->
        <device-schedules v-if="setTimingVisible" ref="deviceSchedules" @schedules-result-data="getDataList"></device-schedules>
    </Modal>
</template>

<script>
import deviceSchedules from './device-schedules'
import cardResult from "@/utils/cardResult.vue"
export default {
  components: {
    deviceSchedules,
    cardResult
  },
  data () {
    return {
      visible: false,
      ids: [],
      tabVal: 'tab1',
      volume: 10,
      dataForm: {},
      modal_loading: false,
      resultData: [],
      name: '',
      dataConlums: [
        // {type: 'selection', width: 60, align: 'center'},
        // {title: 'ID', key: 'id', width: 80, align: 'center'},
        {title: this.$t('common.name'), key: 'name', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.name'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      addOrUpdateVisible: false,
      timedVolume: [],
      dataColumns: [
        {
          title:  this.$t('card.timedVolume'),
          key: 'volume',
          align: 'center',
          width: '200',
          render: (h, { row, index }) => {
            return h('span', {}, row.volume)
          },
          renderHeader: (h) => {
            return h('div', this.$t('card.timedVolume'))
          },
        },
        {
          title: this.$t('card.DateRange'), // 日期范围
          key: 'schedule.dateType',
          align: 'center',
          width: '200',
          render: (h, {row, index}) => {
            var result = ''
            if (row.schedule.dateType === 'Range') {
              result = [
                h('span', {}, row.schedule.startDate),
                h('span', {}, ' -- '),
                h('span', {}, row.schedule.endDate)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.DateRange'))
          }
        },
        {
          title: this.$t('card.timeFrame'), // 时间范围
          key: 'schedule.timeType',
          align: 'center',
          width: '200',
          render: (h, {row, index}) => {
            var result = ''
            if (row.schedule.timeType === 'Range') {
              result = [
                h('span', {}, row.schedule.startTime),
                h('span', {}, ' -- '),
                h('span', {}, row.schedule.endTime)
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.timeFrame'))
          }
        },
        {
          title: this.$t('card.SpecifyWeek'), // 星期范围
          key: 'schedule.filterType',
          align: 'center',
          width: '350',
          tooltip: true,
          render: (h, {row, index}) => {
            var result = ''
            if (row.schedule.filterType === 'Week') {
              result = [
                h('span', {}, this.getWeekDay(row.schedule.weekFilter))
              ]
            } else {
              result = h('span', {}, this.$t('card.notSpecified'))
            }
            return result
          },
          renderHeader:(h)=>{
              return h('div',this.$t('card.SpecifyWeek'))
          }
        }
      ],
      schedulesResultData: [],
      setTimingVisible: false,
      getTimed_loading: false
    }
  },
  methods: {
    // 初始化
    init (ids) {
      this.visible = true
      if (ids) {
        this.ids = ids
      } else {
        this.ids = []
      }
    },
    handlerTab (name) {
      this.tabVal = name
      if (this.tabVal === 'tab2') {
        this.getDataList()
      }
      this.clearLoading()
    },
    // 查询定时音量列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/schedules/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.name,
          'type': 2
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 添加定时音量
    addOrUpdateHandle(id) {
      this.setTimingVisible = true
      this.$nextTick(() => {
        // 传入1表示当前是一个定时音量任务
        this.$refs.deviceSchedules.init(2, id)
      })
    },
    // 设置定时音量
    setTimeHandle (id) {
      if (this.ids.length > 0) {
        this.schedulesResultData = []
        this.dataList.map(item => {
          if (item.id === id) {
            item.timeLoading = true;
          }
        })
        this.$http({
          url: this.$http.adornUrl('/card/set/schedules'),
          method: 'post',
          data: this.$http.adornData({
            'id': id,
            'deviceIds': this.ids
          })
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.schedulesResultData = data.data
          } else {
            this.$Message.error(data.msg);
          }
          this.dataList.map(item => {
            if (item.id === id) {
              item.timeLoading = false;
            }
          })
        })
      }
    },
    // 查询定时
    getTimedVolume () {
      if (this.ids.length > 0) {
        this.timedVolume = []
        this.getTimed_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/query/getTimedVolume'),
          method: 'post',
          data: this.ids
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.timedVolume = data.data
          } else {
            this.$Message.error(data.msg)
          }
            this.getTimed_loading = false
        })
      }
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    deleteHandle (id) {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/screen/schedules/delete'),
            method: 'post',
            data: this.$http.adornData(id, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1) {
                    this.pageIndex--
                  }
                  this.getDataList()
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 提交数据
    dataFormSubmit () {
      if (this.ids.length > 0) {
        this.resultData = []
        this.modal_loading = true
        this.$http({
          url: this.$http.adornUrl('/card/set/volume'),
          method: 'post',
          data: this.$http.adornData({'ids': this.ids, 'volume': this.volume},false)
        }).then(({data}) => {
          if (data && data.code == 0) {
            this.resultData = data.data
          } else {
            this.$Message.error(data.msg)
          }
          this.modal_loading = false
        })
      }
    },
    clearLoading(){
      this.modal_loading=false
      this.getTimed_loading=false
    },
     /**
     * 根据星期数字获取对应的国际化,返回字符串
     * 
     */
     getWeekDay(week) {
  
  let weelStr = ''
  if (week.length > 0) {
    week.forEach(item => {
      switch (item) {
    case 1:
      weelStr+=this.$t('common.Monday')+','
      break
    case 2:
      weelStr+=this.$t('common.Tuesday')+','
      break
    case 3:
      weelStr+=this.$t('common.Wednesday')+','
      break
    case 4:
      weelStr+=this.$t('common.Thursday')+','
      break
    case 5:
      weelStr+=this.$t('common.Friday')+','
      break
    case 6:
      weelStr+=this.$t('common.Saturday')+','
      break
    case 7:
      weelStr+=this.$t('common.Sunday')
      break
    default:
      weelStr=''
  }
    })
  
    weelStr=weelStr.substring(0,weelStr.length-1)
  }
  return weelStr
}
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.tabVal = 'tab1'
        this.resultData = []
        this.timedVolume = []
        this.schedulesResultData = []
        this.volume = 10
        this.clearLoading()
        this.$emit('refreshDataList')
      }
    }
  }
}
</script>
