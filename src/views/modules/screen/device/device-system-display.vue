<template>
  <div>
    <Modal height="500" v-model="visible" width="1050">
      <p slot="header" style="text-align: center; font-size: 20px">
        <span>{{ $t("operation.systemDisplay") }}</span>
      </p>
      <div class="schedules">
        <Form :inline="true" >
          <FormItem>
            <Input v-model="fileName" :placeholder="$t('file.name')" style="width: 300px"/>
          </FormItem>
          <FormItem>
            <Button type="success" icon="ios-search-outline" @click="querySystemDisplayFile()">{{$t('common.query')}}</Button>
          </FormItem>
          <FormItem>
              <Button v-if="isAuth('device:uploadSystemDisplayFile')" type="primary" icon="ios-cloud-upload-outline" @click="uploadFile()">{{ $t("file.upload") }}</Button>
              <device-system-display-file-add v-if="uploadDisplayFileVisible" @uploadDisplayFile="querySystemDisplayFile" ref="uploadDisplayFile"></device-system-display-file-add>
          </FormItem>
        </Form>
        <div style="height: 10px"></div>
        <Table stripe :columns="dataConlums" :data="pageLists" :max-height="tableHeight" :loading="dataListLoading">
          <template slot-scope="{ row, index }" slot="number">
            {{index+1}}
          </template>

          <template slot-scope="{ row }" slot="fileSize">
            <span>{{ row.fileSize | filterType }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="operation">

            <Button type="primary" size="small" :loading="setLoading" @click="setSystemDisplay(row.fileId)">
              <Icon type="md-settings" />{{ $t("common.set") }}</Button>
            <Button type="primary" size="small" icon="ios-cloud-download">
              <a style="color: white" target="_blank" :href="downloadUrl + row.fileId">{{ $t("file.download") }}</a>
            </Button>
            <Button v-if="isAuth('device:uploadSystemDisplayFile')" type="error" size="small" @click="toDelete(row.fileId)">
              <Icon type="md-close" />{{ $t("common.delete") }}</Button>
            <Button v-else disabled size="small">{{ $t("file.WithoutPermission") }}</Button>
          </template>
        </Table>
        <Row>
          <Col span="24">
            <Page style="float: right" :total="page.totalCount" :current="page.currPage" :page-size="page.pageSize"
                  show-elevator show-sizer :page-size-opts="[5]" show-total
                  @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
          </Col>
        </Row>
      </div>
      <div slot="footer" style="text-align: left">
        <span>
          <Alert v-if="formItem.ids.length === 0" type="error" show-icon> {{ $t("tips.upgradeFeatureSelectCard") }} </Alert>
          <Alert v-else type="success" show-icon>{{ $t("tips.cardSelected") }}:</Alert>
        </span>
        <div style="overflow-y: auto; max-height: 42px">
          <Breadcrumb>
            <BreadcrumbItem v-for="item in formItem.ids" :key="item" style="color: #999; font-weight: normal">{{ item }}</BreadcrumbItem>
          </Breadcrumb>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>

import Loading from "@/utils/loading";
import deviceSystemDisplayFileAdd from "./device-system-display-file-add.vue";
import {isAuth} from "../../../../utils";
export default {
  data() {
    return {
      fileName: "",
      downloadUrl: this.$http.adornUrl("/sys/file/download/"),
      dataListLoading: false,
      page: {
        totalPage: 0, // 总页数
        currPage: 1, //  当前页
        pageSize: 5, // 页容量
        totalCount: 0, // 总条数
      },
      dataConlums: [
        {title: this.$t('cardDevice.number'), width: 70,fixed: 'left', align: 'center',slot: 'number',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.number'))
          }
        },
        {
          title: this.$t("file.name"),
          key: "fileName",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('file.name'))
          }
        },
        {
          title: this.$t("file.TheSize"),
          key: "fileSize",
          slot: "fileSize",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('file.TheSize'))
          }
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          align: "center",
          tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'),fixed: 'right', slot: 'operation', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      formItem: {
        ids: [],
      },
      pageLists: [],
      visible: false,
      uploadDisplayFileVisible:false,
      setLoading:false
    };
  },
  components: {
    deviceSystemDisplayFileAdd,
    Loading
  },
  methods: {
    isAuth,
    // 初始化
    init(ids) {
      this.visible = true;
      this.setLoading=false
      this.formItem.ids = ids;
      this.querySystemDisplayFile()
    },

    setSystemDisplay(fileId) {
      this.setLoading = true
      // 在线更新
      this.$http({
        url: this.$http.adornUrl("/card/set/systemDisplay"),
        method: "post",
        data: this.$http.adornData({
          fileId:fileId,
          deviceIds: this.formItem.ids,
        }),
      }).then(({ data }) => {
        if (data && data.code == 0) {
          if (data.data) {
            this.$Message.success(data.msg)
          }
        } else {
          this.$Message.error(data.msg)
        }
        this.setLoading = false

      });
    },

    //删除
    toDelete(id) {
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          // 删除
          this.$http({
            url: this.$http.adornUrl("/screen/media/delete"),
            method: "post",
            data: this.$http.adornData([id], false),
          }).then(({ data }) => {
            if(data && data.code===0){
              this.querySystemDisplayFile();
            }else {
              this.$Message.error(data.msg)
            }
          });
        },
      });
    },
    ModelSaveclose() {
      this.page = {
        totalPage: 0, // 总页数
        currPage: 1, //  当前页
        pageSize: 5, // 页容量
        totalCount: 0, // 总条数
      };
      this.pageLists = [];
    },

    //查询文件列表
    querySystemDisplayFile(){
      this.pageLists = []
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/card/query/pointCheckTable"),
        method: "post",
        params: this.$http.adornParams({
          fileName:this.fileName,
          type: 11,
          page: this.page.currPage,
          limit: this.page.pageSize,
        }),
      }).then(({ data }) => {
        this.page = data.page;
        this.pageLists = data.page.list;
        this.dataListLoading = false;
      });
    },

    uploadFile(){
      this.uploadDisplayFileVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadDisplayFile.init()
      });

    },
    // 每页数
    sizeChangeHandle(val) {
      this.page.pageSize = val;
      this.querySystemDisplayFile();
    },
    // 当前页
    currentChangeHandle(val) {
      this.page.currPage = val;
      this.querySystemDisplayFile();
    },
  },
  mounted() {

  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        this.ModelSaveclose();
        this.$emit("refreshDataList");
      }
    },
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
    formatDate: function (value) {//调用时间戳为日期显示
      let date = new Date(value)
      let y = date.getFullYear()  //获取年份
      let m = date.getMonth() + 1  //获取月份
      m = m < 10 ? "0" + m : m  //月份不满10天显示前加0
      let d = date.getDate()  //获取日期
      d = d < 10 ? "0" + d : d  //日期不满10天显示前加0
      let h = date.getHours(); //小时
      h = h < 10 ? "0" + h : h  //不满10显示前加0
      let mi = date.getMinutes(); //分
      mi = mi < 10 ? "0" + mi : mi  //不满10显示前加0
      let s = date.getSeconds(); //秒
      s = s < 10 ? "0" + s : s  //不满10显示前加0
      return y + "-" + m + "-" + d + " " + h + ":" + mi + ':' + s
    },
  },
  computed: {
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  },
};
</script>
<style scoped>
</style>
