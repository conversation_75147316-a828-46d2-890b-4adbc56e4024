<template>
  <div>
    <Modal :title="title"
      v-model="visible"
      width="600"
    >
      <p slot="header" style="text-align:center">
        <span>{{ $t("file.upload") }}ZIP</span>
      </p>
      <div class="schedules2">
        <Form
          :model="uploadForm"
          style="max-height: 100px; height: 50px"
          :label-width="80"
          label-position="left"
        >
          <Alert type="warning" show-icon>{{$t('screen.supportZip')}}</Alert>
          <Loading :loadBoolValue="load"></Loading>
          <FormItem class="upload" :label="$t('screen.selectFile')">
            <Upload
              :before-upload="handleUpload"
              :action="''"
              accept="application/x-zip-compressed"
            >
              <!-- accept="video/mp4,video/3gp, audio/*, image/gif, image/jpeg, image/png, application/x-shockwave-flash" -->
              <Button type="primary" icon="ios-cloud-upload-outline"
                >{{$t('screen.selectFile')}}</Button
              >
            </Upload>
          </FormItem>
          <div>
            <Progress
              style="margin-bottom: 20px"
              v-if="upload.fileProgressShow"
              :percent="upload.fileProgress"
              :stroke-width="20"
              status="active"
              text-inside
            />
            <FormItem :label="$t('file.name')">
              <Input
                v-model="uploadForm.name"
                :placeholder="$t('file.name')"
              ></Input>
            </FormItem>
            <FormItem :label="$t('file.size')">
              <Input
                disabled
                v-model="uploadForm.size"
                :placeholder="$t('file.size')"
              ></Input>
            </FormItem>
            <FormItem :label="$t('file.type')">
              <Input
                disabled
                v-model="uploadForm.type"
                :placeholder="$t('file.type')"
              ></Input>
            </FormItem>
<!--            <FormItem :label="$t('screen.releaseTime')">
              &lt;!&ndash; <DatePicker type="date" v-model="saleDate" :editable="false"  :value="saleDate" placeholder="Select date" style="width: 200px"></DatePicker> &ndash;&gt;
              <Input
                v-model="saleDate"
                disabled
                placeholder=""
              ></Input>
            </FormItem>
            <FormItem  :label="$t('screen.versionInformation')">
              <Input
              v-if="version == '0' || version == '2'"
                maxlength="300"
                show-word-limit
                v-model="uploadForm.memo"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 10 }"
                placeholder="请按照
                1.
                2.
                3. 的格式输入"
              />
              &lt;!&ndash; <Input
              v-else-if="version == '2'"
                disabled
                v-model="uploadForm.memo"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 10 }"
                :placeholder="$t('screen.testVersion')"
              /> &ndash;&gt;
              <Input
              v-else
                disabled
                v-model="uploadForm.memo"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 10 }"
                :placeholder="$t('screen.hardwareVersion')"
              />
            </FormItem>-->
          </div>
        </Form>
      </div>
      <div slot="footer"><Button type="primary" @click="dataFormSubmit()" :loading="load"
                > {{$t('common.submit')}}</Button
              >
              <Button  @click="closeAll()"
                >{{$t('operation.remove')}}</Button
              ></div>
    </Modal>
  </div>
</template>

<script>
import axios from 'axios'
import { filterType } from '@/utils'
import Loading from '@/utils/loading'
export default {
  data () {
    return {
      load: false,
      title: '',
      version: '',
      saleDate: '',
      visible: false,
      updateForm: {
        id: 0,
        name: ''
      },
      uploadForm: {
        memo: '',
        name: '',
        type: '',
        resourceType: 0,
        size: '',
        dispalyFile: [] // 临时数组，同时用于显示在页面
      },
      // 上传配置
      upload: {
        fileProgressShow: false, // 进度条
        fileProgress: 0 // 进度条进度
      }
    }
  },
  components: {
    Loading
  },
  methods: {
    // 初始化
    init (version) {
      this.version = version
      if (this.version === '0') {
        this.title = this.$t('screen.officialRelease')
      }
      if (this.version === '2') {
        this.title = this.$t('screen.testRelease')
      }
      if (this.version === '3') {
        this.title = this.$t('screen.hardwareRelease')
      }
      this.visible = true
      const myDate = new Date()
      this.saleDate =
        myDate.getFullYear() +
        '/' +
        (myDate.getMonth() + 1) +
        '/' +
        myDate.getDate()
    },
    closeAll () {
      this.uploadForm = {
        memo: '',
        name: '',
        type: '',
        resourceType: 0,
        size: '',
        dispalyFile: [] // 临时数组，同时用于显示在页面
      }
    },
    OpenClose () {
      this.updateForm = { id: 0, name: '' }
      this.uploadForm = {
        memo: '',
        name: '',
        type: '',
        resourceType: 0,
        size: '',
        dispalyFile: []
      }
      this.upload = { fileProgressShow: false, fileProgress: 0 }
    },
    // 文件上传前
    handleUpload (selectFile) {
      var type =  selectFile.type
      // console.log(type)
      // var ele = type.substring(type.lastIndexOf('/') + 1, type.length)
      if (selectFile.size > (100 * 1024 * 1024)) {
        this.$Message.error(selectFile.name + this.$t('screen.sizeMore'))
        this.selectFile = null //超过大小将文件清空
        return false
      }
      // else if (ele !== "x-zip-compressed") {
      //   this.$Message.error(selectFile.name + this.$t('screen.OnlyForZIPType'))
      //   this.selectFile = null //超过大小将文件清空
      //   return false
      // }
      // 临时数组，同时用于显示在页面
      this.uploadForm.dispalyFile = selectFile
      this.uploadForm.name = this.uploadForm.dispalyFile.name
        ? this.uploadForm.dispalyFile.name.substring(
          0,
          this.uploadForm.dispalyFile.name.lastIndexOf('.')
        )
        : null
      this.uploadForm.size = filterType(this.uploadForm.dispalyFile.size)
      var type = this.uploadForm.dispalyFile.name
        ? this.uploadForm.dispalyFile.name.substring(
          this.uploadForm.dispalyFile.name.lastIndexOf('.')
        )
        : null
      this.uploadForm.type = type == null ? null : type.substring(1)
      return false
    },
    // 新增文件上传
    uploadFormSubmit () {
      this.load = true
      if (this.uploadForm.name === '' || this.version === '') {
        this.$Message.error(this.$t('screen.uploadEmpty'))
        this.load = false
        return
      }
      let formData = new FormData()
      // file为后台接收参数
      formData.append('file', this.uploadForm.dispalyFile)
      formData.append('fileName', this.uploadForm.name)
      formData.append('type', this.version)
      formData.append('memo', this.uploadForm.memo)
      axios
        .request({
          url: this.$http.adornUrl('/sys/file/upload'),
          method: 'post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
            token: this.$cookie.get('token')
          },
          onUploadProgress: (progressEvent) => {
            // 用于上传过程中显示进度条
            if (progressEvent.lengthComputable) {
              // 显示进度条
              this.upload.fileProgressShow = true
              // 计算当前进度
              let curValue = (
                (progressEvent.loaded / progressEvent.total) *
                100
              ).toFixed(0)
              // 赋值给进度条组件
              this.upload.fileProgress = parseInt(curValue)
            }
          }
        })
        .then((res) => {
          // 上传成功处理
          if (res.data) {
            if (res.data.code === 0) {
              this.load = false
              this.$Message.success(this.$t('common.operationSuccessful'))
              this.visible = false
            } else {
              this.load = false
              this.$Message.error(res.data.msg)
              this.visible = false
            }
          }
          // 隐藏进度条
          this.upload.fileProgressShow = false
        })
        .catch(() => {
          // 上传失败处理
          // 隐藏进度条
          this.upload.fileProgressShow = false
        })
    },
    // 表单提交
    dataFormSubmit (flag) {
      this.uploadFormSubmit()
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal === false) {
        if (this.version === '0') {
          this.$emit('queryType', 'tab1')
        }
        if (this.version === '2') {
          this.$emit('queryType', 'tab2')
        }
        if (this.version === 3) {
          this.$emit('queryType',"tab3")
        }
        this.version = ''
        this.OpenClose()
      }
    }
  },
  filters: {
    // 文件大小转换
    filterType (val) {
      if (val === 0) {
        return '0 B'
      }
      var k = 1024
      var sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      var i = Math.floor(Math.log(val) / Math.log(k))
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        ' ' +
        sizes[i]
      )
    }
  }
}
</script>
<style scoped>
.schedules {
  height: 600px;
}
.schedules2 {
  height: 450px;
}
</style>
