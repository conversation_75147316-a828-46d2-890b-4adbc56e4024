<template>
    <div>
        <Modal v-model="visible" width="1000">
            <p slot="header" style="text-align:center">
                <span>{{$t('common.release')}}</span>
            </p>
            <Alert type="warning" show-icon>
                <b class="tip" style="margin-bottom:5px"> {{$t('common.note')}}： {{ $t('tips.releaseTips') }}</b><br/>
                <b class="tip" style="margin-bottom:5px">{{ $t('tips.releaseTips1') }}</b>
                <br/>
            </Alert>
            <Form :inline="true" :model="dataForm">
                <FormItem style="margin-bottom: 5px;">
                    <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
                </FormItem>
                <FormItem style="margin-bottom: 5px;">
                    <Select size="large" v-model="dataForm.resolvingPower" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('cardDevice.resolvingPower')">
                        <Option v-for="(item, index) in resolvingPowerList" :value="item.width + ',' + item.height" :key="index">{{item.width}} * {{ item.height }}</Option>
                    </Select>
                </FormItem>
                <FormItem style="margin-bottom: 5px;">
                    <Button style="margin-right:6px" @click="getDataList()"  size="large">
                        <div style="margin:3px 8px">{{$t('common.query')}}</div>
                    </Button>
                </FormItem>
                <FormItem style="margin-bottom: 5px;">
                    <Button size="large" @click="changeGroup()">{{groupName}}</Button>
                </FormItem>
            </Form>
            <Form :inline="true">
                <FormItem style="margin-bottom: 5px;">
                    <span>{{$t('program.enablePlayerLog')}}</span>
                </FormItem>
                <FormItem style="margin-bottom: 5px;">
                    <i-Switch v-model="playLog.isPlayLog" @on-change="checkPlayLogChange" size="large">
                        <template #open>
                            <span>{{ $t('sys.open') }}</span>
                        </template>
                        <template #close>
                            <span>{{ $t('sys.close') }}</span>
                        </template>
                    </i-Switch>
                </FormItem>
            </Form>
            <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle" @on-row-click="selectThisRow"
                :loading="dataListLoading" style="width: 100%" :height="300" ref="selection">
                <template slot-scope="{ row, index }" slot="progress">
                    <div v-if="row.progress && row.progress !== 0" style="height: 20px"><Progress :stroke-width="15" :percent="row.progress" :stroke-color="['#108ee9', '#87d068']" status="active"/></div>
                    <div v-else-if="row.msg">
                        <div v-if="row.isError === true">
                            <Poptip trigger="hover" :title="$t('screen.errorDetails')" width="300" placement="bottom-start" transfer>
                                <div slot="content" class="poptipExplain">{{row.msg}}</div>
                                <Button size="small" type="error">{{$t('screen.errorDetails')}}</Button>
                            </Poptip>
                        </div>
                        <div v-else>{{row.msg}}</div>
                    </div>
                    <div v-else>{{$t('screen.null')}}</div>
                </template>
                <template slot-scope="{ row, index }" slot="online">
                    <div>
                        <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                        <use :xlink:href="row.isOn === 1 ? '#on-line' : '#line'"></use>
                        </svg>
                    </div>
                </template>
                <template slot-scope="{ row, index }" slot="resolvingPower">
                    <div v-if="row.width && row.height">{{row.width}} * {{row.height}}</div>
                    <div v-else></div>
                </template>
                <template slot-scope="{ row, index }" slot="currentProgramName">
                    <div v-if="row.currentProgramName && row.currentProgramName=='暂无节目'">
                    {{ $t('broadcast.noProgram')  }}
                    </div>
                    <div v-else>
                    {{row.currentProgramName}}
                    </div>
                </template>
            </Table>
            <Page style="float:right;margin-top:20px;margin-bottom:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
            @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
            <div slot="footer">
                <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
                <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()" :disabled="dataListSelections.length <= 0">{{$t('common.confirm')}}</Button>
            </div>
          <!--分组弹出框-->
          <Modal v-model="selectGroupVisible" width="500">
            <p slot="header" style="text-align:center">
              <span>{{$t('common.selectGroup')}}</span>
            </p>
            <Alert type="info" show-icon >
              <span>{{this.$t('tips.groupTip')}}</span>
            </Alert>
            <div>
              <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
            </div>
            <div slot="footer">
              <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
              <Button type="primary" size="large" @click="groupFormSubmit()">{{$t('common.confirm')}}</Button>
            </div>
          </Modal>


          <Modal v-model="isPlayLogModal" width="500">
            <p slot="header" style="text-align:center">
              <span>{{ $t('program.playLog') }}</span>
            </p>
            <Alert type="warning" show-icon>
                <b class="tip" style="margin-bottom:5px">{{$t('common.note')}}：{{ $t('tips.releaseTips2') }}</b><br/>
            </Alert>
            <Form>
                <FormItem :label="$t('program.timeInterval')">
                    <InputNumber :max="9999" :min="1" v-model="playLog.interval" controls-outside/>
                </FormItem>
            </Form>
            <div slot="footer">
              <Button size="large" @click="cancelPlayLog()">{{$t('common.cancel')}}</Button>
              <Button type="primary" size="large" @click="playLogSubmit()">{{$t('common.confirm')}}</Button>
            </div>
          </Modal>
        </Modal>
    </div>
</template>

<script>
export default {
    data () {
        return {
            visible: false,
            loading: false,
            dataForm: {
                key: '',
                group: [],
                resolvingPower: ''
            },
            dataConlums: [
                {type: 'selection', width: 60, align: 'center'},
                {title: this.$t('cardDevice.deviceName'), key: 'alias', align: 'center',
                    renderHeader:(h)=>{
                        return h('div',this.$t('cardDevice.deviceName'))
                    }
                },
                {title: 'ID', key: 'deviceId', width: 140, align: 'center'},
                {title: this.$t('cardDevice.online'), key: 'isOn', width: 70, slot: 'online', align: 'center',
                    renderHeader:(h)=>{
                        return h('div',this.$t('cardDevice.online'))
                    }
                },
                {title: this.$t('cardDevice.resolvingPower'), slot: 'resolvingPower', width: 120, align: 'center', tooltip: true,
                    renderHeader:(h)=>{
                        return h('div',this.$t('cardDevice.resolvingPower'))
                    }
                },
                // {title: this.$t('cardDevice.networkType'), key: 'netType', align: 'center'},
                // {title: this.$t('cardDevice.programTask'), key: 'currentProgramName', align: 'center', tooltip: true},
                // {title: this.$t('cardDevice.screenStatus'), key: 'screenStatus', align: 'center',
                //     renderHeader:(h)=>{
                //         return h('div',this.$t('cardDevice.screenStatus'))
                //     }
                // },
                {title: this.$t('cardDevice.programTask'), key: 'currentProgramName', width: 130, align: 'center', tooltip: true,slot: 'currentProgramName' },
                {title: this.$t('screen.showProgress'), key: 'progress', align: 'center',tooltip: true,slot: 'progress',width: 280,
                    renderHeader:(h)=>{
                        return h('div',this.$t('screen.showProgress'))
                    }
                }
            ],
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataListSelections: [],
            itemsId: '',
            programId: '',
            timerList: [],
            groupList: [],
            resolvingPowerList: [],
            //选择分组时，分组框是否可见
            selectGroupVisible:false,
            //  分组名
            groupName:this.$t('common.selectGroup'),
          rootNode:null,
          //是否第一次打开该页面
          isFirst:true,
          //总数量
          totalNum:0,
          successTemp: [],
          playLog: {
            isPlayLog: false,
            interval: 60,
          },
          isPlayLogModal: false,
        }
    },
    methods: {
        // 初始化
        init (itemsId, programId) {
            this.visible = true
            this.dataForm= {
              key: '',
              group: [],
              resolvingPower: ''
            }
            this.getDataList()
            this.getGroupList()
            this.getSelectResolvingPower()
            this.itemsId = itemsId
            this.programId = programId
            this.groupName=this.$t('common.selectGroup')
            this.rootNode=null
            this.isFirst=true
            //总数量
            this.totalNum=0
            this.playLog = {
                isPlayLog: false,
                interval: 60,
            }
            this.isPlayLogModal = false
        },
        // 查询分组列表
        getGroupList () {
            this.$http({
                url: this.$http.adornUrl('/sys/group/select'),
                method: 'get',
                params: this.$http.adornParams({})
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.groupList = data.group
                    this.getUnGroupDevice()
                } else {
                    this.groupList = []
                }
            })
        },
        // 获取分辨率
        getSelectResolvingPower () {
            this.$http({
                url: this.$http.adornUrl('/screen/card/selectResolvingPower'),
                method: 'get',
                params: this.$http.adornParams({})
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.resolvingPowerList = data.resolvingPower
                } else {
                    this.resolvingPowerList = []
                }
            })
        },
        getDataList () {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/screen/card/releaseList'),
                method: 'post',
                data: this.$http.adornData({
                    'page': this.pageIndex+"",
                    'limit': this.pageSize+"",
                    'key': this.dataForm.key,
                    'group':this.dataForm.group,
                    'resolvingPower': this.dataForm.resolvingPower
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.page.list
                    this.totalPage = data.page.totalCount
                  if (this.isFirst){
                    this.totalNum=data.page.totalCount
                    this.isFirst=false
                  }
                  if (this.dataForm.group.length===0){
                    this.groupName=this.$t('common.selectGroup')
                  }
                  // this.dataForm.group=[]
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                    this.dataListSelections = []
                    this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle (val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle (val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle () {
            this.dataListSelections = this.$refs.selection.getSelection()
        },
        selectThisRow(data, index) {
            if (data._disabled === undefined || data._disabled === false) {
                this.$refs.selection.toggleSelect(index);
            }
        },
        // 是否开启播放器日志
        checkPlayLogChange(data) {
            if (data == true) {
                this.isPlayLogModal = true
            }
        },
        // 取消开启播放器日志
        cancelPlayLog() {
            this.playLog.isPlayLog = false
            this.isPlayLogModal = false
        },
        // 确定开启播放器日志
        playLogSubmit() {
            this.isPlayLogModal = false
        },
        // 发布
        dataFormSubmit () {
            if (this.dataListSelections.length > 0) {
                this.loading = true
                var deviceIds =  this.dataListSelections.map(item => {
                    return item.deviceId
                })
                this.$http({
                    url: this.$http.adornUrl('/screen/program/release'),
                    method: 'post',
                    data: this.$http.adornParams({
                        'deviceIds': deviceIds,
                        'programId': this.programId,
                        'itemsId': this.itemsIds,
                        'playLog': this.playLog
                    })
                }).then(({data}) => {
                    this.loading = false
                    this.dataListSelections = []
                    var resultData = []
                    if (data && data.code == 0) {
                        if (data.data) {
                            resultData = data.data;
                        }
                    } else {
                        this.$Message.error(data.msg)
                    }
                    if (resultData.length > 0) {
                        resultData.forEach(item => {
                            if (item._type !== "success") {
                                this.dataList = this.dataList.filter(i => {
                                    if (i.deviceId === item.deviceId) {
                                        i.msg = item.msg
                                        i.isError  = true
                                        i.progress = 0
                                    }
                                    return i
                                })
                            } else {
                                this.dataList = this.dataList.filter(i => {
                                    if (i.deviceId === item.deviceId) {
                                        i.msg = item.msg && item.msg == "offLine" ? this.$t("screen.sendSuccessOffline") : this.$t('screen.sendSuccess')
                                        i.isError = false
                                        i.progress = 0
                                    }
                                    return i
                                })
                                if (item.id) {
                                    var temp = {deviceId: item.deviceId, commandId: item.id, timer: null, progress: 0, status: 0, reason: '', totalTime: 0}
                                    this.successTemp.push(temp)
                                }
                            }
                        })
                        if (this.successTemp.length > 0) {
                            var _this = this
                            for (var item in _this.successTemp) {
                                (function (item) {
                                    var date = new Date()
                                    var min = date.getMinutes()
                                    date.setMinutes(min + 5)
                                    _this.successTemp[item].totalTime = date.getTime(); // 设置总时长为五分钟后
                                    _this.successTemp[item].timer = setInterval(function () {
                                        // 当前时间如果与前面设置的时间相等视为超时
                                        if (_this.successTemp[item].totalTime <= new Date().getTime()) {
                                             clearInterval(_this.successTemp[item].timer)
                                            _this.successTemp[item].timer = null
                                            _this.successTemp[item].progress = 0
                                            _this.dataList = _this.dataList.filter(data => {
                                                if (data.deviceId === _this.successTemp[item].deviceId) {
                                                    data.msg = _this.$t('screen.timesOut')
                                                    data.progress = _this.successTemp[item].progress
                                                    data._disabled = false
                                                }
                                                return data
                                            })
                                        } else {
                                             _this.$http({
                                                url: _this.$http.adornUrl(`/program/playLog/info/${_this.successTemp[item].commandId}`),
                                                method: 'get',
                                                params: _this.$http.adornParams()
                                            }).then(({data}) => {
                                                if (data && data.code === 0) {
                                                    _this.successTemp[item].progress = data.programPlayLog.progress === 0 ? 5 : data.programPlayLog.progress
                                                    _this.successTemp[item].commandId = data.programPlayLog.LogId
                                                    _this.successTemp[item].deviceId = data.programPlayLog.deviceId
                                                    _this.successTemp[item].status = data.programPlayLog.status
                                                    _this.successTemp[item].reason = data.programPlayLog.reason
                                                    if (_this.successTemp[item].status === 0) {
                                                        _this.dataList = _this.dataList.filter(data => {
                                                            if (data.deviceId === _this.successTemp[item].deviceId) {
                                                                data.progress = _this.successTemp[item].progress
                                                                data._disabled = true
                                                            }
                                                            return data
                                                        })
                                                        if (_this.successTemp[item].progress === 100) {
                                                            setTimeout(() => {
                                                                clearInterval(_this.successTemp[item].timer)
                                                                _this.successTemp[item].timer = null
                                                                _this.successTemp[item].progress = 0
                                                                _this.dataList = _this.dataList.filter(data => {
                                                                    if (data.deviceId === _this.successTemp[item].deviceId) {
                                                                        data.progress = _this.successTemp[item].progress
                                                                        data._disabled = true
                                                                    }
                                                                    return data
                                                                })
                                                            }, 500)
                                                        }
                                                    } else {
                                                        _this.successTemp[item].timer = null
                                                        // _this.successTemp[item].progress = 0
                                                        // _this.successTemp[item].reason = ''
                                                        // _this.successTemp[item].status = 0
                                                        _this.dataList = _this.dataList.filter(data => {
                                                        if (data.deviceId === _this.successTemp[item].deviceId) {
                                                                data.msg = _this.successTemp[item].reason
                                                                data.progress = 0
                                                                data.isError = true
                                                                data.status=_this.successTemp[item].status

                                                            }
                                                            return data
                                                        })
                                                    }
                                                } else {
                                                    _this.$Message.error(_this.$t('screen.failedProgress'))
                                                    _this.successTemp[item].timer = null
                                                    _this.successTemp[item].progress = 0
                                                }
                                            })
                                        }
                                        if (_this.dataListSelections && _this.dataListSelections.length !== 0) {
                                            var select = _this.dataListSelections.map(item => {return item.deviceId})
                                            if (select && select.length !== 0) {
                                                _this.dataList.map(item => {
                                                if (select.indexOf(item.deviceId) != -1) {
                                                    item._checked = true
                                                } else {
                                                    item._checked = false
                                                }
                                                })
                                            }
                                        }
                                    }, 2000);
                                })(item);
                            }
                        }
                    }
                })
            } else {
                this.$Message.error(this.$t('screen.selectCard'))
            }
        },
        //获取未分组数量
        getUnGroupDevice(){
        var groupedNum=0;
        this.unGroupNum=0;
        this.groupList.map(item=>{
          groupedNum+=item.count;
        })
        this.unGroupNum=this.totalNum-groupedNum;
        var unGroupObj={
          "id":-1,
          "name": this.$t('common.unclassified'),
          "count":this.unGroupNum,
          "children":[],
          "expand":true
        }
        this.groupList.push(unGroupObj)
      },
        changeGroup(){
          this.getGroupList()
          this.selectGroupVisible=true
        },
        // 表单提交
        groupFormSubmit () {
          this.dataForm.group=[]
          this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
          this.getChildrenNodes(this.rootNode)
          this.selectGroupVisible=false
          this.getDataList()
          this.groupName=this.$t('common.selectingGroup')+":"+this.rootNode.name
          this.rootNode=null
        },
        cancelSelect(){
          this.selectGroupVisible=false
          this.dataForm.group=[]
          this.groupName=this.$t('common.selectGroup')
          this.rootNode=null
          this.getDataList()
          this.getGroupList()

        },
        //获取该分组及其子分组的groupId
        getChildrenNodes(rootNode){
          this.dataForm.group.push(rootNode.id)
          var childNode=rootNode.children;
          if (childNode){
            for (var i=0; i<childNode.length; i++) {
              this.getChildrenNodes(childNode[i])
            }
          }
        },
        renderContent (h, { root, node, data }) {
          return h('span', {
            style: {
              display: 'inline-block',
              width: '100%'
            }
          }, [
            h('span', [
              h('span', data.name+"("+data.count+")")
            ])
          ]);
        },
      },
    watch: {
        'visible': function (newVal, oldVal) {
            if (newVal === false) {
                /* if (this.timerList.length > 0) {
                    this.timerList.forEach(item => {
                        clearInterval(item)
                    })
                }
                this.timerList = [] */
                if(this.successTemp.length > 0) {
                    for (let i = 0; i < this.successTemp.length; i++) {
                        const element = this.successTemp[i];
                        if (element.timer) {
                            clearInterval(element.timer)
                        }
                    }
                }
                this.successTemp = []
            }
        }
    }
}
</script>
<style scoped>
.adv {
  position: fixed;
  right: 10px;
  bottom: 10px;
  border-radius: 2%;
  background-color: rgb(255, 255, 255);
  width: 250px;
  height: 150px;
  padding: 5px;
  overflow: hidden;
}
.poptipExplain{
    float: left;
    /* width: 150px; */
    height: 200px;
    white-space: normal;
    word-break: break-all;
    overflow-x:hidden;
    overflow-y: auto;
    line-height: 25px;
    font-size: 14px;
}
</style>
