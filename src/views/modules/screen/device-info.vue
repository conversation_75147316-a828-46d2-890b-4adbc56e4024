<!-- 智慧屏幕表格列表详情 -->
<template>
    <Modal v-model="visible" width="600">
        <p slot="header" style="text-align:center">
            <span>{{$t('common.info')}}</span>
        </p>
        <b class="info_title" style="font-size:15px;">{{$t('screen.simInformation')}}</b>
        <div v-if="sim">
          <div class="info_div">
            <span class="info_title">DeviceId：</span>
            {{sim.deviceId}}
            <br/>
            <span class="info_title">{{$t('screen.networkState')}}ISO：</span>
            {{sim.networkCountryIso}}
            <br/>
            <span class="info_title">Number：</span>
            {{sim.number}}
            <br/>
            <span class="info_title">SIM {{$t('screen.series')}}：</span>
            {{sim.simSerialNumber}}
            <br/>
            <span class="info_title">SIM {{$t('screen.countries')}}ISO：</span>
            {{sim.simCountryIso}}
            <br/>
            <span class="info_title">{{$t('screen.operationName')}}：</span>
            {{sim.simOperatorName}}
            <br/>
            <span class="info_title">SIM {{$t('common.state')}}：</span>
            <span v-if="sim.simState === 0">{{$t('screen.unknownState')}}</span>
            <span v-else-if="sim.simState === 1">{{$t('screen.noCard')}}</span>
            <span v-else-if="sim.simState === 2">{{$t('screen.PIN')}}</span>
            <span v-else-if="sim.simState === 3">{{$t('screen.PUK')}}</span>
            <span v-else-if="sim.simState === 4">{{$t('screen.PIN2')}}</span>
            <span v-else-if="sim.simState === 5">{{$t('screen.readyState')}}</span>
            <span v-else>{{$t('screen.InvalidState')}}</span>
            <br/>
            <span class="info_title">{{$t('screen.subscribe')}}：</span>
            {{sim.subscriberId}}
          </div>
        </div>
        <div v-else>
          {{$t('cardDevice.NoSim')}}
        </div>
        <div slot="footer">
          <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
          <Button type="primary" size="large" @click="visible = false">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      cardInfo: {},
      sim: ''
    }
  },
  methods: {
    // 初始化
    init (id) {
      this.visible = true
      if (id) {
        this.$http({
          url: this.$http.adornUrl(`/screen/card/info/${id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.cardInfo = data.card
            if (this.cardInfo) {
              if (this.cardInfo.sim) {
                this.sim = JSON.parse(this.cardInfo.sim)
              }
            }
            this.visible = true
          } else {
            this.$Message.error(data.msg)
          }
        })
      }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.cardInfo= {}
          this.sim = ''
        },200)
      }
    }
  }
}
</script>
<style scoped>
.info_div {
    margin-bottom: 10px;
    height: 200px;
    overflow-x: hidden;
    overflow-y: auto;
}
.info_title {
    font-size: 15px;
    font-weight: 600;
    display: inline-block;
    width: 200px;
    margin-top: 5px;
}
</style>
