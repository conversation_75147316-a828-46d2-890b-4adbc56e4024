<template>
  <div class="modiles-program">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="init(isTodo,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.programName" :placeholder="$t('task.name')"></Input>
      </FormItem>
      <FormItem v-if="isTodo === false" style="width: 270px">
        <Select size="large" v-model="dataForm.status" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('common.state')">
          <Option v-for="item in programStatus" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button @click="init(isTodo,1)" style="margin-right:6px" size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button v-if="isTodo === false && isAuth('screen:extendedProgram:cubeScreen:save')" style="margin-right:6px" size="large" type="primary" @click="addOrUpdateHandle()">
          <div style="margin:3px 8px">{{$t('common.newlyBuild')}}</div>
        </Button>
        <Button v-if="isTodo === false && isAuth('screen:extendedProgram:cubeScreen:delete')" style="margin-right:6px" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
        </Button>
        <Button type="warning" size="large" style="margin-right: 6px" v-if="isTodo === true" :disabled="dataListSelections.length <= 0" @click="auditFlowHandle()">{{$t('approval.batchReview')}}</Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList"  @on-selection-change="selectionChangeHandle"  @on-row-click="selectThisRow"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection">
      <template slot-scope="{ row, index }" slot="resolvingPower">
         <div v-if="row.width && row.height">{{row.width}} * {{row.height}}</div>
         <div v-else></div>
      </template>
      <template slot-scope="{ row, index }" slot="type">
         {{row.insert == 1 ? $t('program.insertProgram') : $t('program.ordinaryProgram')}}
      </template>
      <template slot-scope="{ row, index }" slot="status">
        <Tag color="orange" v-if="row.status === 0">{{$t('file.checkPending')}}</Tag>
        <Tag color="cyan" v-if="row.status === 1">{{$t('file.under_review')}}</Tag>
        <Tag color="blue" v-if="row.status === 2">{{$t('file.approved')}}</Tag>
        <Tag color="volcano" v-if="row.status === 3">{{$t('file.auditFailed')}}</Tag>
      </template>
      <template slot-scope="{ row, index }" slot="totalSize">
        <span>{{row.totalSize | filterType}}</span>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" v-if="userInfo.noExamine === 0 && isTodo === false" @click="auditFlowHandle(row.programId)">{{$t('screen.reviewDetails')}}</Button>
        <Button type="warning" size="small" style="margin-right: 5px;font-size: 11px" v-if="isTodo === true" @click="auditFlowHandle(row.programId)">{{$t('file.examine')}}</Button>
        <Button type="info" size="small" style="margin-right: 5px;font-size: 11px"  v-if="isTodo === false" @click="releaseHandle(row.itemsId,row.programId)" :disabled="row.status !== 2">{{$t('common.release')}}</Button>
        <Button v-if="isAuth('screen:extendedProgram:cubeScreen:update') && isTodo === false" :disabled="row.status === 1" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="addOrUpdateHandle(row.programId)">{{$t('common.update')}}</Button>
        <Button v-if="isAuth('screen:extendedProgram:cubeScreen:info') && isTodo === false" type="primary" size="small" style="font-size: 11px" @click="exportJSON(row.programId)">{{$t('file.download')}}</Button>
        <Button v-if="isAuth('screen:extendedProgram:cubeScreen:delete') && isTodo === false" type="error" size="small" style="font-size: 11px" @click="deleteHandle(row.programId)">{{$t('common.delete')}}</Button>
      </template>
    </Table>
    <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
    show-elevator show-sizer :page-size-opts="[10,20,50,100]" show-total
    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
    <!-- 审批 -->
    <audit-flow v-if="auditFlowVisible" ref="auditFlow"  @refreshDataList="init(isTodo)"></audit-flow>

    <!-- 发布 -->
    <program-release v-if="releaseVisible" ref="programRelease"></program-release>

    <!-- 添加节目时输入宽度高度名称信息 -->
    <Modal v-model="addVisible" width="650">
        <p slot="header" style="text-align:center">
            <span>{{$t('common.newlyBuild')}}</span>
        </p>
        <Form ref="addForm" :model="addForm" :rules="addRules" style="height:150px;overflow-x:hidden;overflow-y:auto;" :label-width="90" label-position="left"
        @keyup.enter.native="addFormSubmit()">
            <FormItem prop="canvasName" :label="$t('task.name')">
                <Input size="large" v-model="addForm.canvasName"
                :placeholder="$t('common.PleaseInput') + $t('task.name')"/>
            </FormItem>
            <FormItem prop="canvasWidth" :label="'单面像素'">
                <InputNumber size="large" :max="70000" :min="1" v-model="addForm.canvasWidth" style="width: 100%"
                :placeholder="$t('common.PleaseInput') + $t('cardDevice.width')"
                ></InputNumber>
            </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="addVisible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" @click="addFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
import auditFlow from './cubeScreen-audit-flow.vue'
import cubeScreenRelease from './cubeScreen-release.vue'
import program from '../../program/programModular/program.js'
export default {
  components: {
    auditFlow,
    cubeScreenRelease
  },
  activated () {
    this.initData()
  },
  data () {
    return {
      programData:{},
      dataForm: {
        programName: '',
        status: '',
      },
      addForm: {
          canvasName: ""+new Date().getFullYear()+(new Date().getMonth()+1)+new Date().getDate()+new Date().getHours()+new Date().getMinutes()+new Date().getSeconds(),
          canvasWidth: 128,
      },
      programStatus: [
        {value: '0', label: 'file.checkPending'},
        {value: '1', label: 'file.under_review'},
        {value: '2', label: 'file.approved'},
        {value: '3', label: 'file.auditFailed'}
      ],
      dataList: [],
      dataConlums: [
        {type: 'selection',width: 60, align: 'center'},
        {title: this.$t('task.name'), key: 'name', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('task.name'))
          }
        },
        {title: this.$t('cardDevice.resolvingPower'), slot: 'resolvingPower', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.resolvingPower'))
          }
        },
        {title: this.$t('menu.type'), slot: 'type', align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('menu.type'))
          }
        },
        {title: this.$t('common.state'), key: 'status', slot: 'status', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.state'))
          }
        },
        {title: this.$t('program.totalSize'), key: 'totalSize', slot: 'totalSize', align: 'center', width: 120, tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('program.totalSize'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('common.createTime'))
          }
        },
        {title: this.$t('common.operation'), slot: 'operation', align: 'center', width: 300,
          renderHeader:(h)=>{
            return h('div',this.$t('common.operation'))
          }
        },
      ],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      visible: false,
      auditFlowVisible: false,
      releaseVisible: false,
      isTodo: false,
      addVisible: false,
      programTypes: [
        {value: 0, name: 'program.ordinaryProgram'},
        {value: 1, name: 'program.insertProgram'}
      ],
    }
  },
  methods: {
    // 初始化数据
    initData () {
      this.dataForm = {
        programName: '',
        status: ''
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
    },
    // 获取数据列表
    init (isTodo,isQuery) {
      //点击查询按钮
      if (isQuery){
        this.pageIndex=1
      }
      this.isTodo = isTodo
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/program/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'programName': this.dataForm.programName,
          'status': this.dataForm.status,
          'todo': isTodo === true ? 1 : ''
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.init(isTodo,isQuery)
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.init(this.isTodo)
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.init(this.isTodo)
    },
    // 多选
    selectionChangeHandle () {
      this.dataListSelections = this.$refs.selection.getSelection()
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 删除
    deleteHandle (id) {
      var programIds = id ? [id] : this.dataListSelections.map(item => {
        return item.programId
      })
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/screen/program/delete'),
            method: 'post',
            data: this.$http.adornData(programIds, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === programIds.length) {
                    this.pageIndex--
                  }
                  this.init(this.isTodo)
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      if (id) {
        // 修改
        this.$http({
            url: this.$http.adornUrl(`/screen/program/info/${id}`),
            method: 'get',
            data: this.$http.adornData()
        }).then(({data}) => {
            if (data && data.code === 0) {
              // console.log(data.program)
              this.$router.replace({ name: 'extend-cubeScreen', params: {backRouter: 'screen-program'} })
              program.modifyAdvertisement(data.program, false)
            } else {
              this.$Message.error(data.msg)
            }
        })
      } else { // 新增
        this.addVisible = true
      }
    },
    addFormSubmit () {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          this.$router.replace({ name: 'extend-cubeScreen', params: {'addFrom': this.addForm, backRouter: 'screen-extendedProgram/cubeScreen'}})
        }
      })
    },
    // 查询审核流
    auditFlowHandle (id) {
      var programIds = id ? [id] : this.dataListSelections.map(item => {
        return item.programId
      })
      this.auditFlowVisible = true
      this.$nextTick(() => {
        this.$refs.auditFlow.init(programIds)
      })
    },
    // 发布任务
    releaseHandle (itemsId, programId) {
      // 如果没有认证的用户不能下发节目
      /* var isAuth = this.$cookie.get("isAuth")
      if (null != isAuth && undefined !== isAuth) {
        this.$router.push({ name: 'updateUserInfo'})
        return
      } */
      this.releaseVisible = true
      this.$nextTick(() => {
        this.$refs.programRelease.init(itemsId, programId)
      })
    },
    exportJSON(programId) {
      window.open(this.$http.adornUrl(`/screen/program/exportJSON?token=${this.$cookie.get('token')}&programId=${programId}`))
    }
  },
  computed: {
    tableHeight: {
      get () { return this.$store.state.common.tableHeight }
    },
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    },
    addRules: function() {
      return {
        canvasName: [
            { required: true, message: this.$t('program.name') + this.$t('validate.not_empty'), trigger: 'blur'}
        ],
        canvasWidth: [
            { required: true, type: 'number', message:  this.$t('cardDevice.width') + this.$t('validate.not_empty'), trigger: 'blur' }
        ]
      }
    },
  },
  filters: {
    // 文件大小转换
    filterType (val) {
      if (val === 0) {
        return '0 B'
      }
      var k = 1024
      var sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      var i = Math.floor(Math.log(val) / Math.log(k))
      return (parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) + ' ' + sizes[i])
    }
  },
  watch: {
    'addVisible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.addForm= {
            canvasName: ""+new Date().getFullYear()+(new Date().getMonth()+1)+new Date().getDate()+new Date().getHours()+new Date().getMinutes()+new Date().getSeconds(),
            canvasWidth: 128,
          }
        }, 200)
      }
    },
    program:{ //监听的对象  this.programData指向program
      handler(){
        this.programData=program
      },
      immediate:true,
    }
  }
}
</script>

<style>

</style>
