<template>
  <div class="modiles-program">
    <Tabs type="card" value="my" v-if="userInfo.isExamineUser" @on-click="tabsClick">
        <TabPane :label="$t('task.task')" name="my">
          <cubeScreen-query-and-table ref="programQueryAndTable"></cubeScreen-query-and-table>
        </TabPane>
        <TabPane :label="$t('screen.pending')" name="todo">
          <cubeScreen-query-and-table ref="programQueryAndTableTodo"></cubeScreen-query-and-table>
        </TabPane>
    </Tabs>
    <div v-else>
      <cubeScreen-query-and-table ref="programQueryAndTable"></cubeScreen-query-and-table>
    </div>
  </div>
</template>

<script>
import cubeScreenQueryAndTable from './cubeScreen-query-and-table.vue'
export default {
  components: {
    cubeScreenQueryAndTable,
  },
  data () {
    return {
      isTodo: false
    }
  },
  activated () {
    this.getDataList()
  },
  methods: {
    tabsClick (name) {
      if (name === 'todo') {
        this.isTodo = true
        this.getDataListTodo()
      } else {
        this.isTodo = false
        this.getDataList()
      }
    },
    // 获取数据列表
    getDataList () {
      this.$nextTick(() => {
        this.$refs.programQueryAndTable.init(this.isTodo)
      })
    },
    getDataListTodo () {
      this.$nextTick(() => {
        this.$refs.programQueryAndTableTodo.init(this.isTodo)
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>

<style>

</style>
/** 废弃
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (228, 234, '魔方屏', 'screen/extendedProgram/cubeScreen', NULL, 1, NULL, 1);
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (229, 228, '新建', '', 'screen:extendedProgram:cubeScreen:save', 3, '', 0);
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (230, 288, '修改', NULL, 'screen:extendedProgram:cubeScreen:update', 3, NULL, 0);
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (231, 288, '查询', NULL, 'screen:extendedProgram:cubeScreen:list,screen:extendedProgram:cubeScreen:info', 3, NULL, 0);
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (232, 288, '删除', NULL, 'screen:extendedProgram:cubeScreen:delete', 3, NULL, 0);
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (233, 288, '发布', NULL, 'screen:extendedProgram:cubeScreen:release', 3, NULL, 0);
INSERT INTO `aips4`.`sys_menu`(`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES (234, 1, '扩展节目', 'screen/extendedProgram', NULL, 2, NULL, 3);
 */