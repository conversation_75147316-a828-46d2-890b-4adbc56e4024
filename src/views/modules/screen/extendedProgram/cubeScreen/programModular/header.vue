<template>
    <div>
        <Row>
            <Col span="6" style="text-align:center;">
                <div>{{ programData.canvasName }}</div>
            </Col>
            <Col span="17" style="text-align:right;">
                <el-button type="warning" size="medium" plain @click="setNameOrWidth = true">{{$t('common.set')}}</el-button>
                <el-button type="primary" class="preservation" plain :disabled="programData.isView" size="medium" :loading="programData.loadingSave" 
                    @click="preservation(false)">{{$t('common.save')}}</el-button>
                <el-button type="success" :disabled="programData.isView" plain class="preservation"
                    size="medium" :loading="programData.loadingSaveAndExit"  @click="preservation(true)">{{$t('common.saveAndExit')}}</el-button>
                <el-button type="info" size="medium" plain @click="back()">退出</el-button>
            </Col>
            <Col span="1"></Col>
        </Row>
        <el-dialog :title="$t('program.ProgramInfo')" :visible.sync="setNameOrWidth" width="650px">
            <el-form label-width="90px">
                <el-form-item :label="$t('task.name')" style="text-align: left;">
                    <el-input v-model="canvasName" :placeholder="$t('program.PleaseEnterContent')"></el-input>
                </el-form-item>
                <el-form-item :label="'单面像素'" style="text-align: left;">
                    <el-input-number :max="70000" :min="1"  size="mini" v-model="canvasWidth" ></el-input-number>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" size="small" @click="setNameOrWidth = false">{{ $t('common.cancel') }}</el-button>
                <el-button type="primary" size="small"
                :disabled="!(canvasWidth && canvasName)"
                @click="setNameOrWidthClick">{{ $t('common.confirm') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import program from "./program.js"
export default {
    data() {
        return {
            setNameOrWidth: false,
            canvasWidth: Number,
            canvasName: "",
            backRouter: "",
            programData: {},
        }
    },
    methods: {
         //点击确定更改宽和name
        setNameOrWidthClick() {
            this.setNameOrWidth = false
            program.canvasWidth = this.canvasWidth
            program.programTask.name = this.canvasName
        },
        // 保存
        preservation(isExit){
            program.preservation(isExit)
        },
        // 退出
        back() {
            var subMenuActiveName = this.backRouter === 'screen-extendedProgram/cubeScreen' ? '魔方屏' : '设备管理'
            if (this.backRouter) {
                this.$router.push({ name: this.backRouter, params: { subMenuActiveName: subMenuActiveName, menuActiveName: '智慧屏幕' } })
            } else {
                this.$router.push({ name: 'screen-extendedProgram/cubeScreen', params: { subMenuActiveName: '魔方屏', menuActiveName: '智慧屏幕' } })
            }
        },
    },
    mounted() {
        let addFrom = this.$route.params.addFrom
        let backRouter = this.$route.params.backRouter
        if (backRouter) {
            this.backRouter = backRouter
        }
        this.canvasWidth = program.canvasWidth
        this.canvasName = program.canvasName
        this.programData.loadingSave = false
        this.programData.loadingSaveAndExit = false
        if (addFrom) {
            if (addFrom.canvasWidth != undefined) {
                this.canvasWidth = addFrom.canvasWidth
                this.programData.canvasWidth = this.canvasWidth
            }

            if (addFrom.canvasName != undefined) {
                this.canvasName = addFrom.canvasName
                this.programData.canvasName = this.canvasName
            }
        }
        // 修改尺寸
        program.updateItems()
    },
    watch: {
        program: { //监听的对象  this.programData指向program
            handler() {
                this.programData = program
            },
            immediate: true,
        }
    },
}
</script>
<style scoped>

</style>