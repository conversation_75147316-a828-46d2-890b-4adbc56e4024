<template>
  <div class="cubeContent">
    <div class="main">
      <ul class="box" id="box">
        <li v-for="(item, index) in programLayout" :key="index">
          <div class="con">
            <span v-show="item.titleShow">{{item.title}}</span>
          </div>
        </li>
      </ul>
      <dl class="minBox" id="minBox">
        <dd v-for="(item, index) in programLayout" :key="index">
          <div class="con">
            <img :src="item.sources[0].isDefalut ? require('@/assets/img/cubeDefaultImg.png'): require('@/assets/img/audio.png')" style="height: 100%; width: 100%;"/>
          </div>
        </dd>
      </dl>
    </div>

  </div>
</template>

<script>
import program from "./program.js"
import { EventBus } from './event-bus.js';
export default {
    data() {
        return {
            programLayout: [],
            list: [
                { index: 0, titleShow: true, title: "前-1-1" },
                { index: 1, titleShow: true, title: "左-1-2" },
                { index: 2, titleShow: true, title: "后-1-3" },
                { index: 3, titleShow: true, title: "右-1-4" },
                { index: 4, titleShow: true, title: "下-1-5" },
                { index: 5, titleShow: true, title: "上-1-6" },
            ],
            rotate: 0,
            boxTimer: null,
        };
    },
    methods: {
        imgMove2() {
            let oBox = document.getElementById('box');
            let oMinBox = document.getElementById('minBox');
            var then = this
            let flag = true
            function updateAnimation() {
                then.rotate += 0.5;
                if (flag) {
                    // 向下翻滚
                    if (then.rotate > 360) {
                        flag = false
                        then.rotate = 0
                    }
                    then.programLayout[1].titleShow = false
                    then.programLayout[3].titleShow = false
                    if (then.rotate <= 0 || then.rotate <= 90) {// 前
                        then.programLayout[0].titleShow = true
                        then.programLayout[2].titleShow = false
                        then.programLayout[4].titleShow = true
                        then.programLayout[5].titleShow = true
                        if (then.rotate >= 10) {
                            then.programLayout[4].titleShow = false
                        }
                    } else if (then.rotate <= 90 || then.rotate <= 180){// 上
                        then.programLayout[0].titleShow = true
                        then.programLayout[2].titleShow = true
                        then.programLayout[4].titleShow = false
                        then.programLayout[5].titleShow = true
                        if (then.rotate >= 100) {
                            then.programLayout[0].titleShow = false
                        }
                    } else if (then.rotate <= 180 || then.rotate <= 270){ // 后
                        then.programLayout[0].titleShow = false
                        then.programLayout[2].titleShow = true
                        then.programLayout[4].titleShow = true
                        then.programLayout[5].titleShow = true
                        if (then.rotate >= 100) {
                            then.programLayout[5].titleShow = false
                        }
                    } else if (then.rotate <= 270 || then.rotate <= 360){ // 下
                        then.programLayout[0].titleShow = true
                        then.programLayout[2].titleShow = true
                        then.programLayout[4].titleShow = true
                        then.programLayout[5].titleShow = false
                        if (then.rotate >= 280) {
                            then.programLayout[2].titleShow = false
                        }
                    }
                    oBox.style.transform = 'perspective(800px) translateZ(0px)  rotateY(0deg) rotateX(' + -then.rotate + 'deg)';
                    oMinBox.style.transform =
                    'perspective(800px) rotateY(0deg) translateZ(0px) rotateX(' + -then.rotate + 'deg)';
                } else{
                    // 向左翻滚
                    if (then.rotate > 360) {
                        flag = true
                        then.rotate = 0
                    }
                    then.programLayout[3].titleShow = true
                    then.programLayout[4].titleShow = false
                    then.programLayout[5].titleShow = false
                    if (then.rotate <= 0 || then.rotate <= 90) {// 前
                        then.programLayout[0].titleShow = true
                        then.programLayout[2].titleShow = false
                        then.programLayout[1].titleShow = true
                        then.programLayout[3].titleShow = true
                        if (then.rotate >= 10) {
                            then.programLayout[3].titleShow = false
                        }
                    } else if (then.rotate <= 90 || then.rotate <= 180){// 左
                        then.programLayout[0].titleShow = true
                        then.programLayout[2].titleShow = true
                        then.programLayout[1].titleShow = true
                        then.programLayout[3].titleShow = false
                        if (then.rotate >= 100) {
                            then.programLayout[0].titleShow = false
                        }
                    } else if (then.rotate <= 180 || then.rotate <= 270){// 后
                        then.programLayout[0].titleShow = false
                        then.programLayout[2].titleShow = true
                        then.programLayout[1].titleShow = true
                        then.programLayout[2].titleShow = true
                        if (then.rotate >= 100) {
                            then.programLayout[1].titleShow = false
                        }
                    } else if (then.rotate <= 270 || then.rotate <= 360){// 右
                        then.programLayout[0].titleShow = true
                        then.programLayout[2].titleShow = true
                        then.programLayout[1].titleShow = false
                        then.programLayout[3].titleShow = true
                        if (then.rotate >= 280) {
                            then.programLayout[2].titleShow = false
                        }
                    }

                    oBox.style.transform = 'perspective(800px) translateZ(0px)  rotateX(0deg) rotateY(' + then.rotate + 'deg)';
                    oMinBox.style.transform =
                    'perspective(800px) rotateX(0deg) translateZ(0px) rotateY(' + then.rotate + 'deg)';
                }
                then.boxTimer = window.requestAnimationFrame(updateAnimation)
            }
            then.boxTimer = window.requestAnimationFrame(updateAnimation)

        },
        onClick() {
            this.imgMove2();
            // window.cancelAnimationFrame(this.boxTimer)
        },
    },
    mounted() {
        this.imgMove2();
    },
    created() {
        EventBus.$on('changeSelectItem', (itemIndex) => {
            this.programLayout = program.programItems[program.selectItem - 1]._program.layout
        })
    },
    beforeDestroy() {
        EventBus.$off('changeSelectItem');
    },
    watch: {
        program: { //监听的对象  this.programData指向program
            handler() {
                this.$nextTick(() => {
                    this.programLayout = program.programItems[program.selectItem - 1]._program.layout
                })
            },
            immediate: true,
        }
    },
}
</script>
<style scoped>
    .cubeContent{
        position:relative;
        top: 50%;
        transform: translateY(-50%);
        left: 50%;
        transform: translate(-50%, -50%);
        width: 500px;
        height: 500px;
        /* background-color: brown; */
    }
    .main {
        position: relative;
        width: 500px;
        height: 500px;
        margin: 0px auto;
    }

    .box {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 5;
        width: 270px;
        height: 270px;
        margin-top: -140px;
        margin-left: -140px;
        transform: perspective(800px) rotateX(-0deg) rotateY(-0deg) translateZ(-20px);
        transform-style: preserve-3d;
    }
    
    .box li {
        position: absolute;
        width: 270px;
        height: 270px;
        list-style: none;
    }
    .box li .con {
        line-height: 270px;
        text-align: center;
        color: #000;
        font-size: 25px;
        width: 270px;
        height: 270px;
        vertical-align: middle;
        border: 1px dashed #ffffff;
        /* 半透明的白色玻璃效果 */
        background-color: rgba(255, 255, 255, 0.2); 
    }

    .box li:nth-child(1) {
        -webkit-transform: translateZ(135px);
        transform: translateZ(135px);
    }

    .box li:nth-child(2) {
        -webkit-transform: rotateY(90deg) translateZ(-135px);
        transform: rotateY(90deg) translateZ(-135px);
    }

    .box li:nth-child(3) {
        -webkit-transform: translateZ(-135px);
        transform: translateZ(-135px);
    }

    .box li:nth-child(4) {
        -webkit-transform: rotateY(90deg) translateZ(135px);
        transform: rotateY(90deg) translateZ(135px);
    }

    .box li:nth-child(5) {
         -webkit-transform: rotateX(90deg) translateZ(-135px);
        transform: rotateX(90deg) translateZ(-135px);
    }

    .box li:nth-child(6) {
        -webkit-transform: rotateX(90deg) translateZ(135px);
        transform: rotateX(90deg) translateZ(135px);
    }

    /*内层图形样式*/
    .minBox {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 4;
        width: 210px;
        height: 210px;
        margin-top: -110px;
        margin-left: -110px;
        transform: perspective(800px) rotateX(-0deg) rotateY(-0deg) translateZ(-20px);
        transform-style: preserve-3d;
    }

    .minBox dd {
        position: absolute;
        width: 210px;
        height: 210px;
    }

    .minBox dd .con {
        width: 210px;
        height: 210px;
    }

    .minBox dd:nth-child(1) {
        -webkit-transform: translateZ(105px);
        transform: translateZ(105px);
    }

    .minBox dd:nth-child(2) {
        -webkit-transform: rotateX(90deg) translateZ(-105px);
        transform: rotateX(90deg) translateZ(-105px);
    }

    .minBox dd:nth-child(3) {
        -webkit-transform: translateZ(-105px);
        transform: translateZ(-105px);
    }

    .minBox dd:nth-child(4) {
        -webkit-transform: rotateX(90deg) translateZ(105px);
        transform: rotateX(90deg) translateZ(105px);
    }

    .minBox dd:nth-child(5) {
        -webkit-transform: rotateY(90deg) translateZ(-105px);
        transform: rotateY(90deg) translateZ(-105px);
    }

    .minBox dd:nth-child(6) {
        -webkit-transform: rotateY(90deg) translateZ(105px);
        transform: rotateY(90deg) translateZ(105px);
    }
</style>