
export default {
    loadingSave: false, // 等待保存
    loadingSaveAndExit: false, // 等待保存并退出
    isView: false, // 是否预览模式
    infoId: 0,// 详情Id
    programId: "",  //修改用到的设备id
    sourceFileIds: "",  //修改用到的媒体id
    // 当前选中的列表
    selectItem: 1,
    // 编辑框宽
    canvasWidth: 128,
    // 播放模式
    playModel: 'syncModel',
    // 播放时长
    playTime: 10,
    canvasName: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
    programItems:[
        {
            _id: 'c582ea7f5ef9b55124f546c760b21174',
            priority: 0,
			repeatTimes:1,
			schedules :[],
			version:0,
            _program: {
                _id: 'c582ea7f5ef9b55124qw46c760b21174',
                selectItem: 1,
                layout:[
                    {
                        repeat: false,
                        id: 1,
                        title: '前-1-1',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image",id:"1803602505725136898",md5:'1803602505725136898',
                                width: 128, height: 128,timeSpan: 10,top: 0, left: 128 * 0,
                                isDefalut: false,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 2,
                        title: '左-1-2',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: 128, height: 128,timeSpan: 10,top: 0, left: 128 * 1,
                                isDefalut: true,playTime: 0,enabled:true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 3,
                        title: '后-1-3',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: 128, height: 128,timeSpan: 10,top: 0, left: 128 * 2,
                                isDefalut: true,playTime: 0,enabled:true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 4,
                        title: '右-1-4',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: 128, height: 128,timeSpan: 10,top: 0, left: 128 * 3,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 5,
                        title: '下-1-5',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: 128, height: 128,timeSpan: 10,top: 0, left: 128 * 4,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 6,
                        title: '上-1-6',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: 128, height: 128,timeSpan: 10,top: 0, left: 128 * 5,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    }
                ],
            },
        }
    ],
    // 修改Items中所有的参数
    updateItems(){
        for (let i = 0; i < this.programItems.length; i++) {
            var programItem = this.programItems[i];
            for (let j = 0; j < programItem._program.layout.length; j++) {
                var layoutItem = programItem._program.layout[j];
                for (let k = 0; k < layoutItem.sources.length; k++) {
                    var sources = layoutItem.sources[k];
                    sources.width = this.canvasWidth
                    sources.height = this.canvasWidth
                    sources.left = this.canvasWidth * j
                    if (this.playModel == "syncModel") {
                        sources.timeSpan = this.playTime
                    }
                }
            }
        }
    },
    // 修改播放时长
    updateTimeSpan(){
        for (let i = 0; i < this.programItems.length; i++) {
            var programItem = this.programItems[i];
            for (let j = 0; j < programItem._program.layout.length; j++) {
                var layoutItem = programItem._program.layout[j];
                for (let k = 0; k < layoutItem.sources.length; k++) {
                    var sources = layoutItem.sources[k];
                    if (this.playModel == "syncModel") {
                        sources.timeSpan = this.playTime
                    } else {
                        if (sources._type != "Video"){
                            sources.timeSpan = this.playTime
                        } else {
                            // 如果是视频需要改为视频的播放时长。 TODO
                        }
                    }
                }
            }
        }
    },
    // 添加一个列表
    addProgramItem() {
        this.programItems.push({
            _id: 'c582ea7f5ef9b55124f546c760b21174',
            priority: 0,
			repeatTimes:1,
			schedules :[],
			version:0,
            _program: {
                _id: 'c582ea7f5ef9b55124qw46c760b21174',
                selectItem: 1,
                layout:[
                    {
                        repeat: false,
                        id: 1,
                        title: '前-1-1',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 0,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 2,
                        title: '左-1-2',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 1,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 3,
                        title: '后-1-3',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 2,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 4,
                        title: '右-1-4',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 3,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 5,
                        title: '下-1-5',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 4,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 6,
                        title: '上-1-6',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 5,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    }
                ],
            },
        })
    },
    // 清除所有媒体
    clearAllMedia(index) {
        this.programItems.splice(index, 1, {
            _id: 'c582ea7f5ef9b55124f546c760b21174',
            priority: 0,
			repeatTimes:1,
			schedules :[],
			version:0,
            _program: {
                _id: 'c582ea7f5ef9b55124qw46c760b21174',
                selectItem: 1,
                layout:[
                    {
                        repeat: false,
                        id: 1,
                        title: '前-1-1',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 0,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 2,
                        title: '左-1-2',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 1,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 3,
                        title: '后-1-3',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 2,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 4,
                        title: '右-1-4',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 3,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 5,
                        title: '下-1-5',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 4,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    },
                    {
                        repeat: false,
                        id: 6,
                        title: '上-1-6',
                        titleShow: true,
                        sources:[
                            {   
                                name:"cubeDefaultImg",size: 11782,mime:"image/png",
								_type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, left: this.canvasWidth * 5,
                                isDefalut: true,playTime: 0, enabled: true,
                                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
                            }
                        ]
                    }
                ],
            },
        });
    },
    // 清除指定媒体 
    clearMedia(index) {
        this.programItems[index]._program.layout[this.programItems[index]._program.selectItem - 1].sources = [
            {
                name:"cubeDefaultImg",size: 11782,mime:"image/png",
                _type:"Image",fileExt: "Image", id:"1803602505725136898",md5:'1803602505725136898',
                width: this.canvasWidth, height: this.canvasWidth,timeSpan: this.playTime,top: 0, 
                left: this.canvasWidth * (this.programItems[index]._program.layout[this.programItems[index]._program.selectItem - 1].id - 1),
                isDefalut: true,playTime: 0, enabled: true,
                entryEffectTimeSpan: 2, entryEffect: "None",exitEffectTimeSpan: 2, exitEffect: "None",
            }
        ]
    },
    // 计算每个program的大小
    countProgramTotalSize() {
        for (let i = 0; i < this.programItems.length; i++) {
            var programItem = this.programItems[i];
            programItem._id = this.createId()
            programItem._program._id = this.createId()
            programItem._program.height = this.canvasWidth
            programItem._program.width = this.canvasWidth * 6
            var totalSize = 0
            for (let j = 0; j < programItem._program.layout.length; j++) {
                var layoutItem = programItem._program.layout[j];
                for (let k = 0; k < layoutItem.sources.length; k++) {
                    var sources = layoutItem.sources[k];
                    totalSize += sources.size
                }
            }
            programItem._program.totalSize = totalSize
        }
    },
    // 保存数据
    preservation(isExit){
        if (isExit == true) {
            this.loadingSaveAndExit = true
        } else {
            this.loadingSave = true
        }
        // 计算每一个program的大小。
        this.countProgramTotalSize()
        // 整合节目完整结构
        let totalData = {
            "type": "commandXixunPlayer",
            "_id": this.createId(),
            "command": {
                "_type": "PlayXixunTask",
                "id": thisOutside.createId(),
                "notificationURL": 'http://*************:8080/progress/notification',
                "preDownloadURL": 'http://*************:8080/sys/program/file/download/',
                "task": {
                    "_department": null,
                    "_id": this.createId(),
                    "cmdId": this.createId(),
                    "insert": false,
                    "items": this.programItems,
                    "name": this.canvasName
                }
            }
        }
        let obj = {
            equipment: totalData,
            programId: this.programId,
        }
        console.log(obj)

        if (isExit == true) {
            this.loadingSaveAndExit = false
        } else {
            this.loadingSave = false
        }
    },
    /**
     * 此方法创建一个新id ：b6733a9e-bd6e-40b3-834c-7e4298f352a9
     * @returns
     */
    createId() {
        function S4() {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        };
        // Generate a pseudo-GUID by concatenating random hexadecimal.
        return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
    },
}
/* // 节目任务json
    programTask:{
        // 节目名称
        name: "" + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + new Date().getHours() + new Date().getMinutes() + new Date().getSeconds(),
        _id: this.createId(),
        cmdId: this.createId(),
        insert: false,
        // 节目整体
        items:[],
    }, */