<template>
  <div style="height: calc(100% - 55px);">
    <Button type="primary" @click="addProgramItem()" style="margin: 5px 5px 0px 5px;">添加列表</Button>
    <div class="list">
        <ul>
            <li @mouseenter="selectProgramItemMouse(index)" @mouseleave="selectProgramItemLeave(index)" @click="selectProgramItemClick(index + 1)" 
                :class="(index + 1) == programData.selectItem? 'defaultLi selectLi': 'defaultLi'"
                v-for="(item,index) in programData.programItems" :key="index">
                <div class="programItem">
                    <div :ref="'selectProgramItem' + index" :class="((index + 1) == programData.selectItem)? 'circle selectCircle': 'circle'">{{(index + 1)}}</div>
                    <ul>
                        <li @mouseenter="selectItemMouse(index,index1)" @mouseleave="selectItemLeave(index,index1)" @click="selectItemClick(index,index1 + 1)"
                            class="programItemList" v-for="(item1,index1) in item._program.layout" :key="index1">
                            <img :src="item1.sources[0].isDefalut ? require('@/assets/img/cubeDefaultImg.png'): require('@/assets/img/audio.png')" style="height: 50px; width: 50px;margin: 0px;"/>
                            <span :ref="'selectItem' + index1" :class="(index1 + 1) == item._program.selectItem ?  'itemTitle itemTitleSelect': 'itemTitle'">{{item1.title}}</span>
                        </li>
                    </ul>
                    <ButtonGroup vertical>
                        <Button @click="deleteProgramItem(index)" type="error"
                             size="small" :disabled="programData.programItems.length <= 1">删除列表</Button>
                        <Button @click="clearAllMedia(index)" type="warning" size="small" style="margin-top: 5px;">清除所有媒体</Button>
                    </ButtonGroup>
                </div>
                <transition name="fade">
                    <div v-if="(index + 1) == programData.selectItem" class="programItemFeature transition-box">
                        <ul>
                            <li @click="featureByTypeClick(item1.type)" v-for="(item1,index1) in featureList" :key="index1" class="programItemFeatureLi">
                                <svg width="30px" height="30px" aria-hidden="true">
                                    <use :xlink:href="'#' + item1.icon"></use>
                                </svg>
                                <div>{{$t(item1.title)}}</div>
                            </li>
                            <li class="anim">
                                <Form ref="animEntranceModel" :label-width="40">
                                    <FormItem prop="entryEffect" label="入场" style="margin-bottom: 2px;">
                                        <Select transfer v-model="item._program.layout[item._program.selectItem - 1].sources[0].entryEffect" 
                                            style="width:120px;">
                                            <Option v-for="item in animList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem prop="entryEffectTimeSpan" label="时长" style="margin-bottom: 0px;">
                                        <InputNumber style="width:100px;height: 30px;" 
                                            v-model="item._program.layout[item._program.selectItem - 1].sources[0].entryEffectTimeSpan" 
                                            :max="70000" :min="1" controls-outside />
                                        <span>秒</span>
                                    </FormItem>
                                </Form>
                            </li>
                            <li class="anim">
                                <Form ref="animEnterModel" :label-width="40">
                                    <FormItem prop="exitEffect" label="出场" style="margin-bottom: 2px;">
                                        <Select transfer v-model="item._program.layout[item._program.selectItem - 1].sources[0].exitEffect" 
                                            style="width:120px">
                                            <Option v-for="item in animList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem prop="exitEffectTimeSpan" label="时长" style="margin-bottom: 0px;">
                                        <InputNumber style="width:100px;height: 30px;" 
                                            v-model="item._program.layout[item._program.selectItem - 1].sources[0].exitEffectTimeSpan" 
                                            :max="70000" :min="1" controls-outside />
                                        <span>秒</span>
                                    </FormItem>
                                </Form>
                            </li>
                        </ul>
                    </div>
                </transition>
            </li>
        </ul>
    </div>
    <div class="advanced">
        <div style="margin-bottom: 5px;">
            <span style="font-size: 17px; font-weight: 700;">播放时长</span> 
            <Tooltip placement="right-start" max-width="400" transfer-class-name="tooltipClass">
                <Icon type="ios-help-circle" size="20" style="cursor:pointer;"/>
                <template #content>
                    <strong>播放时长的作用范围为每行六个媒体<br></strong>
                    <span>1、异步时长模式下，视频按照视频时长播放，其他媒体按照10s播放。<br></span>
                    <span>2、同步时长模式下，本行列表不包含视频，那么播放时长为10s；包含视频，则按照最长的视频的时长播放。<br></span>
                    <span>3、自定义时长模式下，本行列表所有媒体按照指定时长播放。</span>
                </template>
            </Tooltip>
        </div>
         <Form ref="palyModel" inline :label-width="50">
            <FormItem prop="model" label="模式">
                <Select transfer v-model="programData.playModel" style="width:150px" @on-change="palyModelChange">
                    <Option v-for="item in modelList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
            </FormItem>
            <FormItem prop="playTime" label="时长">
                <InputNumber style="width:120px" :disabled="programData.playModel=='asyncModel'" @on-change="playTimeChange"
                    v-model="programData.playTime" :max="70000" :min="1" controls-outside />
                <span>秒</span>
            </FormItem>
         </Form>
        
    </div>
  </div>
</template>

<script>
import { EventBus } from './event-bus.js';
import program from "./program.js"
export default {
    data() {
        return {
            programData: {},
            modelList: [
                { value: 'asyncModel', label: '异步模式' },
                { value: 'syncModel', label: '同步模式' },
            ],
            featureList: [
                {icon: 'exportImg', title: '导入图片', type: 'exportImg'},
                {icon: 'exportVideo', title: '导入视频', type: 'exportVideo'},
                {icon: 'exportTemp', title: '导入模版', type: 'exportTemp'},
                {icon: 'copyList', title: '填充列表', type: 'copyList'},
                {icon: 'clearMedia', title: '删除媒体', type: 'clearMedia'},
            ],
            animList: [
                { label: "无", value: "None" },
                { label: "淡入", value: "ALPHA_IN" },
                { label: "连续左移", value: "MOVING_LEFT" },
                { label: "连续右移", value: "MOVING_RIGHT" },
                { label: "连续上移", value: "MOVING_TOP" },
                { label: "连续下移", value: "MOVING_BOTTOM" },
                { label: "放大", value: "ZOOM_IN" },
                { label: "向右旋转", value: "ROTATE_RIGHT" },
                { label: "向左旋转", value: "ROTATE_LEFT" },
                { label: "左上角放大", value: "ZOOM_IN_LEFT_TOP" },
                { label: "左下角放大", value: "ZOOM_IN_LEFT_BOTTOM" },
                { label: "右上角放大", value: "ZOOM_IN_RIGHT_TOP" },
                { label: "右下角放大", value: "ZOOM_IN_RIGHT_BOTTOM" },
            ],
            // 文件前缀
            filePathPrefix: window.SITE_CONFIG.baseUrl + "/sys/file/download/"
        }
    },
    methods: {
        // 选择播放模式
        palyModelChange(model){
            if (model == 'asyncModel'){
                this.programData.playTime = 10
                program.updateTimeSpan()
            }
        },
        // 修改播放时长
        playTimeChange(){
            program.updateTimeSpan()
        },
        //选择某个item
        selectProgramItemClick(itemIndex) {
            this.programData.selectItem = itemIndex
            EventBus.$emit('changeSelectItem');
        },
        // 鼠标移入某个item
        selectProgramItemMouse(index){
            if (index + 1 != this.programData.selectItem) {
                this.$refs['selectProgramItem'+index][0].classList.add('selectCircle')
            }
        },
        // 移出
        selectProgramItemLeave(index){
            if (index + 1 != this.programData.selectItem) {
                this.$refs['selectProgramItem'+index][0].classList.remove('selectCircle')
            }
        },
        // 删除一个列表
        deleteProgramItem(index){
            this.$Modal.confirm({
                title: this.$t("common.tips"),
                content: '确定删除当前列表',
                okText: this.$t("common.confirm"),
                cancelText: this.$t("common.cancel"),
                onOk: () => {
                    if (this.programData.programItems.length > 1) {
                        this.programData.programItems.splice(index, 1)
                        setTimeout(()=>{
                            if (this.programData.selectItem > this.programData.programItems.length) {
                                this.programData.selectItem = this.programData.programItems.length
                                EventBus.$emit('changeSelectItem');
                            }
                        }, 100)
                    }
                },
            });
        },
        // 添加一个列表
        addProgramItem() {
            this.programData.addProgramItem()
            this.programData.selectItem = this.programData.programItems.length
            EventBus.$emit('changeSelectItem');
        },
        // 选中一个面
        selectItemClick(proIndex, itemIndex){
            this.programData.programItems[proIndex]._program.selectItem = itemIndex
        },
        // 鼠标移入某个item的一个面
        selectItemMouse(proIndex, itemIndex){
            if (itemIndex + 1 != this.programData.programItems[proIndex]._program.selectItem) {
                this.$refs['selectItem'+itemIndex][proIndex].classList.add('itemTitleSelect')
            }
        },
        // 移出
        selectItemLeave(proIndex, itemIndex){
            if (itemIndex + 1 != this.programData.programItems[proIndex]._program.selectItem) {
                this.$refs['selectItem'+itemIndex][proIndex].classList.remove('itemTitleSelect')
            }
        },
        // 清除所有媒体
        clearAllMedia(index) {
            this.$Modal.confirm({
                title: this.$t("common.tips"),
                content: '确定清除所有媒体',
                okText: this.$t("common.confirm"),
                cancelText: this.$t("common.cancel"),
                onOk: () => {
                    program.clearAllMedia(index)
                },
            });
        },
        // 功能项
        featureByTypeClick(type) {
            if (type == "exportImg") {
                this.$Message.info('exportImg')
            } else if (type == "exportVideo") {
                this.$Message.info('exportVideo')
            } else if (type == "exportTemp") {
                this.$Message.info('exportTemp')
            } else if (type == "copyList") {
                this.$Message.info('copyList')
            } else if (type == "clearMedia") {
                this.$Modal.confirm({
                    title: this.$t("common.tips"),
                    content: '确定删除当前媒体',
                    okText: this.$t("common.confirm"),
                    cancelText: this.$t("common.cancel"),
                    onOk: () => {
                        program.clearMedia(this.programData.selectItem - 1)
                    },
                });
            } else {
                this.$Message.info('无效功能')
            }
        }
    },
    watch: {
        program: { //监听的对象  this.programData指向program
            handler() {
                this.$nextTick(() => {
                    this.programData = program
                })
            },
            immediate: true,
        }
    },
}
</script>
<style scoped>
.list {
    height: calc(100% - 200px);
    width: 100%;
    overflow-x: auto;
    background-color: #F5F7FA;
}
.defaultLi:hover{
    cursor: pointer;
    color: #515a6e;
    background-color: #2d8bf033;
}
.defaultLi {
    overflow-y: hidden;
    height: 80px;
    width: 98%;
    margin: 3px auto;
    transition: height 1s;
    border: #8d8d8d solid 1px;
}
.selectLi {
    height: 160px;
    color: #515a6e;
    background-color: #2d8bf033;
}
.circle {
    display:inline-block; /* 使用flexbox布局 */
    text-align: center;
    line-height: 25px;
    width: 25px;          /* 圆圈的宽度 */
    height: 25px;         /* 圆圈的高度 */
    border-radius: 50%;     /* 圆形 */
    background-color: #cccccc; /* 圆圈的背景颜色 */
    color: white;         /* 数字的颜色 */
    font-size: 15px;       /* 数字的字体大小 */
    font-weight: bold;     /* 数字的字体加粗 */
    margin-right: 10px;
}
.selectCircle{
    background-color: #3b8fe9;
}
.programItem{
    margin-left: 5px;
    height: 78px;
    width: calc(100% - 5px);
    display: flex; /* 使用Flexbox布局 */
    align-items: center; /* 垂直居中 */
}
.programItemFeature{
    padding-top: 3px;
    border-top: #cccccc solid 1px;
    margin-top: 5px;
    /* margin-left: 50px; */
    height: calc(100% - 85px);
    width: 100%;
    align-items: center; /* 垂直居中 */
}
.programItemFeatureLi{
    float: left;
    padding-top: 5px;
    margin: 0px 5px;
    text-align: center;
    transition: padding-top 0.5s;
}
.programItemFeatureLi:hover {
    padding-top: 7px;
    color: #fff;
    background-color: #5cadff;
}
.anim{
    margin-left: 5px;
    float: left;
    width: 170px;
    height: 57px;
}
.programItemList {
    margin: 0px 5px;
    text-align: center;
    float: left;
    height: 65px;
    width: 65px;
    /* border: #cccccc solid 1px; */
    line-height: 12px;
}
.itemTitle {
    display: inline-block;
    width: 50px;
    margin: 0px;
}
.itemTitleSelect {
    color: #fff;
    background-color: rgb(247, 175, 175);
}
.advanced{
    overflow-x: auto;
    height: 145px;
    width: 98%;
    padding: 5px;
    border-top: #8d8d8d solid 1px;
    background-color: white;
}
.tooltipClass{
    font-size: 14px;
    text-align: left;
}
</style>