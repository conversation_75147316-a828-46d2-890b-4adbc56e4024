<template>
    <div class="standard">
        <div class="left">
            <cube-view></cube-view>
        </div>
        <div class="right">
            <program-top class="right-header"></program-top>
            <program-list></program-list>
        </div>
    </div>
</template>

<script>
import programTop from './programModular/header.vue'
import programList from './programModular/programList.vue'
import cubeView from './programModular/cubeView.vue'

export default {
    components:{
        programTop,programList,cubeView
    }
}
</script>
<style scoped>
.right-header{
    line-height: 55px;
    padding: 0;
    height: 55px;
    background-color: white;
    border-bottom: #8d8d8d solid 1px
}
.left{
    overflow: auto;
    float: left;
    background-color: black;
    height:100%;
    width: calc(100% - 700px);
}
.right{
    float: left;
    background-color: rgb(255, 255, 255);
    height:100%;
    width: 700px;
}
.standard{
    width:100%;
    height:100%;
    overflow: hidden;
}
</style>