<template>
  <div>
    <Form :inline="true" :model="dataForm" @keyup.enter.native="init(isTodo,1)">
      <FormItem>
        <Button v-if="isAuth('screen:media:save') && isTodo === false" style="margin-right: 6px" size="large"
          type="primary" @click="addOrUpdateHandle()">
          <div style="margin: 3px 8px">{{ $t("common.addMedia") }}</div>
        </Button>
      </FormItem>
      <FormItem>
        <Input size="large" v-model="dataForm.name" :placeholder="$t('file.name')"></Input>
      </FormItem>
      <FormItem v-if="isTodo === false">
        <Select size="large" v-model="dataForm.type" filterable clearable
          :placeholder="$t('common.PleaseSelect') + $t('file.type')">
          <Option v-for="item in fileType" :value="item.suffix" :key="item.suffix">{{ item.suffix }}</Option>
        </Select>
      </FormItem>
      <FormItem v-if="isTodo === false">
        <Select size="large" v-model="dataForm.status" v-if="isTodo === false" filterable clearable
          :placeholder="$t('common.PleaseSelect') + $t('file.status')">
          <Option v-for="item in fileStatus" :value="item.value" :key="item.value">{{ $t(item.label) }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button style="margin-right: 6px" @click="init(isTodo,1)" size="large">
          <div style="margin: 3px 8px">{{ $t("common.query") }}</div>
        </Button>
        <Button type="warning" size="large" style="margin-right: 6px" v-if="isTodo === true" :disabled="dataListSelections.length <= 0" @click="auditFlowHandle()">{{$t('approval.batchReview')}}</Button>
        <Button v-if="isAuth('screen:media:delete') && isTodo === false" style="margin-right: 6px" size="large"
          type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin: 3px 8px">{{ $t("common.batchDel") }}</div>
        </Button>
      </FormItem>
    </Form>
    <Table border :columns="dataConlums" :data="dataList" @on-selection-change="selectionChangeHandle"
      :loading="dataListLoading" style="width: 100%" :max-height="tableHeight" ref="selection"
      @on-row-click="selectThisRow">
      <template slot-scope="{ row, index }" slot="fileSize">
        <span>{{ row.fileSize | filterType }}</span>
      </template>
      <template slot-scope="{ row, index }" slot="filePath">
        <div v-if="row.suffix === 'mp4'">
          <svg width="50px" height="50px" style="margin-top: 5px" aria-hidden="true" @click="setPicture(row.fileId, 1)">
            <use xlink:href="#video"></use>
          </svg>
        </div>
        <div v-else-if="row.suffix === 'mp3'">
          <div @click="setPicture(row.fileId, 2)">
            <img src="@/assets/img/audio.png" style="margin-top: 5px" height="50px" width="50px" />
          </div>
        </div>
        <div v-else @click="setPicture(row.fileId, 3)">
          <img :src="downloadUrl + row.fileId" style="margin-top: 5px" height="50px" width="50px" />
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="status">
        <Tag color="orange" v-if="row.status === 0">{{ $t("file.checkPending") }}</Tag>
        <Tag color="cyan" v-if="row.status === 1">{{ $t("file.under_review") }}</Tag>
        <Tag color="blue" v-if="row.status === 2">{{ $t("file.approved") }}</Tag>
        <Tag color="volcano" v-if="row.status === 3">{{ $t("file.auditFailed") }}</Tag>
      </template>
      <template slot-scope="{ row, index }" slot="operation">
        <!-- :href="downloadUrl + row.fileId" -->
        <Button type="dashed" size="small" style="margin-right: 5px; font-size: 11px" v-if="isTodo === false">
          <a target="_blank" :href="downloadUrl + row.fileId">{{ $t("file.download") }}</a>
        </Button>
        <Button type="warning" size="small" v-if="userInfo.noExamine === 0 && isTodo === false" style="margin-right: 5px; font-size: 11px"
         @click="auditFlowHandle(row.fileId)">{{ $t("screen.reviewDetails") }}</Button>
        <Button v-if="isAuth('screen:media:delete') && isTodo === false" type="error" size="small"
          style="font-size: 11px" @click="deleteHandle(row.fileId)">{{ $t("common.delete") }}</Button>
        <Button type="warning" size="small" v-if="isTodo === true" style="margin-right: 5px; font-size: 11px"
          @click="auditFlowHandle(row.fileId)">{{ $t("file.examine") }}</Button>
      </template>
    </Table>
    <Page style="float: right; margin-top: 20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
      show-elevator show-sizer :page-size-opts="[10, 20, 50, 100]" show-total @on-change="currentChangeHandle"
      @on-page-size-change="sizeChangeHandle" />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="init(isTodo)"></add-or-update>
    <!-- 审批流 -->
    <audit-flow v-if="auditFlowVisible" ref="auditFlow" @refreshDataList="init(isTodo)"></audit-flow>
  </div>
</template>

<script>
import AddOrUpdate from "./media-add-or-update";
import auditFlow from "./media-audit-flow";
import Media from '@dongido/vue-viaudio'
export default {
  data() {
    return {
      dataForm: {
        name: "",
        type: "",
        status: "",
      },
      fileType: "",
      fileStatus: [
        { value: "0", label: "file.checkPending" },
        { value: "1", label: "file.under_review" },
        { value: "2", label: "file.approved" },
        { value: "3", label: "file.auditFailed" },
      ],
      dataConlums: [
        { type: "selection", width: 60, align: "center" },
        {
          title: this.$t("file.thumbnail"),
          key: "filePath",
          slot: "filePath",
          align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('file.thumbnail'))
          }
        },
        {
          title: this.$t("file.name"),
          key: "fileName",
          align: "center",
          tooltip: true,
          renderHeader: (h) => {
            return h('div', this.$t('file.name'))
          }
        },
        {
          title: this.$t("file.type"), key: "suffix", align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('file.type'))
          }
        },
        {
          title: this.$t("file.size"),
          key: "fileSize",
          slot: "fileSize",
          align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('file.size'))
          }
        },
        {
          title: this.$t("common.createTime"),
          key: "createTime",
          align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('common.createTime'))
          }
        },
        {
          title: this.$t("common.state"),
          key: "status",
          slot: "status",
          align: "center",
          renderHeader: (h) => {
            return h('div', this.$t('common.state'))
          }
        },
        {
          title: this.$t("common.operation"),
          slot: "operation",
          align: "center",
          width: 250,
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      auditFlowVisible: false,
      downloadUrl: this.$http.adornUrl(`/sys/file/download/`),
      isTodo: false,
    };
  },
  activated() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      this.dataForm = {
        name: "",
        type: "",
        status: "",
      }
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListSelections = []
    },
    setPicture(fileId, type) {
      if (type === 3) {
        window.open(this.$http.adornUrl(`/sys/file/download/${fileId}?inline=true`))
      } else if (type === 2) {
        var page = window.open()
        var html = `<body style='background:black'>`
        html += `<div style='margin:0 auto;'>`
        html += `<audio controls="controls" autoplay style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; max-height: 100%; max-width: 100%; margin:auto">`
        html += `<source src="${this.$http.adornUrl(`/sys/file/download/${fileId}?inline=true`)}" type="audio/mp3"/>`
        html += `</audio>`
        html += `</div>`
        html += `</body>`
        page.document.write(html);
      } else if (type === 1) {
        var page = window.open()
        var html = `<body style='background:black'>`
        html += `<div style='width:80%;margin:auto;'>`
        html += `<video controls width='100%' autoplay src='${this.$http.adornUrl(`/sys/file/download/${fileId}?inline=true`)}'></video>`
        html += `</div>`
        html += `</body>`
        page.document.write(html);
      }
    },
    // 文件类型
    getFileType() {
      this.$http({
        url: this.$http.adornUrl("/sys/file/fileType"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.fileType = data.fileType;
        }
      });
    },
    init(isTodo,isQuery) {
      if (isQuery===1){
        this.pageIndex=1
      }
      this.getFileType();
      this.isTodo = isTodo;
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/screen/media/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex+"",
          limit: this.pageSize,
          fileName: this.dataForm.name,
          status: this.dataForm.status,
          suffix: this.dataForm.type,
          todo: isTodo === true ? 1 : "",
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.init(isTodo,isQuery)
          }
        }
        this.dataListSelections = [];
        this.dataListLoading = false;
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.init(this.isTodo);
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.init(this.isTodo);
    },
    // 多选
    selectionChangeHandle() {
      this.dataListSelections = this.$refs.selection.getSelection();
    },
    selectThisRow(data, index) {
      this.$refs.selection.toggleSelect(index);
    },
    // 删除
    deleteHandle(id) {
      var fileIds = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.fileId;
        });
      this.$Modal.confirm({
        title: this.$t("common.tips"),
        content: this.$t("common.delete_current_option"),
        okText: this.$t("common.confirm"),
        cancelText: this.$t("common.cancel"),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl("/screen/media/delete"),
            method: "post",
            data: this.$http.adornData(fileIds, false),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t("common.operationSuccessful"),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === fileIds.length) {
                    this.pageIndex--
                  }
                  this.init(this.isTodo);
                  this.dataListSelections = [];
                },
              });
            } else {
              this.$Message.error(data.msg);
            }
          });
        },
      });
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id);
      });
    },
    // 查询审核流
    auditFlowHandle(id) {
      var fileIds = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.fileId;
        });
      this.auditFlowVisible = true;
      this.$nextTick(() => {
        this.$refs.auditFlow.init(fileIds);
      });
    },
    handleFullscreen() { }
  },
  computed: {
    $video() {
      return this.$refs.vueMiniPlayer.$video;
    },
    tableHeight: {
      get() {
        return this.$store.state.common.tableHeight;
      },
    },
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
    },
  },
  filters: {
    // 文件大小转换
    filterType(val) {
      if (val === 0) {
        return "0 B";
      }
      var k = 1024;
      var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      var i = Math.floor(Math.log(val) / Math.log(k));
      return (
        parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) +
        " " +
        sizes[i]
      );
    },
  },
  components: {
    AddOrUpdate,
    auditFlow,
    Media
  },
  watch: {
    'picture': function (newVal, oldVal) {

    }
  }
};
</script>

<style>
</style>
