<template>
  <div class="modiles-program">
    <Tabs type="card" value="my" v-if="userInfo.isExamineUser" @on-click="tabsClick">
        <TabPane :label="$t('task.task')" name="my">
          <program-query-and-table ref="programQueryAndTable"></program-query-and-table>
        </TabPane>
        <TabPane :label="$t('screen.pending')" name="todo">
          <program-query-and-table-todo ref="programQueryAndTableTodo"></program-query-and-table-todo>
        </TabPane>
    </Tabs>
    <div v-else>
      <program-query-and-table ref="programQueryAndTable"></program-query-and-table>
    </div>
  </div>
</template>

<script>
import programQueryAndTable from './program-query-and-table.vue'
import programQueryAndTableTodo from './program-query-and-table.vue'
export default {
  components: {
    programQueryAndTable,
    programQueryAndTableTodo
  },
  data () {
    return {
      isTodo: false
    }
  },
  activated () {
    this.getDataList()
  },
  methods: {
    tabsClick (name) {
      if (name === 'todo') {
        this.isTodo = true
        this.getDataListTodo()
      } else {
        this.isTodo = false
        this.getDataList()
      }
    },
    // 获取数据列表
    getDataList () {
      this.$nextTick(() => {
        this.$refs.programQueryAndTable.init(this.isTodo)
      })
    },
    getDataListTodo () {
      this.$nextTick(() => {
        this.$refs.programQueryAndTableTodo.init(this.isTodo)
      })
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>

<style>

</style>
