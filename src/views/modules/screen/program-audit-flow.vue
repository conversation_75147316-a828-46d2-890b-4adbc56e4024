<template>
  <Modal v-model="visible" width="600" footer-hide>
    <p slot="header" style="text-align:center">
        <span>{{$t('file.examine')}}</span>
    </p>
    <Steps :current="current" status="process" direction="vertical" style="height: 300px">
        <Step v-if="data && data.length > 0" v-for="(item,index) in data" :key="index">
            <div slot="title">{{item.auditTime == null ? item.userName : item.userName + $t('file.auditTime') + item.auditTime}}</div>
            <div slot="content">
                <div v-if="item.status === 0">{{$t('file.checkPending')}}</div>
                <div v-if="item.status === 1">{{$t('approval.noAudit')}}</div>
                <div v-if="item.status === 2">{{$t('approval.auditFailed')}}</div>
                <div v-if="item.status === 3">{{$t('approval.approved')}}</div>
                <div>{{item.memo}}</div>
                <div style="margin-top: 20px;float:right">
                    <Button size="small" type="info" @click="handleView()">{{$t('common.info')}}</Button>
                    <Button size="small" type="warning" v-if="current === index && userInfo.userId == item.userId && item.status === 0"
                    :loading="loading" @click="handleExamine(item.userId)">{{$t('file.examine')}}</Button>
                </div>
            </div>
        </Step>
    </Steps>
    <Button type="warning"  style="float: right" v-if="userInfo.userId == 1 && oneClickAudit" :loading="loading" @click="handleExamine(1)">{{ $t('approval.clickAudit') }}</Button>
    <!-- 相关人员审核-->
    <program-to-examine v-if="toExamineVisible" ref="mediaToExamine" @refreshData="closeThisModal"></program-to-examine>
  </Modal>
</template>

<script>
import programToExamine from './program-to-examine.vue'
import program from '../program/programModular/program.js'
export default {
  components: {
    programToExamine
  },
  watch:{
    program:{ //监听的对象  this.programData指向program
        handler(){
          this.programData=program
        },
      immediate:true,
    }
  },
  data () {
    return {
      visible: false,
      programId: [],
      data: [],
      current: 0,
      loading: false,
      toExamineVisible: false,
      oneClickAudit: false,
    }
  },
  methods: {
    // 初始化
    init (id) {
    if (this.programId) {
        this.programId = id
      if (id.length>1){
        this.handleExamine()
      }else {
        var pid=id[0]
          this.$http({
            url: this.$http.adornUrl(`/screen/program/examine`),
            method: 'get',
            params: this.$http.adornParams({'programId': pid})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.data = data.examine
              this.visible = true
              if (this.data && this.data.length > 0) {
                // 如果存在已经处理过的流程
                var current = this.data.filter(item => item.status !== 0)
                if (current && current.length > 0) {
                  this.current = current.length === 1 ? 1 : current.length - 1
                }
                // 如果有未处理的流程
                var untreated = this.data.filter(item => item.status === 0)
                if (untreated && untreated.length > 0) {
                  this.oneClickAudit = true
                } else {
                  this.oneClickAudit = false
                }
              }
            }
          })
        }
    }

    },
    // 审核
    handleExamine (userId) {
      this.toExamineVisible = true
      this.$nextTick(() => {
        console.log(this.programId)
        // debugger

        // 传入审核人以及审核内容的ID
        this.$refs.mediaToExamine.init(userId, this.programId)
      })
    },
    // 查看详情
    handleView () {
      this.$http({
            url: this.$http.adornUrl(`/screen/program/info/${this.programId}`),
            method: 'get',
            data: this.$http.adornData()
        }).then(({data}) => {
            if (data && data.code === 0) {
              // console.log(data);
              this.$router.replace({ name: 'standard', params: {backRouter: 'screen-program'} })
              program.modifyAdvertisement(data.program, true)
            } else {
              this.$Message.error(data.msg)
            }
      })
    },
    // 关闭当前模块
    closeThisModal () {
      this.data = []
      this.current = 0
      this.visible = false
      this.oneClickAudit = false
      this.$emit('refreshDataList')
    }
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo }
    }
  }
}
</script>
