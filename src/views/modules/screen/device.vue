<!-- 智慧屏幕 -->
<template>
   <div class="modiles-device">
    <Form :inline="true" :model="dataForm" @keyup.enter.native="getDataList(null,1)">
      <FormItem>
        <Input size="large" v-model="dataForm.key" :placeholder="$t('cardDevice.deviceName') + '/ID'"></Input>
      </FormItem>
<!--        <Select size="large" v-model="dataForm.group" filterable clearable :placeholder="$t('common.PleaseSelect') + $t('group.name')" @on-change = "getDataList">-->
<!--          <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.name }} ({{item.cardCount}})</Option>-->
<!--        </Select>-->
      <FormItem>
        <Button style="margin-right:6px" @click="getDataList(null,1)"  size="large">
          <div style="margin:3px 8px">{{$t('common.query')}}</div>
        </Button>
        <Button style="margin-right:6px" size="large" @click="changeGroup()">
          <div style="margin:3px 8px">{{groupName != '' ? this.$t('common.selectingGroup')+":"+ groupName : $t('common.selectGroup')}}</div>
        </Button>
        <Button style="margin-right:6px" @click="getTerminalInfo()" :loading="terminalInfoLoading"  type="primary" size="large" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{$t('cardDevice.queryTerminalInfo')}}</div>
        </Button>
        <Button @click="groupHandle()" style="margin-right:6px"  type="primary" size="large" v-if="isAuth('device:group')" :disabled="dataListSelections.length <= 0">
          <div style="margin:3px 8px">{{$t('operation.group')}}</div>
        </Button>
        <Button v-if="isAuth('lampPole:device:delete')" size="large" type="error" :disabled="dataListSelections.length <= 0" @click="deleteHandle()">
          <div style="margin:3px 8px">{{$t('common.batchDel')}}</div>
        </Button>
      </FormItem>
    </Form>
     <u-table
       ref="plTable"
       :data="dataList"
       :height="tableHeightData"
       :data-changes-scroll-top="false"
       use-virtual
       :row-height="rowHeight"
       :pagination-show="true"
       :total="totalPage"
       :page-size="pageSize"
       :current-page="pageIndex"
       layout="total, sizes, prev, pager, next, jumper"
       :page-sizes="pageSizes"
       @handlePageSize="handlePageSize"
       @selection-change="selectionChangeHandle"
       @row-click="selectThisRow"
       @sort-change="onSortChange"
       border>
       <u-table-column
         title="123"
         type="selection"
         align="center"
         width="55">
       </u-table-column>
       <u-table-column  align="center"  type="index" :label="$t('cardDevice.number')" width="100" fixed/>
       <u-table-column  align="center" sortable="custom" prop="alias" :label="$t('cardDevice.deviceName')" width="130" fixed/>
       <u-table-column
         label="ID"
         key="1"
         prop="deviceId"
         fixed
         align="center"
         width="150">
         <template slot-scope="scope">
           {{ scope.row.deviceId }}
           <span v-if="scope.row.msg === 1">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#send"></use>
          </svg>
        </span>
           <span v-if="scope.row.msg === 2">
          <Poptip placement="right-start" v-if="scope.row.text && scope.row.text !== ''"
                  trigger="hover" transfer :title="$t('common.tips')" :content="tipChange(scope.row.text)">
            <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
              <use xlink:href="#fail"></use>
            </svg>
          </Poptip>
          <svg v-else width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#fail"></use>
          </svg>
        </span>
           <span v-if="scope.row.msg === 3">
          <svg width="15px" height="15px" aria-hidden="true" style="vertical-align:middle;">
            <use xlink:href="#success"></use>
          </svg>
        </span>
         </template>
       </u-table-column>
       <u-table-column
         :label="$t('cardDevice.online')"
         align="center"
         width="80">
         <template slot-scope="{ row, index }">
           <div>
             <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
               <use :xlink:href="row.isOn === 1 ? '#on-line' : '#line'"></use>
             </svg>
           </div>
         </template>
       </u-table-column>

       <u-table-column
         :label="$t('cardDevice.networkType')"
         prop="netType"
         key="5"
         align="center"
         width="120">
         <template slot-scope="scope">
            <div v-if="scope.row.netType === 'WIFI'">
               {{scope.row.netType}}
               <svg v-if="scope.row.rssi >= -50 && scope.row.rssi <= 0" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#WIFI-level4"></use>
               </svg>
               <svg v-else-if="scope.row.rssi >= -70 && scope.row.rssi < -50" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#WIFI-level3"></use>
               </svg>
               <svg  v-else-if="scope.row.rssi >= -80 && scope.row.rssi < -70" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#WIFI-level2"></use>
               </svg>
               <svg v-else-if="scope.row.rssi >= -100 && scope.row.rssi < -80" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#WIFI-level1"></use>
               </svg>
            </div>
            <div v-else-if="scope.row.netType === 'LTE' || scope.row.netType === 'UMTS' || scope.row.netType === 'HSPA'
            || scope.row.netType === 'HSPA+' || scope.row.netType === 'EDGE'  || scope.row.netType === 'GPRS'">
               {{scope.row.netType}}
               <svg v-if="scope.row.asu >= 12 && scope.row.asu != 99" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#gsm-0"></use>
               </svg>
               <svg v-else-if="scope.row.asu >= 8 && scope.row.asu  < 12" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#gsm-1"></use>
               </svg>
               <svg  v-else-if="scope.row.asu >= 5 && scope.row.asu < 8" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#gsm-2"></use>
               </svg>
               <svg v-else-if="scope.row.asu >= 3 && scope.row.asu < 5" style="vertical-align: middle;" width="24px" height="24px" aria-hidden="true">
                 <use xlink:href="#gsm-3"></use>
               </svg>
            </div>
            <div v-else>
              {{scope.row.netType}}
            </div>
         </template>
       </u-table-column>
       <u-table-column
         :label="$t('cardDevice.brightness')"
         align="center"
         width="120">
         <template slot-scope="scope">
           <div v-if="scope.row.brightnessPercentage && scope.row.brightnessPercentage != -1">{{scope.row.brightnessPercentage}}%</div>
           <div v-else>{{scope.row.brightness}}</div>
         </template>
       </u-table-column>
       <u-table-column
         :label="$t('cardDevice.resolvingPower')"
         align="center"
         width="120">
         <template slot-scope="scope">
           <div v-if="scope.row.width && scope.row.height">{{scope.row.width}} * {{scope.row.height}}</div>
           <div v-else></div>
         </template>
       </u-table-column>
       <u-table-column
         :label="$t('cardDevice.programTask')"
         align="center"
         width="120">
         <template slot-scope="scope">
           <div v-if="scope.row.currentProgramName && scope.row.currentProgramName=='暂无节目'">
             {{ $t('broadcast.noProgram')  }}
           </div>
           <div v-else>
             {{scope.row.currentProgramName}}
           </div>
         </template>
       </u-table-column>
       <u-table-column
         :label="$t('cardDevice.screenStatus')"
         align="center"
         width="120">
         <template slot-scope="scope">
            <div>
               <svg width="25px" height="25px" aria-hidden="true" style="vertical-align: middle;">
                 <use :xlink:href="scope.row.screenStatus === 'on' ? '#on' : '#off'"></use>
               </svg>
            </div>
         </template>
       </u-table-column>
       <u-table-column
         :label="$t('cardDevice.temperature')"
         align="center"
         width="120">
         <template slot-scope="scope">
            <div v-if="scope.row.temperature">
               {{scope.row.temperature}} ℃
            </div>
            <div v-else>{{scope.row.temperature}}</div>
         </template>
       </u-table-column>
       <u-table-column
         v-for="item in dataConlums"
         :key="item.id"
         :show-overflow-tooltip="item.tooltip"
         :prop="item.key"
         :label="item.title"
         :fixed="item.fixed"
         :align="item.align"
         :width="item.width">
       </u-table-column>
       <u-table-column
         fixed="right"
         align="center"
         :label="$t('common.operation')"
         key="22"
         width="200">
         <template slot-scope="scope">
           <Button v-if="isAuth('screen:card:info')" :disabled="scope.row._disabled" type="warning" size="small" style="margin-right: 5px;font-size: 11px" @click="logHandle(scope.row.deviceId)">{{$t('screen.log')}}</Button>
           <Button v-if="isAuth('screen:card:info')" :disabled="scope.row._disabled" type="info" size="small" style="margin-right: 5px;font-size: 11px" @click="infoHandle(scope.row.deviceId)">{{$t('common.info')}}</Button>
           <Button v-if="isAuth('screen:card:update')" :disabled="scope.row._disabled" type="primary" size="small" style="margin-right: 5px;font-size: 11px" @click="updateHandle(scope.row.deviceId)">{{$t('common.update')}}</Button>
           <Button v-if="isAuth('screen:card:delete')" type="error" size="small" style="font-size: 11px" @click="deleteHandle(scope.row.deviceId)">{{$t('common.delete')}}</Button>
         </template>
       </u-table-column>
     </u-table>
     <!--分组弹出框-->
     <Modal v-model="selectGroupVisible" width="500">
       <p slot="header" style="text-align:center">
         <span>{{$t('common.selectGroup')}}</span>
       </p>
       <Alert type="info" show-icon >
         <span>{{this.$t('tips.groupTip')}}</span>
       </Alert>
       <div>
         <Tree :data="groupList" :render="renderContent" style="height: 300px" ref="groupListTree"></Tree>
       </div>
       <div slot="footer">
         <Button size="large" @click="cancelSelect()">{{$t('common.cancel')}}</Button>
         <Button type="primary" size="large" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
       </div>
     </Modal>

    <!-- 功能菜单-->
    <div style="height: 20px; background:#eee; clear: both;margin-top: 5px;">
      <svg v-if="!isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuixiaohua"></use>
      </svg>
      <svg v-if="isMinimize" style="cursor: pointer;vertical-align:middle;" @click="handleMinimize(isMinimize)" width="20px" height="20px" aria-hidden="true">
        <use xlink:href="#zuidahua"></use>
      </svg>
    </div>
    <div v-if="!isMinimize" class="opera_div">
      <ul class="opera_ul">
        <div v-for="(item, index) in operation" :key="index">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.text)}}</div>
            </div>
            <div class="opera_list" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="45px" height="45px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
              <div class="opera_text">{{$t(item.text)}}</div>
            </div>
          </li>
        </div>
      </ul>
    </div>
    <div v-if="isMinimize" style="height: 50px;" class="opera_div">
      <ul class="opera_ul1">
        <div v-for="(item, index) in operation" :key="index" :title="$t(item.text)">
          <li v-if="isAuth(item.auth)">
            <div class="opera_list1" v-if="!item.disable" @click="operaSuccessHandle(item.id, item.checked)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
            <div class="opera_list1" style="cursor: not-allowed;" v-else @click="operaErrorHandle(item.text)">
              <svg width="30px" height="30px" aria-hidden="true">
                <use :xlink:href="'#' + item.icon"></use>
              </svg>
            </div>
          </li>
        </div>
      </ul>
    </div>

    <!-- 弹窗 -->
    <!-- 详情-->
    <device-info v-if="infoVisible" ref="deviceInfo"></device-info>
    <!-- 截屏-->
    <device-screenshot v-if="screenshotVisible" ref="deviceScreenshot"></device-screenshot>
    <!-- 修改单人 子组件调用父组件方法，修改完刷新-->
    <device-update-one v-if="updateOne" ref="deviceUpdateOne" @refreshDataList="getDataList"></device-update-one>
    <!-- 屏幕开关-->
    <device-screen-switch v-if="screenSwitchVisible" ref="deviceScreenSwitch" @refreshDataList="getDataList"></device-screen-switch>
    <!-- 屏幕亮度-->
    <device-screen-brightness v-if="screenBrightnessVisible" ref="deviceScreenBrightness" @refreshDataList="getDataList"></device-screen-brightness>
    <!-- 音量-->
    <device-volume v-if="volumeVisible" ref="deviceVolume" @refreshDataList="getDataList"></device-volume>
    <!-- 重启系统-->
    <device-restart v-if="restartVisible" ref="deviceRestart" @refreshDataList="getDataList"></device-restart>
    <!-- 在线升级 -->
    <device-online-upgrade v-if="onlineUpgrade" ref="deviceOnlineUpgrade"></device-online-upgrade>
    <!-- 视频直播-->
    <device-live-video v-if="liveVideoVisible" ref="deviceLiveVideo"></device-live-video>
    <!-- 对时配置 -->
    <device-setTime v-if="timingConfig" ref="deviceSetTime"></device-setTime>
    <!-- 同异步配置 -->
    <device-synchronous v-if="synchronous" ref="deviceSynchronous"></device-synchronous>
    <!-- 高级配置 -->
    <device-senior v-if="advancedConfig" ref="deviceSenior"></device-senior>
    <!-- 硬件状态 -->
    <device-hardware v-if="hardwareStatus" ref="deviceHardware"></device-hardware>
    <!-- 播放器背景 -->
    <device-background v-if="screenBackground" ref="deviceBackground"></device-background>
    <!-- 报警开关 -->
    <device-police  v-if="police" ref="devicePolice"></device-police>
    <!-- 导出SIM信息 -->
    <device-exportSim  v-if="exportSim" ref="deviceExportSim"></device-exportSim>
    <!-- 节目任务详情 -->
    <device-viewProgram  v-if="viewProgram" ref="deviceViewProgram"></device-viewProgram>
    <!-- 清除节目 -->
    <device-clearProgram  v-if="clearProgram" ref="clearProgram"></device-clearProgram>
    <!--gsp信息-->
    <device-gps-info v-if="gpsInfo" ref="gpsInfo"></device-gps-info>
    <!--定制开关-->
    <device-custom-switch v-if="customSwitchVisible" ref="customSwitch"></device-custom-switch>
    <!-- 分组 -->
    <device-group v-if="groupVisible" ref="deviceGroup" @refreshDataList="getDataList"></device-group>
    <!--监控-->
    <!-- <device-monitor v-if="monitorVisible" ref="monitor"></device-monitor> -->
    <!--开放第三方平台广告-->
<!--    <device-config-ad v-if="configAdVisible" ref="configAd"></device-config-ad>-->
    <!--播放日志-->
     <device-play-log v-if="logVisible" ref="playLog"></device-play-log>
    <!--坏点检测-->
     <device-point-check v-if="pointCheckVisible" ref="pointCheck"></device-point-check>
    <!--播放器状态-->
     <device-player-state v-if="playerStateVisible" ref="playerState"></device-player-state>
<!--     接入第三方平台广告-->
     <device-get-third-party-ad v-if="thirdPartyAdVisible" ref="thirdPartyAd"></device-get-third-party-ad>
     <!--传感器数据共享-->
     <device-sensor-data-share v-if="sensorDataShareVisible" ref="sensorDataShare"></device-sensor-data-share>
    <!--当前人流量-->
     <device-current-flow v-if="currentFlowVisible" ref="currentFlow"></device-current-flow>

    <!--网络配置-->
     <device-network-config v-if="networkConfigVisible" ref="networkConfig"></device-network-config>

    <!--一键开关屏-->
     <device-online-screen-status v-if="deviceOnlineScreenStatusVisible" ref="deviceOnlineScreenStatus" @refreshDataList="getDataList"></device-online-screen-status>

     <device-system-display v-if="systemDisPlayVisible" ref="systemDisplay" @refreshDataList="getDataList"></device-system-display>

     <device-check-address v-if="checkAddressVisible" ref="checkAddress"></device-check-address>

     <device-human-number-statistic-v-1 v-if="humanNumberStatisticV1Visible" ref="humanNumberStatisticV1"></device-human-number-statistic-v-1>
  </div>
</template>

<script>
import deviceInfo from './device-info'
import deviceScreenshot from './device/device-screenshot'
import deviceScreenSwitch from './device/device-screen-switch'
import deviceScreenBrightness from './device/device-screen-brightness'
import deviceVolume from './device/device-volume'
import deviceRestart from './device/device-restart'
import deviceOnlineUpgrade from './device/device-online-upgrade.vue'
import deviceLiveVideo from './device/device-live-video'
import deviceSetTime from './device/device-setTime'
import deviceSynchronous from  './device/device-synchronous.vue'
import deviceSenior from './device/device-senior'
import deviceHardware from './device/device-hardware'
import deviceUpdateOne from './device/device-update-one'
import deviceBackground from './device/device-background'
import devicePolice from './device/device-police.vue'
import deviceExportSim from './device/device-exportSim'
import deviceViewProgram from './device/device-view-program.vue'
import deviceClearProgram from './device/device-clear-program.vue'
import deviceGpsInfo from "./device/device-gps-info";
import deviceGroup from '../lampPole/device-group.vue'
import deviceCustomSwitch from "./device/device-custom-switch";
// import deviceMonitor from "./device/device-monitor";
// import deviceConfigAd from "./device/device-config-ad";
import devicePlayLog from "./device/device-play-log";
import devicePointCheck from "./device/device-point-check";
import devicePlayerState from "./device/device-player-state";
import DeviceGetThirdPartyAd from "./device/device-get-third-party-ad";
import DeviceSensorDataShare from "./device/device-sensor-data-share";
import DeviceCurrentFlow from "./device/device-current-flow.vue";
import DeviceNetworkConfig from "./device/device-network-config.vue";
import deviceOnlineScreenStatus from "./device/device-online-screen-status.vue";
import deviceSystemDisplay from "./device/device-system-display.vue";
import DeviceCheckAddress from "./device/device-check-address.vue";
import deviceHumanNumberStatisticV1 from "./device/device-human-number-statistic-v1.vue";
export default {
  data () {
    return {
      height: 0,
      rowHeight: 55,
      dialogVisible: true,
      dataForm: {
        key: '',
        group: [],
        shortcutKey: []
      },
      dataConlums: [
        // {id:3,title: this.$t('cardDevice.deviceName'), key: 'alias', width: 130, align: 'center', tooltip: true,
        //   renderHeader:(h)=>{
        //     return h('div',this.$t('cardDevice.deviceName'))
        //   }
        // },
        // {id:7,title: this.$t('cardDevice.programTask'), key: 'currentProgramName', width: 130, align: 'center', tooltip: true,slot: 'currentProgramName' ,
        //   renderHeader:(h)=>{
        //     return h('div',this.$t('cardDevice.programTask'))
        //   }
        // },
        {id:8,title: this.$t('cardDevice.brightness'), key: 'brightness', width: 115, align: 'center', slot: 'brightness',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.brightness'))
          }
        },
        {id:9,title: this.$t('cardDevice.volume'), key: 'volume', width: 90, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.volume'))
          }
        },
        // {id:10,title: this.$t('cardDevice.screenStatus'), key: 'screenStatus', width: 135, align: 'center', slot: 'screenStatus',
        //   renderHeader:(h)=>{
        //     return h('div',this.$t('cardDevice.screenStatus'))
        //   }
        // },
        {id:11,title: this.$t('cardDevice.temperature'), key: 'temperature', width: 125, align: 'center', slot: 'temperature',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.temperature'))
          }
        },
        {id:12,title: this.$t('cardDevice.fireWare'), key: 'fireware',width: 245, align: 'center', tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.fireWare'))
          }
        },
        {id:13,title: 'CardSystem', key: 'ledsetVersion',width: 130, align: 'center', tooltip: true},
        {id:14,title: 'Conn', key: 'connVersion',width: 130, align: 'center', tooltip: true},
        {id:15,title: 'Player', key: 'playerVersion',width: 130, align: 'center', tooltip: true},
        {id:16,title: 'Starter', key: 'starterVersion',width: 140, align: 'center', tooltip: true},
        {id:17,title: 'Display', key: 'displayVersion',width: 140, align: 'center', tooltip: true},
        {id:18,title: this.$t('operation.group'), key: 'groupName', align: 'center', width: 100, tooltip: true,
          renderHeader:(h)=>{
            return h('div',this.$t('operation.group'))
          }
        },
        /* {id:19,title: "IP", key: 'realIp', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div','IP')
          }
        }, */
        {id:20,title: this.$t('cardDevice.lastOffline'), key: 'lastOffTime', width: 170, align: 'center',
          renderHeader:(h)=>{
            return h('div',this.$t('cardDevice.lastOffline'))
          }
        },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      groupList: [],
      infoVisible: false,
      updateOne: false,
      operation: [
        {id: 'screenshot', icon: 'cut', text: 'operation.screenshot', disable: false,auth: 'device:screenshot', checked: true},
        {id: 'clearProgram', icon: 'clear', text: 'operation.clearProgram', disable: false,auth: 'device:clearProgram', checked: true},
        {id: 'screenSwitch', icon: 'kaiguan', text: 'operation.screenSwitch', disable: false,auth: 'device:screenSwitch', checked: true},
        {id: 'screenBrightness', icon: 'liangdu', text: 'operation.screenBrightness', disable: false,auth: 'device:screenBrightness', checked: true},
        {id: 'volumeControl', icon: 'yinliang', text: 'operation.volumeControl', disable: false,auth: 'device:volumeControl', checked: true},
        {id: 'restartSys', icon: 'ziyuanxhdpi', text: 'operation.restartSys', disable: false,auth: 'device:restartSys', checked: true},
        {id: 'exportSim', icon: 'export', text: 'operation.exportSIMInfo', disable: false,auth: 'device:exportSim', checked: false},
        {id: 'onlineUpdate', icon: 'zhongduanzaixianshengji', text: 'operation.onlineUpdate', disable: false,auth: 'device:onlineUpdate', checked: true},
        {id: 'liveVideo', icon: 'shipinzhibo', text: 'operation.liveVideo', disable: false,auth: 'device:liveVideo', checked: true},
        {id: 'timingConfig', icon: 'shijian', text: 'operation.timingConfig', disable: false,auth: 'device:timingConfig', checked: true},
        {id: 'syncAndAsyncConfig', icon: 'yibu', text: 'operation.syncAndAsyncConfig', disable: false,auth: 'device:syncAndAsyncConfig', checked: true},
        {id: 'alarmSwitch', icon: 'baojing', text: 'operation.alarmSwitch', disable: false,auth: 'device:alarmSwitch', checked: true},
        {id: 'advancedConfig', icon: 'gaojishezhi', text: 'operation.connConfig', disable: false,auth: 'device:advancedConfig', checked: true},
        {id: 'backgroundPlayback', icon: 'background', text: 'operation.backgroundPlayback', disable: false,auth: 'device:backgroundPlayback', checked: true},
        {id: 'hardwareStatus', icon: 'yingjian', text: 'operation.hardwareStatus', disable: false,auth: 'device:hardwareStatus', checked: true},
        {id: 'gpsInfo', icon: 'gps', text: 'operation.gpsInfo', disable: false,auth: 'device:gpsInfo', checked: true},
        {id: 'customSwitch', icon: 'customSwitch', text: 'operation.customSwitch', disable: false,auth: 'device:customSwitch', checked: true},
        {id: 'monitor', icon: 'monitorAddress', text: 'bigScreen.VideoSurveillance', disable: false,auth: 'device:videoSurveillance', checked: true},
        {id: 'pointCheck', icon: 'pointCheck', text: 'operation.BadPointDetection', disable: false,auth: 'device:pointCheck', checked: true},
        {id: 'playerState', icon: 'playerState', text: 'operation.playerState', disable: false,auth: 'device:playerState', checked: true},
        {id: 'thirdPartyAd', icon: 'adConfig', text: 'operation.ThirdPartyAdvertising', disable: false,auth: 'device:ThirdPartyAdvertising', checked: true},
        {id: 'sensorDataShare', icon: 'dataShare', text: 'operation.sensorDataShare', disable: false,auth: 'device:sensorDataShare', checked: true},
        {id: 'currentFlow', icon: 'curFlow', text: 'operation.curFlow', disable: false,auth: 'device:curFlow', checked: true},
        {id: 'networkConfig', icon: 'networkConfig', text: 'screen.networkConfig', disable: false,auth: 'device:networkConfig', checked: true},
        {id: 'deviceOnlineScreenStatus', icon: 'batchOperate', text: 'operation.oneClickOperateScreen', disable: false,auth: 'device:allOnlineDeviceScreen', checked: false},
        {id: 'systemDisplay', icon: 'systemDisplay', text: 'operation.systemDisplay', disable: false,auth: 'device:systemDisplay', checked: true},
        {id: 'checkAddress', icon: 'checkAddress', text: 'operation.checkAddress', disable: false,auth: 'device:checkAddress', checked: true},
        {id: 'humanNumberStatisticV1', icon: 'passengerFlow', text: 'nav.客流统计V1', disable: false,auth: 'humanNumberStatistic:deviceV1', checked: true},
        {id: 'cameraMonitoring', icon: 'cameraMonitoring', text: 'nav.摄像头监控', disable: false,auth: 'device:cameraMonitoring', checked: true}
        // {id: 'adConfig', icon: 'adConfig', text: 'operation.ThirdPartyAdvertising', disable: false,auth: 'device:ThirdPartyAdvertising', checked: true}
      ],
      screenshotVisible: false,
      screenSwitchVisible: false,
      screenBrightnessVisible: false,
      volumeVisible: false,
      restartVisible: false,
      liveVideoVisible: false,
      timingConfig: false,
      synchronous: false,
      advancedConfig: false,
      hardwareStatus: false,
      onlineUpgrade: false,
      screenBackground: false,
      police: false,
      terminalInfoLoading: false,
      exportSim: false,
      viewProgram: false,
      clearProgram: false,
      gpsInfo: false,
      groupVisible: false,
      //选择分组时，分组框是否可见
      selectGroupVisible:false,
    //  分组名
      groupName:"",
      rootNode:null,
      //设备总数
      totalNum:0,
      //未分组设备数
      unGroupNum:0,
      //是否为第一次进入该页面
      isFirst:true,
      //定制开关
      customSwitchVisible:false,
      //视频监控
      monitorVisible:false,
      //广告
      // configAdVisible:false,
      isMinimize: false,
      tableHeightData: 0,
      logVisible:false,
      //坏点检测
      pointCheckVisible:false,
      //播放器状态
      playerStateVisible:false,
      //接入第三方广告
      thirdPartyAdVisible:false,
      //传感器数据共享
      sensorDataShareVisible:false,
      // 当前人流量数据
      currentFlowVisible: false,
      // 网络配置
      networkConfigVisible:false,
      // 一键开关屏
      deviceOnlineScreenStatusVisible:false,
      // 系统分辨率
      systemDisPlayVisible:false,
      // 地址检测
      checkAddressVisible:false,
      // 客流统计V1
      humanNumberStatisticV1Visible:false,
      pageSizes:[10,30,50,100,300,500],
      orderByList:[],
    }
  },
  components: {
    DeviceNetworkConfig,
    DeviceCurrentFlow,
    DeviceSensorDataShare,
    DeviceGetThirdPartyAd,
    deviceInfo,
    deviceScreenshot,
    deviceScreenSwitch,
    deviceScreenBrightness,
    deviceVolume,
    deviceRestart,
    deviceLiveVideo,
    deviceSetTime,
    deviceSynchronous,
    deviceSenior,
    deviceHardware,
    deviceUpdateOne,
    deviceOnlineUpgrade,
    deviceBackground,
    devicePolice,
    deviceExportSim,
    deviceViewProgram,
    deviceClearProgram,
    deviceGpsInfo,
    deviceGroup,
    deviceCustomSwitch,
    // deviceMonitor,
    // deviceConfigAd,
    devicePlayLog,
    devicePointCheck,
    devicePlayerState,
    deviceOnlineScreenStatus,
    deviceSystemDisplay,
    DeviceCheckAddress,
    deviceHumanNumberStatisticV1
  },
  activated () {
    this.initData()
    this.getDataList('loading',0)
  },
  methods: {
    onSortChange({ column, prop, order }) {
      if (order != null) {
        const idMap = new Map();
        this.orderByList.forEach(item => {
          idMap.set(item.prop, item);
        });
        if (idMap.has(prop)) {
          const existingItem = idMap.get(prop);
          // 更新属性值
          Object.assign(existingItem, {prop,order});
        } else {
          // 如果不存在，则添加新的对象
          this.orderByList.push({prop,order});
        }
      } else {
        const index = this.orderByList.findIndex(item => item.prop === prop);
        if (index !== -1) {
          this.orderByList.splice(index, 1);
        }
      }
      this.getDataList('loading',1)
    },
    handlePageSize ({page, size}) {
      this.pageSize = size
      this.pageIndex = page
      this.$refs.plTable.selectAll(false)
      this.getDataList()
    },
    // 初始化部分数据
    initData () {
      this.dataForm = {
        key: '',
        group: []
      }
      this.dataList = []
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataListLoading = false
      this.dataListSelections = []
      this.groupName = ""
      this.rootNode=null
      this.isFirst=true
      this.terminalInfoLoading= false
      this.tableHeightData = 0
      this.isMinimize = false;
      setTimeout(() => {
        this.tableHeightData = this.tableHeight + 70
      }, 10)
    },
    updateHandle (deviceId) {
      this.updateOne = true // 修改
      this.$nextTick(() => {
        this.$refs.deviceUpdateOne.init(deviceId,this.totalNum)
      })
    },
    // 查询分组列表
    getGroupList () {
      this.$http({
        url: this.$http.adornUrl('/sys/group/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.groupList = data.group
          this.getUnGroupDevice()
        } else {
          this.groupList = []
        }
      })
    },
    // 获取数据列表
    getDataList(loading,isQuery) {
      if (loading) {
        this.dataListLoading = true
      }
      //使用查询时，将页码重置为1
      if (isQuery === 1){
        this.pageIndex=1;
      }
      this.$http({
        url: this.$http.adornUrl('/screen/card/list'),
        method: 'post',
        data: this.$http.adornData({
          'page': this.pageIndex+"",
          'limit': this.pageSize+"",
          'key': this.dataForm.key,
          'group': this.dataForm.group,
          'orderBy':this.orderByList
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          if (this.isFirst){
            this.totalNum=data.page.totalCount
            this.isFirst=false
          }
          if (this.dataForm.group.length===0){
            this.groupName= ""
          }
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage>this.totalPage&&this.totalPage!==0){
            this.pageIndex=1
            this.getDataList(loading,isQuery)
          }
          // this.dataForm.group=[]
          // 设置选中
          // var select = this.$refs.selection.getSelection().map(item => {return item.deviceId})
          var select =this.dataListSelections.map(item => item.deviceId);
          if (select && select.length !== 0) {
            this.dataList.map(item => {
              if (select.indexOf(item.deviceId) != -1) {
                item._checked = true
              } else {
                item._checked = false
              }
            })
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.$refs.selection.selectAll(false)
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(selection) {
      this.dataListSelections = selection;
    },
    selectThisRow(row, event, column) {
      this.$refs.plTable.toggleRowSelection( [{ row: row, selected: true }]);
    },
    // 查询终端信息
    getTerminalInfo () {
      // 判断选中的列表不为空
      if (this.dataListSelections.length > 0) {
        var deviceIds = this.dataListSelections.map(item => {
          return item.deviceId
        })
        this.dataList = this.dataList.filter(data => {
            if (deviceIds.indexOf(data.deviceId) !== -1) {
              data.msg =  1
              data._checked = true
            } else {
              data._checked = false
            }
            return data
        })
        this.terminalInfoLoading = true
        deviceIds.forEach(item => {
          this.$http({
            url: this.$http.adornUrl('/card/query/getCardInformation'),
            method: 'post',
            data: [item]
          }).then(({ data }) => {
            if (data && data.cards) {
              for (let i = 0; i < data.cards.length; i++) {
                const element = data.cards[i];
                if (element.card.text) {
                  element.card.msg = 2
                } else {
                  element.card.msg = 3
                }
                element.card._checked = true
                this.dataList = this.dataList.map(item => {
                    if (element.card.deviceId.indexOf(item.deviceId) !== -1) {
                        // 使用新对象保持响应式更新
                        return { ...element.card }
                    }
                    return item
                })
                this.$set(this, 'dataList', [...this.dataList]) // 保持数组引用稳定
              }
            } else {
              // this.$Message.error(data.msg)
            }
          });
        })
        this.terminalInfoLoading = false
      }
    },
    // 查询详情
    infoHandle (id) {
      this.infoVisible = true
      this.$nextTick(() => {
        this.$refs.deviceInfo.init(id)
      })
    },
    // 日志详情
    logHandle (id) {
      this.logVisible = true
      this.$nextTick(() => {
        this.$refs.playLog.init(id)
      })
    },
    // 操作处理
    // 不支持
    operaErrorHandle (text) {
      this.$Message.warning({
        content: this.$t('common.supportedTip') + this.$t(text),
        duration: 2
      })
    },
    // 删除
    deleteHandle (id) {
      var deviceIds = id ? [id] : this.dataListSelections.map(item => {
        return item.deviceId
      })
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.delete_current_option'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/lampPole/card/delete'),
            method: 'post',
            data: this.$http.adornData(deviceIds, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$Message.success({
                content: this.$t('common.operationSuccessful'),
                duration: 0.5,
                onClose: () => {
                  if (this.pageIndex != 1 && this.dataList.length === deviceIds.length) {
                    this.pageIndex--
                  }
                  this.getDataList()
                  this.dataListSelections = []
                }
              })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 分组
    groupHandle (id) {
      var deviceIds = id ? [id] : this.dataListSelections.map(item => {
        return item.deviceId
      })
      this.groupVisible = true
      this.$nextTick(() => {
        this.$refs.deviceGroup.init(deviceIds,this.totalNum)
      })
    },
    // 成功
    operaSuccessHandle (id, checked) {
      if (id) {
        if (checked === true) {
          // 获取已选的卡
          var deviceIds = null
          if (this.dataListSelections.length > 0 || id === 'onlineUpdate' || id === "hardwareStatus") {
            deviceIds = this.dataListSelections.map(item => {
              return item.deviceId
            })
          } else {
            this.$Message.warning({
              content: this.$t('common.selectDevice'),
              duration: 2
            })
            return
          }
          if (deviceIds.length >= 1 || id === 'onlineUpdate' || id ==="hardwareStatus") {
            if (id === 'screenshot') { // 屏幕截图
              this.screenshotVisible = true
              this.$nextTick(() => {
                this.$refs.deviceScreenshot.init(deviceIds)
              })
            } else if (id === 'screenSwitch') { // 屏幕开关
              this.screenSwitchVisible = true
              this.$nextTick(() => {
                this.$refs.deviceScreenSwitch.init(deviceIds)
              })
            } else if (id === 'screenBrightness') { // 屏幕亮度
              this.screenBrightnessVisible = true
              this.$nextTick(() => {
                this.$refs.deviceScreenBrightness.init(deviceIds)
              })
            } else if (id === 'volumeControl') { // 音量
              this.volumeVisible = true
              this.$nextTick(() => {
                this.$refs.deviceVolume.init(deviceIds)
              })
            } else if (id === 'restartSys') { // 重启设备
              this.restartVisible = true
              this.$nextTick(() => {
                this.$refs.deviceRestart.init(deviceIds)
              })
            } else if (id === 'group') { // 分组管理
              this.groupVisible = true
              this.$nextTick(() => {
                this.$refs.deviceGroup.init(deviceIds)
              })
            } else if (id === 'onlineUpdate') { // 在线升级
              this.onlineUpgrade = true
              this.$nextTick(() => {
                this.$refs.deviceOnlineUpgrade.init(deviceIds)
              })
            } else if (id === 'liveVideo') { // 视频直播
              this.liveVideoVisible = true
              this.$nextTick(() => {
                this.$refs.deviceLiveVideo.init(deviceIds)
              })
            } else if (id === 'timingConfig') { // 对时配置
              this.timingConfig = true
              this.$nextTick(() => {
                this.$refs.deviceSetTime.init(deviceIds)
              })
            } else if (id === 'syncAndAsyncConfig') { // 同异步配置
              this.synchronous = true
              this.$nextTick(() => {
                this.$refs.deviceSynchronous.init(deviceIds)
              })
            } else if (id === 'advancedConfig') { // 高级配置
              this.advancedConfig = true
              this.$nextTick(() => {
                this.$refs.deviceSenior.init(deviceIds)
              })
            } else if (id === 'backgroundPlayback') { // 播放器背景
              this.screenBackground = true
              this.$nextTick(() => {
                this.$refs.deviceBackground.init(deviceIds)
              })
            } else if (id === 'hardwareStatus') {// 硬件状态
              this.hardwareStatus = true
              this.$nextTick(() => {
                this.$refs.deviceHardware.init(deviceIds)
              })
            } else if (id === 'alarmSwitch') { // 报警开关
              this.police = true
              this.$nextTick(() => {
                this.$refs.devicePolice.init(deviceIds)
              })
            } else if (id === 'clearProgram') {// 清除节目
              this.clearProgram = true
              this.$nextTick(() => {
                this.$refs.clearProgram.init(deviceIds)
              })
            }else if (id === 'gpsInfo') {// 查询gps信息
              this.gpsInfo = true
              this.$nextTick(() => {
                this.$refs.gpsInfo.init(deviceIds)
              })
            }else if (id === 'customSwitch') {// 定制开关
              this.customSwitchVisible = true
              this.$nextTick(() => {
                this.$refs.customSwitch.init(deviceIds)
              })
            } else if (id === 'monitor') {// 视频监控
              if (deviceIds.length > 6){
                this.$Message.error(this.$t('card.monitorTip'))
              }else {
                let routeUrl = this.$router.resolve({
                  path: '/videoSurveillance',
                  query:{
                    'deviceIds':JSON.stringify(deviceIds)
                  }
                })
                window.open(routeUrl.href, '_blank');
                // deviceMonitor.postMessage("test",'*')
              }
            } else if (id === 'cameraMonitoring') {// 摄像头监控
              if (deviceIds.length > 6){
                this.$Message.error(this.$t('card.monitorTip'))
              }else {
                let routeUrl = this.$router.resolve({
                  path: '/cameraMonitoring',
                  query:{
                    deviceIds: JSON.stringify(deviceIds)
                  }
                })
                window.open(routeUrl.href, '_blank');
                // deviceMonitor.postMessage("test",'*')
              }
            }
            // else if (id==='adConfig'){
            //   this.configAdVisible=true
            //   this.$nextTick(()=>{
            //     this.$refs.configAd.init(deviceIds)
            //   })
            // }
            else if (id==='pointCheck'){
              if (deviceIds.length > 1){
                this.$Message.error(this.$t('screen.pointCheckTips'))
              }else {
                this.pointCheckVisible=true
                this.$nextTick(()=>{
                  this.$refs.pointCheck.init(deviceIds)
                })
              }
            }else if (id==='playerState'){
              this.playerStateVisible=true
              this.$nextTick(()=>{
                this.$refs.playerState.init(deviceIds)
              })
            }else if (id==='thirdPartyAd'){
              this.thirdPartyAdVisible=true
              this.$nextTick(()=>{
                this.$refs.thirdPartyAd.init(deviceIds)
              })
            }else if (id==='sensorDataShare'){
              if (deviceIds.length > 1){
                this.$Message.error(this.$t('screen.sensorDataShareTips'))
              }else {
                this.sensorDataShareVisible=true
                this.$nextTick(()=>{
                  this.$refs.sensorDataShare.init(deviceIds)
                })
              }
            }else if(id === "currentFlow"){
              this.currentFlowVisible=true
              this.$nextTick(()=>{
                this.$refs.currentFlow.init(deviceIds)
              })
            }else if(id === "networkConfig") {
              if (deviceIds.length > 1) {
                this.$Message.error(this.$t('screen.singleCardOperation'))
              } else {
                this.networkConfigVisible = true
                this.$nextTick(() => {
                  this.$refs.networkConfig.init(deviceIds)
                })
              }
            } else if(id === "systemDisplay"){
              this.systemDisPlayVisible=true
              this.$nextTick(()=>{
                this.$refs.systemDisplay.init(deviceIds)
              })
            } else if(id === "checkAddress"){
              this.checkAddressVisible=true
              this.$nextTick(()=>{
                this.$refs.checkAddress.init(deviceIds)
              })
            } else if(id === "humanNumberStatisticV1"){
              this.humanNumberStatisticV1Visible=true
              this.$nextTick(()=>{
                this.$refs.humanNumberStatisticV1.init(deviceIds)
              })
            }
          }
        } else if(checked === false) {
          // 导出SIM卡信息不需要选中卡
          if (id === 'exportSim') {
            this.exportSim = true // 导出SIM信息
            this.$nextTick(() => {
              this.$refs.deviceExportSim.init(this.totalNum)
            })
          }else if(id === "deviceOnlineScreenStatus") {
            this.deviceOnlineScreenStatusVisible = true
            this.$nextTick(() => {
              this.$refs.deviceOnlineScreenStatus.init()
            })
          }
        }
      }
    },
    changeGroup(){

      this.selectGroupVisible=true
      this.getGroupList()
    },
    // 表单提交
    dataFormSubmit () {
      this.dataForm.group=[]
      this.rootNode =this.$refs.groupListTree.getSelectedNodes()[0]
      this.getChildrenNodes(this.rootNode)
      this.selectGroupVisible=false
      this.getDataList('loading',1)
      this.groupName= this.rootNode.name
      this.rootNode=null
    },
    //取消选择
    cancelSelect(){
      this.selectGroupVisible=false
      this.dataForm.group=[]
      this.groupName= ""
      this.rootNode=null
      this.getDataList()
      this.getGroupList()

    },
    //获取该分组及其子分组的groupId
    getChildrenNodes(rootNode){
      this.dataForm.group.push(rootNode.id)
      var childNode=rootNode.children;
      if (childNode){
        for (var i=0; i<childNode.length; i++) {
          this.getChildrenNodes(childNode[i])
        }
      }
    },
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        }
      }, [
        h('span', [
          h('span', data.name+"("+data.count+")")
        ])
      ]);
    },
    //获取未分组的设备
    getUnGroupDevice(){
      var groupedNum=0;
      this.unGroupNum=0;
      this.groupList.map(item=>{
        groupedNum+=item.count;
      })
      this.unGroupNum=this.totalNum-groupedNum;
      var unGroupObj={
        "id":-1,
        "name": this.$t('common.unclassified'),
        "count":this.unGroupNum,
        "children":[],
        "expand":true
      }
      this.groupList.push(unGroupObj)
    },
    //tip国际化
    tipChange(tip){
      if (tip=="控制卡连接已断开"){
        return this.$t('monitor.offLineOrNotExist')
      }else {
        return this.$t('log.connectionClosed')
      }
    },

    viewProgramTask (name) {
      this.viewProgram = true
      this.$nextTick(() => {
        this.$refs.deviceViewProgram.init(name)
      })
    },
    // 最大化最小化
    handleMinimize (isMinimize) {
      this.isMinimize = !isMinimize
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 70 + 130
      } else {
        this.tableHeightData = this.tableHeight + 70
      }
    },
  },
  computed: {
    deviceOnlineScreenStatus() {
      return deviceOnlineScreenStatus
    },
    tableHeight: {
      get () {
        if (this.$store.state.common.tableHeight > 155) {
          return this.$store.state.common.tableHeight - 155
        }
        return window.innerHeight - 279
      },
    }
  },
  mounted () {
    this.height = 600 // 动态设置高度
  },
  // 监听窗户高度变化，假如这个是我某个页面使用了表格
  watch: {
    'totalNum': function (newVal, OldVal) {
      this.getGroupList();
    },
    'tableHeight': function(newVal, oldVal) {
      if (this.isMinimize == true) {
        this.tableHeightData = this.tableHeight + 70 + 130
      } else {
        this.tableHeightData = this.tableHeight + 70
      }
    }
  },
}
</script>
<style scoped>
.opera_div {
  border-radius: 1%;
  clear: both;
  height: 180px;
  background:#eee;
  overflow: hidden;
  overflow-y: auto;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul li{
  text-align: center;
  float: left;
  list-style: none;
  width: 120px;
  height: 100px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list {
  padding-top: 10px;
  width: 105px;
  height: 105px;
  margin-left: 8%;
}
.opera_ul li:hover {
  background-color: rgb(210, 174, 245);
  border-radius: 3%;
  cursor: pointer;
}
.opera_text {
  color: rgb(99, 100, 100);
  font-size: 14px;
}
.opera_ul1 {
  margin: 0 auto;
  overflow: hidden;
}
.opera_ul1 li{
  text-align: center;
  float: left;
  list-style: none;
  width: 40px;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  word-break: break-word;
}
.opera_list1 {
  padding-top: 5px;
  width: 40px;
  height: 40px;
}
.opera_ul1 li:hover {
  background-color: rgb(210, 174, 245);
  cursor: pointer;
}
.load-more {
  float: none;
  font-size: 17px;
  margin: 0 auto;
  cursor: pointer;
  width: 900px;
  text-align: center;
}
.load-more:hover {
  background-color: rgb(158, 158, 158);
  color: #fff;
}
</style>
