<template>
  <div>
      <Modal height="400" v-model="visible" width="900">
            <p slot="header" style="text-align:center">
                <span>{{$t("police.materialLibrary")}}</span>
            </p>
            <Form :inline="true" :model="dataForm" @keyup.enter.native="init()">
              <FormItem>
                <Input size="large" v-model="dataForm.name" :placeholder="$t('file.name')"></Input>
              </FormItem>
              <FormItem>
                <Select size="large" v-model="dataForm.suffix" filterable clearable
                  :placeholder="$t('common.PleaseSelect') + $t('file.type')">
                  <Option v-for="item in fileType" :value="item.suffix" :key="item.suffix">{{ item.suffix }}</Option>
                </Select>
              </FormItem>
              <FormItem>
                <Button style="margin-right: 6px" @click="init()" size="large">
                  <div style="margin: 3px 8px">{{ $t("common.query") }}</div>
                </Button>
              </FormItem>
            </Form>
            <div style="height:400px;">
                <Table
                    stripe
                    :columns="dataConlums"
                    :data="dataList"
                    :loading="dataListLoading"
                    >
                    <template slot-scope="{ row }" slot="select">
                        <Button type="success" @click="selectId(row)"><Icon type="md-add" /></Button>
                    </template>
                    <template slot-scope="{ row }" slot="filePath">
                        <div v-if="row.suffix === 'mp4'">
                          <svg
                            width="50px"
                            height="50px"
                            style="margin-top: 5px"
                            aria-hidden="true"
                          >
                            <use xlink:href="#video"></use>
                          </svg>
                        </div>
                        <div v-else-if="row.suffix === 'mp3'">
                          <div>
                          <img
                            src="@/assets/img/audio.png"
                            style="margin-top: 5px"
                            height="50px"
                            width="50px"
                          />
                          </div>
                        </div>
                        <div v-else>
                          <img :src="downloadUrl + row.fileId" style="margin-top: 5px" height="50px" width="50px"/>
                        </div>
                    </template>
                    <template slot-scope="{ row }" slot="fileSize">
                        <span>{{row.fileSize | filterType}}</span>
                    </template>
                </Table>
                <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
                    show-elevator show-sizer :page-size-opts="[5]" show-total
                    @on-change="currentChangeHandle" @on-page-size-change="sizeChangeHandle"/>
            </div>
            <div slot="footer"></div>
      </Modal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      selectItem: '',
      visible: false,
      dataList: [],
      fileType: '',
      dataListLoading: false,
      dataForm: {
        type: 1,
        status: 2,
        name: "",
        suffix: "",
      },
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      dataConlums: [
        {title: this.$t('screen.choose'), width: 80, key: 'filePath', slot: 'select', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('screen.choose'))
          }
        },
        {title: this.$t('file.thumbnail'), key: 'filePath', slot: 'filePath', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('file.thumbnail'))
          }
        },
        {title: this.$t('file.name'), key: 'fileName', align: 'center', width: 120, tooltip: true,
          renderHeader:(h)=>{
              return h('div',this.$t('file.name'))
          }
        },
        {title: this.$t('file.type'), key: 'suffix', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('file.type'))
          }
        },
        {title: this.$t('file.size'), key: 'fileSize', slot: 'fileSize', align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('file.size'))
          }
        },
        {title: this.$t('common.createTime'), key: 'createTime', width: 170, align: 'center',
          renderHeader:(h)=>{
              return h('div',this.$t('common.createTime'))
          }
        },
      ],
      downloadUrl: this.$http.adornUrl(`/sys/program/file/download/`)
    }
  },
  methods: {
    init () {
      this.visible = true
      this.getDataList()
    },
    selectId (item) {
      this.selectItem = item
      this.$emit('setURL', this.selectItem)
      this.visible = false
    },
    // 文件类型
    getFileType () {
      this.$http({
        url: this.$http.adornUrl('/sys/file/fileType'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fileType = data.fileType
        }
      })
    },
    // 获取数据列表
    getDataList () {
      this.getFileType()
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/screen/media/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'fileName': this.dataForm.name,
          'suffix': this.dataForm.suffix,
          'status': this.dataForm.status,
          'type': this.dataForm.type
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    }
  },
  filters: {
    // 文件大小转换
    filterType (val) {
      if (val === 0) {
        return '0 B'
      }
      var k = 1024
      var sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      var i = Math.floor(Math.log(val) / Math.log(k))
      return (parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) + ' ' + sizes[i])
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.dataList = []
        this.pageIndex = 1
        this.pageSize = 5
        this.totalPage = 0
      }
    }
  }
}
</script>

<style scoped>

</style>
