<template>
  <div class="verify-line">
    <Row>
      <Col span="5">
      <Input size="large" v-model="keyLeft" :placeholder="$t('cardDevice.deviceName')" :border="false" @keyup.native="getDataList(true)"></Input>
      <Card shadow :style="{ 'height': tableHeight + 140 + 'px' }">
        <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="tableHeight + 155"> -->
        <div :style="{'height':tableHeight + 45+ 'px', 'overflow-y': 'auto'}">
          <ul class="opera_ul" v-if="dataList && dataList.length > 0">
            <li v-for="(item, index) in dataList" :key="index" @click="changeSelect(item, index)">
              <div :class="select === index ? 'select' : ''"
                style="padding-left: 10px;padding-right: 10px;height: 40px;">
                <!-- <span class="box">{{item.alias}}</span> -->
                {{item.alias}}
                <span style="float: right;">
                  <svg width="25px" height="25px" aria-hidden="true"  style="vertical-align:middle;">
                    <use :xlink:href="item.isOn===1 ? '#on-line' : '#line'"></use>
                  </svg>
                </span>
              </div>
            </li>
          </ul>
          <div v-else style="text-align: center;font-size: 18px;line-height: 100px ">
            {{ $t('home.temporarilyNoData') }}
          </div>
        <!-- </Scroll> -->
        </div>
        <Page size="small" :total="totalPageLeft" :current="pageIndexLeft" :page-size="pageSizeLeft"
              show-elevator show-total @on-change="currentChangeLeftHandle" @on-page-size-change="sizeChangeLeftHandle"/>
      </Card>
      </Col>
      <Col span="19" style="float: right">
        <div :style="{ 'height': tableHeight + 175 + 'px', 'overflow-y': 'auto', 'margin-left': '5px' }">
          <Form :inline="true" :model="dataForm" @keyup.enter.native="getTrafficInfoList(1)">
  <!--          <Button size="large" @click="enableTrafficService">{{ $t('traffic.enableTraffic') }}</Button>-->
            <FormItem>
              <Input
                size="large"
                v-model="dataForm.key"
                :placeholder="$t('traffic.plateNumber')"></Input>
            </FormItem>
            <Button style="margin-right: 6px"  @click="getTrafficInfoList(1)" size="large">
              <div style="margin: 3px 8px">{{ $t("common.query") }}</div>
            </Button>
          </Form>

          <Table border :columns="dataColumns" ref="table" :data="trafficInfoList" style="width: 100%;margin-top: 10px"
            :max-height="tableHeight">

            <template slot-scope="{ row, index }" slot="action">
              <Button type="primary" size="small" style="margin-right: 5px;margin-top: 5px"
                      @click="getPicPreview(row.id)">
                {{ $t('program.preview') }}
              </Button>
              <Button type="primary" size="small" style="margin-right: 5px;margin-top: 5px"
                @click="downloadPic(row, index)">
                {{ $t('file.download') }}
              </Button>
            </template>
          </Table>
          <Page style="float:right;margin-top:20px" :total="totalPage" :current="pageIndex" :page-size="pageSize"
            show-elevator show-sizer :page-size-opts="[10, 20, 50, 100]" show-total @on-change="currentChangeHandle"
            @on-page-size-change="sizeChangeHandle" />
        </div>
      </Col>
    </Row>
    <picture-preview v-if="previewVisible" ref="picturePreview"></picture-preview>
  </div>
</template>

<script>
import PicturePreview from "./picture-preview";
export default {
  components: {PicturePreview},
  data() {
    return {
      dataForm: {
        key: '',
      },
      deviceId: '',
      select: 0,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      keyLeft: '',
      pageIndexLeft: 1,
      pageSizeLeft: 10,
      totalPageLeft: 0,
      startDate: '',
      endDate: '',
      date: [],
      dataColumns: [
        {
          title: this.$t('monitor.serialNumber'), key: 'deviceId', align: 'center', fixed: 'left', tooltip: true, width: 180,
          renderHeader: (h) => {
            return h('div', this.$t('monitor.serialNumber'))
          }
        },
        {
          title: this.$t('traffic.eventName'), key: 'eventName', align: 'center', fixed: 'left', tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.eventName'))
          }
        },
        {
          title: this.$t('traffic.plateNumber'), key: 'plateNumber', align: 'center', fixed: 'left', tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.plateNumber'))
          }
        },
        {
          title: this.$t('traffic.plateType'), key: 'plateType', align: 'center',  tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.plateType'))
          }
        },
        {
          title: this.$t('traffic.plateColor'), key: 'plateColor', align: 'center', tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.plateColor'))
          }
        },
        {
          title: this.$t('traffic.vehicleColor'), key: 'vehicleColor', align: 'center', tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.vehicleColor'))
          }
        },
        {
          title: this.$t('traffic.vehicleType'), key: 'vehicleType', align: 'center',  tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.vehicleType'))
          }
        },
        {
          title: this.$t('traffic.vehicleSize'), key: 'vehicleSize', align: 'center', tooltip: true, width: 130,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.vehicleSize'))
          }
        },
        {
          title: this.$t('traffic.eventTime'), key: 'eventTime', align: 'center', tooltip: true, width: 180,
          renderHeader: (h) => {
            return h('div', this.$t('traffic.eventTime'))
          }
        },
        {
          title: this.$t('common.operation'),
          slot: 'action',
          width: 200,
          align: 'center',
          fixed: 'right',
          renderHeader: (h) => {
            return h('div', this.$t('common.operation'))
          }
        }
      ],
      trafficInfoList: [],
      previewVisible:false
    }
  },
  activated() {
    this.initData()
    this.getDataList()
  },
  methods: {
    initData() {
      this.dataForm = {
        key: ''
      }
      this.select = 0
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
    },
     // 每页数
    sizeChangeLeftHandle(val) {
      this.pageSizeLeft = val
      this.pageIndexLeft = 1
      this.getDataList()
    },
    // 当前页
    currentChangeLeftHandle(val) {
      this.pageIndexLeft = val
      this.getDataList()
    },
    // 获取数据列表
    getDataList(isQuery) {
      if (isQuery) {
         this.pageIndexLeft = 1
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/monitor/traffic/deviceList'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndexLeft,
          'limit': this.pageSizeLeft,
          'key': this.keyLeft,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalPageLeft = data.page.totalCount
          this.dataList = data.page.list
          if (this.dataList.length > 0) {
            this.deviceId = this.dataList[0].deviceId
            this.getTrafficInfoList()
          }
          // this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPageLeft = 0
        }
        this.dataListSelections = []
        this.dataListLoading = false
      })
    },

    changeSelect(item, index) {
      this.select = index
      this.deviceId = item.deviceId
      this.getTrafficInfoList()
    },
    handleReachBottom() {
      return new Promise(resolve => {
        setTimeout(() => {
          this.pageSize = this.pageSize + 10;
          if (this.pageSize > this.totalCount) {
            this.$Message.warning(this.$t('common.noMoreData'));
            return;
          }
          this.getDataList();
          resolve();
        }, 200);
      });
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getTrafficInfoList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getTrafficInfoList()
    },
    /**
     * 开启智慧交通功能
     */
    /*enableTrafficService() {
      this.$http({
        url: this.$http.adornUrl('/monitor/device/subscribeTrafficService'),
        method: 'get',
        params: this.$http.adornParams({
          'deviceId': this.deviceId,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$Message.success(data.msg.msg)
        } else {
          this.$Message.error(data.msg.msg)
        }
      })
    },*/
    /**
     * 获取交通信息列表
     */
    getTrafficInfoList(isQuery) {
      if (isQuery === 1){
        this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/monitor/traffic/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key,
          'deviceId': this.deviceId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {

          this.trafficInfoList = data.page.list
          this.totalPage = data.page.totalCount
          //当输入key后，选中页码超出总页码时，重新赋值页码，并调用接口
          if (data.page.currPage > this.totalPage && this.totalPage !== 0){
            this.pageIndex = 1
            this.getTrafficInfoList(isQuery)
          }
        } else {
          this.trafficInfoList = []
          this.totalPage = 0
        }
      })
    },
    downloadPic(row, index) {
      window.open(this.$http.adornUrl(`/monitor/traffic/download/${row.id}?token=${this.$cookie.get('token')}`))
    },

    //预览
    getPicPreview (deviceId) {
      this.previewVisible = true
      this.$nextTick(() => {
        this.$refs.picturePreview.init(deviceId)
      })
    },
  },
  computed: {
    language: {
      get() {
        return this.$store.state.language.language
      },
    },
    tableHeight: {
      get() { return this.$store.state.common.tableHeight },
    },

    documentClientWidth: {
      get() {
        return this.$store.state.common.documentClientWidth
      },
    },
  }


}
</script>

<style scoped>
.box {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 170px;
}
.opera_ul {
  margin: 0 auto;
  overflow: hidden;
}

.opera_ul li {
  background-color: rgb(255, 255, 255);
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-bottom: 5px;
  cursor: pointer;
  white-space: normal;
  word-break: break-word;
}

.opera_ul li:hover {
  background-color: rgb(204, 204, 204);
  cursor: pointer;
}

.select {
  background-color: rgb(204, 204, 204);
}
</style>
