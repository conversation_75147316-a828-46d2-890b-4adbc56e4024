<template>
  <Modal v-model="visible" width="900" height="500" footer-hide>
    <div slot="header" class="headerClass">
      <p>
        <span>{{ $t('traffic.illegalPic') }}</span>
      </p>
    </div>
    <ul class="screen_ul">
      <li v-for="(item, index) in pics" :key="index">
        <div v-if="previewLoading === true">
          <div class="demo-spin-container">
            <Spin size="large" fix :show="previewLoading"></Spin>
          </div>
        </div>
        <div v-else>
          <div @click="onPreview(item)">
            <el-tooltip class="item" effect="dark" :content="$t('common.clickToEnlarge')" placement="bottom-end">
              <el-image style="width: 220px;height: 220px;margin-top: 20px" :src="item"
                        :alt="$t('common.clickToEnlarge')"></el-image>
            </el-tooltip>
          </div>
        </div>
      </li>
    </ul>
    <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="[showImg]" style="z-index:25000;"/>
  </Modal>
</template>

<script>
import $ from 'jQuery'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      visible: false,
      pics: [],
      showViewer: false,
      showImg: '',
      previewLoading: true
    }
  },
  methods: {
    // 初始化
    init(id) {
      this.picPreview(id)
    },
    // 违章图片预览
    picPreview(id) {
      this.$http({
        url: this.$http.adornUrl('/monitor/traffic/preview/'+ id),
        method: 'get',
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.visible = true
          this.previewLoading = false
          this.pics = data.data
        } else {
          this.$Message.error(data.msg)
        }
      })
    },


    onPreview(img) {
      this.showViewer = true
      this.showImg = img;
    },
    closeViewer() {
      this.showViewer = false
      this.showImg = ''
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        this.pics = []
        $('#dialog-bg').hide()
        this.showViewer = false
      }
    }
  }
}
</script>
<style scoped>
.screen_ul {
  height: 500px;
  overflow: hidden;
  overflow-y: auto;
}

.screen_ul li {
  list-style: none;
  float: left;
  margin-left: 20px;
  margin-top: 10px;
  width: 250px;
  height: 260px;
  background: #ebeef5;
  text-align: center;
  border-radius: 5%;
}

.demo-spin-container {
  width: 220px;
  height: 220px;
  position: relative;
  /* margin: 3px auto 5px; */
  display: inline-block;
  background: rgb(0, 0, 0);
}

.headerClass {
  text-align: center;
  font-size: 20px;
}
</style>
