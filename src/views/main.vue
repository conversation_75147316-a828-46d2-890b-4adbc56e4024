<style scoped>
  img {
    image-rendering: -moz-crisp-edges; /* Firefox */
    image-rendering: -o-crisp-edges; /* Opera */
    image-rendering: -webkit-optimize-contrast; /* Webkit (non-standard naming) */
    image-rendering: crisp-edges;    -ms-interpolation-mode: nearest-neighbor; /* IE (non-standard property) */
  }
  .layout {
    background: #f5f7f9;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .layout-logo{
    width: 15%;
    height: 60px;
    line-height: 60px;
    /* overflow: hidden; */
    background: #ffffff;
    text-align: center;
    border-bottom: solid 1px #dcdee2;
    float: left;
    cursor:pointer;
  }
  .head-menu {
    width: 85%;
    height: 64px;
    float: left;
  }
  .ivu-layout-sider::-webkit-scrollbar {
    display: none;
  }
  .layout-right {
    float: right;
  }
  .arrow-wrap {
    position: absolute;
    z-index: 999;
    left:15.5%;
    top:50%;
    transform: translate(-50%, -50%);
    text-align: center;
    line-height: 50px;
    background: rgba(223, 223, 223, 0.9);
    border-radius: 50%;
    height: 45px;
    width: 45px;
    box-shadow: 0 2px 4px rgba(0,0,0,.20);
    cursor: pointer;
    opacity: 0.7;
  }
  .arrow-wrap:hover {
     background: rgba(209, 209, 209, 0.9);
  }
</style>
<template>
  <div class="layout">
    <Layout :style="{ 'height': documentClientHeight + 'px' }">
      <!-- 菜单，目录菜单 -->
      <div class="layout-head">
        <div class="layout-logo">
          <!-- <img src="@/assets/img/logo.png" style="height: 40px;vertical-align: middle;"/> -->
          <img id="imgLogo" :src="require(`@/assets/img/aips4_${['cn', 'tw', 'us','ja'].includes(selectLang) ? selectLang : 'us'}.png`)" style="height: 44px;width: 150px;vertical-align: middle"
          />

        </div>
        <div class="head-menu">
          <Menu mode="horizontal" theme="light" :active-name="subMenuActiveName" ref="subMenu">
            <div v-if="subMenuList && subMenuList.list && subMenuList.list.length >= 1">
              <div v-for="subMenu in subMenuList.list" :key="subMenu.menuId" v-if="subMenu.type !== 3">
                  <Submenu :name="subMenu.name" :class="subMenuActiveNameClass(subMenu.list)"
                    v-if="subMenu.list && subMenu.list.length >= 1">
                    <template slot="title">{{$t('nav.'+ subMenu.name)}}</template>
                    <MenuItem :name="towMenu.name" v-for="towMenu in subMenu.list" :key="towMenu.menuId"
                      :class="subMenuActiveName == towMenu.name? 'ivu-menu-item ivu-menu-item-active ivu-menu-item-selected' : 'ivu-menu-item'"
                    @click.native="selectSubMenu(towMenu)">{{$t('nav.'+ towMenu.name)}}</MenuItem>
                  </Submenu>
                  <MenuItem :class="subMenuActiveName == subMenu.name?  'ivu-menu-item ivu-menu-item-active ivu-menu-item-selected' : 'ivu-menu-item'"
                  v-else :name="subMenu.name" :id="subMenu.name"
                  @click.native="selectSubMenu(subMenu)">{{$t('nav.'+ subMenu.name)}}</MenuItem>
              </div>
            </div>
            <div class="layout-right">
              <Dropdown style="margin-right: 25px;" v-if="$route.path === '/home'">
                <Tooltip :content="$t('home.DynamicLargeScreen')" placement="bottom">
                <a href="javascript:void(0)" @click="$router.replace({name: 'big-screen'})">
                  <svg width="30px" height="30px" aria-hidden="true" style="vertical-align:middle;">
                    <use xlink:href="#big-screen"></use>
                  </svg>
                </a>
                </Tooltip>
              </Dropdown>
              <a v-if="isVip" href="javascript:void(0)" style="margin-right: 25px;" @click="upgradeVIP()">

                <span v-if="userInfo.vipStatus === -1" style="color: #CD7F32">
                  <img style="vertical-align: middle;" src="@/assets/img/VIP4.png" width="23" height="23">
                  {{$t('pay.superVip')}}</span>
                <span v-if="userInfo.vipStatus === 0" style="color: #ff9999">{{$t('pay.upgradeToPaidVersion')}}</span>
                <span v-else-if="userInfo.vipStatus === 1" style="color: #c0c0c0">
                  <img style="vertical-align: middle;" src="@/assets/img/VIP1.png" width="20" height="20">
                  {{$t('pay.silverCardVip')}}</span>
                <span v-else-if="userInfo.vipStatus === 2" style="color: #ffd700">
                  <img style="vertical-align: middle;" src="@/assets/img/VIP2.png" width="20" height="20">
                  {{$t('pay.goldCardVip')}}</span>
                <span v-else-if="userInfo.vipStatus === 3" style="color: #5cadff">
                  <img style="vertical-align: middle;" src="@/assets/img/VIP3.png" width="23" height="23">
                  {{$t('pay.diamondVip')}}</span>
                <span v-else-if="userInfo.vipStatus === 4" style="color: #CD7F32">
                  <img style="vertical-align: middle;" src="@/assets/img/VIP4.png" width="23" height="23">
                  {{$t('pay.vip4')}}</span>
              </a>
              <Dropdown style="margin-right: 20px">
                <a href="javascript:void(0)">
                    {{$t('common.language')}}
                    <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="item in localeList" :key="item.value" :selected="item.selected" @click.native="changeLocale(item.value)">{{item.name}}</DropdownItem>
                </DropdownMenu>
              </Dropdown>
              <Dropdown style="margin-right: 20px">
                  <a href="javascript:void(0)" @click="openDocument()">{{ $t('common.manual') }}</a>
                  <!-- <Button type="text" @click="openDocument()">{{ $t('common.manual') }}</Button> -->
                  <manual-tutorial v-if="manualTutorialVisible" ref="manualTutorial"></manual-tutorial>
              </Dropdown>
              <Dropdown style="margin-right: 40px">
                <a href="javascript:void(0)">
                  <Avatar style="color: #f56a00;background-color: #fde3cf;font-size: 20px" size="large">{{this.firstName}}</Avatar>
                  <span style="color:#5b5b5b; font-size: 20px;line-height: 50px;">&nbsp;{{userInfo.username}}</span>
                </a>
                <DropdownMenu slot="list">
                    <DropdownItem @click.native="updateUser()">{{$t('common.personalSettings')}}</DropdownItem>
<!--                    <DropdownItem  @click.native="InstructionManual()">{{$t('common.manual')}}</DropdownItem>-->
                    <DropdownItem @click.native="logoutHandle()">{{$t('common.logOut')}}</DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </Menu>
        </div>
      </div>
      <Layout>
          <!-- 主菜单-->
          <Sider :width="navClientWidth" hide-trigger style="sider"
          :style="{background: '#fff', 'overflow-x':'hidden', 'overflow-y':'auto'}">
              <Menu theme="light" width="auto" :active-name="menuActiveName" :style="{height: documentClientHeight - 64 + 'px'}">
                <MenuItem name="home" @click.native="selectMenu('home')" v-if="!isDisMenu">
                  <div :style="{height:'30px'}">
                    <svg width="25px" height="25px" aria-hidden="true">
                      <use xlink:href="#shouye"></use>
                    </svg>
                    <span v-if="!isContraction" :style="{'vertical-align': 'top','font-size': '18px','font-weight':'500'}">{{$t('nav.首页')}}</span>
                  </div>
                </MenuItem>
                <div v-for="menu in menuList" :key="menu.menuId">
                  <Submenu :name="menu.name" v-if="menu.type == 4 && menu.list && menu.list.length >= 1 && !isDisMenu">
                    <template slot="title">
                      <svg  width="25px" height="25px" aria-hidden="true">
                        <use :xlink:href="'#' + menu.icon"></use>
                      </svg>
                      <span v-if="!isContraction" :style="{'vertical-align': 'top','font-size': '18px','font-weight':'500'}">{{$t('nav.'+ menu.name)}}</span>
                    </template>
                    <MenuItem :name="submenu.name" @click.native="selectMenu(submenu)" v-for="submenu in menu.list" :key="submenu.menuId">
                      <div :style="{height:'30px'}">
                        <svg  width="25px" height="25px" aria-hidden="true">
                          <use :xlink:href="'#' + submenu.icon"></use>
                        </svg>
                        <span v-if="!isContraction" :style="{'vertical-align': 'top','font-size': '18px','font-weight':'500'}">{{$t('nav.'+ submenu.name)}}</span>
                      </div>
                    </MenuItem>
                  </Submenu>
                  <MenuItem v-else-if="!isDisMenu || menu.name == '付费服务'" :name="menu.name" @click.native="selectMenu(menu)">
                    <!-- 188 为  LEDOK Lite 是app专用-->
                    <div :style="{height:'30px'}" v-if="menu.menuId != 188">
                      <svg  width="25px" height="25px" aria-hidden="true">
                        <use :xlink:href="'#' + menu.icon"></use>
                      </svg>
                      <span v-if="!isContraction" :style="{'vertical-align': 'top','font-size': '18px','font-weight':'500'}">{{$t('nav.'+ menu.name)}}</span>
                    </div>
                  </MenuItem>
                </div>
              </Menu>
          </Sider>
          <!-- 内容 "-->
          <Layout :style="{padding: '10px 24px 10px'}">
              <!-- <div :style="{margin: '10px 0'}"></div> -->
              <Content :style="{ minHeight: '280px'}"  v-if="!$store.state.common.contentIsNeedRefresh">
                <keep-alive>
                  <router-view/>
                </keep-alive>
              </Content>
          </Layout>
      </Layout>
    </Layout>

    <!-- 右箭头 -->
    <div v-show="menuList.length > 0" :style="isContraction ? 'left:4.5%' : 'left:15.5%'" class="arrow-wrap" @click="contractionClick">
      <Icon :type="isContraction ? 'ios-arrow-forward' : 'ios-arrow-back'" :style="{'vertical-align':'middle'}"
        size="35" color="#424242"/>
    </div>

    <!-- 警告通知 -->
    <Modal v-model="modal" width="360">
      <p slot="header" :style="{'color':'#f60','text-align':'center'}">
          <Icon type="ios-information-circle"></Icon>
          <span>{{$t('home.warningNotice')}}</span>
      </p>
      <div :style="{'overflow-x': 'hidden', 'overflow-y': 'auto','height': '200px'}">
          <p v-if="alarmData">
            <div v-for="(item, index) in alarmData" :key="index">
              <b>{{$t('home.cardNumber')}}：</b><span>{{item.cardId}}</span> <br/>
              <b>{{$t('operation.thealias')}}：</b><span>{{item.alias}}</span> <br/>
              <b>{{$t('sys.alarmType')}}：</b><span>{{item.type}}</span> <br/>
              <b>{{$t('sys.alarmTime')}}：</b><span>{{item.createTime === null ? null : new Date(item.createTime).toLocaleString()}}</span> <br/>
              <br/>
            </div>
          </p>
      </div>
      <div slot="footer">
          <Button type="error" size="large" long :loading="alarm_loading" @click="handlerAlarm">{{$t('sys.Iknown')}}</Button>
      </div>
    </Modal>
    <!-- 密码锁 -->
    <lock-pwd v-if="pwdVisible" ref="lockPwd" @refreshDataList="$router.push({ name: 'home' })"></lock-pwd>
  <!-- 弹窗，修改LOGO-->
<!--  <update-logo v-if="updateLOGOVisible" ref="updateLOGO" @refreshData="getUserInfo"></update-logo>-->
  </div>
</template>

<script>
// import UpdateLogo from "./modules/sys/update-logo.vue";
import manualTutorial from "./common/manual-tutorial";
import { clearLoginInfo } from '@/utils'
import lockPwd from './lock-pwd'
export default {
  data () {
    return {
      // navClientWidth: 0, // 左侧菜单宽度
      dynamicMenuRoutes: [],
      alarmData: [],
      modal: false,
      alarm_loading: false,
      // updateLOGOVisible: false,
      firstName:"",
      // downloadUrl: this.$http.adornUrl(`/sys/user/myIcon/`),
      // flag: true,
      localeList: [
        {name: 'English', value: 'us', selected: false},
        {name: '中文简体', value: 'cn', selected: true},
        {name: '中文繁體', value: 'tw', selected: false},
        {name: '日本語.', value: 'ja', selected: false},
        {name: 'Português(Brasil)', value: 'pt', selected: false},// 葡萄牙语
        {name: 'العربية', value: 'ar', selected: false}, // 阿拉伯语
        {name: 'Français', value: 'fr', selected: false}, // 法语
        {name: 'Español', value: 'es', selected: false}, // 西班牙语
        {name: 'русский', value: 'ru', selected: false}, // 俄语
      ],
      // localeList: [
      //   {name: 'English', value: 'us', selected: false},
      //   {name: '中文繁體', value: 'tw', selected: true}
      // ],
      isVerify: window.SITE_CONFIG.isVerify,
      isContraction: false,
      pwdVisible: false,
      subMenuList: [],
      selectLang: localStorage.getItem('locale') === null ? 'cn' : localStorage.getItem('locale'),
      manualTutorialVisible:false,
      isVip: window.SITE_CONFIG.isVip,
      isDisMenu: false, // 菜单是否禁用
    }
  },
  components: {
    // UpdateLogo
    lockPwd,
    manualTutorial
  },
  methods: {
    subMenuActiveNameClass(list) {
      var res = "ivu-menu-item"
      if (list && list.length > 0) {
        for (let i = 0; i < list.length; i++) {
          const element = list[i];
          if (element.name == this.subMenuActiveName) {
            res = "ivu-menu-item ivu-menu-item-active ivu-menu-item-selected"
            break
          }
        }
      }
      return res
    },
    init() {
      // this.handleSpinCustom()
      // 获取菜单列表与路由列表
      this.menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]')
      this.dynamicMenuRoutes = JSON.parse(sessionStorage.getItem('dynamicMenuRoutes') || '[]')
      // 初始化页面宽高
      this.resetDocumentClientHeight()
      //选中菜单为home时跳转到home页面
      if (this.menuActiveName === 'home') {
        this.$router.push({ name: 'home' })
      }
      this.handlerEventSource()
      this.changeLocale(localStorage.getItem('locale') === null ? 'cn' : localStorage.getItem('locale'))
      if (this.menuList && this.menuList.length > 0) {
        var res = {}
        this.menuList.forEach(element => {
          if (element.name == this.menuActiveName) {
            res = element
          }
        })
        this.subMenuList = res
      }
    },
    contractionClick() {
      if (this.isContraction) {
        this.navClientWidth = document.documentElement['clientWidth'] * (16 / 100)
        this.isContraction = false;
      } else {
        this.navClientWidth = document.documentElement['clientWidth'] * (5 / 100)
        this.isContraction = true;
      }
    },
    // 切换语言
    changeLocale (value) {
      this.$i18n.locale = value
      this.language = value
      localStorage.setItem('language', value)
       this.localeList.map(item => {
         if (item.value === value) {
           item.selected = true
         } else {
           item.selected = false
         }
       })

    },
    resetDocumentClientHeight () {
      // 重置窗口可视高度
      this.documentClientHeight = document.documentElement['clientHeight']
      this.documentClientWidth = document.documentElement['clientWidth']
      // 重置左侧菜单宽度
      if (this.isContraction) {
        this.navClientWidth = document.documentElement['clientWidth'] * (5 / 100)
      } else {
        this.navClientWidth = document.documentElement['clientWidth'] * (16 / 100)
      }
      // 重置表格高度
      this.tableHeight = window.innerHeight - 279
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
        this.documentClientWidth = document.documentElement['clientWidth']
        if (this.isContraction) {
          this.navClientWidth = document.documentElement['clientWidth'] * (5 / 100)
        } else {
          this.navClientWidth = document.documentElement['clientWidth'] * (16 / 100)
        }
        this.tableHeight = window.innerHeight - 279
      }
    },
    // 查询用户信息
    handleSpinCustom () {
      this.$Spin.show({
        render: (h) => {
          return h('div', [
            h('Icon', {
              'style': 'animation: ani-demo-spin 1s linear infinite;',
              props: {
                type: 'ios-loading',
                size: 40
              }
            }),
            h('div', this.$t('common.Loading'))
          ])
        }
      })
    },
    getUserInfo () {
      this.$http({
        url: this.$http.adornUrl('/sys/user/info'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userInfo = data.user
          this.firstName = this.userInfo.username.substring(0,1)
          if (this.userInfo.lock == 1) {
              this.pwdVisible = true;
              this.$nextTick(() => {
                this.$refs.lockPwd.init(this.userInfo.userId)
              })
          }
          if (!this.isVerify) {
            var isStrong = this.$cookie.get("isStrong")
            if ((null != isStrong && undefined !== isStrong)){
              this.$router.push({ name: 'updateUserInfo'})
            } else {
              this.init()
            }
          } else {
            this.init()
          }
          if (this.isVip) {
            // 判断VIP状态
            if (this.userInfo.vipStatus === 0) {
              this.isDisMenu = true;
              this.upgradeVIP()
              this.init()
            }
          }

          // if (this.userInfo.iconUrl) {
          //   if (this.userInfo.iconUrl.substr(0, 6) === 'aips40') {
          //     $('#userUrl').prop({'src': `${this.downloadUrl}${this.userInfo.iconUrl}?token=${this.$cookie.get("token")}`})
          //   } else {
          //     $('#userUrl').prop({'src': this.userInfo.iconUrl})
          //   }
          // }
          // this.$Spin.hide()
        } else {
          this.$Message.error(data.msg)
        }
      })
    },
    // 退出
    logoutHandle () {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('common.areYouSureExit'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/sys/logout'),
            method: 'post',
            data: this.$http.adornData()
          }).then(({data}) => {
            if (data && data.code === 0) {
              clearLoginInfo()
              this.$router.push({ name: 'login' })
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    // 主菜单选择菜单
    selectMenu (menu) {
      this.subMenuList = []
      if (menu === 'home') {
        this.$router.push({ name: 'home' })
        this.menuActiveName = 'home'
        return
      }
      this.menuActiveName = menu.name
      // 主菜单页面默认使用第一个菜单作为页面
      var route;
      if (menu.list[0]) {
        this.subMenuActiveName = menu.list[0].name
        route = this.dynamicMenuRoutes.filter(item => item.meta.menuId === menu.list[0].menuId)
      } else {
        this.subMenuActiveName = menu.name
        route = this.dynamicMenuRoutes.filter(item => item.meta.menuId === menu.menuId)
      }
      if (route.length >= 1) {
        this.$router.push({ name: route[0].name })
      } else {
        this.$router.push('404')
      }
      this.subMenuList = menu
    },
    // 选择子菜单
    selectSubMenu (menu) {
      if (menu.name) {
        this.subMenuActiveName = menu.name
      }
      var route = this.dynamicMenuRoutes.filter(item => item.meta.menuId === menu.menuId)
      if (route.length >= 1) {
        this.$router.push({ name: route[0].name })
      } else {
        this.$router.push('404')
      }
    },
    updateUser () {
      this.$router.push({ name: 'updateUserInfo' })
    },
    // 升级VIP
    upgradeVIP () {
      this.$router.push({ name: 'orderServer-payServer' })
      this.menuActiveName = "付费服务";
      if (this.menuList && this.menuList.length > 0) {
        var res = {}
        this.menuList.forEach(element => {
          if (element.name == this.menuActiveName) {
            res = element
          }
        })
        this.subMenuList = res
      }
      this.subMenuActiveName = "订购服务"
    },
    //使用手册 废弃
    InstructionManual(){
      let routeUrl = this.$router.resolve({name: 'manual'})
      let params = {userId: this.userInfo.userId}
      window["filter"] = params
      window.open(routeUrl.href, '_blank');
    },
    // 警告通知
    handlerEventSource () {
      if (!this.isVerify) {
        if (!this.$cookie.get('token')) { // 退出登录
          this.websocket.close();
        }
        if ("WebSocket" in window) {
          this.websocket = new WebSocket(
            `${window.SITE_CONFIG.WSURL}/alarm/${this.$cookie.get("token")}`
          );
        }
        this.websocket.onopen = function () {
          // console.log('连接成功')
        };
        this.websocket.onerror = function () {
          // console.log('连接出错')
        };
        this.websocket.onclose = function () {
          // console.log('退出连接')
        };
        var _this = this
        this.websocket.onmessage = function (event) {
          var json = JSON.parse(event.data);
          if (json) {
            _this.alarmData = json
            _this.modal = true
            var music = new Audio()
            music.src = require('@/assets/wav/keji.wav')
          }
        }
      }
    },
    handlerAlarm () {
      this.$Modal.confirm({
        title: this.$t('common.tips'),
        content: this.$t('sys.notReminded'),
        okText: this.$t('common.confirm'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          this.$http({
            url: this.$http.adornUrl('/alarm/handlerAlarm'),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.modal = false
            } else {
              this.$Message.error(data.msg)
            }
          })
        }
      })
    },
    //使用手册 新
    openDocument(){
      this.manualTutorialVisible = true;
      this.$nextTick(() => {
        this.$refs.manualTutorial.init()
      });
    },
  },
  provide () {
    return {
      // 刷新
      refresh () {
        this.$store.commit('common/updateContentIsNeedRefresh', true)
        this.$nextTick(() => {
          this.$store.commit('common/updateContentIsNeedRefresh', false)
        })
      }
    }
  },
  computed: {
    language: {
      get () { return this.$store.state.language.language },
      set (val) { this.$store.commit('language/setLanguage', val) }
    },
    documentClientHeight: {
      get () { return this.$store.state.common.documentClientHeight },
      set (val) { this.$store.commit('common/updateDocumentClientHeight', val) }
    },
    documentClientWidth: {
      get () { return this.$store.state.common.documentClientWidth },
      set (val) { this.$store.commit('common/updateDocumentClientWidth', val) }
    },
    navClientWidth: {
      get () { return this.$store.state.common.navClientWidth },
      set (val) { this.$store.commit('common/updateNavClientWidth', val) }
    },
    tableHeight: {
      get () { return this.$store.state.common.tableHeight },
      set (val) { this.$store.commit('common/updateTableHeight', val) }
    },
    menuList: {
      get () { return this.$store.state.common.menuList },
      set (val) { this.$store.commit('common/updateMenuList', val) }
    },
    menuActiveName: {
      get () { return this.$store.state.common.menuActiveName },
      set (val) { this.$store.commit('common/updateMenuActiveName', val) }
    },
    subMenuActiveName: {
      get () { return this.$store.state.common.subMenuActiveName },
      set (val) { this.$store.commit('common/updatesubMenuActiveName', val) }
    },
    userInfo: {
      get () { return this.$store.state.user.userInfo },
      set (val) { this.$store.commit('user/updateUserInfo', val) }
    }
  },
  created () {
    // 获取用户信息
    this.getUserInfo()
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     if (to.name === 'screen-program' && from.name === 'standard' && vm.menuActiveName === '智慧屏幕') {
  //       vm.flag = false
  //     }
  //   });
  // }
  watch: {
    'language': function(newVal, OldVal) {
      this.selectLang = newVal
    }
  },
}
</script>
