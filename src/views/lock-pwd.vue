<template>
    <Modal v-model="visible" width="500">
        <p slot="header" style="text-align:center">
            <span>{{ '认证密码' }}</span>
        </p>
        <Form ref="dataForm" :model="dataForm" style="height: 70px;" :label-width="80" label-position="left"
        @keyup.enter.native="dataFormSubmit()">
          <FormItem :label="$t('login.password')">
            <Input size="large" type="text" v-model="dataForm.password"
            :placeholder="$t('common.PleaseInput') + $t('login.password')"/>
          </FormItem>
        </Form>
        <div slot="footer">
            <Button size="large" @click="visible = false">{{$t('common.cancel')}}</Button>
            <Button type="primary" size="large" :loading="loading" @click="dataFormSubmit()">{{$t('common.confirm')}}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: 0,
        password: '',
      },
      loading: false
    }
  },
  methods: {
    // 初始化
    init (id) {
      this.dataForm.id = id || 0
      this.visible = true
    },
    // 表单提交
    dataFormSubmit () {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl(`/sys/lockPwd/unlock`),
        method: 'post',
        data: this.$http.adornData({userId: this.dataForm.id, 'password': this.$encruption(this.dataForm.password)}, false)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loading = false
          this.userInfo = data.user
          this.$Message.success({
            content: this.$t('common.operationSuccessful'),
            duration: 0.5,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$Message.error(data.msg)
          setTimeout(() => {
            this.loading = false
          }, 500)
        }
      })
    },
  },
  computed: {
    userInfo: {
      get () { return this.$store.state.user.userInfo },
      set (val) { this.$store.commit('user/updateUserInfo', val) }
    }
  },
  watch: {
    'visible': function (newVal, oldVal) {
      if (newVal === false) {
        setTimeout(() => {
          this.dataForm = {
            id: 0,
            password: '',
          }
        }, 500)
      }
    }
  }
}
</script>
