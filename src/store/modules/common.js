export default {
  namespaced: true,
  state: {
    // 侧边栏, 菜单
    menuList: [],
    menuActiveName: 'home',
    subMenuActiveName: '',
    // 内容, 是否需要刷新
    contentIsNeedRefresh: false,
    // 页面窗口可视高度(随窗口改变大小)
    documentClientHeight: 0,
    documentClientWidth: 0,
    // 表格高度
    tableHeight: 0,
    // 左侧菜单宽度
    navClientWidth: 0,
  },
  mutations: {
    updateDocumentClientHeight (state, height) {
      state.documentClientHeight = height
    },
    updateDocumentClientWidth (state, width) {
      state.documentClientWidth = width
    },
    updateNavClientWidth (state, width) {
      state.navClientWidth = width
    },
    updateTableHeight (state, height) {
      state.tableHeight = height
    },
    updateMenuList (state, list) {
      state.menuList = list
    },
    updateMenuActiveName (state, name) {
      state.menuActiveName = name
    },
    updatesubMenuActiveName (state, name) {
      state.subMenuActiveName = name
    },
    updateContentIsNeedRefresh (state, status) {
      state.contentIsNeedRefresh = status
    }
  }
}
