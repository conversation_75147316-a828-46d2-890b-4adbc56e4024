<template>
    <div :id="id" :style="{width, height}"></div>
</template>

<script>
import { Loader } from "@googlemaps/js-api-loader"
export default {
    props: {
        id: {
            type: String,
            default: "map"
        },
        lng: {
            type: Number | String,
            default: 108.2772
        },
        lat: {
            type: Number | String,
            default: 14.0583
        },
        width: {
            type: String,
            default: "100%"
        },
        height: {
            type: String,
            default: "400px"
        },
    },
    data () {
        return {
            map: null,
            value: '',
            service: null,
            markers: []
        }
    },
    created () {
        this.initMap()
    },
    watch: {},
    methods: {
         //初始化地图
        initMap () {
            let this_ = this
            const loader = new Loader({
                apiKey: "AIzaSyC7b2_5RxBtvTjnFPmLrzOa_nmXFdALQU8",//填写google控制台中的apikey
                version: "weekly",
                libraries: ["places", ""]
            });
            loader.load().then(() => {
                this.map = new google.maps.Map(document.getElementById("map"), {
                    center: { lat: Number(this.lat), lng: Number(this.lng) },
                    zoom: 2,
                    mapTypeControl: false,
                    fullscreenControl: false,
                    streetViewControl: false
                });
                this.service = new google.maps.places.PlacesService(this.map)
                let position
                if (this.lat == '' || this.lng == '' || this.lat == null || this.lng == null) {
                position = {
                    lat: 14.0583,
                    lng: 108.2772
                }
                } else {
                position = {
                    lat: Number(this.lat),
                    lng: Number(this.lng)
                }
                }
                const marker = new google.maps.Marker({
                position: { lat: position.lat, lng: position.lng },
                draggable: true,
                map: this.map,
                title: "",
                });
                google.maps.event.addListener(marker, 'dragend', function (event) {
                this_.mouseM(event)
                })
                this.markers.push(marker)
            });
        },
        //获取选择到的信息
        addMark (data) {
            let this_ = this
            let lat = data.geometry.location.lat()
            let lng = data.geometry.location.lng()
            let center = { lat: lat, lng: lng }
            this.map.setCenter(center)
            this.deleteMarkers()//清除之前添加的标记
            const marker = new google.maps.Marker({
                position: center,
                map: this.map,
                draggable: true,
                title: data.label
            });
            google.maps.event.addListener(marker, 'dragend', function (event) {
                this_.mouseM(event)
            })
            this.markers.push(marker)
        },
        // 根据拖拽确定坐标
        mouseM (data) {
            let markPosition = data.latLng.lng() + ',' + data.latLng.lat()
            this.$emit('markChange', markPosition)
            },
            //移除地图上的标记物
            deleteMarkers () {
            for (let i = 0; i < this.markers.length; i++) {
                this.markers[i].setMap(null)
            }
            this.markers = []
        },
        //地图搜索返回数据
        searchByName (searchStr) {
            return new Promise((resolve, reject) => {
                const request = {
                query: searchStr,
                fields: ["name"],
                };
                this.service.textSearch(request, (results, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && results) {
                    let data = this.dealCbData(results)
                    resolve(data);
                } else {
                    reject([]);
                }
                })
            });
        },
        //处理谷歌返回的数据
        dealCbData (data) {
            let arr = []
            for (let i = 0; i < data.length; i++) {
                let obj = { ...data[i] }
                obj.label = data[i].name
                obj.value = data[i].name + data[i].formatted_address
                arr.push(obj)
            }
            return arr
        },
    }
}
</script>

<style scoped>
</style>
