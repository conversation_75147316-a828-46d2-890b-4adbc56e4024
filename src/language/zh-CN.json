{"common": {"PleaseInput": "请输入", "PleaseSelect": "请选择", "Loading": "加载中", "personalSettings": "个人设置", "logOut": "退出登录", "query": "查询", "newlyBuild": "新建", "add": "添加", "delete": "删除", "batchDel": "批量删除", "update": "修改", "state": "状态", "operation": "操作", "tips": "提示", "info": "详情", "deploy": "分配", "cancel": "取消", "confirm": "确定", "areYouSureExit": "是否确定退出？", "setSuccess": "已设置", "notSet": "未设置", "set": "设置", "bindingSuccess": "已绑定", "unbound": "未绑定", "binding": "绑定", "operationSuccessful": "操作成功", "createTime": "创建时间", "normal": "正常", "disable": "禁用", "delete_current_option": "确认删除当前选项", "original": "原", "use_iconfont": "图标统一使用SVG Sprite矢量图标 ", "total": "全部", "supportedTip": "暂不支持", "selectDevice": "请选择设备", "clickToEnlarge": "点击查看大图", "pictureLoadingFailed": "图片加载失败", "passwordError": "密码错误", "OKToRestart": "确定重启", "WaitingForRestart": "等待重启中...", "RestartTimeout": " 重启超时", "modifiedTime": "修改时间", "exportData": "导出数据", "submit": "提交", "modifyTheSuccess": "修改成功", "configuration": "配置", "failure": "失败", "release": "发布", "nextStep": "下一步", "selectMaterial": "选择素材", "notSelectedectMaterial": "注：未选分组默认导出所有卡的sim信息", "exportingSIM": "导出SIM信息", "enterCardPackage": "请输入所属卡/包名...", "note": "注", "enterPackage": "请输入包名...", "noDetails": "无详情", "versionQuery": "版本查询", "CardSysterm5263": "版本查询需要CardSysterm5.2.6.3及以上版本支持", "uninstallCard": "没有选择卡，无法使用在线卸载功能", "packageName": "包名", "versionNumber": "版本号", "versionIdentifiers": "版本标识", "wrong": "本卡的连接状态有误，更新取消！", "upgradeWrong": "选中升级的所有卡连接状态都有问题，请检查后重试！", "cardsNotReturn": "部分卡不支持进度条返回，请求超时，升级结束！", "updateComplete": "更新完成！", "restartCards": "确定要重启这些卡吗？", "addTiming": "请添加定时", "Monday": "星期一", "Tuesday": "星期二", "Wednesday": "星期三", "Thursday": "星期四", "Friday": "星期五", "Saturday": "星期六", "Sunday": "星期日", "brightnessClears": "手动设置亮度会清除整个定时亮度和灵敏度", "fullBrightness": "最大亮度", "screenDuring": "注：设置时间内为开屏状态", "removeTiming": "清除定时", "clearSuccess": "清除成功", "notEnteredNotModified": "未输入的输入框为不修改，使用Conn_v11.1.1.1[261]-AIPS-release-2&4及以上版本未输入为设置为空", "Changing": "修改公司ID或realTime地址会导致卡下线，确定要修改吗？", "networkNTP": "自动校准时间需要网络和NTP地址", "Settings": "同步设置选中模式时，未填选项视为设置为空", "SynchronousMode": "同步模式", "zoneEmpty": "选中时区不能为空！", "synchronousState": "设置定时同异步的时间区间内为同步状态！", "beEmpty": "宽、高不能小于等于0，且不能为空！", "ModifyFailure": "修改失败", "programmeDetails": "节目任务详情", "showWidth": "节目宽度", "showHigh": "节目高度", "notOnPlatform": "查询失败，当前节目不在本平台发布", "allIntervals": "开启定时后未选择定时区间，则视为选中所有区间", "notSelected": "未选定", "true": "是", "false": "否", "name": "名称", "custom": "自定义", "MaximumNumberOfWords200": "字数上限200", "exportingSIMTips": "当前分组没有终端，建议选择其他分组", "language": "语言", "copy": "复制", "save": "保存", "saveAndExit": "保存并退出", "noMoreData": "没有更多数据了", "enable": "启用", "NotEnabled": "未启用", "AssignUsers": "分配用户", "manual": "使用手册", "addMedia": "+ 添加媒体", "reviewCurrentUsers": "请联系我司人员审核当前用户", "selectGroup": "选择分组", "selectingGroup": "当前分组", "unclassified": "未分组", "frontStep": "上一步", "search": "搜索", "CAT1": "CAT1", "DaHuaCamera": "大华摄像头", "GBCamera": "国标摄像头"}, "screen": {"simInformation": "sim卡信息", "networkState": "网络国家", "series": "系列号", "countries": "国家", "operationName": "操作名", "unknownState": "未知状态", "noCard": "没插卡", "PIN": "锁定状态，需要用户的PIN码解锁", "PUK": "锁定状态，需要用户的PUK码解锁", "PIN2": "锁定状态，需要网络的PIN码解锁", "readyState": "就绪状态", "InvalidState": "无效状态", "subscribe": "订阅", "choose": "选择", "supportZip": "版本文件只支持Zip格式！", "selectFile": "选择文件", "releaseTime": "发布时间", "versionInformation": "版本信息", "testVersion": "测试版本无版本详情！", "hardwareVersion": "硬件参数无版本详情！", "officialRelease": "正式版本发布", "testRelease": "测试版本发布", "hardwareRelease": "硬件参数发布", "sizeMore": "大小超过", "OnlyForZIPType!": "只能为ZIP类型！", "uploadEmpty": "上传文件不能为空！", "uploadFile": "上传文件", "fileTips": "仅可上传MP3、MP4、gif、png、jpg格式文件，MP3和图片类型不能超过100M，MP4类型不能超过150M", "fileTips1": "仅可上传MP3格式文件，大小不能超过20M", "picture": "图片", "video": "视频", "picturesOrVideos": "上传媒体文件类型只能为图片或视频!", "my": "我的", "pending": "待审批", "pageImprovement": "页面完善中", "errorDetails": "错误详情", "null": "无", "showProgress": "节目进度", "sendSuccess": "发送成功", "sendSuccessOffline": "已发送离线节目，控制卡72小时内上线自动发送节目", "failedProgress": "获取进度失败", "timesOut": "获取进度超时", "selectCard": "请选择控制卡", "reviewDetails": "审核详情", "satelliteNumber": "卫星数目", "autoBrightnessTable": "自动亮度表", "senorType": "传感器类型", "setAutoBrightnessTable": "设置自动亮度表", "getAutoBrightnessTable": "查询自动亮度表", "default255BrightnessTable": "默认255亮度表", "customizeBrightnessTable": "自定义亮度表", "otherSwitch": "其他开关", "customSwitchFunction": "定制开关功能", "programPlayDetail": "节目播放详情", "programPlayStatistic": "节目播放统计", "programPlaySum": "节目播放总数", "exportPlayLog": "导出日志", "log": "日志", "networkPort": "网口", "clickToCheck": "点击检测", "getPointCheckInfo": "获取点检信息", "imageSize": "图像尺寸", "badPointNum": "坏点数量", "queryPlayerState": "查询播放器状态", "playerStateRep": {"1": "初始化", "2": "定时节目结束", "3": "无待播放的节目", "4": "删除节目", "5": "处理节目中", "6": "暂无信息", "7": "节目可能有误", "8": "关屏", "9": "定点节目不在范围内"}, "fullColor": "全彩", "monochrome": "单色", "redBeadNumberPoints": "红色灯珠坏点数量", "greenBeadNumberPoints": "绿色灯珠坏点数量", "blueBeadNumberPoints": "蓝色灯珠坏点数量", "totalNumberOfBadPoints": "坏点总数", "redBadPointPosition": "红色灯珠坏点坐标", "greenBadPointPosition": "绿色灯珠坏点坐标", "blueBadPointPosition": "蓝色灯珠坏点坐标", "badPointPosition": "坏点坐标", "abscissa": "横坐标", "ordinate": "纵坐标", "noBadPoint": "无坏点", "pointCheckTips": "坏点检测仅支持单卡，请重新选择", "receivingCard": "接收卡", "setRecCardRelaySwitch": "设置接收卡继电器开关", "getRecCardSensorData": "获取接收卡传感器数据", "smoke": "烟雾", "smokeless": "无烟", "openCloseDoor": "开关门", "openDoor": "开门", "closeDoor": "关门", "relaySwitch": "继电器开关", "levelDetection": "电平检测", "accessHighLevel": "表示接入高电平", "noDeviceConnected": "表示无设备接入", "firstRoad": "第一路", "secondWay": "第二路", "thirdWay": "第三路", "fourthWay": "第四路", "theFifthRoad": "第五路", "sensorDataShareTips": "传感器数据共享仅支持单卡，请重新选择", "hour": "小时", "pointTable": "走点表", "pointTableTips": "请在专业人士的指导下进行操作", "pointTableTemplate": "走点表模板", "pleaseUploadPointTable": "请先设置走点表", "networkConfig": "网络配置", "hotspot": "热点", "wifiTip": "设置WIFI使能可能会改变网络状态导致下线，请谨慎操作", "apTip": "注意：请使用大小写字母和数字的组合密码，密码长度在8-20之间", "wifiList": "查询WiFi列表", "selectWifi": "请选择WIFI", "singleCardOperation": "该功能仅支持单卡操作"}, "sys": {"enable": "启用", "remarks": "请输入备注信息", "authenticationMode": "请选择验证方式", "emailVerification": "邮箱验证", "mobileVerification": "手机号验证", "verify": "验证方式", "notOpen": "未开启", "email": "邮箱", "mobile": "手机", "whetherAudit": "是否审核", "open": "开启", "close": "关闭", "companyId": "公司Id", "same": "公司ID与创建人公司ID一致", "roleEmpty": "用户角色不能为空", "alarmType": "报警类型", "alarmTime": "报警时间", "Iknown": "我已知晓", "currentBrowser": "当前浏览器不支持服务器发送的事件", "notReminded": "确认后将不再提醒", "visited": "您访问的页面", "find": "不存在", "url": "请检查网址", "previousPage": "返回上一页", "enterHome": "进入首页", "permissionOfTerminalGroups": "可控制的终端分组", "TheSuperAdminDoesNotRestrictUserGroups": "超级管理员不限制用户分组", "addOrUpdateAuth": "添加/修改认证", "isDefaultPassword": "是否设置默认密码"}, "login": {"dynamicCodeEntryTips": "当前用户未开启验证码验证方式，请先开启验证方式", "login": "登录", "passwordLogin": "密码登录", "username": "用户名", "password": "密码", "ForgetThePassword": "忘记密码", "newPassword": "新密码", "confirmPassword": "确认密码", "dynamicCodeEntry": "验证码登录", "pwdNotes": "建议使用八位数以上的密码", "origin_pwd_incorrect": "原密码不正确", "wrong_account_or_password": "账号或密码错误", "account_has_been_locked": "账号已锁定，请联系管理员", "updateEmail": "修改邮箱", "bindEmail": "绑定邮箱", "updateMobile": "修改手机号", "bindMobile": "绑定手机号", "sliderRight": "请点击按钮进行验证", "loading": "加载中", "passwordMore8": "密码长度必须大于8并且由大写字母、小写字母和数字共同组成", "user4To17": "用户名长度必须大于4，小于17 并且由数字或字母组成", "clickCodeMailbox": "点击获取验证码，信息将发送至安全邮箱", "clickCodePhone": "点击获取验证码，短信将发送至安全手机", "authenticationSuccessful": "验证成功", "securityVerification": "请先进行安全验证", "enterVerificationCode": "请输入验证码", "UserNameEmailMobile": "用户名/邮箱/手机号", "ScopeOfAuthority": "权限范围", "superAdministrator": "超级管理员", "PermissionDetails": "权限详情", "pleaseContactTheAdministrator": "请联系管理员"}, "register": {"register": "注册", "mobile": "手机号", "mailbox": "邮箱号", "code": "验证码", "getCode": "获取验证码", "remind": "建议使用微信扫码登录一键注册", "back": "返回登录", "prependResend": "在 ", "appendResend": " s 后重新发送", "personalInformation": "个人信息", "complete": "完成", "recommendedRegister": "建议使用微信扫码一键注册登录", "enterPassword": "请再次输入密码", "companyId": "公司Id，控制卡上线绑定公司Id，确定后不能随意变更", "companyName": "公司名称", "companyAddress": "公司地址", "companyPhone": "公司电话", "readAIPSAgreement": "请先阅读AIPS用户协议", "readAccepted": "我已阅读并接受", "AIPSAgreement": "AIPS用户协议", "registeredSuccessfully": "注册成功", "clickJumpOr": "点击跳转或", "loginDisplayed": "秒后自动跳转到登录界面", "readAcceptAIPSAgreement": "请阅读并接受 AIPS用户协议", "youAreRight": "您正在对", "resettingVerification": "进行重置密码操作,请先进行安全验证", "switchingAuthentication": "切换验证方式", "pleaseSet": "请设置", "passwordSecurity": "的新密码，建议使用数字、字母、字符的组合密码，提高密码安全等级", "passwordChangedSuccessfully": "密码修改成功", "verified": "实名认证", "idCardNumber": "身份证号", "companyLicense": "上传有效年检期内的《企业法人营业执照》图片", "cachet": "上传加盖企业公章的《授权证明》图片", "idCardFront": "上传身份证正面图片", "idCardReverse": "上传身份证反面图片", "pleaseReadAndCheckAIPS": "请您阅读并勾选《AIPS用户协议》", "personal": "个人", "company": "企业", "CertifiProgress": "认证进度", "waitForAdminAuth": "等待管理员认证", "DownloadLicenseCertificate": "下载《授权证书》"}, "ChangePWD": {"ChangePassword": "修改密码", "remind": "只有绑定了手机号或者邮箱的用户才能找回密码"}, "nav": {"首页": "首页", "智慧屏幕": "智慧屏幕", "智慧广播": "智慧广播", "气象环境": "气象环境", "智慧监控": "智慧监控", "客流统计": "客流统计", "客流统计V1": "客流统计V1", "系统管理": "系统管理", "设备管理": "设备管理", "节目管理": "节目管理", "媒体库": "媒体库", "日志管理": "日志管理", "远程控制日志": "远程控制日志", "播放日志": "播放日志", "用户管理": "用户管理", "菜单管理": "菜单管理", "角色管理": "角色管理", "用户日志": "用户日志", "登录日志": "登录日志", "系统日志": "系统日志", "设备状态": "设备状态", "广播任务": "广播任务", "分组管理": "分组管理", "审批管理": "审批管理", "公告管理": "公告管理", "终端列表": "终端列表", "智慧物联": "智慧物联", "智慧照明": "智慧照明", "CAT1照明": "CAT1照明", "控制卡照明": "控制卡照明", "电能管理": "电能管理", "视频监控": "视频监控", "付费服务": "付费服务", "订购服务": "订购服务", "订单": "订单", "SIP账号管理": "SIP账号管理", "监控回放": "监控回放", "人群聚集": "人群聚集", "订单管理": "订单管理", "报警管理": "报警管理", "报警记录": "报警记录", "通话记录": "通话记录", "智慧交通": "智慧交通", "交通信息": "交通信息", "雷达测速": "雷达测速", "WIFI AC": "WIFI AC", "概览": "概览", "AC管理": "AC管理", "密码管理": "密码管理", "认证审核": "认证审核", "通知策略": "通知策略", "通知日志": "通知日志", "十字箭头": "十字箭头", "设备白名单": "设备白名单", "摄像头监控": "摄像头监控"}, "validate": {"account_cannot_empty": "帐号不能为空", "password_cannot_empty": "密码不能为空", "confirm_password_cannot_empty": "确认密码不能为空", "new_pwd_cannot_empty": "新密码不能为空", "email_cannot_empty": "邮箱不能为空", "mobile_cannot_empty": "手机号不能为空", "code_cannot_empty": "验证码不能为空", "roleName_cannot_empty": "角色名称不能为空", "menuURL_cannot_empty": "菜单URL不能为空", "superior_menu_cannot_empty": "上级菜单不能为空", "menu_name_cannot_empty": "菜单名称不能为空", "group_name_cannot_empty": "分组名称不能为空", "group_type_cannot_empty": "分组类型不能为空", "audit_name_cannot_empty": "审核名称不能为空", "resource_type_cannot_empty": "资源类型不能为空", "status_cannot_be_empty": "状态不能为空", "approval_comments_cannot_blank": "审批意见不能为空", "program_name_cannot_empty": "节目名称不能为空", "the_new_password_is_inconsistent": "确认密码与新密码不一致", "the_password_is_inconsistent": "确认密码与密码不一致", "incorrect_email_format": "电子邮件格式不正确", "code_format": "请输入六位字符", "mobile_format": "手机号格式错误", "mobile_code_empty": "手机验证码不能为空", "email_code_empty": "邮箱验证码不能为空", "company_id_empty": "公司Id不能为空", "not_empty": "不能为空", "alarmAddress_not_empty": "报警地址或呼叫账号不能为空", "alias_cannot_empty": "别名不能为空", "company_name_cannot_empty": "公司名称不能为空", "company_address_cannot_empty": "公司地址不能为空", "company_phone_number_cannot_empty": "公司电话不能为空", "id_card_number_cannot_empty": "身份证号不能为空", "id_card_number_format_wrong": "身份证号格式有误", "validateTip": "为了您账户的安全，现需要您绑定验证方式。"}, "role": {"role": "角色", "roleName": "角色名称", "remark": "备注", "authorization": "授权", "subAdmin": "子管理员", "normalUser": "普通用户"}, "menu": {"name": "名称", "parentName": "上级菜单", "icon": "图标", "type": "类型", "orderNum": "排序号", "url": "菜单URL", "perms": "授权标识", "mainMenu": "主菜单", "parentMenuName": "父级菜单", "permsTips": "多个用逗号分隔, 如: user:list,user:create", "menu": "菜单", "DirectoryMenu": "目录菜单", "button": "按钮", "HomeDirectoryMenu": "主目录菜单"}, "log": {"user_name_user_action": "用户名 / 用户操作", "user_action": "用户操作", "request_method": "请求方法", "request_parameters": "请求参数", "execution_time": "执行时长(毫秒)", "ip_address": "IP地址", "commandId": "命令ID", "response_result": "响应结果", "schedule": "进度", "ReasonForFailure": "失败原因", "RequestTimedOut": "请求超时", "requestSucceeded": "请求成功", "connectionDoesNotExist": "连接不存在", "Disconnect": "连接断开", "connectionClosed": "连接关闭", "requestException": "请求异常"}, "group": {"name": "分组名称", "type": "分组类型", "addingAGroup": "添加分组", "pleaseEnterAGroupName": "请输入分组名称", "addingSubgroup": "添加子分组", "pleaseDeleteTheSubgroupsFirst": "请先删除子分组"}, "cardDevice": {"deviceName": "设备名称", "online": "在线", "networkType": "网络类型", "resolvingPower": "分辨率", "programTask": "节目任务", "broadcastTask": "广播任务", "screenStatus": "屏幕状态", "lastOffline": "最后下线", "queryTerminalInfo": "查询终端信息", "selectedCard": "已选卡", "brightness": "亮度", "volume": "音量", "locked": "锁定", "terminalInfoFirst": "请先获取终端信息", "width": "宽度", "height": "高度", "synchronous": "同步", "asynchronous": "异步", "temperature": "温度", "number": "序号", "NoSim": "无sim卡信息", "fireWare": "固件版本"}, "operation": {"settingMode": "设置方式", "connectionLog": "连接日志", "LEDscreen": "LED屏幕", "screenshot": "屏幕截图", "liveVideo": "视频直播", "screenSwitch": "屏幕开关", "timingSwitch": "定时开关", "screenBrightness": "屏幕亮度", "autoBrightness": "自动亮度", "timingBrightness": "定时亮度", "volumeControl": "音量控制", "timingConfig": "对时配置", "connConfig": "连接配置", "syncAndAsyncConfig": "同异步配置", "alarmSwitch": "报警开关", "onlineUpdate": "在线升级", "restartSys": "重启系统", "backgroundPlayback": "播放器背景", "backupScreenParam": "备份屏参", "restoreScreenParam": "恢复屏参", "hardwareStatus": "硬件参数", "manualconfigurationorquery": "手动配置/查询", "scheduledconfigurationorquery": "定时配置", "thealias": "别名", "webServerAddress": "WEB服务器地址", "thecompany": "公司", "realtimeaddress": "Realtime地址", "remove": "清除", "volumeset": "批量设置", "batchquery": "批量查询", "group": "分组", "exportSIMInfo": "导出SIM信息", "clearProgram": "清除节目", "clearTask": "清除任务", "callAddress": "喊话地址配置", "alarmConfig": "报警配置", "clearBroadcastTask": "清除广播任务", "queryOrClearTiming": "查询或清除定时", "queryTiming": "查询定时", "screenControl": "屏幕控制", "broadcastControl": "广播控制", "monitoringControl": "监控控制", "meteorologicalEnvironmentControl": "气象环境控制", "passengerFlowStatistics": "客流统计", "lightingControl": "照明控制", "setSIPServerAddress": "设置SIP服务器地址", "getSIPServerAddress": "查询SIP服务器地址", "SIPServerAddress": "SIP服务器地址", "AlarmEquipmentMacAddress": "报警设备mac地址", "AlarmEquipmentIpAddress": "报警设备ip地址", "SetAlarmAddress": "设置报警地址", "GetAlarmAddress": "查询报警地址", "AlarmAddress": "报警地址", "CallAccount": "呼叫账号", "AlarmVolume": "报警音量", "LightingLevel": "照明亮度", "LightingSwitch": "照明开关", "Register_SIP_account": "注册报警账号", "getGspInfo": "查询gps信息", "gpsInfo": "GPS信息", "recordingFile": "录音文件", "fullSizeScreenshotOfAndroid": "安卓全尺寸截图", "customSwitch": "定制开关", "ThirdPartyAdvertising": "第三方广告", "BadPointDetection": "坏点检测", "playerState": "播放器状态", "NumberOfCardsReceived": "接收卡数量", "sensorDataShare": "传感器数据共享", "dataShare": "数据共享", "dataRefreshCycle": "数据刷新周期", "sharedDataKey": "共享数据密钥", "curFlow": "当前客流", "isEnableCurFlow": "是否开启当前客流", "flowAddress": "客流量地址", "showLocation": "显示位置", "showPrefix": "显示前缀", "leftTop": "左上", "rightTop": "右上", "leftBottom": "左下", "rightBottom": "右下", "curFlowTip": "客流量地址为空时，则使用平台默认的地址", "detectingBadPixels": "正在坏点检测", "uploadZip": "上传Zip", "versionDelete": "版本删除", "hdvancedConfig": "高级配置", "hardwareConfig": "硬件配置", "realTimeSet": "realTime设置", "networkConfig": "网络配置", "clearBroadcast": "清除广播", "callVolumeControl": "通话音量控制", "queryProgramName": "查询节目名", "list": "列表", "new": "新增", "oneClickOperateScreen": "一键关屏", "systemDisplay": "系统分辨率", "checkAddress": "配置检测地址"}, "tips": {"brightness": "注：亮度为1-255", "volume": "注：音量为0-15", "alarmVolume": "注：音量为1-9", "liveVideo": "支持rtmp,rtsp协议，请先安装live", "liveVideo1": "OFF 关闭直播，ON 开启直播", "liveVideo2": "测试地址", "screenSwitch": "注：清空定时重新操作开关屏即可。", "screenTiming": "注：查询定时开关屏(conn10.0.5T或以上版本支持)", "autoBrightness": "注：本功能从CardSystem-v3.6.0起支持，灵敏度必需为0到100", "autoBrightness1": "亮度根据传感器数据变化自动调整(conn10.0.5T或以上版本支持)", "autoBrightness2": "最大亮度是64的卡，最小亮度可以配置为１%或适当值；最大亮度是255的卡，最小亮度必须配置为36%或以上，否则亮度会偏低。", "timingBrightness": "注：该设定时间内为设定亮度，设定时间外为默认亮度。例如设定默认亮度为80%，设定亮度为20%，时间范围是8:00-17:00，则时间范围内亮度为20%，其他时间内为默认亮度80%", "manualconfigurationorquery1": "仅适用于M70和M80系列卡", "manualconfigurationorquery2": "日期范围内的为同步模式；仅适用于M70和M80系列卡,cardSystem5.2.5.6-8版本及以上", "widthheighterror": "屏幕的宽度和高度都不能低于0像素", "noTenteredNotModified": "未输入此项视为不修改此项", "approval": "根据定义的流程进行审核，流程排序 数字越小排在越前面", "advancedParameter": "注： 高级参数设置接口(conn10.0.5T或以上版本支持)", "cardSelected": "已选中卡号", "cardNameSelected": "已选中终端", "numberEmpty": "选中的卡号数量不能为空", "progressBar": "注：更新进度条为展示更新过程，实际更新结果以卡的实际状态为准！", "upgradeFeatureSelectCard": "没有选择卡，无法使用在线升级功能！", "UninstalledSuccessfully": "状态: 卸载成功！", "uninstallFeatureSelectCard": "没有选择卡，无法使用在线卸载功能！", "SelectUninstall": "请选择要卸载的组件", "selectedNotExist": "选中标签错误，不存在", "backgroundTips": "需素材图片分辨率和屏体分辨率一致，否则报错", "releaseTips": "不在线的卡将离线发送，回复发送成功，不回复进度条，控制卡72小时内上线自动发送节目", "releaseTips1": "开启播放器日志，节目发送成功后将保存每张卡的播放日志", "releaseTips2": "开启播放器日志后，控制卡存在节目时会在设置的间隔时间上传日志到平台", "SIPAddress": "注：如果没有设置过Sip服务器地址，则必须设置Sip地址后，才能使用对讲和报警功能，设置完地址后，请重启控制卡。", "SIPAddress1": "输入框为空使用当前平台作为SIP服务器地址或自定义SIP服务器地址", "alarmConfig": "建议将平台地址设置为报警地址", "CustomCanUseNetworkAddress": "自定义可以使用网络地址", "networkAddressWhenCustomizing": "自定义时请输入网络地址", "broadcastTask": "注：未配置定时任务，重启设备以后，广播会被自动清除，不保存任务!", "SIPTips": "可使用此账号与控制卡进行SIP通话，如果当前账号已注册账号只能进行修改", "broadcastTaskRelease": "注：发布广播任务前，请确保没有正在使用喊话功能", "ModifyTerminal": "提示:修改终端别名，点击智慧物联，切换到列表模式，点击修改", "senorBrightnessTable": "该功能需要systemCore版本为********及以上。使用步骤：先设置一下传感器灵敏度值，再通过EasyBoard里默认的BrightnessTable.xlsx文件上传来修改，里面的数值可以改变，但是文件格式不要变，否则会操作失败！上传文件时，只能选择同种传感器的卡；否则将会失败！", "customSwitchTip": "温馨提示：此开关打开则启用定制屏幕开关，原始屏幕开关将无效，关闭此开关则原始屏幕开关生效，对此有疑问请跟相关人员确认！", "configAdTip": "是否确定接入第三方广告，我们平台不保证第三方接入内容合法合规，请谨慎操作.", "configAdTip1": "是否启用", "configAdTip2": "您是否确定将控制卡开放给第三方平台使用？", "configAdTip3": "此功能需要升级对应APK，详情请联系我司工作人员!!!", "groupTip": "取消后将展示所有设备", "passwordIsWeak": "您的密码过弱，请修改密码。密码至少包含一个英文大写字母、一个英文小写字母、一个数字，且长度至少为9位。", "authTip": "工作人员将在2-3个工作日内审核，审核通过后，可正常使用平台。", "pointCheckTip": "检测完毕后，请耐心等待一会，以确保检测完成，获取到正确的坏点检测信息", "pointCheckCard": "使用方案请联系我司技术人员", "playLogsExportTip": "导出日志时，如未选择日期则导出当天日志，否则导出指定日期内的所有日志。", "oneClickOperateScreenTip": "此操作将关闭所有在线的屏幕"}, "file": {"name": "文件名称", "type": "文件类型", "status": "文件状态", "size": "文件大小", "UploadProgress": "上传进度", "download": "下载", "thumbnail": "缩略图", "checkPending": "待审核", "approved": "审核通过", "auditFailed": "审核未通过", "under_review": "审核中", "examine": "审核", "attachment": "请选择附件", "attachment1": "点击或者拖拽到此次上传文件", "auditTime": "审核时间", "file": "文件", "ApprovalComments": "审批意见", "upload": "上传", "update": "更新", "toView": "查看", "WithoutPermission": "无权限", "uninstall": "卸载", "SerialNumber": "编号", "OnlineUpdate": "在线更新", "TheSize": "大小", "VersionLog": "版本日志", "LogDetails": "日志详情", "Onlineupgrade": "在线升级中...", "Waitingupdates": "等待更新中...", "Allcards": "所有操作更新的卡全部无法更新，请检查问题", "DownloadComplete": "下载完成，解压后更新", "NotSupported": "不支持进度条展示,加载中...", "UpdateSuccessful": "更新成功！", "UpdateFailed": "更新失败！", "ReadyDownload": "准备下载中！", "ConnectionFailed": "连接失败，请检查设备！", "ThreeSeconds": "更新完毕，三秒后关闭更新小窗口", "YouCanOnlyUploadUpTo5Files": "最多只能上传5个文件", "audio": "音频", "fileOverSize": "文件超过限制大小", "fileLimit": "文件仅支持docx格式", "fileVersion": "文件版本", "fileLimitPdfAndVideo": "文件仅支持pdf和mp4"}, "card": {"cardId": "序列号", "setTiming": "设置定时", "getTiming": "查询定时", "noTiming": "无定时", "timing": "定时", "notSpecified": "未指定", "dateType": "日期类型", "DateRange": "日期范围", "startDate": "开始日期", "endDate": "结束日期", "timeType": "时间类型", "timeFrame": "时间范围", "startTime": "开始时间", "endTime": "结束时间", "SpecifyWeek": "指定星期", "WeekRange": "星期范围", "PleaseScheduledTask": "请指定定时任务类型", "sensitivity": "灵敏度", "Minbrightness": "最小亮度", "defaultBrightness": "默认亮度", "timingBrightness": "定时亮度", "timedVolume": "定时音量", "defaultVolume": "默认音量", "cardVoltage": "卡电压", "externalVoltage1": "外部电压1", "externalVoltage2": "外部电压2", "externalVoltage3": "外部电压3", "externalVoltage4": "外部电压4", "doorOpen": "门被打开", "version": "版本", "humidity": "湿度", "temperature": "温度", "smokeWarning": "烟雾警告", "querySuccessful": "查询成功，为您展示数据", "queryFailed": "查询失败，无法为您展示数据", "screenWidth": "屏幕宽度(像素)", "screenHeight": "屏幕高度(像素)", "screenAlias": "屏幕别名", "genericVersion": "通用版本", "notChosenCard": "未选卡", "TestVersion": "测试版本", "rebootNow": "立即重启", "monitorTip": "最多同时播放6个监控画面，请重新选择", "picture-in-picture": "画中画", "pictureTip": "坐标与宽高请勿超出屏幕分辨率可视范围，重新设置需要先关闭画中画", "coordinate": "坐标", "pictureSize": "画面大小", "checkAddressTip": "开启后填写媒体审核服务器地址，用于设备端审核媒体内容，地址为空使用默认地址", "mediaContentReview": "媒体内容审核", "realtimeReview": "实时审核", "realtimeReviewTips": "开启实时审核存在流量消耗", "clearPrompt": "清除提示", "interval": "间隔时间"}, "approval": {"auditName": "审核名称", "auditType": "审核类型", "approvalProcess": "审批流程", "Reviewer": "审核人", "order": "顺序", "mediaResources": "媒体资源", "ProgramType": "节目类型", "BroadcastMediaResources": "广播媒体资源", "BroadcastTaskResources": "广播任务资源", "noAudit": "无需审核", "approved": "审核通过", "auditFailed": "审核未通过", "select_at_least_one_reviewer": "至少选择一位审核人", "approver_cannot_blank": "审核人不能为空", "approval_order_cannot_blank": "审核顺序不能为空", "InsufficientUsers": "用户不足", "clickAudit": "一键审核", "batchReview": "批量审核", "auditMemo": "节目存在问题，请调整后再提交。"}, "program": {"program": "节目", "type": "节目类型", "name": "节目名称", "ordinaryProgram": "普通节目", "insertProgram": "插播节目", "totalSize": "总大小", "state": "资源状态", "ProgramList": "节目列表", "ProgramInfo": "节目信息", "ComponentProperties": "组件属性", "ProgramProperties": "节目属性", "PlaybackMode": "播放方式", "EntryEffects": "进场特效", "DurationMobilizationEffect": "进场特效时长 (s)", "AppearanceEffects": "出场特效", "DurationAppearanceEffect": "出场特效时长 (s)", "StartPlaybackTime": "起始播放时间 (s)", "DurationContinuousDisplay": "持续显示时长 (s)", "region": "区域", "upper": "上", "left": "左", "width": "宽", "height": "高", "BasicProperties": "基础属性", "background": "背景", "pellucidity": "透明度", "DisplayDackground": "显示背景", "open": "开", "close": "关", "BackgroundColor": "背景色", "DisplayHourScale": "显示小时刻度", "HourScaleColor": "小时刻度颜色", "ShowMinuteScale": "显示分钟刻度", "MinuteScaleColor": "分钟刻度颜色", "ScaleStyle": "刻度风格", "IntegerScaleDigitalDisplay": "整点刻度显示数字", "PointerStyle": "指针风格", "ClockPointerColor": "时钟指针颜色", "MinutePointerColor": "分钟指针颜色", "SecondPointerColor": "秒钟指针颜色", "DisplaySecondHand": "显示秒针", "up": "向上", "down": "向下", "play": "播放", "times": "次", "PleaseEnterContent": "请输入内容", "text": "文本", "DigitalClock": "数字时钟", "analogClock": "模拟时钟", "EnvironmentalMonitoring": "环境监测", "weather": "天气", "Multi-materialWindow": "多素材窗口", "html": "网页", "weburl": "网页地址", "enterTime": "请输入时间", "Multi-material": "多素材", "empty": "清空", "oneLevelUp": "向上一层", "oneLevelDown": "向下一层", "layerOnTop": "图层置顶", "bottomLayer": "图层置底", "FullScreen": "全屏铺满", "pageProperties": "页面属性", "effectiveDate": "有效日期", "PlayProperties": "播放属性", "planSchedule": "计划时间表", "sun": "日", "one": "一", "two": "二", "three": "三", "four": "四", "five": "五", "six": "六", "clockProperties": "时钟属性", "PleaseSelectATimeZone": "请选择时区", "year": "年", "month": "月", "day": "日", "hour": "时", "Minute": "分", "Second": "秒", "Week": "星期", "AM": "上午", "PM": "下午", "fourYears": "四位年", "12HourClock": "12小时制", "morningAfternoon": "上午/下午", "style": "风格", "dateStyle": "日期风格", "timeStyle": "时间风格", "displayStyle": "显示风格", "singleLine": "单行", "Multi-line": "多行", "fontSettings": "字体设置", "fontSize": "字体大小", "fontColor": "字体颜色", "PlayTime": "播放时长", "specialEffects": "特效", "specificFrequency": "特效频率", "blink": "闪烁", "breathe": "呼吸", "MonitoringProperties": "监测属性", "compensate": "补偿", "windSpeed": "风速", "windDirection": "风向", "noise": "噪音", "atmosphericPressure": "大气压强", "rainfall": "雨量", "radiation": "辐射", "lightIntensity": "光照强度", "DisplayMode": "展示方式", "stayLeft": "靠左", "Centered": "居中", "KeepRight": "靠右", "singleLineScroll": "单行滚动", "speed": "速度", "ms/pixel": "毫秒/像素", "refreshCycle": "刷新周期", "minute": "分钟", "fileProperties": "文件属性", "Multi-MaterialBasicProperties": "多素材基础属性", "mediaList": "媒体列表", "SelectedMaterialInformation": "所选素材信息", "HourMarkColor": "小时刻度颜色", "minuteScaleColor": "分钟刻度颜色", "hourHandColor": "时针颜色", "minuteHandColor": "分针颜色", "pointerColor": "指针颜色", "backgroundColor": "背景顏色", "static": "静态", "scroll": "滚动", "turnPages": "翻页", "total": "总", "Page": "页", "preview": "预览", "stopPreview": "停止预览", "TextEditor": "文本编辑", "province": "省份", "Multi-material_text": "请在右边添加媒体，可以加入多个不同媒体，LED显示屏将按列表先后顺序播放。", "streaming": "流媒体", "direction": "滚动方向", "ToTheLeft": "向左", "upward": "向上", "ToTheRight": "向右", "addText": "添加文本", "liveStreamAddress": "直播流地址", "deviceAddress": "设备地址", "deviceAddrTip": "默认使用当前地址（https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey），请勿随意修改", "deviceKey": "设备密钥", "deviceKeyTip": "设备密钥平台用户填入指定设备密钥，下发节目在屏中展示指定设备的人流信息。", "CustomHTML": "自定义HTML", "CustomHTMLTip1": "为设备序列号", "CustomHTMLTip2": "为时间", "CustomHTMLTip3": "为今日进入总人数", "CustomHTMLTip4": "为该小时段内进入人数", "CustomHTMLTip5": "为历史总进入人数", "CustomHTMLTip6": "为今日离开总人数", "CustomHTMLTip7": "为该小时段内离开人数", "CustomHTMLTip8": "为历史总离开人数", "flowStatistics": "人流统计", "weatherTip1": "当前温度", "weatherTip2": "AQI（空气质量指数）", "weatherTip3": "当天日期（包含实时温度）", "weatherTip4": "当天天气", "weatherTip5": "当天最高温度", "weatherTip6": "当天最低温度", "weatherTip7": "当天风向", "weatherTip8": "当天风力", "weatherTip9": "当天天气图片，格式：img-宽度-高度", "weatherTip10": "以上%{}里的yesterday代表昨天，arr.0代表当天，1代表明天，2代表后天，3代表大后天，4代表大大后天", "timeType": "时间类型", "timeTypeTip": "修改类型会改变自定义HTML", "HDMITypeDescription": "HDMI类型说明", "HDMIDescription1": "1、HDMI类型节目目前仅支持m70系列，发送成功后，请查看显示屏实际显示情况，具体显示情况以显示屏为准。", "HDMIDescription2": "2、如果实际显示效果有误，请先检查HDMI线插入情况，或对应软件版本是否有误。检测无误后可尝试重发，如果当前控制卡为HDMI画中画模式需要先设置为同步才能重新设置，具体使用方案请联系我司技术人员。", "text-to-speech": "文字转语音", "addProgramTips": "制作普通节目下发成功后覆盖原节目，制作插播节目下发成功后不覆盖原节目，只在插播时间段内播放插播节目", "enablePlayerLog": "是否开启播放器日志", "playLog": "播放日志", "timeInterval": "时间间隔（分钟）", "discount": "每段长度", "isDiscount": "是否打折", "discountText": "每段分隔长度，多个用逗号隔开。如：256,256,128", "segmentation": "分割", "PleaseEnterDiscountWidth": "请输入打折宽度", "PleaseEnterTheCorrectContentFormat": "请输入正确的内容格式", "multi-picture": "多图片", "PleaseSelectPictureVideoSplit": "请选择图片或视频进行分割", "totalWidthDiscountCannotWidth": "打折总长度不能大于素材大小", "sensorsShareData": "传感器共享数据", "broadcastSort": "播报排序", "horizontal": "横排", "verticalRow": "竖排", "discountMode": "打折模式", "level": "水平", "vertical": "垂直", "negativeIon": "负氧离子", "zoomIn": "放大", "zoomOut": "缩小", "materialCycle": "素材循环", "refreshSec": "刷新间隔", "zoom": "缩放", "offset": "偏移", "scale": "拉伸"}, "setTime": {"timeZone": "时区/时间", "y60Channels": "注：该功能需要CardSystem_v5.2.6.3及以上版本支持！", "theTimeZone": "时区", "setUpThe": "设置", "query": "查询", "computerTime": "校准时钟到计算机时间", "ledTime": "查询LED设备当前时间", "queryFails": "查询失败！", "versionCardSystem": "请检查CardSystem的版本！", "deviceTimeZone": "设备时区", "setupFailed": "设置失败", "setupSuccess": "设置成功", "connectTo485": "是否接入485", "calibrationFailure": "校准失败", "successfulCalibration": "校准成功", "querySuccessful": "查询成功", "synchronizationSettings": "同步设置", "model": "模式", "masterSlave": "主/从", "IdentificationCode": "识别码", "timeOffset": "时间偏移(毫秒)", "screenBrightness": "屏幕亮度", "volume": "音量", "screenSwitch": "屏幕开关", "synchronizationInterval": "同步间隔", "lastSynchronousTime": "最后同步时间", "minTime": "(分钟/次)", "main": "主", "from": "从", "masterSlaveMode": "主从模式", "NTPpathNull": "设置NTP路径为空时，会连接超时，但设置成功！", "serverAddress": "NTP服务器地址", "selectA": "请选择一种主从模式", "selectATime": "请选中一个时区"}, "synchronous": {"unknownError": "未知错误", "doesNotExist": "卡的网络状态报错并且请检查CardSystem的版本", "format": "没有配置定时"}, "home": {"totalNumber": "设备总数", "onlineRate": "在线率", "number": "数", "brightScreen": "亮屏率", "operating": "累计操作量", "program": "累计节目量", "switchDate": "切换日期", "month": "月份", "Announcement": "公告", "determined": "待定", "programmeStatistics": "节目创建统计", "operationStatistics": "用户操作统计", "releasePeople": "发布人", "date": "日期", "noMoreAnnouncements": "没有更多公告了！", "show": "节目", "by": "审核总量", "operat": "操作量", "operatingSpeed": "操作速度(毫秒)", "Reviewrate": "审核通过率", "statistics": "节目发布统计", "cardNumber": "卡号", "releaseAmount": "发布总量", "successRate": "成功率", "totalSuccess": "成功总量", "successful": "成功", "failure": "失败", "founder": "创建人", "TotalAverageMilliseconds": "总平均毫秒数", "great": "优", "good": "良", "center": "中", "poor": "差", "NaN": "NaN", "clickRefresh": "点击刷新", "warningNotice": "报警通知", "temperatureWarning": "温度预警", "humidityWarning": "湿度预警", "voltageWarning": "卡电压预警", "voltage1Warning": "外部电压1预警", "voltage2Warning": "外部电压2预警", "doorOpenWarning": "门被打开预警", "smokeWarning": "有烟雾预警", "unknownWarning": "未知报警", "temporarilyNoData": "暂无数据", "showsSentProgram": "显示发送过节目的卡", "announcementDetails": "公告详情", "policeDetails": "报警详情", "untreated": "未处理", "haveDeal": "已处理", "noMoreCalls": "没有更多报警了！", "refreshSuccessful": "刷新成功！", "DynamicLargeScreen": "智慧终端可视化平台", "piece": "个", "EquipmentStatistics": "设备统计", "unitPCS": "单位：个", "PoleStatistics": "终端统计", "ControlStatistics": "控制统计", "UnitTimes": "单位：次", "TotalNumberOfPoles": "终端总数"}, "announcement": {"titleText": "公告标题/内容", "title": "标题", "enterTitle": "请输入公告标题", "content": "内容", "enterContent": "请输入公告内容", "also": "还可以输入", "character": "个字符"}, "resetPWD": {"resetPassword": "重置密码", "accountNumber": "输入帐号", "repairMethod": " 选择修复方式", "changePassword": "修改密码", "success": " 成功", "enterResetPassword": "请输入要重置密码的帐号"}, "police": {"notOpenSettingsNull": "不点击开启的视为设置为空", "monitoringItems": "监控项", "hasBeenOpen": "已开启", "lower": "下限", "ceiling": "上限", "haveSmoke": "有烟雾", "openDoorAlarm": "打开门报警", "turnSmokeAlarm": "打开烟雾报警", "checkCardSysterm": "无法解析操作，请优先检查CardSysterm版本", "isNotOpened": "卡的连接状态错误", "sureToSetAlarmThresholds": "确定设置报警阈值吗？", "upperAndLowerEmpty": "已开启的监控项，上下限不能为空", "numEmpty": "已开启的监控项，上下限输入类型必须为数字", "upperGreaterLower": "已开启的监控项，上限必须大于下限", "materialLibrary": "素材库"}, "hardware": {"timeQuery": "备份参数需要时间，如果第一时间查询不到备份文件，请稍后尝试刷新！", "restoreParam": "恢复参数", "backupParameter": "备份参数", "hardwareStatus": "硬件状态", "restore": "恢复", "akeyBackup": "一键备份", "backupSuccessful": "备份成功", "backupSuccessful1": "当前版本不回复进度", "BackingUp": "正在备份中。。。请稍等", "selectionCard": "没有选择卡，无法使用硬件参数功能", "parameterRecovery": "参数恢复中...", "waitingRecover": "等待恢复中...", "namePackage": "所属卡/包名", "restoreCancel": "本卡的连接状态有误，恢复取消！", "readyRecovery": "准备恢复中!", "tryAgain": "选中恢复的所有卡连接状态都有问题，请检查后重试！", "recoveryComplete": "恢复完成！", "afterRecovery": "恢复完毕，三秒后关闭更新小窗口", "Recovering": "正在恢复中...", "timesOut": "部分卡不支持参数恢复，请求超时，恢复结束！"}, "el": {"colorpicker": {"confirm": "确定", "clear": "清除"}, "image": {"error": "图片加载失败"}, "table": {"emptyText": "暂无数据"}, "pagination": {"total": "总共", "pagesize": "条/页", "goto": "前往", "pageClassifier": "页"}}, "task": {"name": "任务名称", "isCycle": "是否循环", "cycleIndex": "循环次数", "InfiniteLoop": "无限循环", "task": "任务", "type": "任务类型", "text": "文本内容", "voiceName": "声名", "speed": "语速", "pitch": "语调", "femaleVoice": "女声", "maleVoice": "男声", "textToLanguage": "文字转语音", "media": "媒体", "plays": "播放次数", "selectMedia": "选择媒体", "TemplateContent": "模版内容", "ImportTemplate": "导入模版", "import": "导入", "normal": "正常", "faster": "较快", "fast": "快速", "playTypes": "播放方式", "specifyPlays": "指定播放次数", "specifyPlayTime": "指定播放时间", "isTiming": "是否开启定时", "allTime": "播满时间段", "inStream": "是否为插播任务", "normalTask": "普通任务", "inStreamTask": "插播任务", "clearInStream": "仅清除插播任务"}, "lamp": {"poleName": "终端名称", "broadcast": "广播", "monitor": "监控", "environment": "气象环境", "lighting": "照明", "Passenger": "客流统计", "longitude": "经度", "latitude": "纬度", "ChooseTargeting": "选择定位", "LoadingPositioning": "加载定位中", "FailedToGetLocationInformation": "获取定位信息失败", "online": "在线", "offline": "离线", "targetingIsSet": "已设置定位", "targetingNotSet": "未设置定位", "listMode": "列表模式", "mapMode": "地图模式", "updateTime": "更新时间", "TheSensorIsNot": "当前时间段未接入传感器", "getMapException": "获取地图异常", "NoLocationData": "无定位数据", "PleaseEnterThePoleNameOrdeviceID": "请输入终端名/设备ID", "radioState": "广播状态", "latAndLngNotEmpty": "经纬度不能为空", "gpsUploadState": "gps上传"}, "broadcast": {"SIPAddress": "确定设置当前平台服务器地址作为SIP服务器地址", "customSIPAddressStart": "确认设置", "customSIPAddressEnd": "作为SIP服务器地址", "radioState1": "仅支持广播", "radioState2": "支持广播、喊话，不支持报警", "radioState3": "支持广播、报警，不支持喊话", "radioState4": "支持广播、喊话、报警", "SIP_account": "SIP账号", "multicastAddress": "组播地址", "selectDate": "请选择日期", "pauseOrOpenBroadcast": "暂停/开启广播任务", "broadcastInfo": "广播详情", "broadcastProgramState": "广播节目状态", "paused": "暂停中", "playing": "播放中", "haveProgram": "是否有实时节目", "playMode": "播放模式", "focusMode": "焦点模式", "fallingTone": "降音", "mute": "静音", "noProgram": "暂无节目", "noBroadcast": "暂无广播"}, "meteorological": {"temperatureSubText": "温度为--时表示查询出现异常或无数据", "Illuminance": "光照度", "humiditySubText": "湿度为--时表示查询出现异常或无数据", "noiseSubText": "噪音为--时表示查询出现异常或无数据", "windSpeedSubText": "风速为--时表示查询出现异常或无数据", "windDirectionSubText": "风向为--时表示查询出现异常或无数据", "illuminationSubText": "光照度为0时表示查询出现异常或无数据", "PM10SubText": "PM10为--时表示查询出现异常或无数据", "PM25SubText": "PM2.5为--时表示查询出现异常或无数据", "pressureSubText": "气压为--时表示查询出现异常或无数据", "rainFallSubText": "雨量为--时表示查询出现异常或无数据", "radiationSubText": "辐射为--时表示查询出现异常或无数据", "pressure": "气压", "rainFall": "雨量", "radiation": "辐射"}, "bigScreen": {"VideoSurveillance": "视频监控", "DeviceList": "设备列表", "Address": "详细地址", "NumberOfOnline": "上线数", "OperationLog": "操作日志", "ProgramPlayStatistics": "节目播放统计", "RequestFailed": "请求失败"}, "electricity": {"current": "电流", "power": "功率", "electricity": "电能", "voltage": "电压", "currentSubText": "电流为0时表示查询出现异常或无数据", "powerSubText": "功率为0时表示查询出现异常或无数据", "electricitySubText": "电能为0时表示查询出现异常或无数据", "voltageSubText": "电压为0时表示查询出现异常或无数据", "clearData": "清除数据", "clearSuccessfully": "清除成功", "clearFailed": "清除失败", "cancelClear": "取消清除", "monthlyElectricity": "每月电能统计", "exportElectricity": "导出每月电能数据", "setElectricityTime": "设置电能查询周期", "selectTime": "请选择电能查询周期", "tip": "默认查询周期为24小时。", "electricityData": "电能数据", "curElectricityTime": "当前周期"}, "monitor": {"device_ip": "设备IP", "port": "端口", "owning_terminal": "所属终端", "monitorSaveTip": "在线设备，请谨慎修改用户名密码，修改用户名密码如果有误将导致设备掉线", "Device_name_cannot_be_empty": "设备名称不能为空", "Please_enter_the_device_serial_number": "请输入设备序列号", "monitorSaveTip1": "将用户名密码改为：用户名", "Split_screen": "分屏", "PTZ_control": "云台控制", "equipment": "设备", "deviceIdIsNotEmpty": "设备ID不能为空", "CommandISExist": "命令不存在", "notExistOrNotLoggedIn": "未找到设备或设备未登录", "isNotExist": "设备不存在", "notLoggedIn": "设备未登录", "zoom": "变倍", "aperture": "光圈", "focus": "焦点", "screenshot": "截图", "noPlayback": "该时间段没有回放", "downloadPlayback": "下载回放", "selectDateRange": "选择时间段", "tip": "本次下载是将摄像头中的回放记录下载到服务器中，请您等待一段时间再从本页面下载到本地。", "normal": "普通摄像头", "humanNumberStatistic": "客流统计", "insideHumanNumber": "人群聚集", "traffic": "智慧交通", "cameraType": "摄像头类型", "monitorSaveTip2": "修改摄像头功能类型为:", "openTheAlarm": "开启报警", "crowdAlarmThreshold": "人群报警阈值", "intervalForSendingEmails": "邮箱发送间隔时长", "openTheAlarmTip": "开启报警后，设备超过报警阈值人数会发送邮件给设备所属用户，触发邮件报警后根据发送时间间隔发送下一封邮件，邮箱使用所属用户注册的邮箱", "offLineOrNotExist": "设备离线或已断开", "serialNumber": "摄像头序列号", "rtmpStreamState": "是否开启推流", "streamCloseTip": "关闭推流后，您将无法在网页上看到监控画面，请确定是否关闭推流", "streamOpenTip": "开启推流后，您可以在网页上看到监控画面，此操作将消耗一定流量，请确定是否开启推流"}, "pay": {"contactInformation": "联系方式", "addressValue": "香港中环皇后大道1号", "savingsAccountNumber": "储蓄帐户编号", "xixunCompanyValue": "上海熙讯电子科技有限公司", "bankName": "银行名称", "bankNameValue": "香港上海汇丰银行有限公司", "swiftCode": "国际代码", "tips1": "请购买VIP设备才能正常上线", "tips": "登录成功后在付费服务中订购VIP，设备才能正常上线", "totalPrice": "总价", "contactPerson": "联系人", "contactPersonTips": "请输入联系人", "telephone": "联系电话", "telephoneTips": "请输入联系电话", "address": "公司地址", "addressTips": "请输入公司地址", "companyName": "公司全称", "companyNameTips": "请输入公司全称", "vipDuration": "VIP有效时长/年", "vip4": "极品钻石VIP4", "upgradeToPaidVersion": "升级到付费版", "silverCardVip": "银卡vip", "goldCardVip": "金卡vip", "diamondVip": "钻石vip", "superVip": "超级VIP", "orderService": "订购服务", "currentVersion": "当前版本：", "numberOfTerminals": "终端数量", "TheRemainingAmount": "剩余数量", "ExpireDate": "到期时间", "selectVersion": "选择版本", "SelectDuration(years)": "选择时长（年）", "totalOrder": "订单总额", "submitOrder": "提交订单", "freeVersion": "免费版", "price": "价格", "ThereAreUnpaidOrders": "存在未支付订单", "ThereIsAnUnpaidOrderPleaseGoToPay": "存在未支付订单，请前往支付。", "OrderRecord": "订单记录", "100ControlCards": "控制卡数量 100 个", "500ControlCards": "控制卡数量 500 个", "NumberOfControlCards1500": "控制卡数量 1500 个", "unpaid": "未支付", "transactionCreation": "交易创建", "UnpaidTransactionTimeoutClosed": "未付款交易超时关闭", "paymentSuccessful": "支付成功", "OrderDetails": "订单明细", "pay": "支付", "orderNumber": "订单号", "Years": "年限", "PaymentStatus": "支付状态", "amount": "金额", "newPurchase": "新购", "Renewal": "续费", "upgrade": "升级", "PayForTheOrder": "支付订单", "ExpectedPeriod": "预计期限: ", "to": "至", "ActualDeadlineIsSubjectToPayment": "实际期限以支付为准", "cancelOrder": "取消订单", "theOrderWillBeAutomaticallyClosed": "订单有效期 15 天， 请及时支付。14天 后未支付，订单将自动关闭", "AfterOrderIsPaidSuccessfully": "注：订单支付成功后，可进入", "QueryPaymentInformationOnThePage": "页面查询支付信息", "paymentMethod": "支付方式", "publicAccount": "对公帐号", "AccountName": "户 名:", "AccountBank": "开户行:", "LinkNumber": "联行号:", "account": "账 号:", "uploadTheBankReceipt": "如果您已打款至以上帐号，请上传银行回执。", "receivedByMajorBanks": "各大银行对公转账到账时间需要2小时～3个工作日不等。", "notifyYouViaSMS": "我们的工作人员会在收到付款后第一时间为您开通服务", "UploadBankReceipt": "上传银行回执", "contactOurSalesStaff": "如有疑问，请联系我司业务人员", "NotesForPublicTransfers": "对公转账注意事项:", "submittedForFinancialReview": "线下支付到账有延迟，并需提交银行回执予财务审核", "TransfersFromPersonalAccounts": "个人帐号转账至我司对公帐号只能开具个人普通发票。", "IfTheCompanyAccountIsTransferred": "公司帐号转账至我司对公帐号可开具企业增值税普通发票或企业增值税专用发票。", "uploadTip": "支持 JPG或 PNG格式，文件大小不超过5M", "BankCardNumber": "银行卡号", "bankCardNumberWhenTransferring": "请填写转账时的银行卡号", "transactionHour": "交易时间", "CancellationLineItemCannotBeEmpty": "取消订单项不能为空", "WaitingForSellerToConfirm": "等待卖家确定", "PurchaseStatus": "购买状态", "bankReceipt": "银行回执", "Serve": "服务", "Optional": "选配", "deadline": "截止时间：", "orderUpdateTips": "只能修改订单上传的回执或银行卡号，如对其他信息有疑问请联系我司工作人员", "PleaseUploadBankReceipt": "请上传银行回执", "PleaseEnterBankCardNumber": "请输入银行卡号", "NoRelatedOrders": "无相关订单", "ErrorUploadingBankReceipt": "上传银行回执错误", "changeVipStateTip": "如果修改当前用户的VIP状态为超级VIP，当前用户公司ID下所有用户都将为超级VIP"}, "statistic": {"enableHumanStatistic": "开启客流统计", "queryHumanStatisticToday": "查询当日客流", "enter": "进入人数", "exited": "离开人数", "countingMonitoring": "客流统计监控", "viewChart": "查看图表", "currentNumber": "当前人数", "lineChart": "区域内人数统计折线图", "areaPeopleNum": "区域内人数", "selectHistoricalData": "查询历史数据", "sendData": "发送数据", "keyTip": "开启数据展示功能后，密钥会自动生成", "isShowHumanNumber": "是否已开启终端人流量数据展示功能", "dataKey": "密钥", "noDataKey": "当前设备没有密钥", "clickCopy": "点击复制", "copySuccess": "复制成功"}, "alarm": {"alarmNum": "报警号码", "alarmPeople": "报警人", "call": "呼叫方", "receive": "接收方", "callTime": "通话时间", "channel": "通道", "dstChannel": "目的通道", "alarmDeviceInfo": "报警设备信息", "setCallingVolume": "设置通话音量", "callingVolume": "通话音量", "notEnable": "未启用", "setAlarmInfo": "设置报警设备信息", "sipAccount": "Sip账号", "sipServiceAddress": "sip服务器地址", "sipServicePort": "sip服务器端口", "accountState": "账号状态", "alarmDeviceNetwork": "报警设备网络信息", "setNetwork": "设置网络状态", "dynamic": "动态", "static": "静态", "gateway": "网关", "subnetMask": "子网掩码", "alarmAccount": "报警账号", "accountType": "账号类型", "registerAlarmAccount": "注册报警设备账号", "registerPhoneNumber": "注册话机账号", "accountRule": "账号不能为空且必须为11位数字或字母", "account": "账号", "batchSettings": "批量设置", "alarmNetworkTip": "长按报警设备直到有提示音,短按一次将播报IP，短按三次将切换动静态IP", "alarmAddressTip": "在设置报警地址之前，请在报警设备信息处配置报警设备账号等信息", "alarmAddressSetTip": "默认状态时，平台会将报警地址设为平台地址", "defaultState": "默认", "custom": "自定义", "myPhoneNumber": "自己的号码", "calledPhoneNumber": "拨打的号码", "alarmInfoTip": "配置报警设备信息之前，请注册账号", "backupCalledPhoneNumber": "备用号码"}, "traffic": {"enableTraffic": "开启智能交通", "eventName": "事件名称", "plateNumber": "车牌号码", "plateType": "车牌类型", "plateColor": "车牌颜色", "vehicleColor": "车身颜色", "vehicleType": "车身类型", "vehicleSize": "车身大小", "illegalPlace": "违法地点", "eventTime": "事件时间", "downloadEventPic": "下载违章照片", "illegalPic": "违章照片"}, "radar": {"radarSpeed": "雷达测速", "radarSetting": "雷达设置", "fastestCar": "最快车", "closestCar": "最近车", "setResponseTime": "响应时间", "setOutputTarget": "输出目标", "setMinSpeed": "最小检测速度", "setMaxSpeed": "最大检测速度", "setSensitivity": "灵敏度", "isConnect": "是否连接雷达", "speed": "速度", "parameter": "参数", "speedLimitRange": "限速区间", "addSpeedLimitRange": "请添加限速区间", "minSpeed": "最小速度", "maxSpeed": "最大速度", "radarSpeedLimit": "雷达限速"}, "ac": {"apOnlineRate": "AP在线率", "apOnlineNumber": "AP在线数", "apSum": "AP总数", "onlineTerminal": "终端在线数", "flowStatistics": "流量统计", "flowStatisticsToday": "今日流量统计", "name": "AC名称", "macAddress": "mac地址", "belongArea": "所属区域", "belongOrganization": "所属机构", "belongProject": "所属项目", "userOnlineCount": "用户上线总次数", "acOnlineUserCount": "AC所有在线用户数", "upstreamTraffic": "上行流量", "downlinkTraffic": "下行流量", "refresh": "刷新"}, "userAuth": {"pass": "验证成功", "fail": "验证失败", "company": "企业认证", "personal": "个人实名认证", "unverified": "未认证", "certificationAudit": "认证审核", "authMode": "认证方式", "authInfo": "认证信息", "enterpriseLicense": "企业法人营业执照", "OfficialSeal": "企业公章授权证明", "FrontOfIdentityCard": "身份证正面", "ReverseOfIDCard": "身份证反面", "reason": "原因", "authentication": "认证", "pleaseUploadEnterpriseLicenseOfficialSeal": "请上传企业公章证明和企业法人营业执照", "updateAuth": "更新认证", "uploadAuth": "上传认证"}, "cat1": {"temp": "模板温度", "power": "有功功率", "electricEnergy": "有功电能", "roadOne": "1路", "roadTwo": "2路", "addDevice": "添加设备", "channel": "通道", "colorTemp": "色温", "scheduleTip": "该定时是从设定的RealTime时间开始。如设定的时间为12：30，就是从12：30开始，直到下一个时间点结束，单灯的状态为设定的亮度以及色温。如果要关闭单灯，可以设置亮度为0。"}, "notify": {"NotificationStrategy": "通知策略", "NotifyTip": "（用户开启通知后，在指定时间以邮箱方式通知用户）", "OfflineNotify": "离线通知", "CardNotWorkingNotification": "接收卡不工作通知", "tactics": "策略（时）", "selectAll": "全选", "PleaseSelectNotification": "请选择通知策略", "SendingMailbox": "发送邮箱", "SendingTime": "发送时间", "Username/EmailAddress": "用户名/发送邮箱", "OffLineTime": "离线时间", "AbnormalTime": "异常时间", "NumberOfCardsRLastTime": "上一次接收卡数量", "NumberOfCardsReceivedExceptionOccurs": "异常时接收卡数量"}, "thirdPartyAd": {"enabledThirdPartyAd": "第三方广告接入", "thirdPartyUrl": "第三方服务地址", "loopTip": "与设备里已有的广告一起循环播放，多个广告会均匀打散到设备已有的广告列表中", "onceTip": "穿插到设备已有的广告中播放一次，多个广告会均匀打散到设备已有的广告列表中", "mutexTip": "独占循环播放", "adInterval": "广告周期", "queryThirdPartyAd": "查询第三方广告", "downloadUrl": "下载地址", "impression": "播放信息", "playHour": "播放时段", "playCount": "已播放次数"}, "crossArrow": {"addToCross": "添加到十字箭头", "arrowProgram": "箭头节目", "greenArrow": "绿色箭头", "redArrow": "红色的叉", "closeScreen": "关屏", "curProgram": "当前节目", "remove": "移除", "batchRemove": "批量移除"}, "manual": {"operationDocument": "操作文档", "videoTutorial": "视频教程", "resourceManagement": "资源管理", "manualAndTutorial": "使用手册和视频教程", "universal": "通用", "fileTips": "同一时间仅能上传同一种文件"}, "myEditor": {"bottomCenter": "下对齐", "topCenter": "上对齐", "verticalCenter": "居中对齐"}, "employee": {"today": "今日", "yesterday": "昨日", "last7Days": "最近7天", "last30Days": "最近30天", "lineChart": "折线图", "barChart": "柱状图", "unitPerson": "单位: 人", "man": "男", "woman": "女", "tips": "开启后自动统计客流，开启成功后可在客流统计->客流统计V1中查看到设备"}}