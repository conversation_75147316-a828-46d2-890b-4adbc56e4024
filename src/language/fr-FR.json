{"common": {"PleaseInput": "Veuillez <PERSON>", "PleaseSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "Loading": "Chargement en cours", "personalSettings": "Paramètres personnels", "logOut": "Déconnexion", "query": "<PERSON><PERSON><PERSON>", "newlyBuild": "Nouveau", "add": "Ajouter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "batchDel": "Suppression en lot", "update": "Modifier", "state": "État", "operation": "Opération", "tips": "Conseil", "info": "Détails", "deploy": "Attribuer", "cancel": "Annuler", "confirm": "Confirmer", "areYouSureExit": "Êtes-vous sûr de vouloir quitter ?", "setSuccess": "<PERSON><PERSON><PERSON><PERSON>", "notSet": "Non défini", "set": "Définir", "bindingSuccess": "<PERSON><PERSON>", "unbound": "Non lié", "binding": "<PERSON><PERSON>", "operationSuccessful": "Opération réussie", "createTime": "Date de création", "normal": "Normal", "disable": "Désactivé", "delete_current_option": "Confirmer la suppression de l'option actuelle", "original": "Original", "use_iconfont": "Les icônes utilisent SVG Sprite", "total": "<PERSON>ut", "supportedTip": "Non supporté pour le moment", "selectDevice": "Veuillez sélectionner un appareil", "clickToEnlarge": "Cliquez pour agrandir", "pictureLoadingFailed": "Échec du chargement de l'image", "passwordError": "Mot de passe incorrect", "OKToRestart": "Confirmer le redémarrage", "WaitingForRestart": "En attente de redémarrage...", "RestartTimeout": "<PERSON><PERSON>lai de redémarrage dépassé", "modifiedTime": "Date de modification", "exportData": "Exporter les données", "submit": "So<PERSON><PERSON><PERSON>", "modifyTheSuccess": "Modification réussie", "configuration": "Configuration", "failure": "Échec", "release": "Publier", "nextStep": "Étape suiva<PERSON>", "selectMaterial": "Sélectionner un matériel", "notSelectedectMaterial": "Remarque : Les groupes non sélectionnés exporteront les informations SIM de toutes les cartes par défaut", "exportingSIM": "Exporter les informations SIM", "enterCardPackage": "Veuillez saisir le nom de la carte/du paquet...", "note": "<PERSON><PERSON><PERSON>", "enterPackage": "Veuillez saisir le nom du paquet...", "noDetails": "<PERSON><PERSON><PERSON> d<PERSON>", "versionQuery": "Requête de version", "CardSysterm5263": "La requête de version nécessite CardSysterm 5.2.6.3 ou version ultérieure", "uninstallCard": "Aucune carte sélectionnée, impossible d'utiliser la fonction de désinstallation en ligne", "packageName": "Nom du paquet", "versionNumber": "Numéro de version", "versionIdentifiers": "Identifiants de version", "wrong": "L'état de connexion de cette carte est incorrect, la mise à jour est annulée !", "upgradeWrong": "L'état de connexion de toutes les cartes sélectionnées pour la mise à niveau est problématique, veuillez vérifier et réessayer !", "cardsNotReturn": "Certaines cartes ne renvoient pas la barre de progression, d<PERSON><PERSON> de requête dé<PERSON>, la mise à niveau est terminée !", "updateComplete": "Mise à jour terminée !", "restartCards": "Êtes-vous sûr de vouloir redémarrer ces cartes ?", "addTiming": "Veuillez ajouter un minutage", "Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "<PERSON><PERSON><PERSON><PERSON>", "Saturday": "<PERSON><PERSON>", "Sunday": "<PERSON><PERSON><PERSON>", "brightnessClears": "La définition manuelle de la luminosité effacera tout le minutage de luminosité et la sensibilité", "fullBrightness": "Luminosité maximale", "screenDuring": "Remarque : Pendant la période définie, l'écran est allumé", "removeTiming": "Effacer le minutage", "clearSuccess": "Effacement ré<PERSON>i", "notEnteredNotModified": "Les champs non saisis ne seront pas modifiés, l'utilisation de Conn_v11.1.1.1[261]-AIPS-release-2&4 ou version ultérieure considère les champs non saisis comme vides", "Changing": "La modification de l'ID de l'entreprise ou de l'adresse realTime entraînera la déconnexion de la carte, êtes-vous sûr de vouloir modifier ?", "networkNTP": "La synchronisation automatique de l'heure nécessite un réseau et une adresse NTP", "Settings": "Lors de la sélection du mode dans les paramètres de synchronisation, les options non renseignées sont considérées comme vides", "SynchronousMode": "Mode synchrone", "zoneEmpty": "Le fuseau horaire sélectionné ne peut pas être vide !", "synchronousState": "La période de temps définie pour la synchronisation/désynchronisation est en état de synchronisation !", "beEmpty": "La largeur et la hauteur ne peuvent pas être inférieures ou égales à 0 et ne peuvent pas être vides !", "ModifyFailure": "Échec de la modification", "programmeDetails": "Détails de la tâche de programme", "showWidth": "Largeur du programme", "showHigh": "Hauteur du programme", "notOnPlatform": "Échec de la requête, le programme actuel n'est pas publié sur cette plateforme", "allIntervals": "Si le minutage est activé sans sélection d'intervalle, tous les intervalles sont considérés comme sélectionnés", "notSelected": "Non sélectionné", "true": "O<PERSON>", "false": "Non", "name": "Nom", "custom": "<PERSON><PERSON><PERSON><PERSON>", "MaximumNumberOfWords200": "Limite de 200 caractères", "exportingSIMTips": "Le groupe actuel n'a pas de terminal, il est recommandé de sélectionner un autre groupe", "language": "<PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "saveAndExit": "Enregistrer et quitter", "noMoreData": "Pas plus de données", "enable": "Activer", "NotEnabled": "Non activé", "AssignUsers": "Attribuer des utilisateurs", "manual": "Manuel d'utilisation", "addMedia": "+ Ajouter un média", "reviewCurrentUsers": "Veuillez contacter notre personnel pour examiner l'utilisateur actuel", "selectGroup": "Sélectionner un groupe", "selectingGroup": "Groupe actuel", "unclassified": "Non classé", "frontStep": "Étape <PERSON>", "search": "<PERSON><PERSON><PERSON>", "CAT1": "CAT1", "DaHuaCamera": "Caméra DaHua", "GBCamera": "Caméra GB"}, "screen": {"simInformation": "Informations de la carte SIM", "networkState": "État du réseau", "series": "Numéro de série", "countries": "Pays", "operationName": "Nom de l'opération", "unknownState": "État inconnu", "noCard": "Pas de carte insérée", "PIN": "État verrouillé, nécessite le code PIN de l'utilisateur pour déverrouiller", "PUK": "État verrouillé, nécessite le code PUK de l'utilisateur pour déverrouiller", "PIN2": "État verrouillé, nécessite le code PIN du réseau pour déverrouiller", "readyState": "État prêt", "InvalidState": "État invalide", "subscribe": "<PERSON>'abonner", "choose": "Choi<PERSON>", "supportZip": "Le fichier de version ne supporte que le format ZIP !", "selectFile": "Sélectionner un fichier", "releaseTime": "Date de publication", "versionInformation": "Informations sur la version", "testVersion": "La version de test n'a pas de détails de version !", "hardwareVersion": "Les paramètres matériels n'ont pas de détails de version !", "officialRelease": "Version officielle publiée", "testRelease": "Version de test publiée", "hardwareRelease": "Paramètres matériels publiés", "sizeMore": "<PERSON><PERSON>", "OnlyForZIPType!": "Uniquement pour le type ZIP !", "uploadEmpty": "Le fichier à télécharger ne peut pas être vide !", "uploadFile": "Télécharger un fichier", "fileTips": "Seuls les fichiers MP3, MP4, gif, png, jpg sont acceptés, les fichiers MP3 et images ne doivent pas dépasser 100M, les fichiers MP4 ne doivent pas dépasser 150M", "fileTips1": "Seuls les fichiers MP3 sont acceptés, la taille ne doit pas dépasser 20M", "picture": "Image", "video": "Vidéo", "picturesOrVideos": "Le type de fichier média téléchargé doit être une image ou une vidéo !", "my": "Mon", "pending": "En attente d'approbation", "pageImprovement": "Amélioration de la page en cours", "errorDetails": "<PERSON>é<PERSON> de l'erreur", "null": "Aucun", "showProgress": "Progression du programme", "sendSuccess": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "sendSuccessOffline": "Programme hors ligne envoyé, la carte de contrôle se connectera automatiquement dans les 72 heures", "failedProgress": "Échec de la récupération de la progression", "timesOut": "Délai de récupération de la progression dépassé", "selectCard": "Veuillez sélectionner une carte de contrôle", "reviewDetails": "Détails de l'examen", "satelliteNumber": "Nombre de satellites", "autoBrightnessTable": "Table de luminosité automatique", "senorType": "Type de capteur", "setAutoBrightnessTable": "Définir la table de luminosité automatique", "getAutoBrightnessTable": "Interroger la table de luminosité automatique", "default255BrightnessTable": "Table de luminosité par défaut 255", "customizeBrightnessTable": "Table de luminosité personnalisée", "otherSwitch": "<PERSON>tre interrupteur", "customSwitchFunction": "Fonction d'interrupteur personnalisée", "programPlayDetail": "<PERSON><PERSON><PERSON> de la lecture du programme", "programPlayStatistic": "Statistiques de lecture du programme", "programPlaySum": "Nombre total de lectures du programme", "exportPlayLog": "Exporter les journaux de lecture", "log": "Journal", "networkPort": "Port réseau", "clickToCheck": "Cliquer pour vérifier", "getPointCheckInfo": "Obtenir les informations de vérification des points", "imageSize": "<PERSON>lle de l'image", "badPointNum": "Nombre de points défectueux", "queryPlayerState": "Interroger l'état du lecteur", "playerStateRep": {"1": "Initialisation", "2": "Fin du programme programmé", "3": "Aucun programme à lire", "4": "Programme supprimé", "5": "Traitement du programme en cours", "6": "Aucune information pour le moment", "7": "Le programme peut être erroné", "8": "<PERSON><PERSON><PERSON>", "9": "Le programme programmé n'est pas dans la plage"}, "fullColor": "<PERSON><PERSON><PERSON> complète", "monochrome": "Monochrome", "redBeadNumberPoints": "Nombre de points défectueux des LED rouges", "greenBeadNumberPoints": "Nombre de points défectueux des LED vertes", "blueBeadNumberPoints": "Nombre de points défectueux des LED bleues", "totalNumberOfBadPoints": "Nombre total de points défectueux", "redBadPointPosition": "Position des points défectueux des LED rouges", "greenBadPointPosition": "Position des points défectueux des LED vertes", "blueBadPointPosition": "Position des points défectueux des LED bleues", "badPointPosition": "Position des points défectueux", "abscissa": "Abscisse", "ordinate": "Ordonnée", "noBadPoint": "Aucun point défectueux", "pointCheckTips": "La détection des points défectueux ne supporte qu'une seule carte, veuillez en sélectionner une autre", "receivingCard": "Carte réceptrice", "setRecCardRelaySwitch": "Définir l'interrupteur relais de la carte réceptrice", "getRecCardSensorData": "Obt<PERSON><PERSON> les données du capteur de la carte réceptrice", "smoke": "<PERSON><PERSON><PERSON>", "smokeless": "Sans fumée", "openCloseDoor": "Ouvrir/fermer la porte", "openDoor": "<PERSON>u<PERSON><PERSON>r la porte", "closeDoor": "<PERSON><PERSON><PERSON> la porte", "relaySwitch": "Interrupteur relais", "levelDetection": "Détection de niveau", "accessHighLevel": "Indique un accès à un niveau élevé", "noDeviceConnected": "Indique qu'aucun appareil n'est connecté", "firstRoad": "Première voie", "secondWay": "Deuxième voie", "thirdWay": "Troisième voie", "fourthWay": "Quatrième voie", "theFifthRoad": "Cinquième voie", "sensorDataShareTips": "Le partage des données du capteur ne supporte qu'une seule carte, veuillez en sélectionner une autre", "hour": "<PERSON><PERSON>", "pointTable": "Table de points", "pointTableTips": "Veuillez opérer sous la supervision d'un professionnel", "pointTableTemplate": "Modèle de table de points", "pleaseUploadPointTable": "Veuillez d'abord définir la table de points", "networkConfig": "Configuration r<PERSON><PERSON>", "hotspot": "Point d'accès", "wifiTip": "L'activation du WIFI peut modifier l'état du réseau et entraîner une déconnexion, veuillez agir avec prudence", "apTip": "Remarque : Veuillez utiliser un mot de passe composé de lettres majuscules, minuscules et de chiffres, la longueur du mot de passe doit être comprise entre 8 et 20", "wifiList": "Interroger la liste des Wi-Fi", "selectWifi": "Veuillez sélectionner un Wi-Fi", "singleCardOperation": "Cette fonction ne supporte qu'une seule carte"}, "sys": {"enable": "Activer", "remarks": "Veuillez saisir les informations de remarque", "authenticationMode": "Veuillez sélectionner le mode de vérification", "emailVerification": "Vérification par e-mail", "mobileVerification": "Vérification par téléphone", "verify": "Mode de vérification", "notOpen": "Non activé", "email": "E-mail", "mobile": "Téléphone", "whetherAudit": "Soumettre à l'audit", "open": "Activer", "close": "Désactiver", "companyId": "ID de l'entreprise", "same": "L'ID de l'entreprise est identique à celui du créateur", "roleEmpty": "Le rôle de l'utilisateur ne peut pas être vide", "alarmType": "Type d'alarme", "alarmTime": "Heure de l'alarme", "Iknown": "Je comprends", "currentBrowser": "Le navigateur actuel ne prend pas en charge les événements envoyés par le serveur", "notReminded": "Vous ne serez plus rap<PERSON><PERSON> après confirmation", "visited": "La page que vous avez visitée", "find": "N'existe pas", "url": "Veuillez vérifier l'URL", "previousPage": "Retour à la page précédente", "enterHome": "Accéder à la page d'accueil", "permissionOfTerminalGroups": "Groupes de terminaux contrôlables", "TheSuperAdminDoesNotRestrictUserGroups": "Le super administrateur ne limite pas les groupes d'utilisateurs", "addOrUpdateAuth": "Ajouter/modifier l'authentification", "isDefaultPassword": "Définir le mot de passe par défaut"}, "login": {"dynamicCodeEntryTips": "L'utilisateur actuel n'a pas activé la vérification par code, veuillez d'abord activer le mode de vérification", "login": "Connexion", "passwordLogin": "Connexion par mot de passe", "username": "Nom d'utilisateur", "password": "Mot de passe", "ForgetThePassword": "Mot de passe oublié", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "dynamicCodeEntry": "Connexion par code de vérification", "pwdNotes": "Il est recommandé d'utiliser un mot de passe de plus de 8 caractères", "origin_pwd_incorrect": "L'ancien mot de passe est incorrect", "wrong_account_or_password": "Nom d'utilisateur ou mot de passe incorrect", "account_has_been_locked": "Le compte est verrouillé, veuillez contacter l'administrateur", "updateEmail": "Modifier l'e-mail", "bindEmail": "Lier l'e-mail", "updateMobile": "Modifier le numéro de téléphone", "bindMobile": "<PERSON><PERSON> le numéro de téléphone", "sliderRight": "Veuillez cliquer sur le bouton pour vérifier", "loading": "Chargement en cours", "passwordMore8": "Le mot de passe doit contenir plus de 8 caractères et inclure des lettres majuscules, minuscules et des chiffres", "user4To17": "Le nom d'utilisateur doit contenir entre 4 et 17 caractères et inclure des chiffres ou des lettres", "clickCodeMailbox": "Cliquez pour obtenir le code de vérification, le message sera envoyé à l'e-mail de sécurité", "clickCodePhone": "Cliquez pour obtenir le code de vérification, le SMS sera envoyé au téléphone de sécurité", "authenticationSuccessful": "Vérification réussie", "securityVerification": "Veuillez d'abord effectuer la vérification de sécurité", "enterVerificationCode": "Veuillez saisir le code de vérification", "UserNameEmailMobile": "Nom d'utilisateur/E-mail/Téléphone", "ScopeOfAuthority": "Portée des autorisations", "superAdministrator": "Super administrateur", "PermissionDetails": "Détails des autorisations", "pleaseContactTheAdministrator": "Veuillez contacter l'administrateur"}, "register": {"register": "Inscription", "mobile": "Numéro de téléphone", "mailbox": "Adresse e-mail", "code": "Code de vérification", "getCode": "Obtenir le code de vérification", "remind": "Il est recommandé d'utiliser WeChat pour scanner le code QR et s'inscrire en un clic", "back": "Retour à la connexion", "prependResend": "<PERSON><PERSON> ", "appendResend": " s pour renvoyer", "personalInformation": "Informations personnelles", "complete": "<PERSON><PERSON><PERSON>", "recommendedRegister": "Il est recommandé d'utiliser WeChat pour scanner le code QR et s'inscrire en un clic", "enterPassword": "Veuillez saisir à nouveau le mot de passe", "companyId": "ID de l'entreprise, la carte de contrôle se connecte à l'ID de l'entreprise, ne peut pas être modifié après confirmation", "companyName": "Nom de l'entreprise", "companyAddress": "Adresse de l'entreprise", "companyPhone": "Téléphone de l'entreprise", "readAIPSAgreement": "Veuillez d'abord lire l'accord utilisateur AIPS", "readAccepted": "J'ai lu et accepté", "AIPSAgreement": "Accord utilisateur AIPS", "registeredSuccessfully": "Inscription réussie", "clickJumpOr": "Cliquez pour rediriger ou ", "loginDisplayed": " secondes pour rediriger automatiquement vers la page de connexion", "readAcceptAIPSAgreement": "Veuillez lire et accepter l'accord utilisateur AIPS", "youAreRight": "Vous êtes en train de ", "resettingVerification": "réinitialiser le mot de passe, veuillez d'abord effectuer la vérification de sécurité", "switchingAuthentication": "Changer le mode de vérification", "pleaseSet": "Veuillez définir ", "passwordSecurity": "un nouveau mot de passe, il est recommandé d'utiliser une combinaison de chiffres, lettres et caractères pour améliorer la sécurité du mot de passe", "passwordChangedSuccessfully": "Mot de passe modifié avec succès", "verified": "Vérification d'identité", "idCardNumber": "Numéro de carte d'identité", "companyLicense": "Télécharger l'image du certificat d'enregistrement de l'entreprise valide", "cachet": "Télécharger l'image du certificat d'autorisation avec le sceau de l'entreprise", "idCardFront": "Télécharger l'image recto de la carte d'identité", "idCardReverse": "Télécharger l'image verso de la carte d'identité", "pleaseReadAndCheckAIPS": "Veuillez lire et cocher l'accord utilisateur AIPS", "personal": "Personnel", "company": "Entreprise", "CertifiProgress": "Progression de la vérification", "waitForAdminAuth": "En attente de vérification par l'administrateur", "DownloadLicenseCertificate": "Télécharger le certificat d'autorisation"}, "ChangePWD": {"ChangePassword": "Modifier le mot de passe", "remind": "Seuls les utilisateurs ayant un numéro de téléphone ou une adresse e-mail enregistrés peuvent récupérer leur mot de passe"}, "nav": {"首页": "Accueil", "智慧屏幕": "<PERSON><PERSON><PERSON> <PERSON>", "智慧广播": "Diffusion intelligente", "气象环境": "Environnement météorologique", "智慧监控": "Surveillance intelligente", "客流统计": "Statistiques de flux de passagers", "客流统计V1": "Statistiques de flux de passagers V1", "系统管理": "Gestion du système", "设备管理": "Gestion des équipements", "节目管理": "Gestion des programmes", "媒体库": "Bibliothèque multimédia", "日志管理": "Gestion des journaux", "远程控制日志": "Journaux de contrôle à distance", "播放日志": "<PERSON><PERSON><PERSON>", "用户管理": "Gestion des utilisateurs", "菜单管理": "Gestion des menus", "角色管理": "Gestion des rôles", "用户日志": "Journaux des utilisateurs", "登录日志": "Journaux de connexion", "系统日志": "Journaux du système", "设备状态": "État des équipements", "广播任务": "Tâches de diffusion", "分组管理": "Gestion des groupes", "审批管理": "Gestion des approbations", "公告管理": "Gestion des annonces", "终端列表": "Liste des terminaux", "智慧物联": "Internet des objets intelligents", "智慧照明": "É<PERSON>lairage intelligent", "CAT1照明": "Éclairage CAT1", "控制卡照明": "Éclairage de la carte de contrôle", "电能管理": "Gestion de l'énergie électrique", "视频监控": "Surveillance vidéo", "付费服务": "Services payants", "订购服务": "Services d'abonnement", "订单": "Commande", "SIP账号管理": "Gestion des comptes SIP", "监控回放": "Relecture de surveillance", "人群聚集": "Regroupement de foule", "订单管理": "Gestion des commandes", "报警管理": "Gestion des alarmes", "报警记录": "Enregistrement des alarmes", "通话记录": "Enregistrement des appels", "智慧交通": "Transport intelligent", "交通信息": "Informations sur le trafic", "雷达测速": "Radar de vitesse", "WIFI AC": "WIFI AC", "概览": "<PERSON><PERSON><PERSON><PERSON>", "AC管理": "Gestion AC", "密码管理": "Gestion des mots de passe", "认证审核": "Audit de certification", "通知策略": "Stratégie de notification", "通知日志": "Journaux de notification", "十字箭头": "Flèche croisée", "设备白名单": "Liste blanche des équipements", "摄像头监控": "Surveillance par caméra"}, "validate": {"account_cannot_empty": "Le compte ne peut pas être vide", "password_cannot_empty": "Le mot de passe ne peut pas être vide", "confirm_password_cannot_empty": "La confirmation du mot de passe ne peut pas être vide", "new_pwd_cannot_empty": "Le nouveau mot de passe ne peut pas être vide", "email_cannot_empty": "L'e-mail ne peut pas être vide", "mobile_cannot_empty": "Le numéro de téléphone ne peut pas être vide", "code_cannot_empty": "Le code de vérification ne peut pas être vide", "roleName_cannot_empty": "Le nom du rôle ne peut pas être vide", "menuURL_cannot_empty": "L'URL du menu ne peut pas être vide", "superior_menu_cannot_empty": "Le menu supérieur ne peut pas être vide", "menu_name_cannot_empty": "Le nom du menu ne peut pas être vide", "group_name_cannot_empty": "Le nom du groupe ne peut pas être vide", "group_type_cannot_empty": "Le type de groupe ne peut pas être vide", "audit_name_cannot_empty": "Le nom de l'audit ne peut pas être vide", "resource_type_cannot_empty": "Le type de ressource ne peut pas être vide", "status_cannot_be_empty": "Le statut ne peut pas être vide", "approval_comments_cannot_blank": "Les commentaires d'approbation ne peuvent pas être vides", "program_name_cannot_empty": "Le nom du programme ne peut pas être vide", "the_new_password_is_inconsistent": "La confirmation du mot de passe ne correspond pas au nouveau mot de passe", "the_password_is_inconsistent": "La confirmation du mot de passe ne correspond pas au mot de passe", "incorrect_email_format": "Le format de l'e-mail est incorrect", "code_format": "Veuillez saisir six caractères", "mobile_format": "Le format du numéro de téléphone est incorrect", "mobile_code_empty": "Le code de vérification du téléphone ne peut pas être vide", "email_code_empty": "Le code de vérification de l'e-mail ne peut pas être vide", "company_id_empty": "L'ID de l'entreprise ne peut pas être vide", "not_empty": "Ne peut pas être vide", "alarmAddress_not_empty": "L'adresse d'alarme ou le compte d'appel ne peut pas être vide", "alias_cannot_empty": "<PERSON>'alias ne peut pas être vide", "company_name_cannot_empty": "Le nom de l'entreprise ne peut pas être vide", "company_address_cannot_empty": "L'adresse de l'entreprise ne peut pas être vide", "company_phone_number_cannot_empty": "Le téléphone de l'entreprise ne peut pas être vide", "id_card_number_cannot_empty": "Le numéro de la carte d'identité ne peut pas être vide", "id_card_number_format_wrong": "Le format du numéro de la carte d'identité est incorrect", "validateTip": "Pour la sécurité de votre compte, vous devez maintenant lier un mode de vérification."}, "role": {"role": "R<PERSON><PERSON>", "roleName": "Nom du rôle", "remark": "<PERSON><PERSON><PERSON>", "authorization": "Autorisation", "subAdmin": "Sous-administrateur", "normalUser": "Utilisateur normal"}, "menu": {"name": "Nom", "parentName": "<PERSON><PERSON> supérieur", "icon": "Icône", "type": "Type", "orderNum": "Numéro de tri", "url": "URL du menu", "perms": "Identifiant d'autorisation", "mainMenu": "<PERSON>u principal", "parentMenuName": "Nom du menu parent", "permsTips": "Séparés par des virgules, par exemple : user:list,user:create", "menu": "<PERSON><PERSON>", "DirectoryMenu": "Menu de répertoire", "button": "Bouton", "HomeDirectoryMenu": "<PERSON><PERSON> <PERSON> répertoire principal"}, "log": {"user_name_user_action": "Nom d'utilisateur / Action de l'utilisateur", "user_action": "Action de l'utilisateur", "request_method": "<PERSON><PERSON><PERSON><PERSON> de requ<PERSON>", "request_parameters": "Paramètres de la requête", "execution_time": "Durée d'exécution (ms)", "ip_address": "Adresse IP", "commandId": "ID de commande", "response_result": "Résultat de la réponse", "schedule": "Progression", "ReasonForFailure": "Raison de l'échec", "RequestTimedOut": "<PERSON><PERSON><PERSON> d<PERSON>", "requestSucceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectionDoesNotExist": "La connexion n'existe pas", "Disconnect": "Connexion interrompue", "connectionClosed": "Connexion fermée", "requestException": "Exception de requête"}, "group": {"name": "Nom du groupe", "type": "Type de groupe", "addingAGroup": "Ajouter un groupe", "pleaseEnterAGroupName": "Veuillez saisir le nom du groupe", "addingSubgroup": "Ajouter un sous-groupe", "pleaseDeleteTheSubgroupsFirst": "Veuillez d'abord supprimer les sous-groupes"}, "cardDevice": {"deviceName": "Nom de l'appareil", "online": "En ligne", "networkType": "Type de réseau", "resolvingPower": "Résolution", "programTask": "Tâche de programme", "broadcastTask": "Tâche de diffusion", "screenStatus": "État de l'écran", "lastOffline": "Dernière déconnexion", "queryTerminalInfo": "Interroger les informations du terminal", "selectedCard": "<PERSON><PERSON> s<PERSON>", "brightness": "Luminosité", "volume": "Volume", "locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terminalInfoFirst": "Veuillez d'abord obtenir les informations du terminal", "width": "<PERSON><PERSON>", "height": "<PERSON><PERSON>", "synchronous": "Synchrone", "asynchronous": "Asynchrone", "temperature": "Température", "number": "<PERSON><PERSON><PERSON><PERSON>", "NoSim": "Pas d'informations de carte SIM", "fireWare": "Version du firmware"}, "operation": {"settingMode": "Mode de configuration", "connectionLog": "Journal de connexion", "LEDscreen": "Écran LED", "screenshot": "Capture d'écran", "liveVideo": "Vidéo en direct", "screenSwitch": "Interrupteur d'écran", "timingSwitch": "Interrupteur programmé", "screenBrightness": "Luminosité de l'écran", "autoBrightness": "Luminosité automatique", "timingBrightness": "Luminosité programmée", "volumeControl": "Contrôle du volume", "timingConfig": "Configuration de l'heure", "connConfig": "Configuration de connexion", "syncAndAsyncConfig": "Configuration synchrone/asynchrone", "alarmSwitch": "Interrupteur d'alarme", "onlineUpdate": "Mise à jour en ligne", "restartSys": "Redémarrer le système", "backgroundPlayback": "Arrière-plan du lecteur", "backupScreenParam": "Sauvegarde des paramètres d'écran", "restoreScreenParam": "Restaurer les paramètres d'écran", "hardwareStatus": "État du matériel", "manualconfigurationorquery": "Configuration/requête manuelle", "scheduledconfigurationorquery": "Configuration programmée", "thealias": "<PERSON><PERSON>", "webServerAddress": "<PERSON><PERSON><PERSON> <PERSON> serveur WEB", "thecompany": "Société", "realtimeaddress": "Adresse RealTime", "remove": "<PERSON><PERSON><PERSON><PERSON>", "volumeset": "Configuration en lot", "batchquery": "<PERSON><PERSON><PERSON><PERSON><PERSON> en lot", "group": "Groupe", "exportSIMInfo": "Exporter les informations SIM", "clearProgram": "Effacer le programme", "clearTask": "<PERSON>ff<PERSON><PERSON> la tâche", "callAddress": "Configuration de l'adresse d'appel", "alarmConfig": "Configuration de l'alarme", "clearBroadcastTask": "Effacer la tâche de diffusion", "queryOrClearTiming": "Req<PERSON>ête ou effacement programmé", "queryTiming": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "screenControl": "Contrôle de l'écran", "broadcastControl": "Contrôle de la diffusion", "monitoringControl": "Contrôle de la surveillance", "meteorologicalEnvironmentControl": "Contrôle de l'environnement météorologique", "passengerFlowStatistics": "Statistiques de flux de passagers", "lightingControl": "Contrôle de l'éclairage", "setSIPServerAddress": "Définir l'adresse du serveur SIP", "getSIPServerAddress": "Requ<PERSON>te de l'adresse du serveur SIP", "SIPServerAddress": "<PERSON><PERSON><PERSON> du serveur SIP", "AlarmEquipmentMacAddress": "Adresse MAC de l'équipement d'alarme", "AlarmEquipmentIpAddress": "Adresse IP de l'équipement d'alarme", "SetAlarmAddress": "Définir l'adresse d'alarme", "GetAlarmAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON> de l'adresse d'alarme", "AlarmAddress": "<PERSON><PERSON><PERSON> d'alarme", "CallAccount": "<PERSON><PERSON><PERSON> d'<PERSON>el", "AlarmVolume": "Volume d'alarme", "LightingLevel": "Niveau d'éclairage", "LightingSwitch": "Interrupteur d'éclairage", "Register_SIP_account": "Enregistrer le compte SIP", "getGspInfo": "Requête des informations GPS", "gpsInfo": "Informations GPS", "recordingFile": "Fichier d'enregistrement", "fullSizeScreenshotOfAndroid": "Capture d'écran pleine taille Android", "customSwitch": "Interrupteur personnalisé", "ThirdPartyAdvertising": "Publicité tierce", "BadPointDetection": "Détection de points défectueux", "playerState": "État du lecteur", "NumberOfCardsReceived": "Nombre de cartes reçues", "sensorDataShare": "Partage des données du capteur", "dataShare": "Partage de données", "dataRefreshCycle": "Cycle de rafraîchissement des données", "sharedDataKey": "Clé de données partagées", "curFlow": "Flux actuel", "isEnableCurFlow": "Activer le flux actuel", "flowAddress": "<PERSON><PERSON><PERSON>", "showLocation": "Afficher l'emplacement", "showPrefix": "<PERSON>ff<PERSON><PERSON> le préfixe", "leftTop": "Haut gauche", "rightTop": "<PERSON>ut droit", "leftBottom": "Bas gauche", "rightBottom": "Bas droit", "curFlowTip": "Si l'adresse du flux est vide, l'adresse par défaut de la plateforme sera utilisée", "detectingBadPixels": "Détection de points défectueux en cours", "uploadZip": "Télécharger Zip", "versionDelete": "Suppression de version", "hdvancedConfig": "Configuration avancée", "hardwareConfig": "Configuration matérielle", "realTimeSet": "Configuration RealTime", "networkConfig": "Configuration r<PERSON><PERSON>", "clearBroadcast": "Effacer la diffusion", "callVolumeControl": "Contrôle du volume d'appel", "queryProgramName": "Req<PERSON><PERSON>te du nom du programme", "list": "Liste", "new": "Nouveau", "oneClickOperateScreen": "Fermeture d'écran en un clic", "systemDisplay": "Résolution du système", "checkAddress": "Configuration de l'adresse de vérification"}, "tips": {"brightness": "Remarque : La luminosité est de 1 à 255", "volume": "Remarque : Le volume est de 0 à 15", "alarmVolume": "Remarque : Le volume est de 1 à 9", "liveVideo": "Prend en charge les protocoles rtmp, rtsp, veuillez d'abord installer live", "liveVideo1": "OFF désactive la diffusion en direct, ON l'active", "liveVideo2": "<PERSON><PERSON><PERSON> de <PERSON>", "screenSwitch": "Remarque : <PERSON><PERSON><PERSON><PERSON> le minutage pour réinitialiser l'interrupteur d'écran.", "screenTiming": "Remarque : <PERSON>roger l'interrupteur d'écran programmé (nécessite conn10.0.5T ou version ultérieure)", "autoBrightness": "Remarque : Cette fonction est prise en charge à partir de CardSystem-v3.6.0, la sensibilité doit être comprise entre 0 et 100", "autoBrightness1": "La luminosité s'ajuste automatiquement en fonction des données du capteur (nécessite conn10.0.5T ou version ultérieure)", "autoBrightness2": "Pour les cartes avec une luminosité maximale de 64, la luminosité minimale peut être configurée à 1% ou une valeur appropriée ; pour les cartes avec une luminosité maximale de 255, la luminosité minimale doit être configurée à 36% ou plus, sinon la luminosité sera trop faible.", "timingBrightness": "Remarque : Pendant la période définie, la luminosité est réglée, en dehors de cette période, la luminosité par défaut est utilisée. Par exemple, si la luminosité par défaut est de 80%, la luminosité réglée est de 20%, et la période est de 8:00 à 17:00, alors la luminosité sera de 20% pendant cette période et de 80% en dehors de cette période.", "manualconfigurationorquery1": "Uniquement pour les cartes M70 et M80", "manualconfigurationorquery2": "La période définie est en mode synchrone ; uniquement pour les cartes M70 et M80, cardSystem5.2.5.6-8 ou version ultérieure", "widthheighterror": "La largeur et la hauteur de l'écran ne peuvent pas être inférieures à 0 pixel", "noTenteredNotModified": "Les champs non saisis ne seront pas modifiés", "approval": "L'audit est effectué selon le processus défini, les processus sont triés par ordre croissant", "advancedParameter": "Remarque : Interface de paramètres avancés (nécessite conn10.0.5T ou version ultérieure)", "cardSelected": "<PERSON><PERSON> s<PERSON>", "cardNameSelected": "Terminal sélectionné", "numberEmpty": "Le nombre de cartes sélectionnées ne peut pas être vide", "progressBar": "Remarque : La barre de progression est affichée pour montrer le processus de mise à jour, le résultat réel dépend de l'état de la carte !", "upgradeFeatureSelectCard": "Aucune carte sélectionnée, impossible d'utiliser la fonction de mise à jour en ligne !", "UninstalledSuccessfully": "Statut : Désinstallation réussie !", "uninstallFeatureSelectCard": "Aucune carte sélectionnée, impossible d'utiliser la fonction de désinstallation en ligne !", "SelectUninstall": "Veuillez sélectionner le composant à désinstaller", "selectedNotExist": "Erreur de sélection de l'étiquette, n'existe pas", "backgroundTips": "La résolution de l'image doit correspondre à celle de l'écran, sinon une erreur se produira", "releaseTips": "Les cartes hors ligne seront envoyées hors ligne, une réponse de succès sera renvoyée, mais pas la barre de progression, les cartes de contrôle se reconnecteront automatiquement dans les 72 heures pour envoyer le programme", "releaseTips1": "Activer les journaux du lecteur, les journaux de lecture de chaque carte seront enregistrés après l'envoi réussi du programme", "releaseTips2": "Après l'activation des journaux du lecteur, les cartes de contrôle avec un programme existant téléchargeront les journaux sur la plateforme à l'intervalle défini", "SIPAddress": "Remarque : Si l'adresse du serveur SIP n'a pas été configurée, vous devez d'abord configurer l'adresse SIP pour utiliser les fonctions d'interphone et d'alarme, après la configuration, veuillez redémarrer la carte de contrôle.", "SIPAddress1": "Si le champ est vide, l'adresse de la plateforme actuelle sera utilisée comme adresse du serveur SIP ou une adresse SIP personnalisée", "alarmConfig": "Il est recommandé de définir l'adresse de la plateforme comme adresse d'alarme", "CustomCanUseNetworkAddress": "L'adresse réseau peut être utilisée pour la personnalisation", "networkAddressWhenCustomizing": "Veuillez saisir l'adresse réseau lors de la personnalisation", "broadcastTask": "Remarque : Si aucune tâche programmée n'est configurée, la diffusion sera automatiquement effacée après le redémarrage de l'appareil, la tâche ne sera pas sauvegardée !", "SIPTips": "Ce compte peut être utilisé pour des appels SIP avec la carte de contrôle, si le compte actuel est déjà enregistré, seule la modification est possible", "broadcastTaskRelease": "Remarque : Avant de publier une tâche de diffusion, assurez-vous que la fonction d'appel n'est pas en cours d'utilisation", "ModifyTerminal": "Astuce : Pour modifier l'alias du terminal, cliquez sur l'Internet des objets intelligents, passez en mode liste, puis cliquez sur modifier", "senorBrightnessTable": "Cette fonction nécessite systemCore version ******** ou ultérieure. Étapes : Configurez d'abord la sensibilité du capteur, puis modifiez via le fichier BrightnessTable.xlsx par défaut dans EasyBoard, les valeurs peuvent être modifiées, mais le format du fichier ne doit pas être modifié, sinon l'opération échouera ! Lors du téléchargement du fichier, seule une carte avec le même type de capteur peut être sélectionnée ; sinon, l'opération échouera !", "customSwitchTip": "Remarque : Si cet interrupteur est activé, l'interrupteur d'écran personnalisé sera utilisé, l'interrupteur d'écran d'origine sera désactivé, si cet interrupteur est désactivé, l'interrupteur d'écran d'origine sera activé, pour toute question, veuillez confirmer avec le personnel concerné !", "configAdTip": "Êtes-vous sûr de vouloir intégrer une publicité tierce ? Notre plateforme ne garantit pas la légalité et la conformité du contenu tiers, veuillez agir avec prudence.", "configAdTip1": "Activer", "configAdTip2": "Êtes-vous sûr de vouloir ouvrir la carte de contrôle à une plateforme tierce ?", "configAdTip3": "Cette fonction nécessite une mise à niveau de l'APK correspondant, pour plus de détails, veuillez contacter notre personnel !!!", "groupTip": "Après annulation, tous les appareils seront affichés", "passwordIsWeak": "Votre mot de passe est trop faible, veuillez le modifier. Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule, un chiffre et avoir une longueur d'au moins 9 caractères.", "authTip": "Le personnel examinera dans les 2-3 jours ouvrables, après approbation, la plateforme pourra être utilisée normalement.", "pointCheckTip": "Après la détection, veuil<PERSON><PERSON> patienter un moment pour vous assurer que la détection est terminée et obtenir les informations correctes sur la détection des points défectueux", "pointCheckCard": "Pour le plan d'utilisation, veuillez contacter notre personnel technique", "playLogsExportTip": "Lors de l'exportation des journaux, si aucune date n'est sélectionnée, les journaux du jour seront exportés, sinon tous les journaux dans la période spécifiée seront exportés.", "oneClickOperateScreenTip": "Cette opération éteindra tous les écrans en ligne"}, "file": {"name": "Nom du fichier", "type": "Type de fichier", "status": "État du fichier", "size": "<PERSON><PERSON>", "UploadProgress": "Progression du téléchargement", "download": "Télécharger", "thumbnail": "Miniature", "checkPending": "En attente d'approbation", "approved": "Approu<PERSON><PERSON>", "auditFailed": "Échec de l'approbation", "under_review": "En cours d'examen", "examine": "Examiner", "attachment": "Veuillez sélectionner une pièce jointe", "attachment1": "Cliquez ou faites glisser pour télécharger un fichier ici", "auditTime": "Heure de l'approbation", "file": "<PERSON><PERSON><PERSON>", "ApprovalComments": "Commentaires d'approbation", "upload": "Télécharger", "update": "Mettre à jour", "toView": "Voir", "WithoutPermission": "Sans autorisation", "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SerialNumber": "Numéro de série", "OnlineUpdate": "Mise à jour en ligne", "TheSize": "<PERSON><PERSON>", "VersionLog": "Journal des versions", "LogDetails": "Détails du journal", "Onlineupgrade": "Mise à niveau en ligne en cours...", "Waitingupdates": "En attente de mise à jour...", "Allcards": "Toutes les cartes mises à jour ne peuvent pas être mises à jour, veuillez vérifier le problème", "DownloadComplete": "Téléchargement terminé, décompressez pour mettre à jour", "NotSupported": "La barre de progression n'est pas prise en charge, chargement en cours...", "UpdateSuccessful": "Mise à jour réussie !", "UpdateFailed": "Échec de la mise à jour !", "ReadyDownload": "Préparation du téléchargement en cours !", "ConnectionFailed": "Échec de la connexion, veuillez vérifier l'appareil !", "ThreeSeconds": "Mise à jour terminée, la fenêtre de mise à jour se fermera dans trois secondes", "YouCanOnlyUploadUpTo5Files": "Vous ne pouvez télécharger que 5 fichiers maximum", "audio": "Audio", "fileOverSize": "Le fichier dépasse la taille maximale autorisée", "fileLimit": "Le fichier doit être au format docx", "fileVersion": "Version du fichier", "fileLimitPdfAndVideo": "Le fichier doit être au format pdf ou mp4"}, "card": {"cardId": "Numéro de série", "setTiming": "Définir le minutage", "getTiming": "<PERSON><PERSON><PERSON> le minutage", "noTiming": "Pas de minutage", "timing": "Minutage", "notSpecified": "Non spécifié", "dateType": "Type de date", "DateRange": "Plage de dates", "startDate": "Date de début", "endDate": "Date de fin", "timeType": "Type d'heure", "timeFrame": "<PERSON>lage horaire", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "SpecifyWeek": "Spécifier la semaine", "WeekRange": "Plage de semaines", "PleaseScheduledTask": "Veuillez spécifier le type de tâche programmée", "sensitivity": "Sensibilité", "Minbrightness": "Luminosité minimale", "defaultBrightness": "Luminosité par défaut", "timingBrightness": "Luminosité programmée", "timedVolume": "Volume programmé", "defaultVolume": "Volume par défaut", "cardVoltage": "Tension de la carte", "externalVoltage1": "Tension externe 1", "externalVoltage2": "Tension externe 2", "externalVoltage3": "Tension externe 3", "externalVoltage4": "Tension externe 4", "doorOpen": "Porte ouverte", "version": "Version", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "temperature": "Température", "smokeWarning": "Avertissement de fumée", "querySuccessful": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>, affichage des données", "queryFailed": "<PERSON><PERSON><PERSON> de la requête, impossible d'afficher les données", "screenWidth": "Largeur de l'écran (pixels)", "screenHeight": "Hauteur de l'écran (pixels)", "screenAlias": "Alias de l'écran", "genericVersion": "Version générique", "notChosenCard": "Carte non sélectionnée", "TestVersion": "Version de test", "rebootNow": "Redémarrer maintenant", "monitorTip": "Un maximum de 6 flux vidéo peuvent être affichés simultanément, veuillez en sélectionner à nouveau", "picture-in-picture": "Image dans l'image", "pictureTip": "Les coordonnées et les dimensions ne doivent pas dépasser la résolution visible de l'écran, pour redéfinir, vous devez d'abord désactiver l'image dans l'image", "coordinate": "Coordonnées", "pictureSize": "<PERSON>lle de l'image", "checkAddressTip": "Après activation, saisissez l'adresse du serveur d'audit des médias pour l'audit du contenu des médias sur l'appareil", "mediaContentReview": "Vérification du contenu multimédia", "realtimeReview": "Vérification en temps réel", "realtimeReviewTips": "L'activation de la vérification en temps réel entraîne une consommation de trafic", "clearPrompt": "Effacer l'invite", "interval": "Intervalle de temps"}, "approval": {"auditName": "Nom de l'audit", "auditType": "Type d'audit", "approvalProcess": "Processus d'approbation", "Reviewer": "Auditeur", "order": "Ordre", "mediaResources": "Ressources multimédias", "ProgramType": "Type de programme", "BroadcastMediaResources": "Ressources multimédias de diffusion", "BroadcastTaskResources": "Ressources de tâches de diffusion", "noAudit": "Aucun audit requis", "approved": "Approu<PERSON><PERSON>", "auditFailed": "Échec de l'audit", "select_at_least_one_reviewer": "Veuillez sélectionner au moins un auditeur", "approver_cannot_blank": "L'auditeur ne peut pas être vide", "approval_order_cannot_blank": "L'ordre d'approbation ne peut pas être vide", "InsufficientUsers": "Utilisateurs insuffisants", "clickAudit": "Audit en un clic", "batchReview": "Audit par lot", "auditMemo": "Le programme a des problèmes, veuillez les ajuster avant de soumettre à nouveau."}, "program": {"program": "Programme", "type": "Type de programme", "name": "Nom du programme", "ordinaryProgram": "Programme ordinaire", "insertProgram": "Programme d'insertion", "totalSize": "<PERSON>lle totale", "state": "État de la ressource", "ProgramList": "Liste des programmes", "ProgramInfo": "Informations sur le programme", "ComponentProperties": "Propriétés des composants", "ProgramProperties": "Propriétés du programme", "PlaybackMode": "Mode de lecture", "EntryEffects": "Effets d'entrée", "DurationMobilizationEffect": "Durée des effets d'entrée (s)", "AppearanceEffects": "Effets de sortie", "DurationAppearanceEffect": "Durée des effets de sortie (s)", "StartPlaybackTime": "<PERSON><PERSON> de début de lecture (s)", "DurationContinuousDisplay": "Durée d'affichage continue (s)", "region": "Région", "upper": "<PERSON><PERSON>", "left": "G<PERSON><PERSON>", "width": "<PERSON><PERSON>", "height": "<PERSON><PERSON>", "BasicProperties": "Propriétés de base", "background": "Arrière-plan", "pellucidity": "Transparence", "DisplayDackground": "Afficher l'arrière-plan", "open": "Ouvert", "close": "<PERSON><PERSON><PERSON>", "BackgroundColor": "Couleur de l'arrière-plan", "DisplayHourScale": "Afficher l'échelle des heures", "HourScaleColor": "Couleur de l'échelle des heures", "ShowMinuteScale": "Afficher l'échelle des minutes", "MinuteScaleColor": "Couleur de l'échelle des minutes", "ScaleStyle": "Style de l'échelle", "IntegerScaleDigitalDisplay": "Affichage numérique de l'échelle entière", "PointerStyle": "Style du pointeur", "ClockPointerColor": "<PERSON><PERSON><PERSON> du pointeur de l'horloge", "MinutePointerColor": "<PERSON><PERSON><PERSON> du pointeur des minutes", "SecondPointerColor": "<PERSON><PERSON><PERSON> du pointeur des secondes", "DisplaySecondHand": "Afficher la trotteuse", "up": "Vers le haut", "down": "Vers le bas", "play": "Lecture", "times": "<PERSON><PERSON>", "PleaseEnterContent": "Veuillez saisir le contenu", "text": "Texte", "DigitalClock": "Horloge numérique", "analogClock": "Horloge analogique", "EnvironmentalMonitoring": "Surveillance environnementale", "weather": "<PERSON><PERSON><PERSON><PERSON>", "Multi-materialWindow": "<PERSON><PERSON><PERSON> multi-matéria<PERSON>", "html": "Page web", "weburl": "URL de la page web", "enterTime": "Veuillez saisir l'heure", "Multi-material": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empty": "Vider", "oneLevelUp": "Un niveau vers le haut", "oneLevelDown": "Un niveau vers le bas", "layerOnTop": "Couche en haut", "bottomLayer": "Couche en bas", "FullScreen": "Plein écran", "pageProperties": "Propriétés de la page", "effectiveDate": "Date d'effet", "PlayProperties": "Propri<PERSON><PERSON><PERSON>", "planSchedule": "Planification", "sun": "<PERSON><PERSON><PERSON>", "one": "<PERSON><PERSON>", "two": "<PERSON><PERSON>", "three": "<PERSON><PERSON><PERSON><PERSON>", "four": "<PERSON><PERSON>", "five": "<PERSON><PERSON><PERSON><PERSON>", "six": "<PERSON><PERSON>", "clockProperties": "Propriétés de l'horloge", "PleaseSelectATimeZone": "Veuillez sélectionner un fuseau horaire", "year": "<PERSON><PERSON>", "month": "<PERSON><PERSON>", "day": "Jour", "hour": "<PERSON><PERSON>", "Minute": "Minute", "Second": "Seconde", "Week": "<PERSON><PERSON><PERSON>", "AM": "AM", "PM": "PM", "fourYears": "Année à quatre chiffres", "12HourClock": "Format 12 heures", "morningAfternoon": "Matin/Après-midi", "style": "Style", "dateStyle": "Style de date", "timeStyle": "Style d'heure", "displayStyle": "Style d'affichage", "singleLine": "Ligne unique", "Multi-line": "Multi-lignes", "fontSettings": "Paramètres de police", "fontSize": "Taille de la police", "fontColor": "Couleur de la police", "PlayTime": "<PERSON><PERSON><PERSON>", "specialEffects": "<PERSON><PERSON><PERSON> sp<PERSON>ciaux", "specificFrequency": "<PERSON><PERSON><PERSON> spécifi<PERSON>", "blink": "Clignotement", "breathe": "Respiration", "MonitoringProperties": "Propriétés de surveillance", "compensate": "Compensation", "windSpeed": "Vitesse du vent", "windDirection": "Direction du vent", "noise": "Bruit", "atmosphericPressure": "Pression atmosphérique", "rainfall": "Précipitations", "radiation": "Radiation", "lightIntensity": "Intensité lumineuse", "DisplayMode": "Mode d'affichage", "stayLeft": "<PERSON><PERSON><PERSON> à gauche", "Centered": "Centré", "KeepRight": "<PERSON><PERSON><PERSON> d<PERSON>", "singleLineScroll": "Défilement en ligne unique", "speed": "Vitesse", "ms/pixel": "ms/pixel", "refreshCycle": "Cycle de rafraîchissement", "minute": "Minute", "fileProperties": "Propriétés du fichier", "Multi-MaterialBasicProperties": "Propriétés de base multi-matériaux", "mediaList": "Liste des médias", "SelectedMaterialInformation": "Informations sur le matériau sélectionné", "HourMarkColor": "Couleur de l'échelle des heures", "minuteScaleColor": "Couleur de l'échelle des minutes", "hourHandColor": "Couleur de l'aiguille des heures", "minuteHandColor": "Couleur de l'aiguille des minutes", "pointerColor": "<PERSON><PERSON><PERSON> du pointeur", "backgroundColor": "Couleur de l'arrière-plan", "static": "Statique", "scroll": "<PERSON><PERSON><PERSON><PERSON>", "turnPages": "Tourner les pages", "total": "Total", "Page": "Page", "preview": "<PERSON><PERSON><PERSON><PERSON>", "stopPreview": "<PERSON><PERSON><PERSON><PERSON> l'aperçu", "TextEditor": "Éditeur de texte", "province": "Province", "Multi-material_text": "Veuillez ajouter des médias à droite, vous pouvez ajouter plusieurs médias différents, l'écran LED les affichera dans l'ordre de la liste.", "streaming": "Streaming", "direction": "Direction du défilement", "ToTheLeft": "Vers la gauche", "upward": "Vers le haut", "ToTheRight": "Vers la droite", "addText": "A<PERSON>ter du texte", "liveStreamAddress": "Adresse du flux en direct", "deviceAddress": "<PERSON><PERSON><PERSON> de l'appareil", "deviceAddrTip": "L'adresse actuelle est utilisée par défaut (https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey), veuillez ne pas la modifier arbitrairement", "deviceKey": "Clé de l'appareil", "deviceKeyTip": "La clé de l'appareil est remplie par l'utilisateur de la plateforme pour spécifier la clé de l'appareil, le programme envoyé affichera les informations de flux de l'appareil spécifié sur l'écran.", "CustomHTML": "HTML personnalisé", "CustomHTMLTip1": "Numéro de série de l'appareil", "CustomHTMLTip2": "<PERSON><PERSON>", "CustomHTMLTip3": "Nombre total d'entrées aujourd'hui", "CustomHTMLTip4": "Nombre d'entrées dans cette tranche horaire", "CustomHTMLTip5": "Nombre total d'entrées historiques", "CustomHTMLTip6": "Nombre total de sorties aujourd'hui", "CustomHTMLTip7": "Nombre de sorties dans cette tranche horaire", "CustomHTMLTip8": "Nombre total de sorties historiques", "flowStatistics": "Statistiques de flux", "weatherTip1": "Température actuelle", "weatherTip2": "AQI (Indice de qualité de l'air)", "weatherTip3": "Date du jour (incluant la température en temps réel)", "weatherTip4": "<PERSON><PERSON><PERSON><PERSON>our", "weatherTip5": "Température maximale du jour", "weatherTip6": "Température minimale du jour", "weatherTip7": "Direction du vent du jour", "weatherTip8": "Force du vent du jour", "weatherTip9": "Image météo du jour, format : img-largeur-hauteur", "weatherTip10": "Dans %{}, yesterday représente hier, arr.0 représente aujourd'hui, 1 représente demain, 2 représente après-demain, 3 représente le surlendemain, 4 représente le sursurlendemain", "timeType": "Type d'heure", "timeTypeTip": "Modifier le type changera le HTML personnalisé", "HDMITypeDescription": "Description du type HDMI", "HDMIDescription1": "1. Les programmes de type HDMI ne sont actuellement pris en charge que pour la série m70. Après l'envoi réussi, veuillez vérifier l'affichage réel sur l'écran. L'affichage réel dépend de l'écran.", "HDMIDescription2": "2. Si l'affichage réel est incorrect, veuillez d'abord vérifier la connexion du câble HDMI ou si la version du logiciel est incorrecte. Si tout est correct, vous pouvez essayer de renvoyer. Si la carte de contrôle est en mode HDMI Picture-in-Picture, elle doit d'abord être définie en mode synchrone avant de pouvoir être redéfinie. Pour le plan d'utilisation, veuillez contacter notre personnel technique.", "text-to-speech": "Texte en parole", "addProgramTips": "La création d'un programme ordinaire remplacera le programme original après un envoi réussi. La création d'un programme d'insertion ne remplacera pas le programme original après un envoi réussi, il ne sera joué que pendant la période d'insertion.", "enablePlayerLog": "<PERSON><PERSON> les journaux du lecteur", "playLog": "Journal de lecture", "timeInterval": "Intervalle de temps (minutes)", "discount": "Longueur de chaque segment", "isDiscount": "Avec réduction", "discountText": "Longueur de séparation de chaque segment, plusieurs séparées par des virgules. Exemple : 256,256,128", "segmentation": "Segmentation", "PleaseEnterDiscountWidth": "Veuillez saisir la largeur de réduction", "PleaseEnterTheCorrectContentFormat": "Veuillez saisir un format de contenu correct", "multi-picture": "Multi-images", "PleaseSelectPictureVideoSplit": "V<PERSON><PERSON>z sélectionner une image ou une vidéo pour la segmentation", "totalWidthDiscountCannotWidth": "La longueur totale de réduction ne peut pas dépasser la taille du matériau", "sensorsShareData": "Partage des données des capteurs", "broadcastSort": "Ordre de diffusion", "horizontal": "Horizontal", "verticalRow": "Vertical", "discountMode": "Mode de réduction", "level": "Niveau", "vertical": "Vertical", "negativeIon": "<PERSON><PERSON>", "zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "materialCycle": "Cycle de matériau", "refreshSec": "Intervalle de rafraîchissement", "zoom": "Zoom", "offset": "Décalage", "scale": "<PERSON><PERSON><PERSON>"}, "setTime": {"timeZone": "<PERSON><PERSON> horaire/<PERSON>", "y60Channels": "Remarque : Cette fonction nécessite CardSystem_v5.2.6.3 ou version ultérieure !", "theTimeZone": "<PERSON><PERSON> ho<PERSON>", "setUpThe": "Configurer", "query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "computerTime": "Calibrer l'horloge sur l'heure de l'ordinateur", "ledTime": "Requête de l'heure actuelle de l'appareil LED", "queryFails": "Échec de la requête !", "versionCardSystem": "Veuillez vérifier la version de CardSystem !", "deviceTimeZone": "<PERSON><PERSON> horaire de l'appareil", "setupFailed": "Échec de la configuration", "setupSuccess": "Configuration réussie", "connectTo485": "Connecté à 485", "calibrationFailure": "Échec de la calibration", "successfulCalibration": "Calibration réussie", "querySuccessful": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "synchronizationSettings": "Paramètres de synchronisation", "model": "Mode", "masterSlave": "Maître/Esclave", "IdentificationCode": "Code d'identification", "timeOffset": "Décalage horaire (ms)", "screenBrightness": "Luminosité de l'écran", "volume": "Volume", "screenSwitch": "Interrupteur d'écran", "synchronizationInterval": "Intervalle de synchronisation", "lastSynchronousTime": "Dernière synchronisation", "minTime": "(minutes/fois)", "main": "<PERSON><PERSON><PERSON>", "from": "Esclave", "masterSlaveMode": "Mode maître/esclave", "NTPpathNull": "Lorsque le chemin NTP est vide, la connexion expire, mais la configuration réussit !", "serverAddress": "Adresse du serveur NTP", "selectA": "Veuillez sélectionner un mode maître/esclave", "selectATime": "Veuillez sélectionner un fuseau horaire"}, "synchronous": {"unknownError": "<PERSON><PERSON><PERSON> inconnue", "doesNotExist": "Erreur de l'état réseau de la carte, veuillez vérifier la version de CardSystem", "format": "Aucune configuration de minutage"}, "home": {"totalNumber": "Nombre total d'appareils", "onlineRate": "Taux de connexion", "number": "Nombre", "brightScreen": "Taux d'écran allumé", "operating": "Nombre total d'opérations", "program": "Nombre total de programmes", "switchDate": "Changer de date", "month": "<PERSON><PERSON>", "Announcement": "<PERSON><PERSON><PERSON>", "determined": "En attente", "programmeStatistics": "Statistiques de création de programmes", "operationStatistics": "Statistiques des opérations utilisateur", "releasePeople": "Personne de publication", "date": "Date", "noMoreAnnouncements": "Plus d'annonces !", "show": "Programme", "by": "Nombre total d'audits", "operat": "Nombre d'opérations", "operatingSpeed": "Vitesse d'opération (ms)", "Reviewrate": "Taux d'approbation", "statistics": "Statistiques de publication de programmes", "cardNumber": "Numéro de carte", "releaseAmount": "Nombre total de publications", "successRate": "<PERSON><PERSON>", "totalSuccess": "Nombre total de réussites", "successful": "Réussite", "failure": "Échec", "founder": "<PERSON><PERSON><PERSON><PERSON>", "TotalAverageMilliseconds": "Nombre total de millisecondes moyennes", "great": "Excellent", "good": "<PERSON>", "center": "<PERSON><PERSON><PERSON>", "poor": "<PERSON><PERSON><PERSON><PERSON>", "NaN": "NaN", "clickRefresh": "Cliquer pour rafraîchir", "warningNotice": "Notification d'alarme", "temperatureWarning": "Alerte de température", "humidityWarning": "Alerte d'humidité", "voltageWarning": "Alerte de tension de carte", "voltage1Warning": "Alerte de tension externe 1", "voltage2Warning": "Alerte de tension externe 2", "doorOpenWarning": "Alerte de porte ouverte", "smokeWarning": "Alerte de fumée", "unknownWarning": "Alerte inconnue", "temporarilyNoData": "Aucune donnée pour le moment", "showsSentProgram": "Afficher les cartes ayant envoyé des programmes", "announcementDetails": "Détails de l'annonce", "policeDetails": "Dé<PERSON> de l'alarme", "untreated": "Non traité", "haveDeal": "Traité", "noMoreCalls": "Plus d'alarmes !", "refreshSuccessful": "Rafraîchissement réussi !", "DynamicLargeScreen": "Plateforme de visualisation des terminaux intelligents", "piece": "<PERSON><PERSON><PERSON>", "EquipmentStatistics": "Statistiques des appareils", "unitPCS": "Unité : <PERSON><PERSON><PERSON>", "PoleStatistics": "Statistiques des terminaux", "ControlStatistics": "Statistiques de contrôle", "UnitTimes": "Unité : <PERSON><PERSON>", "TotalNumberOfPoles": "Nombre total de terminaux"}, "announcement": {"titleText": "Titre/Contenu de l'annonce", "title": "Titre", "enterTitle": "Veuillez saisir le titre de l'annonce", "content": "Contenu", "enterContent": "Veuillez saisir le contenu de l'annonce", "also": "Peut encore saisir", "character": "caractères"}, "resetPWD": {"resetPassword": "Réinitialiser le mot de passe", "accountNumber": "<PERSON><PERSON> le compte", "repairMethod": "Sélectionner la méthode de réparation", "changePassword": "Modifier le mot de passe", "success": "Su<PERSON>ès", "enterResetPassword": "Veuillez saisir le compte pour réinitialiser le mot de passe"}, "police": {"notOpenSettingsNull": "Les champs non activés seront considérés comme vides", "monitoringItems": "Éléments de surveillance", "hasBeenOpen": "Activé", "lower": "Limite inférieure", "ceiling": "Limite supérieure", "haveSmoke": "Détection de fumée", "openDoorAlarm": "Alarme de porte ouverte", "turnSmokeAlarm": "Activer l'alarme de fumée", "checkCardSysterm": "Impossible d'analyser l'opération, veuillez vérifier la version de CardSysterm", "isNotOpened": "Erreur de l'état de connexion de la carte", "sureToSetAlarmThresholds": "Êtes-vous sûr de vouloir définir le seuil d'alarme ?", "upperAndLowerEmpty": "Les éléments de surveillance activés ne peuvent pas avoir de limites vides", "numEmpty": "Les éléments de surveillance activés doivent avoir des valeurs numériques", "upperGreaterLower": "Pour les éléments de surveillance activés, la limite supérieure doit être supérieure à la limite inférieure", "materialLibrary": "Bibliothèque de ressources"}, "hardware": {"timeQuery": "La sauvegarde des paramètres prend du temps, si le fichier de sauvegarde n'est pas trouvé immédiatement, veuil<PERSON><PERSON> réessayer plus tard !", "restoreParam": "Restaurer les paramètres", "backupParameter": "Sauvegarder les paramètres", "hardwareStatus": "État du matériel", "restore": "<PERSON><PERSON><PERSON>", "akeyBackup": "Sauvegarde en un clic", "backupSuccessful": "<PERSON>uvegarde r<PERSON>", "backupSuccessful1": "La version actuelle ne renvoie pas de progression", "BackingUp": "Sauvegarde en cours... V<PERSON>illez patienter", "selectionCard": "Aucune carte sélectionnée, impossible d'utiliser la fonction de paramètres matériels", "parameterRecovery": "Restauration des paramètres en cours...", "waitingRecover": "En attente de restauration...", "namePackage": "Carte/package associé", "restoreCancel": "L'état de connexion de cette carte est incorrect, restauration annulée !", "readyRecovery": "Préparation de la restauration en cours !", "tryAgain": "Toutes les cartes sélectionnées ont des problèmes de connexion, veuillez vérifier et réessayer !", "recoveryComplete": "Restauration terminée !", "afterRecovery": "Restauration terminée, la fenêtre de mise à jour se fermera dans trois secondes", "Recovering": "Restauration en cours...", "timesOut": "Certaines cartes ne prennent pas en charge la restauration des paramètres, la requête a expiré, la restauration est terminée !"}, "el": {"colorpicker": {"confirm": "Confirmer", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "image": {"error": "Échec du chargement de l'image"}, "table": {"emptyText": "<PERSON><PERSON><PERSON> donnée disponible"}, "pagination": {"total": "Total", "pagesize": "lignes/page", "goto": "<PERSON><PERSON>", "pageClassifier": "page"}}, "task": {"name": "Nom de la tâche", "isCycle": "Est-ce une boucle", "cycleIndex": "Nombre de cycles", "InfiniteLoop": "<PERSON><PERSON><PERSON> infinie", "task": "<PERSON><PERSON><PERSON>", "type": "Type de tâche", "text": "Contenu du texte", "voiceName": "Nom de la voix", "speed": "Vitesse de la voix", "pitch": "Ton de la voix", "femaleVoice": "Voix féminine", "maleVoice": "Voix masculine", "textToLanguage": "Texte en parole", "media": "Média", "plays": "Nombre de lectures", "selectMedia": "Sélectionner un média", "TemplateContent": "Contenu du modèle", "ImportTemplate": "Importer un modèle", "import": "Importer", "normal": "Normal", "faster": "Plus rapide", "fast": "Rapide", "playTypes": "Mode de lecture", "specifyPlays": "Nombre de lectures spécifié", "specifyPlayTime": "Temps de lecture spécifié", "isTiming": "<PERSON><PERSON> le minutage", "allTime": "Période de lecture complète", "inStream": "Est-ce une tâche d'insertion", "normalTask": "Tâche normale", "inStreamTask": "Tâche d'insertion", "clearInStream": "Effacer uniquement les tâches d'insertion"}, "lamp": {"poleName": "Nom du terminal", "broadcast": "Diffusion", "monitor": "Surveillance", "environment": "Environnement météorologique", "lighting": "Éclairage", "Passenger": "Statistiques de flux de passagers", "longitude": "Longitude", "latitude": "Latitude", "ChooseTargeting": "Sélectionner le positionnement", "LoadingPositioning": "Chargement du positionnement en cours", "FailedToGetLocationInformation": "Échec de la récupération des informations de position", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "targetingIsSet": "Positionnement défini", "targetingNotSet": "Positionnement non défini", "listMode": "Mode liste", "mapMode": "Mode carte", "updateTime": "<PERSON><PERSON> de mise à jour", "TheSensorIsNot": "Aucun capteur connecté pendant cette période", "getMapException": "Erreur lors de la récupération de la carte", "NoLocationData": "<PERSON><PERSON>ne donn<PERSON> de <PERSON>", "PleaseEnterThePoleNameOrdeviceID": "Veuillez saisir le nom du terminal ou l'ID de l'appareil", "radioState": "État de la diffusion", "latAndLngNotEmpty": "La longitude et la latitude ne peuvent pas être vides", "gpsUploadState": "Téléversement GPS"}, "broadcast": {"SIPAddress": "Confirmer l'adresse du serveur de la plateforme comme adresse SIP", "customSIPAddressStart": "Confirmer la configuration", "customSIPAddressEnd": "comme adresse SIP", "radioState1": "Uniquement la diffusion", "radioState2": "Diffusion et interphone, pas d'alarme", "radioState3": "Diffusion et alarme, pas d'interphone", "radioState4": "Diffusion, interphone et alarme", "SIP_account": "Compte SIP", "multicastAddress": "Adresse multicast", "selectDate": "Sélectionner une date", "pauseOrOpenBroadcast": "Suspendre/Activer la tâche de diffusion", "broadcastInfo": "Détails de la diffusion", "broadcastProgramState": "État du programme de diffusion", "paused": "Suspendu", "playing": "En cours de lecture", "haveProgram": "Y a-t-il un programme en direct", "playMode": "Mode de lecture", "focusMode": "Mode focus", "fallingTone": "Baisse de ton", "mute": "<PERSON><PERSON>", "noProgram": "Aucun programme", "noBroadcast": "Aucune diffusion"}, "meteorological": {"temperatureSubText": "Une température de -- indique une erreur de requête ou aucune donnée", "Illuminance": "Luminosité", "humiditySubText": "Une humidité de -- indique une erreur de requête ou aucune donnée", "noiseSubText": "Un bruit de -- indique une erreur de requête ou aucune donnée", "windSpeedSubText": "Une vitesse de vent de -- indique une erreur de requête ou aucune donnée", "windDirectionSubText": "Une direction de vent de -- indique une erreur de requête ou aucune donnée", "illuminationSubText": "Une luminosité de 0 indique une erreur de requête ou aucune donnée", "PM10SubText": "Un PM10 de -- indique une erreur de requête ou aucune donnée", "PM25SubText": "Un PM2.5 de -- indique une erreur de requête ou aucune donnée", "pressureSubText": "Une pression de -- indique une erreur de requête ou aucune donnée", "rainFallSubText": "Des précipitations de -- indiquent une erreur de requête ou aucune donnée", "radiationSubText": "Une radiation de -- indique une erreur de requête ou aucune donnée", "pressure": "Pression", "rainFall": "Précipitations", "radiation": "Radiation"}, "bigScreen": {"VideoSurveillance": "Surveillance vidéo", "DeviceList": "Liste des appareils", "Address": "<PERSON><PERSON><PERSON>", "NumberOfOnline": "Nombre d'appareils en ligne", "OperationLog": "Journal des opérations", "ProgramPlayStatistics": "Statistiques de lecture des programmes", "RequestFailed": "Échec de la requête"}, "electricity": {"current": "<PERSON><PERSON><PERSON>", "power": "Puissance", "electricity": "Énergie électrique", "voltage": "Tension", "currentSubText": "Un courant de 0 indique une erreur de requête ou aucune donnée", "powerSubText": "Une puissance de 0 indique une erreur de requête ou aucune donnée", "electricitySubText": "Une énergie électrique de 0 indique une erreur de requête ou aucune donnée", "voltageSubText": "Une tension de 0 indique une erreur de requête ou aucune donnée", "clearData": "Efface<PERSON> les données", "clearSuccessfully": "Effacement ré<PERSON>i", "clearFailed": "Échec de l'effacement", "cancelClear": "Annuler l'effacement", "monthlyElectricity": "Statistiques mensuelles d'énergie électrique", "exportElectricity": "Exporter les données mensuelles d'énergie électrique", "setElectricityTime": "Définir la période de requête d'énergie électrique", "selectTime": "Veuillez sélectionner la période de requête d'énergie électrique", "tip": "La période de requête par défaut est de 24 heures.", "electricityData": "Données d'énergie électrique", "curElectricityTime": "Période actuelle"}, "monitor": {"device_ip": "IP de l'appareil", "port": "Port", "owning_terminal": "Terminal associé", "monitorSaveTip": "Pour les appareils en ligne, veuillez modifier avec précaution le nom d'utilisateur et le mot de passe, une erreur pourrait entraîner la déconnexion de l'appareil", "Device_name_cannot_be_empty": "Le nom de l'appareil ne peut pas être vide", "Please_enter_the_device_serial_number": "Veuillez saisir le numéro de série de l'appareil", "monitorSaveTip1": "Modifier le nom d'utilisateur et le mot de passe en : nom d'utilisateur", "Split_screen": "Écran partagé", "PTZ_control": "Contrôle PTZ", "equipment": "Équipement", "deviceIdIsNotEmpty": "L'ID de l'appareil ne peut pas être vide", "CommandISExist": "La commande n'existe pas", "notExistOrNotLoggedIn": "Appareil introuvable ou non connecté", "isNotExist": "L'appareil n'existe pas", "notLoggedIn": "L'appareil n'est pas connecté", "zoom": "Zoom", "aperture": "Ouverture", "focus": "<PERSON>se au point", "screenshot": "Capture d'écran", "noPlayback": "Aucun enregistrement pour cette période", "downloadPlayback": "Télécharger l'enregistrement", "selectDateRange": "Sélectionner une période", "tip": "Ce téléchargement transfère l'enregistrement de la caméra vers le serveur, veuillez patienter un moment avant de le télécharger localement depuis cette page.", "normal": "Caméra standard", "humanNumberStatistic": "Statistiques de flux de passagers", "insideHumanNumber": "Regroupement de personnes", "traffic": "Trafic intelligent", "cameraType": "Type de caméra", "monitorSaveTip2": "Modifier le type de fonction de la caméra en :", "openTheAlarm": "Activer l'alarme", "crowdAlarmThreshold": "<PERSON><PERSON> d'alarme de foule", "intervalForSendingEmails": "Intervalle d'envoi des e-mails", "openTheAlarmTip": "Après activation de l'alarme, un e-mail sera envoyé à l'utilisateur propriétaire de l'appareil si le seuil est dépassé, et les e-mails suivants seront envoyés selon l'intervalle défini.", "offLineOrNotExist": "Appareil hors ligne ou déconnecté", "serialNumber": "Numéro de série de la caméra", "rtmpStreamState": "Activer le flux RTMP", "streamCloseTip": "Après la désactivation du flux, vous ne pourrez plus voir la vidéo en direct sur la page web, veuil<PERSON><PERSON> confirmer.", "streamOpenTip": "Après l'activation du flux, vous pourrez voir la vidéo en direct sur la page web, cette opération consommera une certaine bande passante, ve<PERSON><PERSON><PERSON> confirmer."}, "pay": {"contactInformation": "連絡先情報", "addressValue": "香港中環皇后大通り1号", "savingsAccountNumber": "預金口座番号", "xixunCompanyValue": "上海熙訊電子科技有限公司", "bankName": "銀行名", "bankNameValue": "香港上海滙豐銀行有限公司", "swiftCode": "国際コード", "tips1": "VIP機器を購入してください。そうしないと正常にオンラインにできません。", "tips": "ログインに成功した後、有料サービスでVIPを注文すると、機器が正常にオンラインにできます。", "totalPrice": "総価格", "contactPerson": "連絡先担当者", "contactPersonTips": "連絡先担当者を入力してください。", "telephone": "連絡先電話番号", "telephoneTips": "連絡先電話番号を入力してください。", "address": "会社の住所", "addressTips": "会社の住所を入力してください。", "companyName": "会社の正式名称", "companyNameTips": "会社の正式名称を入力してください。", "vipDuration": "VIP有効期間/年", "vip4": "極上ダイヤモンドVIP4", "upgradeToPaidVersion": "Passer à la version payante", "silverCardVip": "VIP <PERSON><PERSON>rgent", "goldCardVip": "VIP Carte Or", "diamondVip": "VIP Diamant", "superVip": "Super VIP", "orderService": "Commander un service", "currentVersion": "Version actuelle :", "numberOfTerminals": "Nombre de terminaux", "TheRemainingAmount": "Quantité restante", "ExpireDate": "Date d'expiration", "selectVersion": "Sélectionner la version", "SelectDuration(years)": "Sélectionner la durée (années)", "totalOrder": "Montant total de la commande", "submitOrder": "Soumettre la commande", "freeVersion": "Version gratuite", "price": "Prix", "ThereAreUnpaidOrders": "Il existe des commandes non payées", "ThereIsAnUnpaidOrderPleaseGoToPay": "Il existe une commande non payée, veuillez procéder au paiement.", "OrderRecord": "Historique des commandes", "100ControlCards": "Nombre de cartes de contrôle : 100", "500ControlCards": "Nombre de cartes de contrôle : 500", "NumberOfControlCards1500": "Nombre de cartes de contrôle : 1500", "unpaid": "Non payé", "transactionCreation": "Création de la transaction", "UnpaidTransactionTimeoutClosed": "Transaction non payée expirée et fermée", "paymentSuccessful": "<PERSON><PERSON><PERSON>", "OrderDetails": "<PERSON><PERSON><PERSON> de la commande", "pay": "Payer", "orderNumber": "<PERSON><PERSON><PERSON><PERSON> de commande", "Years": "<PERSON><PERSON><PERSON> (années)", "PaymentStatus": "Statut du paiement", "amount": "<PERSON><PERSON>", "newPurchase": "Nouvel achat", "Renewal": "Renouvellement", "upgrade": "Mise à niveau", "PayForTheOrder": "Payer la commande", "ExpectedPeriod": "Période prévue : ", "to": "à", "ActualDeadlineIsSubjectToPayment": "La date limite réelle dépend du paiement", "cancelOrder": "Annuler la commande", "theOrderWillBeAutomaticallyClosed": "La commande est valable 15 jours, veuillez payer à temps. Après 14 jours sans paiement, la commande sera automatiquement fermée", "AfterOrderIsPaidSuccessfully": "Remarque : <PERSON><PERSON> le paiement réussi de la commande, vous pouvez accéder à", "QueryPaymentInformationOnThePage": "la page pour consulter les informations de paiement", "paymentMethod": "Méthode de paiement", "publicAccount": "Compte public", "AccountName": "Nom du compte :", "AccountBank": "Banque :", "LinkNumber": "Numéro de liaison :", "account": "Compte :", "uploadTheBankReceipt": "Si vous avez effectué un virement vers le compte ci-dessus, ve<PERSON><PERSON><PERSON> télécharger le reçu bancaire.", "receivedByMajorBanks": "Le délai de traitement des virements bancaires peut varier de 2 heures à 3 jours ouvrables.", "notifyYouViaSMS": "Notre personnel vous informera dès que le paiement sera reçu", "UploadBankReceipt": "Télécharger le reçu bancaire", "contactOurSalesStaff": "Pour toute question, veuil<PERSON>z contacter notre personnel commercial", "NotesForPublicTransfers": "Remarques pour les virements publics :", "submittedForFinancialReview": "Les paiements hors ligne peuvent prendre du temps et nécessitent la soumission d'un reçu bancaire pour vérification financière", "TransfersFromPersonalAccounts": "Les virements depuis des comptes personnels vers notre compte public ne peuvent émettre que des factures personnelles ordinaires.", "IfTheCompanyAccountIsTransferred": "Les virements depuis des comptes d'entreprise vers notre compte public peuvent émettre des factures TVA ordinaires ou des factures TVA spéciales.", "uploadTip": "Formats JPG ou PNG acceptés, taille maximale de 5 Mo", "BankCardNumber": "Numéro de carte bancaire", "bankCardNumberWhenTransferring": "Veuillez saisir le numéro de carte bancaire utilisé pour le virement", "transactionHour": "Heure de la transaction", "CancellationLineItemCannotBeEmpty": "L'élément d'annulation de la commande ne peut pas être vide", "WaitingForSellerToConfirm": "En attente de confirmation du vendeur", "PurchaseStatus": "Statut de l'achat", "bankReceipt": "Reçu bancaire", "Serve": "Service", "Optional": "Optionnel", "deadline": "Date limite :", "orderUpdateTips": "Seul le reçu ou le numéro de carte bancaire de la commande peut être modifié, pour toute autre question, veuillez contacter notre personnel", "PleaseUploadBankReceipt": "Veuillez télécharger le reçu bancaire", "PleaseEnterBankCardNumber": "Veuillez saisir le numéro de carte bancaire", "NoRelatedOrders": "Aucune commande associée", "ErrorUploadingBankReceipt": "Erreur lors du téléchargement du reçu bancaire", "changeVipStateTip": "Si vous modifiez le statut VIP de l'utilisateur actuel en Super VIP, tous les utilisateurs sous l'ID de l'entreprise deviendront Super VIP"}, "statistic": {"enableHumanStatistic": "Activer les statistiques de flux de passagers", "queryHumanStatisticToday": "Consulter les statistiques de flux de passagers d'aujourd'hui", "enter": "Nombre d'entrées", "exited": "Nombre de sorties", "countingMonitoring": "Surveillance des statistiques de flux de passagers", "viewChart": "Voir le graphique", "currentNumber": "Nombre actuel", "lineChart": "Graphique en ligne du nombre de personnes dans la zone", "areaPeopleNum": "Nombre de personnes dans la zone", "selectHistoricalData": "Consulter les données historiques", "sendData": "Envoyer les données", "keyTip": "Après l'activation de l'affichage des données, la clé sera générée automatiquement", "isShowHumanNumber": "L'affichage des données de flux de passagers sur le terminal est-il activé ?", "dataKey": "Clé", "noDataKey": "Aucune clé pour cet appareil", "clickCopy": "Cliquer pour copier", "copySuccess": "<PERSON><PERSON> r<PERSON>"}, "alarm": {"alarmNum": "Numéro d'alarme", "alarmPeople": "Personne d'alarme", "call": "<PERSON><PERSON><PERSON>", "receive": "Rece<PERSON>ur", "callTime": "<PERSON><PERSON><PERSON> de l'appel", "channel": "Canal", "dstChannel": "Canal de destination", "alarmDeviceInfo": "Informations sur l'appareil d'alarme", "setCallingVolume": "Définir le volume d'appel", "callingVolume": "Volume d'appel", "notEnable": "Non activé", "setAlarmInfo": "Définir les informations de l'appareil d'alarme", "sipAccount": "Compte SIP", "sipServiceAddress": "<PERSON><PERSON><PERSON> du serveur SIP", "sipServicePort": "Port du serveur SIP", "accountState": "État du compte", "alarmDeviceNetwork": "Informations réseau de l'appareil d'alarme", "setNetwork": "Définir l'état du réseau", "dynamic": "Dynamique", "static": "Statique", "gateway": "<PERSON><PERSON><PERSON>", "subnetMask": "Masque de sous-rés<PERSON>", "alarmAccount": "<PERSON>mpte d'alarme", "accountType": "Type de compte", "registerAlarmAccount": "Enregistrer le compte de l'appareil d'alarme", "registerPhoneNumber": "Enregistrer le numéro de téléphone", "accountRule": "Le compte ne peut pas être vide et doit contenir 11 chiffres ou lettres", "account": "<PERSON><PERSON><PERSON>", "batchSettings": "Paramètres par lot", "alarmNetworkTip": "Maintenez l'appareil d'alarme enfoncé jusqu'à ce qu'un signal sonore se produise, appuyez une fois pour annoncer l'IP, appuyez trois fois pour basculer entre IP dynamique et statique", "alarmAddressTip": "Avant de définir l'adresse d'alarme, ve<PERSON><PERSON><PERSON> configurer le compte de l'appareil d'alarme dans les informations de l'appareil d'alarme", "alarmAddressSetTip": "<PERSON><PERSON> d<PERSON><PERSON><PERSON>, la plateforme définira l'adresse d'alarme sur l'adresse de la plateforme", "defaultState": "<PERSON><PERSON> <PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "myPhoneNumber": "Mon numéro", "calledPhoneNumber": "Numéro appelé", "alarmInfoTip": "Veuillez enregistrer un compte avant de configurer les informations de l'appareil d'alarme", "backupCalledPhoneNumber": "Numéro de secours"}, "traffic": {"enableTraffic": "<PERSON><PERSON> le trafic intelligent", "eventName": "Nom de l'événement", "plateNumber": "Numéro <PERSON>", "plateType": "Type de plaque", "plateColor": "<PERSON><PERSON><PERSON> de la plaque", "vehicleColor": "Couleur du véhicule", "vehicleType": "Type de véhicule", "vehicleSize": "Taille du véhicule", "illegalPlace": "Lieu de l'infraction", "eventTime": "Heure de l'événement", "downloadEventPic": "Télécharger la photo de l'infraction", "illegalPic": "Photo de l'infraction"}, "radar": {"radarSpeed": "Vitesse radar", "radarSetting": "Paramètres radar", "fastestCar": "Voiture la plus rapide", "closestCar": "Voiture la plus proche", "setResponseTime": "Définir le temps de réponse", "setOutputTarget": "Définir la cible de sortie", "setMinSpeed": "Définir la vitesse minimale", "setMaxSpeed": "Définir la vitesse maximale", "setSensitivity": "Définir la sensibilité", "isConnect": "Le radar est-il connecté", "speed": "Vitesse", "parameter": "Paramètre", "speedLimitRange": "Plage de limitation de vitesse", "addSpeedLimitRange": "Veuillez ajouter une plage de limitation de vitesse", "minSpeed": "Vitesse minimale", "maxSpeed": "Vitesse maximale", "radarSpeedLimit": "Limitation de vitesse radar"}, "ac": {"apOnlineRate": "Taux de connexion AP", "apOnlineNumber": "Nombre d'AP en ligne", "apSum": "Nombre total d'AP", "onlineTerminal": "Nombre de terminaux en ligne", "flowStatistics": "Statistiques de flux", "flowStatisticsToday": "Statistiques de flux d'aujourd'hui", "name": "Nom AC", "macAddress": "Adresse MAC", "belongArea": "Zone d'appartenance", "belongOrganization": "Organisation d'appartenance", "belongProject": "Projet d'appartenance", "userOnlineCount": "Nombre total de connexions utilisateur", "acOnlineUserCount": "Nombre d'utilisateurs en ligne AC", "upstreamTraffic": "Flux montant", "downlinkTraffic": "Flux descendant", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "userAuth": {"pass": "Vérification réussie", "fail": "Échec de la vérification", "company": "Certification d'entreprise", "personal": "Certification personnelle", "unverified": "Non certifié", "certificationAudit": "Audit de certification", "authMode": "Mode d'authentification", "authInfo": "Informations d'authentification", "enterpriseLicense": "Licence d'entreprise", "OfficialSeal": "Certificat de sceau d'entreprise", "FrontOfIdentityCard": "Recto de la carte d'identité", "ReverseOfIDCard": "Verso de la carte d'identité", "reason": "<PERSON>son", "authentication": "Authentification", "pleaseUploadEnterpriseLicenseOfficialSeal": "Veuillez télécharger le certificat de sceau d'entreprise et la licence d'entreprise", "updateAuth": "Mettre à jour l'authentification", "uploadAuth": "Télécharger l'authentification"}, "cat1": {"temp": "Température du modèle", "power": "Puissance active", "electricEnergy": "Énergie active", "roadOne": "Voie 1", "roadTwo": "Voie 2", "addDevice": "Ajouter un appareil", "channel": "Canal", "colorTemp": "Température de couleur", "scheduleTip": "Ce minutage commence à l'heure RealTime définie. Par exemple, si l'heure définie est 12:30, cela commence à 12:30 et se termine au prochain point de temps, l'état de la lumière unique est la luminosité et la température de couleur définies. Pour éteindre la lumière unique, vous pouvez définir la luminosité sur 0."}, "notify": {"NotificationStrategy": "Stratégie de notification", "NotifyTip": "(Une fois activée, l'utilisateur sera notifié par e-mail à l'heure spécifiée)", "OfflineNotify": "Notification hors ligne", "CardNotWorkingNotification": "Notification de carte non fonctionnelle", "tactics": "Stratégie (heures)", "selectAll": "<PERSON><PERSON>", "PleaseSelectNotification": "Veuillez sélectionner une stratégie de notification", "SendingMailbox": "Boîte d'envoi", "SendingTime": "Heure d'envoi", "Username/EmailAddress": "Nom d'utilisateur/Boîte d'envoi", "OffLineTime": "<PERSON><PERSON> hors ligne", "AbnormalTime": "Heure anormale", "NumberOfCardsRLastTime": "Nombre de cartes reçues la dernière fois", "NumberOfCardsReceivedExceptionOccurs": "Nombre de cartes reçues lors de l'exception"}, "thirdPartyAd": {"enabledThirdPartyAd": "Intégration de publicité tierce", "thirdPartyUrl": "Adresse du service tiers", "loopTip": "Jouer en boucle avec les publicités existantes dans l'appareil, plusieurs publicités seront réparties uniformément dans la liste des publicités existantes de l'appareil", "onceTip": "Inséré une fois dans les publicités existantes de l'appareil, plusieurs publicités seront réparties uniformément dans la liste des publicités existantes de l'appareil", "mutexTip": "Lecture en boucle exclusive", "adInterval": "Période de publicité", "queryThirdPartyAd": "Rechercher une publicité tierce", "downloadUrl": "URL de téléchargement", "impression": "Informations de lecture", "playHour": "<PERSON><PERSON><PERSON><PERSON>", "playCount": "Nombre de lectures"}, "crossArrow": {"addToCross": "Ajouter à la flèche croisée", "arrowProgram": "Programme de flèche", "greenArrow": "Flèche verte", "redArrow": "Croix rouge", "closeScreen": "<PERSON><PERSON><PERSON> l'écran", "curProgram": "Programme actuel", "remove": "<PERSON><PERSON><PERSON><PERSON>", "batchRemove": "Suppression par lot"}, "manual": {"operationDocument": "Document d'opération", "videoTutorial": "<PERSON><PERSON><PERSON> v<PERSON>", "resourceManagement": "Gestion des ressources", "manualAndTutorial": "Manuel d'utilisation et tutoriel vidéo", "universal": "Universel", "fileTips": "Un seul type de fichier peut être téléchargé à la fois"}, "myEditor": {"bottomCenter": "Aligner en bas", "topCenter": "Aligner en haut", "verticalCenter": "Aligner au centre"}, "employee": {"today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "last7Days": "7 derniers jours", "last30Days": "30 derniers jours", "lineChart": "Graphique en ligne", "barChart": "Graphique en barres", "unitPerson": "Unité : <PERSON><PERSON>", "man": "<PERSON><PERSON>", "woman": "<PERSON>mme", "tips": "Après activation, les statistiques de flux de passagers seront automatiquement enregistrées, vous pouvez les consulter dans Statistiques de flux de passagers -> Statistiques de flux de passagers V1 après l'activation réussie"}}