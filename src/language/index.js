import Vue from 'vue'
import ViewUI from 'view-design'
import VueI18n from 'vue-i18n'
import us from 'view-design/dist/locale/en-US'
import cn from 'view-design/dist/locale/zh-CN'
import tw from 'view-design/dist/locale/zh-TW'
import ja from 'view-design/dist/locale/ja-JP'
import pt from 'view-design/dist/locale/pt-PT'
import ar from 'view-design/dist/locale/ar-SA'
import fr from 'view-design/dist/locale/fr-FR'
import es from 'view-design/dist/locale/es-ES'
import ru from 'view-design/dist/locale/ru-RU'
import US from './en-US'
import CN from './zh-CN'
import TW from './zh-TW'
import JA from './ja-JP'
import PT from './pt-PT'
import AR from './ar-SA'
import FR from './fr-FR'
import ES from './es-ES'
import RU from './ru-RU'

Vue.use(VueI18n)
Vue.locale = () => {}

const messages = {
  us: Object.assign(US, us),// 英语
  cn: Object.assign(CN, cn),// 简体
  tw: Object.assign(TW, tw),// 繁体
  ja: Object.assign(JA, ja),// 日语
  pt: Object.assign(PT, pt),// 葡萄牙语
  ar: Object.assign(AR, ar),// 阿拉伯语
  fr: Object.assign(FR, fr),// 法语
  es: Object.assign(ES, es),// 西班牙语
  ru: Object.assign(RU, ru)// 俄语
}

// Create VueI18n instance with options
const i18n = new VueI18n({
  locale: localStorage.getItem('locale') === null ? 'cn' : localStorage.getItem('locale'), // set locale
  messages, // set locale messages
  silentTranslationWarn: true
})

Vue.use(ViewUI, {
  i18n: (key, value) => i18n.t(key, value)
})

export default i18n
