{"common": {"PleaseInput": "Por favor, ingrese", "PleaseSelect": "Por favor, seleccione", "Loading": "Cargando", "personalSettings": "Configuración personal", "logOut": "<PERSON><PERSON><PERSON>", "query": "Consultar", "newlyBuild": "Nuevo", "add": "Agregar", "delete": "Eliminar", "batchDel": "Eliminar en lote", "update": "Modificar", "state": "Estado", "operation": "Operación", "tips": "Sugerencia", "info": "Detalles", "deploy": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "areYouSureExit": "¿Está seguro de que desea salir?", "setSuccess": "<PERSON><PERSON><PERSON><PERSON>", "notSet": "No configurado", "set": "Configurar", "bindingSuccess": "Vinculado", "unbound": "No vinculado", "binding": "Vincular", "operationSuccessful": "Operación exitosa", "createTime": "Tiempo de creación", "normal": "Normal", "disable": "Deshabilitar", "delete_current_option": "Confirmar la eliminación de la opción actual", "original": "Original", "use_iconfont": "Utilice iconos vectoriales SVG Sprite de forma uniforme", "total": "Total", "supportedTip": "No compatible temporalmente", "selectDevice": "Por favor, seleccione un dispositivo", "clickToEnlarge": "Haga clic para ver una imagen en grande", "pictureLoadingFailed": "Error al cargar la imagen", "passwordError": "Contrase<PERSON>", "OKToRestart": "Confirmar el reinicio", "WaitingForRestart": "<PERSON><PERSON><PERSON><PERSON> a reiniciar...", "RestartTimeout": "Tiempo de espera de reinicio agotado", "modifiedTime": "Tiempo de modificación", "exportData": "Exportar datos", "submit": "Enviar", "modifyTheSuccess": "Modificación exitosa", "configuration": "Configuración", "failure": "Error", "release": "Publicar", "nextStep": "Siguiente paso", "selectMaterial": "Seleccionar material", "notSelectedectMaterial": "Nota: Si no se selecciona ningún grupo, se exportarán automáticamente la información SIM de todas las tarjetas", "exportingSIM": "Exportando información SIM", "enterCardPackage": "Por favor, ingrese el nombre de la tarjeta/paquete...", "note": "<PERSON>a", "enterPackage": "Por favor, ingrese el nombre del paquete...", "noDetails": "<PERSON> de<PERSON>les", "versionQuery": "Consulta de versión", "CardSysterm5263": "La consulta de versión requiere la versión CardSysterm5.2.6.3 o superior", "uninstallCard": "No se ha seleccionado ninguna tarjeta, no se puede utilizar la función de desinstalación en línea", "packageName": "Nombre del paquete", "versionNumber": "Número de versión", "versionIdentifiers": "Identificador de versión", "wrong": "El estado de conexión de esta tarjeta es incorrecto, la actualización se ha cancelado!", "upgradeWrong": "El estado de conexión de todas las tarjetas seleccionadas para la actualización es incorrecto, por favor, verifique e inténtelo de nuevo!", "cardsNotReturn": "Algunas tarjetas no admiten la devolución de la barra de progreso, el tiempo de espera de la solicitud ha expirado, la actualización ha finalizado!", "updateComplete": "Actualización completada!", "restartCards": "¿Está seguro de que desea reiniciar estas tarjetas?", "addTiming": "Por favor, agregue una programación", "Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "Viernes", "Saturday": "Sábado", "Sunday": "Domingo", "brightnessClears": "La configuración manual del brillo eliminará toda la programación de brillo y sensibilidad", "fullBrightness": "<PERSON><PERSON><PERSON>", "screenDuring": "Nota: La pantalla estará encendida durante el tiempo configurado", "removeTiming": "Eliminar programación", "clearSuccess": "Eliminación exitosa", "notEnteredNotModified": "Los campos de entrada no rellenados no se modifican. Con la versión Conn_v11.1.1.1[261]-AIPS-release-2&4 o superior, dejar un campo vacío significa establecerlo como vacío", "Changing": "Modificar el ID de la empresa o la dirección realTime hará que la tarjeta se desconecte. ¿Está seguro de que desea modificar?", "networkNTP": "La calibración automática de la hora requiere una red y una dirección NTP", "Settings": "Al seleccionar un modo de configuración de sincronización, los campos no rellenados se consideran configurados como vacíos", "SynchronousMode": "Modo de sincronización", "zoneEmpty": "Debe seleccionar una zona horaria!", "synchronousState": "El estado de sincronización se establece durante el intervalo de tiempo configurado para la sincronización programada!", "beEmpty": "El ancho y la altura no pueden ser menores o iguales a 0 y no pueden estar vacíos!", "ModifyFailure": "Modificación fallida", "programmeDetails": "Detalles de la tarea del programa", "showWidth": "<PERSON><PERSON> del programa", "showHigh": "Altura del programa", "notOnPlatform": "Consulta fallida, el programa actual no se publicó en esta plataforma", "allIntervals": "Si no se selecciona ningún intervalo de tiempo después de habilitar la programación, se consideran todos los intervalos seleccionados", "notSelected": "No seleccionado", "true": "Sí", "false": "No", "name": "Nombre", "custom": "Personalizado", "MaximumNumberOfWords200": "Límite de 200 caracteres", "exportingSIMTips": "El grupo actual no tiene terminales, se recomienda seleccionar otro grupo", "language": "Idioma", "copy": "Copiar", "save": "Guardar", "saveAndExit": "Guardar y salir", "noMoreData": "No hay más datos", "enable": "Habilitar", "NotEnabled": "No habilitado", "AssignUsers": "<PERSON><PERSON><PERSON> usuarios", "manual": "Manual de uso", "addMedia": "+ Agregar medios", "reviewCurrentUsers": "Por favor, contacte a nuestro personal para revisar el usuario actual", "selectGroup": "Seleccionar grupo", "selectingGroup": "Grupo actual", "unclassified": "No clasificado", "frontStep": "Paso anterior", "search": "Buscar", "CAT1": "CAT1", "DaHuaCamera": "<PERSON><PERSON><PERSON>", "GBCamera": "Cámara estándar nacional"}, "screen": {"simInformation": "Información de la tarjeta SIM", "networkState": "<PERSON>ís de <PERSON>", "series": "Número de serie", "countries": "Países", "operationName": "Nombre de la operación", "unknownState": "Estado desconocido", "noCard": "No hay tarjeta insertada", "PIN": "Estado de bloqueo, se requiere el código PIN del usuario para desbloquear", "PUK": "Estado de bloqueo, se requiere el código PUK del usuario para desbloquear", "PIN2": "Estado de bloqueo, se requiere el código PIN de la red para desbloquear", "readyState": "Estado listo", "InvalidState": "Estado inválido", "subscribe": "Suscribirse", "choose": "Elegir", "supportZip": "¡Los archivos de versión solo admiten el formato Zip!", "selectFile": "Seleccionar archivo", "releaseTime": "Tiempo de lanzamiento", "versionInformation": "Información de la versión", "testVersion": "¡La versión de prueba no tiene detalles de la versión!", "hardwareVersion": "¡Los parámetros de hardware no tienen detalles de la versión!", "officialRelease": "Lanzamiento de la versión oficial", "testRelease": "Lanzamiento de la versión de prueba", "hardwareRelease": "Lanzamiento de los parámetros de hardware", "sizeMore": "El tamaño excede", "OnlyForZIPType!": "¡Solo puede ser del tipo ZIP!", "uploadEmpty": "¡El archivo de carga no puede estar vacío!", "uploadFile": "Cargar archivo", "fileTips": "Solo se pueden cargar archivos en formato MP3, MP4, gif, png, jpg. Los archivos de tipo MP3 y las imágenes no pueden exceder los 100 MB, y los archivos de tipo MP4 no pueden exceder los 150 MB", "fileTips1": "Solo se pueden cargar archivos en formato MP3, y el tamaño no puede exceder los 20 MB", "picture": "Imagen", "video": "Video", "picturesOrVideos": "¡El tipo de archivo de medios cargado solo puede ser imagen o video!", "my": "<PERSON>", "pending": "Pendiente de aprobación", "pageImprovement": "La página está en proceso de mejora", "errorDetails": "Detalles del error", "null": "<PERSON><PERSON><PERSON>", "showProgress": "Progreso del programa", "sendSuccess": "<PERSON><PERSON><PERSON>", "sendSuccessOffline": "Se ha enviado el programa sin conexión. El controlador enviará automáticamente el programa cuando se conecte en línea en las próximas 72 horas", "failedProgress": "Error al obtener el progreso", "timesOut": "Tiempo de espera agotado al obtener el progreso", "selectCard": "Por favor, seleccione la tarjeta de control", "reviewDetails": "Detalles de la revisión", "satelliteNumber": "Número de satélites", "autoBrightnessTable": "Tabla de brillo automático", "senorType": "Tipo de sensor", "setAutoBrightnessTable": "Configurar la tabla de brillo automático", "getAutoBrightnessTable": "Consultar la tabla de brillo automático", "default255BrightnessTable": "Tabla de brillo predeterminada de 255", "customizeBrightnessTable": "Tabla de brillo personalizada", "otherSwitch": "<PERSON><PERSON>s interruptores", "customSwitchFunction": "Función de interruptor personalizado", "programPlayDetail": "Detalles de reproducción del programa", "programPlayStatistic": "Estadísticas de reproducción del programa", "programPlaySum": "Número total de reproducciones del programa", "exportPlayLog": "Exportar registro de reproducción", "log": "Registro", "networkPort": "Puerto de red", "clickToCheck": "Haga clic para detectar", "getPointCheckInfo": "Obtener información de inspección", "imageSize": "Tam<PERSON><PERSON> de la imagen", "badPointNum": "Número de puntos defectuosos", "queryPlayerState": "Consultar el estado del reproductor", "playerStateRep": {"1": "Inicialización", "2": "Fin del programa programado", "3": "No hay programas pendientes de reproducción", "4": "Eliminar programa", "5": "Procesando programa", "6": "Sin información por el momento", "7": "El programa puede tener errores", "8": "Apagar pantalla", "9": "El programa fijo no está dentro del rango"}, "fullColor": "Todo color", "monochrome": "Monocromo", "redBeadNumberPoints": "Número de puntos defectuosos de los perlas rojas", "greenBeadNumberPoints": "Número de puntos defectuosos de los perlas verdes", "blueBeadNumberPoints": "Número de puntos defectuosos de los perlas azules", "totalNumberOfBadPoints": "Número total de puntos defectuosos", "redBadPointPosition": "Coordenadas de los puntos defectuosos de los perlas rojas", "greenBadPointPosition": "Coordenadas de los puntos defectuosos de los perlas verdes", "blueBadPointPosition": "Coordenadas de los puntos defectuosos de los perlas azules", "badPointPosition": "Coordenadas de los puntos defectuosos", "abscissa": "Abscisa", "ordinate": "Ordenada", "noBadPoint": "Sin puntos defectuosos", "pointCheckTips": "La detección de puntos defectuosos solo admite una tarjeta. Por favor, seleccione de nuevo", "receivingCard": "Tarjeta receptora", "setRecCardRelaySwitch": "Configurar el interruptor del relé de la tarjeta receptora", "getRecCardSensorData": "Obtener los datos del sensor de la tarjeta receptora", "smoke": "<PERSON><PERSON>", "smokeless": "Sin humo", "openCloseDoor": "Abrir/cerrar puerta", "openDoor": "<PERSON><PERSON><PERSON> puerta", "closeDoor": "<PERSON><PERSON>r puerta", "relaySwitch": "Interruptor del relé", "levelDetection": "Detección de nivel", "accessHighLevel": "Indica la conexión a un nivel alto", "noDeviceConnected": "Indica que no hay dispositivos conectados", "firstRoad": "Primer camino", "secondWay": "<PERSON><PERSON><PERSON> camino", "thirdWay": "Tercer camino", "fourthWay": "Cuarto camino", "theFifthRoad": "<PERSON><PERSON><PERSON> camino", "sensorDataShareTips": "La compartición de datos del sensor solo admite una tarjeta. Por favor, seleccione de nuevo", "hour": "<PERSON><PERSON>", "pointTable": "Tabla de puntos", "pointTableTips": "Por favor, realice la operación bajo la guía de un profesional", "pointTableTemplate": "Plantilla de la tabla de puntos", "pleaseUploadPointTable": "Por favor, configure primero la tabla de puntos", "networkConfig": "Configuración de red", "hotspot": "Punto de acceso", "wifiTip": "Configurar la habilitación de Wi-Fi puede cambiar el estado de la red y causar la desconexión. Por favor, maneje con precaución", "apTip": "Nota: utilice una contraseña combinada de letras mayús<PERSON>s, minúsculas y números, y la longitud de la contraseña debe estar entre 8 y 20 caracteres", "wifiList": "Consultar la lista de Wi-Fi", "selectWifi": "Por favor, seleccione un Wi-Fi", "singleCardOperation": "Esta función solo admite la operación de una tarjeta"}, "sys": {"enable": "Habilitar", "remarks": "Por favor, ingrese información de notas", "authenticationMode": "Por favor, seleccione el modo de verificación", "emailVerification": "Verificación por correo electrónico", "mobileVerification": "Verificación por número de teléfono móvil", "verify": "Modo de verificación", "notOpen": "No habilitado", "email": "Correo electrónico", "mobile": "Teléfono móvil", "whetherAudit": "¿Se audita?", "open": "Abrir", "close": "<PERSON><PERSON><PERSON>", "companyId": "ID de la empresa", "same": "El ID de la empresa es el mismo que el de la empresa del creador", "roleEmpty": "El rol de usuario no puede estar vacío", "alarmType": "Tipo de alarma", "alarmTime": "Hora de la alarma", "Iknown": "Lo s<PERSON>", "currentBrowser": "El navegador actual no admite eventos enviados por el servidor", "notReminded": "No se recordará después de confirmar", "visited": "La página que ha visitado", "find": "No existe", "url": "Por favor, verifique la URL", "previousPage": "Volver a la página anterior", "enterHome": "Ingresar a la página de inicio", "permissionOfTerminalGroups": "Grupos de terminales controlables", "TheSuperAdminDoesNotRestrictUserGroups": "El superadministrador no restringe los grupos de usuarios", "addOrUpdateAuth": "Agregar/modificar autenticación", "isDefaultPassword": "¿Se establece la contraseña predeterminada?"}, "login": {"dynamicCodeEntryTips": "El usuario actual no ha activado el método de verificación de código dinámico. Por favor, active el método de verificación primero.", "login": "In<PERSON><PERSON>", "passwordLogin": "Iniciar sesión con contraseña", "username": "Nombre de usuario", "password": "Contraseña", "ForgetThePassword": "¿Olvidó su contraseña?", "newPassword": "Nueva contraseña", "confirmPassword": "Confirmar con<PERSON>", "dynamicCodeEntry": "Iniciar sesión con código diná<PERSON>", "pwdNotes": "Se recomienda utilizar una contraseña de al menos ocho dígitos.", "origin_pwd_incorrect": "La contraseña original es incorrecta.", "wrong_account_or_password": "Nombre de usuario o contraseña incorrectos.", "account_has_been_locked": "La cuenta ha sido bloqueada. Por favor, contacte al administrador.", "updateEmail": "Modificar correo electrónico", "bindEmail": "Vincular correo electrónico", "updateMobile": "Modificar número de teléfono móvil", "bindMobile": "Vincular número de teléfono móvil", "sliderRight": "Por favor, haga clic en el botón para verificar.", "loading": "Cargando", "passwordMore8": "La longitud de la contraseña debe ser mayor que 8 y debe estar formada por letras mayúsculas, letras minúsculas y números.", "user4To17": "La longitud del nombre de usuario debe ser mayor que 4 y menor que 17, y debe estar formada por números o letras.", "clickCodeMailbox": "Haga clic para obtener el código de verificación. La información se enviará a su correo electrónico de seguridad.", "clickCodePhone": "Haga clic para obtener el código de verificación. El mensaje de texto se enviará a su teléfono móvil de seguridad.", "authenticationSuccessful": "Verificación exitosa", "securityVerification": "Por favor, realice la verificación de seguridad primero.", "enterVerificationCode": "Por favor, ingrese el código de verificación.", "UserNameEmailMobile": "Nombre de usuario/Correo electrónico/Número de teléfono móvil", "ScopeOfAuthority": "Ámbito de autoridad", "superAdministrator": "Super administrador", "PermissionDetails": "Detalles de permisos", "pleaseContactTheAdministrator": "Por favor, contacte al administrador."}, "register": {"register": "Registro", "mobile": "Número de teléfono móvil", "mailbox": "Dirección de correo electrónico", "code": "Código de verificación", "getCode": "Obtener código de verificación", "remind": "Se recomienda registrarse con un solo clic escanneando el código QR de WeChat", "back": "Volver al inicio de sesión", "prependResend": "En ", "appendResend": " s, puede volver a enviar", "personalInformation": "Información personal", "complete": "Completar", "recommendedRegister": "Se recomienda registrarse e iniciar sesión con un solo clic escanneando el código QR de WeChat", "enterPassword": "Por favor, ingrese la contraseña nuevamente", "companyId": "ID de la empresa. La tarjeta de control se vincula al ID de la empresa al conectarse en línea y no se puede cambiar una vez determinado.", "companyName": "Nombre de la empresa", "companyAddress": "Dirección de la empresa", "companyPhone": "Teléfono de la empresa", "readAIPSAgreement": "Por favor, lea primero el Acuerdo de Usuario de AIPS", "readAccepted": "<PERSON> leído y aceptado", "AIPSAgreement": "Acuerdo de Usuario de AIPS", "registeredSuccessfully": "<PERSON><PERSON>oso", "clickJumpOr": "Haga clic para saltar o", "loginDisplayed": " segundos después, se redirigirá automáticamente a la página de inicio de sesión", "readAcceptAIPSAgreement": "Por favor, lea y acepte el Acuerdo de Usuario de AIPS", "youAreRight": "Está realizando la operación de restablecimiento de contraseña para", "resettingVerification": ", por favor, realice la verificación de seguridad primero", "switchingAuthentication": "Cambiar el método de verificación", "pleaseSet": "Por favor, configure", "passwordSecurity": " la nueva contraseña. Se recomienda usar una contraseña combinada de números, letras y caracteres para mejorar el nivel de seguridad de la contraseña.", "passwordChangedSuccessfully": "Contraseña modificada con éxito", "verified": "Verificación de identidad", "idCardNumber": "Número de identificación", "companyLicense": "Cargue una imagen del 'Licencia de la Empresa Legal' con período de revisión anual válido", "cachet": "Cargue una imagen del 'Certificado de Autorización' sellado con el sello de la empresa", "idCardFront": "Cargue una imagen delantera de la identificación", "idCardReverse": "Cargue una imagen trasera de la identificación", "pleaseReadAndCheckAIPS": "Por favor, lea y marque el 'Acuerdo de Usuario de AIPS'", "personal": "Personal", "company": "Empresa", "CertifiProgress": "Progreso de la verificación", "waitForAdminAuth": "Esperando la verificación del administrador", "DownloadLicenseCertificate": "Descargar el 'Certificado de Autorización'"}, "ChangePWD": {"ChangePassword": "Cambiar contraseña", "remind": "Solo los usuarios que hayan vinculado un número de teléfono móvil o una dirección de correo electrónico pueden recuperar su contraseña."}, "nav": {"首页": "<PERSON><PERSON><PERSON><PERSON> principal", "智慧屏幕": "Pantalla inteligente", "智慧广播": "Radio inteligente", "气象环境": "Entorno meteorológico", "智慧监控": "Monitoreo inteligente", "客流统计": "Estadísticas de flujo de personas", "客流统计V1": "Estadísticas de flujo de personas V1", "系统管理": "Administración del sistema", "设备管理": "Administración de dispositivos", "节目管理": "Administración de programas", "媒体库": "Biblioteca de medios", "日志管理": "Administración de registros", "远程控制日志": "Registro de control remoto", "播放日志": "Registro de reproducción", "用户管理": "Administración de usuarios", "菜单管理": "Administración de menús", "角色管理": "Administración de roles", "用户日志": "Registro de usuarios", "登录日志": "Registro de inicio de sesión", "系统日志": "Registro del sistema", "设备状态": "Estado de los dispositivos", "广播任务": "Tareas de radio", "分组管理": "Administración de grupos", "审批管理": "Administración de aprobaciones", "公告管理": "Administración de anuncios", "终端列表": "Lista de terminales", "智慧物联": "Internet de las cosas inteligente", "智慧照明": "Iluminación inteligente", "CAT1照明": "Iluminación CAT1", "控制卡照明": "Iluminación de tarjetas de control", "电能管理": "Administración de energía eléctrica", "视频监控": "Monitoreo de video", "付费服务": "Servicios de <PERSON>ago", "订购服务": "Servicios de suscripción", "订单": "Pedido", "SIP账号管理": "Administración de cuentas SIP", "监控回放": "Reproducción de monitoreo", "人群聚集": "Acumulación de personas", "订单管理": "Administración de pedidos", "报警管理": "Administración de alarmas", "报警记录": "Registro de alarmas", "通话记录": "Registro de llamadas", "智慧交通": "Tráfico inteligente", "交通信息": "Información de tráfico", "雷达测速": "Medición de velocidad por radar", "WIFI AC": "WIFI AC", "概览": "Resumen", "AC管理": "Administración de AC", "密码管理": "Administración de contraseñas", "认证审核": "Revisión de certificación", "通知策略": "Estrategia de notificación", "通知日志": "Registro de notificaciones", "十字箭头": "<PERSON><PERSON><PERSON> c<PERSON>a", "设备白名单": "Lista blanca de dispositivos", "摄像头监控": "Vigilancia por c<PERSON><PERSON>s"}, "validate": {"account_cannot_empty": "La cuenta no puede estar vacía", "password_cannot_empty": "La contraseña no puede estar vacía", "confirm_password_cannot_empty": "La confirmación de la contraseña no puede estar vacía", "new_pwd_cannot_empty": "La nueva contraseña no puede estar vacía", "email_cannot_empty": "El correo electrónico no puede estar vacío", "mobile_cannot_empty": "El número de teléfono móvil no puede estar vacío", "code_cannot_empty": "El código de verificación no puede estar vacío", "roleName_cannot_empty": "El nombre del rol no puede estar vacío", "menuURL_cannot_empty": "La URL del menú no puede estar vacía", "superior_menu_cannot_empty": "El menú superior no puede estar vacío", "menu_name_cannot_empty": "El nombre del menú no puede estar vacío", "group_name_cannot_empty": "El nombre del grupo no puede estar vacío", "group_type_cannot_empty": "El tipo de grupo no puede estar vacío", "audit_name_cannot_empty": "El nombre de la auditoría no puede estar vacío", "resource_type_cannot_empty": "El tipo de recurso no puede estar vacío", "status_cannot_be_empty": "El estado no puede estar vacío", "approval_comments_cannot_blank": "Los comentarios de aprobación no pueden estar en blanco", "program_name_cannot_empty": "El nombre del programa no puede estar vacío", "the_new_password_is_inconsistent": "La confirmación de la nueva contraseña no coincide con la nueva contraseña", "the_password_is_inconsistent": "La confirmación de la contraseña no coincide con la contraseña", "incorrect_email_format": "El formato del correo electrónico es incorrecto", "code_format": "Por favor, ingrese seis caracteres", "mobile_format": "El formato del número de teléfono móvil es incorrecto", "mobile_code_empty": "El código de verificación del teléfono móvil no puede estar vacío", "email_code_empty": "El código de verificación del correo electrónico no puede estar vacío", "company_id_empty": "El ID de la empresa no puede estar vacío", "not_empty": "No puede estar vacío", "alarmAddress_not_empty": "La dirección de alarma o la cuenta de llamada no puede estar vacía", "alias_cannot_empty": "El alias no puede estar vacío", "company_name_cannot_empty": "El nombre de la empresa no puede estar vacío", "company_address_cannot_empty": "La dirección de la empresa no puede estar vacía", "company_phone_number_cannot_empty": "El número de teléfono de la empresa no puede estar vacío", "id_card_number_cannot_empty": "El número de identificación no puede estar vacío", "id_card_number_format_wrong": "El formato del número de identificación es incorrecto", "validateTip": "Por seguridad de su cuenta, ahora es necesario que vincule un método de verificación."}, "role": {"role": "Rol", "roleName": "Nombre del rol", "remark": "Notas", "authorization": "Autorización", "subAdmin": "Subadministrador", "normalUser": "Usuario normal"}, "menu": {"name": "Nombre", "parentName": "Menú superior", "icon": "Icono", "type": "Tipo", "orderNum": "<PERSON>ú<PERSON><PERSON> de orden", "url": "URL del menú", "perms": "Identificador de autorización", "mainMenu": "<PERSON><PERSON> principal", "parentMenuName": "<PERSON><PERSON> padre", "permsTips": "Separe múltiples con comas, por ejemplo: user:list,user:create", "menu": "Menú", "DirectoryMenu": "Menú de directorio", "button": "Botón", "HomeDirectoryMenu": "Menú de directorio principal"}, "log": {"user_name_user_action": "Nombre de usuario / Acción del usuario", "user_action": "Acción del usuario", "request_method": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>", "request_parameters": "Parámetros de solicitud", "execution_time": "Tiempo de ejecución (milisegundos)", "ip_address": "Dirección IP", "commandId": "ID de comando", "response_result": "Resultado de respuesta", "schedule": "Progreso", "ReasonForFailure": "Razón del fracaso", "RequestTimedOut": "Tiempo de solicitud agotado", "requestSucceeded": "Solicitud exitosa", "connectionDoesNotExist": "La conexión no existe", "Disconnect": "Conexión desconectada", "connectionClosed": "Conexión cerrada", "requestException": "Excepción de solicitud"}, "group": {"name": "Nombre del grupo", "type": "Tipo de grupo", "addingAGroup": "Agregar grupo", "pleaseEnterAGroupName": "Por favor, ingrese el nombre del grupo", "addingSubgroup": "Agregar subgrupo", "pleaseDeleteTheSubgroupsFirst": "Por favor, elimine primero los subgrupos"}, "cardDevice": {"deviceName": "Nombre del dispositivo", "online": "En línea", "networkType": "Tipo de red", "resolvingPower": "Resolución", "programTask": "Tarea de programa", "broadcastTask": "Tarea de transmisión", "screenStatus": "Estado de la pantalla", "lastOffline": "Última desconexión", "queryTerminalInfo": "Consultar información del terminal", "selectedCard": "Tarjeta seleccionada", "brightness": "<PERSON><PERSON><PERSON>", "volume": "Volumen", "locked": "Bloqueado", "terminalInfoFirst": "Por favor, obtenga primero la información del terminal", "width": "<PERSON><PERSON>", "height": "Alto", "synchronous": "Síncrono", "asynchronous": "Asíncrono", "temperature": "Temperatura", "number": "Número", "NoSim": "Sin información de tarjeta SIM", "fireWare": "Versión del firmware"}, "operation": {"settingMode": "Modo de configuración", "connectionLog": "Registro de conexión", "LEDscreen": "Pantalla LED", "screenshot": "Captura de pantalla", "liveVideo": "Transmisión en vivo", "screenSwitch": "Interruptor de pantalla", "timingSwitch": "Interruptor de temporización", "screenBrightness": "<PERSON><PERSON><PERSON> de <PERSON> pantalla", "autoBrightness": "Brillo automático", "timingBrightness": "Brillo de temporización", "volumeControl": "Control de volumen", "timingConfig": "Configuración de sincronización de tiempo", "connConfig": "Configuración de conexión", "syncAndAsyncConfig": "Configuración de sincronización y asincronía", "alarmSwitch": "Interruptor de alarma", "onlineUpdate": "Actualización en línea", "restartSys": "Reiniciar el sistema", "backgroundPlayback": "Reproducción en segundo plano", "backupScreenParam": "Respaldar parámetros de pantalla", "restoreScreenParam": "Restaurar parámetros de pantalla", "hardwareStatus": "Parámetros de hardware", "manualconfigurationorquery": "Configuración/consulta manual", "scheduledconfigurationorquery": "Configuración programada", "thealias": "<PERSON><PERSON>", "webServerAddress": "Dirección del servidor WEB", "thecompany": "Compañía", "realtimeaddress": "Dirección de tiempo real", "remove": "Eliminar", "volumeset": "Configuración masiva", "batchquery": "Consulta masiva", "group": "Grupo", "exportSIMInfo": "Exportar información de SIM", "clearProgram": "Borrar programa", "clearTask": "<PERSON><PERSON><PERSON> tarea", "callAddress": "Configuración de dirección de llamada", "alarmConfig": "Configuración de alarma", "clearBroadcastTask": "Borrar tarea de transmisión", "queryOrClearTiming": "Consultar o borrar temporización", "queryTiming": "Consultar temporización", "screenControl": "Control de pantalla", "broadcastControl": "Control de transmisión", "monitoringControl": "Control de monitoreo", "meteorologicalEnvironmentControl": "Control del entorno meteorológico", "passengerFlowStatistics": "Estadísticas de flujo de pasajeros", "lightingControl": "Control de iluminación", "setSIPServerAddress": "Configurar dirección del servidor SIP", "getSIPServerAddress": "Consultar dirección del servidor SIP", "SIPServerAddress": "Dirección del servidor SIP", "AlarmEquipmentMacAddress": "Dirección MAC del equipo de alarma", "AlarmEquipmentIpAddress": "Dirección IP del equipo de alarma", "SetAlarmAddress": "Configurar dirección de alarma", "GetAlarmAddress": "Consultar dirección de alarma", "AlarmAddress": "Dirección de alarma", "CallAccount": "Cuenta de llamada", "AlarmVolume": "Volumen de alarma", "LightingLevel": "Nivel de iluminación", "LightingSwitch": "Interruptor de iluminación", "Register_SIP_account": "Registrar cuenta SIP", "getGspInfo": "Consultar información de GPS", "gpsInfo": "Información de GPS", "recordingFile": "Archivo de grabación", "fullSizeScreenshotOfAndroid": "Captura de pantalla a tamaño completo de Android", "customSwitch": "Interruptor personalizado", "ThirdPartyAdvertising": "Anuncios de terceros", "BadPointDetection": "Detección de puntos defectuosos", "playerState": "Estado del reproductor", "NumberOfCardsReceived": "Número de tarjetas recibidas", "sensorDataShare": "Compartir datos de sensores", "dataShare": "Compartir da<PERSON>", "dataRefreshCycle": "Ciclo de actualización de datos", "sharedDataKey": "Clave de datos compartidos", "curFlow": "<PERSON><PERSON><PERSON>", "isEnableCurFlow": "¿Habilitar flujo actual?", "flowAddress": "Dirección del flujo", "showLocation": "Ubicación de visualización", "showPrefix": "Prefijo de visualización", "leftTop": "Arriba a la izquierda", "rightTop": "Arriba a la derecha", "leftBottom": "Abajo a la izquierda", "rightBottom": "Abajo a la derecha", "curFlowTip": "Si la dirección del flujo está vacía, se utilizará la dirección predeterminada de la plataforma", "detectingBadPixels": "Detectando puntos defectuosos", "uploadZip": "Subir archivo Zip", "versionDelete": "Eliminar versión", "hdvancedConfig": "Configuración avanzada", "hardwareConfig": "Configuración de hardware", "realTimeSet": "Configuración de tiempo real", "networkConfig": "Configuración de red", "clearBroadcast": "Borrar transmisión", "callVolumeControl": "Control de volumen de llamada", "queryProgramName": "Consultar nombre de programa", "list": "Lista", "new": "Nuevo", "oneClickOperateScreen": "<PERSON><PERSON><PERSON> pantalla con un solo clic", "systemDisplay": "Resolución del sistema", "checkAddress": "Configurar dirección de detección"}, "tips": {"brightness": "Nota: El brillo debe estar entre 1 y 255.", "volume": "Nota: El volumen debe estar entre 0 y 15.", "alarmVolume": "Nota: El volumen debe estar entre 1 y 9.", "liveVideo": "Soporta los protocolos rtmp y rtsp. Por favor, instale live primero.", "liveVideo1": "OFF para cerrar la transmisión en vivo, ON para abrirla.", "liveVideo2": "Dirección de prueba", "screenSwitch": "Nota: Borre la programación y vuelva a operar el interruptor de la pantalla.", "screenTiming": "Nota: Consultar la programación de encendido y apagado de la pantalla (soportado en la versión conn10.0.5T o superior).", "autoBrightness": "Nota: Esta función es compatible a partir de CardSystem-v3.6.0. La sensibilidad debe estar entre 0 y 100.", "autoBrightness1": "El brillo se ajusta automáticamente según los datos del sensor (soportado en la versión conn10.0.5T o superior).", "autoBrightness2": "Para las tarjetas con brillo máximo de 64, el brillo mínimo se puede configurar en 1% o un valor adecuado; para las tarjetas con brillo máximo de 255, el brillo mínimo debe configurarse en 36% o superior, de lo contrario, el brillo será bajo.", "timingBrightness": "Nota: Durante el tiempo establecido, el brillo será el configurado; fuera de este tiempo, será el brillo predeterminado. Por eje<PERSON><PERSON>, si el brillo predeterminado se establece en 80% y el brillo configurado en 20% para el rango de tiempo de 8:00 a 17:00, el brillo será del 20% durante ese período y del 80% en el resto del tiempo.", "manualconfigurationorquery1": "Solo se aplica a las tarjetas de la serie M70 y M80.", "manualconfigurationorquery2": "Durante el rango de fechas, el modo es sincrónico; solo se aplica a las tarjetas de la serie M70 y M80, versión cardSystem5.2.5.6 - 8 o superior.", "widthheighterror": "Tanto el ancho como la altura de la pantalla no pueden ser inferiores a 0 píxeles.", "noTenteredNotModified": "Si no se ingresa un valor, se considerará que no se modificará este campo.", "approval": "Realice la revisión según el flujo definido. Cuanto menor sea el número en la secuencia de flujo, más adelante se colocará.", "advancedParameter": "Nota: La interfaz de configuración de parámetros avanzados (soportado en la versión conn10.0.5T o superior).", "cardSelected": "Tarjetas se<PERSON>", "cardNameSelected": "Terminales seleccionados", "numberEmpty": "La cantidad de tarjetas seleccionadas no puede estar vacía.", "progressBar": "Nota: La barra de progreso de actualización muestra el proceso de actualización. El resultado real de la actualización se basará en el estado real de la tarjeta.", "upgradeFeatureSelectCard": "No se ha seleccionado ninguna tarjeta. No se puede utilizar la función de actualización en línea.", "UninstalledSuccessfully": "Estado: ¡Desinstalación exitosa!", "uninstallFeatureSelectCard": "No se ha seleccionado ninguna tarjeta. No se puede utilizar la función de desinstalación en línea.", "SelectUninstall": "Por favor, seleccione los componentes a desinstalar.", "selectedNotExist": "La etiqueta seleccionada es incorrecta y no existe.", "backgroundTips": "La resolución de la imagen de material debe coincidir con la resolución del panel, de lo contrario, se mostrará un error.", "releaseTips": "Las tarjetas no en línea se enviarán de forma offline. Se mostrará un mensaje de envío exitoso, pero no se mostrará la barra de progreso. La tarjeta de control enviará automáticamente el programa cuando se conecte en línea en las próximas 72 horas.", "releaseTips1": "Si se habilita el registro del reproductor, se guardará el registro de reproducción de cada tarjeta después de que el programa se envíe correctamente.", "releaseTips2": "Después de habilitar el registro del reproductor, la tarjeta de control cargará el registro al plataforma en el intervalo de tiempo establecido cuando haya un programa en la tarjeta.", "SIPAddress": "Nota: Si no se ha configurado la dirección del servidor SIP, debe configurar la dirección SIP antes de poder utilizar las funciones de intercomunicación y alarma. Después de configurar la dirección, reinicie la tarjeta de control.", "SIPAddress1": "Si el campo de entrada está vacío, se utilizará la plataforma actual como dirección del servidor SIP o se puede personalizar la dirección del servidor SIP.", "alarmConfig": "Se recomienda configurar la dirección de la plataforma como dirección de alarma.", "CustomCanUseNetworkAddress": "Se puede utilizar una dirección de red para la personalización.", "networkAddressWhenCustomizing": "Por favor, ingrese una dirección de red al personalizar.", "broadcastTask": "Nota: Si no se configura una tarea programada, la transmisión se eliminará automáticamente después de reiniciar el dispositivo y no se guardará la tarea.", "SIPTips": "Puede utilizar esta cuenta para realizar llamadas SIP con la tarjeta de control. Si la cuenta actual ya está registrada, solo se puede modificar.", "broadcastTaskRelease": "Nota: Asegúrese de no estar utilizando la función de difusión antes de publicar una tarea de transmisión.", "ModifyTerminal": "Sugerencia: Para modificar el alias del terminal, haga clic en Internet de las cosas inteligentes, cambie al modo de lista y haga clic en Modificar.", "senorBrightnessTable": "Esta función requiere la versión systemCore ******** o superior. Pasos de uso: primero, configure el valor de sensibilidad del sensor; luego, cargue el archivo BrightnessTable.xlsx predeterminado en EasyBoard para realizar modificaciones. Los valores dentro del archivo se pueden cambiar, pero no se debe cambiar el formato del archivo, de lo contrario, la operación fallará. Al cargar el archivo, solo se pueden seleccionar tarjetas del mismo tipo de sensor; de lo contrario, la operación fallará.", "customSwitchTip": "Aviso: Si se abre este interruptor, se habilitará el interruptor personalizado de la pantalla y el interruptor original de la pantalla dejará de funcionar. Si se cierra este interruptor, el interruptor original de la pantalla volverá a funcionar. Si tiene alguna duda, consulte con el personal correspondiente.", "configAdTip": "¿Está seguro de que desea conectar anuncios de terceros? Nuestra plataforma no garantiza que el contenido de la conexión de terceros sea legal y conforme. Por favor, actúe con precaución.", "configAdTip1": "¿<PERSON>bil<PERSON>r?", "configAdTip2": "¿Está seguro de que desea abrir la tarjeta de control para su uso por parte de una plataforma de terceros?", "configAdTip3": "Esta función requiere la actualización de la APK correspondiente. Para más detalles, póngase en contacto con nuestro personal de servicio.", "groupTip": "Despu<PERSON> de cancelar, se mostrarán todos los dispositivos.", "passwordIsWeak": "Su contraseña es demasiado débil. Por favor, cambie su contraseña. La contraseña debe contener al menos una letra mayúscula, una letra minúscula y un número, y tener una longitud de al menos 9 caracteres.", "authTip": "El personal revisará su solicitud en 2 - 3 días hábiles. Después de la aprobación, podrá utilizar la plataforma normalmente.", "pointCheckTip": "Después de completar la detección, espere un momento para asegurarse de que la detección se haya completado y obtener la información correcta de la detección de puntos defectuosos.", "pointCheckCard": "Para el plan de uso, póngase en contacto con nuestro personal de servicio.", "playLogsExportTip": "Al exportar el registro, si no se selecciona una fecha, se exportará el registro del día actual; de lo contrario, se exportarán todos los registros dentro de la fecha especificada.", "oneClickOperateScreenTip": "Esta operación cerrará todas las pantallas en línea."}, "file": {"name": "Nombre del archivo", "type": "Tipo de archivo", "status": "Estado del archivo", "size": "Tamaño del archivo", "UploadProgress": "Progreso de la carga", "download": "<PERSON><PERSON><PERSON>", "thumbnail": "Miniatura", "checkPending": "Pendiente de revisión", "approved": "Revisión aprobada", "auditFailed": "Revisión no aprobada", "under_review": "En revisión", "examine": "Rev<PERSON><PERSON>", "attachment": "Por favor, seleccione el archivo adjunto", "attachment1": "Haga clic o arrastre el archivo aquí para cargarlo", "auditTime": "Tiempo de revisión", "file": "Archivo", "ApprovalComments": "Comentarios de aprobación", "upload": "<PERSON><PERSON>", "update": "Actualizar", "toView": "<PERSON>er", "WithoutPermission": "Sin permiso", "uninstall": "<PERSON><PERSON><PERSON><PERSON>", "SerialNumber": "Número de serie", "OnlineUpdate": "Actualización en línea", "TheSize": "<PERSON><PERSON><PERSON>", "VersionLog": "Registro de versiones", "LogDetails": "Detalles del registro", "Onlineupgrade": "Actualización en línea en progreso...", "Waitingupdates": "Esperando actualización...", "Allcards": "Todas las tarjetas seleccionadas para la actualización no se pueden actualizar. Por favor, verifique el problema.", "DownloadComplete": "Descarga completada. Descomprima el archivo y actualice.", "NotSupported": "No se admite la visualización de la barra de progreso. Cargando...", "UpdateSuccessful": "¡Actualización exitosa!", "UpdateFailed": "¡Actualización fallida!", "ReadyDownload": "Preparando para descargar...", "ConnectionFailed": "Conexión fallida. Por favor, verifique el dispositivo.", "ThreeSeconds": "Actualización completada. La ventana de actualización se cerrará en tres segundos.", "YouCanOnlyUploadUpTo5Files": "Solo se pueden cargar un máximo de 5 archivos.", "audio": "Audio", "fileOverSize": "El archivo supera el tamaño máximo permitido.", "fileLimit": "El archivo solo admite el formato docx.", "fileVersion": "Versión del archivo", "fileLimitPdfAndVideo": "El archivo solo admite los formatos pdf y mp4."}, "card": {"cardId": "Número de serie", "setTiming": "Configurar temporizador", "getTiming": "Consultar temporizador", "noTiming": "Sin temporizador", "timing": "Temporizador", "notSpecified": "No especificado", "dateType": "Tipo de fecha", "DateRange": "<PERSON><PERSON>", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "timeType": "Tipo de tiempo", "timeFrame": "Rango de <PERSON>", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "SpecifyWeek": "Especificar día de la semana", "WeekRange": "Rango de días de la semana", "PleaseScheduledTask": "Por favor, especifique el tipo de tarea programada", "sensitivity": "Sensibilidad", "Minbrightness": "<PERSON><PERSON><PERSON>", "defaultBrightness": "<PERSON><PERSON><PERSON> predeterminado", "timingBrightness": "Brillo programado", "timedVolume": "Volumen programado", "defaultVolume": "Volumen predeterminado", "cardVoltage": "Voltaje de la tarjeta", "externalVoltage1": "Voltaje externo 1", "externalVoltage2": "Voltaje externo 2", "externalVoltage3": "Voltaje externo 3", "externalVoltage4": "Voltaje externo 4", "doorOpen": "La puerta está abierta", "version": "Versión", "humidity": "Humedad", "temperature": "Temperatura", "smokeWarning": "Advertencia de humo", "querySuccessful": "Consulta exitosa, se muestran los datos", "queryFailed": "Consulta fallida, no se pueden mostrar los datos", "screenWidth": "<PERSON><PERSON> <PERSON> (píxeles)", "screenHeight": "Alto de la pantalla (píxeles)", "screenAlias": "Alias de la pantalla", "genericVersion": "Versión genérica", "notChosenCard": "No se ha seleccionado ninguna tarjeta", "TestVersion": "Versión de prueba", "rebootNow": "<PERSON><PERSON><PERSON><PERSON> ahora", "monitorTip": "Máximo 6 pantallas de monitoreo pueden reproducirse simultáneamente. Vuelva a seleccionar", "picture-in-picture": "Imagen dentro de imagen", "pictureTip": "Las coordenadas y el ancho/alto no deben exceder el rango visible de la resolución de la pantalla. Para reiniciar la configuración, primero debe cerrar la imagen dentro de imagen", "coordinate": "<PERSON><PERSON><PERSON><PERSON>", "pictureSize": "Tam<PERSON><PERSON> de la imagen", "checkAddressTip": "Después de activar, ingrese la dirección del servidor de revisión de medios para la revisión de contenido de medios en el dispositivo", "mediaContentReview": "Revisión de contenido multimedia", "realtimeReview": "Revisión en tiempo real", "realtimeReviewTips": "Activar la revisión en tiempo real conlleva un consumo de tráfico", "clearPrompt": "Borrar aviso", "interval": "Intervalo de tiempo"}, "approval": {"auditName": "Nombre de la revisión", "auditType": "Tipo de revisión", "approvalProcess": "Proceso de aprobación", "Reviewer": "Revisor", "order": "Orden", "mediaResources": "Recursos de medios", "ProgramType": "Tipo de programa", "BroadcastMediaResources": "Recursos de medios de difusión", "BroadcastTaskResources": "Recursos de tareas de difusión", "noAudit": "No requiere revisión", "approved": "Revisión aprobada", "auditFailed": "Revisión no aprobada", "select_at_least_one_reviewer": "Seleccione al menos un revisor", "approver_cannot_blank": "El revisor no puede estar vacío", "approval_order_cannot_blank": "El orden de revisión no puede estar vacío", "InsufficientUsers": "Faltan usuarios", "clickAudit": "Revisar con un solo clic", "batchReview": "Revisión en lote", "auditMemo": "El programa tiene problemas. Ajuste y vuelva a enviar."}, "program": {"program": "Programa", "type": "Tipo de programa", "name": "Nombre del programa", "ordinaryProgram": "Programa normal", "insertProgram": "Programa de inserción", "totalSize": "Tamaño total", "state": "Estado del recurso", "ProgramList": "Lista de programas", "ProgramInfo": "Información del programa", "ComponentProperties": "Propiedades del componente", "ProgramProperties": "Propiedades del programa", "PlaybackMode": "Modo de reproducción", "EntryEffects": "Efecto de entrada", "DurationMobilizationEffect": "Duración del efecto de entrada (s)", "AppearanceEffects": "Efecto de salida", "DurationAppearanceEffect": "Duración del efecto de salida (s)", "StartPlaybackTime": "Tiempo de inicio de reproducción (s)", "DurationContinuousDisplay": "Duración de visualización continua (s)", "region": "Región", "upper": "Arriba", "left": "Iz<PERSON>erda", "width": "<PERSON><PERSON>", "height": "Alto", "BasicProperties": "Propiedades básicas", "background": "Fondo", "pellucidity": "Transparencia", "DisplayDackground": "Mostrar fondo", "open": "<PERSON>bie<PERSON>o", "close": "<PERSON><PERSON><PERSON>", "BackgroundColor": "Color de fondo", "DisplayHourScale": "Mostrar escala de horas", "HourScaleColor": "Color de la escala de horas", "ShowMinuteScale": "Mostrar escala de minutos", "MinuteScaleColor": "Color de la escala de minutos", "ScaleStyle": "Estilo de la escala", "IntegerScaleDigitalDisplay": "Mostrar números en la escala de horas enteras", "PointerStyle": "Estilo de las agujas", "ClockPointerColor": "Color de la aguja de las horas", "MinutePointerColor": "Color de la aguja de los minutos", "SecondPointerColor": "Color de la aguja de los segundos", "DisplaySecondHand": "Mostrar la aguja de los segundos", "up": "<PERSON><PERSON> arriba", "down": "<PERSON><PERSON> abajo", "play": "Reproducir", "times": "<PERSON><PERSON><PERSON>", "PleaseEnterContent": "Por favor, ingrese el contenido", "text": "Texto", "DigitalClock": "Reloj digital", "analogClock": "<PERSON><PERSON><PERSON>ó<PERSON>", "EnvironmentalMonitoring": "Monitoreo ambiental", "weather": "Clima", "Multi-materialWindow": "Ventana de múltiples materiales", "html": "Página web", "weburl": "Dirección web", "enterTime": "Por favor, ingrese la hora", "Multi-material": "<PERSON><PERSON><PERSON><PERSON> materiales", "empty": "Vaciar", "oneLevelUp": "Subir un nivel", "oneLevelDown": "Bajar un nivel", "layerOnTop": "Poner la capa en la cima", "bottomLayer": "Poner la capa en el fondo", "FullScreen": "Pantalla completa", "pageProperties": "Propiedades de la página", "effectiveDate": "Fecha de vigencia", "PlayProperties": "Propiedades de reproducción", "planSchedule": "Horario de planificación", "sun": "Dom", "one": "<PERSON>n", "two": "Mar", "three": "<PERSON><PERSON>", "four": "<PERSON><PERSON>", "five": "Vie", "six": "<PERSON><PERSON><PERSON>", "clockProperties": "Propiedades del reloj", "PleaseSelectATimeZone": "Por favor, seleccione una zona horaria", "year": "<PERSON><PERSON>", "month": "<PERSON><PERSON>", "day": "Día", "hour": "<PERSON><PERSON>", "Minute": "Min<PERSON>", "Second": "<PERSON><PERSON><PERSON>", "Week": "Se<PERSON>", "AM": "AM", "PM": "PM", "fourYears": "Año de cuatro dígitos", "12HourClock": "Reloj de 12 horas", "morningAfternoon": "AM/PM", "style": "<PERSON><PERSON><PERSON>", "dateStyle": "<PERSON><PERSON><PERSON>", "timeStyle": "<PERSON><PERSON><PERSON>", "displayStyle": "Estilo de visualización", "singleLine": "Línea <PERSON>", "Multi-line": "<PERSON><PERSON><PERSON><PERSON> lín<PERSON>", "fontSettings": "Configuración de la fuente", "fontSize": "Tamaño de la fuente", "fontColor": "Color de la fuente", "PlayTime": "Duración de reproducción", "specialEffects": "Efectos especiales", "specificFrequency": "Frecuencia de los efectos", "blink": "<PERSON><PERSON><PERSON><PERSON>", "breathe": "Respiración", "MonitoringProperties": "Propiedades de monitoreo", "compensate": "Compensar", "windSpeed": "Velocidad del viento", "windDirection": "Dirección del viento", "noise": "<PERSON><PERSON><PERSON>", "atmosphericPressure": "Presión atmosférica", "rainfall": "<PERSON><PERSON><PERSON>", "radiation": "Radiación", "lightIntensity": "Intensidad de la luz", "DisplayMode": "Modo de visualización", "stayLeft": "A la izquierda", "Centered": "Centrado", "KeepRight": "A la derecha", "singleLineScroll": "Desplazamiento en una sola línea", "speed": "Velocidad", "ms/pixel": "Milisegundos/píxel", "refreshCycle": "Ciclo de actualización", "minute": "Min<PERSON>", "fileProperties": "Propiedades del archivo", "Multi-MaterialBasicProperties": "Propiedades básicas de múltiples materiales", "mediaList": "Lista de medios", "SelectedMaterialInformation": "Información del material seleccionado", "HourMarkColor": "Color de la marca de la hora", "minuteScaleColor": "Color de la escala de los minutos", "hourHandColor": "Color de la manecilla de las horas", "minuteHandColor": "Color de la manecilla de los minutos", "pointerColor": "Color de la aguja", "backgroundColor": "Color de fondo", "static": "Está<PERSON><PERSON>", "scroll": "Desplazamiento", "turnPages": "Cambio de página", "total": "Total", "Page": "<PERSON><PERSON><PERSON><PERSON>", "preview": "Vista previa", "stopPreview": "Detener vista previa", "TextEditor": "Edición de texto", "province": "Provincia", "Multi-material_text": "Agregue medios a la derecha. Puede agregar varios medios diferentes. La pantalla LED reproducirá los medios en el orden de la lista.", "streaming": "Transmisión en vivo", "direction": "Dirección de desplazamiento", "ToTheLeft": "Hacia la izquierda", "upward": "<PERSON><PERSON> arriba", "ToTheRight": "Hacia la derecha", "addText": "Agregar texto", "liveStreamAddress": "Dirección de la transmisión en vivo", "deviceAddress": "Dirección del dispositivo", "deviceAddrTip": "<PERSON>r defecto, se utiliza la dirección actual (https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey). No modifique esta dirección sin autorización.", "deviceKey": "Clave del dispositivo", "deviceKeyTip": "Los usuarios de la plataforma deben ingresar la clave del dispositivo específico. Al enviar un programa, se mostrará la información del flujo de personas del dispositivo específico en la pantalla.", "CustomHTML": "HTML personalizado", "CustomHTMLTip1": "Es el número de serie del dispositivo", "CustomHTMLTip2": "Es la hora", "CustomHTMLTip3": "Es el número total de entradas hoy", "CustomHTMLTip4": "Es el número de entradas en este período de una hora", "CustomHTMLTip5": "Es el número total de entradas históricas", "CustomHTMLTip6": "Es el número total de salidas hoy", "CustomHTMLTip7": "Es el número de salidas en este período de una hora", "CustomHTMLTip8": "Es el número total de salidas históricas", "flowStatistics": "Estadísticas del flujo de personas", "weatherTip1": "Temperatura actual", "weatherTip2": "Índice de calidad del aire (AQI)", "weatherTip3": "Fecha actual (incluyendo la temperatura en tiempo real)", "weatherTip4": "Clima actual", "weatherTip5": "Temperatura máxima del día", "weatherTip6": "Temperatura mínima del día", "weatherTip7": "Viento del día", "weatherTip8": "Viento del día actual", "weatherTip9": "Imagen del tiempo del día actual, formato: img-ancho-alto", "weatherTip10": "En los %{} anteriores, 'yesterday' representa ayer, 'arr.0' representa el día actual, '1' representa mañana, '2' representa pasado mañana, '3' representa el día después de pasado mañana y '4' representa el día después del día después de pasado mañana.", "timeType": "Tipo de tiempo", "timeTypeTip": "Cambiar el tipo cambiará el HTML personalizado", "HDMITypeDescription": "Descripción del tipo HDMI", "HDMIDescription1": "1. Los programas de tipo HDMI actualmente solo son compatibles con la serie m70. Después de enviar con éxito, por favor verifique la situación real de visualización en la pantalla. La situación real de visualización se basará en la pantalla.", "HDMIDescription2": "2. Si el efecto de visualización real es erróneo, primero verifique la conexión del cable HDMI o si la versión del software correspondiente es errónea. Después de verificar sin errores, puede intentar reenviar. Si la tarjeta de control actual está en modo de imagen en imagen HDMI, debe configurarla en modo de sincronización antes de volver a configurar. Por favor contacte a nuestro personal técnico para obtener el plan de uso específico.", "text-to-speech": "Texto a voz", "addProgramTips": "Después de enviar con éxito un programa normal, reemplazará el programa original. Después de enviar con éxito un programa de inserción, no reemplazará el programa original y solo se reproducirá durante el período de inserción.", "enablePlayerLog": "¿Habilitar el registro de reproducción?", "playLog": "Registro de reproducción", "timeInterval": "Intervalo de tiempo (minutos)", "discount": "Longitud de cada segmento", "isDiscount": "¿Aplicar descuento?", "discountText": "Longitud de cada segmento separada por comas. Por ejemplo: 256,256,128", "segmentation": "División", "PleaseEnterDiscountWidth": "Por favor ingrese el ancho de descuento", "PleaseEnterTheCorrectContentFormat": "Por favor ingrese el formato de contenido correcto", "multi-picture": "<PERSON><PERSON><PERSON><PERSON>", "PleaseSelectPictureVideoSplit": "Por favor seleccione una imagen o un video para dividir", "totalWidthDiscountCannotWidth": "La longitud total de descuento no puede ser mayor que el tamaño del material", "sensorsShareData": "Datos compartidos por los sensores", "broadcastSort": "Orden de reproducción", "horizontal": "Fila horizontal", "verticalRow": "Fila vertical", "discountMode": "Modo de descuento", "level": "Horizontal", "vertical": "Vertical", "negativeIon": "<PERSON><PERSON> negativos", "zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>", "materialCycle": "Ciclo de material", "refreshSec": "Intervalo de actualización", "zoom": "Zoom", "offset": "Desplazamiento", "scale": "<PERSON>st<PERSON><PERSON>"}, "setTime": {"timeZone": "Zona horaria/Hora", "y60Channels": "Nota: ¡Esta función requiere la versión CardSystem_v5.2.6.3 o superior!", "theTimeZone": "Zona horaria", "setUpThe": "Configurar", "query": "Consultar", "computerTime": "Calibrar el reloj a la hora del equipo", "ledTime": "Consultar la hora actual del dispositivo LED", "queryFails": "¡Consulta fallida!", "versionCardSystem": "¡Por favor, verifique la versión de CardSystem!", "deviceTimeZone": "Zona horaria del dispositivo", "setupFailed": "Configuración fallida", "setupSuccess": "Configuración exitosa", "connectTo485": "¿Conectado a 485?", "calibrationFailure": "Calibración fallida", "successfulCalibration": "Calibración exitosa", "querySuccessful": "Consulta exitosa", "synchronizationSettings": "Configuración de sincronización", "model": "Modo", "masterSlave": "Maestro/Eslavo", "IdentificationCode": "Código de identificación", "timeOffset": "Desfase de tiempo (milisegundos)", "screenBrightness": "<PERSON><PERSON><PERSON> de <PERSON> pantalla", "volume": "Volumen", "screenSwitch": "Interruptor de pantalla", "synchronizationInterval": "Intervalo de sincronización", "lastSynchronousTime": "Última hora de sincronización", "minTime": "(minutos/vez)", "main": "Maestro", "from": "<PERSON><PERSON><PERSON>", "masterSlaveMode": "Modo maestro/esclavo", "NTPpathNull": "¡Al configurar la ruta NTP como vacía, se producirá un tiempo de espera de conexión, pero la configuración se realizará correctamente!", "serverAddress": "Dirección del servidor NTP", "selectA": "Por favor, seleccione un modo maestro/esclavo", "selectATime": "Por favor, seleccione una zona horaria"}, "synchronous": {"unknownError": "Error descon<PERSON>", "doesNotExist": "Error en el estado de red de la tarjeta y por favor, verifique la versión de CardSystem", "format": "No se ha configurado la programación"}, "home": {"totalNumber": "Número total de dispositivos", "onlineRate": "Tasa de conectividad", "number": "Número", "brightScreen": "Tasa de pantalla encendida", "operating": "Volumen acumulado de operaciones", "program": "Volumen acumulado de programas", "switchDate": "<PERSON><PERSON> de camb<PERSON>", "month": "<PERSON><PERSON>", "Announcement": "<PERSON><PERSON><PERSON>", "determined": "Pendiente de determinar", "programmeStatistics": "Estadísticas de creación de programas", "operationStatistics": "Estadísticas de operaciones de usuarios", "releasePeople": "Persona de publicación", "date": "<PERSON><PERSON>", "noMoreAnnouncements": "¡No hay más anuncios!", "show": "Programa", "by": "Volumen total de revisiones", "operat": "Volumen de operaciones", "operatingSpeed": "Velocidad de operación (milisegundos)", "Reviewrate": "Tasa de aprobación de revisiones", "statistics": "Estadísticas de publicación de programas", "cardNumber": "Número de tarjeta", "releaseAmount": "Volumen total de publicaciones", "successRate": "Tasa de éxito", "totalSuccess": "Volumen total de éxitos", "successful": "Éxito", "failure": "<PERSON><PERSON><PERSON>", "founder": "<PERSON><PERSON><PERSON>", "TotalAverageMilliseconds": "Número total de milisegundos promedio", "great": "Excelente", "good": "Bueno", "center": "Regular", "poor": "Malo", "NaN": "NaN", "clickRefresh": "Haga clic para actualizar", "warningNotice": "Notificación de alerta", "temperatureWarning": "Alerta de temperatura", "humidityWarning": "<PERSON><PERSON><PERSON> de <PERSON>", "voltageWarning": "Alerta de voltaje de tarjeta", "voltage1Warning": "Alerta de voltaje externo 1", "voltage2Warning": "Alerta de voltaje externo 2", "doorOpenWarning": "Alerta de puerta abierta", "smokeWarning": "Alerta de humo", "unknownWarning": "Alerta desconocida", "temporarilyNoData": "No hay datos disponibles temporalmente", "showsSentProgram": "Mostrar tarjetas a las que se ha enviado un programa", "announcementDetails": "Detalles del anuncio", "policeDetails": "Detalles de la alerta", "untreated": "Sin tratar", "haveDeal": "Trata<PERSON>", "noMoreCalls": "¡No hay más alertas!", "refreshSuccessful": "¡Actualización exitosa!", "DynamicLargeScreen": "Plataforma visualizable de terminales inteligentes", "piece": "Unidad", "EquipmentStatistics": "Estadísticas de dispositivos", "unitPCS": "Unidad: unidades", "PoleStatistics": "Estadísticas de terminales", "ControlStatistics": "Estadísticas de control", "UnitTimes": "Unidad: veces", "TotalNumberOfPoles": "Número total de terminales"}, "announcement": {"titleText": "Título y contenido del anuncio", "title": "<PERSON><PERSON><PERSON><PERSON>", "enterTitle": "Por favor, ingrese el título del anuncio", "content": "Contenido", "enterContent": "Por favor, ingrese el contenido del anuncio", "also": "Todavía se pueden ingresar", "character": "caracteres"}, "resetPWD": {"resetPassword": "Restablecer contraseña", "accountNumber": "Ingrese la cuenta", "repairMethod": " Seleccione el método de reparación", "changePassword": "Cambiar contraseña", "success": " Éxito", "enterResetPassword": "Por favor, ingrese la cuenta para restablecer la contraseña"}, "police": {"notOpenSettingsNull": "<PERSON><PERSON><PERSON> que no se habiliten se consideran configurados como vacíos", "monitoringItems": "Elementos de monitoreo", "hasBeenOpen": "Habilitado", "lower": "Límite inferior", "ceiling": "Límite superior", "haveSmoke": "Hay humo", "openDoorAlarm": "Alarma de apertura de puerta", "turnSmokeAlarm": "Activar alarma de humo", "checkCardSysterm": "No se puede analizar la operación. Verifique primero la versión de CardSysterm", "isNotOpened": "Error en el estado de conexión de la tarjeta", "sureToSetAlarmThresholds": "¿Está seguro de que desea establecer los umbrales de alarma?", "upperAndLowerEmpty": "Para los elementos de monitoreo habilitados, los límites superior e inferior no pueden estar vacíos", "numEmpty": "Para los elementos de monitoreo habilitados, el tipo de entrada de los límites superior e inferior debe ser numérico", "upperGreaterLower": "Para los elementos de monitoreo habilitados, el límite superior debe ser mayor que el límite inferior", "materialLibrary": "Biblioteca de materiales"}, "hardware": {"timeQuery": "La copia de seguridad de los parámetros tarda tiempo. Si no se encuentra el archivo de copia de seguridad en primer intento, intente actualizar la página más tarde.", "restoreParam": "Restau<PERSON>", "backupParameter": "Realizar copia de seguridad de parámetros", "hardwareStatus": "Estado del hardware", "restore": "Restaurar", "akeyBackup": "Copia de seguridad con un solo clic", "backupSuccessful": "Copia de seguridad realizada con éxito", "backupSuccessful1": "La versión actual no devuelve el progreso", "BackingUp": "Realizando copia de seguridad... Por favor, espere.", "selectionCard": "No se ha seleccionado ninguna tarjeta. No se puede utilizar la función de parámetros de hardware.", "parameterRecovery": "Restaurando parámetros...", "waitingRecover": "Esperando para restaurar...", "namePackage": "Tarjeta/Paquete al que pertenece", "restoreCancel": "El estado de conexión de esta tarjeta es incorrecto. La restauración se ha cancelado.", "readyRecovery": "Preparándose para restaurar...", "tryAgain": "El estado de conexión de todas las tarjetas seleccionadas para la restauración es incorrecto. Por favor, verifique y vuelva a intentarlo.", "recoveryComplete": "Restauración completada.", "afterRecovery": "Restauración finalizada. La ventana de actualización se cerrará en tres segundos.", "Recovering": "Restaurando...", "timesOut": "Algunas tarjetas no admiten la restauración de parámetros. El tiempo de espera de la solicitud ha expirado. La restauración ha finalizado."}, "el": {"colorpicker": {"confirm": "Confirmar", "clear": "Limpiar"}, "image": {"error": "Error al cargar la imagen"}, "table": {"emptyText": "No hay datos disponibles"}, "pagination": {"total": "Total", "pagesize": "elementos/página", "goto": "Ir a", "pageClassifier": "p<PERSON><PERSON><PERSON>"}}, "task": {"name": "Nombre de la tarea", "isCycle": "¿Es cíclica?", "cycleIndex": "Número de c<PERSON>los", "InfiniteLoop": "<PERSON><PERSON><PERSON> infinito", "task": "Tarea", "type": "Tipo de tarea", "text": "Contenido de texto", "voiceName": "Declaración", "speed": "Velocidad de <PERSON>bla", "pitch": "<PERSON>no de voz", "femaleVoice": "Voz femenina", "maleVoice": "<PERSON><PERSON> mascu<PERSON>", "textToLanguage": "Texto a voz", "media": "Medios", "plays": "Número de reproducciones", "selectMedia": "Seleccionar medios", "TemplateContent": "Contenido de la plantilla", "ImportTemplate": "Importar plantilla", "import": "Importar", "normal": "Normal", "faster": "<PERSON><PERSON>", "fast": "<PERSON><PERSON><PERSON><PERSON>", "playTypes": "Modos de reproducción", "specifyPlays": "Especificar número de reproducciones", "specifyPlayTime": "Especificar tiempo de reproducción", "isTiming": "¿Habilitar temporizador?", "allTime": "Reproducir durante el período de tiempo", "inStream": "¿Es una tarea de inserción?", "normalTask": "Tarea normal", "inStreamTask": "Tarea de inserción", "clearInStream": "Solo borrar tareas de inserción"}, "lamp": {"poleName": "Nombre del terminal", "broadcast": "Radiodifusión", "monitor": "Monitoreo", "environment": "Entorno meteorológico", "lighting": "Iluminación", "Passenger": "Estadísticas de flujo de personas", "longitude": "<PERSON><PERSON><PERSON>", "latitude": "Latitud", "ChooseTargeting": "Seleccionar ubicación", "LoadingPositioning": "Cargando ubicación...", "FailedToGetLocationInformation": "Error al obtener la información de ubicación", "online": "En línea", "offline": "Fuera de línea", "targetingIsSet": "Ubicación configurada", "targetingNotSet": "Ubicación no configurada", "listMode": "Mo<PERSON> lista", "mapMode": "Modo mapa", "updateTime": "Hora de actualización", "TheSensorIsNot": "No se ha conectado un sensor en este período de tiempo", "getMapException": "Error al obtener el mapa", "NoLocationData": "No hay datos de ubicación", "PleaseEnterThePoleNameOrdeviceID": "Por favor, ingrese el nombre del terminal o el ID del dispositivo", "radioState": "Estado de la radiodifusión", "latAndLngNotEmpty": "La latitud y la longitud no pueden estar vacías", "gpsUploadState": "Carga de GPS"}, "broadcast": {"SIPAddress": "Confirmar la configuración de la dirección del servidor de la plataforma actual como dirección del servidor SIP", "customSIPAddressStart": "Confirmar la configuración", "customSIPAddressEnd": "como dirección del servidor SIP", "radioState1": "Solo admite radiodifusión", "radioState2": "Admite radiodifusión y transmisión en vivo, pero no admite alarmas", "radioState3": "Admite radiodifusión y alarmas, pero no admite transmisión en vivo", "radioState4": "Admite radiodifusión, transmisión en vivo y alarmas", "SIP_account": "Cuenta SIP", "multicastAddress": "Dirección de multidifusión", "selectDate": "Por favor, seleccione una fecha", "pauseOrOpenBroadcast": "Pausar/Reanudar la tarea de radiodifusión", "broadcastInfo": "Detalles de la radiodifusión", "broadcastProgramState": "Estado del programa de radiodifusión", "paused": "En pausa", "playing": "En reproducción", "haveProgram": "¿Hay un programa en directo?", "playMode": "Modo de reproducción", "focusMode": "Modo de en<PERSON>", "fallingTone": "<PERSON><PERSON> descendente", "mute": "Silenciar", "noProgram": "No hay programas disponibles", "noBroadcast": "No hay radiodifusiones disponibles"}, "meteorological": {"temperatureSubText": "Una temperatura de '--' indica una consulta fallida o falta de datos", "Illuminance": "Iluminación", "humiditySubText": "Una humedad de '--' indica una consulta fallida o falta de datos", "noiseSubText": "Un ruido de '--' indica una consulta fallida o falta de datos", "windSpeedSubText": "Una velocidad del viento de '--' indica una consulta fallida o falta de datos", "windDirectionSubText": "Una dirección del viento de '--' indica una consulta fallida o falta de datos", "illuminationSubText": "Una iluminación de 0 indica una consulta fallida o falta de datos", "PM10SubText": "Un valor de PM10 de '--' indica una consulta fallida o falta de datos", "PM25SubText": "Un valor de PM2.5 de '--' indica una consulta fallida o falta de datos", "pressureSubText": "Una presión de '--' indica una consulta fallida o falta de datos", "rainFallSubText": "Una precipitación de '--' indica una consulta fallida o falta de datos", "radiationSubText": "Una radiación de '--' indica una consulta fallida o falta de datos", "pressure": "Presión", "rainFall": "Precipitación", "radiation": "Radiación"}, "bigScreen": {"VideoSurveillance": "Monitoreo de video", "DeviceList": "Lista de dispositivos", "Address": "Dirección detallada", "NumberOfOnline": "Número de dispositivos en línea", "OperationLog": "Registro de operaciones", "ProgramPlayStatistics": "Estadísticas de reproducción de programas", "RequestFailed": "Error en la solicitud"}, "electricity": {"current": "<PERSON><PERSON><PERSON>", "power": "Potencia", "electricity": "Energía eléctrica", "voltage": "Voltaje", "currentSubText": "Un valor de corriente igual a 0 indica una consulta anómala o la ausencia de datos.", "powerSubText": "Un valor de potencia igual a 0 indica una consulta anómala o la ausencia de datos.", "electricitySubText": "Un valor de energía eléctrica igual a 0 indica una consulta anómala o la ausencia de datos.", "voltageSubText": "Un valor de voltaje igual a 0 indica una consulta anómala o la ausencia de datos.", "clearData": "<PERSON><PERSON><PERSON> da<PERSON>", "clearSuccessfully": "<PERSON><PERSON><PERSON>", "clearFailed": "<PERSON><PERSON><PERSON> al borrar", "cancelClear": "<PERSON><PERSON><PERSON> borra<PERSON>", "monthlyElectricity": "Estadísticas mensuales de energía eléctrica", "exportElectricity": "Exportar datos mensuales de energía eléctrica", "setElectricityTime": "Configurar el período de consulta de energía eléctrica", "selectTime": "Por favor, seleccione el período de consulta de energía eléctrica", "tip": "El período de consulta predeterminado es de 24 horas.", "electricityData": "Datos de energía eléctrica", "curElectricityTime": "<PERSON><PERSON><PERSON> actual"}, "monitor": {"device_ip": "IP del dispositivo", "port": "Puerto", "owning_terminal": "Terminal al que pertenece", "monitorSaveTip": "Para dispositivos en línea, modifique con precaución el nombre de usuario y la contraseña. Un error en la modificación puede causar la desconexión del dispositivo.", "Device_name_cannot_be_empty": "El nombre del dispositivo no puede estar vacío.", "Please_enter_the_device_serial_number": "Por favor, ingrese el número de serie del dispositivo.", "monitorSaveTip1": "Cambiar el nombre de usuario y la contraseña a: Nombre de usuario", "Split_screen": "<PERSON><PERSON><PERSON> dividida", "PTZ_control": "Control de pan, tilt y zoom", "equipment": "Equipamiento", "deviceIdIsNotEmpty": "El ID del dispositivo no puede estar vacío.", "CommandISExist": "El comando no existe.", "notExistOrNotLoggedIn": "El dispositivo no se encontró o no está registrado.", "isNotExist": "El dispositivo no existe.", "notLoggedIn": "El dispositivo no está registrado.", "zoom": "Zoom", "aperture": "Apertura", "focus": "Enfoque", "screenshot": "Captura de pantalla", "noPlayback": "No hay reproducciones disponibles para este período.", "downloadPlayback": "<PERSON><PERSON><PERSON> reproducci<PERSON>", "selectDateRange": "Seleccione un período de tiempo", "tip": "Esta descarga transferirá las grabaciones de reproducción de la cámara al servidor. Espere un tiempo y luego descárguelas a su dispositivo desde esta página.", "normal": "Cámara normal", "humanNumberStatistic": "Estadísticas de flujo de personas", "insideHumanNumber": "Acumulación de personas", "traffic": "Tráfico inteligente", "cameraType": "Tipo <PERSON>", "monitorSaveTip2": "Cambiar el tipo de función de la cámara a:", "openTheAlarm": "Activar alarma", "crowdAlarmThreshold": "Umbral de alarma de aglomeración", "intervalForSendingEmails": "Intervalo de tiempo para enviar correos electrónicos", "openTheAlarmTip": "Al activar la alarma, si el número de personas en el dispositivo supera el umbral de alarma, se enviará un correo electrónico al usuario al que pertenece el dispositivo. Después de activar la alarma por correo electrónico, se enviará el siguiente correo según el intervalo de tiempo establecido. Se utilizará el correo electrónico de registro del usuario.", "offLineOrNotExist": "El dispositivo está desconectado o no existe.", "serialNumber": "Número de serie de la cámara", "rtmpStreamState": "¿Activar transmisión RTMP?", "streamCloseTip": "Al cerrar la transmisión, no podrá ver la imagen de monitoreo en la página web. ¿Está seguro de que desea cerrar la transmisión?", "streamOpenTip": "Al activar la transmisión, podrá ver la imagen de monitoreo en la página web. Esta operación consumirá un cierto flujo de datos. ¿Está seguro de que desea activar la transmisión?"}, "pay": {"contactInformation": "Información de contacto", "addressValue": "1.º, Avenida de la Reina, Central, Hong Kong", "savingsAccountNumber": "Número de cuenta de ahorros", "xixunCompanyValue": "Shanghai Xixun Electronic Technology Co., Ltd.", "bankName": "Nombre del banco", "bankNameValue": "The Hongkong and Shanghai Banking Corporation Limited", "swiftCode": "Código SWIFT", "tips1": "Por favor, compre un dispositivo VIP para que funcione correctamente.", "tips": "Después de iniciar sesión correctamente, suscríbase a un VIP en los servicios pagos para que el dispositivo funcione correctamente.", "totalPrice": "Precio total", "contactPerson": "Persona de contacto", "contactPersonTips": "Por favor, ingrese la persona de contacto", "telephone": "Teléfono de contacto", "telephoneTips": "Por favor, ingrese el teléfono de contacto", "address": "Dirección de la empresa", "addressTips": "Por favor, ingrese la dirección de la empresa", "companyName": "Nombre completo de la empresa", "companyNameTips": "Por favor, ingrese el nombre completo de la empresa", "vipDuration": "Duración válida del VIP/año", "vip4": "VIP 4 de diamante de primera calidad", "upgradeToPaidVersion": "Actualizar a la versión de pago", "silverCardVip": "VIP de tarjeta plateada", "goldCardVip": "VIP de tarjeta de oro", "diamondVip": "VIP de diamante", "superVip": "Super VIP", "orderService": "Suscribirse a servicios", "currentVersion": "Versión actual: ", "numberOfTerminals": "Número de terminales", "TheRemainingAmount": "Cantidad restante", "ExpireDate": "<PERSON><PERSON>nc<PERSON>o", "selectVersion": "Seleccionar versión", "SelectDuration(years)": "Seleccionar duración (años)", "totalOrder": "Total del pedido", "submitOrder": "Enviar pedido", "freeVersion": "Versión gratuita", "price": "Precio", "ThereAreUnpaidOrders": "Hay pedidos sin pagar.", "ThereIsAnUnpaidOrderPleaseGoToPay": "Hay un pedido sin pagar. Por favor, vaya a realizar el pago.", "OrderRecord": "Registro de pedidos", "100ControlCards": "Número de tarjetas de control: 100", "500ControlCards": "Número de tarjetas de control: 500", "NumberOfControlCards1500": "Número de tarjetas de control: 1500", "unpaid": "<PERSON> pagar", "transactionCreation": "Creación de transacción", "UnpaidTransactionTimeoutClosed": "La transacción sin pagar ha expirado y se ha cerrado.", "paymentSuccessful": "<PERSON><PERSON>oso", "OrderDetails": "Detalles del pedido", "pay": "<PERSON><PERSON>", "orderNumber": "Número de pedido", "Years": "<PERSON><PERSON><PERSON>", "PaymentStatus": "Estado de pago", "amount": "Monto", "newPurchase": "Compra nueva", "Renewal": "Renovación", "upgrade": "Actualización", "PayForTheOrder": "Pagar el pedido", "ExpectedPeriod": "<PERSON><PERSON><PERSON>: ", "to": "hasta", "ActualDeadlineIsSubjectToPayment": "El plazo real se determinará según el pago.", "cancelOrder": "Cancelar pedido", "theOrderWillBeAutomaticallyClosed": "El pedido tiene una validez de 15 días. Por favor, realice el pago a tiempo. Si no se paga después de 14 días, el pedido se cerrará automáticamente.", "AfterOrderIsPaidSuccessfully": "Nota: Después de pagar el pedido con éxito, puede acceder a la", "QueryPaymentInformationOnThePage": "página para consultar la información de pago.", "paymentMethod": "Método de pago", "publicAccount": "Cuenta pública", "AccountName": "Titular de la cuenta: ", "AccountBank": "Banco: ", "LinkNumber": "<PERSON><PERSON><PERSON><PERSON>: ", "account": "Número de cuenta: ", "uploadTheBankReceipt": "Si ha realizado un depósito a la cuenta anterior, por favor, cargue el comprobante bancario.", "receivedByMajorBanks": "El tiempo de acreditación de las transferencias bancarias públicas de los principales bancos varía entre 2 horas y 3 días hábiles.", "notifyYouViaSMS": "Nuestro personal le notificará y activará el servicio tan pronto como recibamos el pago.", "UploadBankReceipt": "Cargar comprobante bancario", "contactOurSalesStaff": "Si tiene alguna pregunta, pón<PERSON>e en contacto con nuestro personal de ventas.", "NotesForPublicTransfers": "Notas para transferencias públicas:", "submittedForFinancialReview": "El acreditación del pago en línea tiene un retraso y se debe enviar el comprobante bancario para la revisión financiera.", "TransfersFromPersonalAccounts": "Las transferencias desde cuentas personales a nuestra cuenta pública solo pueden emitir facturas fiscales para personas físicas.", "IfTheCompanyAccountIsTransferred": "Las transferencias desde cuentas de empresas a nuestra cuenta pública pueden emitir facturas fiscales para empresas (IVA general o IVA especial).", "uploadTip": "Se admiten formatos JPG o PNG, con un tamaño máximo de 5 MB.", "BankCardNumber": "Número de tarjeta bancaria", "bankCardNumberWhenTransferring": "Por favor, ingrese el número de tarjeta bancaria utilizado en la transferencia.", "transactionHour": "Hora de la transacción", "CancellationLineItemCannotBeEmpty": "El artículo de cancelación del pedido no puede estar vacío.", "WaitingForSellerToConfirm": "Esperando confirmación del vendedor", "PurchaseStatus": "Estado de compra", "bankReceipt": "Comprobante bancario", "Serve": "<PERSON><PERSON><PERSON>", "Optional": "Opcional", "deadline": "Fecha límite: ", "orderUpdateTips": "Solo se pueden modificar el comprobante bancario cargado o el número de tarjeta bancaria del pedido. Si tiene preguntas sobre otra información, póngase en contacto con nuestro personal.", "PleaseUploadBankReceipt": "Por favor, cargue el comprobante bancario.", "PleaseEnterBankCardNumber": "Por favor, ingrese el número de tarjeta bancaria.", "NoRelatedOrders": "No hay pedidos relacionados.", "ErrorUploadingBankReceipt": "Error al cargar el comprobante bancario", "changeVipStateTip": "Si se cambia el estado VIP del usuario actual a Super VIP, todos los usuarios bajo el ID de empresa del usuario actual se convertirán en Super VIP."}, "statistic": {"enableHumanStatistic": "Habilitar estadísticas de flujo de personas", "queryHumanStatisticToday": "Consultar el flujo de personas del día actual", "enter": "Número de entradas", "exited": "Número de salidas", "countingMonitoring": "Monitoreo de estadísticas de flujo de personas", "viewChart": "<PERSON>er g<PERSON>", "currentNumber": "Número actual", "lineChart": "Gráfico de líneas de estadísticas de personas en la zona", "areaPeopleNum": "Número de personas en la zona", "selectHistoricalData": "Consultar datos histó<PERSON>", "sendData": "<PERSON><PERSON><PERSON> datos", "keyTip": "Después de habilitar la función de visualización de datos, se generará automáticamente la clave", "isShowHumanNumber": "¿Se ha habilitado la función de visualización de datos de flujo de personas en el terminal?", "dataKey": "Clave", "noDataKey": "El dispositivo actual no tiene clave", "clickCopy": "Haga clic para copiar", "copySuccess": "<PERSON><PERSON> exitosa"}, "alarm": {"alarmNum": "Número de alarma", "alarmPeople": "Persona de alarma", "call": "Llamante", "receive": "Receptor", "callTime": "Tiempo de llamada", "channel": "Canal", "dstChannel": "Canal de destino", "alarmDeviceInfo": "Información del dispositivo de alarma", "setCallingVolume": "Configurar el volumen de llamada", "callingVolume": "Volumen de llamada", "notEnable": "No habilitado", "setAlarmInfo": "Configurar la información del dispositivo de alarma", "sipAccount": "Cuenta SIP", "sipServiceAddress": "Dirección del servidor SIP", "sipServicePort": "Puerto del servidor SIP", "accountState": "Estado de la cuenta", "alarmDeviceNetwork": "Información de red del dispositivo de alarma", "setNetwork": "Configurar el estado de la red", "dynamic": "Dinámico", "static": "Está<PERSON><PERSON>", "gateway": "<PERSON><PERSON><PERSON>", "subnetMask": "Máscara de subred", "alarmAccount": "Cuenta de alarma", "accountType": "Tipo de cuenta", "registerAlarmAccount": "Registrar la cuenta del dispositivo de alarma", "registerPhoneNumber": "Registrar la cuenta del teléfono", "accountRule": "La cuenta no puede estar vacía y debe ser de 11 dígitos o letras", "account": "C<PERSON><PERSON>", "batchSettings": "Configuración en lote", "alarmNetworkTip": "Mantenga presionado el dispositivo de alarma hasta que suene una señal. Pulse una vez para reproducir la IP y pulse tres veces para cambiar entre IP dinámica y estática", "alarmAddressTip": "Antes de configurar la dirección de alarma, configure la cuenta del dispositivo de alarma y otra información en la información del dispositivo de alarma", "alarmAddressSetTip": "En estado predeterminado, la plataforma configurará la dirección de alarma como la dirección de la plataforma", "defaultState": "Predeterminado", "custom": "Personalizado", "myPhoneNumber": "Su propio número", "calledPhoneNumber": "<PERSON><PERSON><PERSON><PERSON>", "alarmInfoTip": "Antes de configurar la información del dispositivo de alarma, registre una cuenta", "backupCalledPhoneNumber": "Númer<PERSON>"}, "traffic": {"enableTraffic": "Habilitar el tráfico inteligente", "eventName": "Nombre del evento", "plateNumber": "Número de matrícula", "plateType": "Tipo de matrícula", "plateColor": "Color de la matrícula", "vehicleColor": "Color del vehículo", "vehicleType": "Tipo de vehículo", "vehicleSize": "Tamaño del vehículo", "illegalPlace": "Lugar de infracción", "eventTime": "Hora del evento", "downloadEventPic": "Descargar la foto de la infracción", "illegalPic": "Foto de la infracción"}, "radar": {"radarSpeed": "Medición de velocidad por radar", "radarSetting": "Configuración del radar", "fastestCar": "Coche más rápido", "closestCar": "Coche más cercano", "setResponseTime": "Tiempo de respuesta", "setOutputTarget": "Objetivo de salida", "setMinSpeed": "Velocidad mínima de detección", "setMaxSpeed": "Velocidad máxima de detección", "setSensitivity": "sensibilidad", "isConnect": "Conectado al radar o no", "speed": "velocidad", "parameter": "El parámetro", "speedLimitRange": "Límite de velocidad", "addSpeedLimitRange": "Por favor agregue un intervalo de límite de velocidad", "minSpeed": "Velocidad <PERSON>", "maxSpeed": "Velocidad m<PERSON>xi<PERSON>", "radarSpeedLimit": "Límite de velocidad radar"}, "ac": {"apOnlineRate": "Tasa de AP en línea", "apOnlineNumber": "Número de AP en línea", "apSum": "Total de AP", "onlineTerminal": "Número de terminales en línea", "flowStatistics": "Estadísticas de tráfico", "flowStatisticsToday": "Estadísticas de tráfico de hoy", "name": "Nombre de AC", "macAddress": "Dirección MAC", "belongArea": "<PERSON><PERSON>", "belongOrganization": "Organización perteneciente", "belongProject": "Proyecto perteneciente", "userOnlineCount": "Número total de usuarios en línea", "acOnlineUserCount": "Número de usuarios en línea de AC", "upstreamTraffic": "Tráfico ascendente", "downlinkTraffic": "Tráfico descendente", "refresh": "Actualizar"}, "userAuth": {"pass": "Verificación exitosa", "fail": "Verificación fallida", "company": "Certificación empresarial", "personal": "Certificación de identidad personal", "unverified": "No verificado", "certificationAudit": "Auditoría de certificación", "authMode": "Método de autenticación", "authInfo": "Información de autenticación", "enterpriseLicense": "Licencia comercial de la empresa", "OfficialSeal": "Certificado de sello oficial de la empresa", "FrontOfIdentityCard": "Frente del DNI", "ReverseOfIDCard": "Reverso del DNI", "reason": "Razón", "authentication": "Autenticación", "pleaseUploadEnterpriseLicenseOfficialSeal": "Por favor, suba el certificado de sello oficial y la licencia comercial de la empresa", "updateAuth": "Actualizar autenticación", "uploadAuth": "Subir autenticación"}, "cat1": {"temp": "Temperatura de la plantilla", "power": "Potencia activa", "electricEnergy": "Energía activa", "roadOne": "Carril 1", "roadTwo": "Carril 2", "addDevice": "Agregar dispositivo", "channel": "Canal", "colorTemp": "Temperatura de color", "scheduleTip": "Este temporizador comienza desde el tiempo RealTime establecido. <PERSON>r eje<PERSON><PERSON>, si el tiempo establecido es 12:30, comenzará a las 12:30 y terminará en el siguiente punto de tiempo, el estado de la luz individual será el brillo y la temperatura de color establecidos. Si desea apagar la luz individual, puede establecer el brillo en 0."}, "notify": {"NotificationStrategy": "Estrategia de notificación", "NotifyTip": "(Después de que el usuario habilite la notificación, se le notificará por correo electrónico en el momento especificado)", "OfflineNotify": "Notificación fuera de línea", "CardNotWorkingNotification": "Notificación de tarjeta no funcional", "tactics": "Estrategia (horas)", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "PleaseSelectNotification": "Por favor, seleccione una estrategia de notificación", "SendingMailbox": "Buzón de envío", "SendingTime": "<PERSON>ra de env<PERSON>", "Username/EmailAddress": "Nombre de usuario/Correo electrónico de envío", "OffLineTime": "Tiempo fuera de línea", "AbnormalTime": "Tiempo anormal", "NumberOfCardsRLastTime": "Número de tarjetas recibidas la última vez", "NumberOfCardsReceivedExceptionOccurs": "Número de tarjetas recibidas cuando ocurre una excepción"}, "thirdPartyAd": {"enabledThirdPartyAd": "Conexión de anuncios de terceros", "thirdPartyUrl": "Dirección del servicio de terceros", "loopTip": "Reproducir en bucle con los anuncios existentes en el dispositivo, los múltiples anuncios se distribuirán uniformemente en la lista de anuncios existentes del dispositivo", "onceTip": "Insertar en los anuncios existentes del dispositivo para reproducir una vez, los múltiples anuncios se distribuirán uniformemente en la lista de anuncios existentes del dispositivo", "mutexTip": "Reproducción en bucle exclusiva", "adInterval": "<PERSON><PERSON>lo de an<PERSON>", "queryThirdPartyAd": "Consultar anuncios de terceros", "downloadUrl": "Dirección de descarga", "impression": "Información de reproducción", "playHour": "Horario de reproducción", "playCount": "Número de reproducciones"}, "crossArrow": {"addToCross": "Agregar a la flecha cruzada", "arrowProgram": "Programa de flecha", "greenArrow": "Flecha verde", "redArrow": "Cruz roja", "closeScreen": "Apagar pantalla", "curProgram": "Programa actual", "remove": "Eliminar", "batchRemove": "Eliminación por lotes"}, "manual": {"operationDocument": "Documento de operación", "videoTutorial": "Tutorial en video", "resourceManagement": "Gestión de recursos", "manualAndTutorial": "Manual y tutorial en video", "universal": "Universal", "fileTips": "Solo se puede cargar un tipo de archivo a la vez"}, "myEditor": {"bottomCenter": "Alineación inferior", "topCenter": "Alineación superior", "verticalCenter": "Alineación central"}, "employee": {"today": "Hoy", "yesterday": "Ayer", "last7Days": "Últimos 7 días", "last30Days": "Últimos 30 días", "lineChart": "Gráfico de líneas", "barChart": "Gráfico de barras", "unitPerson": "Unidad: personas", "man": "Hombre", "woman": "<PERSON><PERSON>", "tips": "Después de habilitar, se contará automáticamente el flujo de pasajeros. Después de habilitar con éxito, puede ver el dispositivo en Estadísticas de flujo de pasajeros -> Estadísticas de flujo de pasajeros V1"}}