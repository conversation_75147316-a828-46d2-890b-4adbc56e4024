{"common": {"PleaseInput": "Пожалуйста, введите", "PleaseSelect": "Пожалуйста, выберите", "Loading": "Загрузка", "personalSettings": "Личные настройки", "logOut": "Выйти", "query": "Запрос", "newlyBuild": "Создать", "add": "Добавить", "delete": "Удалить", "batchDel": "Массовое удаление", "update": "Изменить", "state": "Статус", "operation": "Действие", "tips": "Подсказка", "info": "Подробности", "deploy": "Назначить", "cancel": "Отмена", "confirm": "Подтвердить", "areYouSureExit": "Вы уверены, что хотите выйти?", "setSuccess": "Установлено", "notSet": "Не установлено", "set": "Установить", "bindingSuccess": "Привязано", "unbound": "Не привязано", "binding": "Привязать", "operationSuccessful": "Операция успешна", "createTime": "Время создания", "normal": "Нормальный", "disable": "Отключить", "delete_current_option": "Подтвердите удаление текущей опции", "original": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "use_iconfont": "Иконки используют SVG Sprite", "total": "Все", "supportedTip": "Не поддерживается", "selectDevice": "Пожалуйста, выберите устройство", "clickToEnlarge": "Нажмите для увеличения", "pictureLoadingFailed": "Не удалось загрузить изображение", "passwordError": "Ошибка пароля", "OKToRestart": "Подтвердите перезагрузку", "WaitingForRestart": "Ожидание перезагрузки...", "RestartTimeout": "Тайм-аут перезагрузки", "modifiedTime": "Время изменения", "exportData": "Экспорт данных", "submit": "Отправить", "modifyTheSuccess": "Изменение успешно", "configuration": "Конфигурация", "failure": "Ошибка", "release": "Опубликовать", "nextStep": "Следующий шаг", "selectMaterial": "Выберите материал", "notSelectedectMaterial": "Примечание: если группа не выбрана, по умолчанию экспортируются SIM-карты всех устройств", "exportingSIM": "Экспорт информации о SIM-карте", "enterCardPackage": "Введите название карты/пакета...", "note": "Примечание", "enterPackage": "Введите название пакета...", "noDetails": "Нет подробностей", "versionQuery": "Запрос версии", "CardSysterm5263": "Запрос версии требует поддержки CardSysterm 5.2.6.3 и выше", "uninstallCard": "Карта не выбрана, невозможно использовать функцию онлайн-удаления", "packageName": "Название пакета", "versionNumber": "Номер версии", "versionIdentifiers": "Идентификаторы версии", "wrong": "Ошибка состояния подключения карты, обновление отменено!", "upgradeWrong": "Ошибка состояния подключения всех выбранных карт, проверьте и повторите попытку!", "cardsNotReturn": "Некоторые карты не поддерживают возврат прогресса, тайм-аут запроса, обновление завершено!", "updateComplete": "Обновление завершено!", "restartCards": "Вы уверены, что хотите перезагрузить эти карты?", "addTiming": "Пожалуйста, добавьте таймер", "Monday": "Понедельник", "Tuesday": "Вторник", "Wednesday": "Среда", "Thursday": "Четверг", "Friday": "Пятница", "Saturday": "Суббота", "Sunday": "Воскресенье", "brightnessClears": "Ручная настройка яркости очищает весь таймер яркости и чувствительность", "fullBrightness": "Максимальная яркость", "screenDuring": "Примечание: экран включен в течение установленного времени", "removeTiming": "Очистить таймер", "clearSuccess": "Очистка успешна", "notEnteredNotModified": "Незаполненные поля не изменяются, для версий Conn_v11.1.1.1[261]-AIPS-release-2&4 и выше незаполненные поля устанавливаются как пустые", "Changing": "Изменение ID компании или адреса realTime приведет к отключению карты, вы уверены, что хотите изменить?", "networkNTP": "Автоматическая синхронизация времени требует сети и адреса NTP", "Settings": "При выборе режима синхронизации незаполненные поля считаются пустыми", "SynchronousMode": "Режим синхронизации", "zoneEmpty": "Выбранная временная зона не может быть пустой!", "synchronousState": "Установка временного интервала синхронизации/асинхронизации в синхронное состояние!", "beEmpty": "Ширина и высота не могут быть меньше или равны 0 и не могут быть пустыми!", "ModifyFailure": "Ошибка изменения", "programmeDetails": "Подробности задачи программы", "showWidth": "Ширина программы", "showHigh": "Высота программы", "notOnPlatform": "Ошибка запроса, текущая программа не опубликована на этой платформе", "allIntervals": "Если временной интервал не выбран после включения таймера, считается, что выбраны все интервалы", "notSelected": "Не выбрано", "true": "Да", "false": "Нет", "name": "Название", "custom": "Пользовательский", "MaximumNumberOfWords200": "Лимит до 200 слов", "exportingSIMTips": "В текущей группе нет терминалов, рекомендуется выбрать другую группу", "language": "Язык", "copy": "Копировать", "save": "Сохранить", "saveAndExit": "Сохранить и выйти", "noMoreData": "Нет больше данных", "enable": "Включить", "NotEnabled": "Не включено", "AssignUsers": "Назначить пользователей", "manual": "Руководство пользователя", "addMedia": "+ Добавить медиа", "reviewCurrentUsers": "Пожалуйста, свяжитесь с нашим персоналом для проверки текущих пользователей", "selectGroup": "Выбрать группу", "selectingGroup": "Текущая группа", "unclassified": "Не классифицировано", "frontStep": "Предыдущий шаг", "search": "Поиск", "CAT1": "CAT1", "DaHuaCamera": "Камера Dahua", "GBCamera": "Камера GB"}, "screen": {"simInformation": "Информация о SIM-карте", "networkState": "Сетевое государство", "series": "Серийный номер", "countries": "Страна", "operationName": "Название операции", "unknownState": "Неизвестное состояние", "noCard": "Нет карты", "PIN": "Заблокировано, требуется PIN-код пользователя", "PUK": "Заблокировано, требуется PUK-код пользователя", "PIN2": "Заблокировано, требуется сетевой PIN-код", "readyState": "Готово", "InvalidState": "Недействительное состояние", "subscribe": "Подписка", "choose": "Выбрать", "supportZip": "Файл версии поддерживает только формат Zip!", "selectFile": "Выбрать файл", "releaseTime": "Время выпуска", "versionInformation": "Информация о версии", "testVersion": "Тестовая версия без деталей версии!", "hardwareVersion": "Аппаратные параметры без деталей версии!", "officialRelease": "Официальный выпуск версии", "testRelease": "Тестовый выпуск версии", "hardwareRelease": "Выпуск аппаратных параметров", "sizeMore": "Размер превышает", "OnlyForZIPType!": "Только для типа ZIP!", "uploadEmpty": "Загружаемый файл не может быть пустым!", "uploadFile": "Загрузить файл", "fileTips": "Поддерживаются только файлы форматов MP3, MP4, gif, png, jpg. MP3 и изображения не должны превышать 100 МБ, MP4 — 150 МБ", "fileTips1": "Поддерживаются только файлы формата MP3, размер не должен превышать 20 МБ", "picture": "Изображение", "video": "Видео", "picturesOrVideos": "Тип загружаемого медиафайла должен быть изображением или видео!", "my": "Мои", "pending": "Ожидает утверждения", "pageImprovement": "Страница в разработке", "errorDetails": "Детали ошибки", "null": "Нет", "showProgress": "Прогресс программы", "sendSuccess": "Отправлено успешно", "sendSuccessOffline": "Офлайн-программа отправлена, карта автоматически отправит программу в течение 72 часов после подключения", "failedProgress": "Не удалось получить прогресс", "timesOut": "Тайм-аут получения прогресса", "selectCard": "Пожалуйста, выберите карту управления", "reviewDetails": "Детали проверки", "satelliteNumber": "Количество спутников", "autoBrightnessTable": "Таблица автоматической яркости", "senorType": "Тип датчика", "setAutoBrightnessTable": "Настроить таблицу автоматической яркости", "getAutoBrightnessTable": "Запросить таблицу автоматической яркости", "default255BrightnessTable": "Таблица яркости по умолчанию 255", "customizeBrightnessTable": "Пользовательская таблица яркости", "otherSwitch": "Другие переключатели", "customSwitchFunction": "Функция пользовательского переключателя", "programPlayDetail": "Детали воспроизведения программы", "programPlayStatistic": "Статистика воспроизведения программы", "programPlaySum": "Общее количество воспроизведений программы", "exportPlayLog": "Экспорт журнала", "log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkPort": "Сетевой порт", "clickToCheck": "Нажмите для проверки", "getPointCheckInfo": "Получить информацию о проверке точек", "imageSize": "Размер изображения", "badPointNum": "Количество плохих точек", "queryPlayerState": "Запросить состояние плеера", "playerStateRep": {"1": "Инициализация", "2": "Завершение программы по таймеру", "3": "Нет программ для воспроизведения", "4": "Удаление программы", "5": "Обработка программы", "6": "Нет информации", "7": "Программа может быть ошибочной", "8": "Эк<PERSON>ан выключен", "9": "Программа не в пределах диапазона"}, "fullColor": "Полноцветный", "monochrome": "Монохромный", "redBeadNumberPoints": "Количество плохих точек красных светодиодов", "greenBeadNumberPoints": "Количество плохих точек зеленых светодиодов", "blueBeadNumberPoints": "Количество плохих точек синих светодиодов", "totalNumberOfBadPoints": "Общее количество плохих точек", "redBadPointPosition": "Координаты плохих точек красных светодиодов", "greenBadPointPosition": "Координаты плохих точек зеленых светодиодов", "blueBadPointPosition": "Координаты плохих точек синих светодиодов", "badPointPosition": "Координаты плохих точек", "abscissa": "Абсцисса", "ordinate": "Ордината", "noBadPoint": "Нет плохих точек", "pointCheckTips": "Проверка плохих точек поддерживает только одну карту, пожалуйста, выберите заново", "receivingCard": "Карта приема", "setRecCardRelaySwitch": "Настроить переключатель реле карты приема", "getRecCardSensorData": "Получить данные датчика карты приема", "smoke": "Дым", "smokeless": "Без дыма", "openCloseDoor": "Открыть/закрыть дверь", "openDoor": "Открыть дверь", "closeDoor": "Закрыть дверь", "relaySwitch": "Переключатель реле", "levelDetection": "Обнаружение уровня", "accessHighLevel": "Обнаружен высокий уровень", "noDeviceConnected": "Устройство не подключено", "firstRoad": "Первая дорога", "secondWay": "Вторая дорога", "thirdWay": "Третья дорога", "fourthWay": "Четвертая дорога", "theFifthRoad": "Пятая дорога", "sensorDataShareTips": "Обмен данными датчиков поддерживает только одну карту, пожалуйста, выберите заново", "hour": "<PERSON><PERSON><PERSON>", "pointTable": "Таблица точек", "pointTableTips": "Пожалуйста, выполняйте операции под руководством профессионалов", "pointTableTemplate": "Шаблон таблицы точек", "pleaseUploadPointTable": "Пожалуйста, сначала настройте таблицу точек", "networkConfig": "Сетевая конфигурация", "hotspot": "Точка доступа", "wifiTip": "Включение Wi-Fi может изменить состояние сети и привести к отключению, пожалуйста, будьте осторожны", "apTip": "Обратите внимание: пожалуйста, используйте комбинационные пароли с большими буквами и цифрами, длина которых составляет от 8 до 20", "wifiList": "Запросить список Wi-Fi", "selectWifi": "Пожалуйста, выберите Wi-Fi", "singleCardOperation": "Эта функция поддерживает только одну карту"}, "sys": {"enable": "Включить", "remarks": "Пожалуйста, введите примечания", "authenticationMode": "Пожалуйста, выберите способ проверки", "emailVerification": "Проверка по электронной почте", "mobileVerification": "Проверка по номеру телефона", "verify": "Способ проверки", "notOpen": "Не включено", "email": "Электронная почта", "mobile": "Телефон", "whetherAudit": "Требуется ли проверка", "open": "Открыть", "close": "Закрыть", "companyId": "ID компании", "same": "ID компании совпадает с ID создателя", "roleEmpty": "Роль пользователя не может быть пустой", "alarmType": "Тип тревоги", "alarmTime": "Время тревоги", "Iknown": "<PERSON><PERSON>ю", "currentBrowser": "Текущий браузер не поддерживает события, отправляемые сервером", "notReminded": "После подтверждения больше не будет напоминаний", "visited": "Посещенная вами страница", "find": "Не существует", "url": "Пожалуйста, проверьте URL", "previousPage": "Вернуться на предыдущую страницу", "enterHome": "Перейти на главную страницу", "permissionOfTerminalGroups": "Контролируемые группы терминалов", "TheSuperAdminDoesNotRestrictUserGroups": "Супер-администратор не ограничивает группы пользователей", "addOrUpdateAuth": "Добавить/изменить аутентификацию", "isDefaultPassword": "Установить пароль по умолчанию"}, "login": {"dynamicCodeEntryTips": "Текущий пользователь не включил проверку по коду, пожалуйста, сначала включите способ проверки", "login": "Войти", "passwordLogin": "Вход по паролю", "username": "Имя пользователя", "password": "Пароль", "ForgetThePassword": "Забыли пароль", "newPassword": "Новый пароль", "confirmPassword": "Подтвердите пароль", "dynamicCodeEntry": "Вход по коду", "pwdNotes": "Рекомендуется использовать более 8 - значный пароль", "origin_pwd_incorrect": "Исходный код неправильный", "wrong_account_or_password": "Номер счета или опечатка пароля", "account_has_been_locked": "Номер счета заблокирован, пожалуйста, свяжитесь с администратором", "updateEmail": "Изменить почтовый ящик", "bindEmail": "Связывающий почтовый ящик", "updateMobile": "Измени номер телефона.", "bindMobile": "Привязанный номер телефона", "sliderRight": "Нажмите на кнопку для подтверждения", "loading": "Загрузка.", "passwordMore8": "Длина пароля должна быть больше 8 и состоит из заглавной, маленькой буквы и цифр", "user4To17": "Длина имени пользователя должна быть больше 4, меньше 17 и состоит из чисел или букв", "clickCodeMailbox": "Нажмите на код доступа, и информация будет отправлена в защищенный почтовый ящик", "clickCodePhone": "Нажмите, чтобы получить код подтверждения, SMS будет отправлено на защищенный телефон", "authenticationSuccessful": "Аутентификация успешна", "securityVerification": "Пожалуйста, сначала пройдите проверку безопасности", "enterVerificationCode": "Пожалуйста, введите код подтверждения", "UserNameEmailMobile": "Имя пользователя/Email/Телефон", "ScopeOfAuthority": "Область полномочий", "superAdministrator": "Супер администратор", "PermissionDetails": "Детали разрешений", "pleaseContactTheAdministrator": "Пожалуйста, свяжитесь с администратором"}, "register": {"register": "Регистрация", "mobile": "Номер телефона", "mailbox": "Электронная почта", "code": "Код подтверждения", "getCode": "Получить код подтверждения", "remind": "Рекомендуется использовать WeChat для сканирования QR-кода и регистрации в один клик", "back": "Вернуться к входу", "prependResend": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "appendResend": " с повторная отправка", "personalInformation": "Личная информация", "complete": "Завершить", "recommendedRegister": "Рекомендуется использовать WeChat для сканирования QR-кода и регистрации в один клик", "enterPassword": "Пожалуйста, введите пароль еще раз", "companyId": "ID компании, привязка карты управления к ID компании, после подтверждения нельзя изменить", "companyName": "Название компании", "companyAddress": "Адрес компании", "companyPhone": "Телефон компании", "readAIPSAgreement": "Пожалуйста, сначала прочитайте пользовательское соглашение AIPS", "readAccepted": "Я прочитал и принимаю", "AIPSAgreement": "Пользовательское соглашение AIPS", "registeredSuccessfully": "Регистрация успешна", "clickJumpOr": "Нажмите для перехода или", "loginDisplayed": "секунд до автоматического перехода на страницу входа", "readAcceptAIPSAgreement": "Пожалуйста, прочитайте и примите пользовательское соглашение AIPS", "youAreRight": "Вы работаете с", "resettingVerification": "Для сброса пароля сначала пройдите проверку безопасности", "switchingAuthentication": "Смена способа проверки", "pleaseSet": "Пожалуйста, установите", "passwordSecurity": "Новые пароли рекомендуют использовать комбинацию цифр, букв, символов и повысить уровень безопасности пароля", "passwordChangedSuccessfully": "Код изменен.", "verified": "аутентификация", "idCardNumber": "Идентификационный номер", "companyLicense": "Загрузка фото корпоративной лицензии на ведение бизнеса в течение года", "cachet": "Загрузи фото 'санкционированное доказательство' на корпоративный герб", "idCardFront": "Загрузка позитивных фото", "idCardReverse": "Загрузи фото с обратной стороны", "pleaseReadAndCheckAIPS": "Прочитайте и поставьте галочку в протоколе пользователя AIPS.", "personal": "Личный", "company": "Компания", "CertifiProgress": "Прогресс сертификации", "waitForAdminAuth": "Ожидание подтверждения администратором", "DownloadLicenseCertificate": "Скачать 'мандат'"}, "ChangePWD": {"ChangePassword": "Измени код.", "remind": "Только пользователь, связанный с номером телефона или почтовым ящиком, может вернуть пароль"}, "nav": {"首页": "Главная страница", "智慧屏幕": "Интеллектуальный экран", "智慧广播": "Интеллектуальное вещание", "气象环境": "Метеорологическое окружение", "智慧监控": "Интеллектуальный мониторинг", "客流统计": "Статистика потока посетителей", "客流统计V1": "Статистика потока посетителей V1", "系统管理": "Управление системой", "设备管理": "Управление оборудованием", "节目管理": "Управление программами", "媒体库": "Медиатека", "日志管理": "Управление журналами", "远程控制日志": "Журнал удаленного управления", "播放日志": "Журнал воспроизведения", "用户管理": "Управление пользователями", "菜单管理": "Управление меню", "角色管理": "Управление ролями", "用户日志": "<PERSON><PERSON><PERSON><PERSON>л пользователя", "登录日志": "<PERSON><PERSON><PERSON><PERSON><PERSON> входа", "系统日志": "Системный журнал", "设备状态": "Состояние оборудования", "广播任务": "Задачи вещания", "分组管理": "Управление группами", "审批管理": "Управление одобрением", "公告管理": "Управление объявлениями", "终端列表": "Список терминалов", "智慧物联": "Интеллектуальная Интернет-вещей", "智慧照明": "Интеллектуальное освещение", "CAT1照明": "Освещение CAT1", "控制卡照明": "Освещение с использованием контроллерных карт", "电能管理": "Управление электрической энергией", "视频监控": "Видеонаблюдение", "付费服务": "Платные услуги", "订购服务": "Заказ услуг", "订单": "Зак<PERSON>з", "SIP账号管理": "Управление SIP-аккаунтами", "监控回放": "Просмотр записи мониторинга", "人群聚集": "Накопление людей", "订单管理": "Управление заказами", "报警管理": "Управление тревогами", "报警记录": "Записи тревог", "通话记录": "Записи разговоров", "智慧交通": "Интеллектуальное транспортное обеспечение", "交通信息": "Транспортная информация", "雷达测速": "Радарное измерение скорости", "WIFI AC": "WIFI AC", "概览": "Обзор", "AC管理": "Управление AC", "密码管理": "Управление паролями", "认证审核": "Аудит сертификации", "通知策略": "Стратегия уведомлений", "通知日志": "Журнал уведомлений", "十字箭头": "Крестик-стрелка", "设备白名单": "Белый список оборудования", "摄像头监控": "Камера наблюдения"}, "validate": {"account_cannot_empty": "Аккаунт не может быть пустым", "password_cannot_empty": "Пароль не может быть пустым", "confirm_password_cannot_empty": "Подтверждение пароля не может быть пустым", "new_pwd_cannot_empty": "Новый пароль не может быть пустым", "email_cannot_empty": "Электронная почта не может быть пустой", "mobile_cannot_empty": "Номер телефона не может быть пустым", "code_cannot_empty": "Код подтверждения не может быть пустым", "roleName_cannot_empty": "Название роли не может быть пустым", "menuURL_cannot_empty": "URL-адрес меню не может быть пустым", "superior_menu_cannot_empty": "Родительское меню не может быть пустым", "menu_name_cannot_empty": "Название меню не может быть пустым", "group_name_cannot_empty": "Название группы не может быть пустым", "group_type_cannot_empty": "Тип группы не может быть пустым", "audit_name_cannot_empty": "Название проверки не может быть пустым", "resource_type_cannot_empty": "Тип ресурса не может быть пустым", "status_cannot_be_empty": "Статус не может быть пустым", "approval_comments_cannot_blank": "Комментарий к утверждению не может быть пустым", "program_name_cannot_empty": "Название программы не может быть пустым", "the_new_password_is_inconsistent": "Подтверждение нового пароля не совпадает с новым паролем", "the_password_is_inconsistent": "Подтверждение пароля не совпадает с паролем", "incorrect_email_format": "Некорректный формат электронной почты", "code_format": "Введите шесть символов, пожалуйста", "mobile_format": "Неверный формат номера телефона", "mobile_code_empty": "Код подтверждения по смс не может быть пустым", "email_code_empty": "Код подтверждения по электронной почте не может быть пустым", "company_id_empty": "ID компании не может быть пустым", "not_empty": "Не может быть пустым", "alarmAddress_not_empty": "Адрес оповещения или номер вызова не могут быть пустыми", "alias_cannot_empty": "Псевдоним не может быть пустым", "company_name_cannot_empty": "Название компании не может быть пустым", "company_address_cannot_empty": "Адрес компании не может быть пустым", "company_phone_number_cannot_empty": "Телефон компании не может быть пустым", "id_card_number_cannot_empty": "Номер паспорта не может быть пустым", "id_card_number_format_wrong": "Неверный формат номера паспорта", "validateTip": "Для безопасности вашего аккаунта необходимо связать метод подтверждения."}, "role": {"role": "Роль", "roleName": "Название роли", "remark": "Примечание", "authorization": "Авторизация", "subAdmin": "Подадминистратор", "normalUser": "Обычный пользователь"}, "menu": {"name": "Название", "parentName": "Родительское меню", "icon": "Иконка", "type": "Тип", "orderNum": "Номер сортировки", "url": "URL-адрес меню", "perms": "Идентификатор авторизации", "mainMenu": "Главное меню", "parentMenuName": "Родительское меню", "permsTips": "Несколько значений разделяются запятой, например: user:list,user:create", "menu": "<PERSON>е<PERSON><PERSON>", "DirectoryMenu": "Меню каталога", "button": "Кнопка", "HomeDirectoryMenu": "Главное меню каталога"}, "log": {"user_name_user_action": "Имя пользователя / Действие пользователя", "user_action": "Действие пользователя", "request_method": "Метод запроса", "request_parameters": "Параметры запроса", "execution_time": "Время выполнения (миллисекунды)", "ip_address": "IP-адрес", "commandId": "ID команды", "response_result": "Результат ответа", "schedule": "Прогресс", "ReasonForFailure": "Причина неудачи", "RequestTimedOut": "Истекло время ожидания запроса", "requestSucceeded": "Запрос выполнен успешно", "connectionDoesNotExist": "Подключение не существует", "Disconnect": "Подключение разорвано", "connectionClosed": "Подключение закрыто", "requestException": "Ошибка запроса"}, "group": {"name": "Название группы", "type": "Тип группы", "addingAGroup": "Добавить группу", "pleaseEnterAGroupName": "Пожалуйста, введите название группы", "addingSubgroup": "Добавить подгруппу", "pleaseDeleteTheSubgroupsFirst": "Пожалуйста, сначала удалите подгруппы"}, "cardDevice": {"deviceName": "Название устройства", "online": "В сети", "networkType": "Тип сети", "resolvingPower": "Разрешение", "programTask": "Задача программы", "broadcastTask": "Задача вещания", "screenStatus": "Состояние экрана", "lastOffline": "Последний выход из сети", "queryTerminalInfo": "Запросить информацию о терминале", "selectedCard": "Выбранная карта", "brightness": "Яркость", "volume": "Громкость", "locked": "Заблокировано", "terminalInfoFirst": "Пожалуйста, получите информацию о терминале сначала", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "synchronous": "Синхронный", "asynchronous": "Асинхронный", "temperature": "Температура", "number": "Номер", "NoSim": "Нет информации о SIM-карте", "fireWare": "Версия прошивки"}, "operation": {"settingMode": "Способ настройки", "connectionLog": "<PERSON>у<PERSON><PERSON>л подключений", "LEDscreen": "LED-экран", "screenshot": "Скриншот экрана", "liveVideo": "Видеопрямые эфиры", "screenSwitch": "Переключатель экрана", "timingSwitch": "Таймер включения/выключения", "screenBrightness": "Яркость экрана", "autoBrightness": "Автоматическая яркость", "timingBrightness": "Таймер яркости", "volumeControl": "Управление громкостью", "timingConfig": "Конфигурация синхронизации времени", "connConfig": "Конфигурация подключения", "syncAndAsyncConfig": "Конфигурация синхронного и асинхронного режима", "alarmSwitch": "Переключатель тревоги", "onlineUpdate": "Онлайн-обновление", "restartSys": "Перезагрузка системы", "backgroundPlayback": "Фон плеера", "backupScreenParam": "Резервное копирование параметров экрана", "restoreScreenParam": "Восстановление параметров экрана", "hardwareStatus": "Параметры оборудования", "manualconfigurationorquery": "Ручная настройка/запрос", "scheduledconfigurationorquery": "Планируемая настройка", "thealias": "Псевдоним", "webServerAddress": "Адрес WEB-сервера", "thecompany": "Компания", "realtimeaddress": "Адрес Realtime", "remove": "Удалить", "volumeset": "Массовая настройка", "batchquery": "Массовый запрос", "group": "Группа", "exportSIMInfo": "Экспорт информации о SIM-карте", "clearProgram": "Очистка программ", "clearTask": "Очистка задач", "callAddress": "Конфигурация адреса разговора", "alarmConfig": "Конфигурация тревоги", "clearBroadcastTask": "Очистка задач вещания", "queryOrClearTiming": "Запрос или очистка таймера", "queryTiming": "Запрос таймера", "screenControl": "Управление экраном", "broadcastControl": "Управление вещанием", "monitoringControl": "Управление мониторингом", "meteorologicalEnvironmentControl": "Управление метеорологическим окружением", "passengerFlowStatistics": "Статистика потока пассаж<PERSON>ров", "lightingControl": "Управление освещением", "setSIPServerAddress": "Установка адреса SIP-сервера", "getSIPServerAddress": "Запрос адреса SIP-сервера", "SIPServerAddress": "Адрес SIP-сервера", "AlarmEquipmentMacAddress": "MAC-адрес оборудования тревоги", "AlarmEquipmentIpAddress": "IP-адрес оборудования тревоги", "SetAlarmAddress": "Установка адреса тревоги", "GetAlarmAddress": "Запрос адреса тревоги", "AlarmAddress": "Адрес тревоги", "CallAccount": "Аккаунт вызова", "AlarmVolume": "Громкость тревоги", "LightingLevel": "Уровень освещения", "LightingSwitch": "Переключатель освещения", "Register_SIP_account": "Регистрация аккаунта тревоги", "getGspInfo": "Запрос информации о GPS", "gpsInfo": "Информация о GPS", "recordingFile": "Аудиозапись", "fullSizeScreenshotOfAndroid": "Полноразмерный скриншот Android", "customSwitch": "Кастомный переключатель", "ThirdPartyAdvertising": "Реклама третьих лиц", "BadPointDetection": "Поиск плохих точек", "playerState": "Состояние плеера", "NumberOfCardsReceived": "Количество полученных карт", "sensorDataShare": "Обмен данными датчиков", "dataShare": "Обмен данными", "dataRefreshCycle": "Цикл обновления данных", "sharedDataKey": "Ключ обмена данными", "curFlow": "Текущий поток пасса<PERSON><PERSON><PERSON>ов", "isEnableCurFlow": "Включить текущий поток пассаж<PERSON><PERSON>ов", "flowAddress": "Адрес статистики потока", "showLocation": "Место отображения", "showPrefix": "Префикс отображения", "leftTop": "Сверху слева", "rightTop": "Сверху справа", "leftBottom": "Снизу слева", "rightBottom": "Снизу справа", "curFlowTip": "Если адрес статистики потока пуст, используется адрес по умолчанию платформы", "detectingBadPixels": "Поиск плохих точек в процессе", "uploadZip": "Загрузка Zip-архива", "versionDelete": "Удаление версии", "hdvancedConfig": "Расширенная конфигурация", "hardwareConfig": "Конфигурация оборудования", "realTimeSet": "Настройка RealTime", "networkConfig": "Сетевые настройки", "clearBroadcast": "Очистка вещания", "callVolumeControl": "Управление громкостью разговора", "queryProgramName": "Запрос названия программы", "list": "Список", "new": "Новый", "oneClickOperateScreen": "Однонажатие для выключения экрана", "systemDisplay": "Разрешение системы", "checkAddress": "Конфигурация адреса проверки"}, "tips": {"brightness": "Примечание: яркость должна быть в диапазоне от 1 до 255", "volume": "Примечание: громкость должна быть в диапазоне от 0 до 15", "alarmVolume": "Примечание: громкость должна быть в диапазоне от 1 до 9", "liveVideo": "Поддерживаются протоколы rtmp и rtsp. Пожалуйста, установите live.", "liveVideo1": "OFF - выключить прямую трансляцию, ON - включить прямую трансляцию", "liveVideo2": "Тестовый адрес", "screenSwitch": "Примечание: чтобы очистить таймер и повторить операцию включения/выключения экрана, просто выполните соответствующую операцию снова.", "screenTiming": "Примечание: функция запроса таймера включения/выключения экрана поддерживается на версиях conn10.0.5T и выше.", "autoBrightness": "Примечание: эта функция поддерживается начиная с версии CardSystem-v3.6.0. Чувствительность должна быть в диапазоне от 0 до 100.", "autoBrightness1": "Яркость автоматически регулируется в соответствии с данными датчика (поддерживается на версиях conn10.0.5T и выше).", "autoBrightness2": "Для карт с максимальной яркостью 64 минимальная яркость может быть установлена на 1% или другое подходящее значение. Для карт с максимальной яркостью 255 минимальная яркость должна быть установлена на 36% или выше, иначе яркость будет недостаточной.", "timingBrightness": "Примечание: в установленное время яркость будет соответствовать заданному значению, а за пределами этого времени - значению по умолчанию. Например, если значение по умолчанию установлено на 80%, а заданное значение - на 20%, и временной диапазон - с 8:00 до 17:00, то в этот период яркость будет 20%, а в остальное время - 80%.", "manualconfigurationorquery1": "Применяется только к картам серии M70 и M80.", "manualconfigurationorquery2": "В заданном диапазоне дат будет использоваться режим синхронизации. Применяется только к картам серии M70 и M80 на версиях cardSystem 5.2.5.6 - 8 и выше.", "widthheighterror": "Ширина и высота экрана не могут быть меньше 0 пикселей.", "noTenteredNotModified": "Если поле не заполнено, то параметр не изменяется.", "approval": "Проверка осуществляется в соответствии с определенным процессом. Чем меньше номер в порядке сортировки процесса, тем раньше он выполняется.", "advancedParameter": "Примечание: интерфейс настройки расширенных параметров поддерживается на версиях conn10.0.5T и выше.", "cardSelected": "Выбранные номера карт", "cardNameSelected": "Выбранные терминалы", "numberEmpty": "Количество выбранных номеров карт не может быть пустым.", "progressBar": "Примечание: полоса загрузки обновления показывает процесс обновления, но фактический результат обновления определяется по фактическому состоянию карты!", "upgradeFeatureSelectCard": "Не выбрана ни одна карта, невозможно использовать функцию онлайн-обновления!", "UninstalledSuccessfully": "Статус: удаление выполнено успешно!", "uninstallFeatureSelectCard": "Не выбрана ни одна карта, невозможно использовать функцию онлайн-удаления!", "SelectUninstall": "Пожалуйста, выберите компоненты для удаления.", "selectedNotExist": "Выбранные метки ошибочны, они не существуют.", "backgroundTips": "Разрешение фонового изображения должно совпадать с разрешением экрана, иначе будет ошибка.", "releaseTips": "Программа будет отправлена оффлайн на неактивные карты. Система сообщит об успешной отправке, но не отобразит полосу загрузки. Контроллер автоматически отправит программу в течение 72 часов после подключения.", "releaseTips1": "При включении журнала проигрывателя после успешной отправки программы будет сохраняться журнал проигрывания каждой карты.", "releaseTips2": "После включения журнала проигрывателя контроллер будет отправлять журналы на платформу с установленным интервалом времени, если на карте есть программы.", "SIPAddress": "Примечание: если адрес сервера SIP не был установлен, то перед использованием функций разговора и оповещения необходимо установить адрес SIP. После установки адреса перезапустите контроллер.", "SIPAddress1": "Если поле ввода пустое, будет использован текущий сервер платформы в качестве сервера SIP, или можно указать собственный адрес сервера SIP.", "alarmConfig": "Рекомендуется установить адрес платформы в качестве адреса оповещения.", "CustomCanUseNetworkAddress": "При настройке можно использовать сетевой адрес.", "networkAddressWhenCustomizing": "При настройке введите сетевой адрес.", "broadcastTask": "Примечание: если не настроена задача по таймеру, то после перезапуска устройства задача вещания будет автоматически удалена и не сохранится!", "SIPTips": "Этот аккаунт можно использовать для SIP-разговоров с контроллером. Если аккаунт уже зарегистрирован, можно только изменить его.", "broadcastTaskRelease": "Примечание: перед публикацией задачи вещания убедитесь, что функция разговора не используется.", "ModifyTerminal": "Подсказка: чтобы изменить псевдоним терминала, перейдите в раздел 'Интеллектуальная Интернет-вещей', переключитесь в режим списка и нажмите 'Изменить'.", "senorBrightnessTable": "Эта функция требует версию systemCore ******** и выше. Шаги использования: сначала установите значение чувствительности датчика, затем загрузите файл BrightnessTable.xlsx из EasyBoard для изменения значений. Вы можете изменить значения в файле, но не изменяйте его формат, иначе операция завершится неудачно! При загрузке файла можно выбрать только карты с одним и тем же типом датчика, иначе операция завершится неудачно!", "customSwitchTip": "Внимание: при включении этого переключателя будет использоваться настраиваемый переключатель экрана, а исходный переключатель экрана станет недействительным. При выключении этого переключателя исходный переключатель экрана снова станет активным. Если у вас есть вопросы, пожалуйста, уточните у соответствующих сотрудников!", "configAdTip": "Вы уверены, что хотите подключить рекламу от третьих сторон? Наша платформа не гарантирует законность и соответствие содержания, предоставляемого третьими сторонами. Будьте осторожны при выполнении этой операции.", "configAdTip1": "Включить?", "configAdTip2": "Вы уверены, что хотите предоставить доступ к контроллеру третьей стороне?", "configAdTip3": "Для использования этой функции необходимо обновить соответствующий APK. Подробности уточните у наших сотрудников!!!", "groupTip": "После отмены будут отображены все устройства.", "passwordIsWeak": "Ваш пароль слишком слаб, пожал<PERSON>йста, измени его. Пароль содержит по крайней мере одну заглавную букву на английском языке, одну маленькую букву на английском языке, одну цифру и имеет длину не менее девяти бит.", "authTip": "Персонал будет проверять в течение 2-3 рабочих дней, после принятия которых платформа будет доступна для нормального использования.", "pointCheckTip": "После тестирования, пожалуйста, подождите немного, чтобы убедиться, что тест завершен, и получить нужную информацию для обнаружения плохих точек", "pointCheckCard": "Пожалуйста, свяжитесь с техниками моего отдела", "playLogsExportTip": "При вывозе журнала, если дата не выбрана, выводится журнал дня, в противном случае все записи в указанной дате.", "oneClickOperateScreenTip": "Эта операция отключит все онлайн-экраны"}, "file": {"name": "Название файла", "type": "Тип файла", "status": "Статус файла", "size": "Размер файла", "UploadProgress": "Прогресс загрузки", "download": "Скачать", "thumbnail": "Миниатюра", "checkPending": "Ожидает проверки", "approved": "Проверено", "auditFailed": "Проверка не пройдена", "under_review": "На проверке", "examine": "Проверить", "attachment": "Пожалуйста, выберите вложение", "attachment1": "Нажмите или перетащите сюда для загрузки файла", "auditTime": "Время проверки", "file": "<PERSON>а<PERSON><PERSON>", "ApprovalComments": "Комментарий к утверждению", "upload": "Загрузить", "update": "Обновить", "toView": "Просмотреть", "WithoutPermission": "Нет прав доступа", "uninstall": "Удалить", "SerialNumber": "Серийный номер", "OnlineUpdate": "Онлайн-обновление", "TheSize": "Размер", "VersionLog": "<PERSON><PERSON><PERSON><PERSON><PERSON> версий", "LogDetails": "Подробности журнала", "Onlineupgrade": "Онлайн-обновление в процессе...", "Waitingupdates": "Ожидание обновления...", "Allcards": "Обновление не удалось для всех карт, участвующих в операции. Пожалуйста, проверьте проблему.", "DownloadComplete": "Загрузка завершена. Распакуйте и обновите.", "NotSupported": "Показ прогресса не поддерживается. Загрузка...", "UpdateSuccessful": "Обновление успешно завершено!", "UpdateFailed": "Обновление не удалось!", "ReadyDownload": "Подготовка к загрузке...", "ConnectionFailed": "Соединение не удалось. Проверьте устройство!", "ThreeSeconds": "Обновление завершено. Окно обновления закроется через три секунды.", "YouCanOnlyUploadUpTo5Files": "Максимальное количество загружаемых файлов - 5.", "audio": "Аудио", "fileOverSize": "Файл превышает допустимый размер", "fileLimit": "Файл поддерживает только формат docx", "fileVersion": "Версия файла", "fileLimitPdfAndVideo": "Файлы поддерживают только pdf и mp4"}, "card": {"cardId": "Серийный номер", "setTiming": "Установить таймер", "getTiming": "Запросить таймер", "noTiming": "Без таймера", "timing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notSpecified": "Не указано", "dateType": "Тип даты", "DateRange": "Диа<PERSON>азон дат", "startDate": "Начальная дата", "endDate": "Конечная дата", "timeType": "Тип времени", "timeFrame": "Временной диапазон", "startTime": "Время начала", "endTime": "Время окончания", "SpecifyWeek": "Указать день недели", "WeekRange": "Диа<PERSON>азон дней недели", "PleaseScheduledTask": "Пожалуйста, укажите тип задачи по таймеру", "sensitivity": "Чувствительность", "Minbrightness": "Минимальная яркость", "defaultBrightness": "Яркость по умолчанию", "timingBrightness": "Яркость по таймеру", "timedVolume": "Громкость по таймеру", "defaultVolume": "Громкость по умолчанию", "cardVoltage": "Напряжение на карте", "externalVoltage1": "Внешнее напряжение 1", "externalVoltage2": "Внешнее напряжение 2", "externalVoltage3": "Внешнее напряжение 3", "externalVoltage4": "Внешнее напряжение 4", "doorOpen": "Дверь открыта", "version": "Версия", "humidity": "Влажность", "temperature": "Температура", "smokeWarning": "Предупреждение о дыме", "querySuccessful": "Запрос выполнен успешно, показаны данные", "queryFailed": "Запрос не выполнен, невозможно показать данные", "screenWidth": "Ширина экрана (пиксели)", "screenHeight": "Высота экрана (пиксели)", "screenAlias": "Алиас экрана", "genericVersion": "Общая версия", "notChosenCard": "Карта не выбрана", "TestVersion": "Тестовая версия", "rebootNow": "Перезагрузить сейчас", "monitorTip": "Максимум можно одновременно воспроизводить 6 видеоканалов, пожалуйста, сделайте новый выбор", "picture-in-picture": "Картинка в картинке", "pictureTip": "Координаты и размеры не должны выходить за пределы разрешения экрана. Чтобы изменить настройки, необходимо сначала отключить режим 'Картинка в картинке'.", "coordinate": "Координаты", "pictureSize": "Размер изображения", "checkAddressTip": "После включения укажите адрес сервера проверки медиа-контента, который будет использоваться для проверки контента на устройстве.", "mediaContentReview": "Проверка медиа-контента", "realtimeReview": "Реaltime-проверка", "realtimeReviewTips": "Включение реального времени проверки связано с расходом трафика", "clearPrompt": "Очистить подсказку", "interval": "Интервал времени"}, "approval": {"auditName": "Название проверки", "auditType": "Тип проверки", "approvalProcess": "Процесс утверждения", "Reviewer": "Проверяющий", "order": "Порядок", "mediaResources": "Медиа-ресурсы", "ProgramType": "Тип программы", "BroadcastMediaResources": "Радиомедиа-ресурсы", "BroadcastTaskResources": "Ресурсы радиозадачи", "noAudit": "Проверка не требуется", "approved": "Проверка пройдена", "auditFailed": "Проверка не пройдена", "select_at_least_one_reviewer": "Выберите хотя бы одного проверяющего", "approver_cannot_blank": "Проверяющий не может быть пустым", "approval_order_cannot_blank": "Порядок проверки не может быть пустым", "InsufficientUsers": "Недостаточно пользователей", "clickAudit": "Однонажатие для проверки", "batchReview": "Пакетная проверка", "auditMemo": "В программе есть проблемы, пожалуйста, отрегулируйте и отправьте снова."}, "program": {"program": "Программа", "type": "Тип программы", "name": "Название программы", "ordinaryProgram": "Обычная программа", "insertProgram": "Вставочная программа", "totalSize": "Общий размер", "state": "Состояние ресурса", "ProgramList": "Список программ", "ProgramInfo": "Информация о программе", "ComponentProperties": "Свойства компонента", "ProgramProperties": "Свойства программы", "PlaybackMode": "Вот так.", "EntryEffects": "спецэффекты", "DurationMobilizationEffect": "Время выхода на поле", "AppearanceEffects": "Выход на спецэффекты", "DurationAppearanceEffect": "Продолжительность выхода на поле", "StartPlaybackTime": "Начальное время воспроизведения (s)", "DurationContinuousDisplay": "Длительность отображения (s)", "region": "Регион", "upper": "Верх", "left": "Лево", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "BasicProperties": "Базовые свойства", "background": "Фон", "pellucidity": "Прозрачность", "DisplayDackground": "Показать фон", "open": "Включить", "close": "Выключить", "BackgroundColor": "Цвет фона", "DisplayHourScale": "Показывать часовые деления", "HourScaleColor": "Цвет часовых делений", "ShowMinuteScale": "Показывать минутные деления", "MinuteScaleColor": "Цвет минутных делений", "ScaleStyle": "Стиль делений", "IntegerScaleDigitalDisplay": "Отображать цифры на целых часовых делениях", "PointerStyle": "Стиль стрелок", "ClockPointerColor": "Цвет часовой стрелки", "MinutePointerColor": "Цвет минутной стрелки", "SecondPointerColor": "Цвет секундной стрелки", "DisplaySecondHand": "Показывать секундную стрелку", "up": "Ввер<PERSON>", "down": "<PERSON><PERSON><PERSON><PERSON>", "play": "Воспроизвести", "times": "раз", "PleaseEnterContent": "Пожалуйста, введите содержимое", "text": "Текст", "DigitalClock": "Цифровые часы", "analogClock": "Аналоговые часы", "EnvironmentalMonitoring": "Мониторинг окружающей среды", "weather": "Погода", "Multi-materialWindow": "Окно с несколькими материалами", "html": "Веб - страница", "weburl": "Адрес веб - страницы", "enterTime": "Пожалуйста, введите время", "Multi-material": "Много материа<PERSON>ов", "empty": "Очистить", "oneLevelUp": "На один уровень вверх", "oneLevelDown": "На один уровень вниз", "layerOnTop": "Разместить слой наверху", "bottomLayer": "Разместить слой внизу", "FullScreen": "Заполнить экран целиком", "pageProperties": "Свойства страницы", "effectiveDate": "Дата действия", "PlayProperties": "Свойства воспроизведения", "planSchedule": "Плановый график", "sun": "Вс", "one": "Пн", "two": "Вт", "three": "Ср", "four": "Чт", "five": "Пт", "six": "Сб", "clockProperties": "Свойства часов", "PleaseSelectATimeZone": "Пожалуйста, выберите часовой пояс", "year": "Год", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "day": "День", "hour": "<PERSON><PERSON><PERSON>", "Minute": "Минута", "Second": "Секунда", "Week": "Неделя", "AM": "До полудня", "PM": "После полудня", "fourYears": "Четырехзначный год", "12HourClock": "12-часовой формат", "morningAfternoon": "До полудня/После полудня", "style": "Стиль", "dateStyle": "Стиль даты", "timeStyle": "Стиль времени", "displayStyle": "Стиль отображения", "singleLine": "Однострочный", "Multi-line": "Многострочный", "fontSettings": "Настройки шрифта", "fontSize": "Размер шрифта", "fontColor": "Цвет шрифта", "PlayTime": "Время воспроизведения", "specialEffects": "Спецэффекты", "specificFrequency": "Частота спецэффектов", "blink": "Мигание", "breathe": "Дыхание", "MonitoringProperties": "Свойства мониторинга", "compensate": "Компенсация", "windSpeed": "Скорость ветра", "windDirection": "Направление ветра", "noise": "<PERSON>ум", "atmosphericPressure": "Атмосферное давление", "rainfall": "Осадки", "radiation": "Радиация", "lightIntensity": "Интенсивность света", "DisplayMode": "Режим отображения", "stayLeft": "Прижать к левому краю", "Centered": "По центру", "KeepRight": "Прижать к правому краю", "singleLineScroll": "Однострочный скроллинг", "speed": "Скорость", "ms/pixel": "мс/пиксель", "refreshCycle": "Цикл обновления", "minute": "Минута", "fileProperties": "Свойства файла", "Multi-MaterialBasicProperties": "Базовые свойства много素材", "mediaList": "Список медиа", "SelectedMaterialInformation": "Информация о выбранном материале", "HourMarkColor": "Цвет часовых делений", "minuteScaleColor": "Цвет минутных делений", "hourHandColor": "Цвет часовой стрелки", "minuteHandColor": "Цвет минутной стрелки", "pointerColor": "Цвет стрелки", "backgroundColor": "Цвет фона", "static": "Статичный", "scroll": "Прокрутка", "turnPages": "Перелистывание страниц", "total": "Всего", "Page": "Страница", "preview": "Предпросмотр", "stopPreview": "Остановить предварительный просмотр", "TextEditor": "Редактор текста", "province": "Провинция", "Multi-material_text": "Пожалуйста, добавьте медиа на правой стороне. Можно добавить несколько разных медиа. LED-дисплей будет воспроизводить их в порядке списка.", "streaming": "Стриминг", "direction": "Направление скроллинга", "ToTheLeft": "Влево", "upward": "Ввер<PERSON>", "ToTheRight": "Вправо", "addText": "Добавить текст", "liveStreamAddress": "Адрес потокового вещания", "deviceAddress": "Адрес устройства", "deviceAddrTip": "По умолчанию используется текущий адрес (https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey). Не изменяйте его без необходимости.", "deviceKey": "<PERSON><PERSON><PERSON>ч устройства", "deviceKeyTip": "Пользователь платформы должен ввести указанный ключ устройства. После отправки программы на экран будут отображаться данные о потоке посетителей указанного устройства.", "CustomHTML": "Пользовательский HTML", "CustomHTMLTip1": "Это серийный номер устройства", "CustomHTMLTip2": "Это время", "CustomHTMLTip3": "Это общее количество входов сегодня", "CustomHTMLTip4": "Это количество входов за этот час", "CustomHTMLTip5": "Это общее количество входов за историю", "CustomHTMLTip6": "Это общее количество выходов сегодня", "CustomHTMLTip7": "Это количество выходов за этот час", "CustomHTMLTip8": "Это общее количество выходов за историю", "flowStatistics": "Статистика потока посетителей", "weatherTip1": "Текущая температура", "weatherTip2": "AQI (Индекс качества воздуха)", "weatherTip3": "Текущая дата (включая текущую температуру)", "weatherTip4": "Погода сегодня", "weatherTip5": "Максимальная температура сегодня", "weatherTip6": "Минимальная температура сегодня", "weatherTip7": "Направление ветра сегодня", "weatherTip8": "Сила ветра сегодня", "weatherTip9": "Картинка погоды на день, формат: img-ширина-высота", "weatherTip10": "В вышеуказанных %{} 'yesterday' означает вчера, 'arr.0' - сегодня, '1' - завтра, '2' - послезавтра, '3' - послепослезавтра, '4' - послепослепослезавтра", "timeType": "Тип времени", "timeTypeTip": "Изменение типа изменит пользовательский HTML", "HDMITypeDescription": "Описание типа HDMI", "HDMIDescription1": "1. Программы типа HDMI в настоящее время поддерживаются только для серии m70. После успешной отправки проверьте фактическое отображение на дисплее. Фактическое отображение определяется дисплеем.", "HDMIDescription2": "2. Если фактический результат отображения неверен, сначала проверьте подключение HDMI - кабеля или правильность версии соответствующего программного обеспечения. После успешной проверки попробуйте отправить снова. Если текущая управляющая карта находится в режиме HDMI - вложенной картинки, сначала установите ее в синхронный режим, а затем можно переустановить. Конкретный план использования обратитесь к нашим техническим специалистам.", "text-to-speech": "Преобразование текста в речь", "addProgramTips": "После успешной отправки обычной программы она заменяет предыдущую программу. После успешной отправки вставочной программы она не заменяет предыдущую программу, а воспроизводится только в период вставки.", "enablePlayerLog": "Включить журнал проигрывателя?", "playLog": "Журнал проигрывания", "timeInterval": "Временной интервал (минуты)", "discount": "<PERSON><PERSON><PERSON>на каждого сегмента", "isDiscount": "Применять скидку?", "discountText": "<PERSON><PERSON>ина каждого разделения, несколько значений разделяются запятой. Например: 256,256,128", "segmentation": "Разделение", "PleaseEnterDiscountWidth": "Пожалуйста, введите ширину скидки", "PleaseEnterTheCorrectContentFormat": "Пожалуйста, введите правильный формат содержимого", "multi-picture": "Много изображений", "PleaseSelectPictureVideoSplit": "Пожалуйста, выберите изображение или видео для разделения", "totalWidthDiscountCannotWidth": "Общая длина скидки не может быть больше размера материала", "sensorsShareData": "Общие данные датчиков", "broadcastSort": "Сортировка вещания", "horizontal": "Горизонтальная строка", "verticalRow": "Вертикальная строка", "discountMode": "Режим скидки", "level": "Горизонтальный", "vertical": "Вертикальный", "negativeIon": "Отрицательные ионы", "zoomIn": "Увеличить", "zoomOut": "Уменьшить", "materialCycle": "<PERSON>икл мате<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshSec": "Интервал обновления", "zoom": "Масш<PERSON><PERSON><PERSON>", "offset": "Смещение", "scale": "Растяжение"}, "setTime": {"timeZone": "Часовой пояс/Время", "y60Channels": "Примечание: эта функция требует поддержки версии CardSystem_v5.2.6.3 и выше!", "theTimeZone": "Часовой пояс", "setUpThe": "Настроить", "query": "Запросить", "computerTime": "Калибровать часы по времени компьютера", "ledTime": "Запросить текущее время устройства LED", "queryFails": "Запрос не удался!", "versionCardSystem": "Пожалуйста, проверьте версию CardSystem!", "deviceTimeZone": "Часовой пояс устройства", "setupFailed": "Настройка не удалась", "setupSuccess": "Настройка прошла успешно", "connectTo485": "Подключить к 485?", "calibrationFailure": "Калибровка не удалась", "successfulCalibration": "Калибровка прошла успешно", "querySuccessful": "Запрос выполнен успешно", "synchronizationSettings": "Настройки синхронизации", "model": "Режим", "masterSlave": "Мастер/Слейв", "IdentificationCode": "Код идентификации", "timeOffset": "Временное смещение (миллисекунды)", "screenBrightness": "Яркость экрана", "volume": "Громкость", "screenSwitch": "Переключатель экрана", "synchronizationInterval": "Интервал синхронизации", "lastSynchronousTime": "Последнее время синхронизации", "minTime": "(минуты/раз)", "main": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from": "Слейв", "masterSlaveMode": "Мастер - Слейв режим", "NTPpathNull": "При установке пустого пути NTP возникает тайм - аут подключения, но настройка успешно завершена!", "serverAddress": "Адрес NTP - сервера", "selectA": "Пожалуйста, выберите режим Мастер - Слейв", "selectATime": "Пожалуйста, выберите часовой пояс"}, "synchronous": {"unknownError": "Неизвестная ошибка", "doesNotExist": "Отчет о сетевом состоянии карты содержит ошибку. Пожалуйста, проверьте версию CardSystem.", "format": "Таймер не настроен"}, "home": {"totalNumber": "Общее количество устройств", "onlineRate": "Онлайн-коэффициент", "number": "Количество", "brightScreen": "Коэффициент включенных экранов", "operating": "Совокупный объем операций", "program": "Совокупное количество программ", "switchDate": "Дата переключения", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Announcement": "Объявление", "determined": "Требует определения", "programmeStatistics": "Статистика создания программ", "operationStatistics": "Статистика действий пользователя", "releasePeople": "Опубликовавший", "date": "Дата", "noMoreAnnouncements": "Больше нет объявлений!", "show": "Программа", "by": "Общее количество проверок", "operat": "Объем операций", "operatingSpeed": "Скорость операции (миллисекунды)", "Reviewrate": "Процент прохождения проверки", "statistics": "Статистика публикации программ", "cardNumber": "Но<PERSON>ер карты", "releaseAmount": "Общее количество публикаций", "successRate": "Процент успешных операций", "totalSuccess": "Общее количество успешных операций", "successful": "Успешно", "failure": "Неудача", "founder": "Создатель", "TotalAverageMilliseconds": "Общее среднее количество миллисекунд", "great": "Отлично", "good": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "center": "Удовлетворительно", "poor": "Плохо", "NaN": "NaN", "clickRefresh": "Нажмите для обновления", "warningNotice": "Уведомление о тревоге", "temperatureWarning": "Предупреждение о температуре", "humidityWarning": "Предупреждение о влажности", "voltageWarning": "Предупреждение о напряжении на карте", "voltage1Warning": "Предупреждение о внешнем напряжении 1", "voltage2Warning": "Предупреждение о внешнем напряжении 2", "doorOpenWarning": "Предупреждение о открытой двери", "smokeWarning": "Предупреждение о дыме", "unknownWarning": "Неизвестная тревога", "temporarilyNoData": "Временно нет данных", "showsSentProgram": "Показать карты, на которые отправляли программы", "announcementDetails": "Подробности объявления", "policeDetails": "Подробности тревоги", "untreated": "Не обработано", "haveDeal": "Обработано", "noMoreCalls": "Больше нет тревог!", "refreshSuccessful": "Обновление прошло успешно!", "DynamicLargeScreen": "Визуальная платформа умных терминалов", "piece": "шт.", "EquipmentStatistics": "Статистика оборудования", "unitPCS": "Единица измерения: шт.", "PoleStatistics": "Статистика терминалов", "ControlStatistics": "Статистика управления", "UnitTimes": "Единица измерения: раз", "TotalNumberOfPoles": "Общее количество терминалов"}, "announcement": {"titleText": "Заголовок/содержание объявления", "title": "Заголовок", "enterTitle": "Пожалуйста, введите заголовок объявления", "content": "Содержание", "enterContent": "Пожалуйста, введите содержание объявления", "also": "Можно еще ввести", "character": "Символ."}, "resetPWD": {"resetPassword": "Сбросить пароль", "accountNumber": "Введите номер аккаунта", "repairMethod": " Выберите метод восстановления", "changePassword": "Изменить пароль", "success": " Успешно", "enterResetPassword": "Пожалуйста, введите номер аккаунта для сброса пароля"}, "police": {"notOpenSettingsNull": "Если не нажать на кнопку включения, настройки считаются пустыми", "monitoringItems": "Пункты мониторинга", "hasBeenOpen": "Включено", "lower": "Нижний предел", "ceiling": "Верхний предел", "haveSmoke": "Есть дым", "openDoorAlarm": "Активировать сигнализацию при открытии двери", "turnSmokeAlarm": "Активировать дымовую сигнализацию", "checkCardSysterm": "Не удалось распознать операцию. Пожалуйста, сначала проверьте версию CardSysterm", "isNotOpened": "Ошибка в состоянии подключения карты", "sureToSetAlarmThresholds": "Вы уверены, что хотите установить пороги сигнализации?", "upperAndLowerEmpty": "Для включенных пунктов мониторинга верхний и нижний пределы не могут быть пустыми", "numEmpty": "Для включенных пунктов мониторинга верхний и нижний пределы должны быть числами", "upperGreaterLower": "Для включенных пунктов мониторинга верхний предел должен быть больше нижнего", "materialLibrary": "Библиотека материалов"}, "hardware": {"timeQuery": "Создание резервной копии параметров требует времени. Если резервная копия не найдена при первом запросе, попробуйте обновить страницу позже!", "restoreParam": "Восстановить параметры", "backupParameter": "Создать резервную копию параметров", "hardwareStatus": "Состояние оборудования", "restore": "Восстановить", "akeyBackup": "Однонажатие для создания резервной копии", "backupSuccessful": "Резервная копия успешно создана", "backupSuccessful1": "Текущая версия не отображает прогресс", "BackingUp": "Создание резервной копии... Пожалуйста, подождите", "selectionCard": "Не выбрана ни одна карта. Функция параметров оборудования недоступна.", "parameterRecovery": "Восстановление параметров...", "waitingRecover": "Ожидание восстановления...", "namePackage": "Принадлежащая карта/название пакета", "restoreCancel": "Некорректное состояние соединения карты. Восстановление отменено!", "readyRecovery": "Подготовка к восстановлению!", "tryAgain": "Состояние соединения всех выбранных карт для восстановления некорректно. Проверьте и попробуйте снова!", "recoveryComplete": "Восстановление завершено!", "afterRecovery": "Восстановление завершено. Окно обновления закроется через три секунды", "Recovering": "Восстановление в процессе...", "timesOut": "Некоторые карты не поддерживают восстановление параметров. Истекло время ожидания запроса. Восстановление завершено!"}, "el": {"colorpicker": {"confirm": "Подтвердить", "clear": "Очистить"}, "image": {"error": "Не удалось загрузить изображение"}, "table": {"emptyText": "Нет данных"}, "pagination": {"total": "Всего", "pagesize": "шт./страница", "goto": "Перейти", "pageClassifier": "страница"}}, "task": {"name": "Название задачи", "isCycle": "Циклически?", "cycleIndex": "Количество циклов", "InfiniteLoop": "Бесконечный цикл", "task": "Задача", "type": "Ти<PERSON> задачи", "text": "Текстовое содержание", "voiceName": "Объявление", "speed": "Скорость речи", "pitch": "Интонация", "femaleVoice": "Женский голос", "maleVoice": "Мужской голос", "textToLanguage": "Преобразование текста в речь", "media": "Медиа", "plays": "Количество воспроизведений", "selectMedia": "Выбрать медиа", "TemplateContent": "Содержание шаблона", "ImportTemplate": "Импортировать шаблон", "import": "Импортировать", "normal": "Нормальная", "faster": "Быстрее", "fast": "Быстро", "playTypes": "Типы воспроизведения", "specifyPlays": "Указать количество воспроизведений", "specifyPlayTime": "Указать время воспроизведения", "isTiming": "Включить таймер?", "allTime": "Воспроизводить в течение всего периода", "inStream": "Это задача вставки?", "normalTask": "Обычная задача", "inStreamTask": "Задача вставки", "clearInStream": "Очистить только задачи вставки"}, "lamp": {"poleName": "Название терминала", "broadcast": "Вещание", "monitor": "Мони<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environment": "Метеорологическое окружение", "lighting": "Освещение", "Passenger": "Статистика потока пассаж<PERSON>ров", "longitude": "Долгота", "latitude": "Шир<PERSON><PERSON>а", "ChooseTargeting": "Выберите позиционирование", "LoadingPositioning": "Загрузка позиционирования...", "FailedToGetLocationInformation": "Не удалось получить информацию о местоположении", "online": "В сети", "offline": "Не в сети", "targetingIsSet": "Позиционирование установлено", "targetingNotSet": "Позиционирование не установлено", "listMode": "Режим списка", "mapMode": "<PERSON>ежим карты", "updateTime": "Время обновления", "TheSensorIsNot": "В текущий период времени датчик не подключен", "getMapException": "Ошибка при получении карты", "NoLocationData": "Нет данных о местоположении", "PleaseEnterThePoleNameOrdeviceID": "Пожалуйста, введите название терминала или ID устройства", "radioState": "Состояние вещания", "latAndLngNotEmpty": "Широта и долгота не могут быть пустыми", "gpsUploadState": "Передача GPS-данных"}, "broadcast": {"SIPAddress": "Подтвердить установку текущего адреса сервера платформы в качестве адреса SIP - сервера", "customSIPAddressStart": "Подтвердить установку", "customSIPAddressEnd": "в качестве адреса SIP - сервера", "radioState1": "Поддерживается только вещание", "radioState2": "Поддерживается вещание и разговор, но не тревога", "radioState3": "Поддерживается вещание и тревога, но не разговор", "radioState4": "Поддерживается вещание, разговор и тревога", "SIP_account": "SIP - аккаунт", "multicastAddress": "Мультикастный адрес", "selectDate": "Пожалуйста, выберите дату", "pauseOrOpenBroadcast": "Приостановить/включить задачу вещания", "broadcastInfo": "Детали вещания", "broadcastProgramState": "Состояние радиопрограммы", "paused": "Приостановлено", "playing": "Воспроизводится", "haveProgram": "Есть ли реальная программа", "playMode": "Режим игры.", "focusMode": "Режим фокуса", "fallingTone": "До.", "mute": "звук", "noProgram": "Пока нет.", "noBroadcast": "Временно без радио"}, "meteorological": {"temperatureSubText": "Если температура указана как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "Illuminance": "Освещенность", "humiditySubText": "Если влажность указана как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "noiseSubText": "Если шум указан как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "windSpeedSubText": "Если скорость ветра указана как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "windDirectionSubText": "Если направление ветра указано как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "illuminationSubText": "Если освещенность равна 0, это означает, что при запросе произошла ошибка или данные отсутствуют.", "PM10SubText": "Если PM10 указан как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "PM25SubText": "Если PM2.5 указан как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "pressureSubText": "Если давление указано как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "rainFallSubText": "Если количество осадков указано как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "radiationSubText": "Если радиация указана как '--', это означает, что при запросе произошла ошибка или данные отсутствуют.", "pressure": "Атмосферное давление", "rainFall": "Осадки", "radiation": "Радиация"}, "bigScreen": {"VideoSurveillance": "Видеонаблюдение", "DeviceList": "Список устройств", "Address": "Подробный адрес", "NumberOfOnline": "Количество подключенных", "OperationLog": "<PERSON><PERSON><PERSON><PERSON><PERSON> операций", "ProgramPlayStatistics": "Статистика воспроизведения программ", "RequestFailed": "Запрос не выполнен"}, "electricity": {"current": "Ток", "power": "Мощность", "electricity": "Электроэнергия", "voltage": "Напряжение", "currentSubText": "Если ток равен 0, это означает, что при запросе произошла ошибка или данные отсутствуют.", "powerSubText": "Если мощность равна 0, это означает, что при запросе произошла ошибка или данные отсутствуют.", "electricitySubText": "Если электроэнергия равна 0, это означает, что при запросе произошла ошибка или данные отсутствуют.", "voltageSubText": "Если напряжение равно 0, это означает, что при запросе произошла ошибка или данные отсутствуют.", "clearData": "Очистить данные", "clearSuccessfully": "Очистка выполнена успешно", "clearFailed": "Очистка не удалась", "cancelClear": "Отменить очистку", "monthlyElectricity": "Ежемесячная статистика электроэнергии", "exportElectricity": "Экспорт ежемесячных данных об электроэнергии", "setElectricityTime": "Установить период запроса электроэнергии", "selectTime": "Пожалуйста, выберите период запроса электроэнергии", "tip": "По умолчанию период запроса составляет 24 часа.", "electricityData": "Данные об электроэнергии", "curElectricityTime": "Текущий период"}, "monitor": {"device_ip": "IP-адрес устройства", "port": "Порт", "owning_terminal": "Принадлежащий терминал", "monitorSaveTip": "Для онлайн-устройств будьте осторожны при изменении имени пользователя и пароля. Неправильно введенные имя пользователя и пароль могут привести к отключению устройства от сети.", "Device_name_cannot_be_empty": "Название устройства не может быть пустым", "Please_enter_the_device_serial_number": "Пожалуйста, введите серийный номер устройства", "monitorSaveTip1": "Изменить имя пользователя и пароль на: имя пользователя", "Split_screen": "Разделение экрана", "PTZ_control": "Управление пановоротом и зумом", "equipment": "Устройство", "deviceIdIsNotEmpty": "ID устройства не может быть пустым", "CommandISExist": "Команда не существует", "notExistOrNotLoggedIn": "Устройство не найдено или не вошло в систему", "isNotExist": "Устройство не существует", "notLoggedIn": "Устройство не вошло в систему", "zoom": "<PERSON>ум", "aperture": "Диа<PERSON>рагма", "focus": "Фокус", "screenshot": "Скриншот", "noPlayback": "В этом временном диапазоне нет записи для просмотра", "downloadPlayback": "Скачать запись для просмотра", "selectDateRange": "Выберите временной диапазон", "tip": "При скачивании записи с камеры она будет сохранена на сервере. Пожалуйста, подождите некоторое время, а затем скачайте ее на свой компьютер с данной страницы.", "normal": "Обычная камера", "humanNumberStatistic": "Статистика потока посетителей", "insideHumanNumber": "Накопление людей", "traffic": "Интеллектуальное транспортное обеспечение", "cameraType": "<PERSON>и<PERSON> камеры", "monitorSaveTip2": "Изменить тип функции камеры на:", "openTheAlarm": "Включить сигнализацию", "crowdAlarmThreshold": "Порог сигнализации о накоплении людей", "intervalForSendingEmails": "Интервал времени для отправки писем", "openTheAlarmTip": "После включения сигнализации, если количество людей на устройстве превысит порог сигнализации, на электронную почту пользователя, которому принадлежит устройство, будет отправлено письмо. После срабатывания сигнализации по электронной почте следующее письмо будет отправлено через заданный интервал времени. Будет использована электронная почта, на которую зарегистрирован пользователь.", "offLineOrNotExist": "Устройство находится в автономном режиме или отключено", "serialNumber": "Серийный номер камеры", "rtmpStreamState": "Включить потоковое вещание?", "streamCloseTip": "После отключения потокового вещания вы не сможете просматривать видеонаблюдение на веб - странице. Вы уверены, что хотите отключить потоковое вещание?", "streamOpenTip": "После включения потокового вещания вы сможете просматривать видеонаблюдение на веб - странице. Эта операция потребует определенного объема трафика. Вы уверены, что хотите включить потоковое вещание?"}, "pay": {"contactInformation": "Контактная информация", "addressValue": "1-й Цзянхоу ДАО, Цзунхуань, Гонконг", "savingsAccountNumber": "Номер сберегательного счета", "xixunCompanyValue": "Шанхай Сишун Электроник Технолоджи Компани Лтд", "bankName": "Название банка", "bankNameValue": "Компания «Банк Хонконга и Шанхая»", "swiftCode": "Международный код", "tips1": "Пожал<PERSON>йста, купите оборудование VIP, чтобы оно могло работать нормально", "tips": "После успешного входа в систему, чтобы оборудование могло работать нормально, оформите подписку на VIP в платных услугах", "totalPrice": "Общая стоимость", "contactPerson": "Контактное лицо", "contactPersonTips": "Пожалуйста, введите контактное лицо", "telephone": "Контактный телефон", "telephoneTips": "Пожалуйста, введите контактный телефон", "address": "Адрес компании", "addressTips": "Пожалуйста, введите адрес компании", "companyName": "Полное название компании", "companyNameTips": "Пожалуйста, введите полное название компании", "vipDuration": "Срок действия VIP/год", "vip4": "VIP 4 класса «Алмазный»", "upgradeToPaidVersion": "Обновить до платной версии", "silverCardVip": "Серебряный VIP-карта", "goldCardVip": "Золотой VIP-карта", "diamondVip": "Алмазный VIP", "superVip": "Супер VIP", "orderService": "Заказать услугу", "currentVersion": "Текущая версия: ", "numberOfTerminals": "Количество терминалов", "TheRemainingAmount": "Остаток", "ExpireDate": "Дата истечения срока", "selectVersion": "Выбрать версию", "SelectDuration(years)": "Выбрать срок (лет)", "totalOrder": "Общая сумма заказа", "submitOrder": "Подтвердить заказ", "freeVersion": "Бесплатная версия", "price": "Цена", "ThereAreUnpaidOrders": "Есть неоплаченные заказы", "ThereIsAnUnpaidOrderPleaseGoToPay": "Есть неоплаченный заказ, пожалуйста, перейдите к оплате.", "OrderRecord": "Записи заказов", "100ControlCards": "Количество контроллерных карт: 100 штук", "500ControlCards": "Количество контроллерных карт: 500 штук", "NumberOfControlCards1500": "Количество контроллерных карт: 1500 штук", "unpaid": "Не оплачено", "transactionCreation": "Создание транзакции", "UnpaidTransactionTimeoutClosed": "Транзакция без оплаты закрыта по истечении времени", "paymentSuccessful": "Оплата прошла успешно", "OrderDetails": "Детали заказа", "pay": "Оплатить", "orderNumber": "Номер заказа", "Years": "Годы", "PaymentStatus": "Статус оплаты", "amount": "Сумма", "newPurchase": "Новая покупка", "Renewal": "Продление", "upgrade": "Обновление", "PayForTheOrder": "Оплатить заказ", "ExpectedPeriod": "Предполагаемый период: ", "to": "по", "ActualDeadlineIsSubjectToPayment": "Фактический срок зависит от оплаты", "cancelOrder": "Отменить заказ", "theOrderWillBeAutomaticallyClosed": "Срок действия заказа - 15 дней. Пожалуйста, оплатите его в срок. Если заказ не будет оплачен в течение 14 дней, он будет автоматически закрыт.", "AfterOrderIsPaidSuccessfully": "Примечание: После успешной оплаты заказа вы можете перейти на", "QueryPaymentInformationOnThePage": "страницу для запроса информации об оплате", "paymentMethod": "Способ оплаты", "publicAccount": "Корпоративный счет", "AccountName": "Наименование учетной записи:", "AccountBank": "Банк, где открыт счет:", "LinkNumber": "Номер межбанковского соединения:", "account": "Номер счета:", "uploadTheBankReceipt": "Если вы перевели деньги на указанный счет, пожалуйста, загрузите банковский квитанцию.", "receivedByMajorBanks": "Время поступления денежных средств при банковском переводе между юридическими лицами в различных банках составляет от 2 часов до 3 рабочих дней.", "notifyYouViaSMS": "Наши сотрудники активируют услугу для вас сразу после получения платежа.", "UploadBankReceipt": "Загрузить банковский квитанцию", "contactOurSalesStaff": "В случае возникновения вопросов, пожалуйста, свяжитесь с нашими менеджерами по продажам.", "NotesForPublicTransfers": "Важно знать при банковском переводе между юридическими лицами:", "submittedForFinancialReview": "При оплате через банк есть задержка в поступлении средств, и вам необходимо предоставить банковский квитанцию для финансовой проверки.", "TransfersFromPersonalAccounts": "Перевод с личного счета на наш корпоративный счет позволяет получить только простой личный счет-фактуру.", "IfTheCompanyAccountIsTransferred": "Перевод с корпоративного счета на наш корпоративный счет позволяет получить обычный счет-фактуру для юридических лиц или счет-фактуру с НДС для юридических лиц.", "uploadTip": "Поддерживаются форматы JPG и PNG, размер файла не должен превышать 5 МБ.", "BankCardNumber": "Номер банковской карты", "bankCardNumberWhenTransferring": "Пожалуйста, укажите номер банковской карты, с которой был выполнен перевод.", "transactionHour": "Время транзакции", "CancellationLineItemCannotBeEmpty": "Пункты отмененного заказа не могут быть пустыми.", "WaitingForSellerToConfirm": "Ожидание подтверждения продавцом", "PurchaseStatus": "Статус покупки", "bankReceipt": "Банковский квитанцию", "Serve": "Услуга", "Optional": "Дополнительно", "deadline": "Срок действия: ", "orderUpdateTips": "Вы можете изменить только загруженную банковскую квитанцию или номер банковской карты в заказе. В случае вопросов по другим параметрам заказа, пожалуйста, свяжитесь с нашими сотрудниками.", "PleaseUploadBankReceipt": "Пожалуйста, загрузите банковский квитанцию", "PleaseEnterBankCardNumber": "Пожалуйста, введите номер банковской карты", "NoRelatedOrders": "Нет связанных заказов", "ErrorUploadingBankReceipt": "Ошибка при загрузке банковской квитанции", "changeVipStateTip": "Если вы измените статус VIP текущего пользователя на 'Супер VIP', все пользователи в рамках компании текущего пользователя также получат статус 'Супер VIP'."}, "statistic": {"enableHumanStatistic": "Включить статистику потока посетителей", "queryHumanStatisticToday": "Запросить статистику потока посетителей за сегодня", "enter": "Количество входящих", "exited": "Количество выходящих", "countingMonitoring": "Монито<PERSON><PERSON><PERSON>г статистики потока посетителей", "viewChart": "Просмотреть график", "currentNumber": "Текущее количество людей", "lineChart": "Линейный график статистики количества людей в зоне", "areaPeopleNum": "Количество людей в зоне", "selectHistoricalData": "Запросить исторические данные", "sendData": "Отправить данные", "keyTip": "После включения функции отображения данных ключ будет сгенерирован автоматически", "isShowHumanNumber": "Включена ли функция отображения данных о потоке посетителей на терминале", "dataKey": "<PERSON><PERSON><PERSON><PERSON>", "noDataKey": "У текущего устройства нет ключа", "clickCopy": "Нажмите для копирования", "copySuccess": "Копирование успешно выполнено"}, "alarm": {"alarmNum": "Номер тревоги", "alarmPeople": "Оповещавший", "call": "Звонивший", "receive": "Получатель", "callTime": "Время разговора", "channel": "<PERSON><PERSON><PERSON><PERSON>", "dstChannel": "Целевой канал", "alarmDeviceInfo": "Информация об оборудовании тревоги", "setCallingVolume": "Настроить громкость разговора", "callingVolume": "Громкость разговора", "notEnable": "Не включено", "setAlarmInfo": "Настроить информацию об оборудовании тревоги", "sipAccount": "SIP - аккаунт", "sipServiceAddress": "Адрес SIP - сервера", "sipServicePort": "Порт SIP - сервера", "accountState": "Состояние аккаунта", "alarmDeviceNetwork": "Сетевые сведения об оборудовании тревоги", "setNetwork": "Настроить сетевое состояние", "dynamic": "Динамический", "static": "Статический", "gateway": "<PERSON><PERSON><PERSON><PERSON>", "subnetMask": "Маска подсети", "alarmAccount": "Аккаунт тревоги", "accountType": "Тип аккаунта", "registerAlarmAccount": "Зарегистрировать аккаунт оборудования тревоги", "registerPhoneNumber": "Зарегистрировать аккаунт телефона", "accountRule": "Аккаунт не может быть пустым и должен состоять из 11 цифр или букв", "account": "Аккаунт", "batchSettings": "Пакетные настройки", "alarmNetworkTip": "Удерживайте кнопку на устройстве сигнализации до появления звукового сигнала. Одно нажатие - озвучивание IP-адреса, три быстрых нажатия - переключение между динамическим и статическим IP-адресом.", "alarmAddressTip": "Перед настройкой адреса сигнализации, пожалуйста, настройте учетную запись и другую информацию устройства сигнализации в разделе информации о устройстве сигнализации.", "alarmAddressSetTip": "В режиме по умолчанию платформа установит адрес сигнализации равным адресу платформы.", "defaultState": "По умолчанию", "custom": "Настраиваемый", "myPhoneNumber": "Свой номер", "calledPhoneNumber": "Номер для звонка", "alarmInfoTip": "Перед настройкой информации о устройстве сигнализации, пожалуйста, зарегистрируйте учетную запись.", "backupCalledPhoneNumber": "Резервный номер"}, "traffic": {"enableTraffic": "Включить интеллектуальное транспортное обеспечение", "eventName": "Название события", "plateNumber": "Номер автомобильного номера", "plateType": "Тип автомобильного номера", "plateColor": "Цвет автомобильного номера", "vehicleColor": "Цвет кузова автомобиля", "vehicleType": "Тип кузова автомобиля", "vehicleSize": "Размер кузова автомобиля", "illegalPlace": "Место нарушения", "eventTime": "Время события", "downloadEventPic": "Скачать фото нарушения", "illegalPic": "Фото нарушения"}, "radar": {"radarSpeed": "Радарное измерение скорости", "radarSetting": "Настройка радара", "fastestCar": "Самый быстрый автомобиль", "closestCar": "Самый близкий автомобиль", "setResponseTime": "Установить время отклика", "setOutputTarget": "Установить выходной цель", "setMinSpeed": "Минимальная скорость детекции", "setMaxSpeed": "Максимальная скорость детекции", "setSensitivity": "Чувствительность", "isConnect": "Подключен ли радар", "speed": "Скорость", "parameter": "Параметр", "speedLimitRange": "Интервал ограничения скорости", "addSpeedLimitRange": "Пожалуйста, добавьте интервал ограничения скорости", "minSpeed": "Минимальная скорость", "maxSpeed": "Максимальная скорость", "radarSpeedLimit": "Радарное ограничение скорости"}, "ac": {"apOnlineRate": "Коэффициент онлайн-работы AP", "apOnlineNumber": "Количество онлайн-AP", "apSum": "Общее количество AP", "onlineTerminal": "Количество онлайн-терми<PERSON><PERSON>ов", "flowStatistics": "Статистика трафика", "flowStatisticsToday": "Статистика трафика за сегодня", "name": "Название AC", "macAddress": "MAC-адрес", "belongArea": "Принадлежащий регион", "belongOrganization": "Принадлежащая организация", "belongProject": "Принадлежащий проект", "userOnlineCount": "Общее количество подключений пользователей", "acOnlineUserCount": "Общее количество онлайн-пользователей на AC", "upstreamTraffic": "Поток вверх", "downlinkTraffic": "Поток вниз", "refresh": "Обновить"}, "userAuth": {"pass": "Аутентификация пройдена", "fail": "Аутентификация не пройдена", "company": "Аутентификация компании", "personal": "Персональная идентификация", "unverified": "Не аутентифицирован", "certificationAudit": "Аудит сертификации", "authMode": "Способ аутентификации", "authInfo": "Информация об аутентификации", "enterpriseLicense": "Лицензия на ведение бизнеса юридического лица", "OfficialSeal": "Свидетельство о предоставлении права на печать компании", "FrontOfIdentityCard": "Лицевая сторона паспорта", "ReverseOfIDCard": "Обратная сторона паспорта", "reason": "Причина", "authentication": "Аутентификация", "pleaseUploadEnterpriseLicenseOfficialSeal": "Пожалуйста, загрузите свидетельство о печати компании и лицензию на ведение бизнеса юридического лица", "updateAuth": "Обновить аутентификацию", "uploadAuth": "Загрузить аутентификацию"}, "cat1": {"temp": "Температура шаблона", "power": "Активная мощность", "electricEnergy": "Активная электрическая энергия", "roadOne": "Путь 1", "roadTwo": "Путь 2", "addDevice": "Добавить устройство", "channel": "<PERSON><PERSON><PERSON><PERSON>", "colorTemp": "Цветовая температура", "scheduleTip": "Этот таймер начинается с установленного времени RealTime. Например, если установленное время - 12:30, то это означает, что с 12:30 до следующей заданной точки времени состояние отдельной лампы будет соответствовать установленной яркости и цветовой температуре. Чтобы выключить отдельную лампу, можно установить яркость равной 0."}, "notify": {"NotificationStrategy": "Стратегия уведомлений", "NotifyTip": "（После включения уведомлений пользователем, уведомления будут отправляться на электронную почту в указанное время）", "OfflineNotify": "Уведомление об отключении", "CardNotWorkingNotification": "Уведомление о неисправности приёмной карты", "tactics": "Стратегия (часы)", "selectAll": "Выбрать все", "PleaseSelectNotification": "Пожалуйста, выберите стратегию уведомлений", "SendingMailbox": "Отправитель электронной почты", "SendingTime": "Время отправки", "Username/EmailAddress": "Имя пользователя/Адрес электронной почты отправителя", "OffLineTime": "Время отключения", "AbnormalTime": "Время аварии", "NumberOfCardsRLastTime": "Количество приёмных карт в прошлый раз", "NumberOfCardsReceivedExceptionOccurs": "Количество приёмных карт при аварии"}, "thirdPartyAd": {"enabledThirdPartyAd": "Подключение рекламных объявлений третьих сторон", "thirdPartyUrl": "Адрес сервиса третьей стороны", "loopTip": "Воспроизводится в цикле вместе с существующими рекламными объявлениями на устройстве. Несколько рекламных объявлений будут равномерно распределены по списку существующих рекламных объявлений на устройстве.", "onceTip": "Воспроизводится один раз, вставленным в существующие рекламные объявления на устройстве. Несколько рекламных объявлений будут равномерно распределены по списку существующих рекламных объявлений на устройстве.", "mutexTip": "Воспроизводится в цикле в исключительном режиме", "adInterval": "Рекламный цикл", "queryThirdPartyAd": "Запросить рекламные объявления третьих сторон", "downloadUrl": "Адрес загрузки", "impression": "Информация о воспроизведении", "playHour": "Временной интервал воспроизведения", "playCount": "Количество уже воспроизведенных раз"}, "crossArrow": {"addToCross": "Добавить в крестик-стрелку", "arrowProgram": "Программа с стрелками", "greenArrow": "Зеленая стрелка", "redArrow": "Красный крестик", "closeScreen": "Выключить экран", "curProgram": "Текущая программа", "remove": "Удалить", "batchRemove": "Пакетное удаление"}, "manual": {"operationDocument": "Документация по эксплуатации", "videoTutorial": "Видеоурок", "resourceManagement": "Управление ресурсами", "manualAndTutorial": "Руководство пользователя и видеокурс", "universal": "Универсальный", "fileTips": "В одно и то же время можно загружать только один тип файлов"}, "myEditor": {"bottomCenter": "Выравнивание по нижнему краю", "topCenter": "Выравнивание по верхнему краю", "verticalCenter": "Вертикальное выравнивание по центру"}, "employee": {"today": "Сегодня", "yesterday": "Вчера", "last7Days": "Последние 7 дней", "last30Days": "Последние 30 дней", "lineChart": "Линейный график", "barChart": "Столбчатый график", "unitPerson": "Единица измерения: чел.", "man": "Мужчина", "woman": "Женщина", "tips": "После включения статистика потока посетителей будет автоматически вестись. После успешного включения вы можете найти устройство в разделе 'Статистика потока посетителей' -> 'Статистика потока посетителей V1'."}}