{"common": {"PleaseInput": "入力お願いします", "PleaseSelect": "選択をお願いします", "Loading": "ロード中です", "personalSettings": "個人設定です", "logOut": "ログアウトします", "query": "照会します", "newlyBuild": "新築です", "add": "追加します", "delete": "削除します", "batchDel": "一括削除です", "update": "修正します", "state": "状態です", "operation": "操作します", "tips": "ヒントです", "info": "詳細です", "deploy": "分配します", "cancel": "取り消します", "confirm": "確定です", "areYouSureExit": "脱退は確定ですか?", "setSuccess": "設置済みです", "notSet": "未設定です", "set": "設置します", "bindingSuccess": "バインディング済みです", "unbound": "バインディングしていません", "binding": "バインディングです", "operationSuccessful": "操作成功です", "createTime": "作成時間です", "normal": "正常です", "disable": "無効です", "delete_current_option": "現在のオプションの削除を確認します", "original": "原です", "use_iconfont": "アイコンはSVG Spriteベクターアイコンで統一します。", "total": "全部です", "supportedTip": "支持を差し控えます", "selectDevice": "装置の選択をお願いします", "clickToEnlarge": "クリックすると大きな図になります", "pictureLoadingFailed": "画像の読み込みに失敗します", "passwordError": "パスワードが違います", "OKToRestart": "再稼働確定です", "WaitingForRestart": "再開を待っています...", "RestartTimeout": "再開タイムアウトです", "modifiedTime": "時間を修正します", "exportData": "データを導出します", "submit": "提出します", "modifyTheSuccess": "修正成功です", "configuration": "配置します", "failure": "失敗です", "release": "頒布します", "nextStep": "次のステップです", "selectMaterial": "素材を選びます", "notSelectedectMaterial": "注:選択されていないパケットは、すべてのカードのsim情報をデフォルトで導出します", "exportingSIM": "SIM情報を導出します", "enterCardPackage": "ご所属カード/パケット名の入力をお願いします。...", "note": "注", "enterPackage": "パケット名を入力します。...", "noDetails": "詳しくはありません", "versionQuery": "バージョン照会です", "CardSysterm5263": "バージョンクエリにはCardSysterm5.2.6.3以降が必要です", "uninstallCard": "オプションカードがないため、オンラインアンインストール機能は利用できません。", "packageName": "かばんの名前です", "versionNumber": "バージョン番号です", "versionIdentifiers": "バージョン識別です", "wrong": "本カードの接続状態に誤りがあり、更新をキャンセルします!", "upgradeWrong": "ランクアップを選択した全てのカードの接続状態に問題があります。", "cardsNotReturn": "一部のカードがサポートされていませんが、プログレスバー戻り、リクエストタイムオーバー、アップグレード終了!", "updateComplete": "更新完了です!", "restartCards": "これらのカードを再起動しますか?", "addTiming": "タイミングをお願いします。", "Monday": "月曜日です", "Tuesday": "火曜日です", "Wednesday": "水曜日です", "Thursday": "木曜日です", "Friday": "金曜日です", "Saturday": "土曜日です", "Sunday": "日曜日です", "brightnessClears": "手動で明るさを設定すると、全タイミングで明るさと感度がなくなります。", "fullBrightness": "最大輝度です", "screenDuring": "注:設定時間は画面が開いている状態です", "removeTiming": "消去タイミングです", "clearSuccess": "除去成功です", "notEnteredNotModified": "未入力の場合は修正なし、Conn_v11.1.1.1[261]-AIPS-release-2&4以降のバージョンでは未入力の場合は空に設定します。", "Changing": "会社のIDを修正しますあるいはrealTimeアドレスはカードのラインオフにつながることができて、修正することを確定しますか?", "networkNTP": "自動キャリブレーションにはネットワークとNTPアドレスが必要です", "Settings": "[選択モード]を同期設定した場合、未記入は空に設定されているとみなされます。", "SynchronousMode": "同期モードです", "zoneEmpty": "タイムゾーンを空にしてはいけません!", "synchronousState": "設定タイミングが非同期の時間区間で同期状態になります!", "beEmpty": "幅、高さは0以下、空ではいけません!", "ModifyFailure": "修正失敗です", "programmeDetails": "ミッションの詳細です", "showWidth": "番組の幅です", "showHigh": "番組の高さです", "notOnPlatform": "クエリは失敗し、現在の番組はこのプラットフォームでは配信されません。", "allIntervals": "オンのタイミングでタイミング区間が選択されなかった場合は、すべての区間が選択されたものとなります。", "notSelected": "未選定です", "true": "そうです", "false": "いいえ", "name": "名前です", "custom": "カスタムです", "MaximumNumberOfWords200": "文字数の上限200です", "exportingSIMTips": "現在のパケットには端末がありませんので、別のパケットをお勧めします。", "language": "言語です", "copy": "複製します", "save": "保存します", "saveAndExit": "保存して退会します", "noMoreData": "これ以上のデータはありません", "enable": "起用します", "NotEnabled": "未使用です", "AssignUsers": "ユーザーを割り当てます。", "manual": "使用マニュアル", "addMedia": "+ メディアを追加します", "reviewCurrentUsers": "弊社に連絡して現在のユーザーをチェックしてもらいます。", "selectGroup": "パケットを選択します", "selectingGroup": "現在のパケット", "unclassified": "未パケット化です", "frontStep": "一歩進みます", "search": "検索します", "CAT1": "CAT1", "DaHuaCamera": "ダイファのカメラです", "GBCamera": "国標カメラです"}, "screen": {"simInformation": "simカード情報です", "networkState": "インターネット国家です", "series": "シリーズ番号です", "countries": "国です", "operationName": "オペレーション名です", "unknownState": "未知の状態です", "noCard": "カードが入っていません", "PIN": "ロック状態,ユーザーのPINコードが必要です", "PUK": "ロック状態,ユーザーのPUKコードが必要です", "PIN2": "ロック状態です,ネットワークのPINコードが必要です", "readyState": "準備完了状態です", "InvalidState": "無効状態です", "subscribe": "購読します", "choose": "選択します", "supportZip": "バージョンファイルはZip形式のみサポートします!", "selectFile": "ファイルを選択します", "releaseTime": "発表時間です", "versionInformation": "バージョン情報です", "testVersion": "バージョンの詳細はありませんテスト版!", "hardwareVersion": "ハードウェアのパラメータはバージョンの詳細がありません!", "officialRelease": "正式版がリリースされました", "testRelease": "テスト版のリリースです", "hardwareRelease": "ハードウェアパラメータのリリースです", "sizeMore": "大きさ以上です", "OnlyForZIPType!": "ZIPタイプのみです!", "uploadEmpty": "アップロードしたファイルを空にしてはいけません!", "uploadFile": "ファイルをアップロードします", "fileTips": "MP3、MP4、gif、png、jpg形式のファイルのみアップロードできます。MP3と画像は100M、MP4は150Mを超えてはいけません。", "fileTips1": "MP3ファイルのみアップロードでき、サイズは20Mを超えません。", "picture": "画像です", "video": "ビデオです", "picturesOrVideos": "アップロードされるメディアファイルは画像またはビデオのみです!", "my": "私のです", "pending": "審査待ちです", "pageImprovement": "ページ完成中です。", "errorDetails": "エラーの詳細です", "null": "ありません", "showProgress": "番組進行です", "sendSuccess": "送信成功です", "sendSuccessOffline": "オフライン番組が送信された場合、コントロールカードは72時間以内に自動配信されます。", "failedProgress": "進捗取得に失敗します", "timesOut": "取得進捗タイムオーバーです", "selectCard": "コントロールカードを選択します", "reviewDetails": "詳細を審査します。", "satelliteNumber": "衛星の数です", "autoBrightnessTable": "自動明るさ計です", "senorType": "センサータイプです", "setAutoBrightnessTable": "自動明るさ計を設定します", "getAutoBrightnessTable": "自動明るさ計を設定します", "default255BrightnessTable": "255のデフォルトテーブルです", "customizeBrightnessTable": "カスタム輝度テーブルです", "otherSwitch": "その他のスイッチです", "customSwitchFunction": "カスタマイズスイッチ機能です", "programPlayDetail": "番組放送の詳細です", "programPlayStatistic": "放送統計です", "programPlaySum": "総放送数です", "exportPlayLog": "導出ログです", "log": "ログ", "networkPort": "網の口です", "clickToCheck": "クリック検出です", "getPointCheckInfo": "点検情報を取得します", "imageSize": "画像サイズです。", "badPointNum": "悪い点の数です", "queryPlayerState": "プレーヤーの状態を照会します", "playerStateRep": {"1": "初期化です", "2": "定時番組終了です", "3": "放送予定の番組がありません", "4": "番組を削除します", "5": "番組の中です", "6": "まだ情報がありません", "7": "番組が間違っているかもしれません", "8": "スクリーンを切ります", "9": "定点番組は対象外です"}, "fullColor": "フルカラーです", "monochrome": "単色です", "redBeadNumberPoints": "赤玉の不良数です", "greenBeadNumberPoints": "緑色の玉の不良数です", "blueBeadNumberPoints": "青い玉の不良数です", "totalNumberOfBadPoints": "悪い点の総数です", "redBadPointPosition": "赤ビーズの悪点座標です", "greenBadPointPosition": "緑のビーズの悪点座標です", "blueBadPointPosition": "悪い点の座標です", "badPointPosition": "悪点座標です", "abscissa": "横軸です", "ordinate": "縦座標です", "noBadPoint": "悪い点はありません", "pointCheckTips": "悪点検出はシングルカードのみ対応しますので、選択し直してください", "receivingCard": "受信カードです", "setRecCardRelaySwitch": "受信カードリレースイッチをセットします", "getRecCardSensorData": "受信カードセンサデータを取得します", "smoke": "煙です", "smokeless": "無煙です", "openCloseDoor": "ドアの開閉です", "openDoor": "開けます", "closeDoor": "閉まります", "relaySwitch": "リレースイッチです", "levelDetection": "レベル検出です", "accessHighLevel": "アクセスレベルを示します", "noDeviceConnected": "アクセス機器がないことを示します", "firstRoad": "最初の道です", "secondWay": "第2ルートです", "thirdWay": "第三ルートです", "fourthWay": "第四ルートです", "theFifthRoad": "第五ルートです", "sensorDataShareTips": "センサー データの共有は 1 枚のカードのみをサポートします。もう一度選択してください。", "hour": "時", "pointTable": "ポイントテーブル", "pointTableTips": "専門家の指導の下で操作してください", "pointTableTemplate": "ポイント テーブル テンプレート", "pleaseUploadPointTable": "最初にポイント テーブルを設定してください。", "networkConfig": "ネットワーク構成", "hotspot": "ホットスポット", "wifiTip": "WIFI を有効に設定すると、ネットワークの状態が変化し、オフラインになる可能性があります。慎重に操作してください。", "apTip": "注: パスワードには大文字、小文字、数字を組み合わせて使用してください。パスワードの長さは 8 ～ 20 文字にする必要があります。", "wifiList": "WiFi リストのクエリ", "selectWifi": "WIFIを選択してください", "singleCardOperation": "この関数はシングル カード操作のみをサポートします"}, "sys": {"enable": "有効にする", "remarks": "備考を入力してください", "authenticationMode": "認証方法を選択してください", "emailVerification": "メールの確認", "mobileVerification": "携帯電話の確認", "verify": "確認方法", "notOpen": "未開放", "email": "メール", "mobile": "携帯", "whetherAudit": "審査するかどうか", "open": "開く", "close": "閉じる", "companyId": "企業ID", "same": "企業IDと作成者の企業IDが一致しています", "roleEmpty": "ユーザーロールを空にすることはできません", "alarmType": "警報の種類", "alarmTime": "警報の時間", "Iknown": "承知した", "currentBrowser": "現在のブラウザはサーバーからのイベントをサポートしていません", "notReminded": "確認すると、再度通知されません", "visited": "あなたが訪れたページ", "find": "見つかりません", "url": "URLを確認してください", "previousPage": "前のページに戻る", "enterHome": "ホームに入る", "permissionOfTerminalGroups": "制御可能な端末グループ", "TheSuperAdminDoesNotRestrictUserGroups": "スーパー管理者はユーザーグループを制限しません", "addOrUpdateAuth": "認証を追加/更新する", "isDefaultPassword": "デフォルトのパスワードを設定するかどうか"}, "login": {"dynamicCodeEntryTips": "現在のユーザーはまだ確認方法を開始していません。まず、確認方法を開始してください", "login": "ログイン", "passwordLogin": "パスワードでログイン", "username": "ユーザー名", "password": "パスワード", "ForgetThePassword": "パスワードを忘れました", "newPassword": "新しいパスワード", "confirmPassword": "パスワードの確認", "dynamicCodeEntry": "動的コードログイン", "pwdNotes": "8桁以上のパスワードを使用することをお勧めします", "origin_pwd_incorrect": "元のパスワードが間違っています", "wrong_account_or_password": "アカウントまたはパスワードが間違っています", "account_has_been_locked": "アカウントはロックされています。管理者に連絡してください", "updateEmail": "メールアドレスを変更する", "bindEmail": "メールアドレスをバインドする", "updateMobile": "携帯電話番号を変更する", "bindMobile": "携帯電話番号をバインドする", "sliderRight": "ボタンをクリックして確認してください", "loading": "読み込み中", "passwordMore8": "パスワードは8桁以上で、大文字、小文字、数字の組み合わせである必要があります", "user4To17": "ユーザー名は4文字以上、17文字未満で、数字または英字でなければなりません", "clickCodeMailbox": "確認コードを取得するには、ボタンをクリックしてください。情報は安全なメールアドレスに送信されます", "clickCodePhone": "確認コードを取得するには、ボタンをクリックしてください。SMSが安全な携帯電話に送信されます", "authenticationSuccessful": "認証に成功しました", "securityVerification": "まず安全な確認を行ってください", "enterVerificationCode": "確認コードを入力してください", "UserNameEmailMobile": "ユーザー名/メール/携帯電話", "ScopeOfAuthority": "権限範囲", "superAdministrator": "スーパー管理者", "PermissionDetails": "権限の詳細", "pleaseContactTheAdministrator": "管理者に連絡してください"}, "register": {"register": "登録", "mobile": "携帯電話", "mailbox": "メールボックス", "code": "確認コード", "getCode": "確認コードを取得", "remind": "WeChatのスキャンログインをお勧めします", "back": "ログインに戻る", "prependResend": "前に ", "appendResend": " 秒後に再送信する", "personalInformation": "個人情報", "complete": "完了", "recommendedRegister": "WeChatのスキャンログインをお勧めします", "enterPassword": "もう一度パスワードを入力してください", "companyId": "企業ID、カードオンラインにバインドされた企業ID、確定後は変更できません", "companyName": "企業名", "companyAddress": "企業住所", "companyPhone": "企業電話", "readAIPSAgreement": "まずAIPSユーザー契約をお読みください", "readAccepted": "私は読み、受け入れました", "AIPSAgreement": "AIPSユーザー契約", "registeredSuccessfully": "登録に成功しました", "clickJumpOr": "クリックしてジャンプするか", "loginDisplayed": "秒後に自動的にログイン画面にジャンプします", "readAcceptAIPSAgreement": "AIPSユーザー契約をお読みください", "youAreRight": "You are right", "resettingVerification": "パスワードをリセットする操作を行っています。まず安全な確認を行ってください", "switchingAuthentication": "確認方法を切り替える", "pleaseSet": "設定してください", "passwordSecurity": "新しいパスワードを設定してください。数字、英字、記号の組み合わせのパスワードを使用することをお勧めします", "passwordChangedSuccessfully": "パスワードを変更しました", "verified": "実名認証", "idCardNumber": "身分証明書番号", "companyLicense": "有効な年次検査期間内の「企業法人事業免許証」の画像をアップロードしてください", "cachet": "企業の公式シールが付いた「承認証明書」の画像をアップロードしてください", "idCardFront": "身分証明書の表面の画像をアップロードしてください", "idCardReverse": "身分証明書の裏面の画像をアップロードしてください", "pleaseReadAndCheckAIPS": "AIPSユーザー契約をお読みください", "personal": "個人", "company": "企業", "CertifiProgress": "認証の進捗", "waitForAdminAuth": "管理者の認証を待っています", "DownloadLicenseCertificate": "「承認証明書」をダウンロード"}, "ChangePWD": {"ChangePassword": "パスワード変更", "remind": "携帯電話番号やメールアドレスを登録したユーザーのみがパスワードを再設定できます"}, "nav": {"首页": "ホーム", "智慧屏幕": "スマートスクリーン", "智慧广播": "スマート放送", "气象环境": "気象環境", "智慧监控": "スマート監視", "客流统计": "旅客統計です", "客流统计V1": "旅客統計ですV1", "系统管理": "システム管理", "设备管理": "デバイス管理", "节目管理": "番組管理", "媒体库": "メディアライブラリ", "日志管理": "ログ管理", "远程控制日志": "リモート制御ログ", "播放日志": "再生ログ", "用户管理": "ユーザー管理", "菜单管理": "メニュー管理", "角色管理": "ロール管理", "用户日志": "ユーザーログ", "登录日志": "ログインログ", "系统日志": "システムログ", "设备状态": "デバイスの状態", "广播任务": "放送タスク", "分组管理": "グループ管理", "审批管理": "承認管理", "公告管理": "お知らせ管理", "终端列表": "端末リスト", "智慧物联": "スマートIoT", "智慧照明": "スマート照明", "CAT1照明": "CAT1照明", "控制卡照明": "制御カード照明", "电能管理": "電力管理", "视频监控": "ビデオ監視", "付费服务": "有料サービス", "订购服务": "オーダーサービス", "订单": "注文", "SIP账号管理": "SIPアカウント管理", "监控回放": "監視再生", "人群聚集": "人群集まり", "订单管理": "注文管理", "报警管理": "警告管理", "报警记录": "アラーム記録", "通话记录": "通話記録", "智慧交通": "スマート交通", "交通信息": "交通情報", "雷达测速": "レーダースピードメータ", "WIFI AC": "WIFI AC", "概览": "概要", "AC管理": "AC管理", "密码管理": "パスワード管理", "认证审核": "認証審査", "通知策略": "通知戦略", "通知日志": "通知ログ", "十字箭头": "十字矢印", "设备白名单": "デバイスの白名單", "摄像头监控": "カメラで監視します"}, "validate": {"account_cannot_empty": "アカウントは空にできません", "password_cannot_empty": "パスワードは空にできません", "confirm_password_cannot_empty": "確認用パスワードは空にできません", "new_pwd_cannot_empty": "新しいパスワードは空にできません", "email_cannot_empty": "メールは空にできません", "mobile_cannot_empty": "携帯電話番号は空にできません", "code_cannot_empty": "コードは空にできません", "roleName_cannot_empty": "ロール名は空にできません", "menuURL_cannot_empty": "メニューURLは空にできません", "superior_menu_cannot_empty": "親メニューは空にできません", "menu_name_cannot_empty": "メニュー名は空にできません", "group_name_cannot_empty": "グループ名は空にできません", "group_type_cannot_empty": "グループタイプは空にできません", "audit_name_cannot_empty": "審査名は空にできません", "resource_type_cannot_empty": "リソースタイプは空にできません", "status_cannot_be_empty": "状態は空にできません", "approval_comments_cannot_blank": "承認意見は空にできません", "program_name_cannot_empty": "プログラム名は空にできません", "the_new_password_is_inconsistent": "確認用パスワードと新しいパスワードが一致しません", "the_password_is_inconsistent": "確認用パスワードとパスワードが一致しません", "incorrect_email_format": "メールの形式が正しくありません", "code_format": "6桁の文字を入力してください", "mobile_format": "携帯電話番号の形式が間違っています", "mobile_code_empty": "携帯電話の認証コードは空にできません", "email_code_empty": "メールの認証コードは空にできません", "company_id_empty": "会社IDは空にできません", "not_empty": "空にできません", "alarmAddress_not_empty": "警報アドレスまたはコールアカウントは空にできません", "alias_cannot_empty": "別名は空にできません", "company_name_cannot_empty": "会社名は空にできません", "company_address_cannot_empty": "会社の住所は空にできません", "company_phone_number_cannot_empty": "会社の電話番号は空にできません", "id_card_number_cannot_empty": "身分証明書番号は空にできません", "id_card_number_format_wrong": "身分証明書番号の形式が誤っています", "validateTip": "アカウントのセキュリティのため、検証方法をバインドする必要があります。"}, "role": {"role": "役割", "roleName": "役割名", "remark": "備考", "authorization": "認可", "subAdmin": "サブ管理者", "normalUser": "一般ユーザー"}, "menu": {"name": "名前", "parentName": "親メニュー", "icon": "アイコン", "type": "種類", "orderNum": "並べ替え番号", "url": "メニューURL", "perms": "認可識別子", "mainMenu": "メインメニュー", "parentMenuName": "親メニュー", "permsTips": "複数をコンマで区切って入力してください。例：user:list,user:create", "menu": "メニュー", "DirectoryMenu": "ディレクトリメニュー", "button": "ボタン", "HomeDirectoryMenu": "ホームディレクトリメニュー"}, "log": {"user_name_user_action": "ユーザー名 / ユーザー操作", "user_action": "ユーザー操作", "request_method": "リクエストメソッド", "request_parameters": "リクエストパラメータ", "execution_time": "実行時間（ミリ秒）", "ip_address": "IPアドレス", "commandId": "コマンドID", "response_result": "応答結果", "schedule": "進捗", "ReasonForFailure": "失敗理由", "RequestTimedOut": "リクエストがタイムアウトしました", "requestSucceeded": "リクエストが成功しました", "connectionDoesNotExist": "接続が存在しません", "Disconnect": "接続が切断されました", "connectionClosed": "接続が閉じられました", "requestException": "リクエスト例外"}, "group": {"name": "グループ名", "type": "グループの種類", "addingAGroup": "グループの追加", "pleaseEnterAGroupName": "グループ名を入力してください", "addingSubgroup": "サブグループの追加", "pleaseDeleteTheSubgroupsFirst": "最初にサブグループを削除してください"}, "cardDevice": {"deviceName": "デバイス名", "online": "オンライン", "networkType": "ネットワークタイプ", "resolvingPower": "解像度", "programTask": "プログラムタスク", "broadcastTask": "放送タスク", "screenStatus": "スクリーンステータス", "lastOffline": "最終オフライン", "queryTerminalInfo": "端末情報をクエリ", "selectedCard": "選択したカード", "brightness": "<PERSON>るさ", "volume": "音量", "locked": "ロック", "terminalInfoFirst": "まず端末情報を取得してください", "width": "幅", "height": "高さ", "synchronous": "同期", "asynchronous": "非同期", "temperature": "温度", "number": "番号", "NoSim": "SIMカード情報なし", "fireWare": "ファームウェアバージョン"}, "operation": {"settingMode": "設定モード", "connectionLog": "接続ログ", "LEDscreen": "LEDスクリーン", "screenshot": "スクリーンショット", "liveVideo": "ライブビデオ", "screenSwitch": "スクリーン切り替え", "timingSwitch": "タイミング切り替え", "screenBrightness": "スクリーンの明るさ", "autoBrightness": "自動輝度", "timingBrightness": "タイミング輝度", "volumeControl": "音量制御", "timingConfig": "時刻設定", "connConfig": "接続設定", "syncAndAsyncConfig": "同期と非同期の設定", "alarmSwitch": "アラームスイッチ", "onlineUpdate": "オンラインアップデート", "restartSys": "システム再起動", "backgroundPlayback": "後ろで再生", "backupScreenParam": "バックアップ画面パラメータ", "restoreScreenParam": "画面パラメータを復元", "hardwareStatus": "ハードウェアステータス", "manualconfigurationorquery": "手動構成/クエリ", "scheduledconfigurationorquery": "定期構成", "thealias": "エイリアス", "webServerAddress": "WEBサーバーアドレス", "thecompany": "会社", "realtimeaddress": "リアルタイムアドレス", "remove": "クリア", "volumeset": "一括設定", "batchquery": "一括クエリ", "group": "グループ", "exportSIMInfo": "SIM情報のエクスポート", "clearProgram": "プログラムをクリア", "clearTask": "タスクをクリア", "callAddress": "呼び出しアドレスの構成", "alarmConfig": "アラームの構成", "clearBroadcastTask": "放送タスクをクリア", "queryOrClearTiming": "時点をクエリまたはクリア", "queryTiming": "時点をクエリ", "screenControl": "画面制御", "broadcastControl": "放送制御", "monitoringControl": "モニタリング制御", "meteorologicalEnvironmentControl": "気象環境制御", "passengerFlowStatistics": "乗客流量統計", "lightingControl": "照明制御", "setSIPServerAddress": "SIPサーバーアドレスの設定", "getSIPServerAddress": "SIPサーバーアドレスの取得", "SIPServerAddress": "SIPサーバーアドレス", "AlarmEquipmentMacAddress": "アラム機器MACアドレス", "AlarmEquipmentIpAddress": "アラーム機器IPアドレス", "SetAlarmAddress": "アラームアドレスの設定", "GetAlarmAddress": "アラームアドレスの取得", "AlarmAddress": "アラームアドレス", "CallAccount": "呼び出しアカウント", "AlarmVolume": "アラーム音量", "LightingLevel": "照明レベル", "LightingSwitch": "照明スイッチ", "Register_SIP_account": "アラームアカウントの登録", "getGspInfo": "GPS情報を取得", "gpsInfo": "GPS情報", "recordingFile": "録音ファイル", "fullSizeScreenshotOfAndroid": "Androidのフルサイズスクリーンショット", "customSwitch": "カスタムスイッチ", "ThirdPartyAdvertising": "第三者広告", "BadPointDetection": "不良点検出", "playerState": "プレーヤーの状態", "NumberOfCardsReceived": "受信カードの数", "sensorDataShare": "センサー データ共有", "dataShare": "データ共有", "dataRefreshCycle": "データ更新周期", "sharedDataKey": "共有データキー", "curFlow": "現在の乗客の流れ", "isEnableCurFlow": "現在の乗客の流れを有効にするかどうか", "flowAddress": "乗客の流れのアドレス", "showLocation": "場所を表示", "showPrefix": "プレフィックスを表示", "leftTop": "左上", "rightTop": "右上", "leftBottom": "左下", "rightBottom": "右下", "curFlowTip": "旅客フロー アドレスが空の場合、プラットフォームのデフォルト アドレスが使用されます", "detectingBadPixels": "不良ピクセルを検出しています", "uploadZip": "Zipをアップロードします", "versionDelete": "バージョン削除です", "hdvancedConfig": "高級仕様です", "hardwareConfig": "ハードウェア構成です", "realTimeSet": "realtime設定です", "networkConfig": "ネットワーク構成です", "clearBroadcast": "放送を消します", "callVolumeControl": "通話音量コントロールです", "queryProgramName": "番組名を調べます", "list": "リストです", "new": "新たに増えます", "oneClickOperateScreen": "ワンクリックで画面をオフにする"}, "tips": {"brightness": "明るさは1-255です", "volume": "ボリュームは0 ~ 15です。", "alarmVolume": "注:ボリュームは1-9です。", "liveVideo": "rtmp、rtspプロトコルをサポートしますliveをインストールしてください", "liveVideo1": "ライブビデオ1：OFF：ライブを停止、ON：ライブを開始", "liveVideo2": "テストアドレス", "screenSwitch": "注：定時をクリアすると、再度スイッチを操作して画面をオフにできます。", "screenTiming": "注：定時スイッチ（conn10.0.5T以上のバージョンでサポート）を確認してください。", "autoBrightness": "注：この機能はCardSystem-v3.6.0以降でサポートされており、感度は0から100の間である必要があります。", "autoBrightness1": "明るさはセンサーデータに基づいて自動的に変化します（conn10.0.5T以上のバージョンでサポート）。", "autoBrightness2": "最大輝度が64のカードの場合、最小輝度は1％または適切な値に構成できます。最大輝度が255のカードの場合、最小輝度は36％以上に構成する必要があります。そうでないと輝度が低くなります。", "timingBrightness": "注：この設定された時間内は設定された輝度であり、時間外はデフォルトの輝度となります。例えば、デフォルトの輝度を80％、設定された輝度を20％とし、時間範囲を8:00-17:00とすると、時間範囲内の輝度は20％になり、それ以外の時間はデフォルトの輝度80％になります。", "manualconfigurationorquery1": "M70およびM80シリーズのカードにのみ適用されます。", "manualconfigurationorquery2": "日付範囲内で同期モードが適用されます；M70およびM80シリーズのカードにのみ適用され、cardSystem5.2.5.6-8バージョン以上", "widthheighterror": "画面の幅と高さは0ピクセルより低くしてはいけません", "noTenteredNotModified": "この項目に入力しない場合、変更されないものとみなされます", "approval": "定義されたプロセスに従って承認を行い、プロセスの順序は数字が小さい順に並びます", "advancedParameter": "注:高度なパラメーター設定インターフェース(conn10.0.5T以上のバージョンがサポートします)", "cardSelected": "カード番号を選択しました", "cardNameSelected": "端末を選択しました", "numberEmpty": "選択したカード番号の数を空にすることはできません。", "progressBar": "注:更新プログレスバーは更新プロセスを示すため、実際の更新結果はカードの実際の状態を基準とします!", "upgradeFeatureSelectCard": "オプションカードがないと、オンラインアップグレード機能が使えません!", "UninstalledSuccessfully": "状態:アンインストール成功しました!", "uninstallFeatureSelectCard": "オプションカードがないので、オンラインアンインストール機能は使えません!", "SelectUninstall": "アンインストールするコンポーネントを選択します。", "selectedNotExist": "選択ラベルエラーは存在しません", "backgroundTips": "素材画像の解像度と画面解像度が一致していないとエラーが発生します。", "releaseTips": "オンラインでないカードはオフラインで送信し、送信に成功します。プログレスバーに返信しません。", "releaseTips1": "プレイヤーログをオンにし、番組送信に成功すると、各カードの再生ログを保存します。", "releaseTips2": "プレーヤーのログを開いた後に、制御カードは番組が存在する時設定の間隔でログをプラットフォームにアップロードします", "SIPAddress": "注:Sipサーバーのアドレスを設定したことがない場合は、Sipアドレスを設定してから、トーク&アラーム機能を使用する必要があります。アドレスを設定してから、コントロールカードを再起動してください。", "SIPAddress1": "入力ボックスは空で、現在のプラットフォームをSIPサーバアドレスまたはカスタムSIPサーバアドレスとして使用します。", "alarmConfig": "プラットフォームのアドレスを通報先に設定することをお勧めします", "CustomCanUseNetworkAddress": "カスタムはネットワークアドレスを使います", "networkAddressWhenCustomizing": "カスタマイズの際はwebアドレスを入力します。", "broadcastTask": "注：定時タスクが設定されていません。デバイスを再起動すると、ブロードキャストは自動的にクリアされ、タスクは保存されません！", "SIPTips": "このアカウントを使用して制御カードとSIP通話を行うことができます。現在のアカウントが登録済みの場合、アカウントは変更のみ可能です", "broadcastTaskRelease": "注：ブロードキャストタスクをリリースする前に、発声機能が使用中でないことを確認してください", "ModifyTerminal": "ヒント：端末エイリアスを変更するには、スマートIoTに切り替えてリストモードにし、変更をクリックします", "senorBrightnessTable": "この機能は、systemCoreバージョン********以上が必要です。使用手順：まず、センサーの感度値を設定し、次に、デフォルトのBrightnessTable.xlsxファイルをEasyBoardにアップロードして変更します。ファイル内の値は変更できますが、ファイルフォーマットを変更しないでください。そうしないと操作に失敗します。ファイルをアップロードする際は、同じ種類のセンサーのカードのみを選択できます。それ以外では失敗します！", "customSwitchTip": "温馨提示：このスイッチを開くと、カスタムスクリーンスイッチが有効になり、元のスクリーンスイッチは無効になります。このスイッチを閉じると、元のスクリーンスイッチが有効になります。疑問がある場合は関係者に確認してください！", "configAdTip": "第三者の広告を接続することを確認しますか？弊社プラットフォームは第三者の接続内容が合法かつ合規であることを保証しませんので、注意して操作してください。", "configAdTip1": "有効化しますか", "configAdTip2": "制御カードを第三者プラットフォームに開放することを確認しますか？", "configAdTip3": "この機能を使用するには、対応するAPKをアップグレードする必要があります。詳細については当社のスタッフにお問い合わせください！", "groupTip": "キャンセルすると、すべてのデバイスが表示されます", "passwordIsWeak": "パスワードが弱すぎます。パスワードには大文字1文字、小文字1文字、数字1文字が含まれている必要があり、長さは少なくとも9文字である必要があります。", "authTip": "スタッフが2-3営業日内に審査し、審査が合格するとプラットフォームを正常に使用できます。", "pointCheckTip": "テストが完了するまでしばらくお待ちください。テストが完了し、正しい不良点検出情報が取得されるために。", "pointCheckCard": "このプランを使用するには、当社の技術スタッフにお問い合わせください", "playLogsExportTip": "ログをエクスポートする際、日付を選択しない場合は当日のログがエクスポートされ、それ以外の場合は指定された日付内のすべてのログがエクスポートされます。", "oneClickOperateScreenTip": "これにより、すべてのオンライン画面が閉じます", "systemDisplay": "システム解像度", "checkAddress": "設定検出アドレス"}, "file": {"name": "ファイル名", "type": "ファイルタイプ", "status": "ファイルステータス", "size": "ファイルサイズ", "UploadProgress": "アップロード進捗", "download": "ダウンロード", "thumbnail": "サムネイル", "checkPending": "審査待ち", "approved": "承認", "auditFailed": "審査失敗", "under_review": "審査中", "examine": "審査", "attachment": "添付ファイルを選択", "attachment1": "ここにクリックまたはドラッグしてアップロード", "auditTime": "審査時間", "file": "ファイル", "ApprovalComments": "承認コメント", "upload": "アップロード", "update": "更新", "toView": "閲覧", "WithoutPermission": "権限なし", "uninstall": "アンインストール", "SerialNumber": "シリアル番号", "OnlineUpdate": "オンライン更新", "TheSize": "サイズ", "VersionLog": "バージョンログ", "LogDetails": "ログの詳細", "Onlineupgrade": "オンラインアップグレード中…", "Waitingupdates": "更新を待っています…", "Allcards": "すべての操作で更新されたカードは更新できませんでした。問題を確認してください。", "DownloadComplete": "ダウンロードが完了し、解凍後に更新されます", "NotSupported": "プログレスバーの表示がサポートされていません、読み込み中…", "UpdateSuccessful": "更新が成功しました！", "UpdateFailed": "更新に失敗しました！", "ReadyDownload": "ダウンロードの準備ができました！", "ConnectionFailed": "接続に失敗しました。デバイスを確認してください。", "ThreeSeconds": "更新が完了しました。3秒後に更新ウィンドウを閉じます。", "YouCanOnlyUploadUpTo5Files": "最大5つのファイルしかアップロードできません", "audio": "オーディオ", "fileOverSize": "ファイルサイズが制限を超えています", "fileLimit": "ファイルはdocx形式のみをサポートしています", "fileVersion": "ファイルのバージョン", "fileLimitPdfAndVideo": "ファイルは PDF と mp4 のみをサポートします"}, "card": {"cardId": "カードID", "setTiming": "タイミング設定", "getTiming": "タイミング照会", "noTiming": "未設定", "timing": "タイミング", "notSpecified": "未指定", "dateType": "日付タイプ", "DateRange": "日付範囲", "startDate": "開始日", "endDate": "終了日", "timeType": "時間タイプ", "timeFrame": "時間範囲", "startTime": "開始時間", "endTime": "終了時間", "SpecifyWeek": "指定曜日", "WeekRange": "曜日の範囲", "PleaseScheduledTask": "定時タスクのタイプを指定してください", "sensitivity": "感度", "Minbrightness": "最小輝度", "defaultBrightness": "デフォルト輝度", "timingBrightness": "タイミング輝度", "timedVolume": "タイミング音量", "defaultVolume": "デフォルト音量", "cardVoltage": "カード電圧", "externalVoltage1": "外部電圧1", "externalVoltage2": "外部電圧2", "externalVoltage3": "外部電圧3", "externalVoltage4": "外部電圧4", "doorOpen": "ドアが開いた", "version": "バージョン", "humidity": "湿度", "temperature": "温度", "smokeWarning": "煙警告", "querySuccessful": "クエリが成功しました、データを表示します", "queryFailed": "クエリが失敗しました、データを表示できません", "screenWidth": "画面の幅（ピクセル）", "screenHeight": "画面の高さ（ピクセル）", "screenAlias": "画面の別名", "genericVersion": "一般的なバージョン", "notChosenCard": "選択されていないカード", "TestVersion": "テストバージョン", "rebootNow": "即時再起動", "monitorTip": "最大6つのモニタ画面の同時再生が可能です、再選択してください", "picture-in-picture": "画中画", "pictureTip": "座標とサイズは画面解像度範囲内であることを確認してください、画中画を再設定する前に閉じてください", "coordinate": "座標", "pictureSize": "画面サイズ", "checkAddressTip": "有効にした後、メディア審査サーバーのアドレスを入力してください。これはデバイス端でメディアコンテンツを審査するために使用されます", "mediaContentReview": "メディアコンテンツの審査", "realtimeReview": "リアルタイム審査", "realtimeReviewTips": "リアルタイム審査を有効にすると、トラフィックが消費されます", "clearPrompt": "プロンプトをクリア", "interval": "インターバル時間"}, "approval": {"auditName": "審査名", "auditType": "審査タイプ", "approvalProcess": "承認プロセス", "Reviewer": "審査者", "order": "順序", "mediaResources": "メディアリソース", "ProgramType": "番組タイプ", "BroadcastMediaResources": "放送メディアリソース", "BroadcastTaskResources": "放送タスクリソース", "noAudit": "審査不要", "approved": "審査通過", "auditFailed": "審査未通過", "select_at_least_one_reviewer": "少なくとも1人の審査者を選択してください", "approver_cannot_blank": "審査者を空にすることはできません", "approval_order_cannot_blank": "審査順序を空にすることはできません", "InsufficientUsers": "ユーザーが不足しています", "clickAudit": "一括審査", "batchReview": "一括審査", "auditMemo": "問題がある場合は調整してから再提出してください。"}, "program": {"program": "プログラム", "type": "プログラムタイプ", "name": "プログラム名", "ordinaryProgram": "通常のプログラム", "insertProgram": "挿入プログラム", "totalSize": "合計サイズ", "state": "リソースステータス", "ProgramList": "プログラムリスト", "ProgramInfo": "プログラム情報", "ComponentProperties": "コンポーネントの属性", "ProgramProperties": "プログラムの属性", "PlaybackMode": "再生方式", "EntryEffects": "入場エフェクト", "DurationMobilizationEffect": "入場エフェクトの長さ (s)", "AppearanceEffects": "出場エフェクト", "DurationAppearanceEffect": "出場エフェクトの長さ (s)", "StartPlaybackTime": "再生開始時間 (s)", "DurationContinuousDisplay": "連続表示時間 (s)", "region": "領域", "upper": "上", "left": "左", "width": "幅", "height": "高さ", "BasicProperties": "基本属性", "background": "背景", "pellucidity": "透明度", "DisplayDackground": "背景を表示", "open": "開く", "close": "閉じる", "BackgroundColor": "背景色", "DisplayHourScale": "時計の時間目盛りを表示", "HourScaleColor": "時間目盛りの色", "ShowMinuteScale": "時計の分の目盛りを表示", "MinuteScaleColor": "分の目盛りの色", "ScaleStyle": "目盛りのスタイル", "IntegerScaleDigitalDisplay": "整数スケールデジタル表示", "PointerStyle": "針のスタイル", "ClockPointerColor": "時計の針の色", "MinutePointerColor": "分針の色", "SecondPointerColor": "秒針の色", "DisplaySecondHand": "秒針を表示", "up": "上", "down": "下", "play": "再生", "times": "回", "PleaseEnterContent": "内容を入力してください", "text": "テキスト", "DigitalClock": "デジタル時計", "analogClock": "アナログ時計", "EnvironmentalMonitoring": "環境モニタリング", "weather": "天気", "Multi-materialWindow": "複数素材ウィンドウ", "html": "html", "weburl": "ウェブサイトURL", "enterTime": "時間を入力してください", "Multi-material": "マルチ素材", "empty": "空にする", "oneLevelUp": "一つ上に", "oneLevelDown": "一つ下に", "layerOnTop": "最前面に配置", "bottomLayer": "最背面に配置", "FullScreen": "フルスクリーンモード", "pageProperties": "ページのプロパティ", "effectiveDate": "有効日", "PlayProperties": "再生プロパティ", "planSchedule": "予定スケジュール", "sun": "日曜日", "one": "一", "two": "二", "three": "三", "four": "四", "five": "五", "six": "六", "clockProperties": "時計のプロパティ", "PleaseSelectATimeZone": "タイムゾーンを選択してください", "year": "年", "month": "月", "day": "日", "hour": "時", "Minute": "分", "Second": "秒", "Week": "曜日", "AM": "AM", "PM": "PM", "fourYears": "四桁年", "12HourClock": "12時間制", "morningAfternoon": "午前/午後", "style": "スタイル", "dateStyle": "日付スタイル", "timeStyle": "時間スタイル", "displayStyle": "表示スタイル", "singleLine": "1行", "Multi-line": "複数行", "fontSettings": "フォント設定", "fontSize": "フォントサイズ", "fontColor": "フォント色", "PlayTime": "再生時間", "specialEffects": "エフェクト", "specificFrequency": "特殊効果周波数です", "blink": "点滅します", "breathe": "呼吸します", "MonitoringProperties": "監視プロパティ", "compensate": "補償", "windSpeed": "風速", "windDirection": "風向", "noise": "ノイズ", "atmosphericPressure": "大気圧です", "rainfall": "雨量です", "radiation": "放射線です", "lightIntensity": "光の強さです", "DisplayMode": "表示モード", "stayLeft": "左寄せ", "Centered": "中央揃え", "KeepRight": "右寄せ", "singleLineScroll": "1行スクロール", "speed": "速度", "ms/pixel": "ミリ秒/ピクセル", "refreshCycle": "更新サイクル", "minute": "分", "fileProperties": "ファイルのプロパティ", "Multi-MaterialBasicProperties": "複数素材の基本プロパティ", "mediaList": "メディアリスト", "SelectedMaterialInformation": "選択された素材情報", "HourMarkColor": "時針の色", "minuteScaleColor": "分刻みの色", "hourHandColor": "時針の色", "minuteHandColor": "分針の色", "pointerColor": "ポインタの色", "backgroundColor": "背景色", "static": "静的", "scroll": "スクロール", "turnPages": "ページめくり", "total": "合計", "Page": "ページ", "preview": "プレビュー", "stopPreview": "プレビューを停止します", "TextEditor": "テキストエディタ", "province": "都道府県", "Multi-material_text": "右側にメディアを追加してください。複数の異なるメディアを追加することができ、LEDディスプレイはリストの順に再生されます。", "streaming": "ストリーミング", "direction": "スクロール方向", "ToTheLeft": "左へ", "upward": "上向き", "ToTheRight": "右へ", "addText": "テキストを追加", "liveStreamAddress": "ライブストリームアドレス", "deviceAddress": "デバイスアドレス", "deviceAddrTip": "デフォルトのアドレスを使用します（https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey）。不用意に変更しないでください", "deviceKey": "デバイスキー", "deviceKeyTip": "デバイスキーをプラットフォームユーザーが指定デバイスに入力し、指定デバイスの人流情報を画面に表示します。", "CustomHTML": "カスタムHTML", "CustomHTMLTip1": "デバイスのシリアル番号", "CustomHTMLTip2": "時間", "CustomHTMLTip3": "今日の総人数", "CustomHTMLTip4": "その時間帯の入場者数", "CustomHTMLTip5": "歴史的な総入場者数", "CustomHTMLTip6": "本日の合計退場者数", "CustomHTMLTip7": "この時間帯の退場者数", "CustomHTMLTip8": "歴史的な総退場者数", "flowStatistics": "人流統計", "weatherTip1": "現在の気温", "weatherTip2": "AQI（大気汚染指数）", "weatherTip3": "当日の日付（リアルタイムの気温を含む）", "weatherTip4": "当日の天気", "weatherTip5": "当日の最高気温", "weatherTip6": "当日の最低気温", "weatherTip7": "当日の風向", "weatherTip8": "当日の風力", "weatherTip9": "当日の天気画像、フォーマット：img-幅-高さ", "weatherTip10": "上記の%{}里のyesterdayは昨日を示し、arr.0は今日を示し、1は明日を示し、2は明後日を示し、3は大後日を示し、4は大大後日を示します", "timeType": "時間タイプ", "timeTypeTip": "変更タイプによりカスタムHTMLが変更されます", "HDMITypeDescription": "HDMIタイプの説明", "HDMIDescription1": "1、HDMIタイプの番組は現在m70シリーズのみサポートされており、送信後にディスプレイの実際の表示状況を確認してください。具体的な表示状況はディスプレイによります。", "HDMIDescription2": "2、実際の表示効果が誤っている場合は、まずHDMIケーブルの挿入状況を確認し、対応するソフトウェアバージョンが正しいかどうかを確認してください。問題がない場合は再送信を試みることができます。現在のコントロールカードがHDMIピクチャインピクチャモードの場合は、同期設定に変更する必要があります。具体的な使用方法については当社の技術スタッフにお問い合わせください。", "text-to-speech": "テキスト読み上げ", "addProgramTips": "通常のプログラムの配信成功後に元のプログラムを上書きし、インターバルプログラムの配信成功後に元のプログラムを上書きせず、インターバル時間内にのみインターバルプログラムを再生します", "enablePlayerLog": "プレーヤーログを有効にしますか", "playLog": "播放ログ", "timeInterval": "時間間隔（分）", "discount": "割引", "isDiscount": "割引するかどうか", "discountText": "各セグメントの割引幅、カンマで区切って入力してください。 例：256,256,128", "segmentation": "セグメンテーション", "PleaseEnterDiscountWidth": "割引幅を入力してください", "PleaseEnterTheCorrectContentFormat": "正しいコンテンツ形式を入力してください", "multi-picture": "マルチ画像", "PleaseSelectPictureVideoSplit": "画像またはビデオを選択して分割してください", "totalWidthDiscountCannotWidth": "割引の総幅は画像の幅を超えることはできません", "sensorsShareData": "センサはデータを共有します", "broadcastSort": "順序付けです", "horizontal": "横に並べる", "verticalRow": "縦に並べます", "discountMode": "割引モデルです", "level": "レベルです", "vertical": "垂直です", "negativeIon": "負酸素イオンです", "zoomIn": "ズームイン", "zoomOut": "ズームアウト", "materialCycle": "素材サイクル", "refreshSec": "更新間隔", "zoom": "ズーム", "offset": "オフセット", "scale": "引き伸ばし"}, "setTime": {"timeZone": "タイムゾーン/時間", "y60Channels": "注：この機能はCardSystem_v5.2.6.3以上のバージョンが必要です！", "theTimeZone": "タイムゾーン", "setUpThe": "設定", "query": "<PERSON><PERSON>リ", "computerTime": "コンピュータ時間に時計を合わせる", "ledTime": "LEDデバイスの現在の時間を確認", "queryFails": "クエリに失敗しました！", "versionCardSystem": "CardSystemのバージョンを確認してください！", "deviceTimeZone": "デバイスのタイムゾーン", "setupFailed": "設定に失敗しました", "setupSuccess": "設定が成功しました", "connectTo485": "485に接続しますか", "calibrationFailure": "キャリブレーションに失敗しました", "successfulCalibration": "キャリブレーションが成功しました", "querySuccessful": "クエリが成功しました", "synchronizationSettings": "同期設定", "model": "モデル", "masterSlave": "マスター/スレーブ", "IdentificationCode": "識別コード", "timeOffset": "時間オフセット（ミリ秒）", "screenBrightness": "画面の明るさ", "volume": "音量", "screenSwitch": "画面の切り替え", "synchronizationInterval": "同期間隔", "lastSynchronousTime": "最終同期時間です", "minTime": "(分/回)", "main": "メイン", "from": "フロム", "masterSlaveMode": "マスタースレーブモード", "NTPpathNull": "NTPパスが空の場合、接続はタイムアウトしますが、設定は成功します！", "serverAddress": "NTPサーバーアドレス", "selectA": "マスタースレーブモードを選択してください", "selectATime": "タイムゾーンを選択してください"}, "synchronous": {"unknownError": "不明なエラー", "doesNotExist": "カードのネットワークの状態がエラーを報告し、CardSystemのバージョンを確認してください", "format": "タイミングを設定していない"}, "home": {"totalNumber": "デバイス総数", "onlineRate": "オンライン率", "number": "数", "brightScreen": "明るい画面率", "operating": "累計操作量", "program": "累計プログラム数", "switchDate": "切り替え日", "month": "月", "Announcement": "お知らせ", "determined": "未定", "programmeStatistics": "プログラム作成統計", "operationStatistics": "ユーザ操作統計", "releasePeople": "公開者", "date": "日付", "noMoreAnnouncements": "もうお知らせはありません！", "show": "プログラム", "by": "総審査数", "operat": "操作数", "operatingSpeed": "操作スピード(ミリ秒)", "Reviewrate": "審査合格率", "statistics": "プログラム公開統計", "cardNumber": "カード番号", "releaseAmount": "公開総数", "successRate": "成功率", "totalSuccess": "成功総数", "successful": "成功", "failure": "失敗", "founder": "作成者", "TotalAverageMilliseconds": "総平均ミリ秒数", "great": "優", "good": "良", "center": "中", "poor": "悪", "NaN": "NaN", "clickRefresh": "更新をクリック", "warningNotice": "警告通知", "temperatureWarning": "温度警告", "humidityWarning": "湿度警告", "voltageWarning": "電圧警告", "voltage1Warning": "外部電圧1警告", "voltage2Warning": "外部電圧2警告", "doorOpenWarning": "扉が開いている警告", "smokeWarning": "煙警告", "unknownWarning": "未知の警告", "temporarilyNoData": "データなし", "showsSentProgram": "プログラムを送信したカードを表示", "announcementDetails": "お知らせの詳細", "policeDetails": "警告の詳細", "untreated": "未処理", "haveDeal": "処理済み", "noMoreCalls": "もう警告はありません！", "refreshSuccessful": "更新に成功しました！", "DynamicLargeScreen": "スマート端末ビジュアル化プラットフォーム", "piece": "個", "EquipmentStatistics": "機器統計", "unitPCS": "単位：個", "PoleStatistics": "端末統計", "ControlStatistics": "制御統計", "UnitTimes": "単位：回", "TotalNumberOfPoles": "端末の総数"}, "announcement": {"titleText": "公告标题/内容", "title": "タイトル", "enterTitle": "公告タイトルを入力してください", "content": "内容", "enterContent": "公告内容を入力してください", "also": "さらに入力可能", "character": "文字"}, "resetPWD": {"resetPassword": "パスワードをリセット", "accountNumber": "アカウントを入力", "repairMethod": "修復方法を選択", "changePassword": "パスワード変更", "success": "成功", "enterResetPassword": "リセットするアカウントを入力してください"}, "police": {"notOpenSettingsNull": "クリックされていない設定は空です", "monitoringItems": "監視項目", "hasBeenOpen": "すでに開いています", "lower": "下限", "ceiling": "上限", "haveSmoke": "煙があります", "openDoorAlarm": "ドアを開けると警報が鳴ります", "turnSmokeAlarm": "煙感知器が作動します", "checkCardSysterm": "操作を解析できません、CardSystermのバージョンを優先してチェックしてください", "isNotOpened": "カードの接続状態が正しくありません", "sureToSetAlarmThresholds": "警報の閾値を設定しますか？", "upperAndLowerEmpty": "既に開かれた監視項目の上限および下限は空にできません", "numEmpty": "既に開かれた監視項目の上限および下限の入力タイプは数値でなければなりません", "upperGreaterLower": "既に開かれた監視項目の上限は下限よりも大きくなければなりません", "materialLibrary": "素材ライブラリ"}, "hardware": {"timeQuery": "バックアップパラメーターに時間がかかります。最初の時間にバックアップファイルが見つからない場合は、後で再試行して更新してください。", "restoreParam": "パラメーターを復元", "backupParameter": "パラメーターのバックアップ", "hardwareStatus": "ハードウェアの状態", "restore": "復元", "akeyBackup": "ワンクリックバックアップ", "backupSuccessful": "バックアップが成功しました", "backupSuccessful1": "現在のバージョンでは進行状況が復元されません", "BackingUp": "バックアップ中… お待ちください", "selectionCard": "カードが選択されていません。ハードウェアパラメーター機能を使用できません", "parameterRecovery": "パラメーターの復元中…", "waitingRecover": "復元を待っています…", "namePackage": "所属カード/パッケージ名", "restoreCancel": "このカードの接続状態が正しくありません。復元がキャンセルされました！", "readyRecovery": "復元の準備中!", "tryAgain": "選択したすべてのカードの接続状態に問題があるため、確認後に再試行してください！", "recoveryComplete": "復元が完了しました！", "afterRecovery": "復元完了後、3秒で更新ウィンドウが閉じます", "Recovering": "復元中…", "timesOut": "一部のカードはパラメーターの復元をサポートしておらず、リクエストがタイムアウトしました。復元が終了しました！"}, "el": {"colorpicker": {"confirm": "確定", "clear": "クリア"}, "image": {"error": "画像の読み込みに失敗しました"}, "table": {"emptyText": "データがありません"}, "pagination": {"合計": "合計", "pagesize": "記事/ページ", "goto": "行く", "pageClassifier": "ページ"}}, "task": {"name": "タスク名", "isCycle": "繰り返し", "cycleIndex": "繰り返し回数", "InfiniteLoop": "無限ループ", "task": "タスク", "type": "タスクの種類", "text": "テキスト内容", "voiceName": "声の名前", "speed": "速度", "pitch": "ピッチ", "femaleVoice": "女性の声", "maleVoice": "男性の声", "textToLanguage": "テキストから音声へ", "media": "メディア", "plays": "再生回数", "selectMedia": "メディアを選択", "TemplateContent": "テンプレートの内容", "ImportTemplate": "テンプレートをインポート", "import": "インポート", "normal": "通常", "faster": "速い", "fast": "高速", "playTypes": "再生方法", "specifyPlays": "再生回数を指定", "specifyPlayTime": "再生時間を指定", "isTiming": "定時を有効にする", "allTime": "全時間帯再生", "inStream": "ストリーム挿入タスク", "normalTask": "通常タスク", "inStreamTask": "ストリーム挿入タスク", "clearInStream": "ストリーム挿入タスクをクリア"}, "lamp": {"poleName": "端末名", "broadcast": "ブロードキャスト", "monitor": "モニター", "environment": "気象環境", "lighting": "照明", "Passenger": "乗客の流れ統計", "longitude": "経度", "latitude": "緯度", "ChooseTargeting": "ターゲティングを選択", "LoadingPositioning": "位置情報の読み込み中", "FailedToGetLocationInformation": "位置情報の取得に失敗しました", "online": "オンライン", "offline": "オフライン", "targetingIsSet": "ターゲティングが設定されています", "targetingNotSet": "ターゲティングが設定されていません", "listMode": "リストモード", "mapMode": "マップモード", "updateTime": "更新時間", "TheSensorIsNot": "今の時間帯にはセンサーが接続されていません", "getMapException": "地図の取得エラー", "NoLocationData": "位置データがありません", "PleaseEnterThePoleNameOrdeviceID": "端末名/デバイスID を入力してください", "radioState": "ラジオ状態", "latAndLngNotEmpty": "緯度と経度が空ではありません", "gpsUploadState": "gpsアップロード"}, "broadcast": {"SIPAddress": "SIPサーバーアドレスを現在のプラットフォームサーバーアドレスとして設定することを確認", "customSIPAddressStart": "次のように設定を確認", "customSIPAddressEnd": "SIPサーバーアドレスとして", "radioState1": "放送のみサポート", "radioState2": "放送およびページングのみサポートで、アラームはサポートされていません", "radioState3": "放送およびアラームのみサポートで、ページングはサポートされていません", "radioState4": "放送、ページング、アラームをサポート", "SIP_account": "SIPアカウント", "multicastAddress": "マルチキャストアドレス", "selectDate": "日付を選択してください", "pauseOrOpenBroadcast": "放送タスクを一時停止/再開する", "broadcastInfo": "放送の詳細", "broadcastProgramState": "放送プログラムの状態", "paused": "一時停止中", "playing": "再生中", "haveProgram": "リアルタイムプログラムがあるかどうか", "playMode": "再生モード", "focusMode": "フォーカスモード", "fallingTone": "フォールバック", "mute": "ミュート", "noProgram": "プログラムがありません", "noBroadcast": "放送がありません"}, "meteorological": {"temperatureSubText": "気温が--の場合、クエリーに異常があるかデータがないことを示します", "illumination": "照度", "humiditySubText": "湿度が--の場合、クエリに異常があるかデータがないことを示します", "noiseSubText": "騒音が--の場合、クエリに異常があるかデータがないことを示します", "windSpeedSubText": "風速が--の場合、クエリに異常があるかデータがないことを示します", "windDirectionSubText": "風向が--の場合、クエリに異常があるかデータがないことを示します", "illuminationSubText": "照度が0の場合、クエリに異常があるかデータがないことを示します", "PM10SubText": "PM10が--の場合、クエリに異常があるかデータがないことを示します", "PM25SubText": "PM2.5が--の場合、クエリに異常があるかデータがないことを示します", "pressureSubText": "気圧が—であれば、異常が発生したり、異常が発生したりします。", "rainFallSubText": "雨量が——の場合は、異常が発生したり、データが殺到したりしています。", "radiationSubText": "放射線が——の場合は、照会の異常や無数のデータを示します。", "pressure": "気圧です", "rainFall": "雨量です", "radiation": "放射線です"}, "bigScreen": {"VideoSurveillance": "ビデオ監視", "DeviceList": "デバイスリスト", "Address": "詳細な住所", "NumberOfOnline": "オンライン数", "OperationLog": "操作ログ", "ProgramPlayStatistics": "番組再生統計", "RequestFailed": "リクエスト失敗"}, "electricity": {"current": "電流", "power": "電力", "electricity": "電力量", "voltage": "電圧", "currentSubText": "電流が0の場合、クエリに異常があるかデータがないことを示します", "powerSubText": "電力が0の場合、クエリに異常があるかデータがないことを示します", "electricitySubText": "電力量が0の場合、クエリに異常があるかデータがないことを示します", "voltageSubText": "電圧が0の場合、クエリに異常があるかデータがないことを示します", "clearData": "データを消去", "clearSuccessfully": "消去成功", "clearFailed": "消去失敗", "cancelClear": "消去をキャンセル", "monthlyElectricity": "月次電力量統計", "exportElectricity": "月次電力量データのエクスポート", "setElectricityTime": "電力量のクエリ周期を設定", "selectTime": "電力量のクエリ周期を選択してください", "tip": "デフォルトのクエリ周期は24時間です。", "electricityData": "電力量データ", "curElectricityTime": "現在の周期"}, "monitor": {"device_ip": "デバイスIP", "port": "ポート", "owning_terminal": "所属端末", "monitorSaveTip": "オンラインデバイスの場合、ユーザー名とパスワードを慎重に変更してください。ユーザー名とパスワードを誤って変更すると、デバイスがオフラインになります。", "Device_name_cannot_be_empty": "デバイス名を空にすることはできません", "Please_enter_the_device_serial_number": "デバイスシリアル番号を入力してください", "monitorSaveTip1": "ユーザー名とパスワードを次のように変更します：ユーザー名", "Split_screen": "分割表示", "PTZ_control": "PTZ制御", "equipment": "装置", "deviceIdIsNotEmpty": "デバイスIDを空にすることはできません", "CommandISExist": "コマンドが存在しません", "notExistOrNotLoggedIn": "デバイスが見つからないか、ログインしていません", "isNotExist": "デバイスが存在しません", "notLoggedIn": "デバイスがログインしていません", "zoom": "ズーム", "aperture": "絞り", "focus": "フォーカス", "screenshot": "スクリーンショット", "noPlayback": "この時間帯には再生がありません", "downloadPlayback": "再生をダウンロード", "selectDateRange": "日付範囲を選択", "tip": "このダウンロードは、カメラの再生履歴をサーバーにダウンロードするものです。しばらくお待ちいただき、このページからローカルにダウンロードしてください。", "normal": "標準カメラ", "humanNumberStatistic": "人数統計", "insideHumanNumber": "集団人数", "traffic": "スマートトラフィック", "cameraType": "カメラタイプ", "monitorSaveTip2": "カメラの機能タイプを次のように変更します：", "openTheAlarm": "アラームを開く", "crowdAlarmThreshold": "人群アラーム閾値", "intervalForSendingEmails": "メール送信間隔", "openTheAlarmTip": "アラームをオンにした後、デバイスはアラームのしきい値を超えてメールを送信する人の数がデバイスに属しているユーザーに送信されます。アラームを起動した後、メールの送信間隔に応じて次のメールを送信します", "offLineOrNotExist": "装置はオフライン、または切断されています", "serialNumber": "カメラのシリアル番号です", "rtmpStreamState": "プッシュ ストリーミングが有効かどうか", "streamCloseTip": "プッシュ ストリームをオフにすると、Web ページで監視画面が表示されなくなります。プッシュ ストリームをオフにするかどうかを確認してください。", "streamOpenTip": "プッシュストリーミングをオンにすると、Webページ上にモニタリング画面が表示されます。この操作により一定のトラフィックが消費されます。プッシュストリーミングをオンにするかどうかを確認してください。"}, "pay": {"contactInformation": "連絡先情報", "addressValue": "香港中環皇后大道1号", "savingsAccountNumber": "預金口座番号", "xixunCompanyValue": "上海熙訊電子科技有限公司", "bankName": "銀行名", "bankNameValue": "香港上海滙豐銀行有限公司", "swiftCode": "国際コード", "tips1": "正常にオンラインにするには、VIP機器を購入してください。", "tips": "ログインに成功した後、有料サービスでVIPを注文すると、機器が正常にオンラインになります。", "totalPrice": "総額", "contactPerson": "連絡先担当者", "contactPersonTips": "連絡先担当者を入力してください。", "telephone": "連絡先電話番号", "telephoneTips": "連絡先電話番号を入力してください。", "address": "会社住所", "addressTips": "会社住所を入力してください。", "companyName": "会社の正式名称", "companyNameTips": "会社の正式名称を入力してください。", "vipDuration": "VIP有効期間/年", "vip4": "極上ダイヤモンドVIP4", "upgradeToPaidVersion": "有料版にアップグレードします", "silverCardVip": "シルバーカードvipです", "goldCardVip": "ゴールドカードvipです", "diamondVip": "ダイヤモンドのvipです", "superVip": "超VIPです", "orderService": "ご註文サービスです", "currentVersion": "現在のバージョンです:", "numberOfTerminals": "端末数です", "TheRemainingAmount": "残りの数です", "ExpireDate": "期限が切れます", "selectVersion": "選択バージョンです", "SelectDuration(years)": "期間(年)を選びます。", "totalOrder": "受注総額です", "submitOrder": "注文書を提出します。", "freeVersion": "無料版です", "price": "値段です", "ThereAreUnpaidOrders": "未払い註文があります。", "ThereIsAnUnpaidOrderPleaseGoToPay": "未払いの註文がありますので、お支払いに行ってください。", "OrderRecord": "ご註文記録です", "100ControlCards": "コントロールカード数100個です", "500ControlCards": "コントロールカード数500枚です", "NumberOfControlCards1500": "コントロールカード数1500枚です", "unpaid": "未払いです", "transactionCreation": "取引作成です", "UnpaidTransactionTimeoutClosed": "未払い取引は時間切れとなります。", "paymentSuccessful": "支払い成功です", "OrderDetails": "註文明細です", "pay": "支払います", "orderNumber": "註文番号です", "Years": "年限です", "PaymentStatus": "支払い状態です", "amount": "金額です", "newPurchase": "新しく購入します", "Renewal": "おかわりです", "upgrade": "アップグレードします", "PayForTheOrder": "註文を支払います。", "ExpectedPeriod": "予定期限です: ", "to": "至ります", "ActualDeadlineIsSubjectToPayment": "実際の期限は支払い基準です。", "cancelOrder": "註文をキャンセルします。", "theOrderWillBeAutomaticallyClosed": "ご註文は15日間有効ですので、お支払いください。14日後にお支払いにならない場合、自動的に註文が停止されます。", "AfterOrderIsPaidSuccessfully": "注:ご註文のお支払いが完了した後、お支払いできます。", "QueryPaymentInformationOnThePage": "支払い情報の検索ページです", "paymentMethod": "支払い方法です", "publicAccount": "公的口座番号です", "AccountName": "戸名:そうですか。", "AccountBank": "口座開設します:", "LinkNumber": "連行番号です:", "account": "帳簿番号です:", "uploadTheBankReceipt": "上記の口座に入金された場合は、銀行リターンをアップロードしてください。", "receivedByMajorBanks": "銀行によって2時間~ 3営業日かかります。", "notifyYouViaSMS": "お支払いを受け次第、スタッフがサービスを開始します。", "UploadBankReceipt": "銀行からの返信をアップロードします", "contactOurSalesStaff": "ご質問がございましたら、弊社の営業担当までご連絡ください。", "NotesForPublicTransfers": "公的送金に関する注意事項です:", "submittedForFinancialReview": "オフライン決済では決済に遅れがあり、財務審査のために銀行に返書を提出する必要があります。", "TransfersFromPersonalAccounts": "箇人口座振替は弊社の公口座に対して箇人普通伝票を発行することしかできません。", "IfTheCompanyAccountIsTransferred": "会社の口座番号を我が社の対公の口座番号に振り込みます企業増値税普通インボイスまたは企業増値専用インボイスを開くことができます。", "uploadTip": "JPGまたはPNGに対応し、ファイルサイズは5M以下です。", "BankCardNumber": "カード番号です", "bankCardNumberWhenTransferring": "振込時のカード番号をお願いします。", "transactionHour": "取引時間です", "CancellationLineItemCannotBeEmpty": "註文のキャンセルを空にすることはできません。", "WaitingForSellerToConfirm": "売り手が確定するまで待ちます", "PurchaseStatus": "購入状態です", "bankReceipt": "銀行からのお返しです", "Serve": "サービスです", "Optional": "オプション", "deadline": "締め切り時間です：", "orderUpdateTips": "註文アップロードの返信やカード番号しか修正できません。その他の情報についてご質問があれば弊社スタッフまでご連絡ください。", "PleaseUploadBankReceipt": "銀行からの返信をアップロードします。", "PleaseEnterBankCardNumber": "カード番号をお願いします。", "NoRelatedOrders": "ご註文はございません", "ErrorUploadingBankReceipt": "銀行の返信エラーをアップロードします", "changeVipStateTip": "現在のユーザのVIPステータスがスーパーVIPに変更された場合、現在のユーザ会社IDですべてのユーザがスーパーVIPになる。"}, "statistic": {"enableHumanStatistic": "客流統計を有効にする", "queryHumanStatisticToday": "今日の客流を照会する", "enter": "入場人数", "exited": "退出人数", "countingMonitoring": "客流統計の監視", "viewChart": "グラフを表示", "currentNumber": "現在の人数", "lineChart": "エリア内の人数統計折れ線グラフ", "areaPeopleNum": "エリア内の人数", "selectHistoricalData": "過去のデータを選択", "sendData": "データを送信", "keyTip": "データ表示機能を有効にすると、鍵が自動生成されます", "isShowHumanNumber": "端末の人流量データ表示機能を既に有効にしましたか", "dataKey": "鍵", "noDataKey": "現在のデバイスには鍵がありません", "clickCopy": "クリックしてコピー", "copySuccess": "コピー完了"}, "alarm": {"alarmNum": "警報番号", "alarmPeople": "警報者", "call": "発呼者", "receive": "受信者", "callTime": "通話時間", "channel": "チャンネル", "dstChannel": "目的地チャンネル", "alarmDeviceInfo": "警報装置情報", "setCallingVolume": "通話音量の設定", "callingVolume": "通話音量", "notEnable": "無効", "setAlarmInfo": "警報装置情報の設定", "sipAccount": "SIPアカウント", "sipServiceAddress": "SIPサーバーアドレス", "sipServicePort": "SIPサーバーポート", "accountState": "アカウント状態", "alarmDeviceNetwork": "警報装置ネットワーク情報", "setNetwork": "ネットワーク状態の設定", "dynamic": "ダイナミック", "static": "スタティック", "gateway": "ゲートウェイ", "subnetMask": "サブネットマスク", "alarmAccount": "警報アカウント", "accountType": "アカウントタイプ", "registerAlarmAccount": "警報装置アカウントの登録", "registerPhoneNumber": "電話アカウントの登録", "accountRule": "口座番号は空にすることはできず、11 桁の数字または文字である必要があります", "account": "アカウント", "batchSettings": "一括設定", "alarmNetworkTip": "警報装置を長押しすると、ヒント音が鳴ります。1回押すとIPが放送され、3回押すと動的と静的IPが切り替わります。", "alarmAddressTip": "警報アドレスを設定する前に、警報装置情報にアカウントなどの情報を設定してください。", "alarmAddressSetTip": "デフォルトの状態では、プラットフォームは警報アドレスをプラットフォームアドレスに設定します。", "defaultState": "デフォルト", "custom": "カスタム", "myPhoneNumber": "自分の電話番号", "calledPhoneNumber": "かける電話番号", "alarmInfoTip": "警報装置情報を設定する前に、アカウント登録してください", "backupCalledPhoneNumber": "バックアップ番号"}, "traffic": {"enableTraffic": "交通情報を有効にする", "eventName": "イベント名", "plateNumber": "ナンバープレート", "plateType": "ナンバープレートの種類", "plateColor": "ナンバープレートの色", "vehicleColor": "車両の色", "vehicleType": "車両の種類", "vehicleSize": "車両のサイズ", "illegalPlace": "違法な場所", "eventTime": "イベント時間", "downloadEventPic": "違反写真をダウンロード", "illegalPic": "違反写真"}, "radar": {"radarSpeed": "レーダー速度計", "radarSetting": "レーダー設定", "fastestCar": "最速の車", "closestCar": "最寄りの車", "setResponseTime": "応答時間を設定", "setOutputTarget": "出力ターゲットを設定", "setMinSpeed": "最小検出速度を設定", "setMaxSpeed": "最大検出速度を設定", "setSensitivity": "感度を設定", "isConnect": "レーダーに接続されているか", "speed": "速度", "parameter": "パラメータ", "speedLimitRange": "速度制限範囲", "addSpeedLimitRange": "速度制限範囲を追加してください", "minSpeed": "最小速度", "maxSpeed": "最大速度", "radarSpeedLimit": "レーダー速度制限"}, "ac": {"apOnlineRate": "APオンライン率", "apOnlineNumber": "オンラインAP数", "apSum": "AP総数", "onlineTerminal": "オンライン端末数", "flowStatistics": "トラフィック統計", "flowStatisticsToday": "本日のトラフィック統計", "name": "AC名", "macAddress": "MACアドレス", "belongArea": "所属エリア", "belongOrganization": "所属組織", "belongProject": "所属プロジェクト", "userOnlineCount": "ユーザーオンライン総数", "acOnlineUserCount": "ACオンラインユーザー数", "upstreamTraffic": "上りトラフィック", "downlinkTraffic": "下りトラフィック", "refresh": "リフレッシュ"}, "userAuth": {"pass": "認証成功", "fail": "認証失敗", "company": "企業認証", "personal": "個人実名認証", "unverified": "未認証", "certificationAudit": "認証審査", "authMode": "認証モード", "authInfo": "認証情報", "enterpriseLicense": "企業法人営業許可証", "OfficialSeal": "企業公印授権証明", "FrontOfIdentityCard": "身分証表面", "ReverseOfIDCard": "身分証裏面", "reason": "理由", "authentication": "認証", "pleaseUploadEnterpriseLicenseOfficialSeal": "企業公印証明書および企業法人営業許可証をアップロードしてください", "updateAuth": "認証を更新します", "uploadAuth": "アップロード認証です"}, "cat1": {"temp": "テンプレート温度", "power": "有効パワー", "electricEnergy": "有効電力", "roadOne": "道路1", "roadTwo": "道路2", "addDevice": "デバイスを追加", "channel": "チャンネル", "colorTemp": "色温", "scheduleTip": "スケジュールは指定されたリアルタイムから開始します。たとえば、設定した時間が12:30の場合、12:30から次の時間まで、単一のライトの状態は設定された明るさと色温度です。ライトをオフにするには、明るさを0に設定できます。"}, "notify": {"NotificationStrategy": "通知戦略", "NotifyTip": "（ユーザーが通知をオンにすると、指定した時間にメールで通知します）", "OfflineNotify": "オフライン通知", "CardNotWorkingNotification": "カードが動作しない通知", "tactics": "戦術（時）", "selectAll": "全て選択", "PleaseSelectNotification": "通知戦略を選択してください", "SendingMailbox": "送信するメールアドレス", "SendingTime": "送信時間", "Username/EmailAddress": "ユーザー名/メールアドレス", "OffLineTime": "オフライン時間", "AbnormalTime": "異常時間", "NumberOfCardsRLastTime": "前回の受信カードの数", "NumberOfCardsReceivedExceptionOccurs": "異常時の受信カードの数"}, "thirdPartyAd": {"enabledThirdPartyAd": "第三方广告接入を有効にする", "thirdPartyUrl": "第三者サービスのURL", "loopTip": "デバイス内の既存の広告と一緒にループ再生し、複数の広告はデバイス内の広告リストに均等に分散されます", "onceTip": "デバイス内の既存の広告に一度に挿入再生し、複数の広告はデバイス内の広告リストに均等に分散されます", "mutexTip": "排他的なループ再生", "adInterval": "広告の間隔", "queryThirdPartyAd": "第三者広告をクエリ", "downloadUrl": "ダウンロードURL", "impression": "再生情報", "playHour": "再生時間帯", "playCount": "再生回数"}, "crossArrow": {"addToCross": "十字矢印に追加", "arrowProgram": "矢印プログラム", "greenArrow": "緑色の矢印", "redArrow": "赤い十字矢印", "closeScreen": "画面を閉じる", "curProgram": "現在のプログラム", "remove": "削除", "batchRemove": "一括削除"}, "manual": {"operationDocument": "操作ドキュメント", "videoTutorial": "ビデオチュートリアル", "resourceManagement": "リソース管理", "manualAndTutorial": "ユーザーマニュアルとビデオチュートリアル", "universal": "ユニバーサル", "fileTips": "同時にアップロードできるのは同じ種類のファイルのみです"}, "myEditor": {"bottomCenter": "下揃え", "topCenter": "上揃え", "verticalCenter": "中央揃え"}, "employee": {"today": "今日", "yesterday": "昨日", "last7Days": "過去7日間", "last30Days": "過去30日間", "lineChart": "折れ線グラフ", "barChart": "棒グラフ", "unitPerson": "単位:人です", "man": "男です", "woman": "女です", "tips": "有効にすると、自動的に乗客の流れが統計されます。有効化に成功すると、「乗客の流れ統計」->「乗客の流れ統計V1」でデバイスを確認できます"}}