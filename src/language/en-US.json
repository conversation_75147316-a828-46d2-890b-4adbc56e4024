{"common": {"PleaseInput": "Please Input ", "PleaseSelect": "Please Select ", "Loading": "Loading", "personalSettings": "Personal Settings", "logOut": "Logout", "query": "Query", "newlyBuild": "New", "add": "Add", "delete": "Delete", "batchDel": "Batch delete", "update": "Update", "state": "State", "operation": "Operation", "tips": "Tips", "info": "Info", "deploy": "Assign", "cancel": "Cancel", "confirm": "Confirm", "areYouSureExit": "Are you sure to exit?", "setSuccess": " Success", "notSet": "not set", "set": "Set", "bindingSuccess": "Binding success", "unbound": "Not bound", "binding": "Binding", "operationSuccessful": "Operation success", "createTime": "CreateTime", "normal": "Normal", "disable": "Disable", "delete_current_option": "Confirm to delete current option", "original": "Original ", "use_iconfont": "Uniformly use SVG Sprite vector icons ", "total": "Total", "supportedTip": "Not support ", "selectDevice": "Select a device", "clickToEnlarge": "View Large Image", "pictureLoadingFailed": "Picture loading failed", "passwordError": "Error", "OKToRestart": "<PERSON><PERSON>", "WaitingForRestart": "Waiting for restart...", "RestartTimeout": " Restart timeout", "modifiedTime": "modify Time", "exportData": "Export data", "submit": "Submit", "modifyTheSuccess": "Modify success", "configuration": "Configuration", "failure": "Failure", "release": "Release", "nextStep": "Next Step", "selectMaterial": "Select", "notSelectedectMaterial": "Note: Export all cards' SIM information if the group is not selected", "exportingSIM": "Export SIM Information", "enterCardPackage": "Please input card /package name...", "note": "Note", "enterPackage": "Please input package name...", "noDetails": "No details", "versionQuery": "Version Query", "CardSysterm5263": "Version query requires CardSysterm5.2.6.3 or above", "uninstallCard": "The online uninstall function cannot be used without a card", "packageName": "Package Name", "versionNumber": "Version Number", "versionIdentifiers": "Version ID", "wrong": "The connection status of this card is wrong, update cancelled!", "upgradeWrong": " All selected card connection status is wrong, please check and try again!", "cardsNotReturn": "Some cards do not support the return of progress bars. The request times out. Upgrade complete.", "updateComplete": "Update complete!", "restartCards": "Are you sure to restart these cards?", "addTiming": "Please add timing", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "brightnessClears": "Manually setting brightness will clears the entire timing brightness and sensitivity", "fullBrightness": "<PERSON>", "screenDuring": "Note: Screen on during the setting time", "removeTiming": "Clear timing", "clearSuccess": "Clear success", "notEnteredNotModified": "If the input box is not entered, it is not modified. If Conn_v11.1.1.1[261]-AIPS-release-2&4 and later versions are not entered, it is set to empty", "Changing": "Change the company ID or Realtime address will make the card offline. Are you sure to change ?", "networkNTP": "Automatic time calibration requires network and NTP address", "Settings": "When the mode is selected for synchronization setting, the unfilled option will be regarded as empty", "SynchronousMode": "Synchronous mode", "zoneEmpty": "The selected time zone cannot be empty!", "synchronousState": "Sync&Async time schedule, default is synchronous mode during setting time!", "beEmpty": "Width and height cannot be less than or equal to 0, and cannot be empty!", "ModifyFailure": "Modify failed", "programmeDetails": "Program Task Details", "showWidth": "Show  width", "showHigh": "Show height", "notOnPlatform": "Query failed, the current program is not on this platform", "allIntervals": "If not select any timing interval when enable timing function, default is select all intervals", "notSelected": "Not selected", "true": "True", "false": "False", "name": "Name", "custom": "Custom", "MaximumNumberOfWords200": "Maximum 200 words", "exportingSIMTips": "No terminal in the current group. Please select other group", "language": "Language", "copy": "Copy", "save": "Save", "saveAndExit": "Save and Exit", "noMoreData": "No more data", "enable": "Enabled", "NotEnabled": "Not Enabled", "AssignUsers": "Assign users", "manual": "Manual", "addMedia": "+Add media", "reviewCurrentUsers": "Please contact us to review current users", "selectGroup": "Select a group", "selectingGroup": "Current Group", "unclassified": "Unclassified", "frontStep": "Previous Step", "search": "Search", "CAT1": "CAT1", "DaHuaCamera": "DaHua Camera", "GBCamera": "GB Camera"}, "screen": {"simInformation": "SIM card information", "networkState": "Network Country", "series": "Series Number", "countries": "Country", "operationName": "Operator", "unknownState": "Unknown State", "noCard": "No card", "PIN": "Locking status, requiring the user's PIN code to unlock", "PUK": "Locking status, requiring the user's PUK code to unlock", "PIN2": "Locking status, requiring network PIN code to unlock", "readyState": "Ready State", "InvalidState": "Invalid state", "subscribe": "Subscribed ID", "choose": "<PERSON><PERSON>", "supportZip": "Support Zip file only!", "selectFile": "Select the file", "releaseTime": "Release time", "versionInformation": "Version Information", "testVersion": "Test version no version details!", "hardwareVersion": "Hardware parameters no version details!", "officialRelease": "Official Release", "testRelease": "Release test version", "hardwareRelease": "Release hardware parameters", "sizeMore": "Size exceed", "OnlyForZIPType!": "Only ZIP type!", "uploadEmpty": "Upload file cannot be empty!", "uploadFile": "Upload file", "fileTips": "Only files in the format of MP3, MP4, gif, png, and jpg can be uploaded. The size of MP3 and picture cannot exceed 100M, and the size of MP4 cannot exceed 150M", "fileTips1": "Only files in MP3 format can be uploaded, and the size cannot exceed 20M", "picture": "Picture", "video": "Video", "picturesOrVideos": "Upload media file type can only be picture or video!", "my": "my", "pending": "Pending", "pageImprovement": "Page Improvement", "errorDetails": "Error details", "null": "<PERSON><PERSON>", "showProgress": "Program sending progress", "sendSuccess": "Send Success", "sendSuccessOffline": "Offline programs have been sent, and programs will be automatically sent within 72 hours when the control card goes online", "failedProgress": "Get progress failed", "timesOut": "Obtaining progress times out", "selectCard": "Please select the control card", "reviewDetails": "Review  details", "satelliteNumber": "Satellite number", "autoBrightnessTable": "AutoBrightnessTable", "senorType": "Sensor Type", "setAutoBrightnessTable": "Set AutoBrightnessTable", "getAutoBrightnessTable": "Query Auto Brightness Table", "default255BrightnessTable": "Default 255 BrightnessTable", "customizeBrightnessTable": "Customize Brightness Table", "otherSwitch": "Other Switch", "customSwitchFunction": "Custom Switch Function", "programPlayDetail": "Program Play Details", "programPlayStatistic": "Program play statistics", "programPlaySum": "Total number of programs played", "exportPlayLog": "Export PlayLog", "log": "log", "networkPort": "Network Port", "clickToCheck": "Click detection", "getPointCheckInfo": "Get point check information", "imageSize": "Image Size", "badPointNum": "Number of bad points", "queryPlayerState": "Query Player State", "playerStateRep": {"1": "initialization", "2": "Timed program ends", "3": "No programs to play", "4": "Delete show", "5": "Processing the show", "6": "No information yet", "7": "The program may be wrong", "8": "Screen Off", "9": "The fixed-point program is not within the range"}, "fullColor": "Full color", "monochrome": "monochrome", "redBeadNumberPoints": "Red bead number of broken points", "greenBeadNumberPoints": "Green bead number of broken points", "blueBeadNumberPoints": "Blue lamp bead number of broken points", "totalNumberOfBadPoints": "Total number of bad points", "redBadPointPosition": "Red light bead bad point coordinates", "greenBadPointPosition": "Green light bead bad point coordinates", "blueBadPointPosition": "Blue light bead bad point coordinates", "badPointPosition": "Bad Point Position", "abscissa": "Abs<PERSON><PERSON>", "ordinate": "Ordinate", "noBadPoint": "No Bad Point", "pointCheckTips": "Bad point detection only supports single card. Please select another card", "receivingCard": "Receiving card", "setRecCardRelaySwitch": "Set the receiver card relay switch", "getRecCardSensorData": "Get the receiving card sensor data", "smoke": "smoke", "smokeless": "smokeless", "openCloseDoor": "Open and close the door", "openDoor": "Open the door", "closeDoor": "close the door.", "relaySwitch": "Relay switch", "levelDetection": "Level detection", "accessHighLevel": "Indicates the access high level", "noDeviceConnected": "Indicates that no device is connected", "firstRoad": "First road", "secondWay": "Second way", "thirdWay": "Third Way", "fourthWay": "Fourth Way", "theFifthRoad": "The Fifth Road", "sensorDataShareTips": "Sensor data sharing only supports a single card, please select again", "hour": "hour", "pointTable": "Point table", "pointTableTips": "Please operate under the guidance of professionals", "pointTableTemplate": "Point table template", "pleaseUploadPointTable": "Please set up the point table first", "networkConfig": "Network Configuration", "hotspot": "Hotspot", "wifiTip": "Enabling WIFI may change the network status and cause offline, please operate with caution", "apTip": "Note: Please use a combination of uppercase and lowercase letters and numbers for the password, and the password length should be between 8-20 characters", "wifiList": "Query WiFi list", "selectWifi": "Please select WIFI", "singleCardOperation": "This function only supports single card operation"}, "sys": {"enable": "enable", "remarks": "Please enter remarks", "authenticationMode": "Select an authentication mode", "emailVerification": "Email address verification", "mobileVerification": "SMS verification", "verify": "Verification", "notOpen": "Did not open", "email": "email", "mobile": "mobile", "whetherAudit": "Audit", "open": "open", "close": "close", "companyId": "Company ID", "same": "The company ID is the same as the founder company ID", "roleEmpty": "The user role cannot be empty", "alarmType": "Alarm type", "alarmTime": "Alarm time", "Iknown": "I have known", "currentBrowser": "The current browser does not support events sent by the server", "notReminded": "Confirmation will not be reminded", "visited": "The page you visited", "find": "There is no", "url": "Please check the url", "previousPage": "Return to previous page", "enterHome": "Enter the home page", "permissionOfTerminalGroups": "Permission Of Terminal Groups", "TheSuperAdminDoesNotRestrictUserGroups": "The Super Administrator Does Not Restrict User Groups", "addOrUpdateAuth": "Add/Modify Authentication", "isDefaultPassword": "Whether to set a default password"}, "login": {"dynamicCodeEntryTips": "The verification code verification mode is not enabled for the current user. Please enable the verification mode first", "login": "login", "passwordLogin": "Password Login", "username": "Account", "password": "Password", "ForgetThePassword": "forget the password", "newPassword": "new password", "confirmPassword": "confirm password", "dynamicCodeEntry": "Dynamic code entry", "pwdNotes": "It is recommended to use a password with more than eight digits", "origin_pwd_incorrect": "The original password is incorrect", "wrong_account_or_password": "Wrong account or password", "account_has_been_locked": "Account has been locked, please contact the administrator", "updateEmail": "update email", "bindEmail": "binding email", "updateMobile": "update mobile number", "bindMobile": "binding mobile number", "sliderRight": "Please click the button to verify", "loading": "Loading", "passwordMore8": "The password length must be greater than 8 and consist of uppercase letters, lowercase letters and numbers", "user4To17": "Username be 4 to 17 char in length and consists of letters or numbers", "clickCodeMailbox": "Click to get the verification code and the message will be sent to the secure mailbox", "clickCodePhone": "Click to get the verification code and the text message will be sent to the secure phone", "authenticationSuccessful": "Authentication is successful", "securityVerification": "Please perform security verification first", "enterVerificationCode": "Please enter the verification code", "UserNameEmailMobile": "Account / Email / Mobile", "ScopeOfAuthority": "Permissions", "superAdministrator": "super administrator", "PermissionDetails": "Permission List", "pleaseContactTheAdministrator": "please contact the administrator"}, "register": {"register": "register", "mobile": "Mobile", "mailbox": "Email", "code": "code", "getCode": "get code", "remind": "It is recommended to use wechat scan code to log in", "back": "go back", "prependResend": "Resend in ", "appendResend": "seconds", "personalInformation": "Personal information", "complete": "complete", "recommendedRegister": "It is recommended to use wechat scan code to register and log in", "enterPassword": "Please enter your password again", "companyId": "Company Id, the control card online binding company Id, can not be changed at will after confirmation", "companyName": "The name of the company", "companyAddress": "The company address", "companyPhone": "The phone company", "readAIPSAgreement": "Please read the AIPS User Agreement first", "readAccepted": "I have read and accepted", "AIPSAgreement": "AIPS User agreement", "registeredSuccessfully": "Registered successfully", "clickJumpOr": "Click the jump or", "loginDisplayed": "The login page is displayed", "readAcceptAIPSAgreement": "Please read and accept the AIPS User Agreement", "youAreRight": "You are right", "resettingVerification": "Before resetting the password, perform security verification", "switchingAuthentication": "Switching authentication Modes", "pleaseSet": "Please set up", "passwordSecurity": "To improve password security, you are advised to use a combination of digits, letters, and characters", "passwordChangedSuccessfully": "Password changed successfully", "verified": "Verified", "idCardNumber": "ID card number", "companyLicense": "Upload the picture of the 《Enterprise Legal Person Business License》 within the valid annual inspection period", "cachet": "Upload the picture of 《Authorization Certificate》 stamped with the company's official seal", "idCardFront": "Upload ID card front image", "idCardReverse": "Upload ID card reverse image", "pleaseReadAndCheckAIPS": "Please read and check the 《AIPS User Agreement》", "personal": "personal", "company": "company", "CertifiProgress": "Certification progress", "waitForAdminAuth": "Waiting for administrator authentication", "DownloadLicenseCertificate": "Download the License Certificate"}, "ChangePWD": {"Change Password": "Change Password", "remind": "Only the user with mobile number or email can modify the password"}, "nav": {"首页": "Home", "智慧屏幕": "Smart Screen", "智慧广播": "Smart Broadcast", "气象环境": "Meteorological", "智慧监控": "Monitor", "客流统计": "Statistics", "客流统计V1": "Statistics V1", "系统管理": "System", "设备管理": "<PERSON><PERSON>", "节目管理": "Program", "媒体库": "Media", "日志管理": "Log", "远程控制日志": "Remote control log", "播放日志": "Play log", "用户管理": "User", "菜单管理": "<PERSON><PERSON>", "角色管理": "Role", "用户日志": "User log", "登录日志": "Login log", "系统日志": "System log", "设备状态": "Device State", "广播任务": "Broadcast Task", "分组管理": "Group", "审批管理": "Examine", "公告管理": "Announcement", "终端列表": "Terminal list", "智慧物联": "Smart IoT", "智慧照明": "Smart Lighting", "CAT1照明": "CAT1 Lighting", "控制卡照明": "<PERSON><PERSON> Lighting", "电能管理": "Electricity", "视频监控": "Video Monitor", "付费服务": "Paid service", "订购服务": "Order service", "订单": "Order", "SIP账号管理": "SIP Account Management", "监控回放": "Monitor Playback", "人群聚集": "Crowds Gather", "订单管理": "Order Management", "报警管理": "Alarm Management", "报警记录": "Alarm Record", "通话记录": "Call Record", "智慧交通": "Smart Traffic", "交通信息": "Traffic Info", "雷达测速": "Radar Speed", "WIFI AC": "WIFI AC", "概览": "Overview", "AC管理": "AC Management", "密码管理": "pwd Management", "认证审核": "Certification Review", "通知策略": "Notification strategy", "通知日志": "Notification log", "十字箭头": "Cross Arrow", "设备白名单": "<PERSON><PERSON>list", "摄像头监控": "Camera Monitoring"}, "validate": {"account_cannot_empty": "Account number cannot be empty", "password_cannot_empty": "Password cannot be empty", "confirm_password_cannot_empty": "Confirm password cannot be empty", "new_pwd_cannot_empty": "New password cannot be empty", "email_cannot_empty": "mailbox cannot be empty", "mobile_cannot_empty": "mobile number cannot be empty", "code_cannot_empty": "code number cannot be empty", "roleName_cannot_empty": "Role name cannot be empty", "menuURL_cannot_empty": "menu URL cannot be empty", "superior_menu_cannot_empty": "Superior menu cannot be empty", "menu_name_cannot_empty": "Menu name cannot be empty", "group_name_cannot_empty": "group name cannot be empty", "group_type_cannot_empty": "group type cannot be empty", "audit_name_cannot_empty": "Audit name cannot be empty", "resource_type_cannot_empty": "Resource type cannot be empty", "status_cannot_be_empty": "Status cannot be empty", "approval_comments_cannot_blank": "Approval comments cannot be blank", "program_name_cannot_empty": "Program name cannot be empty", "the_new_password_is_inconsistent": "The confirmation password is not consistent with the new password", "the_password_is_inconsistent": "The confirmation password is not consistent with the password", "incorrect_email_format": "Incorrect email format", "code_format": "Please enter six characters", "mobile_format": "Wrong format of mobile number", "mobile_code_empty": "The mobile verification code cannot be empty", "email_code_empty": "The email verification code cannot be empty", "company_id_empty": "The company Id cannot be empty", "not_empty": "not empty", "alarmAddress_not_empty": "Alarm address or call account number cannot be empty", "alias_cannot_empty": "Alias cannot be empty", "company_name_cannot_empty": "Company name cannot be empty", "company_address_cannot_empty": "Company address cannot be empty", "company_phone_number_cannot_empty": "Company phone number cannot be empty", "id_card_number_cannot_empty": "Id card number cannot be empty", "id_card_number_format_wrong": "ID card number format is wrong", "validateTip": "For the security of your account, you are now required to bind the verification method."}, "role": {"role": "role", "roleName": "Role Name", "remark": "remark", "authorization": "authorization", "subAdmin": "Sub-administrator", "normalUser": "Regular user"}, "menu": {"name": "name", "parentName": "parentName", "icon": "icon", "type": "Type", "orderNum": "orderNum", "url": "url", "perms": "perms", "mainMenu": "main menu", "parentMenuName": "parent Menu name", "permsTips": "Multiple users are separated by commas, such as: user:list,user:create", "menu": "menu", "DirectoryMenu": "Directory menu", "button": "button", "HomeDirectoryMenu": "Home directory menu"}, "log": {"user_name_user_action": "user name / user action", "user_action": "user action", "request_method": "request method", "request_parameters": "request parameters", "execution_time": "Execution time (MS)", "ip_address": "IP address", "commandId": "Command ID", "response_result": "Response result", "schedule": "Progress", "ReasonForFailure": "Failure Reason", "RequestTimedOut": "Request timed out", "requestSucceeded": "request succeeded", "connectionDoesNotExist": "connection does not exist", "Disconnect": "Disconnect", "connectionClosed": "connection closed", "requestException": "request exception"}, "group": {"name": "group name", "type": "group type", "addingAGroup": "Add a group", "pleaseEnterAGroupName": "Please Enter A Group Name", "addingSubgroup": "Add a group", "pleaseDeleteTheSubgroupsFirst": "Please Delete The Subgroups First"}, "cardDevice": {"deviceName": "<PERSON><PERSON>", "online": "Online", "networkType": "Network type", "resolvingPower": "Resolution", "programTask": "Program task", "broadcastTask": "Broadcast task", "screenStatus": "Screen status", "lastOffline": "Last Offline", "queryTerminalInfo": "Query terminal information", "selectedCard": "selected card", "brightness": "Brightness", "volume": "Volume", "locked": "locked", "terminalInfoFirst": "Please get the terminal information first", "width": "width", "height": "height", "synchronous": "synchronous", "asynchronous": "asynchronous", "temperature": "Temp", "number": "No.", "NoSim": "No SIM card information", "fireWare": "Firmware version"}, "operation": {"settingMode": "Setting mode", "connectionLog": "Connection log", "LEDscreen": "LED screen", "screenshot": "Screenshot", "liveVideo": "Live Video", "screenSwitch": "Screen Switch", "timingSwitch": "Timing switch", "screenBrightness": "Screen Brightness", "autoBrightness": "Auto Brightness", "timingBrightness": "Timing Brightness", "volumeControl": "Volume Control", "timingConfig": "Time&Date", "connConfig": "Connection config", "syncAndAsyncConfig": "Sync And Async Config", "alarmSwitch": "Alarm Switch", "onlineUpdate": "Online Update", "restartSys": "Restart System", "backgroundPlayback": "Player Background", "backupScreenParam": "Backup screen param", "restoreScreenParam": "Restore screen param", "hardwareStatus": "Hardware Parameters", "manualconfigurationorquery": "Manual configuration or query", "scheduledconfigurationorquery": "Scheduled configuration", "thealias": "<PERSON><PERSON>", "webServerAddress": "Web Server Address", "thecompany": "Company", "realtimeaddress": "Realtime address", "remove": "Remove", "volumeset": "Volume set", "batchquery": "Batch query", "group": "Group", "exportSIMInfo": "Export SIM Information", "clearProgram": "Clear Program", "clearTask": "Clear Task", "callAddress": "Call Address", "alarmConfig": "Alarm Config", "clearBroadcastTask": "Clear Broadcast Task", "queryOrClearTiming": "Query or clear timing", "queryTiming": "Query Timing", "screenControl": "Screen Control", "broadcastControl": "Broadcast Control", "monitoringControl": "Monitoring Control", "meteorologicalEnvironmentControl": "Meteorological Environment Control", "passengerFlowStatistics": "Passenger Flow Statistics", "lightingControl": "Lighting Control", "setSIPServerAddress": "Set SIP server address", "getSIPServerAddress": "Get SIP server address", "SIPServerAddress": "SIP Server Address", "AlarmEquipmentMacAddress": "Alarm equipment mac address", "AlarmEquipmentIpAddress": "Alarm equipment ip address", "SetAlarmAddress": "Set alarm address", "GetAlarmAddress": "Get alarm address", "AlarmAddress": "Alarm Address", "CallAccount": "Call Account", "AlarmVolume": "Alarm Volume", "LightingLevel": "Lighting Level", "LightingSwitch": "Lighting Switch", "Register_SIP_account": "Register Alarm Account", "getGspInfo": "query", "gpsInfo": "GPS Information", "recordingFile": "Record File", "fullSizeScreenshotOfAndroid": "Full size screenshot of Android", "customSwitch": "Custom Switch", "ThirdPartyAdvertising": "Third-party advertising", "BadPointDetection": "Bad Point Detection", "playerState": "Player State", "NumberOfCardsReceived": "Number of cards received", "sensorDataShare": "Sensor Data Share", "dataShare": "Data Share", "dataRefreshCycle": "Data Refresh Cycle", "sharedDataKey": "Shared DataKey", "curFlow": "Current passenger flow", "isEnableCurFlow": "Whether to enable the current passenger flow", "flowAddress": "Passenger flow address", "showLocation": "Show location", "showPrefix": "Show prefix", "leftTop": "top left", "rightTop": "top right", "leftBottom": "left bottom", "rightBottom": "right bottom", "curFlowTip": "When the passenger flow address is empty, the platform's default address will be used", "detectingBadPixels": "Detecting bad pixels", "uploadZip": "Upload Zip", "versionDelete": "Version Deletion", "hdvancedConfig": "Advanced Configuration", "hardwareConfig": "Hardware Configuration", "realTimeSet": "RealTime Settings", "networkConfig": "Network Configuration", "clearBroadcast": "Clear Broadcast", "callVolumeControl": "Call Volume Control", "queryProgramName": "Search Program Name", "list": "list", "new": "new", "oneClickOperateScreen": "One-click screen off", "systemDisplay": "System resolution", "checkAddress": "Configure detection address"}, "tips": {"brightness": "Note: the brightness is 1-255", "volume": "Note: the volume is 0-15", "alarmVolume": "Note: the volume is 1-9", "liveVideo": "RTMP and RTSP protocols are supported. Please install live first", "liveVideo1": "Off turns off the live broadcast and on turns on the live broadcast", "liveVideo2": "Test address", "screenSwitch": "Note: clear the timing and re operate the switch screen.", "screenTiming": "Note: query timing switch screen (supported by conn10.0.5t or above)", "autoBrightness": "Note: this function is supported from cardsystem-v3.6.0, and the sensitivity must be 0 to 100", "autoBrightness1": "The brightness is automatically adjusted according to the change of sensor data (supported by conn10.0.5t or above)", "autoBrightness2": "For a card with a maximum brightness of 64, the minimum brightness can be configured as 1 or an appropriate value; For a card with a maximum brightness of 255, the minimum brightness must be configured as 80 or above, otherwise the brightness will be low.", "timingBrightness": "Note: within the set time is the set brightness, and outside the set time is the default brightness.For example, if the default brightness is 80% and the time range is 8:00-17:00, the brightness is 20% within the time range, and the default brightness is 80% at other times!", "manualconfigurationorquery1": "Only applicable to M70 and M80 series cards", "manualconfigurationorquery2": "Date range is synchronous mode;Only applicable to M70 and M80 series cards, CardSystem version 5.2.5.6-8 or later", "widthheighterror": "The width and height of the screen should not be less than 0 pixels", "noTenteredNotModified": "If this item is not entered, this item is not modified", "approval": "Approve according to the defined process. The smaller the process sorting number, the higher the ranking", "Advanced parameter": "Note： Advanced parameter setting interface", "cardSelected": "The card number is selected", "cardNameSelected": "Terminal selected", "numberEmpty": "The number of selected cards cannot be empty", "progressBar": "Update progress bar The update progress bar shows the update process. The actual update result is based on the actual card status！", "upgradeFeatureSelectCard!": "You can't use the online upgrade feature without a select card!", "UninstalledSuccessfully!": "Status: Uninstalled successfully!", "uninstallFeatureSelectCard": "The online uninstall function cannot be used without a select card!", "SelectUninstall": "Select the components to uninstall", "selectedNotExist": "The selected label is incorrect and does not exist", "backgroundTips": "The resolution of the material picture shall be consistent with the screen resolution, otherwise an error will be reported", "releaseTips": "The card that is not online will be sent offline, and the control card will automatically send programs online within 72 hours", "releaseTips1": "Start the player log, the playback log of each card will be saved after the program is successfully sent", "releaseTips2": "After the player log is enabled, the control card will upload the log to the platform at the set interval when there is a program", "SIPAddress": "Note: The Intercom and Alarm functions can only use enabled when the SIP address setup. Please restart controller after setting the SIP address. You can use the existed platform as the SIP server address or customized another one if the input box is empty.", "SIPAddress1": "If the input box is empty, use the current platform as the SIP server address or customize the SIP server address", "alarmConfig": "It is recommended to set the platform address as the alarm address", "CustomCanUseNetworkAddress": "Custom can use network address", "networkAddressWhenCustomizing": "Please enter the network address when customizing", "broadcastTask": "Note: the scheduled task is not configured. After restarting the device, the broadcast will be automatically cleared and the task will not be saved!", "SIPTips": "This account can be used to make SIP calls with the control card. If the current account has been registered, it can only be modified.", "broadcastTaskRelease": "Note: Before posting a broadcast task, please make sure that the shout function is not in use", "ModifyTerminal": "Tip: Modify terminal alias, click Intelligent Iot, switch to list mode, click Modify", "senorBrightnessTable": "This function requires the systemCore version  ******** and above. Use steps: first set the sensor sensitivity value then modify it by uploading the default BrightnessTable.xlsx file in LEDOK Express. The value inside can be changed, but  do not change the file format, otherwise the operation will fail! When uploading files, only cards with the same sensor can be selected; otherwise, it will fail!", "customSwitchTip": "Reminder: When this switch is turned on, the custom screen switch will be enabled, and the original screen switch will be invalid. If this switch is turned off, the original screen switch will take effect. If you have any questions about this, please confirm with the relevant personnel!", "configAdTip": "Whether you are sure to access third-party advertisements, our platform does not guarantee that the third-party access content is legal and compliant, please operate with caution.", "configAdTip1": "Whether to enable", "configAdTip2": "Are you sure you want to open the control card to third party platforms?", "configAdTip3": "This function needs to be upgraded corresponding to APK. For details, please contact our staff!!!", "groupTip": "All devices will be displayed after cancellation", "passwordIsWeak": "Your password is too weak, please change it. The password must contain at least one English uppercase letter, one English lowercase letter, and one number, and the length must be at least 9 characters.", "authTip": "The staff will review within 2-3 working days. After the review is passed, the platform can be used normally.", "pointCheckTip": "After the detection is completed, please wait patiently for a while to ensure that the detection is completed and the correct dead point detection information is obtained", "pointCheckCard": "Please contact our technical staff for the usage plan", "playLogsExportTip": "When exporting logs, if no date is selected, the current day's logs will be exported, otherwise all logs within the specified date will be exported.", "oneClickOperateScreenTip": "This operation will turn off all online screens"}, "file": {"name": "Name", "type": "Type", "status": "Status", "size": "Size", "UploadProgress": "Upload progress", "download": "Download", "thumbnail": "Preview", "checkPending": "check pending", "approved": "Approved", "auditFailed": "Examine failed", "under_review": "Under review", "examine": "examine", "attachment": "Please select an attachment", "attachment1": "Click or drag to the uploaded file", "auditTime": "Examine Time", "file": "file", "ApprovalComments": "Approval comments", "upload": "Upload", "update": "Update", "toView": "To view", "WithoutPermission": "Without permission", "uninstall": "Uninstall", "SerialNumber": "Serial number", "OnlineUpdate": "Online Update", "TheSize": "Size", "VersionLog": "Version log", "LogDetails": "Log details", "Onlineupgrade": "Online upgrade...", "Waitingupdates": "Waiting for updates...", "Allcards": "All cards updated by all operations cannot be updated, please check the problem", "DownloadComplete": "Download complete, unzip after the update", "NotSupported": "Progress bar display is not supported, loading...", "UpdateSuccessful": "Update successful!", "UpdateFailed": "Update failed!", "ReadyDownload": "Ready to download!", "ConnectionFailed": "Connection failed, please check the device!", "ThreeSeconds": "Update completed, close the update widget after three seconds", "YouCanOnlyUploadUpTo5Files": "You can only upload up to 5 files", "audio": "audio", "fileOverSize": "File exceeds limit size", "fileLimit": "File only supports docx format", "fileVersion": "file version", "fileLimitPdfAndVideo": "Files only support pdf and mp4"}, "card": {"cardId": "cardId", "setTiming": "Set Timing", "getTiming": "Get timing", "noTiming": "No timing", "timing": "timing", "notSpecified": "Not specified", "dateType": "dateType", "DateRange": "Date range", "startDate": "startDate", "endDate": "endDate", "timeType": "timeType", "timeFrame": "time range", "startTime": "startTime", "endTime": "endTime", "SpecifyWeek": "Specify week", "WeekRange": "WeekRange", "PleaseScheduledTask": "Please specify the scheduled task type", "sensitivity": "Sensitivity", "Minbrightness": "<PERSON>", "defaultBrightness": "default Brightness", "timingBrightness": "timing Brightness", "timedVolume": "Timed volume", "defaultVolume": "default volume", "cardVoltage": "Card voltage", "externalVoltage1": "External voltage 1", "externalVoltage2": "External voltage 2", "externalVoltage3": "External voltage 3", "externalVoltage4": "External voltage 4", "doorOpen": "The door was open", "version": "version", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "temperature": "Temperature", "smokeWarning": "Smoke warning", "querySuccessful": "Query successful, show you the data", "queryFailed": "The query failed and cannot show you the data", "screenWidth": "Screen width (pixels)", "screenHeight": "Screen height (pixels)", "screenAlias": "Screen alias", "genericVersion": "General version", "notChosenCard": "Not chosen card", "TestVersion": "Test version", "rebootNow": "Reboot now", "monitorTip": "A maximum of six monitoring screens can be played at the same time. Select another screen", "picture-in-picture": "Picture", "pictureTip": "The coordinates and width and height do not exceed the visual range of screen resolution. To reset the Settings, you need to close the picture in picture first", "coordinate": "coordinate", "pictureSize": "Picture size", "checkAddressTip": "After enabling it, fill in the address of the media review server, which is used for reviewing media content on the device side. If the address is empty, use the default address", "mediaContentReview": "Media content review", "realtimeReview": "Real-time review", "realtimeReviewTips": "Enabling real-time review will consume traffic", "clearPrompt": "Clear prompt", "interval": "Interval time"}, "approval": {"auditName": "Examine name", "auditType": "Examine type", "approvalProcess": "Approval process", "Reviewer": "Reviewer", "order": "Sequence", "mediaResources": "Media resources", "ProgramType": "Program Type", "BroadcastMediaResources": "Broadcast media resources", "BroadcastTaskResources": "Broadcast task resources", "noAudit": "No audit required", "approved": "Approved", "auditFailed": "Examine failed", "select_at_least_one_reviewer": "Select at least one reviewer", "approver_cannot_blank": "Approver cannot be blank", "approval_order_cannot_blank": "Approval sequence cannot be blank", "InsufficientUsers": "Insufficient users", "clickAudit": "One click audit", "batchReview": "Batch review", "auditMemo": "There is a problem with the program, please adjust it before submitting."}, "program": {"program": "Program", "type": "Program Type", "name": "Program name", "ordinaryProgram": "ordinary Program", "insertProgram": "insert Program", "totalSize": "Size", "state": "Resource status", "ProgramList": "Program list", "ProgramInfo": "Program info", "ComponentProperties": "Component properties", "ProgramProperties": "Program properties", "PlaybackMode": "Playback mode", "EntryEffects": "Entry effects", "DurationMobilizationEffect": "Duration of mobilization effect (s)", "AppearanceEffects": "Appearance effects", "DurationAppearanceEffect": "Duration of appearance effect (s)", "StartPlaybackTime": "Start playback time (s)", "DurationContinuousDisplay": "Duration of continuous display (s)", "region": "region", "upper": "upper", "left": "left", "width": "width", "height": "height", "BasicProperties": "Basic properties", "background": "background", "pellucidity": "pellucidity", "DisplayDackground": "Display background", "open": "open", "close": "close", "BackgroundColor": "Background color", "DisplayHourScale": "Display hour scale", "HourScaleColor": "Hour scale color", "ShowMinuteScale": "Show minute scale", "MinuteScaleColor": "Minute scale color", "ScaleStyle": "Scale style", "IntegerScaleDigitalDisplay": "Integer scale digital display", "PointerStyle": "Pointer style", "ClockPointerColor": "Clock pointer color", "MinutePointerColor": "Minute pointer color", "SecondPointerColor": "Second pointer color", "DisplaySecondHand": "Display second hand", "up": "up", "down": "down", "play": "play", "times": "times", "PleaseEnterContent": "Please enter content", "text": "text", "DigitalClock": "Digital clock", "analogClock": "analog clock", "EnvironmentalMonitoring": "Environmental", "weather": "weather", "Multi-materialWindow": "Multi window", "html": "webpage", "weburl": "website", "enterTime": "Please enter the time", "Multi-material": "Multi-material", "empty": "clearup", "oneLevelUp": "one level up", "oneLevelDown": "one level down", "layerOnTop": "layer on top", "bottomLayer": "bottom layer", "FullScreen": "Full screen", "pageProperties": "page properties", "effectiveDate": "effective date", "PlayProperties": "Play properties", "planSchedule": "plan schedule", "sun": "Sun.", "one": "Mon.", "two": "<PERSON><PERSON>.", "three": "Wed.", "four": "<PERSON><PERSON>.", "five": "Fri.", "six": "Sat.", "clockProperties": "clock properties", "PleaseSelectATimeZone": "Please select a time zone", "year": "Year", "month": "Month", "day": "Day", "hour": "Hour", "Minute": "Minute", "Second": "Second", "Week": "Week", "AM": "AM", "PM": "PM", "fourYears": "four digital years", "12HourClock": "12 hours system", "morningAfternoon": "AM/PM", "style": "style", "dateStyle": "date style", "timeStyle": "time style", "displayStyle": "display style", "singleLine": "single line", "Multi-line": "Multi-line", "fontSettings": "font setup", "fontSize": "font size", "fontColor": "font color", "PlayTime": "Play time", "specialEffects": "Special Effects", "specificFrequency": "Specific Frequency", "blink": "blink", "breathe": "breathe", "MonitoringProperties": "Monitoring properties", "compensate": "compensate", "windSpeed": "Wind Speed", "windDirection": "Wind Direction", "noise": "Noise", "atmosphericPressure": "Atmospheric Pressure", "rainfall": "Rainfall", "radiation": "Radiation", "lightIntensity": "Light intensity", "DisplayMode": "Display mode", "stayLeft": "stay left", "Centered": "Centered", "KeepRight": "Keep Right", "singleLineScroll": "single line scroll", "speed": "speed", "ms/pixel": "ms/pixel", "refreshCycle": "refresh cycle", "minute": "minute", "fileProperties": "file properties", "Multi-MaterialBasicProperties": "Multi-Material Basic Properties", "mediaList": "media list", "SelectedMaterialInformation": "Selected material information", "HourMarkColor": "Hour Mark Color", "minuteScaleColor": "minute scale color", "hourHandColor": "hour hand color", "minuteHandColor": "minute hand color", "pointerColor": "pointer color", "backgroundColor": "background color", "static": "static", "scroll": "scroll", "turnPages": "turn pages", "total": "total", "Page": "Pages", "preview": "preview", "stopPreview": "Stop Preview", "TextEditor": "Text Editor", "province": "province", "Multi-material_text": "Please add media on the right, you can add multiple different media, and the LED display will play in the order of the list.", "streaming": "Streaming", "direction": "Direction of roll", "ToTheLeft": "To the left", "upward": "upward", "ToTheRight": "To the right", "addText": "Add text", "liveStreamAddress": "Live stream address", "deviceAddress": "Device address", "deviceAddrTip": "Default to the current address (https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey), please do not modify", "deviceKey": "Device key", "deviceKeyTip": "Device key platform The user enters the specified device key, delivers the program, and displays the flow information of the specified device on the screen.", "CustomHTML": "Custom HTML", "CustomHTMLTip1": "is the serial number of the device", "CustomHTMLTip2": "is the time", "CustomHTMLTip3": "is the total number of people entered today", "CustomHTMLTip4": "is the number of people entered in this hour", "CustomHTMLTip5": "is the total number of people who have entered", "CustomHTMLTip6": "is the total number of people who have left today", "CustomHTMLTip7": "is the number of persons leaving within the hour period", "CustomHTMLTip8": "is the total number of people who have left during this hour", "flowStatistics": "Flow statistics", "weatherTip1": "Current temperature", "weatherTip2": "AQI (Air Quality Index)", "weatherTip3": "Date of the day (including real-time temperature)", "weatherTip4": "Weather of the day", "weatherTip5": "Maximum temperature of the day", "weatherTip6": "Minimum temperature of the day", "weatherTip7": "Wind direction of the day", "weatherTip8": "Day wind", "weatherTip9": "Weather picture of the day, format: img-width-height", "weatherTip10": "${} is the same day, 1 is tomorrow, 2 is the day after tomorrow, 3 is the day after tomorrow, and 4 is the day after tomorrow", "timeType": "Time type", "timeTypeTip": "Changing the type changes the custom HTML", "HDMITypeDescription": "HDMI Type Description", "HDMIDescription1": "1. Currently, only m70 series of HDMI programs are supported. Please check the actual display of the screen after the HDMI program is successfully sent.", "HDMIDescription2": "2. If the actual display effect is incorrect, check whether the HDMI cable is properly inserted or whether the corresponding software version is incorrect. If the current control card is in HDMI picture-in-picture mode, it needs to be set to sync before it can be reset. Please contact our technical personnel for the specific use plan.", "text-to-speech": "text-to-speech", "addProgramTips": "The original program will be covered after the successful delivery of ordinary programs, while the original program will not be covered after the successful delivery of episodic programs. Only episodic programs will be played during the episodic period", "enablePlayerLog": "Whether to enable the player log", "playLog": "Play log", "timeInterval": "Time interval (minutes)", "discount": "Length Per Section", "isDiscount": "Split or not", "discountText": "The length of each section is separated by commas. For example: 256,256,128", "segmentation": "segmentation", "PleaseEnterDiscountWidth": "Please enter discount width", "PleaseEnterTheCorrectContentFormat": "Please enter the correct content format", "multi-picture": "multi-picture", "PleaseSelectPictureVideoSplit": "Please select a picture or video to split", "totalWidthDiscountCannotWidth": "The total length of the discount cannot be greater than the size of the material", "sensorsShareData": "Sensor Shared Data", "broadcastSort": "Broadcast Sort", "horizontal": "Horizontal", "verticalRow": "Vertical Row", "discountMode": "Discount Mode", "level": "level", "vertical": "vertical", "negativeIon": "Negative oxygen ion", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "materialCycle": "Material Cycle", "refreshSec": "Refresh Interval", "zoom": "Zoom", "offset": "Offset", "scale": "<PERSON><PERSON><PERSON>"}, "setTime": {"timeZone": "Time Zone/Time", "y60Channels": "This feature requires cardSystem_V5.2.6.3 to support transparent channels!", "theTimeZone": "time zone", "setUpThe": "Set up the", "query": "The query", "computerTime": "Calibrate clock to computer time", "ledTime": "Example Query the current time of an LED device", "queryFails": "The query fails!", "versionCardSystem": "Please check the version of CardSystem!", "deviceTimeZone": "The device time zone", "setwotupFailed": "Setup failed", "setupSuccess": "Set up the success", "connectTo485": "Connect to 485", "calibrationFailure": "Calibration failure", "successfulCalibration": "Successful calibration", "querySuccessful": "The query is successful", "synchronizationSettings": "Synchronization Settings", "model": "model", "masterSlave": "The master/slave", "IdentificationCode": "Identification code", "timeOffset": "Time offset (ms)", "screenBrightness": "The screen brightness", "volume": "The volume", "screenSwitch": "The screen switch", "synchronizationInterval": "Synchronization Interval", "lastSynchronousTime": "Last Synchronous Time", "minTime": "(min/time)", "main": "The main", "from": "slave", "masterSlaveMode": "A master-slave mode", "NTPpathNull": "If the NTP path is set to empty, the connection times out, but the setting succeeds!", "serverAddress": "NTP　Server address", "selectA": "Select a master-slave mode", "selectATime": "Please select a time zone"}, "synchronous": {"unknownError": "An unknown error", "doesNotExist": "The network status of the card is wrong , please check the version of CardSystem", "format": "no timing"}, "home": {"totalNumber": "The total number of devices", "onlineRate": "Online rate", "number": "number", "brightScreen": "Bright screen rate", "operating": "Accumulated operating quantity", "program": "Cumulative program quantity", "switchDate": "Switch the date", "month": "Month", "Announcement": "Announcement", "determined": "To be determined", "programmeStatistics": "Program Creation statistics", "operationStatistics": "User operation statistics", "releasePeople": "Release person", "date": " date", "noMoreAnnouncements": "No more announcements!", "show": "Show the amount", "by": "By the amount", "operat": "Operating quantity", "operatingSpeed": "Operation speed (ms)", "Reviewrate": "Review the pass rate", "statistics": "Program release statistics", "cardNumber": "The card number", "releaseAmount": "Release the amount", "successRate": "The success rate", "totalSuccess": "Total Success", "successful": "successful", "failure": "failure", "founder": "founder", "TotalAverageMilliseconds": "Total average milliseconds", "great": "great", "good": "good", "center": "center", "poor": "poor", "NaN": "NaN", "clickRefresh": "Click the refresh", "warningNotice": "Warning notice", "temperatureWarning": "Temperature warning", "humidityWarning": "Humidity warning", "voltageWarning": "Card voltage warning", "voltage1Warning": "External voltage 1 warning", "voltage2Warning": "External voltage 2 warning", "doorOpenWarning": "The door open warning", "smokeWarning": "There's a smoke warning", "unknownWarning": "Unknown alarm", "temporarilyNoData": "Temporarily no data", "showsSentProgram": "Shows the card that sent the program", "announcementDetails": "Announcement  details", "policeDetails": "alarm details", "untreated": "untreated", "haveDeal": "processed", "noMoreCalls": "No more alarms!", "refreshSuccessful": "Refresh successful!", "DynamicLargeScreen": "Smart  Visualization Platform", "piece": "piece", "EquipmentStatistics": "Equipment Statistics", "unitPCS": "unit: PCS", "PoleStatistics": "Terminal Statistics", "ControlStatistics": "Control Statistics", "UnitTimes": "Unit: times", "TotalNumberOfPoles": "Total number of terminals"}, "announcement": {"titleText": "Announcement title/content", "title": "Title", "enterTitle": "Please enter the announcement title", "content": "Content", "enterContent": "Please enter the announcement content", "also": "You can type", "character": "numbers of character"}, "resetPWD": {"resetPassword": "reset password", "accountNumber": "account number", "repairMethod": "Repair method", "changePassword": "Change Password", "success": "success", "enterResetPassword": "Please enter the account to reset the password"}, "police": {"notOpenSettingsNull": "If you do not click open, the Set<PERSON><PERSON> will be null", "monitoringItems": "Monitoring items", "hasBeenOpen": "Has been open", "lower": "lower", "ceiling": "ceiling", "haveSmoke": "Open  smoke", "openDoorAlarm": "Open the door alarm", "turnSmokeAlarm": "Turn on the smoke alarm", "checkCardSysterm": "Unable to parse operation, please check CardSysterm version first", "isNotOpened": "is not opened", "sureToSetAlarmThresholds": "Are you sure to set alarm thresholds?", "upperAndLowerEmpty": "The upper and lower limits of enabled monitoring items cannot be empty", "numEmpty": "For enabled monitoring items, the upper and lower limits must be digits", "upperGreaterLower": "The upper limit of enabled monitoring items must be greater than the lower limit", "materialLibrary": "Material library"}, "hardware": {"timeQuery": "Backup parameters take time, if you cannot query the backup file at first time, please try to refresh later!", "restoreParam": "Restore Parameters", "backupParameter": "Backup parameter", "hardwareStatus": "The hardware status", "restore": "restore", "akeyBackup": "A key backup", "backupSuccessful": "The backup successful", "backupSuccessful1": "The current version does not reply to the progress", "BackingUp": "Backing up...Please wait a moment", "selectionCard": "The hardware parameters function cannot use without a selection card", "parameterRecovery": "Parameter recovery...", "waitingRecover": "Waiting to recover...", "namePackage": "Controller or Package Name", "restoreCancel": "The connection status of this card is wrong, restore cancel!", "readyRecovery": "Ready for recovery!", "tryAgain": "All the cards that have been selected to recover have problems in connection, please check and try again!", "recoveryComplete": "Recovery complete!", "afterRecovery": "After recovery, close the update window in three seconds", "Recovering": "Recovering...", "timesOut": "Some cards do not support parameter recovery, the request times out, the recovery ends!"}, "el": {"colorpicker": {"confirm": "confirm", "clear": "clear"}, "image": {"error": "Picture loading failed"}, "table": {"emptyText": "no data"}, "pagination": {"total": "Total", "pagesize": "Items/page", "goto": "Go to", "pageClassifier": "Page"}}, "task": {"name": "taskName", "isCycle": "cycle or not", "cycleIndex": "cycle time", "InfiniteLoop": "Infinite loop", "task": "task", "type": "task type", "text": "text content", "voiceName": "voiceName", "speed": "speed", "pitch": "pitch", "femaleVoice": "Female voice", "maleVoice": "Male voice", "textToLanguage": "Text to language", "media": "Media", "plays": "Playback times", "selectMedia": "Select media", "TemplateContent": "Template content", "ImportTemplate": "Import template", "import": "import", "normal": "normal", "faster": "faster", "fast": "fast", "playTypes": "play mode", "specifyPlays": "Specify the number of plays", "specifyPlayTime": "Specify play time", "isTiming": "timed task or not", "allTime": "All time period", "inStream": "In-stream Task or not", "normalTask": "Common Tasks", "inStreamTask": "In-stream Task", "clearInStream": "Clear in-stream task"}, "lamp": {"poleName": "<PERSON><PERSON>", "broadcast": "BRD", "monitor": "Monitor", "environment": "MET", "lighting": "Lighting", "Passenger": "PFM", "longitude": "longitude", "latitude": "latitude", "ChooseTargeting": "Choose targeting", "LoadingPositioning": "Loading positioning", "FailedToGetLocationInformation": "Failed to get location information", "online": "Online", "offline": "Offline", "targetingIsSet": "Geolocated", "targetingNotSet": "No geolocated", "listMode": "List Mode", "mapMode": "Map Mode", "updateTime": "update time", "TheSensorIsNot": "The sensor is not connected to the current time period", "getMapException": "get map exception", "NoLocationData": "No GPS data", "PleaseEnterThePoleNameOrdeviceID": "Please enter terminal name/device ID", "radioState": "Radio state", "latAndLngNotEmpty": "Latitude and longitude cannot be empty", "gpsUploadState": "gps upload"}, "broadcast": {"SIPAddress": "Confirm to take the current platform server address as the SIP server address", "customSIPAddressStart": "Confirm to set", "customSIPAddressEnd": "as the SIP server address", "radioState1": "Broadcast only", "radioState2": "Support broadcast, shout, but not alarm", "radioState3": "Support broadcast and alarm, but not for shout", "radioState4": "Support broadcast, alarm, shout", "SIP_account": "SIP account", "multicastAddress": "Multicast address", "selectDate": "Please select a date", "pauseOrOpenBroadcast": "Pause/Open Broadcast Task", "broadcastInfo": "Broadcast Details", "broadcastProgramState": "Broadcast Program State", "paused": "Paused", "playing": "Playing", "haveProgram": "Whether there is a real-time program", "playMode": "Play Mode", "focusMode": "Focus Mode", "fallingTone": "fallingTone", "mute": "Mute", "noProgram": "No program", "noBroadcast": "No broadcast"}, "meteorological": {"temperatureSubText": "When the temperature is --, it means that the query is abnormal or there is no data", "Illuminance": "Illuminance", "humiditySubText": "When the humidity is --, it means that the query is abnormal or there is no data", "noiseSubText": "When the noise is --, it means that the query is abnormal or there is no data", "windSpeedSubText": "When the wind speed is --, it means that the query is abnormal or there is no data.", "windDirectionSubText": "When the wind direction is --, it means that the query is abnormal or there is no data.", "illuminationSubText": "When the illumination is 0, it means that the query is abnormal or there is no data", "PM10SubText": "When PM10 is --, it means that the query is abnormal or there is no data", "PM25SubText": "When PM2.5 is --, it means that the query is abnormal or there is no data", "pressureSubText": "When pressure is --, it means that the query is abnormal or there is no data", "rainFallSubText": "When rainfall is --, it means that the query is abnormal or there is no data", "radiationSubText": "When radiation is --, it means that the query is abnormal or there is no data", "pressure": "Pressure", "rainFall": "Rainfall", "radiation": "Radiation"}, "bigScreen": {"VideoSurveillance": "Video Surveillance", "DeviceList": "Device List", "Address": "Address", "NumberOfOnline": "Number of online", "OperationLog": "Operation log", "ProgramPlayStatistics": "Program Play Statistics", "RequestFailed": "Request failed"}, "electricity": {"current": "Current", "power": "Power", "electricity": "Electricity", "voltage": "Voltage", "currentSubText": "When the current is 0, it means that the query is abnormal or there is no data", "powerSubText": "When the power is 0, it means that the query is abnormal or there is no data", "electricitySubText": "When the electricity is 0, it means that the query is abnormal or there is no data", "voltageSubText": "When the voltage is 0, it means that the query is abnormal or there is no data", "clearData": "Clear data", "clearSuccessfully": "cleared successfully", "clearFailed": "Clear failed", "cancelClear": "<PERSON><PERSON> clear", "monthlyElectricity": "Monthly Electricity Statistics", "exportElectricity": "Export monthly electricity data", "setElectricityTime": "Set the query range", "selectTime": "Please select the period of energy inquiry", "tip": "The default query period is 24 hours.", "electricityData": "Electricity Data", "curElectricityTime": "Current cycle"}, "monitor": {"device_ip": "Device IP", "port": "port", "owning_terminal": "Terminal ID", "monitorSaveTip": "Online devices, please be careful to modify the user name and password. If the user name and password are modified incorrectly, the device will be disconnected.", "Device_name_cannot_be_empty": "Device name cannot be empty", "Please_enter_the_device_serial_number": "Please enter the device serial number", "monitorSaveTip1": "Change the username and password to: username", "Split_screen": "Split Screen", "PTZ_control": "PTZ control", "equipment": "Equipment", "deviceIdIsNotEmpty": "Device ID cannot be empty", "CommandISExist": "command does not exist", "notExistOrNotLoggedIn": "Device not found or device not logged in", "isNotExist": "device does not exist", "notLoggedIn": "<PERSON><PERSON> not logged in", "zoom": "zoom", "aperture": "aperture", "focus": "focus", "screenshot": "screenShot", "noPlayback": "No playback for this time period", "downloadPlayback": "Download the Playback", "selectDateRange": "Select time period", "tip": "This download is to download the playback record in the camera to the server, please wait for a certain period of time before downloading from this page to the local.", "normal": "Normal camera", "humanNumberStatistic": "People Statistics", "insideHumanNumber": "Crowd gathering", "traffic": "Smart Traffic", "cameraType": "Camera Type", "monitorSaveTip2": "Modify the camera function type to:", "openTheAlarm": "Open the alarm", "crowdAlarmThreshold": "Crowd alarm threshold", "intervalForSendingEmails": "Interval for sending emails", "openTheAlarmTip": "After the alarm is enabled, an email will be sent to the owner of the device for the number of people exceeding the alarm threshold. After the email alarm is triggered, the next email will be sent according to the sending interval. The mailbox is the registered mailbox of the owner", "offLineOrNotExist": "The device is offline or disconnected", "serialNumber": "Serial Number", "rtmpStreamState": "Whether to enable streaming", "streamCloseTip": "After turning off streaming, you will not be able to see the monitoring screen on the web page, please confirm whether to turn off streaming", "streamOpenTip": "After turning on streaming, you can see the monitoring screen on the web page, this operation will consume a certain amount of traffic, please confirm whether to turn on streaming"}, "pay": {"contactInformation": "Contact information", "addressValue": "No. 1 Queen's Road Central, Central, Hong Kong", "savingsAccountNumber": "Savings account number", "xixunCompanyValue": "Shanghai Xixun Electronic Technology Co., Ltd.", "bankName": "Bank name", "bankNameValue": "The Hongkong and Shanghai Banking Corporation Limited", "swiftCode": "SWIFT code", "tips1": "Please purchase VIP equipment to go online normally", "tips": "After successful login, subscribe to VIP in the paid service for the equipment to go online normally", "totalPrice": "Total price", "contactPerson": "Contact person", "contactPersonTips": "Please enter the contact person", "telephone": "Contact phone number", "telephoneTips": "Please enter the contact phone number", "address": "Company address", "addressTips": "Please enter the company address", "companyName": "Full company name", "companyNameTips": "Please enter the full company name", "vipDuration": "VIP validity period/year", "vip4": "Premium Diamond VIP4", "upgradeToPaidVersion": "Upgrade to paid version", "silverCardVip": "silver card vip", "goldCardVip": "gold card vip", "diamondVip": "diamond vip", "superVip": "Super vip", "orderService": "order service", "currentVersion": "current version: ", "numberOfTerminals": "number of terminals", "TheRemainingAmount": "The remaining amount", "ExpireDate": "Expire date", "selectVersion": "select version", "SelectDuration(years)": "Select duration (years)", "totalOrder": "total order", "submitOrder": "Submit Order", "freeVersion": "Free version", "price": "price", "ThereAreUnpaidOrders": "There are unpaid orders", "ThereIsAnUnpaidOrderPleaseGoToPay": "There is an unpaid order, please go to pay.", "OrderRecord": "Order record", "100ControlCards": "100 control cards", "500ControlCards": "500 control cards", "NumberOfControlCards1500": "Number of control cards 1500", "unpaid": "unpaid", "transactionCreation": "transaction creation", "UnpaidTransactionTimeoutClosed": "Unpaid Transaction Timeout Closed", "paymentSuccessful": "payment successful", "OrderDetails": "Order Details", "pay": "pay", "orderNumber": "order number", "Years": "Years", "PaymentStatus": "Payment status", "amount": "amount", "newPurchase": "new purchase", "Renewal": "Renewal", "upgrade": "upgrade", "PayForTheOrder": "Pay for the order", "ExpectedPeriod": "Expected period:", "to": "to", "ActualDeadlineIsSubjectToPayment": "Actual deadline is subject to payment", "cancelOrder": "cancel order", "theOrderWillBeAutomaticallyClosed": "Orders are valid for 15 days, please pay in time. Unpaid after 14 days, the order will be automatically closed", "AfterOrderIsPaidSuccessfully": "Note: After the order is paid successfully, you can enter", "QueryPaymentInformationOnThePage": "Query payment information on the page", "paymentMethod": "payment method", "publicAccount": "public account", "AccountName": "Account Name:", "AccountBank": "Account Bank:", "LinkNumber:": "Link number:", "account": "account:", "uploadTheBankReceipt": "If you have made payment to the above account, please upload the bank receipt.", "receivedByMajorBanks": "It takes 2 hours to 3 working days for public transfers to be received by major banks.", "notifyYouViaSMS": "Our staff will activate the service for you as soon as we receive the payment and notify you via SMS.", "UploadBankReceipt": "Upload bank receipt", "contactOurSalesStaff": "If you have any questions, please contact our sales staff", "NotesForPublicTransfers": "Notes for public transfers:", "submittedForFinancialReview": "There is a delay in the arrival of offline payment, and a bank receipt needs to be submitted for financial review", "TransfersFromPersonalAccounts": "Transfers from personal accounts to our company's corporate accounts can only issue personal ordinary invoices.", "IfTheCompanyAccountIsTransferred": "If the company account is transferred to our company account, the company can issue ordinary VAT invoices or special corporate VAT invoices.", "uploadTip": "Support JPG or PNG format, the file size does not exceed 5M", "BankCardNumber": "Bank card number", "bankCardNumberWhenTransferring": "Please fill in the bank card number when transferring", "transactionHour": "transaction hour", "CancellationLineItemCannotBeEmpty": "Cancellation line item cannot be empty", "WaitingForSellerToConfirm": "Waiting for seller to confirm", "PurchaseStatus": "Purchase status", "bankReceipt": "bank receipt", "Serve": "Serve", "Optional": "Optional", "deadline": "deadline:", "orderUpdateTips": "Only the receipt or bank card number uploaded in the order can be modified. If you have any questions about other information, please contact our staff", "PleaseUploadBankReceipt": "Please upload bank receipt", "PleaseEnterBankCardNumber": "Please enter bank card number", "NoRelatedOrders": "No related orders", "ErrorUploadingBankReceipt": "Error in uploading bank receipt", "changeVipStateTip": "If the current user's VIP status is changed to super VIP, all users under the current user's company ID will be super VIP"}, "statistic": {"enableHumanStatistic": "Open passenger flow statistics", "queryHumanStatisticToday": "Query the passenger flow of the day", "enter": "Number of people entering ", "exited": "Number of people leaving", "countingMonitoring": "Passenger flow statistics monitoringg", "viewChart": "View Chart", "currentNumber": "Current number of people", "lineChart": "Line chart of people statistics in the area", "areaPeopleNum": "Number of people in the area", "selectHistoricalData": "Query historical data", "sendData": "Send Data", "keyTip": "When the data display function is enabled, the key will be automatically generated", "isShowHumanNumber": "Whether the terminal human flow data display function is enabled", "dataKey": "Key", "noDataKey": "The current device has no key", "clickCopy": "Click to copy", "copySuccess": "Copy successful"}, "alarm": {"alarmNum": "Alarm number", "alarmPeople": "Alarm people", "call": "call", "receive": "receive", "callTime": "callTime", "channel": "channel", "dstChannel": "dstChannel", "alarmDeviceInfo": "Alarm Device Info", "setCallingVolume": "Set Calling Volume", "callingVolume": "Call Volume", "notEnable": "Not enabled", "setAlarmInfo": "Set Alarm Device Information", "sipAccount": "SIP Account", "sipServiceAddress": "SIP server address", "sipServicePort": "SIP server port", "accountState": "Account State", "alarmDeviceNetwork": "Alarm Device Network Information", "setNetwork": "Set Network Status", "dynamic": "dynamic", "static": "static", "gateway": "Gateway", "subnetMask": "Subnet Mask", "alarmAccount": "Alarm account", "accountType": "Account Type", "registerAlarmAccount": "Register for Alarm Device", "registerPhoneNumber": "Register for  Telephone", "accountRule": "Account number cannot be empty and must be 11 digits or letters", "account": "Account", "batchSettings": "Batch settings", "alarmNetworkTip": "Press and hold the alarm device until there is a voice prompt; short press once to broadcast IP; short press three times to switch static/dynamic IP.", "alarmAddressTip": "Before setting the alarm address, please configure the alarm device account and other information in the alarm device information", "alarmAddressSetTip": "In the default state, the platform will set the alarm address as the platform address", "defaultState": "default", "custom": "custom", "myPhoneNumber": "My Number", "calledPhoneNumber": "Dialing Number", "alarmInfoTip": "Please register an account before configuring alarm device information", "backupCalledPhoneNumber": "Backup number"}, "traffic": {"enableTraffic": "Turn on traffic", "eventName": "Event Name", "plateNumber": "Plate Number", "plateType": "Plate Type", "plateColor": "Plate Color", "vehicleColor": "Vehicle Color", "vehicleType": "Vehicle Type", "vehicleSize": "Vehicle Size", "illegalPlace": "Illegal Place", "eventTime": "Event Time", "downloadEventPic": "Download illegal photos", "illegalPic": "Illegal Photos"}, "radar": {"radarSpeed": "Radar Speed", "radarSetting": "Radar Setting", "fastestCar": "Fastest Car", "closestCar": "Nearest car", "setResponseTime": "Response Time", "setOutputTarget": "Output Target", "setMinSpeed": "Minimum detection speed", "setMaxSpeed": "Maximum detection speed", "setSensitivity": "Sensitivity", "isConnect": "Radar connecting state", "speed": "Speed", "parameter": "Parameter", "speedLimitRange": "Speed Range ", "addSpeedLimitRange": "Please add a speed limit range ", "minSpeed": "Minimum speed ", "maxSpeed": "Maximum speed ", "radarSpeedLimit": "Radar speed limit"}, "ac": {"apOnlineRate": "AP Online Rate", "apOnlineNumber": "AP online number", "apSum": "AP Sum", "onlineTerminal": "Number of terminals online", "flowStatistics": "Flow Statistics", "flowStatisticsToday": "Today's flow statistics", "name": "AC Name", "macAddress": "MAC Address", "belongArea": "Belonging Area", "belongOrganization": "Belonging Organization", "belongProject": "Belonging Project", "userOnlineCount": "Total number of users online", "acOnlineUserCount": "Number of all AC online users", "upstreamTraffic": "Upstream Traffic", "downlinkTraffic": "Downlink Traffic", "refresh": "Refresh"}, "userAuth": {"pass": "pass", "fail": "fail", "company": "company", "personal": "Personal", "unverified": "unverified", "certificationAudit": "Certification Audit", "authMode": "Authentication mode", "authInfo": "Authentication info", "enterpriseLicense": "Enterprise License", "OfficialSeal": "Official seal", "FrontOfIdentityCard": "Front of identity card", "ReverseOfIDCard": "Reverse of ID card", "reason": "reason", "authentication": "authentication", "pleaseUploadEnterpriseLicenseOfficialSeal": "Please upload the corporate official seal certificate and corporate legal person business license", "updateAuth": "Update authentication", "uploadAuth": "Upload authentication"}, "cat1": {"temp": "template temperature", "power": "Active power", "electricEnergy": "Active Energy", "roadOne": "road 1", "roadTwo": "road 2", "addDevice": "Add <PERSON>", "channel": "channel", "colorTemp": "Color Temperature", "scheduleTip": "The timing starts from the set RealTime time. For example, if the set time is 12:30, it will start from 12:30 until the end of the next time point, and the state of the single light will be the set brightness and Color temperature. If you want to turn off a single light, you can set the brightness to 0."}, "notify": {"NotificationStrategy": "Notification strategy", "NotifyTip": "(After the user enables the notification, the user is notified by email at a specified time)", "OfflineNotify": "Offline notification", "CardNotWorkingNotification": "Receive card not working notification", "tactics": "Strategy (hour)", "selectAll": "select all", "PleaseSelectNotification": "Please select a notification policy", "SendingMailbox": "Sending mailbox", "SendingTime": "Sending time", "Username/EmailAddress": "User name/Email address", "OffLineTime": "Off-line time", "AbnormalTime": "Abnormal time", "NumberOfCardsRLastTime": "Number of cards received last time", "NumberOfCardsReceivedExceptionOccurs": "Number of abnormal cards"}, "thirdPartyAd": {"enabledThirdPartyAd": "Third Party Ad Access", "thirdPartyUrl": "Third Party Service Address", "loopTip": "Play in a loop with the existing advertisements in the device, and multiple advertisements will be evenly scattered into the existing advertisement list of the device", "onceTip": "Play once interspersed with the existing advertisements of the device, and multiple advertisements will be evenly scattered into the existing advertisement list of the device", "mutexTip": "Exclusive loop playback", "adInterval": "Advertising Period", "queryThirdPartyAd": "Query third-party ads", "downloadUrl": "Download address", "impression": "Playback information", "playHour": "play time", "playCount": "Number of times played"}, "crossArrow": {"addToCross": "Add to cross arrow", "arrowProgram": "Arrow Program", "greenArrow": "Green arrow", "redArrow": "Red cross", "closeScreen": "Close screen", "curProgram": "current program", "remove": "remove", "batchRemove": "Batch removal"}, "manual": {"operationDocument": "Operation Document", "videoTutorial": "Video Tutorial", "resourceManagement": "Resource Management", "manualAndTutorial": "User manual and video tutorial", "universal": "universal", "fileTips": "Only the same type of file can be uploaded at the same time"}, "myEditor": {"bottomCenter": "Bottom alignment", "topCenter": "Top alignment", "verticalCenter": "Center alignment"}, "employee": {"today": "Today", "yesterday": "Yesterday", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "lineChart": "Line chart", "barChart": "Bar chart", "unitPerson": "Unit: Person", "man": "Man", "woman": "Woman", "tips": "After enabling, the passenger flow will be automatically counted. After successful enabling, you can view the device in Passenger Flow Statistics -> Passenger Flow Statistics V1"}}