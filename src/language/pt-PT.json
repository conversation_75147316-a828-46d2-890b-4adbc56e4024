{"common": {"PleaseInput": "Por favor, insira", "PleaseSelect": "Por favor, selecione", "Loading": "Carregando", "personalSettings": "Configuraç<PERSON><PERSON> pessoais", "logOut": "<PERSON><PERSON>", "query": "Consultar", "newlyBuild": "Novo", "add": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Excluir", "batchDel": "Exclusão em lote", "update": "Modificar", "state": "Estado", "operation": "Operação", "tips": "Dicas", "info": "<PERSON><PERSON><PERSON>", "deploy": "Atribuir", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "areYouSureExit": "Tem certeza que deseja sair?", "setSuccess": "Configurado com sucesso", "notSet": "Não configurado", "set": "Configurar", "bindingSuccess": "Vinculado com sucesso", "unbound": "Não vinculado", "binding": "Vincular", "operationSuccessful": "Operação bem-sucedida", "createTime": "Data de criação", "normal": "Normal", "disable": "Desativar", "delete_current_option": "Confirmar exclusão da opção atual", "original": "Original", "use_iconfont": "Os ícones usam SVG Sprite", "total": "Todos", "supportedTip": "Ainda não suportado", "selectDevice": "Por favor, selecione o dispositivo", "clickToEnlarge": "Clique para ampliar", "pictureLoadingFailed": "Falha ao carregar a imagem", "passwordError": "Senha incorreta", "OKToRestart": "Confirmar reinicialização", "WaitingForRestart": "Aguardando reinicialização...", "RestartTimeout": "Tempo limite de reinicialização", "modifiedTime": "Data de modificação", "exportData": "Exportar dados", "submit": "Enviar", "modifyTheSuccess": "Modificação bem-sucedida", "configuration": "Configuração", "failure": "<PERSON><PERSON><PERSON>", "release": "Publicar", "nextStep": "Próximo passo", "selectMaterial": "Selecionar material", "notSelectedectMaterial": "Nota: Se nenhum grupo for selecionado, as informações SIM de todos os cartões serão exportadas por padrão", "exportingSIM": "Exportando informações SIM", "enterCardPackage": "Por favor, insira o nome do cartão/pacote...", "note": "<PERSON>a", "enterPackage": "Por favor, insira o nome do pacote...", "noDetails": "<PERSON><PERSON> de<PERSON>", "versionQuery": "Consulta de versão", "CardSysterm5263": "A consulta de versão requer CardSysterm 5.2.6.3 ou superior", "uninstallCard": "Nenhum cartão selecionado, não é possível usar a função de desinstalação online", "packageName": "Nome do pacote", "versionNumber": "Númer<PERSON> da <PERSON>ão", "versionIdentifiers": "Identificadores de versão", "wrong": "O estado de conexão deste cartão está incorreto, a atualização foi cancelada!", "upgradeWrong": "Todos os cartões selecionados para atualização têm problemas de conexão, por favor, verifique e tente novamente!", "cardsNotReturn": "Alguns cartões não suportam a barra de progresso, tempo limite da solicitação, atualização encerrada!", "updateComplete": "Atualização concluída!", "restartCards": "Tem certeza que deseja reiniciar esses cartões?", "addTiming": "Por favor, adicione o tempo", "Monday": "Segunda-feira", "Tuesday": "Terça-feira", "Wednesday": "Quarta-feira", "Thursday": "Quin<PERSON>-f<PERSON>", "Friday": "Sexta-feira", "Saturday": "Sábado", "Sunday": "Domingo", "brightnessClears": "A configuração manual do brilho limpará todo o tempo e a sensibilidade do brilho", "fullBrightness": "<PERSON><PERSON><PERSON>", "screenDuring": "Nota: Durant<PERSON> o tempo configurado, a tela estará ligada", "removeTiming": "<PERSON><PERSON> tempo", "clearSuccess": "Limpeza bem-sucedida", "notEnteredNotModified": "Os campos não preenchidos não serão modificados, usando Conn_v11.1.1.1[261]-AIPS-release-2&4 ou superior, os campos não preenchidos serão definidos como vazios", "Changing": "A alteração do ID da empresa ou do endereço realTime fará com que o cartão fique offline, tem certeza que deseja modificar?", "networkNTP": "A calibração automática do tempo requer rede e endereço NTP", "Settings": "Na configuração de sincronização, os campos não preenchidos serão considerados como vazios", "SynchronousMode": "Modo de sincronização", "zoneEmpty": "O fuso horário selecionado não pode estar vazio!", "synchronousState": "O intervalo de tempo configurado para sincronização estará em estado de sincronização!", "beEmpty": "A largura e a altura não podem ser menores ou iguais a 0 e não podem estar vazias!", "ModifyFailure": "Falha na modificação", "programmeDetails": "Detalhes da tarefa do programa", "showWidth": "Largura do programa", "showHigh": "Altura do programa", "notOnPlatform": "Falha na consulta, o programa atual não foi publicado nesta plataforma", "allIntervals": "Se o tempo estiver ativado e nenhum intervalo de tempo for selecionado, todos os intervalos serão considerados selecionados", "notSelected": "Não selecionado", "true": "<PERSON>m", "false": "Não", "name": "Nome", "custom": "Personalizado", "MaximumNumberOfWords200": "Limite de 200 caracteres", "exportingSIMTips": "O grupo atual não tem terminais, sugere-se selecionar outro grupo", "language": "Idioma", "copy": "Copiar", "save": "<PERSON><PERSON>", "saveAndExit": "Salvar e sair", "noMoreData": "<PERSON><PERSON> há mais dados", "enable": "Ativar", "NotEnabled": "<PERSON><PERSON>", "AssignUsers": "Atribuir usuários", "manual": "Manual de uso", "addMedia": "+ <PERSON><PERSON><PERSON><PERSON> mí<PERSON>", "reviewCurrentUsers": "Por favor, entre em contato com nossa equipe para revisar o usuário atual", "selectGroup": "Selecionar grupo", "selectingGroup": "Grupo atual", "unclassified": "Não classificado", "frontStep": "Passo anterior", "search": "<PERSON><PERSON><PERSON><PERSON>", "CAT1": "CAT1", "DaHuaCamera": "<PERSON><PERSON><PERSON><PERSON>", "GBCamera": "Câmera GB"}, "screen": {"simInformation": "Informação do cartão SIM", "networkState": "Estado da rede", "series": "Número de série", "countries": "<PERSON><PERSON>", "operationName": "Nome da operação", "unknownState": "Estado desconhecido", "noCard": "Nenhum cartão inserido", "PIN": "Bloqueado, requer PIN do usuário para desbloquear", "PUK": "Bloqueado, requer PUK do usuário para desbloquear", "PIN2": "<PERSON><PERSON><PERSON><PERSON>, requer PIN da rede para desbloquear", "readyState": "Estado pronto", "InvalidState": "Estado inválido", "subscribe": "<PERSON><PERSON><PERSON>", "choose": "<PERSON><PERSON><PERSON><PERSON>", "supportZip": "O arquivo de versão suporta apenas formato ZIP!", "selectFile": "Selecionar arquivo", "releaseTime": "Hora de lançamento", "versionInformation": "Informação da versão", "testVersion": "Versão de teste sem detalhes!", "hardwareVersion": "Parâmetros de hardware sem detalhes!", "officialRelease": "Lançamento oficial", "testRelease": "Lançamento de teste", "hardwareRelease": "Lançamento de parâmetros de hardware", "sizeMore": "<PERSON><PERSON><PERSON> excedido", "OnlyForZIPType!": "Apenas tipo ZIP!", "uploadEmpty": "O arquivo enviado não pode estar vazio!", "uploadFile": "Enviar arquivo", "fileTips": "Apenas arquivos nos formatos MP3, MP4, GIF, PNG, JPG são suportados. MP3 e imagens não podem exceder 100M, MP4 não pode exceder 150M", "fileTips1": "Apenas arquivos MP3 são suportados, tamanho máximo de 20M", "picture": "Imagem", "video": "Vídeo", "picturesOrVideos": "O tipo de arquivo de mídia enviado deve ser imagem ou vídeo!", "my": "<PERSON><PERSON>", "pending": "Pendente de aprovação", "pageImprovement": "Página em aprimoramento", "errorDetails": "Detalhes do erro", "null": "<PERSON><PERSON><PERSON>", "showProgress": "Progresso do programa", "sendSuccess": "Enviado com sucesso", "sendSuccessOffline": "Programa offline enviado, o cartão de controle enviará automaticamente o programa dentro de 72 horas", "failedProgress": "Falha ao obter progresso", "timesOut": "Tempo limite ao obter progresso", "selectCard": "Por favor, selecione o cartão de controle", "reviewDetails": "Detalhes da revisão", "satelliteNumber": "Número de satélites", "autoBrightnessTable": "Tabela de brilho automático", "senorType": "Tipo de sensor", "setAutoBrightnessTable": "Configurar tabela de brilho automático", "getAutoBrightnessTable": "Consultar tabela de brilho automático", "default255BrightnessTable": "Tabela de brilho padrão 255", "customizeBrightnessTable": "Tabela de brilho personalizada", "otherSwitch": "Outro interruptor", "customSwitchFunction": "Função de interruptor personalizado", "programPlayDetail": "Detalhes da reprodução do programa", "programPlayStatistic": "Estatísticas de reprodução do programa", "programPlaySum": "Total de reproduções do programa", "exportPlayLog": "Exportar log", "log": "Log", "networkPort": "Porta de rede", "clickToCheck": "Clique para verificar", "getPointCheckInfo": "Obter informações de verificação de pontos", "imageSize": "<PERSON><PERSON><PERSON>", "badPointNum": "Número de pontos ruins", "queryPlayerState": "Consultar estado do player", "playerStateRep": {"1": "Inicialização", "2": "Fim do programa agendado", "3": "Nenhum programa para reproduzir", "4": "Programa excluído", "5": "Processando programa", "6": "Nenhuma informação", "7": "Programa pode estar incorreto", "8": "Tela desligada", "9": "Programa fixo fora do alcance"}, "fullColor": "Cores completas", "monochrome": "Monocromático", "redBeadNumberPoints": "Número de pontos ruins do LED vermelho", "greenBeadNumberPoints": "Número de pontos ruins do LED verde", "blueBeadNumberPoints": "Número de pontos ruins do LED azul", "totalNumberOfBadPoints": "Total de pontos ruins", "redBadPointPosition": "Coordenadas dos pontos ruins do LED vermelho", "greenBadPointPosition": "Coordenadas dos pontos ruins do LED verde", "blueBadPointPosition": "Coordenadas dos pontos ruins do LED azul", "badPointPosition": "Coordenadas dos pontos ruins", "abscissa": "Coordenada X", "ordinate": "Coordenada Y", "noBadPoint": "Nenhum ponto ruim", "pointCheckTips": "A verificação de pontos ruins suporta apenas um cartão, por favor, selecione novamente", "receivingCard": "Cartão receptor", "setRecCardRelaySwitch": "Configurar interruptor de relé do cartão receptor", "getRecCardSensorData": "Obter dados do sensor do cartão receptor", "smoke": "Fumaça", "smokeless": "<PERSON><PERSON> f<PERSON>", "openCloseDoor": "Abrir/fechar porta", "openDoor": "Abrir porta", "closeDoor": "<PERSON><PERSON><PERSON> porta", "relaySwitch": "Interruptor de relé", "levelDetection": "Detecção de nível", "accessHighLevel": "Indica acesso ao nível alto", "noDeviceConnected": "Indica que nenhum dispositivo está conectado", "firstRoad": "Primeira via", "secondWay": "Segunda via", "thirdWay": "Terceira via", "fourthWay": "Quarta via", "theFifthRoad": "<PERSON><PERSON><PERSON> via", "sensorDataShareTips": "O compartilhamento de dados do sensor suporta apenas um cartão, por favor, selecione novamente", "hour": "<PERSON><PERSON>", "pointTable": "Tabela de pontos", "pointTableTips": "Por favor, opere sob orientação profissional", "pointTableTemplate": "Modelo de tabela de pontos", "pleaseUploadPointTable": "Por favor, configure a tabela de pontos primeiro", "networkConfig": "Configuração de rede", "hotspot": "Ponto de acesso", "wifiTip": "Habilitar WiFi pode alterar o estado da rede e causar desconexão, por favor, opere com cuidado", "apTip": "Nota: Por favor, use uma combinação de letras maiúsculas, minúsculas e números para a senha, com comprimento entre 8-20", "wifiList": "Consultar lista de WiFi", "selectWifi": "Por favor, selecione o WiFi", "singleCardOperation": "Esta função suporta apenas um cartão"}, "sys": {"enable": "Ativar", "remarks": "Por favor, insira as informações de observação", "authenticationMode": "Por favor, selecione o método de verificação", "emailVerification": "Verificação de e-mail", "mobileVerification": "Verificação de número de telefone", "verify": "Método de verificação", "notOpen": "<PERSON><PERSON>", "email": "E-mail", "mobile": "Telefone", "whetherAudit": "Se deve ser auditado", "open": "Abrir", "close": "<PERSON><PERSON><PERSON>", "companyId": "ID da empresa", "same": "O ID da empresa é o mesmo que o ID da empresa do criador", "roleEmpty": "O papel do usuário não pode estar vazio", "alarmType": "Tipo de alarme", "alarmTime": "<PERSON>ra do alarme", "Iknown": "Eu sei", "currentBrowser": "O navegador atual não suporta eventos enviados pelo servidor", "notReminded": "Após a confirmação, não será mais lembrado", "visited": "A página que você visitou", "find": "Não existe", "url": "Por favor, verifique o URL", "previousPage": "Voltar à página anterior", "enterHome": "Ir para a página inicial", "permissionOfTerminalGroups": "Grupos de terminais que podem ser controlados", "TheSuperAdminDoesNotRestrictUserGroups": "O superadministrador não restringe grupos de usuários", "addOrUpdateAuth": "Adicionar/Modificar autenticação", "isDefaultPassword": "De<PERSON><PERSON> senha pad<PERSON>"}, "login": {"dynamicCodeEntryTips": "O usuário atual não ativou o método de verificação de código, por favor, ative o método de verificação primeiro", "login": "<PERSON><PERSON>", "passwordLogin": "Login com senha", "username": "Nome de usuário", "password": "<PERSON><PERSON>", "ForgetThePassword": "<PERSON><PERSON><PERSON> a senha", "newPassword": "Nova senha", "confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "dynamicCodeEntry": "Login com código de verificação", "pwdNotes": "Recomenda-se usar uma senha com mais de oito caracteres", "origin_pwd_incorrect": "A senha original está incorreta", "wrong_account_or_password": "Nome de usuário ou senha incorretos", "account_has_been_locked": "A conta foi bloqueada, por favor, entre em contato com o administrador", "updateEmail": "Modificar e-mail", "bindEmail": "Vincular e-mail", "updateMobile": "Modificar número de telefone", "bindMobile": "Vincular número de telefone", "sliderRight": "Por favor, clique no botão para verificar", "loading": "Carregando", "passwordMore8": "A senha deve ter mais de 8 caracteres e conter letras maiúsculas, minúsculas e números", "user4To17": "O nome de usuário deve ter mais de 4 e menos de 17 caracteres e conter apenas números ou letras", "clickCodeMailbox": "Clique para obter o código de verificação, a informação será enviada para o e-mail de segurança", "clickCodePhone": "Clique para obter o código de verificação, a mensagem será enviada para o telefone de segurança", "authenticationSuccessful": "Verificação bem-sucedida", "securityVerification": "Por favor, faça a verificação de segurança primeiro", "enterVerificationCode": "Por favor, insira o código de verificação", "UserNameEmailMobile": "Nome de usuário/E-mail/Número de telefone", "ScopeOfAuthority": "Escopo de autoridade", "superAdministrator": "Superadministrador", "PermissionDetails": "Det<PERSON><PERSON> da permis<PERSON>ão", "pleaseContactTheAdministrator": "Por favor, entre em contato com o administrador"}, "register": {"register": "Registro", "mobile": "Número de telefone", "mailbox": "Endereço de e-mail", "code": "Código de verificação", "getCode": "Obter código de verificação", "remind": "Recomenda-se usar o login via QR code do WeChat para registro rápido", "back": "Voltar ao login", "prependResend": "Em ", "appendResend": " s para reenviar", "personalInformation": "Informações pessoais", "complete": "Concluir", "recommendedRegister": "Recomenda-se usar o login via QR code do WeChat para registro rápido", "enterPassword": "Por favor, insira a senha novamente", "companyId": "ID da empresa, o cartão de controle será vinculado ao ID da empresa após a ativação, não pode ser alterado arbitrariamente após a confirmação", "companyName": "Nome da empresa", "companyAddress": "Endereço da empresa", "companyPhone": "Telefone da empresa", "readAIPSAgreement": "Por favor, leia o contrato de usuário do AIPS primeiro", "readAccepted": "Eu li e aceito", "AIPSAgreement": "Contrato de usuário do AIPS", "registeredSuccessfully": "Registro bem-sucedido", "clickJumpOr": "Clique para pular ou", "loginDisplayed": "segundos para pular automaticamente para a interface de login", "readAcceptAIPSAgreement": "Por favor, leia e aceite o contrato de usuário do AIPS", "youAreRight": "Você está realizando a operação de redefinição de senha para", "resettingVerification": ", por favor, faça a verificação de segurança primeiro", "switchingAuthentication": "Alternar método de verificação", "pleaseSet": "Por favor, defina", "passwordSecurity": "uma nova senha, recomenda-se usar uma combinação de números, letras e caracteres para aumentar a segurança da senha", "passwordChangedSuccessfully": "Senha alterada com sucesso", "verified": "Autenticação real", "idCardNumber": "Número do documento de identidade", "companyLicense": "Faça upload da imagem da Licença Comercial do Representante Legal da Empresa dentro do período de inspeção anual válido", "cachet": "Faça upload da imagem do Certificado de Autorização com o carimbo da empresa", "idCardFront": "Faça upload da frente do documento de identidade", "idCardReverse": "Faça upload do verso do documento de identidade", "pleaseReadAndCheckAIPS": "Por favor, leia e marque o contrato de usuário do AIPS", "personal": "Pessoal", "company": "Empresa", "CertifiProgress": "Progresso da autenticação", "waitForAdminAuth": "Aguardando autenticação do administrador", "DownloadLicenseCertificate": "Baixar Certificado de Licença"}, "ChangePWD": {"ChangePassword": "<PERSON><PERSON><PERSON><PERSON>", "remind": "Somente usuários com número de telefone ou e-mail vinculado podem recuperar a senha"}, "nav": {"首页": "Página inicial", "智慧屏幕": "Tela inteligente", "智慧广播": "Transmissão inteligente", "气象环境": "Ambiente meteorológico", "智慧监控": "Monitoramento inteligente", "客流统计": "Estatísticas de tráfego", "客流统计V1": "Estatísticas de tráfego V1", "系统管理": "Gerenciamento do sistema", "设备管理": "Gerenciamento de dispositivos", "节目管理": "Gerenciamento de programas", "媒体库": "Biblioteca de mídia", "日志管理": "Gerenciamento de logs", "远程控制日志": "Log de controle remoto", "播放日志": "Log de reprodução", "用户管理": "Gerenciamento de usuários", "菜单管理": "Gerenciamento de menus", "角色管理": "Gerenciamento de funções", "用户日志": "Log de usuários", "登录日志": "Log de login", "系统日志": "Log do sistema", "设备状态": "Status do dispositivo", "广播任务": "Tarefa de transmissão", "分组管理": "Gerenciamento de grupos", "审批管理": "Gerenciamento de aprovação", "公告管理": "Gerenciamento de anúncios", "终端列表": "Lista de terminais", "智慧物联": "Internet das Coisas inteligente", "智慧照明": "Iluminação inteligente", "CAT1照明": "Iluminação CAT1", "控制卡照明": "Iluminação de cartão de controle", "电能管理": "Gerenciamento de energia", "视频监控": "Monitoramento de vídeo", "付费服务": "Serviço pago", "订购服务": "Serviço de assinatura", "订单": "Pedido", "SIP账号管理": "Gerenciamento de conta SIP", "监控回放": "Reprodução de monitoramento", "人群聚集": "Agrupamento de pessoas", "订单管理": "Gerenciamento de pedidos", "报警管理": "Gerenciamento de alarmes", "报警记录": "Registro de alarmes", "通话记录": "Registro de chamadas", "智慧交通": "<PERSON><PERSON>ân<PERSON><PERSON> inteligente", "交通信息": "Informações de trânsito", "雷达测速": "Medição de velocidade por radar", "WIFI AC": "WIFI AC", "概览": "Visão geral", "AC管理": "Gerenciamento AC", "密码管理": "Gerenciamento de senhas", "认证审核": "Auditoria de certificação", "通知策略": "Política de notificação", "通知日志": "Log de notificação", "十字箭头": "<PERSON>a cruzada", "设备白名单": "Lista branca de dispositivos", "摄像头监控": "Vigilância por câmeras"}, "validate": {"account_cannot_empty": "A conta não pode estar vazia", "password_cannot_empty": "A senha não pode estar vazia", "confirm_password_cannot_empty": "A confirmação da senha não pode estar vazia", "new_pwd_cannot_empty": "A nova senha não pode estar vazia", "email_cannot_empty": "O e-mail não pode estar vazio", "mobile_cannot_empty": "O número de telefone não pode estar vazio", "code_cannot_empty": "O código de verificação não pode estar vazio", "roleName_cannot_empty": "O nome da função não pode estar vazio", "menuURL_cannot_empty": "O URL do menu não pode estar vazio", "superior_menu_cannot_empty": "O menu superior não pode estar vazio", "menu_name_cannot_empty": "O nome do menu não pode estar vazio", "group_name_cannot_empty": "O nome do grupo não pode estar vazio", "group_type_cannot_empty": "O tipo de grupo não pode estar vazio", "audit_name_ccannot_empty": "O nome da auditoria não pode estar vazio", "resource_type_cannot_empty": "O tipo de recurso não pode estar vazio", "status_cannot_be_empty": "O status não pode estar vazio", "approval_comments_cannot_blank": "Os comentários de aprovação não podem estar em branco", "program_name_cannot_empty": "O nome do programa não pode estar vazio", "the_new_password_is_inconsistent": "A confirmação da senha não coincide com a nova senha", "the_password_is_inconsistent": "A confirmação da senha não coincide com a senha", "incorrect_email_format": "Formato de e-mail incorreto", "code_format": "Por favor, insira seis caracteres", "mobile_format": "Formato de número de telefone incorreto", "mobile_code_empty": "O código de verificação do telefone não pode estar vazio", "email_code_empty": "O código de verificação do e-mail não pode estar vazio", "company_id_empty": "O ID da empresa não pode estar vazio", "not_empty": "Não pode estar vazio", "alarmAddress_not_empty": "O endereço de alarme ou a conta de chamada não pode estar vazio", "alias_cannot_empty": "O apelido não pode estar vazio", "company_name_cannot_empty": "O nome da empresa não pode estar vazio", "company_address_cannot_empty": "O endereço da empresa não pode estar vazio", "company_phone_number_cannot_empty": "O telefone da empresa não pode estar vazio", "id_card_number_cannot_empty": "O número do documento de identidade não pode estar vazio", "id_card_number_format_wrong": "Formato do número do documento de identidade incorreto", "validateTip": "Para a segurança da sua conta, agora é necessário vincular um método de verificação."}, "role": {"role": "Função", "roleName": "Nome da função", "remark": "Observação", "authorization": "Autorização", "subAdmin": "Subadministrador", "normalUser": "Utilizador normal"}, "menu": {"name": "Nome", "parentName": "Menu superior", "icon": "Ícone", "type": "Tipo", "orderNum": "Número de ordenação", "url": "URL do menu", "perms": "Identificador de autorização", "mainMenu": "<PERSON>u principal", "parentMenuName": "Nome do menu pai", "permsTips": "<PERSON><PERSON><PERSON><PERSON> separados por vírgula, por exemplo: user:list,user:create", "menu": "<PERSON><PERSON>", "DirectoryMenu": "Menu de diretório", "button": "Botão", "HomeDirectoryMenu": "Menu de diretório inicial"}, "log": {"user_name_user_action": "Nome de usuário / Ação do usuário", "user_action": "Ação do usuário", "request_method": "Método de solicitação", "request_parameters": "Parâmetros da solicitação", "execution_time": "Tempo de execução (ms)", "ip_address": "Endereço IP", "commandId": "ID do comando", "response_result": "Resultado da resposta", "schedule": "Progresso", "ReasonForFailure": "Motivo da falha", "RequestTimedOut": "Tempo limite da solicitação", "requestSucceeded": "Solicitação bem-sucedida", "connectionDoesNotExist": "Conexão não existe", "Disconnect": "Conexão desconectada", "connectionClosed": "Conexão fechada", "requestException": "Exceção na solicitação"}, "group": {"name": "Nome do grupo", "type": "Tipo de grupo", "addingAGroup": "Adicionar grupo", "pleaseEnterAGroupName": "Por favor, insira o nome do grupo", "addingSubgroup": "Adicionar subgrupo", "pleaseDeleteTheSubgroupsFirst": "Por favor, exclua os subgrupos primeiro"}, "cardDevice": {"deviceName": "Nome do dispositivo", "online": "Online", "networkType": "Tipo de rede", "resolvingPower": "Resolução", "programTask": "Tarefa do programa", "broadcastTask": "Tarefa de transmissão", "screenStatus": "Status da tela", "lastOffline": "Última desconexão", "queryTerminalInfo": "Consultar informações do terminal", "selectedCard": "Cartão selecionado", "brightness": "Bril<PERSON>", "volume": "Volume", "locked": "Bloqueado", "terminalInfoFirst": "Por favor, obtenha as informações do terminal primeiro", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "synchronous": "Sincronizado", "asynchronous": "Assíncrono", "temperature": "Temperatura", "number": "Número de série", "NoSim": "Sem informações do cartão SIM", "fireWare": "Versão do firmware"}, "operation": {"settingMode": "Modo de configuração", "connectionLog": "Log de conexão", "LEDscreen": "Tela LED", "screenshot": "Captura de tela", "liveVideo": "Transmissão ao vivo", "screenSwitch": "Interruptor da tela", "timingSwitch": "Interruptor de temporização", "screenBrightness": "<PERSON><PERSON><PERSON> da tela", "autoBrightness": "<PERSON><PERSON><PERSON> automá<PERSON>", "timingBrightness": "Brilho de temporização", "volumeControl": "Controle de volume", "timingConfig": "Configuração de tempo", "connConfig": "Configuração de conexão", "syncAndAsyncConfig": "Configuração síncrona e assíncrona", "alarmSwitch": "Interruptor de alarme", "onlineUpdate": "Atualização online", "restartSys": "Reiniciar sistema", "backgroundPlayback": "Fundo do player", "backupScreenParam": "Backup de parâmetros da tela", "restoreScreenParam": "Restaurar parâmetros da tela", "hardwareStatus": "Parâmetros de hardware", "manualconfigurationorquery": "Configuração/consulta manual", "scheduledconfigurationorquery": "Configuração de temporização", "thealias": "Apelido", "webServerAddress": "Endereço do servidor WEB", "thecompany": "Empresa", "realtimeaddress": "Endereço Realtime", "remove": "Limpar", "volumeset": "Configuração em lote", "batchquery": "Consulta em lote", "group": "Grupo", "exportSIMInfo": "Exportar informações do SIM", "clearProgram": "Limpar programa", "clearTask": "<PERSON><PERSON> ta<PERSON>", "callAddress": "Configuração de endereço de chamada", "alarmConfig": "Configuração de alarme", "clearBroadcastTask": "Limpar ta<PERSON>fa de transmissão", "queryOrClearTiming": "Consultar ou limpar temporização", "queryTiming": "Consultar temporização", "screenControl": "Controle de tela", "broadcastControl": "Controle de transmissão", "monitoringControl": "Controle de monitoramento", "meteorologicalEnvironmentControl": "Controle de ambiente meteorológico", "passengerFlowStatistics": "Estatísticas de fluxo de passageiros", "lightingControl": "Controle de iluminação", "setSIPServerAddress": "Configurar endereço do servidor SIP", "getSIPServerAddress": "Consultar endereço do servidor SIP", "SIPServerAddress": "Endereço do servidor SIP", "AlarmEquipmentMacAddress": "Endereço MAC do equipamento de alarme", "AlarmEquipmentIpAddress": "Endereço IP do equipamento de alarme", "SetAlarmAddress": "Configurar endereço de alarme", "GetAlarmAddress": "Consultar endereço de alarme", "AlarmAddress": "Endereço de alarme", "CallAccount": "Conta de chamada", "AlarmVolume": "Volume de alarme", "LightingLevel": "Nível de iluminação", "LightingSwitch": "Interruptor de iluminação", "Register_SIP_account": "Registrar conta SIP", "getGspInfo": "Consultar informações GPS", "gpsInfo": "Informações GPS", "recordingFile": "Arquivo de gravação", "fullSizeScreenshotOfAndroid": "Captura de tela em tamanho real do Android", "customSwitch": "Interruptor personalizado", "ThirdPartyAdvertising": "Publicidade de terceiros", "BadPointDetection": "Detecção de pontos ruins", "playerState": "Estado do player", "NumberOfCardsReceived": "Número de cartões recebidos", "sensorDataShare": "Compartilhamento de dados do sensor", "dataShare": "Compartilhamento de dados", "dataRefreshCycle": "Ciclo de atualização de dados", "sharedDataKey": "Chave de dados compartilhados", "curFlow": "Fluxo atual", "isEnableCurFlow": "Ativar fluxo atual", "flowAddress": "Endereço de fluxo", "showLocation": "Mostrar localização", "showPrefix": "Mostrar prefixo", "leftTop": "Superior esquerdo", "rightTop": "Superior direito", "leftBottom": "Inferior esquerdo", "rightBottom": "Inferior direito", "curFlowTip": "Se o endereço de fluxo estiver vazio, o endereço padrão da plataforma será usado", "detectingBadPixels": "Detectando pixels ruins", "uploadZip": "Enviar ZIP", "versionDelete": "Excluir versão", "hdvancedConfig": "Configuração avançada", "hardwareConfig": "Configuração de hardware", "realTimeSet": "Configuração Realtime", "networkConfig": "Configuração de rede", "clearBroadcast": "<PERSON><PERSON>", "callVolumeControl": "Controle de volume de chamada", "queryProgramName": "Consultar nome do programa", "list": "Lista", "new": "Novo", "oneClickOperateScreen": "Desligar tela com um clique", "systemDisplay": "Resolução do sistema", "checkAddress": "Configurar endereço de verificação"}, "tips": {"brightness": "Nota: O brilho deve estar entre 1-255", "volume": "Nota: O volume deve estar entre 0-15", "alarmVolume": "Nota: O volume deve estar entre 1-9", "liveVideo": "Suporta protocolos rtmp e rtsp, por favor instale o live primeiro", "liveVideo1": "OFF desliga a transmissão ao vivo, ON liga a transmissão ao vivo", "liveVideo2": "Endereço de teste", "screenSwitch": "Nota: Limpe o temporizador e redefina o interruptor da tela.", "screenTiming": "Nota: Consultar o temporizador do interruptor da tela (suportado na versão conn10.0.5T ou superior)", "autoBrightness": "Nota: Esta função é suportada a partir do CardSystem-v3.6.0, a sensibilidade deve estar entre 0 e 100", "autoBrightness1": "O brilho é ajustado automaticamente de acordo com os dados do sensor (suportado na versão conn10.0.5T ou superior)", "autoBrightness2": "Para cartões com brilho máximo de 64, o brilho mínimo pode ser configurado como 1% ou um valor apropriado; para cartões com brilho máximo de 255, o brilho mínimo deve ser configurado como 36% ou mais, caso contr<PERSON><PERSON>, o brilho será muito baixo.", "timingBrightness": "Nota: Durante o tempo configurado, o brilho será o configurado, fora desse tempo, o brilho padrão será aplicado. Por exemplo, se o brilho padrão for 80%, o brilho configurado for 20% e o intervalo de tempo for 8:00-17:00, o brilho será 20% durante esse intervalo e 80% fora dele.", "manualconfigurationorquery1": "Apenas para cartões da série M70 e M80", "manualconfigurationorquery2": "O modo de sincronização é aplicado dentro do intervalo de datas; apenas para cartões da série M70 e M80, versão cardSystem5.2.5.6-8 ou superior", "widthheighterror": "A largura e a altura da tela não podem ser menores que 0 pixels", "noTenteredNotModified": "Se este campo não for preenchido, não será modificado", "approval": "A auditoria é realizada de acordo com o processo definido, o número menor é priorizado", "advancedParameter": "Nota: Interface de configuração de parâmetros avançados (suportado na versão conn10.0.5T ou superior)", "cardSelected": "Cartão selecionado", "cardNameSelected": "Terminal selecionado", "numberEmpty": "O número de cartões selecionados não pode estar vazio", "progressBar": "Nota: A barra de progresso é apenas para exibição, o resultado real depende do estado do cartão!", "upgradeFeatureSelectCard": "Nenhum cartão selecionado, não é possível usar a função de atualização online!", "UninstalledSuccessfully": "Status: Desinstalado com sucesso!", "uninstallFeatureSelectCard": "Nenhum cartão selecionado, não é possível usar a função de desinstalação online!", "SelectUninstall": "Por favor, selecione o componente para desinstalar", "selectedNotExist": "O rótulo selecionado está incorreto, não existe", "backgroundTips": "A resolução da imagem do material deve corresponder à resolução da tela, caso contr<PERSON><PERSON>, ocorrerá um erro", "releaseTips": "Cartões offline serão enviados offline, a resposta será bem-sucedida, mas a barra de progresso não será exibida, o cartão de controle enviará automaticamente o programa dentro de 72 horas", "releaseTips1": "Ativar o log do player, após o envio bem-sucedido do programa, o log de reprodução de cada cartão será salvo", "releaseTips2": "Após ativar o log do player, se o cartão de controle tiver um programa, ele enviará o log para a plataforma no intervalo de tempo configurado", "SIPAddress": "Nota: Se o endereço do servidor SIP não foi configurado, é necessário configurar o endereço SIP antes de usar as funções de intercomunicação e alarme. Após configurar o endereço, reinicie o cartão de controle.", "SIPAddress1": "Se o campo estiver vazio, o endereço do servidor SIP da plataforma atual será usado ou um endereço SIP personalizado", "alarmConfig": "Recomenda-se definir o endereço da plataforma como o endereço de alarme", "CustomCanUseNetworkAddress": "Endereço de rede personalizado pode ser usado", "networkAddressWhenCustomizing": "Por favor, insira o endereço de rede ao personalizar", "broadcastTask": "Nota: Se nenhuma tarefa de temporização for configurada, a transmissão será automaticamente limpa após reiniciar o dispositivo, a tarefa não será salva!", "SIPTips": "Esta conta pode ser usada para chamadas SIP com o cartão de controle, se a conta atual já estiver registrada, apenas modificações são permitidas", "broadcastTaskRelease": "Nota: Antes de publicar a tarefa de transmissão, certifique-se de que a função de chamada não está em uso", "ModifyTerminal": "Dica: Para modificar o apelido do terminal, clique em Internet das Coisas inteligente, mude para o modo de lista e clique em modificar", "senorBrightnessTable": "Esta função requer a versão systemCore ******** ou superior. Passos: Primeiro defina o valor de sensibilidade do sensor, depois faça o upload do arquivo BrightnessTable.xlsx padrão do EasyBoard para modificar, os valores podem ser alterados, mas o formato do arquivo não deve ser alterado, caso contrário, a operação falhará! Ao fazer o upload do arquivo, apenas cartões com o mesmo tipo de sensor podem ser selecionados; caso contrário, a operação falhará!", "customSwitchTip": "Aviso: Se este interruptor estiver ligado, o interruptor de tela personalizado será ativado, o interruptor de tela original será desativado. Se este interruptor estiver desligado, o interruptor de tela original será ativado. Em caso de dúvidas, por favor, confirme com o pessoal relevante!", "configAdTip": "Tem certeza de que deseja integrar publicidade de terceiros? Nossa plataforma não garante a legalidade ou conformidade do conteúdo de terceiros, por favor, proceda com cautela.", "configAdTip1": "Ativar", "configAdTip2": "Tem certeza de que deseja disponibilizar o cartão de controle para uso por plataformas de terceiros?", "configAdTip3": "Esta função requer a atualização do APK correspondente, para mais de<PERSON>, entre em contato com nossa equipe!!!", "groupTip": "Após o cancelamento, todos os dispositivos serão exibidos", "passwordIsWeak": "Sua senha é muito fraca, por favor, altere-a. A senha deve conter pelo menos uma letra maiúscula, uma letra minúscula, um número e ter pelo menos 9 caracteres.", "authTip": "Nossa equipe revisará em 2-3 dias úteis. Após a aprovação, você poderá usar a plataforma normalmente.", "pointCheckTip": "Após a conclusão da verificação, aguarde um momento para garantir que a verificação esteja completa e que as informações corretas de detecção de pontos ruins sejam obtidas", "pointCheckCard": "Para o plano de uso, entre em contato com nossa equipe técnica", "playLogsExportTip": "Ao exportar logs, se nenhuma data for selecionada, os logs do dia serão exportados; caso contrário, todos os logs dentro do intervalo de datas especificado serão exportados.", "oneClickOperateScreenTip": "Esta operação desligará todas as telas online"}, "file": {"name": "Nome do arquivo", "type": "Tipo de arquivo", "status": "Status do arquivo", "size": "Tamanho do arquivo", "UploadProgress": "Progresso do upload", "download": "Download", "thumbnail": "Miniatura", "checkPending": "Pendente de aprovação", "approved": "<PERSON><PERSON><PERSON>", "auditFailed": "Reprovado", "under_review": "Em revisão", "examine": "Rev<PERSON><PERSON>", "attachment": "Por favor, selecione o anexo", "attachment1": "Clique ou arraste o arquivo aqui para fazer o upload", "auditTime": "Tempo de revisão", "file": "Arquivo", "ApprovalComments": "Comentários de aprovação", "upload": "Upload", "update": "<PERSON><PERSON><PERSON><PERSON>", "toView": "Visualizar", "WithoutPermission": "<PERSON><PERSON> permis<PERSON>", "uninstall": "<PERSON><PERSON><PERSON><PERSON>", "SerialNumber": "Número de série", "OnlineUpdate": "Atualização online", "TheSize": "<PERSON><PERSON><PERSON>", "VersionLog": "Log de versão", "LogDetails": "Detalhes do log", "Onlineupgrade": "Atualização online em andamento...", "Waitingupdates": "Aguardando atualização...", "Allcards": "Todos os cartões selecionados para atualização não puderam ser atualizados, por favor, verifique o problema", "DownloadComplete": "Download concluído, descompacte para atualizar", "NotSupported": "Barra de progresso não suportada, carregando...", "UpdateSuccessful": "Atualização bem-sucedida!", "UpdateFailed": "Atualização falhou!", "ReadyDownload": "Preparando para download!", "ConnectionFailed": "<PERSON>alha na conexão, por favor, verifique o dispositivo!", "ThreeSeconds": "Atualização concluída, a janela será fechada em 3 segundos", "YouCanOnlyUploadUpTo5Files": "Você pode fazer upload de no máximo 5 arquivos", "audio": "<PERSON><PERSON><PERSON>", "fileOverSize": "O arquivo excede o tamanho máximo permitido", "fileLimit": "O arquivo deve estar no formato docx", "fileVersion": "Versão do arquivo", "fileLimitPdfAndVideo": "O arquivo deve estar no formato pdf ou mp4"}, "card": {"cardId": "Número de série", "setTiming": "Configurar temporização", "getTiming": "Consultar temporização", "noTiming": "Sem temporização", "timing": "Temporização", "notSpecified": "Não especificado", "dateType": "Tipo de data", "DateRange": "Intervalo de datas", "startDate": "Data de início", "endDate": "Data de término", "timeType": "Tipo de tempo", "timeFrame": "Intervalo de tempo", "startTime": "Hora de início", "endTime": "<PERSON>ra de término", "SpecifyWeek": "Especificar semana", "WeekRange": "Intervalo de semanas", "PleaseScheduledTask": "Por favor, especifique o tipo de tarefa de temporização", "sensitivity": "Sensibilidade", "Minbrightness": "<PERSON><PERSON><PERSON>", "defaultBrightness": "<PERSON><PERSON><PERSON>", "timingBrightness": "Brilho de temporização", "timedVolume": "Volume de temporização", "defaultVolume": "Volume padrão", "cardVoltage": "Voltagem do cartão", "externalVoltage1": "Voltagem externa 1", "externalVoltage2": "Voltagem externa 2", "externalVoltage3": "Voltagem externa 3", "externalVoltage4": "Voltagem externa 4", "doorOpen": "Porta aberta", "version": "Vers<PERSON>", "humidity": "Umidade", "temperature": "Temperatura", "smokeWarning": "Alerta de fumaça", "querySuccessful": "Consulta bem-sucedida, exibindo os dados", "queryFailed": "<PERSON>alha na consulta, não foi possível exibir os dados", "screenWidth": "<PERSON><PERSON><PERSON> da tela (pixels)", "screenHeight": "Altura da tela (pixels)", "screenAlias": "Apelido da tela", "genericVersion": "Versão genérica", "notChosenCard": "Cartão não selecionado", "TestVersion": "Versão de teste", "rebootNow": "Reiniciar agora", "monitorTip": "No máximo 6 telas de monitoramento podem ser reproduzidas simultaneamente, por favor, selecione novamente", "picture-in-picture": "Picture-in-picture", "pictureTip": "As coordenadas e as dimensões não devem exceder a área visível da resolução da tela, para redefinir é necessário desativar o picture-in-picture primeiro", "coordinate": "<PERSON><PERSON><PERSON><PERSON>", "pictureSize": "<PERSON><PERSON><PERSON> da tela", "checkAddressTip": "<PERSON><PERSON><PERSON> ativar, preencha o endereço do servidor de auditoria de mídia, usado para auditar o conteúdo da mídia no dispositivo", "mediaContentReview": "Revisão de conteúdo de mídia", "realtimeReview": "Revisão em tempo real", "realtimeReviewTips": "Ativar a revisão em tempo real consome dados", "clearPrompt": "Limpar prompt", "interval": "Intervalo de tempo"}, "approval": {"auditName": "Nome da auditoria", "auditType": "Tipo de auditoria", "approvalProcess": "Processo de aprovação", "Reviewer": "Revisor", "order": "Ordem", "mediaResources": "Recursos de mídia", "ProgramType": "Tipo de programa", "BroadcastMediaResources": "Recursos de mídia de transmissão", "BroadcastTaskResources": "Recursos de tarefa de transmissão", "noAudit": "Sem auditoria", "approved": "<PERSON><PERSON><PERSON>", "auditFailed": "Reprovado", "select_at_least_one_reviewer": "Selecione pelo menos um revisor", "approver_cannot_blank": "O revisor não pode estar em branco", "approval_order_cannot_blank": "A ordem de aprovação não pode estar em branco", "InsufficientUsers": "Usuários insuficientes", "clickAudit": "Auditoria com um clique", "batchReview": "Revisão em lote", "auditMemo": "O programa tem problemas, por favor, ajuste e envie novamente."}, "program": {"program": "Programa", "type": "Tipo de programa", "name": "Nome do programa", "ordinaryProgram": "Programa comum", "insertProgram": "Programa de inserção", "totalSize": "Tam<PERSON>ho total", "state": "Status do recurso", "ProgramList": "Lista de programas", "ProgramInfo": "Informações do programa", "ComponentProperties": "Propriedades do componente", "ProgramProperties": "Propriedades do programa", "PlaybackMode": "Modo de reprodução", "EntryEffects": "Efeitos de entrada", "DurationMobilizationEffect": "Duração do efeito de entrada (s)", "AppearanceEffects": "Efeitos de saída", "DurationAppearanceEffect": "Duração do efeito de saída (s)", "StartPlaybackTime": "Tempo inicial de reprodução (s)", "DurationContinuousDisplay": "Duração de exibição contínua (s)", "region": "Região", "upper": "Superior", "left": "E<PERSON>rda", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "BasicProperties": "Propriedades básicas", "background": "Fundo", "pellucidity": "Transparência", "DisplayDackground": "Exibir fundo", "open": "Ligar", "close": "<PERSON><PERSON><PERSON>", "BackgroundColor": "Cor de fundo", "DisplayHourScale": "<PERSON>ibir escala de horas", "HourScaleColor": "<PERSON>r da escala de horas", "ShowMinuteScale": "<PERSON><PERSON>r escala de minutos", "MinuteScaleColor": "<PERSON><PERSON> da escala de minutos", "ScaleStyle": "<PERSON>st<PERSON> da es<PERSON>a", "IntegerScaleDigitalDisplay": "Exibição digital da escala inteira", "PointerStyle": "Estilo do ponteiro", "ClockPointerColor": "Cor do ponteiro do relógio", "MinutePointerColor": "<PERSON><PERSON> <PERSON> ponteiro dos minutos", "SecondPointerColor": "Cor do ponteiro dos segundos", "DisplaySecondHand": "<PERSON><PERSON><PERSON> de segundos", "up": "Para cima", "down": "Para baixo", "play": "Reproduzir", "times": "Vezes", "PleaseEnterContent": "Por favor, insira o conteúdo", "text": "Texto", "DigitalClock": "Relógio digital", "analogClock": "Relógio analógico", "EnvironmentalMonitoring": "Monitoramento ambiental", "weather": "Clima", "Multi-materialWindow": "<PERSON><PERSON> m<PERSON><PERSON> materia<PERSON>", "html": "Página web", "weburl": "URL da página web", "enterTime": "Por favor, insira o tempo", "Multi-material": "<PERSON><PERSON><PERSON><PERSON> mater<PERSON>", "empty": "Limpar", "oneLevelUp": "Um nível acima", "oneLevelDown": "Um nível abaixo", "layerOnTop": "Camada no topo", "bottomLayer": "Camada na base", "FullScreen": "Tela cheia", "pageProperties": "Pro<PERSON><PERSON><PERSON> da página", "effectiveDate": "Data efetiva", "PlayProperties": "Propriedades de reprodução", "planSchedule": "Cronograma do plano", "sun": "Domingo", "one": "Segunda", "two": "<PERSON><PERSON><PERSON>", "three": "Quarta", "four": "<PERSON><PERSON><PERSON>", "five": "Sexta", "six": "Sábado", "clockProperties": "Propriedades do relógio", "PleaseSelectATimeZone": "Por favor, selecione um fuso horário", "year": "<PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "day": "<PERSON>a", "hour": "<PERSON><PERSON>", "Minute": "Min<PERSON>", "Second": "<PERSON><PERSON><PERSON>", "Week": "Se<PERSON>", "AM": "AM", "PM": "PM", "fourYears": "Ano de quatro dígitos", "12HourClock": "Formato de 12 horas", "morningAfternoon": "Manhã/Tarde", "style": "<PERSON><PERSON><PERSON>", "dateStyle": "Estilo da data", "timeStyle": "<PERSON>st<PERSON> do tempo", "displayStyle": "Estilo de exibição", "singleLine": "Linha ú<PERSON>", "Multi-line": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "fontSettings": "Configurações de fonte", "fontSize": "<PERSON><PERSON><PERSON>", "fontColor": "<PERSON><PERSON> da fonte", "PlayTime": "Tempo de reprodução", "specialEffects": "Efeitos especiais", "specificFrequency": "Frequência específica", "blink": "Piscar", "breathe": "Respirar", "MonitoringProperties": "Propriedades de monitoramento", "compensate": "Compensar", "windSpeed": "Velocidade do vento", "windDirection": "Direção do vento", "noise": "<PERSON><PERSON><PERSON><PERSON>", "atmosphericPressure": "Pressão atmosférica", "rainfall": "Precipitação", "radiation": "Radiação", "lightIntensity": "Intensidade da luz", "DisplayMode": "Modo de exibição", "stayLeft": "Alinhar à esquerda", "Centered": "Centralizado", "KeepRight": "Alinhar à direita", "singleLineScroll": "Rolagem de linha única", "speed": "Velocidade", "ms/pixel": "ms/pixel", "refreshCycle": "Ciclo de atualização", "minute": "Min<PERSON>", "fileProperties": "Propriedades do arquivo", "Multi-MaterialBasicProperties": "Propriedades básicas de múltiplos materiais", "mediaList": "Lista de mídia", "SelectedMaterialInformation": "Informações do material selecionado", "HourMarkColor": "<PERSON>r da marca de hora", "minuteScaleColor": "<PERSON><PERSON> da escala de minutos", "hourHandColor": "Cor do ponteiro das horas", "minuteHandColor": "<PERSON><PERSON> <PERSON> ponteiro dos minutos", "pointerColor": "Cor <PERSON>onteiro", "backgroundColor": "Cor de fundo", "static": "Está<PERSON><PERSON>", "scroll": "Rolagem", "turnPages": "<PERSON><PERSON><PERSON>", "total": "Total", "Page": "<PERSON><PERSON><PERSON><PERSON>", "preview": "Pré-visualização", "stopPreview": "Parar pré-visualização", "TextEditor": "Editor de texto", "province": "<PERSON>v<PERSON><PERSON>", "Multi-material_text": "Por favor, adicione mídia à direita, você pode adicionar várias mídias diferentes, o display LED reproduzirá na ordem da lista.", "streaming": "Streaming", "direction": "Direção de rolagem", "ToTheLeft": "Para a esquerda", "upward": "Para cima", "ToTheRight": "Para a direita", "addText": "Adicionar texto", "liveStreamAddress": "Endereço de streaming ao vivo", "deviceAddress": "Endereço do dispositivo", "deviceAddrTip": "<PERSON>r <PERSON><PERSON><PERSON>, usa o endereço atual (https://www.ledokcloud.com/aips4/monitor/humanNumberStatistic/queryHumanNumberByDataKey), por favor, não modifique arbitrariamente", "deviceKey": "Chave do dispositivo", "deviceKeyTip": "A chave do dispositivo é preenchida pelo usuário da plataforma com a chave do dispositivo especificado, o programa enviado exibirá as informações de fluxo de pessoas do dispositivo especificado na tela.", "CustomHTML": "HTML personalizado", "CustomHTMLTip1": "É o número de série do dispositivo", "CustomHTMLTip2": "É o tempo", "CustomHTMLTip3": "É o número total de pessoas que entraram hoje", "CustomHTMLTip4": "É o número de pessoas que entraram neste período de hora", "CustomHTMLTip5": "É o número total de pessoas que entraram historicamente", "CustomHTMLTip6": "É o número total de pessoas que saíram hoje", "CustomHTMLTip7": "É o número de pessoas que saíram neste período de hora", "CustomHTMLTip8": "É o número total de pessoas que saíram historicamente", "flowStatistics": "Estatísticas de fluxo de pessoas", "weatherTip1": "Temperatura atual", "weatherTip2": "AQI (Índice de Qualidade do Ar)", "weatherTip3": "Data atual (incluindo temperatura em tempo real)", "weatherTip4": "Clima atual", "weatherTip5": "Temperatura máxima do dia", "weatherTip6": "Temperatura mínima do dia", "weatherTip7": "Direção do vento do dia", "weatherTip8": "Velocidade do vento do dia", "weatherTip9": "Imagem do clima do dia, formato: img-largura-altura", "weatherTip10": "No %{} a<PERSON><PERSON>, 'yesterday' representa ontem, 'arr.0' representa o dia atual, '1' representa amanhã, '2' representa depois de amanhã, '3' representa três dias depois, '4' representa quatro dias depois", "timeType": "Tipo de tempo", "timeTypeTip": "Alterar o tipo mudará o HTML personalizado", "HDMITypeDescription": "Descrição do tipo HDMI", "HDMIDescription1": "1. Programas do tipo HDMI atualmente suportam apenas a série m70. Após o envio bem-sucedido, verifique a exibição real na tela. A exibição específica depende da tela.", "HDMIDescription2": "2. Se o efeito de exibição real estiver incorreto, verifique primeiro a conexão do cabo HDMI ou se a versão do software está correta. Se estiver tudo correto, tente reenviar. Se o cartão de controle atual estiver no modo picture-in-picture HDMI, ele precisa ser configurado como sincronizado antes de redefinir. Para o plano de uso específico, entre em contato com nossa equipe técnica.", "text-to-speech": "Texto para fala", "addProgramTips": "Após o envio bem-sucedido de um programa comum, ele substituirá o programa original. Após o envio bem-sucedido de um programa de inserção, ele não substituirá o programa original, apenas será reproduzido durante o período de inserção.", "enablePlayerLog": "Ativar log do player", "playLog": "Log de reprodução", "timeInterval": "Intervalo de tempo (minutos)", "discount": "Comprimento de cada segmento", "isDiscount": "Aplicar desconto", "discountText": "Comprimento de separação de cada segmento, múltiplos separados por vírgula. Exemplo: 256,256,128", "segmentation": "Segmentação", "PleaseEnterDiscountWidth": "Por favor, insira a largura do desconto", "PleaseEnterTheCorrectContentFormat": "Por favor, insira o formato de conteúdo correto", "multi-picture": "Múl<PERSON><PERSON> imagens", "PleaseSelectPictureVideoSplit": "Por favor, selecione uma imagem ou vídeo para segmentar", "totalWidthDiscountCannotWidth": "O comprimento total do desconto não pode ser maior que o tamanho do material", "sensorsShareData": "Compartilhamento de dados do sensor", "broadcastSort": "Ordenação de transmissão", "horizontal": "Horizontal", "verticalRow": "Vertical", "discountMode": "<PERSON><PERSON>", "level": "Nível", "vertical": "Vertical", "negativeIon": "Íon negativo", "zoomIn": "Ampliar", "zoomOut": "Reduzir", "materialCycle": "Ciclo de material", "refreshSec": "Intervalo de atualização", "zoom": "Zoom", "offset": "Deslocamento", "scale": "Esticar"}, "setTime": {"timeZone": "<PERSON><PERSON>/Te<PERSON>", "y60Channels": "Nota: Esta função requer a versão CardSystem_v5.2.6.3 ou superior!", "theTimeZone": "<PERSON><PERSON>", "setUpThe": "Configurar", "query": "Consultar", "computerTime": "Calibrar o relógio para o tempo do computador", "ledTime": "Consultar o tempo atual do dispositivo LED", "queryFails": "Falha na consulta!", "versionCardSystem": "Por favor, verifique a versão do CardSystem!", "deviceTimeZone": "Fuso horário do dispositivo", "setupFailed": "Configuração falhou", "setupSuccess": "Configuração bem-sucedida", "connectTo485": "Conectar ao 485", "calibrationFailure": "Falha na calibração", "successfulCalibration": "Calibração bem-sucedida", "querySuccessful": "Consulta bem-sucedida", "synchronizationSettings": "Configurações de sincronização", "model": "Modo", "masterSlave": "Mestre/Escravo", "IdentificationCode": "Código de identificação", "timeOffset": "Deslocamento de tempo (ms)", "screenBrightness": "<PERSON><PERSON><PERSON> da tela", "volume": "Volume", "screenSwitch": "Interruptor da tela", "synchronizationInterval": "Intervalo de sincronização", "lastSynchronousTime": "Último tempo de sincronização", "minTime": "(minutos/vez)", "main": "Mestre", "from": "Escravo", "masterSlaveMode": "Modo mestre/escravo", "NTPpathNull": "Quando o caminho NTP está vazio, ocorrerá um timeout, mas a configuração será bem-sucedida!", "serverAddress": "Endereço do servidor NTP", "selectA": "Por favor, selecione um modo mestre/escravo", "selectATime": "Por favor, selecione um fuso horário"}, "synchronous": {"unknownError": "<PERSON><PERSON>conhe<PERSON>", "doesNotExist": "Erro no estado da rede do cartão, por favor, verifique a versão do CardSystem", "format": "Nenhuma configuração de tempo"}, "home": {"totalNumber": "Número total de dispositivos", "onlineRate": "Taxa online", "number": "Número", "brightScreen": "Taxa de tela ligada", "operating": "Volume total de operações", "program": "Volume total de programas", "switchDate": "Data de troca", "month": "<PERSON><PERSON><PERSON>", "Announcement": "<PERSON><PERSON><PERSON>", "determined": "Pendente", "programmeStatistics": "Estatísticas de criação de programas", "operationStatistics": "Estatísticas de operações do usuário", "releasePeople": "Pessoa de lançamento", "date": "Data", "noMoreAnnouncements": "<PERSON>ão há mais anún<PERSON>!", "show": "Programa", "by": "Volume total de auditoria", "operat": "Volume de operações", "operatingSpeed": "Velocidade de operação (ms)", "Reviewrate": "Taxa de aprovação de auditoria", "statistics": "Estatísticas de lançamento de programas", "cardNumber": "Número do cartão", "releaseAmount": "Volume total de lançamentos", "successRate": "Taxa de sucesso", "totalSuccess": "Volume total de sucessos", "successful": "Sucesso", "failure": "<PERSON><PERSON><PERSON>", "founder": "<PERSON><PERSON><PERSON>", "TotalAverageMilliseconds": "Média total de milissegundos", "great": "Excelente", "good": "Bo<PERSON>", "center": "Médio", "poor": "<PERSON><PERSON><PERSON>", "NaN": "NaN", "clickRefresh": "Clique para atualizar", "warningNotice": "Notificação de alarme", "temperatureWarning": "Alerta de temperatura", "humidityWarning": "Alerta de umidade", "voltageWarning": "Alerta de voltagem do cartão", "voltage1Warning": "Alerta de voltagem externa 1", "voltage2Warning": "Alerta de voltagem externa 2", "doorOpenWarning": "Alerta de porta aberta", "smokeWarning": "Alerta de fumaça", "unknownWarning": "Al<PERSON>a desconhecido", "temporarilyNoData": "Sem dados temporariamente", "showsSentProgram": "Mostrar cartões que enviaram programas", "announcementDetails": "Detalhes do anúncio", "policeDetails": "Detalhes do alarme", "untreated": "Não tratado", "haveDeal": "Trata<PERSON>", "noMoreCalls": "<PERSON>ão há mais alarmes!", "refreshSuccessful": "Atualização bem-sucedida!", "DynamicLargeScreen": "Plataforma visual de terminal inteligente", "piece": "Peça", "EquipmentStatistics": "Estatísticas de equipamentos", "unitPCS": "Unidade: peça", "PoleStatistics": "Estatísticas de terminais", "ControlStatistics": "Estatísticas de controle", "UnitTimes": "Unidade: vezes", "TotalNumberOfPoles": "Número total de terminais"}, "announcement": {"titleText": "Título/Conteúdo do anúncio", "title": "<PERSON><PERSON><PERSON><PERSON>", "enterTitle": "Por favor, insira o título do anúncio", "content": "<PERSON><PERSON><PERSON><PERSON>", "enterContent": "Por favor, insira o conteúdo do anúncio", "also": "<PERSON>da pode inserir", "character": "caracteres"}, "resetPWD": {"resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "accountNumber": "Inserir número da conta", "repairMethod": "Selecionar método de reparo", "changePassword": "<PERSON><PERSON><PERSON><PERSON>", "success": "Sucesso", "enterResetPassword": "Por favor, insira o número da conta para redefinir a senha"}, "police": {"notOpenSettingsNull": "Se não clicar em abrir, será considerado como vazio", "monitoringItems": "Itens de monitoramento", "hasBeenOpen": "Abe<PERSON>o", "lower": "Limite inferior", "ceiling": "Limite superior", "haveSmoke": "<PERSON><PERSON>", "openDoorAlarm": "Alarme de porta aberta", "turnSmokeAlarm": "Ativar alarme de fumaça", "checkCardSysterm": "Não foi possível analisar a operação, verifique primeiro a versão do CardSysterm", "isNotOpened": "Erro no estado de conexão do cartão", "sureToSetAlarmThresholds": "Tem certeza de que deseja definir o limite de alarme?", "upperAndLowerEmpty": "Para itens de monitoramento ativados, os limites superior e inferior não podem estar vazios", "numEmpty": "Para itens de monitoramento ativados, os valores dos limites devem ser numéricos", "upperGreaterLower": "Para itens de monitoramento ativados, o limite superior deve ser maior que o inferior", "materialLibrary": "Biblioteca de materiais"}, "hardware": {"timeQuery": "O backup dos parâmetros leva tempo. Se o arquivo de backup não for encontrado imediatamente, tente atualizar mais tarde!", "restoreParam": "Restaurar <PERSON>", "backupParameter": "Backup de parâmet<PERSON>", "hardwareStatus": "Status do hardware", "restore": "Restaurar", "akeyBackup": "Backup com um clique", "backupSuccessful": "Backup bem-sucedido", "backupSuccessful1": "A versão atual não retorna o progresso", "BackingUp": "Backup em andamento... Por favor, aguarde", "selectionCard": "Nenhum cartão selecionado, não é possível usar a função de parâmetros de hardware", "parameterRecovery": "Restauração de parâmetros em andamento...", "waitingRecover": "Aguardando restauração...", "namePackage": "Cartão/Pacote pertencente", "restoreCancel": "Erro no estado de conexão do cartão, restauração cancelada!", "readyRecovery": "Preparando para restaurar!", "tryAgain": "Todos os cartões selecionados para restauração têm problemas de conexão, verifique e tente novamente!", "recoveryComplete": "Restauração concluída!", "afterRecovery": "Restauração concluída, a janela será fechada em 3 segundos", "Recovering": "Restauração em andamento...", "timesOut": "Alguns cartões não suportam restauração de parâmetros, tempo limite da solicitação, restauração encerrada!"}, "el": {"colorpicker": {"confirm": "Confirmar", "clear": "Limpar"}, "image": {"error": "Falha ao carregar a imagem"}, "table": {"emptyText": "Sem dados"}, "pagination": {"total": "Total", "pagesize": "Linhas/página", "goto": "<PERSON>r <PERSON>", "pageClassifier": "<PERSON><PERSON><PERSON><PERSON>"}}, "task": {"name": "Nome da tarefa", "isCycle": "É cíclico", "cycleIndex": "Número de c<PERSON>los", "InfiniteLoop": "Loop infinito", "task": "<PERSON><PERSON><PERSON>", "type": "Tipo de tarefa", "text": "Conteúdo do texto", "voiceName": "Nome da voz", "speed": "Velocidade da fala", "pitch": "<PERSON> da voz", "femaleVoice": "Voz feminina", "maleVoice": "<PERSON><PERSON> mascu<PERSON>", "textToLanguage": "Texto para fala", "media": "Mí<PERSON>", "plays": "Número de reproduções", "selectMedia": "Selecionar mídia", "TemplateContent": "Conteúdo do modelo", "ImportTemplate": "Importar modelo", "import": "Importar", "normal": "Normal", "faster": "<PERSON><PERSON>", "fast": "<PERSON><PERSON><PERSON><PERSON>", "playTypes": "Modo de reprodução", "specifyPlays": "Especificar número de reproduções", "specifyPlayTime": "Especificar tempo de reprodução", "isTiming": "Ativar temporizador", "allTime": "Reproduzir durante todo o período", "inStream": "É uma tarefa de inserção", "normalTask": "Tarefa normal", "inStreamTask": "Tarefa de inserção", "clearInStream": "Limpar apenas tarefas de inserção"}, "lamp": {"poleName": "Nome do terminal", "broadcast": "Transmissão", "monitor": "Monitoramento", "environment": "Ambiente meteorológico", "lighting": "Iluminação", "Passenger": "Estatísticas de fluxo de passageiros", "longitude": "Longitude", "latitude": "Latitude", "ChooseTargeting": "Selecionar localização", "LoadingPositioning": "Carregando localização", "FailedToGetLocationInformation": "Falha ao obter informações de localização", "online": "Online", "offline": "Offline", "targetingIsSet": "Localização definida", "targetingNotSet": "Localização não definida", "listMode": "<PERSON><PERSON> de lista", "mapMode": "Modo de mapa", "updateTime": "Tempo de atualização", "TheSensorIsNot": "Nenhum sensor conectado no período atual", "getMapException": "Erro ao obter o mapa", "NoLocationData": "Sem dados de localização", "PleaseEnterThePoleNameOrdeviceID": "Por favor, insira o nome do terminal/ID do dispositivo", "radioState": "Estado da transmissão", "latAndLngNotEmpty": "Longitude e latitude não podem estar vazias", "gpsUploadState": "Upload do GPS"}, "broadcast": {"SIPAddress": "Confirmar o endereço do servidor SIP da plataforma atual como o endereço do servidor SIP", "customSIPAddressStart": "Confirmar configura<PERSON>", "customSIPAddressEnd": "como endereço do servidor SIP", "radioState1": "Apenas suporta transmissão", "radioState2": "Suporta transmissão e chamada, mas não suporta alarme", "radioState3": "Suporta transmissão e alarme, mas não suporta chamada", "radioState4": "Suporta transmissão, chamada e alarme", "SIP_account": "Conta SIP", "multicastAddress": "Endereço de multicast", "selectDate": "Por favor, selecione a data", "pauseOrOpenBroadcast": "Pausar/Iniciar tare<PERSON> de transmissão", "broadcastInfo": "<PERSON><PERSON><PERSON> da <PERSON>mis<PERSON>ão", "broadcastProgramState": "Estado do programa de transmissão", "paused": "<PERSON><PERSON><PERSON>", "playing": "Reproduzindo", "haveProgram": "Há programa em tempo real", "playMode": "Modo de reprodução", "focusMode": "Modo de foco", "fallingTone": "<PERSON> de<PERSON>", "mute": "<PERSON><PERSON>", "noProgram": "Nenhum programa disponível", "noBroadcast": "Nenhuma transmissão disponível"}, "meteorological": {"temperatureSubText": "Quando a temperatura for --, indica que houve uma exceção na consulta ou não há dados", "Illuminance": "Iluminância", "humiditySubText": "Quando a umidade for --, indica que houve uma exceção na consulta ou não há dados", "noiseSubText": "Quando o ruído for --, indica que houve uma exceção na consulta ou não há dados", "windSpeedSubText": "Quando a velocidade do vento for --, indica que houve uma exceção na consulta ou não há dados", "windDirectionSubText": "Quando a direção do vento for --, indica que houve uma exceção na consulta ou não há dados", "illuminationSubText": "Quando a iluminação for 0, indica que houve uma exceção na consulta ou não há dados", "PM10SubText": "Quando o PM10 for --, indica que houve uma exceção na consulta ou não há dados", "PM25SubText": "Quando o PM2.5 for --, indica que houve uma exceção na consulta ou não há dados", "pressureSubText": "Quando a pressão for --, indica que houve uma exceção na consulta ou não há dados", "rainFallSubText": "Quando a precipitação for --, indica que houve uma exceção na consulta ou não há dados", "radiationSubText": "Quando a radiação for --, indica que houve uma exceção na consulta ou não há dados", "pressure": "Pressão", "rainFall": "Precipitação", "radiation": "Radiação"}, "bigScreen": {"VideoSurveillance": "Monitoramento de vídeo", "DeviceList": "Lista de dispositivos", "Address": "Endereço detalhado", "NumberOfOnline": "Número de online", "OperationLog": "Log de operações", "ProgramPlayStatistics": "Estatísticas de reprodução de programas", "RequestFailed": "Falha na solicitação"}, "electricity": {"current": "<PERSON><PERSON><PERSON>", "power": "Potência", "electricity": "Energia elétrica", "voltage": "Voltagem", "currentSubText": "Quando a corrente for 0, indica que houve uma exceção na consulta ou não há dados", "powerSubText": "Quando a potência for 0, indica que houve uma exceção na consulta ou não há dados", "electricitySubText": "Quando a energia elétrica for 0, indica que houve uma exceção na consulta ou não há dados", "voltageSubText": "Quando a voltagem for 0, indica que houve uma exceção na consulta ou não há dados", "clearData": "Limpar dad<PERSON>", "clearSuccessfully": "Limpeza bem-sucedida", "clearFailed": "Falha na limpeza", "cancelClear": "<PERSON><PERSON><PERSON>", "monthlyElectricity": "Estatísticas mensais de energia elétrica", "exportElectricity": "Exportar dados mensais de energia elétrica", "setElectricityTime": "Definir período de consulta de energia elétrica", "selectTime": "Por favor, selecione o período de consulta de energia elétrica", "tip": "O período de consulta padrão é de 24 horas.", "electricityData": "Dados de energia elétrica", "curElectricityTime": "<PERSON><PERSON><PERSON> at<PERSON>"}, "monitor": {"device_ip": "IP do dispositivo", "port": "Porta", "owning_terminal": "Terminal pertencente", "monitorSaveTip": "Dispositivos online, por favor, modifique o nome de usuário e senha com cuidado, pois erros podem causar a desconexão do dispositivo", "Device_name_cannot_be_empty": "O nome do dispositivo não pode estar vazio", "Please_enter_the_device_serial_number": "Por favor, insira o número de série do dispositivo", "monitorSaveTip1": "Alterar nome de usuário e senha para: nome de usuário", "Split_screen": "Dividir tela", "PTZ_control": "Controle PTZ", "equipment": "Equipamento", "deviceIdIsNotEmpty": "O ID do dispositivo não pode estar vazio", "CommandISExist": "Comando não existe", "notExistOrNotLoggedIn": "Dispositivo não encontrado ou não logado", "isNotExist": "Dispositivo não existe", "notLoggedIn": "Dispositivo não logado", "zoom": "Zoom", "aperture": "Abertura", "focus": "Foco", "screenshot": "Captura de tela", "noPlayback": "Nenhuma gravação neste período", "downloadPlayback": "Baixar gravação", "selectDateRange": "Selecionar intervalo de datas", "tip": "Este download baixa a gravação da câmera para o servidor, aguarde um momento antes de baixar localmente.", "normal": "Câmera normal", "humanNumberStatistic": "Estatísticas de fluxo de pessoas", "insideHumanNumber": "Agrupamento de pessoas", "traffic": "Tráfego inteligente", "cameraType": "Tipo de câmera", "monitorSaveTip2": "Alterar tipo de função da câmera para:", "openTheAlarm": "Ativar alarme", "crowdAlarmThreshold": "Limite de alarme de multidão", "intervalForSendingEmails": "Intervalo de envio de e-mails", "openTheAlarmTip": "Após ativar o alarme, se o dispositivo exceder o limite de pessoas, um e-mail será enviado ao usuário do dispositivo. Após o alarme por e-mail, o próximo e-mail será enviado de acordo com o intervalo de tempo.", "offLineOrNotExist": "Dispositivo offline ou desconectado", "serialNumber": "Número de série da câmera", "rtmpStreamState": "Ativar transmissão RTMP", "streamCloseTip": "Após desativar a transmissão, você não poderá ver a imagem de monitoramento na web, confirme se deseja desativar a transmissão.", "streamOpenTip": "Após ativar a transmissão, você poderá ver a imagem de monitoramento na web, esta operação consumirá tráfego, confirme se deseja ativar a transmissão."}, "pay": {"contactInformation": "Informações de contato", "addressValue": "<PERSON>ni<PERSON> 1, Central, Hong Kong", "savingsAccountNumber": "Número da conta poupança", "xixunCompanyValue": "Shanghai Xixun Electronic Technology Co., Ltd.", "bankName": "Nome do banco", "bankNameValue": "The Hongkong and Shanghai Banking Corporation Limited", "swiftCode": "Código SWIFT", "tips1": "Por favor, compre um dispositivo VIP para que ele possa funcionar normalmente.", "tips": "Após fazer login com sucesso, assine o VIP nos serviços pagos para que o dispositivo funcione normalmente.", "totalPrice": "Preço total", "contactPerson": "Pessoa de contato", "contactPersonTips": "Por favor, insira a pessoa de contato", "telephone": "Telefone de contato", "telephoneTips": "Por favor, insira o telefone de contato", "address": "Endereço da empresa", "addressTips": "Por favor, insira o endereço da empresa", "companyName": "Nome completo da empresa", "companyNameTips": "Por favor, insira o nome completo da empresa", "vipDuration": "Duração válida do VIP/ano", "vip4": "VIP Diamante de Alta Qualidade 4", "upgradeToPaidVersion": "Atualizar para versão paga", "silverCardVip": "VIP cartão prata", "goldCardVip": "VIP cartão ouro", "diamondVip": "VIP diamante", "superVip": "Super VIP", "orderService": "Solicitar serviço", "currentVersion": "Versão atual:", "numberOfTerminals": "Número de terminais", "TheRemainingAmount": "Quantidade restante", "ExpireDate": "Data de expiração", "selectVersion": "Selecionar versão", "SelectDuration(years)": "Selecionar duração (anos)", "totalOrder": "Total do pedido", "submitOrder": "Enviar pedido", "freeVersion": "<PERSON><PERSON><PERSON> gratuita", "price": "Preço", "ThereAreUnpaidOrders": "Existem pedidos não pagos", "ThereIsAnUnpaidOrderPleaseGoToPay": "Existe um pedido não pago, por favor, vá pagar.", "OrderRecord": "Registro de pedidos", "100ControlCards": "100 cartões de controle", "500ControlCards": "500 cartões de controle", "NumberOfControlCards1500": "1500 cartões de controle", "unpaid": "Não pago", "transactionCreation": "Criação de transação", "UnpaidTransactionTimeoutClosed": "Transação não paga expirada e fechada", "paymentSuccessful": "Pagamento bem-sucedido", "OrderDetails": "Detalhes do pedido", "pay": "<PERSON><PERSON>", "orderNumber": "Número do pedido", "Years": "<PERSON><PERSON>", "PaymentStatus": "Status do pagamento", "amount": "Valor", "newPurchase": "Nova compra", "Renewal": "Renovação", "upgrade": "Atualização", "PayForTheOrder": "Pagar o pedido", "ExpectedPeriod": "Per<PERSON>do esperado: ", "to": "até", "ActualDeadlineIsSubjectToPayment": "O prazo real depende do pagamento", "cancelOrder": "Cancelar pedido", "theOrderWillBeAutomaticallyClosed": "O pedido é válido por 15 dias, pague a tempo. Após 14 dias sem pagamento, o pedido será fechado automaticamente", "AfterOrderIsPaidSuccessfully": "Nota: Após o pagamento bem-sucedido do pedido, você pode acessar", "QueryPaymentInformationOnThePage": "a página para consultar as informações de pagamento", "paymentMethod": "Método de pagamento", "publicAccount": "Conta corporativa", "AccountName": "Nome da conta:", "AccountBank": "Banco:", "LinkNumber": "Número de ligação:", "account": "Conta:", "uploadTheBankReceipt": "Se você já fez o pagamento para a conta acima, por favor, envie o comprovante bancário.", "receivedByMajorBanks": "O tempo de transferência para contas corporativas varia de 2 horas a 3 dias úteis.", "notifyYouViaSMS": "Nossa equipe ativará o serviço assim que receber o pagamento.", "UploadBankReceipt": "Enviar comprovante bancário", "contactOurSalesStaff": "Em caso de dúvidas, entre em contato com nossa equipe de vendas.", "NotesForPublicTransfers": "Notas para transferências corporativas:", "submittedForFinancialReview": "Pagamentos offline têm atraso e precisam enviar o comprovante bancário para revisão financeira.", "TransfersFromPersonalAccounts": "Transferências de contas pessoais para nossa conta corporativa só podem emitir notas fiscais pessoais comuns.", "IfTheCompanyAccountIsTransferred": "Transferências de contas corporativas para nossa conta corporativa podem emitir notas fiscais de valor agregado comuns ou especiais.", "uploadTip": "Suporta formatos JPG ou PNG, tamanho máximo de 5M", "BankCardNumber": "Número do cartão bancário", "bankCardNumberWhenTransferring": "Por favor, insira o número do cartão bancário usado na transferência", "transactionHour": "Hora da transação", "CancellationLineItemCannotBeEmpty": "O item de cancelamento do pedido não pode estar vazio", "WaitingForSellerToConfirm": "Aguardando confirmação do vendedor", "PurchaseStatus": "Status da compra", "bankReceipt": "Comprovante bancário", "Serve": "Serviço", "Optional": "Opcional", "deadline": "Prazo final:", "orderUpdateTips": "Só é possível modificar o comprovante ou o número do cartão bancário enviado no pedido, para outras dúvidas, entre em contato com nossa equipe.", "PleaseUploadBankReceipt": "Por favor, envie o comprovante bancário", "PleaseEnterBankCardNumber": "Por favor, insira o número do cartão bancário", "NoRelatedOrders": "Nenhum pedido relacionado", "ErrorUploadingBankReceipt": "Erro ao enviar comprovante bancário", "changeVipStateTip": "Se alterar o status VIP do usuário atual para Super VIP, todos os usuários sob o ID da empresa do usuário atual serão Super VIP."}, "statistic": {"enableHumanStatistic": "Ativar estatísticas de fluxo de pessoas", "queryHumanStatisticToday": "Consultar fluxo de pessoas do dia", "enter": "Número de entradas", "exited": "Número de saídas", "countingMonitoring": "Monitoramento de estatísticas de fluxo de pessoas", "viewChart": "<PERSON>er g<PERSON>", "currentNumber": "Número atual de pessoas", "lineChart": "Gráfico de linha do número de pessoas na área", "areaPeopleNum": "Número de pessoas na área", "selectHistoricalData": "Consultar dados históricos", "sendData": "Enviar dados", "keyTip": "Após ativar a exibição de dados, a chave será gerada automaticamente", "isShowHumanNumber": "A função de exibição de dados de fluxo de pessoas do terminal está ativada?", "dataKey": "Chave", "noDataKey": "O dispositivo atual não tem chave", "clickCopy": "Clique para copiar", "copySuccess": "Cópia be<PERSON>-sucedida"}, "alarm": {"alarmNum": "Número de alarme", "alarmPeople": "Pessoa do <PERSON>e", "call": "<PERSON><PERSON><PERSON>", "receive": "Receptor", "callTime": "Tempo de chamada", "channel": "Canal", "dstChannel": "Canal de destino", "alarmDeviceInfo": "Informações do dispositivo de alarme", "setCallingVolume": "Definir volume de chamada", "callingVolume": "Volume de chamada", "notEnable": "<PERSON><PERSON>", "setAlarmInfo": "Definir informações do dispositivo de alarme", "sipAccount": "Conta SIP", "sipServiceAddress": "Endereço do servidor SIP", "sipServicePort": "Porta do servidor SIP", "accountState": "Estado da conta", "alarmDeviceNetwork": "Informações de rede do dispositivo de alarme", "setNetwork": "Definir estado da rede", "dynamic": "Dinâmico", "static": "Está<PERSON><PERSON>", "gateway": "Gateway", "subnetMask": "Máscara de sub-rede", "alarmAccount": "Conta de alarme", "accountType": "Tipo de conta", "registerAlarmAccount": "Registrar conta do dispositivo de alarme", "registerPhoneNumber": "Registrar número de telefone", "accountRule": "A conta não pode estar vazia e deve ter 11 dígitos ou letras", "account": "Conta", "batchSettings": "Configuração em lote", "alarmNetworkTip": "Mantenha pressionado o dispositivo de alarme até ouvir um sinal sonoro, pressione uma vez para reproduzir o IP, pressione três vezes para alternar entre IP dinâmico e estático", "alarmAddressTip": "Antes de definir o endereço de alarme, configure a conta do dispositivo de alarme nas informações do dispositivo de alarme", "alarmAddressSetTip": "No estado padrão, a plataforma definirá o endereço de alarme como o endereço da plataforma", "defaultState": "Padrão", "custom": "Personalizado", "myPhoneNumber": "<PERSON><PERSON>", "calledPhoneNumber": "Número discado", "alarmInfoTip": "Antes de configurar as informações do dispositivo de alarme, registre uma conta", "backupCalledPhoneNumber": "Número de backup"}, "traffic": {"enableTraffic": "Ativar tráfego inteligente", "eventName": "Nome do evento", "plateNumber": "Número da placa", "plateType": "Tipo de placa", "plateColor": "<PERSON><PERSON> <PERSON>laca", "vehicleColor": "Cor do veículo", "vehicleType": "Tipo de veículo", "vehicleSize": "Tamanho do veículo", "illegalPlace": "Local da infração", "eventTime": "Hora do evento", "downloadEventPic": "Baixar foto da infração", "illegalPic": "Foto da infração"}, "radar": {"radarSpeed": "Velocidade do radar", "radarSetting": "Configuração do radar", "fastestCar": "<PERSON><PERSON> mais r<PERSON>", "closestCar": "<PERSON><PERSON> mais próximo", "setResponseTime": "Definir tempo de resposta", "setOutputTarget": "Definir alvo de saída", "setMinSpeed": "Velocidade mínima de detecção", "setMaxSpeed": "Velocidade máxima de detecção", "setSensitivity": "Sensibilidade", "isConnect": "O radar está conectado?", "speed": "Velocidade", "parameter": "Parâmetro", "speedLimitRange": "Intervalo de limite de velocidade", "addSpeedLimitRange": "Por favor, adicione o intervalo de limite de velocidade", "minSpeed": "A velocidade mínima", "maxSpeed": "Velocidade máxima velocidade", "radarSpeedLimit": "Limite de velocidade do radar"}, "ac": {"apOnlineRate": "Taxa de AP online", "apOnlineNumber": "Número de AP online", "apSum": "Total de AP", "onlineTerminal": "Número de terminais online", "flowStatistics": "Estatísticas de fluxo", "flowStatisticsToday": "Estatísticas de fluxo de hoje", "name": "Nome do AC", "macAddress": "Endereço MAC", "belongArea": "<PERSON><PERSON> pertencente", "belongOrganization": "Organização pertencente", "belongProject": "Projeto pertencente", "userOnlineCount": "Total de logins de usuários", "acOnlineUserCount": "Total de usuários online do AC", "upstreamTraffic": "Fluxo de upload", "downlinkTraffic": "Fluxo de download", "refresh": "<PERSON><PERSON><PERSON><PERSON>"}, "userAuth": {"pass": "Verificação bem-sucedida", "fail": "Falha na verificação", "company": "Certificação empresarial", "personal": "Certificação pessoal", "unverified": "Não verificado", "certificationAudit": "Auditoria de certificação", "authMode": "Modo de autenticação", "authInfo": "Informações de autenticação", "enterpriseLicense": "Licença empresarial", "OfficialSeal": "Certificado de selo empresarial", "FrontOfIdentityCard": "Frente do documento de identidade", "ReverseOfIDCard": "Verso do documento de identidade", "reason": "Motivo", "authentication": "Autenticação", "pleaseUploadEnterpriseLicenseOfficialSeal": "Por favor, envie o certificado de selo empresarial e a licença empresarial", "updateAuth": "Atualizar autentica<PERSON>", "uploadAuth": "Enviar autenticação"}, "cat1": {"temp": "Temperatura do modelo", "power": "Potência ativa", "electricEnergy": "Energia ativa", "roadOne": "Rota 1", "roadTwo": "Rota 2", "addDevice": "Adicionar dispositivo", "channel": "Canal", "colorTemp": "Temperatura de cor", "scheduleTip": "Este temporizador começa no horário RealTime configurado. Por exemplo, se o horário configurado for 12:30, ele começará às 12:30 e terminará no próximo ponto de tempo, com o estado da luz única sendo o brilho e a temperatura de cor configurados. Para desligar a luz única, o brilho pode ser configurado como 0."}, "notify": {"NotificationStrategy": "Estratégia de notificação", "NotifyTip": "(Após o usuário ativar a notificação, será notificado por e-mail no horário especificado)", "OfflineNotify": "Notificação offline", "CardNotWorkingNotification": "Notificação de cartão não funcionando", "tactics": "Estratégia (horas)", "selectAll": "Selecionar tudo", "PleaseSelectNotification": "Por favor, selecione a estratégia de notificação", "SendingMailbox": "Caixa de envio", "SendingTime": "<PERSON><PERSON><PERSON><PERSON>", "Username/EmailAddress": "Nome de usuário/Caixa de envio", "OffLineTime": "Tempo offline", "AbnormalTime": "Tempo anormal", "NumberOfCardsRLastTime": "Número de cartões recebidos da última vez", "NumberOfCardsReceivedExceptionOccurs": "Número de cartões recebidos em caso de exceção"}, "thirdPartyAd": {"enabledThirdPartyAd": "Integração de publicidade de terceiros", "thirdPartyUrl": "Endereço do serviço de terceiros", "loopTip": "Reproduzir em loop com os anúncios existentes no dispositivo, vários anúncios serão distribuídos uniformemente na lista de anúncios existentes", "onceTip": "Reproduzir uma vez entre os anúncios existentes no dispositivo, vários anúncios serão distribuídos uniformemente na lista de anúncios existentes", "mutexTip": "Reprodução exclusiva em loop", "adInterval": "Intervalo de anúncios", "queryThirdPartyAd": "Consultar publicidade de terceiros", "downloadUrl": "URL de download", "impression": "Informações de reprodução", "playHour": "Ho<PERSON><PERSON><PERSON> de reprodução", "playCount": "Número de reproduções"}, "crossArrow": {"addToCross": "Adicionar à seta cruzada", "arrowProgram": "Programa de seta", "greenArrow": "Seta verde", "redArrow": "<PERSON> vermel<PERSON>", "closeScreen": "Des<PERSON>r tela", "curProgram": "Programa atual", "remove": "Remover", "batchRemove": "Remoção em lote"}, "manual": {"operationDocument": "Documento de operação", "videoTutorial": "Tutorial em vídeo", "resourceManagement": "Gerenciamento de recursos", "manualAndTutorial": "Manual e tutorial em vídeo", "universal": "Universal", "fileTips": "Apenas um tipo de arquivo pode ser enviado por vez"}, "myEditor": {"bottomCenter": "Alinhar inferior", "topCenter": "Alinhar superior", "verticalCenter": "Alinhar central"}, "employee": {"today": "Hoje", "yesterday": "Ontem", "last7Days": "Últimos 7 dias", "last30Days": "Últimos 30 dias", "lineChart": "Gráfico de linha", "barChart": "Gráfico de barras", "unitPerson": "Unidade: pessoa", "man": "<PERSON><PERSON>", "woman": "<PERSON><PERSON><PERSON>", "tips": "Após ativar, as estatísticas de fluxo de pessoas serão coletadas automaticamente. Após o sucesso, o dispositivo poderá ser visualizado em Estatísticas de Fluxo de Pessoas -> Estatísticas de Fluxo de Pessoas V1"}}