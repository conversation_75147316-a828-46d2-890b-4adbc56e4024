import Vue from 'vue'
import Router from 'vue-router'
import http from '@/utils/httpRequest'
import { isURL } from '@/utils/validate'
import { clearLoginInfo } from '@/utils'
// import i18n from '@/language'

Vue.use(Router)

// 开发环境不使用懒加载, 因为懒加载页面太多的话会造成webpack热更新太慢, 所以只有生产环境使用懒加载
const _import = require('./import-' + process.env.NODE_ENV)

// 全局路由(无需嵌套上左右整体布局)
const globalRoutes = [
  { path: '/login', component: _import('common/login'), name: 'login', meta: { title: '登录' } },
  { path: '/404', component: _import('common/404'), name: '404', meta: { title: '404未找到' } },
  { path: '/register', component: _import('common/register'), name: 'register', meta: { title: '注册' } },
  { path: '/registerVIP', component: _import('common/registerVIP'), name: 'registerVIP', meta: { title: '注册VIP' } },
  { path: '/password', component: _import('common/password'), name: 'password', meta: { title: '修改密码' } },
  { path: '/agreement', component: _import('common/agreement'), name: 'agreement', meta: { title: '协议事项' } },
  { path: '/agreementEn', component: _import('common/agreementEn'), name: 'agreementEn', meta: { title: '协议事项' } },
  { path: '/big-screen', component: _import('common/big-screen'), name: 'big-screen', meta: { title: '大屏模式' } },
  { path: '/manual', component: _import('common/manual'), name: 'manual', meta: { title: '使用手册' } },
  { path: '/videoTutorial', component: _import('common/videoTutorial'), name: 'videoTutorial', meta: { title: '视频教程' } },
  { path: '/operationDocument', component: _import('common/operationDocument'), name: 'operationDocument', meta: { title: '操作文档' } },
  { path: '/riskReminder', component: _import('common/riskReminder'), name: 'riskReminder', meta: { title: '风险告知书' } },
  {
    path: '/videoSurveillance',
    component: _import('modules/screen/device/device-monitor'),
    name: 'device-monitor',
    meta: { title: '视频监控' },
    beforeEnter (to, from, next) {
      let token = Vue.cookie.get('token')
      if (!token || !/\S/.test(token)) {
        clearLoginInfo()
        next({ name: 'login' })
      }
      next()
    }
  },
  {
    path: '/cameraMonitoring',
    component: _import('modules/screen/device/device-cameraMonitoring'),
    name: 'device-cameraMonitoring',
    meta: { title: '摄像头监控' },
    beforeEnter (to, from, next) {
      let token = Vue.cookie.get('token')
      if (!token || !/\S/.test(token)) {
        clearLoginInfo()
        next({ name: 'login' })
      }
      next()
    }
  }
]

// 主入口路由(需嵌套上左右整体布局)
const mainRoutes = [{
  path: '/',
  component: _import('main'),
  name: 'main',
  redirect: { name: 'home' },
  meta: { title: '主入口整体布局' },
  children: [
    // 通过meta对象设置路由展示方式
    // 2. iframeUrl: 是否通过iframe嵌套展示内容, '以http[s]://开头': 是, '': 否
    // 提示: 如需要通过iframe嵌套展示内容, 但不通过tab打开, 请自行创建组件使用iframe处理!
    { path: '/home', component: _import('common/home'), name: 'home', meta: { title: '首页' } },
    { path: '/updateUserInfo', component: _import('modules/sys/updateUserInfo'), name: 'updateUserInfo', meta: { title: '个人信息' } }
  ],
  beforeEnter (to, from, next) {
    let token = Vue.cookie.get('token')
    if (!token || !/\S/.test(token)) {
      clearLoginInfo()
      next({ name: 'login' })
    }
    next()
  }
},{
  path: '/standard',
  component: _import('modules/program/standard'),
  name: 'standard',
  meta: { title: '节目编辑' },
  beforeEnter (to, from, next) {
    let token = Vue.cookie.get('token')
    if (!token || !/\S/.test(token)) {
      clearLoginInfo()
      next({ name: 'login' })
    }
    next()
  }
},
// 废弃
{
  path: '/extend-cubeScreen',
  component: _import('modules/screen/extendedProgram/cubeScreen/standard'),
  name: 'extend-cubeScreen',
  meta: { title: '魔方屏编辑' },
  beforeEnter (to, from, next) {
    let token = Vue.cookie.get('token')
    if (!token || !/\S/.test(token)) {
      clearLoginInfo()
      next({ name: 'login' })
    }
    next()
  }
}]

const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  isAddDynamicMenuRoutes: false, // 是否已经添加动态(菜单)路由
  routes: globalRoutes.concat(mainRoutes)
})

router.beforeEach((to, from, next) => {
  // 添加动态(菜单)路由
  // 1. 已经添加 or 全局路由, 直接访问
  // 2. 获取菜单列表, 添加并保存本地存储
  if (router.options.isAddDynamicMenuRoutes || fnCurrentRouteType(to, globalRoutes) === 'global') {
    next()
  } else {
    http({
      url: http.adornUrl('/sys/menu/nav'),
      method: 'get',
      params: http.adornParams()
    }).then(({data}) => {
      if (data && data.code === 0) {
        fnAddDynamicMenuRoutes(data.menuList)
        router.options.isAddDynamicMenuRoutes = true
        sessionStorage.setItem('menuList', JSON.stringify(data.menuList || '[]'))
        sessionStorage.setItem('permissions', JSON.stringify(data.permissions || '[]'))
        next({ ...to, replace: true })
      } else {
        sessionStorage.setItem('menuList', '[]')
        sessionStorage.setItem('permissions', '[]')
        next()
      }
    }).catch((e) => {
      console.log(`%c${e} 请求菜单列表和权限失败，跳转至登录页！！`, 'color:blue')
      router.push({ name: 'login' })
    })
  }
})

/**
 * 判断当前路由类型, global: 全局路由, main: 主入口路由
 * @param {*} route 当前路由
 */
function fnCurrentRouteType (route, globalRoutes = []) {
  var temp = []
  for (var i = 0; i < globalRoutes.length; i++) {
    if (route.path === globalRoutes[i].path) {
      return 'global'
    } else if (globalRoutes[i].children && globalRoutes[i].children.length >= 1) {
      temp = temp.concat(globalRoutes[i].children)
    }
  }
  return temp.length >= 1 ? fnCurrentRouteType(route, temp) : 'main'
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes (menuList = [], routes = []) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].list && menuList[i].list.length >= 1) {
      temp = temp.concat(menuList[i].list)
    } else if (menuList[i].url && /\S/.test(menuList[i].url)) {
      menuList[i].url = menuList[i].url.replace(/^\//, '')
      var route = {
        path: menuList[i].url.replace('/', '-'),
        component: null,
        name: menuList[i].url.replace('/', '-'),
        meta: {
          menuId: menuList[i].menuId,
          title: menuList[i].name,
          isDynamic: true,
          iframeUrl: ''
        }
      }
      // url以http[s]://开头, 通过iframe展示
      if (isURL(menuList[i].url)) {
        route['path'] = `i-${menuList[i].menuId}`
        route['name'] = `i-${menuList[i].menuId}`
        route['meta']['iframeUrl'] = menuList[i].url
      } else {
        try {
          route['component'] = _import(`modules/${menuList[i].url}`) || null
        } catch (e) {}
      }
      routes.push(route)
    }
  }
  if (temp.length >= 1) {
    fnAddDynamicMenuRoutes(temp, routes)
  } else {
    mainRoutes[0].name = 'main-dynamic'
    mainRoutes[0].children = routes
    router.addRoutes([
      mainRoutes[0],
      { path: '*', redirect: { name: '404' } }
    ])
    sessionStorage.setItem('dynamicMenuRoutes', JSON.stringify(mainRoutes[0].children || '[]'))
    // console.log('\n')
    // console.log('%c!<-------------------- 动态(菜单)路由 s -------------------->', 'color:blue')
    // console.log(mainRoutes.children)
    // console.log('%c!<-------------------- 动态(菜单)路由 e -------------------->', 'color:blue')
  }
}

const originalPush = Router.prototype.push
Router.prototype.push = function push (location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

export default router
