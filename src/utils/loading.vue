<template>
    <div class="loadClass" v-if="loadBool">
        <div id="loading3">
            <div class="demo3"></div>
            <div class="demo3"></div>
            <div class="demo3"></div>
            <div class="demo3"></div>
            <div class="demo3"></div>
            <div class="demo3"></div>
            <div class="demo3"></div>
            <div class="demo3"></div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        loadBoolValue: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            loadBool: false
        }
    },
    mounted() {
        this.loadBool = this.loadBoolValue
    },
    watch: {
        loadBoolValue: function (newVal, oldVal) {
            this.loadBool = newVal
        }
    }
}
</script>

<style scoped>
.loadClass{
  height:50px;
  width:50px;
  background-color:transparent;
  position:absolute;
  z-index:9;
  left:45%;
  top:45%;
}
 #loading3 {
      position: relative;
      width: 50px;
      height: 50px;
  }
  .demo3 {
      width: 4px;
      height: 4px;
      border-radius: 2px;
     background: #28bef5;
     position: absolute;
     animation: demo3 linear 0.8s infinite;
     -webkit-animation: demo3 linear 0.8s infinite;
 }
 .demo3:nth-child(1){
     left: 24px;
     top: 2px;
     animation-delay:0s;
 }
 .demo3:nth-child(2){
     left: 40px;
     top: 8px;
     animation-delay:0.1s;
 }
 .demo3:nth-child(3){
     left: 47px;
     top: 24px;
     animation-delay:0.1s;
 }
 .demo3:nth-child(4){
     left: 40px;
     top: 40px;
     animation-delay:0.2s;
 }
 .demo3:nth-child(5){
     left: 24px;
     top: 47px;
     animation-delay:0.4s;
 }
 .demo3:nth-child(6){
     left: 8px;
     top: 40px;
     animation-delay:0.5s;
 }
 .demo3:nth-child(7){
     left: 2px;
     top: 24px;
     animation-delay:0.6s;
 }
 .demo3:nth-child(8){
     left: 8px;
     top: 8px;
     animation-delay:0.7s;
 }
 
 @keyframes demo3 
 {
     0%,40%,100% {transform: scale(1);}
     20% {transform: scale(3);}
 }
 @-webkit-keyframes demo3 
 {
     0%,40%,100% {transform: scale(1);}
     20% {transform: scale(3);}
 }
</style>