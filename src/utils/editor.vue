<template>
    <div class="editor" :style="`height: calc(100vh - ${heightProps}px)`">
        <Toolbar style="border-bottom: 1px solid #ccc;" :editor="editor"
            :defaultConfig="toolbarConfig" :mode="mode" />
        <Editor :style="`overflow-y: hidden;height: calc(100vh - ${heightEditorProps}px);`" v-model="html" :defaultConfig="editorConfig" :mode="mode"
            @onCreated="onCreated" @onChange="onChange"/>
        <div id="fontSizeDiv" class="editorExtend">
            <el-input-number v-model="fontSize" :min="1" :max="100"
                controls-position="right" size="small"
            />
            <el-button style="margin-left: 2px;" type="primary" size="small" @click="clickFontSize(true)">确定</el-button>
            <el-button style="margin-left: 2px;" size="small" @click="clickFontSize(false)">取消</el-button>
        </div>
        <div id="fontColorDiv" class="editorExtend">
            <el-color-picker size="small" v-model="fontColor"/>
            <el-button style="margin-left: 2px;" type="primary" size="small" @click="clickFontColor(true)">确定</el-button>
            <el-button style="margin-left: 2px;" size="small" @click="clickFontColor(false)">取消</el-button>
        </div>
        <div id="bgColorDiv" class="editorExtend">
            <el-color-picker size="small" v-model="bgColor"/>
            <el-button style="margin-left: 2px;" type="primary" size="small" @click="clickBgColor(true)">确定</el-button>
            <el-button style="margin-left: 2px;" size="small" @click="clickBgColor(false)">取消</el-button>
        </div>
        <div class="editorExtend" id="verticalCenterDiv" @click="clickAlignmentMode('vertical')"></div>
        <div class="editorExtend" id="bottomCenterDiv" @click="clickAlignmentMode('bottom')"></div>
        <div class="editorExtend" id="topCenterDiv" @click="clickAlignmentMode('top')"></div>
    </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import {i18nChangeLanguage, Boot} from "@wangeditor/editor";
import myfontSize from  "./myEditor/MyfontSize"
import myfontColor from  "./myEditor/MyfontColor"
import mybgColor from  "./myEditor/MybgColor"
import verticalCenter from  "./myEditor/VerticalCenter"
import bottomCenter from  "./myEditor/BottomCenter"
import topCenter from  "./myEditor/TopCenter"

export default {
    components: { Editor, Toolbar },
    props: {
        htmlProps: {
            type: String,
            default: ""
        },
        heightProps: {
            type: Number,
            default: 410
        },
        heightEditorProps: {
            type: Number,
            default: 490
        },
        programHeight: {
            type: Number,
            default: 300
        },
        isAlignmentMode: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            editor: null,
            html: "",
            toolbarConfig: {
                // insertKeys: {
                //     index: 9,
                //     keys: [
                //         "VerticalCenter",
                //         // "BottomCenter",
                //         "TopCenter",
                //         "MyfontSize",
                //         "MyfontColor",
                //         "MybgColor",
                //     ]
                // },
                /* 显示哪些菜单，如何排序、分组 */
                // 新增菜单
                toolbarKeys: [
                    // 菜单 key
                    // 'headerSelect', // 文本、H1、H2...
                    // 'color',// 字体颜色
                    // 'bgColor',// 背景色
                    'bold', // 加粗
                    'italic',// 斜体
                    'fontSize',// 字号
                    'fontFamily',// 字体
                    'justifyLeft', // 左对齐
                    'justifyRight', // 右对齐
                    'justifyCenter', // 居中对齐
                ],
                excludeKeys:[ 'fontSize' ]
            },
            editorConfig: {
                placeholder: this.$t('program.PleaseEnterContent'),
                // autoFocus: false,
                // 所有的菜单配置，都要在 MENU_CONF 属性下
                MENU_CONF: {
                    // 自定义颜色
                    /* color: {
                        colors: [
                            'rgb(0, 0, 0)', 'rgb(38, 38, 38)', 'rgb(89, 89, 89)', 'rgb(140, 140, 140)', 'rgb(191, 191, 191)',
                            'rgb(217, 217, 217)', 'rgb(233, 233, 233)', 'rgb(245, 245, 245)', 'rgb(250, 250, 250)', 'rgb(255, 255, 255)',
                            'rgb(225, 60, 57)', 'rgb(231, 95, 51)', 'rgb(235, 144, 58)', 'rgb(245, 219, 77)', 'rgb(114, 192, 64)',
                            'rgb(89, 191, 192)', 'rgb(66, 144, 247)', 'rgb(54, 88, 226)', 'rgb(106, 57, 201)', 'rgb(216, 68, 147)',
                            'rgb(251, 233, 230)', 'rgb(252, 237, 225)', 'rgb(252, 239, 212)', 'rgb(252, 251, 207)', 'rgb(231, 246, 213)',
                            'rgb(218, 244, 240)', 'rgb(217, 237, 250)', 'rgb(224, 232, 250)', 'rgb(237, 225, 248)', 'rgb(246, 226, 234)',
                            'rgb(255, 163, 158)', 'rgb(255, 187, 150)', 'rgb(255, 213, 145)', 'rgb(255, 251, 143)', 'rgb(183, 235, 143)',
                            'rgb(135, 232, 222)', 'rgb(145, 213, 255)', 'rgb(173, 198, 255)', 'rgb(211, 173, 247)', 'rgb(255, 173, 210)',
                            'rgb(255, 77, 79)', 'rgb(255, 122, 69)', 'rgb(255, 169, 64)', 'rgb(255, 236, 61)', 'rgb(115, 209, 61)',
                            'rgb(54, 207, 201)', 'rgb(64, 169, 255)', 'rgb(89, 126, 247)', 'rgb(146, 84, 222)', 'rgb(247, 89, 171)',
                            'rgb(255, 0, 0)', 'rgb(212, 56, 13)', 'rgb(212, 107, 8)', 'rgb(212, 177, 6)', 'rgb(0, 255, 0)',
                            'rgb(8, 151, 156)', 'rgb(9, 109, 217)', 'rgb(0, 0, 255)', 'rgb(83, 29, 171)', 'rgb(196, 29, 127)',
                            'rgb(130, 0, 20)', 'rgb(135, 20, 0)', 'rgb(135, 56, 0)', 'rgb(97, 71, 0)', 'rgb(19, 82, 0)',
                            'rgb(0, 71, 79)', 'rgb(0, 58, 140)', 'rgb(6, 17, 120)', 'rgb(34, 7, 94)', 'rgb(120, 6, 80)',

                        ],
                    },
                    bgColor: {
                        colors: [
                            'rgb(0, 0, 0)', 'rgb(38, 38, 38)', 'rgb(89, 89, 89)', 'rgb(140, 140, 140)', 'rgb(191, 191, 191)',
                            'rgb(217, 217, 217)', 'rgb(233, 233, 233)', 'rgb(245, 245, 245)', 'rgb(250, 250, 250)', 'rgb(255, 255, 255)',
                            'rgb(225, 60, 57)', 'rgb(231, 95, 51)', 'rgb(235, 144, 58)', 'rgb(245, 219, 77)', 'rgb(114, 192, 64)',
                            'rgb(89, 191, 192)', 'rgb(66, 144, 247)', 'rgb(54, 88, 226)', 'rgb(106, 57, 201)', 'rgb(216, 68, 147)',
                            'rgb(251, 233, 230)', 'rgb(252, 237, 225)', 'rgb(252, 239, 212)', 'rgb(252, 251, 207)', 'rgb(231, 246, 213)',
                            'rgb(218, 244, 240)', 'rgb(217, 237, 250)', 'rgb(224, 232, 250)', 'rgb(237, 225, 248)', 'rgb(246, 226, 234)',
                            'rgb(255, 163, 158)', 'rgb(255, 187, 150)', 'rgb(255, 213, 145)', 'rgb(255, 251, 143)', 'rgb(183, 235, 143)',
                            'rgb(135, 232, 222)', 'rgb(145, 213, 255)', 'rgb(173, 198, 255)', 'rgb(211, 173, 247)', 'rgb(255, 173, 210)',
                            'rgb(255, 77, 79)', 'rgb(255, 122, 69)', 'rgb(255, 169, 64)', 'rgb(255, 236, 61)', 'rgb(115, 209, 61)',
                            'rgb(54, 207, 201)', 'rgb(64, 169, 255)', 'rgb(89, 126, 247)', 'rgb(146, 84, 222)', 'rgb(247, 89, 171)',
                            'rgb(255, 0, 0)', 'rgb(212, 56, 13)', 'rgb(212, 107, 8)', 'rgb(212, 177, 6)', 'rgb(0, 255, 0)',
                            'rgb(8, 151, 156)', 'rgb(9, 109, 217)', 'rgb(0, 0, 255)', 'rgb(83, 29, 171)', 'rgb(196, 29, 127)',
                            'rgb(130, 0, 20)', 'rgb(135, 20, 0)', 'rgb(135, 56, 0)', 'rgb(97, 71, 0)', 'rgb(19, 82, 0)',
                            'rgb(0, 71, 79)', 'rgb(0, 58, 140)', 'rgb(6, 17, 120)', 'rgb(34, 7, 94)', 'rgb(120, 6, 80)',
                        ],
                    } */
                  fontSize:{
                    fontSizeList: Array.from({length: 100}, (_, i) => `${i + 1}px`)
                  }
                },
            },
            mode: "simple",
            fontSize: 16,
            fontColor: "#000000",
            bgColor: "",
            lineHeight: 1,
        }
    },
    beforeDestroy() {
        const editor = this.editor
        const windowEditor = window.editor
        if (editor != null || windowEditor != null) {
            editor.destroy() // 组件销毁时，及时销毁编辑器
            windowEditor.destroy() // 组件销毁时，及时销毁编辑器
        }
    },
    created() {
        this.html = this.htmlProps
        if (this.isAlignmentMode) {
            this.toolbarConfig.insertKeys = {
                index: 9,
                keys: [
                    "VerticalCenter",
                    // "BottomCenter",
                    "TopCenter",
                    "MyfontSize",
                    "MyfontColor",
                    "MybgColor",
                ]
            }
        } else {
            this.toolbarConfig.insertKeys = {
                index: 9,
                keys: [
                    "MyfontSize",
                    "MyfontColor",
                    "MybgColor",
                ]
            }
        }
    },
    methods: {
        onCreated(editor) {
            this.editor = Object.seal(editor)
            if (localStorage.getItem('language')=='us'){
              i18nChangeLanguage('en')
            }else {
              i18nChangeLanguage('zh-CN')
            }
            window.editor = Object.seal(editor) // 【注意】一定要用 Object.seal() 否则会报错
            const conf1 = myfontSize()
            // bottomCenter(), topCenter()
            const module = {
                menus: [conf1, myfontColor(), mybgColor(), verticalCenter(), topCenter()],
            }
            // console.log(editor.getAllMenuKeys())
            this.$nextTick(() => {
                if (editor) {
                    const toolsConfig = this.editor.getConfig()
                    if (toolsConfig.MENU_CONF[conf1.key] == undefined) {
                        Boot.registerModule(module)
                    }
                }
            })

        },
        onChange(editor) {
            this.$emit("onChange", editor)
            // program.newText(editor.getHtml())
            // setTimeout(() => program.newText(editor.getHtml()));
        },
        clickFontSize(flag) {
            if (flag) {
                this.editor.restoreSelection()
                this.editor.addMark('fontSize', this.fontSize + "px")
            }
            document.getElementById("fontSizeDiv").style.display = "none";
        },
        clickFontColor(flag) {
            if (flag) {
                this.editor.restoreSelection()
                this.editor.addMark('color', this.fontColor)
            }
            document.getElementById("fontColorDiv").style.display = "none";
        },
        clickBgColor(flag) {
            if (flag) {
                this.editor.restoreSelection()
                this.editor.addMark('bgColor', this.bgColor)
            }
            document.getElementById("bgColorDiv").style.display = "none";
        },
        clickAlignmentMode(type) {
            this.$emit("clickAlignmentMode", type, this.editor)
        }
    }
}
</script>

<style scoped>
.editor {
    position: relative;
    top: 0;
    left: 0;
}
.editorExtend {
    position: absolute;
    z-index: 100;
    width: auto;
    height: auto;
    background-color: rgb(255, 255, 255);
    display: none;
    align-items: center; /* 如果需要垂直居中 */
    top: 80px;
    left: 0px;
}
</style>
