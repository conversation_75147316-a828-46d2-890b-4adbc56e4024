import VueObj from '@/main'
// 插入变量
class MyButtonMenu{
    constructor() {
        this.title = VueObj.$t('myEditor.topCenter')
        this.iconSvg =  `<img src="${require('@/assets/img/top-align.png')}" style="width: 12px; height: 12px;color: #595959" />`
        this.tag = 'button'
    }
    isActive(editor) {
        return false
    }
    // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
    getValue(editor) {
        return ''
    }
    // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
    isDisabled(editor) {
        return false
    }
    // 点击菜单时触发的函数
    exec(editor, value) {
        if (this.isDisabled(editor))  return
        document.getElementById("topCenterDiv").click()
    }
}
const menu1Conf = () => {
    return {
        key: 'TopCenter', // 定义 menu key ：要保证唯一、不重复（重要）
        factory() {
            return new MyButtonMenu() // 把 `MyButtonMenu` 替换为你菜单的 class
        },
    }
 
}
export default menu1Conf
 
 
 