import VueObj from '@/main'
class MyButtonMenu {                       // JS 语法

    constructor(data) {
        this.title = VueObj.$t('program.fontSize')// 自定义菜单标题
        // this.iconSvg = '<svg>...</svg>' // 可选
        this.tag = 'button'
    }

    // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
    getValue(editor) { // JS 语法
        return editor.getSelectionText()
    }

    // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
    isActive(editor) {                    // JS 语法
        return false
    }

    // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
    isDisabled(editor) {                     // JS 语法
        return false
    }

    // 点击菜单时触发的函数
    exec(editor, value) {                              // JS 语法
        if (this.isDisabled(editor))  return
        document.getElementById("fontSizeDiv").style.display = "flex"
        // var fontSize = prompt('请输入字号大小', 16);
        
        // if (fontSize) {
        //     if (fontSize > 0 && Number.isInteger(parseInt(fontSize))) {
        //         editor.addMark('fontSize', fontSize + "px")
        //     } else {
        //         VueObj.$Message.error('请输入正确的字号');
        //     }
        // }
    }

}

const menu1Conf = (data) => {
    return {
        key: 'MyfontSize', // 定义 menu key ：要保证唯一、不重复（重要）
        factory() {
            return new MyButtonMenu(data) // 把 `MyButtonMenu` 替换为你菜单的 class
        },
    }
 
}

export default menu1Conf