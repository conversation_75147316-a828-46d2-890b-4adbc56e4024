/**
 * 邮箱
 * @param {*} s
 */
export function isEmail (s) {
  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile (s) {
  return /^[0-9]{11}$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone (s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL (s) {
  return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 6位数
 */
export function isSixCode (s) {
  return s.length === 6
}

/**
 * 判断是否是数字或者字母
 */
export function isNumOrLetter (s) {
  return /^[A-Za-z0-9]*$/.test(s) && s.length >= 5 && s.length <= 16
}

/**
 * 长度大于8
 */
export function PWDLenght (s) {
  if(s.length > 8){
    var dx =  /[A-Z]/g;
    var xx =  /[a-z]/g;
    var as =  /[0-9]/g;
    var ls = 0
    if(s.match(dx)!=null&&s.match(dx).length>=1){ ls++; }
    if(s.match(xx)!=null&&s.match(xx).length>=1){ ls++; }
    if(s.match(as)!=null&&s.match(as).length>=1){ ls++; }
    if (ls>=3)  {
        return true;
    } else {
        return false;
    }
  }else{
    return false
  }
}

/**
 * 判断是否是身份证号
 * @param s
 * @returns {boolean}
 */
export function isIdCardNumber (s) {
  //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  var reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/;
  if (reg.test(s) === false) {
    return false;
  }
  return true;
}


