import Jsrsasign from 'jsrsasign';

// JWT配置
const JWT_CONFIG = {
    SECRET_KEY: 'rtmp_xixun88833820_secret_key',
    EXPIRATION_TIME: 60 * 60 * 2, // 2小时（秒）
    ISSUER: 'rtmp-server'
};

/**
 * 生成JWT令牌（可选择是否设置过期时间）
 * @param {string} streamKey 流密钥
 * @param {boolean} hasExpiration 是否设置过期时间
 * @returns {string} JWT令牌
 */
export function generateToken(streamKey, hasExpiration) {
    // 创建JWT头部
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    };
  
    // 创建JWT载荷
    const payload = {
      streamKey: streamKey,
      iss: JWT_CONFIG.ISSUER,
      iat: Math.floor(Date.now() / 1000) // 签发时间（秒）
    };
  
    // 根据参数决定是否设置过期时间
    if (hasExpiration) {
      payload.exp = Math.floor(Date.now() / 1000) + JWT_CONFIG.EXPIRATION_TIME; // 过期时间（秒）
    }
  
    // 生成JWT令牌
    const token = Jsrsasign.jws.JWS.sign(
      header.alg,
      JSON.stringify(header),
      JSON.stringify(payload),
      JWT_CONFIG.SECRET_KEY
    );
  
    return token;
}