<template>
    <div class="player">
        <div v-if="videoData">
            <video v-if="videoData.isOn === 1" :id="'videoElement' + videoData.deviceId" autoplay muted width="100%"  height="100%">
              {{ $t('common.Loading') }}...
            </video>
            <div v-else class="videoText">
                {{videoData.alias}}
                <div class="msgVideoText">{{ $t('monitor.offLineOrNotExist') }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import flvjs from 'flv.js'
import {generateToken} from '@/utils/jwtUtils'
export default {
    props: {
        videoData: {
            type: Object,
            default: null
        },
    },
    data() {
        return {
            flvPlayer: null,
            lastDecodedFrame: 0
        }
    },
    // activated() {
    //     this.destoryVideo()
    //     this.$nextTick(() => {
    //         this.playVideo(); //视频加载
    //     });
    // },
    mounted() {
        this.$nextTick(() => {
            this.playVideo(); //视频加载
        });
    },
    methods: {
        playVideo() {
            this.lastDecodedFrame = 0
            // 加载前先销毁
            if (this.videoData) {
                let videoWin = document.getElementById("videoElement" + this.videoData.deviceId);
                var than = this;
                if (flvjs.isSupported() && than.videoData.isOn === 1) {
                    this.flvPlayer = flvjs.createPlayer({
                        type: "flv",// 媒体类型
                        isLive: true,//是否是实时流
                        hasAudio: false,//是否有音频
                        url: window.SITE_CONFIG.rtmp + than.videoData.deviceId +"&token="+ generateToken(than.videoData.deviceId),// 视频流地址
                        stashInitialSize: 128 // 减少首帧显示等待时长
                    }, {
                        enableWorker: false,// 不启动分离线程
                        enableStashBuffer: false,// 关闭IO隐藏缓冲区
                        reuseRedirectedURL: true,// 重用301、302重定向url，用于随后的请求，入查找、重新连接等。
                        autoCleanupSourceBuffer: true, // 自动清除缓存
                        fixAudioTimestampGap: false,// false 音频同步
                    });
                    // 断流重连
                    this.flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
                        console.log("errorType:", errorType);
                        console.log("errorDetail:", errorDetail);
                        console.log("errorInfo:", errorInfo);
                        if (this.flvPlayer) {
                            this.destoryVideo();
                        }
                    });
                    // 画面卡死重连
                    var than = this
                    this.flvPlayer.on("statistics_info", function (res) {
                        if (than.lastDecodedFrame == 0) {
                            than.lastDecodedFrame = res.decodedFrames;
                            return;
                        }
                        if (than.lastDecodedFrame != res.decodedFrames) {
                            than.lastDecodedFrame = res.decodedFrames;
                        } else {
                            than.lastDecodedFrame = 0;
                            if (than.flvPlayer) {
                                than.destoryVideo();
                            }
                        }
                    });
                    this.flvPlayer.attachMediaElement(videoWin);
                    this.flvPlayer.load();
                    this.flvPlayer.play();
                    // console.log(this.flvPlayer)
                }
            }
        },
        //销毁断流方法
        destoryVideo () {
            if (this.flvPlayer !== null) {
                // console.log('ssss')
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null;
            }
        },
        // 手动跳帧
        /* interval () {
            setInterval(() => {
                if (this.flvPlayer.buffered.length) {
                    let end = this.flvPlayer.buffered.end(0);//获取当前buffered值
                    let diff = end - this.flvPlayer.currentTime;//获取buffered与currentTime的差值
                    if (diff >= 0.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
                        this.flvPlayer.currentTime = this.flvPlayer.buffered.end(0);//手动跳帧
                    }
                }
            }, 2000); //2000毫秒执行一次
        } */
    },
}
</script>

<style scoped>
    .player {
        background-color: black;
        height: 100%;
        border: 1px solid white;
        color: white;
        text-align: center;
    }
    .videoText {
        color: red;
    }
    .msgVideoText {
        margin-top: 30px;
        color: #ff9900;
        font-size: 20px;
        font-weight: 500;
    }
</style>
