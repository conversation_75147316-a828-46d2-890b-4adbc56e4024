<template>
  <div class="card-box" v-if="dataList.length > 0">
    <ul>
        <li :style="{width: cardItemWidth + 'px'}" class="card-li" v-for="(item, index) in dataList" :key="index">
            <div class="res-centent" :style="item._type === 'success' ? 'height: '+ tableHeight +'px' : ''">
                <div>
                    <div class="card-top" style="background-color: #eeeeee;" v-if="item.loading">
                        <span class="card-title" v-if="item.deviceId">{{item.deviceId}}</span>
                        <span class="card-title" v-else-if="item.cardId">{{item.cardId}}</span>
                        <Icon type="md-paper-plane"/>
                    </div> 
                    <div v-else>
                        <div class="card-top" style="background-color: #f0ad4e;" 
                            v-if="item._type === undefined || item._type !== 'success'">
                        <span class="card-title" v-if="item.deviceId">{{item.deviceId}}</span>
                        <span class="card-title" v-else-if="item.cardId">{{item.cardId}}</span>
                        <Icon type="md-close"/>
                        </div>
                        <div class="card-top" style="background-color: #5cb85c;" v-else>
                        <span class="card-title" v-if="item.deviceId">{{item.deviceId}}</span>
                        <span class="card-title" v-else-if="item.cardId">{{item.cardId}}</span>
                        <Icon type="md-checkmark"/>
                        </div>
                    </div>
                </div> 
                <div v-if="!item.loading">
                    <div class="card-bottom" v-if="item._type === undefined">
                        <div style="color: red;">{{item}}</div>
                    </div>
                    <div class="card-bottom" v-else-if="item._type === 'loading'">
                        <Spin style="position: relative;" size="large" :show="true"></Spin>
                    </div>
                    <div class="card-bottom" v-else-if="item._type !== 'success'">
                        <div style="color: red;" v-if="item.msg">{{item.msg}}</div>
                        <div style="color: red;" v-else-if="item.errorMessage">{{item.errorMessage}}</div>
                    </div>
                    <div v-else>
                        <div v-if="isQuery">
                            <div class="card-bottom" v-if="resultItem && resultItem.length > 0" :style="{'height': resultHeight + 'px'}">
                                <div style="color: green;" v-for="(rItem, rIndex) in resultItem" :key="rIndex">
                                    <span v-if="item[rItem.name] || item[rItem.name] == 0">
                                        <span>
                                            <span v-if="rItem.textCond && rItem.textCond.length > 0">
                                                <span v-for="(text, iText) in rItem.textCond" :key="iText">
                                                    <span v-if="rItem.text == text.value">{{$t(text.text)}}</span>
                                                </span>
                                            </span>
                                            <span v-else>
                                                {{$t(rItem.text)}}
                                            </span>
                                            {{rItem.suffix}}
                                        </span>
                                        <span v-if="rItem.resultSet && rItem.resultSet.length > 0">
                                            <span v-for="(res, i) in rItem.resultSet" :key="i">
                                                <span v-if="res.otherCond">
                                                    <span v-if="item[rItem.name] == res.value && item[res.otherCond.name] == res.otherCond.value">
                                                        <span v-if="res.text"> {{$t(res.text)}} </span>
                                                        <span v-if="res.type && res.type == 'i18n'">
                                                            {{$t(res.name)}}
                                                        </span>
                                                        <span v-else>
                                                            {{res.name}}
                                                        </span>
                                                    </span>
                                                </span>
                                                <span v-else>
                                                    <span v-if="item[rItem.name] == res.value">
                                                        <span v-if="res.text">{{$t(res.text)}}</span>
                                                        <span v-if="res.type && res.type == 'i18n'">
                                                            {{$t(res.name)}}
                                                        </span>
                                                        <span v-else>
                                                            {{res.name}}
                                                        </span>
                                                    </span>
                                                </span>
                                            </span>
                                        </span>
                                        <span v-else-if="rItem.nameLv2 && rItem.nameLv2.length > 0">
                                            <span v-for="(res, i) in rItem.nameLv2" :key="i">
                                                <span v-if="res.text">{{$t(res.text)}}</span>
                                                <span>
                                                    {{item[rItem.name][res.name]}}
                                                </span>
                                            </span>
                                        </span>
                                        <span v-else>
                                            <span v-if="rItem.replace">
                                                {{item[rItem.name] == rItem.replace ? rItem.replaceValue : item[rItem.name]}}
                                            </span>
                                            <span v-else>
                                                {{item[rItem.name]}}
                                            </span>
                                        </span>
                                        <span v-if="rItem.unit">{{rItem.unit}}</span>
                                    </span>
                                </div>
                            </div>
                            <div v-if="isTable">
                                <div v-if="tableFieldNameLv1">
                                    <div v-if="tableFieldNameLv2">
                                        <div class="card-bottom1" v-if="tableColumns.length > 0  && item[tableFieldNameLv1]">
                                            <div v-if="tableFieldOther.length > 0" v-for="(rItem, rIndex) in tableFieldOther" :key="rIndex">
                                                <span v-if="item[tableFieldNameLv1][rItem.name] || item[tableFieldNameLv1][rItem.name] == 0">
                                                    <span>
                                                        {{$t(rItem.text)}} {{item[tableFieldNameLv1][rItem.name]}} 
                                                        <span v-if="rItem.unit">{{rItem.unit}}</span>
                                                    </span>
                                                </span>
                                            </div>
                                            <Table v-if="item[tableFieldNameLv1][tableFieldNameLv2]" :columns="tableColumns" 
                                            :data="item[tableFieldNameLv1][tableFieldNameLv2]" :height="tableHeight - 50"></Table>
                                        </div>
                                        <div class="card-bottom1" v-if="!item[tableFieldNameLv1] || item[tableFieldNameLv1] == undefined 
                                            || item[tableFieldNameLv1] == null || item[tableFieldNameLv1] == 'null'">
                                            <Table :columns="tableColumns" :data="[]" :height="tableHeight - 50" :no-data-text="$t('card.noTiming')"></Table>
                                            </div>
                                    </div>
                                    <div v-else>
                                        <div class="card-bottom1" v-if="tableColumns.length > 0  && item[tableFieldNameLv1]">
                                            <Table v-if="item[tableFieldNameLv1]  && item[tableFieldNameLv1] !== 'error'" :columns="tableColumns" 
                                            :data="item[tableFieldNameLv1]" :height="tableHeight - 50"></Table>
                                        </div>
                                        <div class="card-bottom1" v-if="!item[tableFieldNameLv1] || item[tableFieldNameLv1] === 'error'">
                                            <Table :columns="tableColumns" :data="[]" :height="tableHeight - 50" :no-data-text="$t('common.CardSysterm5263')"></Table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-bottom" v-else style="color:green;">
                            <span v-if="setText">{{$t(setText)}}</span>
                            <span v-else>{{$t('setTime.setupSuccess')}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </li>
    </ul>
  </div>
</template>
<script>
export default {
    props: {
        /**
         * 设备ID列表
         */
        ids: {
            type: Array,
            default: () => []
        },
        /**
         * 发送结果列表
         */
        resultData: {
            type: Array,
            default: () => []
        },
        /**
         * 每个结果集的宽度
         */
        cardItemWidth: {
            type: Number,
            default: 350
        },
        /**
         * 是否查询
         */
        isQuery: {
            type: Boolean,
            default: true
        },
        /**
         * isQuery == true时 结果集中是否有表格
         */
        isTable: {
            type: Boolean,
            default: false
        },
        /**
         * 表格的高度
         */
        tableHeight: {
            type: Number,
            default: 110
        },
        /**
         * 结果集高度
         */
        resultHeight: {
            type: Number,
            default: 75
        },
        /**
         * isTable == true时 结果集中表格字段名的第一位
         */
        tableFieldNameLv1: {
            type: String,
            default: null
        },
        /**
         *  isTable == true时 结果集中表格字段名的第二位
         */
        tableFieldNameLv2: {
            type: String,
            default: null
        },
        /**
         *  isTable == true时 结果集中表格字段名的其他属性名
         */
        tableFieldOther: {
            type: Array,
            default: () => []
        },
        /**
         * isTable == true时 表格的列表内容
         */
        tableColumns: {
            type: Array,
            default: () => []
        },
        /**
         * isQuery == true时 非表格状态时
         * [{text: '文本内容', name: '字段名', unit: '单位', replace: '字段等于对应值时替换为 replaceValue的值', replaceValue: '替换值', 
         * suffix: '文本内容后缀',
         * textCond:[{value: 值, text: 显示内容}] ,
         * resultSet：[{text: '文本内容', value: 结果, name: 显示内容, type: '类型：不填为 普通类型，填了为i18n',
         *  otherCond（其他条件）: {name: '字段名', value: '条件值'}}] 结果集数组显示内容},
         * nameLv2: [{name: '显示内容', text:'文本内容'}]]
         */
        resultItem: {
            type: Array,
            default: () => []
        },
        /**
         * 设置成功时的文本内容
         */
        setText: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            dataList: [],
        }
    },
    mounted() {
        this.init();
        /* console.log(this.ids)
        console.log(this.resultData) */
    },
    watch: {
        resultData: {
            handler(newVal, oldVal) {
                this.init();
            },
            deep: true
        }
    },
    methods: {
        init() {
            if (this.ids.length > 0) {
                this.dataList = this.ids.map(data => {
                    return {deviceId: data, loading: true}
                })
                if (this.resultData.length > 0) {
                    for (let i = 0; i < this.resultData.length; i++) {
                        let element = this.resultData[i];
                        this.dataList.forEach((item, index) => {
                            if (element.deviceId == item.deviceId) {
                                element.loading = false
                                this.$set(this.dataList, index, element)
                                // this.dataList.splice(index, 1, element)
                            }
                        })
                    }
                }
            }
        }
    },
}
</script>
<style scoped>
.card-box {
    margin-top: 5px;
}
.card-title {
    margin:0 5px;
    color: #ffffff;
}
.card-top {
    width: 180px;
    border: 1px #fff;
    border-radius: 10%;
    text-align: center;
}
.res-centent {
    height: 110px;
    width: 100%;
    overflow: hidden;
    margin-bottom: 5px;
}
.card-bottom {
    margin-top: 5px;
    border: dashed #000000 1px;
    background-color: #f8f8f9;
    height: 75px;
    padding: 2px;
    overflow-y: auto;
}
.card-bottom1 {
    margin-top: 5px;
    padding: 2px;
    /* height: 120px; */
    /* overflow-y: auto; */
}
.card-li {
    float: left;
    margin: 0 5px;
    width: 350px;
    height: 100%;
}
</style>