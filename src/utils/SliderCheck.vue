<template>
  <div class="slider-check-box">
    <!-- <div class="slider-check" :class="rangeStatus ? 'success' : ''">
      <i @mousedown="rangeMove" :class="rangeStatus ? successIcon : startIcon"></i>
      {{ rangeStatus ? successText : startText }}
    </div> -->
    <div @click="clickValidation" class="click-check" :class="clickStatus ? 'success' : ''">
      <div v-if="showicon">
        <i v-if="clickStatus" :class="successIcon"></i>
        <svg v-else width="25px" height="25px" aria-hidden="true" class="i" style="vertical-align: middle;top: 0">
          <use xlink:href="#clickbefor"></use>
        </svg>
      </div>
      <i v-else :class="onclickevent"></i>
      <span v-if="showicon">{{ clickStatus ? successText : startText }}</span>
      <span v-else>{{$t('login.loading')}}</span>
    </div>
  </div>
</template>
<script>
// import i18n from '@/language'
export default {
  props: {
    // 成功之后的函数
    successFun: {
      type: Function
    },
    //成功图标
    successIcon: {
      type: String,
      default: 'el-icon-success'
    },
    //成功文字
    successText: {
      type: String,
      default: ''
    },
    //开始的图标
    startIcon: {
      type: String,
      default: 'el-icon-d-arrow-right'
    },
    //开始的文字
    startText: {
      type: String,
      default: ''
    },
    //失败之后的函数
    errorFun: {
      type: Function
    },
    //或者用值来进行监听
    status: {
      type: String
    },

    clickbefor: {
      type: String,
      default: 'el-icon-video-play'
    },

    onclickevent: {
      type: String,
      default: 'el-icon-loading'
    }
  },
  data() {
    return {
      disX: 0,
      rangeStatus: false,
      clickStatus: false,
      showicon: true
    }
  },
  methods: {
    //滑块移动
    rangeMove(e) {
      let ele = e.target
      let startX = e.clientX
      let eleWidth = ele.offsetWidth
      let parentWidth = ele.parentElement.offsetWidth
      let MaxX = parentWidth - eleWidth
      if (this.rangeStatus) {
        //不运行
        return false
      }
      document.onmousemove = e => {
        let endX = e.clientX
        this.disX = endX - startX
        if (this.disX <= 0) {
          this.disX = 0
        }
        if (this.disX >= MaxX - eleWidth) {
          //减去滑块的宽度,体验效果更好
          this.disX = MaxX
        }
        ele.style.transition = '.1s all'
        ele.style.transform = 'translateX(' + this.disX + 'px)'
        e.preventDefault()
      }
      document.onmouseup = () => {
        if (this.disX !== MaxX) {
          ele.style.transition = '.5s all'
          ele.style.transform = 'translateX(0)'
          //执行成功的函数
          this.errorFun && this.errorFun()
        } else {
          this.rangeStatus = true
          if (this.status) {
            this.$parent[this.status] = true
          }
          //执行成功的函数
          this.successFun && this.successFun()
        }
        document.onmousemove = null
        document.onmouseup = null
      }
    },

    clickValidation(e) {
      this.clickStatus = true
      this.showicon = false
      if (this.status) {
        this.$parent[this.status] = true
      }
      setTimeout(() => {
        this.showicon = true
        //执行成功的函数
        this.successFun && this.successFun()
      }, 500)

    }
  }
}
</script>
<style scoped>
.slider-check {
  background-color: #ececec;
  position: relative;
  transition: 1s all;
  user-select: none;
  color: #585858;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  border: solid 1px #b8b8b8;
}

.click-check {
  background-color: #ececec;
  position: relative;
  transition: 1s all;
  user-select: none;
  color: #585858;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  border: solid 1px #b8b8b8;
  cursor: pointer;
}
.i {
  position: absolute;
  left: 10px;
  height: 100%;
}
i {
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 100%;
  color: #19be6b;
  /* background-color: #fff;
  border: 1px solid #d8d8d8; */
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.success {
  background-color: rgba(25, 190, 107, 0.2);
  border: solid 1px #07bb60;
  color: #555;
}

.success i {
  color: #19be6b;
}
</style>