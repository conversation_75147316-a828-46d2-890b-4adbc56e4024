"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _wangeditor = _interopRequireDefault(require("wangeditor"));

var _jQuery = _interopRequireDefault(require("jQuery"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }

function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }

function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }

function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }

// 获取必要的变量，这些在下文中都会用到
var DropListMenu = _wangeditor["default"].DropListMenu; // 标题菜单的 class ，可作为 DropList 菜单的参考代码

var Head =
/*#__PURE__*/
function (_DropListMenu) {
  _inherits(Head, _DropListMenu);

  function Head(editor) {
    var _this;

    _classCallCheck(this, Head);

    // 菜单栏中，标题菜单的 DOM 元素
    // 注意，这里的 $ 不是 jQuery ，是 E.$ （wangEditor 自带的 DOM 操作工具，类似于 jQuery）
    // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述
    var $elem = (0, _jQuery["default"])('<div class="w-e-menu" data-title="标题"><i class="w-e-icon-header"></i></div>'); // droplist 配置

    var dropListConf = {
      width: 100,
      title: '设置标题',
      type: 'list',
      list: [{
        $elem: (0, _jQuery["default"])('<h1>H1</h1>'),
        value: '<h1>'
      }, {
        $elem: (0, _jQuery["default"])('<h2>H2</h2>'),
        value: '<h2>'
      }, {
        $elem: (0, _jQuery["default"])('<h3>H3</h3>'),
        value: '<h3>'
      }, {
        $elem: (0, _jQuery["default"])('<h4>H4</h4>'),
        value: '<h4>'
      }, {
        $elem: (0, _jQuery["default"])('<h5>H5</h5>'),
        value: '<h5>'
      }, {
        $elem: (0, _jQuery["default"])('<p>正文</p>'),
        value: '<p>'
      }],
      // droplist 每个 item 的点击事件
      clickHandler: function clickHandler(value) {
        // value 参数即 dropListConf.list 中配置的 value
        _this.command(value);
      }
    };
    return _this = _possibleConstructorReturn(this, _getPrototypeOf(Head).call(this, $elem, editor, dropListConf));
  }

  _createClass(Head, [{
    key: "command",
    value: function command(value) {
      // 设置标题
      this.editor.cmd["do"]('formatBlock', value);
    } // 菜单是否需要激活

  }, {
    key: "tryChangeActive",
    value: function tryChangeActive() {
      var reg = /^h/i;
      var cmdValue = this.editor.cmd.queryCommandValue('formatBlock');

      if (reg.test(cmdValue)) {
        // 选区处于标题内，激活菜单
        this.active();
      } else {
        // 否则，取消激活
        this.unActive();
      }
    }
  }]);

  return Head;
}(DropListMenu);

var _default = Head;
exports["default"] = _default;