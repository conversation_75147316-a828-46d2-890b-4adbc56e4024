"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getUUID = getUUID;
exports.isAuth = isAuth;
exports.setCheckedNodes = setCheckedNodes;
exports.selectMenus = selectMenus;
exports.clearLoginInfo = clearLoginInfo;
exports.sendCardCmd = sendCardCmd;
exports.filterType = filterType;

var _vue = _interopRequireDefault(require("vue"));

var _router = _interopRequireDefault(require("@/router"));

var _store = _interopRequireDefault(require("@/store"));

var _jQuery = _interopRequireDefault(require("jQuery"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

/**
 * 获取uuid
 */
function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    return (c === 'x' ? Math.random() * 16 | 0 : 'r&0x3' | '0x8').toString(16);
  });
}
/**
 * 是否有权限
 * @param {*} key
 */


function isAuth(key) {
  return JSON.parse(sessionStorage.getItem('permissions') || '[]').indexOf(key) !== -1 || false;
}
/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */

/* export function treeDataTranslate (data, id = 'id', pid = 'parentId') {
  var res = []
  var temp = {}
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i]
    temp[data[i][id]]['title'] = data[i].name
    temp[data[i][id]]['checked'] = false
  }
  for (var j = 0; j < data.length; j++) {
    if (temp[data[j][pid]] && data[j][id] !== data[j][pid]) {
      if (!temp[data[j][pid]]['children']) {
        temp[data[j][pid]]['children'] = []
      }
      if (!temp[data[j][pid]]['_level']) {
        temp[data[j][pid]]['_level'] = 1
      }
      data[j]['_level'] = temp[data[j][pid]]._level + 1
      temp[data[j][pid]]['children'].push(data[j])
    } else {
      res.push(data[j])
    }
  }
  if (res.length > 0) {
    res.sort((a, b) => {
      return a.orderNum - b.orderNum
    })
  }
  return res
} */

/**
 * 设置树形菜单选中
 * @param {*} menuList
 * @param {*} menuIdList
 */


function setCheckedNodes(menuList, menuIdList) {
  // 判断选中的菜单id以及菜单列表是否为空
  if (menuIdList.length > 0 && menuList.length > 0) {
    for (var i = 0; i < menuList.length; i++) {
      // 目录菜单的菜单不为空
      if (menuList[i].children && menuList[i].children.length > 0) {
        var subMenu = menuList[i].children;

        for (var j = 0; j < subMenu.length; j++) {
          // 如果匹配到了对应的id说明已选当前菜单，如果菜单类型等于2或者等于1说明是菜单
          if (menuIdList.indexOf(subMenu[j].menuId) !== -1) {
            if (subMenu[j].type !== 2 && subMenu[j].type !== 1) {
              subMenu[j].checked = true;
            }
          } // 匹配对应的目录菜单下的菜单


          if (subMenu[j].children && subMenu[j].children.length > 0) {
            var subTwoMenu = subMenu[j].children;

            for (var k = 0; k < subTwoMenu.length; k++) {
              if (menuIdList.indexOf(subTwoMenu[k].menuId) !== -1) {
                subTwoMenu[k].checked = true;
              }
            }
          } else {
            if (menuIdList.indexOf(subMenu[j].menuId) !== -1) {
              subMenu[j].checked = true;
            }
          }
        }
      } else {
        if (menuIdList.indexOf(menuList[i].menuId) !== -1) {
          menuList[i].checked = true;
        }
      }
    }

    return menuList;
  }

  return menuList;
}
/**
 * 根据选择的菜单类型，遍历对应的菜单
 * @param {*} menuList
 * @param {*} type
 */


function selectMenus(menuList, type) {
  var res = [];
  var temp = {};

  if (menuList.length > 0) {
    for (var a = 0; a < menuList.length; a++) {
      temp[menuList[a]] = menuList[a];
      temp[menuList[a]]['value'] = menuList[a].menuId;
      temp[menuList[a]]['label'] = menuList[a].name;
    } // 为1，表示菜单，显示主菜单以及目录菜单


    if (type === 1) {
      for (var i = 0; i < menuList.length; i++) {
        if (menuList[i].type === 0 || menuList[i].type === 2) {
          res.push(menuList[i]);
        }
      }
    } else if (type === 2) {
      // 为2，表示目录菜单，显示主菜单
      for (var j = 0; j < menuList.length; j++) {
        if (menuList[j].type === 0) {
          res.push(menuList[j]);
        }
      }
    } else if (type === 3) {
      // 为3，表示按钮，显示所有菜单及二级菜单
      for (var k = 0; k < menuList.length; k++) {
        if (menuList[k].type === 1) {
          res.push(menuList[k]);
        }
      }
    }
  }

  if (res.length > 0) {
    res.sort(function (a, b) {
      return a.parentId - b.parentId;
    });
  }

  return res;
}
/**
 * 清除登录信息
 */


function clearLoginInfo() {
  _vue["default"].cookie["delete"]('token');

  _store["default"].commit('resetStore');

  _router["default"].options.isAddDynamicMenuRoutes = false;
}
/**
 * 发送请求给realtime
 * @param {*} cardId 卡号
 * @param {*} data 发送的数据 {'type': 'getCardInformation'}
 * @returns 返回数据
 */


function sendCardCmd(cardId, data) {
  var result = '';

  _jQuery["default"].ajax({
    url: "http://192.168.2.11:10010/cmd/".concat(cardId),
    type: 'post',
    dataType: 'json',
    contentType: 'application/json; charset=utf-8',
    data: JSON.stringify(data),
    async: false,
    success: function success(data) {
      result = data;
    }
  });

  return result;
}
/**
 * 字节转换
 */


function filterType(val) {
  if (val === 0) {
    return '0 B';
  }

  var k = 1024;
  var sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  var i = Math.floor(Math.log(val) / Math.log(k));
  return parseFloat(val / Math.pow(k, i).toPrecision(3)).toFixed(2) + ' ' + sizes[i];
}
