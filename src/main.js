// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from '@/router'
import ViewUI from 'view-design'
import 'view-design/dist/styles/iview.css'
import i18n from '@/language'
import httpRequest from '@/utils/httpRequest'
import VueCookie from 'vue-cookie'
import store from '@/store'
// import VueParticles from 'vue-particles'// 粒子背景
import { isAuth } from '@/utils'
import cloneDeep from 'lodash/cloneDeep'
import '@/assets/css/index.css'
import '@/icons' // api: http://www.iconfont.cn/
import 'viewerjs/dist/viewer.css'
// import echarts from '@/utils/echarts'
import * as echarts from 'echarts'
// import JsEncrypt from 'jsencrypt'
// 新的引入方式
import JsEncrypt from 'jsencrypt/bin/jsencrypt.min'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './iconfont/iconfont.js'
import './assets/font/font_2707944_wjceekf9nw/iconfont.css';
import dataV from '@jiaminghi/data-view'
import "@wangeditor/editor/dist/css/style.css"
import * as html2canvas from '@/utils/html2canvas.js'
import UmyUi from 'umy-ui'
import 'umy-ui/lib/theme-chalk/index.css';// 引入样式
// import locale from 'umy-ui/lib/locale/lang/en'
// import flvjs from 'flv.js'

// import "lib-flexible/flexible.js";
// import BaiduMap from 'vue-baidu-map'
// Vue.use(BaiduMap, {
//   // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
//   ak: 'RQtRYAXG5Uw6CNToTqAwYePzyds1iPD5'
// })

Vue.prototype.$echarts = echarts
Vue.config.productionTip = false
Vue.prototype.$http = httpRequest // ajax请求方法
Vue.prototype.isAuth = isAuth // 权限方法
Vue.prototype.html2canvas = html2canvas
/**
 * 配置全局的加密方法
 * @param obj 需要加密的字符串
 */
Vue.prototype.$encruption = function (obj) {
  let encrypt = new JsEncrypt();
  encrypt.setPublicKey(
    `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAsn3LoyXzv1D6LVAC/Pg5Rbc/7F0yVbQVaSR9ebVAb9O8aC1573De+SA1nnZaVO9rvfrG84oMU25WMBkcpKbCqehczLSceFFE/JI+ZzrN/51Pe7RoS8LwIdW+MDneatfXi0zbceCmVeHNyhBUpLq0zIt2gannnPfk4sg1BaRKcwIDAQAB`
  )
  return encrypt.encrypt(obj);
}

Vue.use(dataV)
Vue.use(ElementUI);
Vue.use(VueCookie)
// Vue.use(VueParticles)
Vue.use(ViewUI)
Vue.use(UmyUi);

// Vue.use(flvjs)
// 保存整站vuex本地储存初始状态
window.SITE_CONFIG['storeState'] = cloneDeep(store.state)

/* eslint-disable no-new */
let vueThis = new Vue({
  el: '#app',
  router,
  i18n,
  store,
  components: { App },
  template: '<App/>'
})
export default vueThis
