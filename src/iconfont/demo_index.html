<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">text</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea04;</span>
                <div class="name">设备类_服务器流媒体</div>
                <div class="code-name">&amp;#xea04;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">置底</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec13;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xec13;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fb;</span>
                <div class="name">向下一级</div>
                <div class="code-name">&amp;#xe7fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fc;</span>
                <div class="name">向上一级</div>
                <div class="code-name">&amp;#xe7fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9c0;</span>
                <div class="name">时间系列</div>
                <div class="code-name">&amp;#xe9c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a8;</span>
                <div class="name">环境监测</div>
                <div class="code-name">&amp;#xe6a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">天气</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8af;</span>
                <div class="name">网页链接</div>
                <div class="code-name">&amp;#xe8af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">撤销</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">恢复</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">水平左对齐</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">垂直居中对齐</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">水平居中对齐</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">垂直底对齐</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">水平右对齐</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">垂直顶对齐</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe768;</span>
                <div class="name">rss</div>
                <div class="code-name">&amp;#xe768;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">清空</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeca6;</span>
                <div class="name">窗口</div>
                <div class="code-name">&amp;#xeca6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">时间3</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe742;</span>
                <div class="name">水平放大</div>
                <div class="code-name">&amp;#xe742;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe747;</span>
                <div class="name">垂直放大</div>
                <div class="code-name">&amp;#xe747;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">删 除</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.ttf?t=1629873981867') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-text"></span>
            <div class="name">
              text
            </div>
            <div class="code-name">.icon-text
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shebeileifuwuqiliumeiti"></span>
            <div class="name">
              设备类_服务器流媒体
            </div>
            <div class="code-name">.icon-shebeileifuwuqiliumeiti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.icon-rili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-control-top"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-control-top
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-control-bottom"></span>
            <div class="name">
              置底
            </div>
            <div class="code-name">.icon-control-bottom
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjian"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.icon-wenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dashujukeshihuaico-"></span>
            <div class="name">
              向下一级
            </div>
            <div class="code-name">.icon-dashujukeshihuaico-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dashujukeshihuaico-1"></span>
            <div class="name">
              向上一级
            </div>
            <div class="code-name">.icon-dashujukeshihuaico-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Digital-AlarmClock"></span>
            <div class="name">
              时间系列
            </div>
            <div class="code-name">.icon-Digital-AlarmClock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huanjingjiance"></span>
            <div class="name">
              环境监测
            </div>
            <div class="code-name">.icon-huanjingjiance
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianqi"></span>
            <div class="name">
              天气
            </div>
            <div class="code-name">.icon-tianqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wangyelianjie"></span>
            <div class="name">
              网页链接
            </div>
            <div class="code-name">.icon-wangyelianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chexiao"></span>
            <div class="name">
              撤销
            </div>
            <div class="code-name">.icon-chexiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huifu"></span>
            <div class="name">
              恢复
            </div>
            <div class="code-name">.icon-huifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuipingzuoduiqi"></span>
            <div class="name">
              水平左对齐
            </div>
            <div class="code-name">.icon-shuipingzuoduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuizhijuzhongduiqi"></span>
            <div class="name">
              垂直居中对齐
            </div>
            <div class="code-name">.icon-chuizhijuzhongduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuipingjuzhongduiqi"></span>
            <div class="name">
              水平居中对齐
            </div>
            <div class="code-name">.icon-shuipingjuzhongduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuizhididuiqi"></span>
            <div class="name">
              垂直底对齐
            </div>
            <div class="code-name">.icon-chuizhididuiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuipingyouduiqi"></span>
            <div class="name">
              水平右对齐
            </div>
            <div class="code-name">.icon-shuipingyouduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuizhidingduiqi"></span>
            <div class="name">
              垂直顶对齐
            </div>
            <div class="code-name">.icon-chuizhidingduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rss"></span>
            <div class="name">
              rss
            </div>
            <div class="code-name">.icon-rss
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingkong"></span>
            <div class="name">
              清空
            </div>
            <div class="code-name">.icon-qingkong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuangkou"></span>
            <div class="name">
              窗口
            </div>
            <div class="code-name">.icon-chuangkou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shijian3"></span>
            <div class="name">
              时间3
            </div>
            <div class="code-name">.icon-shijian3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuipingfangda"></span>
            <div class="name">
              水平放大
            </div>
            <div class="code-name">.icon-shuipingfangda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuizhifangda"></span>
            <div class="name">
              垂直放大
            </div>
            <div class="code-name">.icon-chuizhifangda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删 除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-text"></use>
                </svg>
                <div class="name">text</div>
                <div class="code-name">#icon-text</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shebeileifuwuqiliumeiti"></use>
                </svg>
                <div class="name">设备类_服务器流媒体</div>
                <div class="code-name">#icon-shebeileifuwuqiliumeiti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#icon-rili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-control-top"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-control-top</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-control-bottom"></use>
                </svg>
                <div class="name">置底</div>
                <div class="code-name">#icon-control-bottom</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjian"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#icon-wenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dashujukeshihuaico-"></use>
                </svg>
                <div class="name">向下一级</div>
                <div class="code-name">#icon-dashujukeshihuaico-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dashujukeshihuaico-1"></use>
                </svg>
                <div class="name">向上一级</div>
                <div class="code-name">#icon-dashujukeshihuaico-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Digital-AlarmClock"></use>
                </svg>
                <div class="name">时间系列</div>
                <div class="code-name">#icon-Digital-AlarmClock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huanjingjiance"></use>
                </svg>
                <div class="name">环境监测</div>
                <div class="code-name">#icon-huanjingjiance</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianqi"></use>
                </svg>
                <div class="name">天气</div>
                <div class="code-name">#icon-tianqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangyelianjie"></use>
                </svg>
                <div class="name">网页链接</div>
                <div class="code-name">#icon-wangyelianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chexiao"></use>
                </svg>
                <div class="name">撤销</div>
                <div class="code-name">#icon-chexiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huifu"></use>
                </svg>
                <div class="name">恢复</div>
                <div class="code-name">#icon-huifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuipingzuoduiqi"></use>
                </svg>
                <div class="name">水平左对齐</div>
                <div class="code-name">#icon-shuipingzuoduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuizhijuzhongduiqi"></use>
                </svg>
                <div class="name">垂直居中对齐</div>
                <div class="code-name">#icon-chuizhijuzhongduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuipingjuzhongduiqi"></use>
                </svg>
                <div class="name">水平居中对齐</div>
                <div class="code-name">#icon-shuipingjuzhongduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuizhididuiqi"></use>
                </svg>
                <div class="name">垂直底对齐</div>
                <div class="code-name">#icon-chuizhididuiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuipingyouduiqi"></use>
                </svg>
                <div class="name">水平右对齐</div>
                <div class="code-name">#icon-shuipingyouduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuizhidingduiqi"></use>
                </svg>
                <div class="name">垂直顶对齐</div>
                <div class="code-name">#icon-chuizhidingduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rss"></use>
                </svg>
                <div class="name">rss</div>
                <div class="code-name">#icon-rss</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingkong"></use>
                </svg>
                <div class="name">清空</div>
                <div class="code-name">#icon-qingkong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuangkou"></use>
                </svg>
                <div class="name">窗口</div>
                <div class="code-name">#icon-chuangkou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijian3"></use>
                </svg>
                <div class="name">时间3</div>
                <div class="code-name">#icon-shijian3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuipingfangda"></use>
                </svg>
                <div class="name">水平放大</div>
                <div class="code-name">#icon-shuipingfangda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuizhifangda"></use>
                </svg>
                <div class="name">垂直放大</div>
                <div class="code-name">#icon-chuizhifangda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删 除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
