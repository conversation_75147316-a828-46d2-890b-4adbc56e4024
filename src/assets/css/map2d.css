@charset "UTF-8";
main > * {
  display: flex;
  overflow: hidden; }
main h3 .more {
  position: absolute;
  right: 2rem;
  font-style: italic;
  font-size: 1.25rem; }
main .l {
  flex: 0 0 66.66%;
  padding: 1rem; }
  main .l .chart-wrap {
    position: relative; }
    main .l .chart-wrap .radioGroup {
      position: absolute;
      right: 2rem;
      top: 4rem;
      z-index: 1; }
      main .l .chart-wrap .radioGroup input {
        vertical-align: middle; }
    main .l .chart-wrap .chart-box {
      flex: 1;
      position: absolute;
      width: 100%;
      bottom: 0; }
      main .l .chart-wrap .chart-box .chart {
        height: 53.125rem; }
/* main .chart-wrap {
  border: 0.0625rem solid rgba(76, 180, 231, 0.33);
  display: flex;
  flex: 1;
  flex-direction: column; } */
  main .chart-wrap .chart {
    flex: 1;
    height: 0; }
main .r {
  flex: 0 0 33.33%;
  flex-direction: column; }
  main .r > div {
    display: flex;
    flex: 0 0 50%;
    padding: 1rem; }
    main .r > div .chart-wrap {
      position: relative; }
  main .r .r-t .chart-wrap .chart:before {
    content: '北↑';
    position: absolute;
    left: 9.75rem;
    top: 1rem; }
  main .r .r-t .chart-wrap .chart.notebook:before {
    left: 7.75rem;
    top: .5rem; }
  main .r .r-t .chart-wrap .input-group {
    margin-left: 1rem; }
  main .r .r-t .chart-wrap .text {
    position: absolute;
    width: 38%;
    right: 1rem;
    top: 9rem; }
    main .r .r-t .chart-wrap .text.notebook {
      width: 13rem;
      top: 7rem; }
  main .r .r-b #c04_bestLocation {
    flex: 0;
    display: flex; }
    main .r .r-b #c04_bestLocation .blocks {
      display: flex;
      position: relative;
      flex-wrap: wrap;
      width: 19rem;
      top: 2rem;
      left: 2rem; }
      main .r .r-b #c04_bestLocation .blocks > div {
        width: 5rem;
        height: 5rem;
        text-align: center;
        line-height: 4rem;
        font-size: 2rem;
        background-color: rgba(255, 255, 255, 0.13);
        border: 0.125rem solid rgba(255, 255, 255, 0.63);
        transition: all .5s; }
        main .r .r-b #c04_bestLocation .blocks > div.active {
          background: deepskyblue; }
    main .r .r-b #c04_bestLocation form {
      margin-top: 2rem;
      width: 17rem; }
      main .r .r-b #c04_bestLocation form .input-group {
        margin-bottom: .5rem; }
        main .r .r-b #c04_bestLocation form .input-group button {
          margin-left: 0; }
        main .r .r-b #c04_bestLocation form .input-group #floorNumber {
          width: 4rem; }

/*# sourceMappingURL=map2d.css.map */
