:root {
    --background-color: #2c3e50;
    --border-color: #7591AD;
    --text-color: #34495e;
    --color1: #EC3E27;
    --color2: #fd79a8;
    --color3: #0984e3;
    --color4: #00b894;
    --color5: #fdcb6e;
    --color6: #e056fd;
    --color7: #F97F51;
    --color8: #BDC581;
}
* { touch-action: pan-y; }
/* * {
    margin: 0;
    padding: 0;
}

html {
    font-size: 14px;
}

body {
    width: 100vw;
    height: 100vh;
    background-color: var(--background-color);
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Montserrat', sans-serif, Arial, 'Microsoft Yahei';
} */

/* .channel {
    position: absolute;
    width: 80%;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -200px);
    font-size: 30px;
    font-weight: bold;
    color: #fff;
} */

/* .container {
    position: relative;
    width: 20vh;
    height: 20vh;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: animate_container 2s linear 1 forwards;
} */

@keyframes animate_container {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.loader {
  
    position: absolute;
    width: 7vh;
    height: 7vh;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: animate_loader 5s linear infinite;
}
.loader2 {
    position: absolute;
    width: 7vh;
    height: 7vh;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: animate_loader2 2.5s linear infinite;
}

@keyframes animate_loader {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
@keyframes animate_loader2 {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(-360deg);
    }
}

.petal {
    position: absolute;
    width: 30%;
    height: 15%;
    /* background-color: var(--color3); */
    border: .1vh solid #2a7fff;
    box-shadow: 0 0 1vh #2a7fff;
    border-radius: 0 1vh 0 1vh;
    transform: rotate(calc(var(--i) * 45deg)) translate(2vh,2vh);
    animation: animate_petal 1.5s linear infinite;
    animation-delay: calc(var(--i) * 200ms);
}


@keyframes animate_tensile {
    0% {
        transform: translateY(3vh);
    }
    33% {
        transform: translateY(4vh);
    }
    100% {
        transform: translateY(5vh);
    }
}


@keyframes animate_petal {
    0% {
        border: .1vh solid #2a7fff;
        box-shadow: 0 0 1vh #2a7fff;
    }
    33% {
        border: .1vh solid #fff38b;
        box-shadow: 0 0 1vh #fff38b;
    }
    100% {
        border: .1vh solid #2a7fff;
        box-shadow: 0 0 1vh #2a7fff;
    }
}

/* .progress {
    position: absolute;
    width: 30vh;
    height: 1vh;
    background-color: #F97F51;
    bottom: 0;
    border-radius: .5vh;
    overflow: hidden;
} */

.progress::after {
    position: absolute;
    content: '';
    width: inherit;
    height: inherit;
    border-radius: inherit;
    background-color: #fff;
    opacity: 0.6;
    left: -100%;
    animation: animate_progress 8s ease-in-out 1 1.5s forwards;
}

@keyframes animate_progress {
    0% {
        left: -100%;
    }
    100% {
        left: 0%;
    }
}