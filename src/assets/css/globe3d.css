@charset "UTF-8";
main > * {
  display: flex;
  overflow: hidden; }
main h3 .more {
  position: absolute;
  right: 2rem;
  font-style: italic;
  font-size: 1.25rem; }
main .l {
  flex: 0 0 66.66%;
  padding: 1rem; }
  main .l .chart-wrap {
    position: relative; }
    main .l .chart-wrap .radioGroup {
      position: absolute;
      right: 2rem;
      top: 4rem;
      z-index: 1; }
      main .l .chart-wrap .radioGroup input {
        vertical-align: middle; }
    main .l .chart-wrap .chart-box {
      flex: 1;
      position: absolute;
      width: 100%;
      bottom: 0; }
      main .l .chart-wrap .chart-box .chart {
        height: 53.125rem; }
main .r {
  flex: 0 0 33.33%;
  flex-direction: column; }
  main .r > div {
    display: flex;
    flex: 0 0 50%;
    padding: 1rem; }
    main .r > div .chart-wrap {
      position: relative; }
      main .r > div .chart-wrap .blessings-input-wrap {
        position: absolute;
        right: 3rem;
        padding-top: 7rem; }
        main .r > div .chart-wrap .blessings-input-wrap #blessings_input {
          position: relative;
          transform: rotate(-90deg);
          transform-origin: right top;
          top: 1.5rem;
          width: 16rem; }
          main .r > div .chart-wrap .blessings-input-wrap #blessings_input.notebook {
            top: 1rem;
            width: 10rem; }
        main .r > div .chart-wrap .blessings-input-wrap label {
          position: absolute;
          right: -2rem; }
        main .r > div .chart-wrap .blessings-input-wrap .label1, main .r > div .chart-wrap .blessings-input-wrap .label2 {
          position: absolute;
          right: 0; }
        main .r > div .chart-wrap .blessings-input-wrap .label1 {
          top: 8.5rem; }
        main .r > div .chart-wrap .blessings-input-wrap .label2 {
          top: 23rem; }
          main .r > div .chart-wrap .blessings-input-wrap .label2.notebook {
            top: 16.5rem; }
      main .r > div .chart-wrap .startTime-input-wrap {
        position: relative;
        height: 3rem;
        z-index: 1; }
        main .r > div .chart-wrap .startTime-input-wrap label {
          position: absolute;
          top: 0;
          right: 4rem; }
        main .r > div .chart-wrap .startTime-input-wrap #startTime_input {
          position: absolute;
          top: 1rem;
          left: 3rem;
          width: 29rem; }
          main .r > div .chart-wrap .startTime-input-wrap #startTime_input.notebook {
            width: 22rem; }
main .r-b .text {
  position: absolute;
  top: 4rem;
  left: 2rem;
  width: 20rem; }
  main .r-b .text.notebook {
    width: 13.5rem; }
  main .r-b .text strong {
    font-size: 2rem; }
main .r-b .input-group {
  position: absolute;
  right: 2rem;
  bottom: .5rem; }
  main .r-b .input-group label {
    display: block; }
    main .r-b .input-group label.notebook {
      height: 2.5rem; }
    main .r-b .input-group label output {
      display: inline-block;
      width: 1rem; }
    main .r-b .input-group label input {
      position: relative;
      margin: .75rem 0;
      vertical-align: middle;
      width: 7rem;
      height: 2rem; }
      main .r-b .input-group label input:before, main .r-b .input-group label input:after {
        font-size: .9125rem;
        position: absolute;
        top: -1rem; }
      main .r-b .input-group label input:before {
        left: -1rem; }
      main .r-b .input-group label input:after {
        right: -1rem; }
    main .r-b .input-group label:nth-of-type(1) input:before {
      content: '消极沮丧'; }
    main .r-b .input-group label:nth-of-type(1) input:after {
      content: '积极乐观'; }
    main .r-b .input-group label:nth-of-type(2) input:before {
      content: '急躁易怒'; }
    main .r-b .input-group label:nth-of-type(2) input:after {
      content: '谦和淡定'; }
    main .r-b .input-group label:nth-of-type(3) input:before {
      content: '懒散不动'; }
    main .r-b .input-group label:nth-of-type(3) input:after {
      content: '坚持锻炼'; }
    main .r-b .input-group label:nth-of-type(4) input:before {
      content: '熬夜嗜睡'; }
    main .r-b .input-group label:nth-of-type(4) input:after {
      content: '早起早睡'; }
    main .r-b .input-group label:nth-of-type(5) input:before {
      content: '暴食重口'; }
    main .r-b .input-group label:nth-of-type(5) input:after {
      content: '规律清淡'; }
    main .r-b .input-group label:nth-of-type(6) input:before {
      content: '颓废庸俗'; }
    main .r-b .input-group label:nth-of-type(6) input:after {
      content: '高雅艺术'; }
main .chart-wrap {
  border: 0.0625rem solid rgba(76, 180, 231, 0.33);
  display: flex;
  flex: 1;
  flex-direction: column;
  background: linear-gradient(#00faff, #00faff) left top,
		linear-gradient(#00faff, #00faff) left top,
		linear-gradient(#00faff, #00faff) right top,
		linear-gradient(#00faff, #00faff) right top,
		linear-gradient(#00faff, #00faff) left bottom,
		linear-gradient(#00faff, #00faff) left bottom,
		linear-gradient(#00faff, #00faff) right bottom,
		linear-gradient(#00faff, #00faff) right bottom;
    background-repeat: no-repeat;
  background-size: 3px 20px, 20px 3px;
}
  main .chart-wrap .chart {
    flex: 1;
    height: 0; }

/*# sourceMappingURL=globe3d.css.map */
