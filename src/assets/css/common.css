@charset "UTF-8";
@media all {
  html, body, ul, li, ol, dl, dd, dt, p, h1, h2, h3, h4, h5, h6, form, fieldset, legend, img {
    margin: 0;
    padding: 0;
    font-family: sans-serif;
  }

  ul, ol, li {
    list-style: none;
  }

  a, u {
    text-decoration: none;
  }

  img {
    display: block;
    border: 0;
  }

  * {
    box-sizing: border-box;
  }

  :root {
    --c_mainBlue: #7ed2fc;
    --c_highlightBlue: #7ed2fc;
    --c_mainWhite: #fff;
    --c_mainGray: #666;
    --c_bg: rgb(242, 246, 248);
    --c_bg_chart: rgba(0, 138, 255, 0.1);
  }

  html, body {
    height: 100%;
    color: var(--c_mainWhite);
    background-color: var(--c_bg);
  }

  h3, h4, h5 {
    font-weight: normal;
  }

  button {
    outline: none;
  }

  input {
    height: 1.75rem;
    vertical-align: middle;
  }

  button {
    margin-left: 1rem;
    padding: .0625rem 1rem;
    font-size: 1rem;
    color: #fff;
    background: rgb(102, 187, 216);
  }

  @keyframes linkTo {
    from, 80%, 90%, to {
      left: 0;
    }
    85%, 95% {
      left: .5rem;
    }
  }
  #container {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
  }
  #container nav {
    text-align: right;
    padding-right: 2rem;
  }
  #container nav a {
    padding-left: 1rem;
    color: lightskyblue;
    font-style: italic;
  }
  #container nav a:hover {
    text-decoration: underline;
  }
  #container nav a:before {
    content: '→ ';
    position: relative;
    left: 0;
    animation: linkTo 5s ease-out;
    animation-iteration-count: infinite;
  }
  }
.myBg {
  background-size: 100% 100%;
  background-origin: content-box;
  background-repeat: no-repeat; }

strong {
  color: var(--c_mainBlue); }

h3 {
  margin-bottom: 1rem;
  padding: .5rem 1rem;
  font-size: 2.3vh;
  letter-spacing: .0625rem;
  color: var(--c_mainWhite);
  background: linear-gradient(to right,rgb(121, 216, 248), #fff);
  z-index: 1;
}
  h3.en {
    margin-top: .3rem;
    color: #999;
    font-size: .75rem; }

h4 {
  font-size: 1.25rem;
  letter-spacing: .125rem; }

h5 {
  letter-spacing: .125rem; }

#container {
  height: 100%; }

@media all {
  i {
    display: inline-block;
    background-size: contain;
    background-repeat: no-repeat; }

  .icon {
    margin-right: .5rem;
    width: 1.875rem;
    height: 1.875rem;
    vertical-align: -0.5em;
    fill: currentColor;
    overflow: hidden; } }
/*风筝动画*/
@keyframes logoAnimation {
  from {
    transform: skew(0) scale(1); }
  25% {
    transform: skewX(1deg) rotateX(30deg) scale(0.9) translate(-0.125rem, -0.25rem); }
  40% {
    transform: scale(0.85);
    opacity: 0.7; }
  60% {
    transform: skewX(-15deg) skewY(-10deg); }
  70% {
    transform: skewX(-15deg) skewY(-10deg) rotateZ(10deg); }
  90% {
    transform: skewY(5deg); }
  to {
    transform: skew(0) scale(1); } }
#container > header {
  position: relative;
  padding: 1.5rem 2rem 1rem;
  height: 6.25rem;
  /*&:before{
    #container > header:before {
      display: block;
      position: absolute;
      content: '';
      left: 13rem;
      height: 7rem;
      width: 14rem;
      background: url(../img/gestures.jpg);
      background-position: 2rem -12.5rem;
      background-size: 200%;
      !* background-origin: border-box; *!
    }
  }*/
  /*js操作伪元素方法 https://www.jianshu.com/p/37f639f108dd*/ }
  @media screen and (max-width: 760px) {
    #container > header.showGesture:after {
      content: "　← 左右滑动这里试试 →　　　点击可关闭";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      line-height: 2.5;
      font-size: 2.5rem;
      background: rgba(0, 0, 0, 0.8);
      border: .25rem solid #fff;
      border-radius: .5rem;
      box-shadow: 0 0 1.5rem inset; } }
  #container > header .logo {
    position: absolute;
    animation: 20s logoAnimation infinite ease; }
    #container > header .logo img {
      width: 8rem; }
  #container > header h1 {
    text-align: center;
    margin: 0 4.5rem .5rem;
    font-size: 2.5rem;
    font-weight: normal;
    color: var(--c_mainWhite); }
  #container > header #headerTime {
    position: absolute;
    right: 1rem;
    top: 2.5rem;
    font-size: 1.125rem;
    letter-spacing: .0625rem;
    margin-right: 1rem; }
    #container > header #headerTime > span {
      display: block; }
    #container > header #headerTime .digital-num {
      font-family: "digitalNumber", serif;
      font-size: 1.5rem;
      margin: 0 .125rem; }
      #container > header #headerTime .digital-num:last-child {
        margin-left: 1rem;
        margin-right: 0; }
      #container > header #headerTime .digital-num .colon {
        display: inline-block;
        font-family: sans-serif;
        width: 0.125rem; }
  #container > header .weatherBox {
    position: absolute;
    right: 11rem;
    top: 2.5rem; }
    #container > header .weatherBox #weatherIcon {
      vertical-align: top;
      width: 3rem;
      height: 3rem;
      filter: brightness(3);
      background-size: contain;
      /*background-image: url("https://cdn.heweather.com/cond_icon/101.png");*/ }
    #container > header .weatherBox ul {
      font-size: 1.125rem;
      display: inline-block; }
      #container > header .weatherBox ul .temperature {
        margin-bottom: .25rem; }
#container > main {
  flex: 1;
  display: flex;
  padding: 0 1rem 2rem; }

body > aside {
  z-index: 1;
  position: absolute;
  bottom: 2rem;
  border: .5rem solid transparent;
  background: midnightblue;
  width: 26rem;
  border-radius: 0 1rem 1rem 0;
  box-shadow: 0 0 0.5rem 0.5rem deepskyblue;
  transform: translateX(-27rem);
  transition: all .3s ease; }
  body > aside:after {
    content: '';
    display: block;
    position: absolute;
    width: 1rem;
    height: 100%;
    top: 0;
    right: 0;
    transform: translateX(2rem); }
  body > aside:hover {
    transform: initial; }
  body > aside .input-group ~ .input-group {
    border-top: 0.0625rem solid rgba(135, 206, 235, 0.63); }
  body > aside .input-group {
    padding: 1rem .5rem; }
    body > aside .input-group:last-child {
      text-align: center; }
    body > aside .input-group h4 {
      margin-bottom: .5rem; }
    body > aside .input-group label {
      margin: 0 .5rem 0 1rem;
      white-space: nowrap; }
    body > aside .input-group.colors label {
      margin-left: 0; }
    body > aside .input-group.other label {
      line-height: 2.5rem; }
    body > aside .input-group input {
      padding: .125rem .25rem;
      width: 6rem;
      font-size: 1.125rem;
      color: #fff;
      border: 0.0625rem solid rgba(255, 255, 255, 0.5);
      background: transparent; }
      body > aside .input-group input[type=radio] {
        width: auto;
        height: 1rem;
        vertical-align: top; }
      body > aside .input-group input[type=checkbox] {
        margin-left: 0;
        width: auto;
        vertical-align: middle; }
      body > aside .input-group input button {
        margin-top: .5rem;
        padding: .25rem 1rem;
        font-size: 1.25rem;
        color: #fff;
        background: var(--c_mainBlue);
        border: none;
        border-radius: .25rem;
        cursor: pointer; }
        body > aside .input-group input button:hover {
          background: #4EA3D6; }
        body > aside .input-group input button:active {
          background: #4e93c6; }

/*# sourceMappingURL=common.css.map */

main .l, main .m, main .r {
    flex: 0 0 33.33%;
    display: flex;
    flex-direction: column;
    overflow: hidden; }
  main .l > div, main .m > div, main .r > div {
    display: flex;
    flex: 0 0 50%;
    padding: 3vh 1vw 0 1vw; }
  /* main .chart-wrap {
    border: 0.0625rem solid rgba(76, 180, 231, 0.33);
    display: flex;
    flex: 1;
    flex-direction: column; } */
    main .chart-wrap .chart {
      flex: 1;
      height: 0; }

  /*# sourceMappingURL=index.css.map */
