.ivu-scroll-container {
    overflow-y: auto;
}
.ivu-modal-footer {
    border-top: 0;

}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu){
    color: #5e81a2;
    background: rgba(94,130,162,0.1);
}
.ivu-modal-body {
    overflow: auto;
}
.ivu-steps .ivu-steps-title {
    display: inline-block;
    margin-bottom: 4px;
    padding-right: 10px;
    font-size: 14px;
    font-weight: 700;
    color: #666;
    background: rgba(255, 255, 255, 0);
}
.ivu-steps .ivu-steps-head {
    background: rgba(255, 255, 255, 0);
}
/* .ivu-table td {
    height: 40px;
} */
ul li {
    list-style: none;
}
img{ pointer-events: none; }
/* 滚动条样式 */
::-webkit-scrollbar {
    width: 19px;
    height: 19px;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
    border-radius: 999px;
    border: 5px solid transparent;
}

::-webkit-scrollbar-track {
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2) inset;
}

::-webkit-scrollbar-thumb {
    min-height: 20px;
    background-clip: content-box;
    box-shadow: 0 0 0 5px rgba(0, 0, 0, 0.2) inset;
}

::-webkit-scrollbar-corner {
    background: transparent;
}
svg:not(:root) {
    overflow: hidden;
}
.ivu-divider-horizontal {
    margin: 2px 0;
}
