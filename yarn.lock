# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@~4.3.3":
  version "4.3.3"
  resolved "https://registry.npmjs.org/@adobe/css-tools/-/css-tools-4.3.3.tgz"
  integrity sha512-rE0Pygv0sEZ4vBWHlAgJLGDU7Pm8xoO6p3wsEceb7GYAjScrOHpEo8KK/eVkAcnSM+slAEtXjA2JpdjLp4fJQQ==

"@babel/code-frame@^7.0.0-beta.35", "@babel/code-frame@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560663383&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.0.0-beta.44.tgz"
  integrity sha1-KgJkM2jegJFhYr5whlyXd08629k=
  dependencies:
    "@babel/highlight" "7.0.0-beta.44"

"@babel/generator@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/generator/download/@babel/generator-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560663614&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.0.0-beta.44.tgz"
  integrity sha1-x+Z7m1KEr89pswm1DX038+UDPUI=
  dependencies:
    "@babel/types" "7.0.0-beta.44"
    jsesc "^2.5.1"
    lodash "^4.2.0"
    source-map "^0.5.0"
    trim-right "^1.0.1"

"@babel/helper-function-name@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560944177&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.0.0-beta.44.tgz"
  integrity sha1-4YVSqq4iMRAKbkheA4VLw1MtRN0=
  dependencies:
    "@babel/helper-get-function-arity" "7.0.0-beta.44"
    "@babel/template" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-get-function-arity@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560945700&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-get-function-arity%2Fdownload%2F%40babel%2Fhelper-get-function-arity-7.0.0-beta.44.tgz"
  integrity sha1-0Dym3SufewseazLFbHKDYUDbOhU=
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-module-imports@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.16.0.tgz?cache=0&sync_timestamp=1635560941965&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-module-imports%2Fdownload%2F%40babel%2Fhelper-module-imports-7.16.0.tgz"
  integrity sha1-kFOOYLZy7PG0SPX09UM9N+eaPsM=
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560943488&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-split-export-declaration%2Fdownload%2F%40babel%2Fhelper-split-export-declaration-7.0.0-beta.44.tgz"
  integrity sha1-wLNRc14PvLOCLIrY205YOwXr2dw=
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-validator-identifier@^7.15.7":
  version "7.15.7"
  resolved "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.15.7.tgz?cache=0&sync_timestamp=1631920110587&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-identifier%2Fdownload%2F%40babel%2Fhelper-validator-identifier-7.15.7.tgz"
  integrity sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=

"@babel/highlight@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560940881&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhighlight%2Fdownload%2F%40babel%2Fhighlight-7.0.0-beta.44.tgz"
  integrity sha1-GMlM5UORaoBVPtzc9oGJCyAHR9U=
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

"@babel/runtime-corejs3@^7.11.2":
  version "7.21.0"
  resolved "https://registry.npmmirror.com/@babel/runtime-corejs3/-/runtime-corejs3-7.21.0.tgz"
  integrity sha512-TDD4UJzos3JJtM+tHX+w2Uc+KWj7GV+VKKFdMVd2Rx8sdA19hcc3P3AHFYd5LVOw+pYuSd5lICC3gm52B6Rwxw==
  dependencies:
    core-js-pure "^3.25.1"
    regenerator-runtime "^0.13.11"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.15.3", "@babel/runtime@^7.15.4", "@babel/runtime@^7.5.5":
  version "7.16.3"
  resolved "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.16.3.tgz?cache=0&sync_timestamp=1636494819594&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.16.3.tgz"
  integrity sha512-WBwekcqacdY2e9AF/Q7WLFUWmdJGJTkbjqTjoMDgXkVZ3ZRUvOPsLb5KdwISoQVsbP+DQzVZW4Zhci0DvpbNTQ==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.0.0-beta.44.tgz"
  integrity sha1-+IMvT9zuXVm/UV5ZX8UQbFKbOU8=
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    lodash "^4.2.0"

"@babel/traverse@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1636494921751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.0.0-beta.44.tgz"
  integrity sha1-qXCixFR3rRgBfi5GWgYG/u4NKWY=
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/generator" "7.0.0-beta.44"
    "@babel/helper-function-name" "7.0.0-beta.44"
    "@babel/helper-split-export-declaration" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    debug "^3.1.0"
    globals "^11.1.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

"@babel/types@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.16.0.tgz?cache=0&sync_timestamp=1635560908248&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.16.0.tgz"
  integrity sha1-2zsxOAT5aq3Qt3bEgj4SetZyibo=
  dependencies:
    "@babel/helper-validator-identifier" "^7.15.7"
    to-fast-properties "^2.0.0"

"@babel/types@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1635560908248&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.0.0-beta.44.tgz"
  integrity sha1-axsWRZH3fewKA0KsqZXy0Eazp1c=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^2.0.0"

"@dongido/vue-viaudio@^0.3.5":
  version "0.3.5"
  resolved "https://registry.npmmirror.com/@dongido/vue-viaudio/download/@dongido/vue-viaudio-0.3.5.tgz"
  integrity sha1-BBs9DYICjFU4HiffM8vgteyV608=
  dependencies:
    core-js "^2.6.5"
    vue "^2.6.10"

"@googlemaps/js-api-loader@^1.15.1":
  version "1.15.1"
  resolved "https://registry.npmmirror.com/@googlemaps/js-api-loader/-/js-api-loader-1.15.1.tgz"
  integrity sha512-AsnEgNsB7S/VdrHGEQUaUM2e5tmjFGKBAfzR/AqO8O7TPq/jQGvoRw5liPBw4EMF38RDsHmKDV89q/X+qiUREQ==
  dependencies:
    fast-deep-equal "^3.1.3"

"@jiaminghi/bezier-curve@*":
  version "0.0.9"
  resolved "https://registry.npmmirror.com/@jiaminghi/bezier-curve/-/bezier-curve-0.0.9.tgz"
  integrity sha512-u9xJPOEl6Dri2E9FfmJoGxYQY7vYJkURNX04Vj64tdi535tPrpkuf9Sm0lNr3QTKdHQh0DdNRsaa62FLQNQEEw==
  dependencies:
    "@babel/runtime" "^7.5.5"

"@jiaminghi/c-render@^0.4.3":
  version "0.4.3"
  resolved "https://registry.npmmirror.com/@jiaminghi/c-render/-/c-render-0.4.3.tgz"
  integrity sha512-FJfzj5hGj7MLqqqI2D7vEzHKbQ1Ynnn7PJKgzsjXaZpJzTqs2Yw5OSeZnm6l7Qj7jyPAP53lFvEQNH4o4j6s+Q==
  dependencies:
    "@babel/runtime" "^7.5.5"
    "@jiaminghi/bezier-curve" "*"
    "@jiaminghi/color" "*"
    "@jiaminghi/transition" "*"

"@jiaminghi/charts@*":
  version "0.2.18"
  resolved "https://registry.npmmirror.com/@jiaminghi/charts/-/charts-0.2.18.tgz"
  integrity sha512-K+HXaOOeWG9OOY1VG6M4mBreeeIAPhb9X+khG651AbnwEwL6G2UtcAQ8GWCq6GzhczcLwwhIhuaHqRygwHC0sA==
  dependencies:
    "@babel/runtime" "^7.5.5"
    "@jiaminghi/c-render" "^0.4.3"

"@jiaminghi/color@*":
  version "0.1.1"
  resolved "https://registry.npmmirror.com/@jiaminghi/color/-/color-0.1.1.tgz"
  integrity sha512-M09+Sb5HGqVim0zo+nG5gU1v+6gXT8ptr0BZR6dMGt83XmCJgnZtO8s7llTW4hLFFFM5co6geZvTekqLpSPAAQ==
  dependencies:
    "@babel/runtime" "^7.5.5"

"@jiaminghi/data-view@^2.10.0":
  version "2.10.0"
  resolved "https://registry.npmmirror.com/@jiaminghi/data-view/-/data-view-2.10.0.tgz"
  integrity sha512-Cud2MTiMcqc5k2KWabR/svuVQmXHANqURo+yj40370/LdI/gyUJ6LG203hWXEnT1nMCeiv/SLVmxv3PXLScCeA==
  dependencies:
    "@babel/runtime" "^7.5.5"
    "@jiaminghi/charts" "*"

"@jiaminghi/transition@*":
  version "1.1.11"
  resolved "https://registry.npmmirror.com/@jiaminghi/transition/-/transition-1.1.11.tgz"
  integrity sha512-owBggipoHMikDHHDW5Gc7RZYlVuvxHADiU4bxfjBVkHDAmmck+fCkm46n2JzC3j33hWvP9nSCAeh37t6stgWeg==
  dependencies:
    "@babel/runtime" "^7.5.5"

"@transloadit/prettier-bytes@0.0.7":
  version "0.0.7"
  resolved "https://registry.npmjs.org/@transloadit/prettier-bytes/-/prettier-bytes-0.0.7.tgz"
  integrity sha512-VeJbUb0wEKbcwaSlj5n+LscBl9IPgLPkHVGBkh00cztv6X4L/TJXK58LzFuBKX7/GAfiGhIwH67YTLTlzvIzBA==

"@types/event-emitter@^0.3.3":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@types/event-emitter/-/event-emitter-0.3.3.tgz"
  integrity sha512-UfnOK1pIxO7P+EgPRZXD9jMpimd8QEFcEZ5R67R1UhGbv4zghU5+NE7U8M8G9H5Jc8FI51rqDWQs6FtUfq2e/Q==

"@types/googlemaps@^3.39.1":
  version "3.43.3"
  resolved "https://registry.npmmirror.com/@types/googlemaps/-/googlemaps-3.43.3.tgz"
  integrity sha512-ZWNoz/O8MPEpiajvj7QiqCY8tTLFNqNZ/a+s+zTV58wFVNAvvqV4bdGfnsjTb5Cs4V6wEsLrX8XRhmnyYJ2Tdg==

"@types/json-schema@^7.0.8":
  version "7.0.15"
  resolved "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.nlark.com/@types/json5/download/@types/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/q@^1.5.1":
  version "1.5.5"
  resolved "https://registry.npmmirror.com/@types/q/download/@types/q-1.5.5.tgz"
  integrity sha1-daKo59irSyMEFFBdkjNdHctTpt8=

"@types/strip-bom@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/@types/strip-bom/download/@types/strip-bom-3.0.0.tgz"
  integrity sha1-FKjsOVbC6B7bdSB5CuzyHCkK69I=

"@types/strip-json-comments@0.0.30":
  version "0.0.30"
  resolved "https://registry.npm.taobao.org/@types/strip-json-comments/download/@types/strip-json-comments-0.0.30.tgz"
  integrity sha1-mqMMBNshKpoGSdaub9UKzMQHSKE=

"@uppy/companion-client@^2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@uppy/companion-client/-/companion-client-2.2.2.tgz"
  integrity sha512-5mTp2iq97/mYSisMaBtFRry6PTgZA6SIL7LePteOV5x0/DxKfrZW3DEiQERJmYpHzy7k8johpm2gHnEKto56Og==
  dependencies:
    "@uppy/utils" "^4.1.2"
    namespace-emitter "^2.0.1"

"@uppy/core@^2.1.1":
  version "2.3.3"
  resolved "https://registry.npmjs.org/@uppy/core/-/core-2.3.3.tgz"
  integrity sha512-oTFYZT02dIoUGm8Ar6+Tg/xbL8MliwiPQdiuoCimPBmY19ZhuJm/K4wEYZ6nOFeYsgBWYi1yWfsmdx8LvFVx4g==
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.2"
    lodash.throttle "^4.1.1"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^3.1.25"
    preact "^10.5.13"

"@uppy/store-default@^2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@uppy/store-default/-/store-default-2.1.1.tgz"
  integrity sha512-xnpTxvot2SeAwGwbvmJ899ASk5tYXhmZzD/aCFsXePh/v8rNvR2pKlcQUH7cF/y4baUGq3FHO/daKCok/mpKqQ==

"@uppy/utils@^4.1.2":
  version "4.1.3"
  resolved "https://registry.npmjs.org/@uppy/utils/-/utils-4.1.3.tgz"
  integrity sha512-nTuMvwWYobnJcytDO3t+D6IkVq/Qs4Xv3vyoEZ+Iaf8gegZP+rEyoaFT2CK5XLRMienPyqRqNbIfRuFaOWSIFw==
  dependencies:
    lodash.throttle "^4.1.1"

"@uppy/xhr-upload@^2.0.3":
  version "2.1.3"
  resolved "https://registry.npmjs.org/@uppy/xhr-upload/-/xhr-upload-2.1.3.tgz"
  integrity sha512-YWOQ6myBVPs+mhNjfdWsQyMRWUlrDLMoaG7nvf/G6Y3GKZf8AyjFDjvvJ49XWQ+DaZOftGkHmF1uh/DBeGivJQ==
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    nanoid "^3.1.25"

"@videojs/http-streaming@2.14.2":
  version "2.14.2"
  resolved "https://registry.npmmirror.com/@videojs/http-streaming/-/http-streaming-2.14.2.tgz"
  integrity sha512-K1raSfO/pq5r8iUas3OSYni0kXOj91n8ealIpV02khghzGv9LQ6O3YUqYd/eAhJ1HIrmZWOnrYpK/P+mhUExXQ==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "3.0.5"
    aes-decrypter "3.1.3"
    global "^4.4.0"
    m3u8-parser "4.7.1"
    mpd-parser "0.21.1"
    mux.js "6.0.1"
    video.js "^6 || ^7"

"@videojs/vhs-utils@^3.0.4", "@videojs/vhs-utils@^3.0.5", "@videojs/vhs-utils@3.0.5":
  version "3.0.5"
  resolved "https://registry.npmmirror.com/@videojs/vhs-utils/-/vhs-utils-3.0.5.tgz"
  integrity sha512-PKVgdo8/GReqdx512F+ombhS+Bzogiofy1LgAj4tN8PfdBx3HSS7V5WfJotKTqtOWGwVfSWsrYN/t09/DSryrw==
  dependencies:
    "@babel/runtime" "^7.12.5"
    global "^4.4.0"
    url-toolkit "^2.2.1"

"@videojs/xhr@2.6.0":
  version "2.6.0"
  resolved "https://registry.npmmirror.com/@videojs/xhr/-/xhr-2.6.0.tgz"
  integrity sha512-7J361GiN1tXpm+gd0xz2QWr3xNWBE+rytvo8J3KuggFaLg+U37gZQ2BuPLcnkfGffy2e+ozY70RHC8jt7zjA6Q==
  dependencies:
    "@babel/runtime" "^7.5.5"
    global "~4.4.0"
    is-function "^1.0.1"

"@vue/composition-api@^1.3.0":
  version "1.3.0"
  resolved "https://registry.npmmirror.com/@vue/composition-api/-/composition-api-1.3.0.tgz"
  integrity sha512-LlJAZHI6kS88oFZQ55c4BcGSa6n2vZ288i96cuK+/r/VFjyAd5oQpMTw1zjxSxJ78a0x/d+lcq7Ctg+9VoD6Rw==
  dependencies:
    tslib "^2.3.0"

"@wangeditor/basic-modules@^1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@wangeditor/basic-modules/-/basic-modules-1.1.3.tgz"
  integrity sha512-TGJix4UelO46yAgwI946ctx4lSIJbYBwNvjSJ9Tf8mKr0WMCeLVBV+MV85rXPsfcmWtR4wBNwSg648Z+RbqRUg==
  dependencies:
    is-url "^1.2.4"

"@wangeditor/code-highlight@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@wangeditor/code-highlight/-/code-highlight-1.0.2.tgz"
  integrity sha512-SCtOcUxjKqIso/LSxGSOaYr3G6MC2En0gNTyHIMCG928T0fo0ufaqp/vIXKQzVL2Y+X/CSAOB2EbrFlgGvr0AQ==
  dependencies:
    prismjs "^1.23.0"

"@wangeditor/core@^1.1.12":
  version "1.1.12"
  resolved "https://registry.npmjs.org/@wangeditor/core/-/core-1.1.12.tgz"
  integrity sha512-u6NBap1fHldFkxTLYdpezsGsi+a8F3ES0Bg6pu2TCQm4Qwl6xV0yvehFxZnOPomL+GrVGuCrvvOLnr9T3zntEg==
  dependencies:
    "@types/event-emitter" "^0.3.3"
    event-emitter "^0.3.5"
    html-void-elements "^2.0.0"
    i18next "^20.4.0"
    scroll-into-view-if-needed "^2.2.28"
    slate-history "^0.66.0"

"@wangeditor/editor-for-vue@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmmirror.com/@wangeditor/editor-for-vue/-/editor-for-vue-1.0.2.tgz"
  integrity sha512-BOENvAXJVtVXlE2X50AAvjV82YlCUeu5cbeR0cvEQHQjYtiVnJtq7HSoj85r2kTgGouI5OrpJG9BBEjSjUSPyA==

"@wangeditor/editor@^5.1.15":
  version "5.1.15"
  resolved "https://registry.npmjs.org/@wangeditor/editor/-/editor-5.1.15.tgz"
  integrity sha512-3QuxzBewDa3/yfVQxIXy6xMJg/oLZ3049MtcNI1Vzovozd57LUrU8160k+rB6bS2bq3mr7UkGTL4HxOMuMcYpw==
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.3"
    "@wangeditor/code-highlight" "^1.0.2"
    "@wangeditor/core" "^1.1.12"
    "@wangeditor/list-module" "^1.0.2"
    "@wangeditor/table-module" "^1.1.1"
    "@wangeditor/upload-image-module" "^1.0.1"
    "@wangeditor/video-module" "^1.1.2"
    dom7 "^3.0.0"
    is-hotkey "^0.2.0"
    lodash.camelcase "^4.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.debounce "^4.0.8"
    lodash.foreach "^4.5.0"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.toarray "^4.4.0"
    nanoid "^3.2.0"
    slate "^0.72.0"
    snabbdom "^3.1.0"

"@wangeditor/list-module@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@wangeditor/list-module/-/list-module-1.0.2.tgz"
  integrity sha512-VfENZEFvsLTiLxN/cj8cibFGy9NVV+/cfATTiLiH9ef+8lgKv8apttXYVlqIAfnlJLLuCk0cIm8c/zH+hbtrZg==

"@wangeditor/table-module@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@wangeditor/table-module/-/table-module-1.1.1.tgz"
  integrity sha512-VPjEWQtncS2DsXYXiHUxPSxn2Xhc8GdhG3la7N5YhvxQde1+4N0SZLXeWsYvbGzOq4um5XToq5pktLLbE8G+EA==

"@wangeditor/upload-image-module@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@wangeditor/upload-image-module/-/upload-image-module-1.0.1.tgz"
  integrity sha512-vgUV4ENttTITblqtVuzleIq732OmzmzzgrIvX6b3GRGPSw5u8glJ/87tOEhvHjHECc4oFo18B7xzJ1GpBj79/w==

"@wangeditor/video-module@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@wangeditor/video-module/-/video-module-1.1.2.tgz"
  integrity sha512-y8pMejpQ/AnAQZx+A86sGFo+ockg4tSc3oKpuN+1xFbnYMF6Tb+t7yt3+qY/ZJ2FffL635wxZpYbQWOboNUn9g==

"@xmldom/xmldom@^0.7.2":
  version "0.7.13"
  resolved "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.7.13.tgz"
  integrity sha512-lm2GW5PkosIzccsaZIz7tp8cPADSIlIHWDFTR1N0SzfinhhYgeIQjFMz4rYzanCScr3DqQLeomUDArp6MWKm+g==

abab@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/abab/download/abab-2.0.5.tgz"
  integrity sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=

abbrev@1:
  version "1.1.1"
  resolved "https://registry.nlark.com/abbrev/download/abbrev-1.1.1.tgz"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5:
  version "1.3.7"
  resolved "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-dynamic-import@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/acorn-dynamic-import/download/acorn-dynamic-import-2.0.2.tgz"
  integrity sha1-x1K9IQvvZ5UBtsbLf8hPj0cVjMQ=
  dependencies:
    acorn "^4.0.3"

acorn-globals@^4.1.0:
  version "4.3.4"
  resolved "https://registry.nlark.com/acorn-globals/download/acorn-globals-4.3.4.tgz"
  integrity sha1-n6GSat3BHJcwjE5m163Q1Awycuc=
  dependencies:
    acorn "^6.0.1"
    acorn-walk "^6.0.1"

acorn-jsx@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/acorn-jsx/download/acorn-jsx-3.0.1.tgz"
  integrity sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=
  dependencies:
    acorn "^3.0.4"

acorn-walk@^6.0.1:
  version "6.2.0"
  resolved "https://registry.nlark.com/acorn-walk/download/acorn-walk-6.2.0.tgz"
  integrity sha1-Ejy487hMIXHx9/slJhWxx4prGow=

acorn@^3.0.4:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-3.3.0.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-3.3.0.tgz"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^4.0.3:
  version "4.0.13"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-4.0.13.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-4.0.13.tgz"
  integrity sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c=

acorn@^5.0.0, acorn@^5.2.1, acorn@^5.3.0, acorn@^5.5.0, acorn@^5.5.3:
  version "5.7.4"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-5.7.4.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-5.7.4.tgz"
  integrity sha1-Po2KmUfQWZoXltECJddDL0pKz14=

acorn@^6.0.1:
  version "6.4.2"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-6.4.2.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-6.4.2.tgz"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

adler-32@~1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/adler-32/download/adler-32-1.2.0.tgz"
  integrity sha1-aj5r8KY5ALoVZSgIyxXGgT0aXyU=
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

adler-32@~1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/adler-32/download/adler-32-1.3.0.tgz"
  integrity sha1-PK0bcc36afbIqR8+NhXTGk/e3HI=
  dependencies:
    printj "~1.2.2"

aes-decrypter@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/aes-decrypter/-/aes-decrypter-1.0.3.tgz"
  integrity sha512-rsx8pfx7wJsn+ziYbpJ8XA5c93hKAtBCrfydxJqJCMT+qfjipd/B5wC2xHtBcoxyvlqJcpeAo3K55t0lXOn9yQ==
  dependencies:
    pkcs7 "^0.2.3"

aes-decrypter@3.1.3:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/aes-decrypter/-/aes-decrypter-3.1.3.tgz"
  integrity sha512-VkG9g4BbhMBy+N5/XodDeV6F02chEk9IpgRTq/0bS80y4dzy79VH2Gtms02VXomf3HmyRe3yyJYkJ990ns+d6A==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^3.0.5"
    global "^4.4.0"
    pkcs7 "^1.0.4"

agent-base@2:
  version "2.1.1"
  resolved "https://registry.nlark.com/agent-base/download/agent-base-2.1.1.tgz"
  integrity sha1-1t4Q1a9hMtW9aSQn1G/FOFOQlMc=
  dependencies:
    extend "~3.0.0"
    semver "~5.0.1"

ajv-keywords@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-2.1.1.tgz"
  integrity sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I=

ajv-keywords@^3.1.0, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^5.0.0:
  version "5.5.2"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-5.5.2.tgz"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^5.2.3:
  version "5.5.2"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-5.5.2.tgz"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^5.3.0:
  version "5.5.2"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-5.5.2.tgz"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^6.1.0, ajv@^6.12.3, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://registry.nlark.com/align-text/download/align-text-0.1.4.tgz"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

alphanum-sort@^1.0.0, alphanum-sort@^1.0.1, alphanum-sort@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/amdefine/-/amdefine-1.0.1.tgz"
  integrity sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==

animate.css@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/animate.css/download/animate.css-4.1.1.tgz"
  integrity sha1-YU7FqBEx1+TcNipYFD90BqvWgHU=

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz?cache=0&sync_timestamp=1618847144938&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-escapes%2Fdownload%2Fansi-escapes-3.2.0.tgz"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-html@0.0.7:
  version "0.0.7"
  resolved "https://registry.nlark.com/ansi-html/download/ansi-html-0.0.7.tgz"
  integrity sha1-gTWEAhliqenm/QOflA0S9WynhZ4=

ansi-regex@^0.2.0, ansi-regex@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-0.2.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-0.2.1.tgz"
  integrity sha1-DY6UaWej2BQ/k+JOKYUl/BsiNfk=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.1.tgz"
  integrity sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==

ansi-styles@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-1.1.0.tgz"
  integrity sha512-f2PKUkN5QngiSemowa6Mrk9MPCdtFiOSmibjZ+j1qhLGHHYsqZwmBMRF3IRMVXo8sybDqx2fJl2d/8OphBoWkA==

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.2.tgz"
  integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-transform@^0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/append-transform/download/append-transform-0.4.0.tgz"
  integrity sha1-126/jKlNJ24keja61EpLdKthGZE=
  dependencies:
    default-require-extensions "^1.0.0"

aproba@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/aproba/download/aproba-1.2.0.tgz"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/arr-diff/download/arr-diff-2.0.0.tgz"
  integrity sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/arr-flatten/download/arr-flatten-1.1.0.tgz?cache=0&sync_timestamp=1618846805394&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farr-flatten%2Fdownload%2Farr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/array-equal/download/array-equal-1.0.0.tgz"
  integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/array-find-index/download/array-find-index-1.0.2.tgz"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-2.1.2.tgz"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

array-includes@^3.0.3, array-includes@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npmmirror.com/array-includes/download/array-includes-3.1.4.tgz"
  integrity sha1-9bSTFix2DzU5Yx8AW6K7Rqy0W6k=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    is-string "^1.0.7"

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz?cache=0&sync_timestamp=1614624407140&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-union%2Fdownload%2Farray-union-1.0.2.tgz"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.nlark.com/array-uniq/download/array-uniq-1.0.3.tgz?cache=0&sync_timestamp=1620042121153&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farray-uniq%2Fdownload%2Farray-uniq-1.0.3.tgz"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/array-unique/download/array-unique-0.2.1.tgz"
  integrity sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.flat@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/array.prototype.flat/download/array.prototype.flat-1.2.5.tgz?cache=0&sync_timestamp=1633109609894&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Farray.prototype.flat%2Fdownload%2Farray.prototype.flat-1.2.5.tgz"
  integrity sha1-B+CXXYS7x8SM0YedYJ5oJZjTPhM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"

arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/arrify/download/arrify-1.0.1.tgz?cache=0&sync_timestamp=1619599497996&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farrify%2Fdownload%2Farrify-1.0.1.tgz"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asn1.js@^4.10.1:
  version "4.10.1"
  resolved "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz"
  integrity sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "https://registry.npm.taobao.org/asn1.js/download/asn1.js-5.4.1.tgz"
  integrity sha1-EamAuE67kXgc41sP3C7ilON4Pwc=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asn1@~0.2.3:
  version "0.2.6"
  resolved "https://registry.npmmirror.com/asn1/download/asn1-0.2.6.tgz?cache=0&sync_timestamp=1635986760581&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fasn1%2Fdownload%2Fasn1-0.2.6.tgz"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@^1.0.0, assert-plus@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "https://registry.nlark.com/assert/download/assert-1.5.0.tgz?cache=0&sync_timestamp=1618847153747&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fassert%2Fdownload%2Fassert-1.5.0.tgz"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assertion-error@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/assertion-error/download/assertion-error-1.0.0.tgz"
  integrity sha1-x/hUOP3UZrx8oWq5DIFRN5el0js=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

ast-types@0.9.6:
  version "0.9.6"
  resolved "https://registry.npmmirror.com/ast-types/-/ast-types-0.9.6.tgz"
  integrity sha512-qEdtR2UH78yyHX/AUNfXmJTlM48XoFZKBdwi1nzkI1mJL21cmbu0cvjxjpkXJ5NENMq42H+hNs8VLJcqXLerBQ==

ast-types@0.x.x:
  version "0.14.2"
  resolved "https://registry.npmmirror.com/ast-types/download/ast-types-0.14.2.tgz"
  integrity sha1-YAuILfhYPjzU8t9fog+oN1nUvf0=
  dependencies:
    tslib "^2.0.1"

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/astral-regex/download/astral-regex-1.0.0.tgz"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/async-each/download/async-each-1.0.3.tgz"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.1.tgz"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@^3.3.0:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/async-validator/download/async-validator-3.5.2.tgz?cache=0&sync_timestamp=1634529574100&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fasync-validator%2Fdownload%2Fasync-validator-3.5.2.tgz"
  integrity sha1-aOhmqWgk6LJpT/eoMcGiXETV5QA=

async-validator@~1.8.1:
  version "1.8.5"
  resolved "https://registry.npmmirror.com/async-validator/download/async-validator-1.8.5.tgz?cache=0&sync_timestamp=1634529574100&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fasync-validator%2Fdownload%2Fasync-validator-1.8.5.tgz"
  integrity sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=
  dependencies:
    babel-runtime "6.x"

async@^2.1.2, async@^2.1.4, async@^2.4.1, async@^2.6.2:
  version "2.6.4"
  resolved "https://registry.npmjs.org/async/-/async-2.6.4.tgz"
  integrity sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^6.3.1:
  version "6.7.7"
  resolved "https://registry.npmmirror.com/autoprefixer/download/autoprefixer-6.7.7.tgz"
  integrity sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ=
  dependencies:
    browserslist "^1.7.6"
    caniuse-db "^1.0.30000634"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^5.2.16"
    postcss-value-parser "^3.2.3"

autoprefixer@^7.1.2:
  version "7.2.6"
  resolved "https://registry.npmmirror.com/autoprefixer/download/autoprefixer-7.2.6.tgz"
  integrity sha1-JWZy+G98c12oScTwfQCKuwVgZ9w=
  dependencies:
    browserslist "^2.11.3"
    caniuse-lite "^1.0.30000805"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^6.0.17"
    postcss-value-parser "^3.2.3"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "https://registry.nlark.com/aws4/download/aws4-1.11.0.tgz"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axios@^0.21.4:
  version "0.21.4"
  resolved "https://registry.npmmirror.com/axios/download/axios-0.21.4.tgz?cache=0&sync_timestamp=1635213960429&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Faxios%2Fdownload%2Faxios-0.21.4.tgz"
  integrity sha1-xnuQ3AVo5cHPKwuFjEO6KOLtpXU=
  dependencies:
    follow-redirects "^1.14.0"

babel-code-frame@^6.22.0, babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.0.0, babel-core@^6.22.1, babel-core@^6.25.0, babel-core@^6.26.0:
  version "6.26.3"
  resolved "https://registry.nlark.com/babel-core/download/babel-core-6.26.3.tgz"
  integrity sha1-suLwnjQtDwyI4vAuBneUEl51wgc=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-eslint@^8.2.1:
  version "8.2.6"
  resolved "https://registry.npmmirror.com/babel-eslint/download/babel-eslint-8.2.6.tgz"
  integrity sha1-YnDQxzIFYoBnwPeuFpOp55es79k=
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/traverse" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    eslint-scope "3.7.1"
    eslint-visitor-keys "^1.0.0"

babel-generator@^6.18.0, babel-generator@^6.26.0:
  version "6.26.1"
  resolved "https://registry.npm.taobao.org/babel-generator/download/babel-generator-6.26.1.tgz"
  integrity sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA=
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-bindify-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz"
  integrity sha1-FMGeXxQte0fxmlJDHlKxzLxAozA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz"
  integrity sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz"
  integrity sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz"
  integrity sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz"
  integrity sha1-8luCz33BBDPFX3BZLVdGQArCLKo=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-explode-class@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz"
  integrity sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes=
  dependencies:
    babel-helper-bindify-decorators "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz"
  integrity sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz"
  integrity sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz"
  integrity sha1-HssnaJydJVE+rbyZFKc/VAi+enY=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz"
  integrity sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz"
  integrity sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz"
  integrity sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz"
  integrity sha1-v22/5Dk40XNpohPKiov3S2qQqxo=
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-vue-jsx-merge-props@^2.0.0, babel-helper-vue-jsx-merge-props@^2.0.2, babel-helper-vue-jsx-merge-props@^2.0.3:
  version "2.0.3"
  resolved "https://registry.nlark.com/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  integrity sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-helpers/download/babel-helpers-6.24.1.tgz"
  integrity sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-jest@^21.0.2:
  version "21.2.0"
  resolved "https://registry.npmmirror.com/babel-jest/download/babel-jest-21.2.0.tgz"
  integrity sha1-LOBZUZqTdKLEbyRVtvvvWtddhj4=
  dependencies:
    babel-plugin-istanbul "^4.0.0"
    babel-preset-jest "^21.2.0"

babel-jest@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/babel-jest/download/babel-jest-22.4.4.tgz"
  integrity sha1-l3JZJAQg4idETr5J4iamHknqZZ0=
  dependencies:
    babel-plugin-istanbul "^4.1.5"
    babel-preset-jest "^22.4.4"

babel-loader@^7.1.1:
  version "7.1.5"
  resolved "https://registry.npmmirror.com/babel-loader/download/babel-loader-7.1.5.tgz"
  integrity sha1-4+4M1zlKpVfgE7AtPkkr/QeqbWg=
  dependencies:
    find-cache-dir "^1.0.0"
    loader-utils "^1.0.2"
    mkdirp "^0.5.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.nlark.com/babel-messages/download/babel-messages-6.23.0.tgz"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz"
  integrity sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-dynamic-import-node@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-1.2.0.tgz?cache=0&sync_timestamp=1618846790496&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-dynamic-import-node%2Fdownload%2Fbabel-plugin-dynamic-import-node-1.2.0.tgz"
  integrity sha1-+RYx5wPgWV5H1L6vuwiFdsh/vu4=
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"

babel-plugin-import@^1.13.3:
  version "1.13.3"
  resolved "https://registry.npm.taobao.org/babel-plugin-import/download/babel-plugin-import-1.13.3.tgz?cache=0&sync_timestamp=1606209944483&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-import%2Fdownload%2Fbabel-plugin-import-1.13.3.tgz"
  integrity sha1-nbu6fRrHK9QSkXqDDUReAJQdJtc=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

babel-plugin-istanbul@^4.0.0, babel-plugin-istanbul@^4.1.5:
  version "4.1.6"
  resolved "https://registry.npmmirror.com/babel-plugin-istanbul/download/babel-plugin-istanbul-4.1.6.tgz"
  integrity sha1-NsWbIZLvzoHFs3gyG3QXWt0cmkU=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.13.0"
    find-up "^2.1.0"
    istanbul-lib-instrument "^1.10.1"
    test-exclude "^4.2.1"

babel-plugin-jest-hoist@^21.2.0:
  version "21.2.0"
  resolved "https://registry.npmmirror.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-21.2.0.tgz"
  integrity sha1-LO9jclm9S2KKbKzgOd5fzRTbsAY=

babel-plugin-jest-hoist@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-22.4.4.tgz"
  integrity sha1-uYUZBuqzTHv2+MiVorCL6hqETAs=

babel-plugin-jsx-event-modifiers@^2.0.2:
  version "2.0.5"
  resolved "https://registry.nlark.com/babel-plugin-jsx-event-modifiers/download/babel-plugin-jsx-event-modifiers-2.0.5.tgz"
  integrity sha1-k+brtddVO7CPn+2/egvuOvCaBHI=

babel-plugin-jsx-v-model@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/babel-plugin-jsx-v-model/download/babel-plugin-jsx-v-model-2.0.3.tgz"
  integrity sha1-w5ZBa5nLGveCCHMVrh0+YuBw9H0=
  dependencies:
    babel-plugin-syntax-jsx "^6.18.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

babel-plugin-jsx-vue-functional@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/babel-plugin-jsx-vue-functional/download/babel-plugin-jsx-vue-functional-2.1.0.tgz"
  integrity sha1-VjCgyG/hkE0owwRl5r8c9xI1ojk=

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz"
  integrity sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU=

babel-plugin-syntax-async-generators@^6.5.0:
  version "6.13.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz"
  integrity sha1-a8lj67FuzLrmuStZbrfzXDQqi5o=

babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz"
  integrity sha1-1+sjt5oxf4VDlixQW4J8fWysJ94=

babel-plugin-syntax-decorators@^6.13.0:
  version "6.13.0"
  resolved "https://registry.nlark.com/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz"
  integrity sha1-MSVjtNvePMgGzuPkFszurd0RrAs=

babel-plugin-syntax-dynamic-import@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz"
  integrity sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo=

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://registry.nlark.com/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz"
  integrity sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4=

babel-plugin-syntax-jsx@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-syntax-jsx/download/babel-plugin-syntax-jsx-6.18.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-syntax-jsx%2Fdownload%2Fbabel-plugin-syntax-jsx-6.18.0.tgz"
  integrity sha1-CvMqmm4Tyno/1QaeYtew9Y0NiUY=

babel-plugin-syntax-object-rest-spread@^6.13.0, babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "https://registry.nlark.com/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz?cache=0&sync_timestamp=1618847044608&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-syntax-object-rest-spread%2Fdownload%2Fbabel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  integrity sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz"
  integrity sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM=

babel-plugin-transform-async-generator-functions@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz"
  integrity sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-generators "^6.5.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-async-to-generator@^6.22.0, babel-plugin-transform-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz"
  integrity sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-properties@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz"
  integrity sha1-anl2PqYdM9NvN7YRqp3vgagbRqw=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz"
  integrity sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0=
  dependencies:
    babel-helper-explode-class "^6.24.1"
    babel-plugin-syntax-decorators "^6.13.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  integrity sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  integrity sha1-u8UbSflk1wy42OC5ToICRs46YUE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0:
  version "6.26.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  integrity sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.23.0:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  integrity sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  integrity sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  integrity sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  integrity sha1-c+s9MQypaePvnskcU3QabxV2Qj4=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.23.0:
  version "6.23.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  integrity sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  integrity sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  integrity sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  integrity sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1, babel-plugin-transform-es2015-modules-commonjs@^6.26.0:
  version "6.26.2"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  integrity sha1-WKeThjqefKhwvcWogRF/+sJ9tvM=
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  integrity sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  integrity sha1-rJl+YoXNGO1hdq22B9YCNErThGg=
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  integrity sha1-JM72muIcuDp/hgPa0CH1cusnj40=
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.23.0:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  integrity sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  integrity sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  integrity sha1-1taKmfia7cRTbIGlQujdnxdG+NE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  integrity sha1-AMHNsaynERLN8M9hJsLta0V8zbw=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  integrity sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  integrity sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  integrity sha1-04sS9C6nMj9yk4fxinxa4frrNek=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.22.0, babel-plugin-transform-exponentiation-operator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz"
  integrity sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.22.0, babel-plugin-transform-object-rest-spread@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  integrity sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.26.0"

babel-plugin-transform-regenerator@^6.22.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz"
  integrity sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-runtime@^6.15.0, babel-plugin-transform-runtime@^6.22.0:
  version "6.23.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-runtime/download/babel-plugin-transform-runtime-6.23.0.tgz"
  integrity sha1-iEkNRGUC6puOfvsP4J7E2ZR5se4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz"
  integrity sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-vue-jsx@^3.5.0:
  version "3.7.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-transform-vue-jsx/download/babel-plugin-transform-vue-jsx-3.7.0.tgz"
  integrity sha1-1ASS5mkqNrWU9+mhko9D6Wl0CWA=
  dependencies:
    esutils "^2.0.2"

babel-preset-env@^1.3.2, babel-preset-env@^1.6.0:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/babel-preset-env/download/babel-preset-env-1.7.0.tgz"
  integrity sha1-3qefpOvriDzTXasH4mDBycBN93o=
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^3.2.6"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-preset-jest@^21.2.0:
  version "21.2.0"
  resolved "https://registry.npmmirror.com/babel-preset-jest/download/babel-preset-jest-21.2.0.tgz"
  integrity sha1-/50rzgir2Y6KNtmopRibkXO4Vjg=
  dependencies:
    babel-plugin-jest-hoist "^21.2.0"
    babel-plugin-syntax-object-rest-spread "^6.13.0"

babel-preset-jest@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/babel-preset-jest/download/babel-preset-jest-22.4.4.tgz"
  integrity sha1-7J+9i819/SS4tTIODmiAEyNbfDk=
  dependencies:
    babel-plugin-jest-hoist "^22.4.4"
    babel-plugin-syntax-object-rest-spread "^6.13.0"

babel-preset-stage-2@^6.22.0:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz"
  integrity sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE=
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-class-properties "^6.24.1"
    babel-plugin-transform-decorators "^6.24.1"
    babel-preset-stage-3 "^6.24.1"

babel-preset-stage-3@^6.24.1:
  version "6.24.1"
  resolved "https://registry.nlark.com/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz"
  integrity sha1-g2raCp56f6N8sTj7kyb4eTSkg5U=
  dependencies:
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-generator-functions "^6.24.1"
    babel-plugin-transform-async-to-generator "^6.24.1"
    babel-plugin-transform-exponentiation-operator "^6.24.1"
    babel-plugin-transform-object-rest-spread "^6.22.0"

babel-preset-vue-app@^1.3.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/babel-preset-vue-app/download/babel-preset-vue-app-1.3.2.tgz"
  integrity sha1-U+mKwBKk7dP4NWzGqvoACm6YApI=
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-object-rest-spread "^6.26.0"
    babel-plugin-transform-runtime "^6.15.0"
    babel-preset-env "^1.6.0"
    babel-preset-vue "^1.2.1"
    babel-runtime "^6.20.0"

babel-preset-vue@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/babel-preset-vue/download/babel-preset-vue-1.2.1.tgz"
  integrity sha1-sN6Jd+DOmB/GgkzwpTeRegKm/oc=
  dependencies:
    babel-helper-vue-jsx-merge-props "^2.0.2"
    babel-plugin-jsx-event-modifiers "^2.0.2"
    babel-plugin-jsx-v-model "^2.0.1"
    babel-plugin-jsx-vue-functional "^2.1.0"
    babel-plugin-syntax-jsx "^6.18.0"
    babel-plugin-transform-vue-jsx "^3.5.0"

babel-register@^6.22.0, babel-register@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.taobao.org/babel-register/download/babel-register-6.26.0.tgz"
  integrity sha1-btAhFz4vy0htestFxgCahW9kcHE=
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@^6.0.0, babel-runtime@^6.18.0, babel-runtime@^6.20.0, babel-runtime@^6.22.0, babel-runtime@^6.26.0, babel-runtime@^6.9.2, babel-runtime@6.x:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.16.0, babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-template/download/babel-template-6.26.0.tgz"
  integrity sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.18.0, babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-traverse/download/babel-traverse-6.26.0.tgz"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.18.0, babel-types@^6.19.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-types/download/babel-types-6.26.0.tgz"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.taobao.org/babylon/download/babylon-6.18.0.tgz"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

babylon@7.0.0-beta.44:
  version "7.0.0-beta.44"
  resolved "https://registry.npm.taobao.org/babylon/download/babylon-7.0.0-beta.44.tgz"
  integrity sha1-iRWeFebjDFCW4i1zjYwK+KDoyh0=

balanced-match@^0.4.2:
  version "0.4.2"
  resolved "https://registry.npm.taobao.org/balanced-match/download/balanced-match-0.4.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbalanced-match%2Fdownload%2Fbalanced-match-0.4.2.tgz"
  integrity sha1-yz8+PHMtwPAe5wtAPzAuYddwmDg=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbalanced-match%2Fdownload%2Fbalanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

base62@^1.1.0:
  version "1.2.8"
  resolved "https://registry.npmmirror.com/base62/-/base62-1.2.8.tgz"
  integrity sha512-V6YHUbjLxN1ymqNLb1DPHoU1CpfdL7d2YTIp5W3U4hhoG4hhxNmsFDs66M9EXxBiSEke5Bt5dwdfMwwZF70iLA==

base64-js@^1.0.2:
  version "1.5.1"
  resolved "https://registry.nlark.com/base64-js/download/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

batch-processor@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/batch-processor/download/batch-processor-1.0.0.tgz"
  integrity sha1-dclcMrdI4IUNEMKxaPa9vpiRrOg=

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.taobao.org/batch/download/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj-node4@^5.2.0:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/bfj-node4/download/bfj-node4-5.3.1.tgz"
  integrity sha1-4j2LJwV/HQIU/FYRQq2duZjyaDA=
  dependencies:
    bluebird "^3.5.1"
    check-types "^7.3.0"
    tryer "^1.0.0"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/big.js/download/big.js-3.2.0.tgz"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://registry.nlark.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

bluebird@^3.1.1, bluebird@^3.4.7, bluebird@^3.5.0, bluebird@^3.5.1:
  version "3.7.2"
  resolved "https://registry.nlark.com/bluebird/download/bluebird-3.7.2.tgz?cache=0&sync_timestamp=1618847007562&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbluebird%2Fdownload%2Fbluebird-3.7.2.tgz"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bmaplib.curveline@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/bmaplib.curveline/-/bmaplib.curveline-1.0.0.tgz"
  integrity sha512-9wcFMVhiYxNPqpvsLDAADn3qDhNzXp2mA6VyHSHg2XOAgSooC7ZiujdFhy0sp+0QYjTfJ/MjmLuNoUg2HHxH4Q==

bmaplib.heatmap@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/bmaplib.heatmap/-/bmaplib.heatmap-1.0.4.tgz"
  integrity sha512-rmhqUARBpUSJ9jXzUI2j7dIOqnc38bqubkx/8a349U2qtw/ulLUwyzRD535OrA8G7w5cz4aPKm6/rNvUAarg/Q==

bmaplib.lushu@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/bmaplib.lushu/-/bmaplib.lushu-1.0.7.tgz"
  integrity sha512-LVvgpESPii6xGxyjnQjq8u+ic4NjvhdCPV/RiSS/PGTUdZKeTDS7prSpleJLZH3ES0+oc0gYn8bw0LtPYUSz2w==

bmaplib.markerclusterer@^1.0.13:
  version "1.0.13"
  resolved "https://registry.npmmirror.com/bmaplib.markerclusterer/-/bmaplib.markerclusterer-1.0.13.tgz"
  integrity sha512-VrLyWSiuDEVNi0yUfwOhFQ6z1oEEHS4w36GNu3iASu6p52QIx9uAXMUkuSCHReNR0bj2Cp9SA1dSx5RpojXajQ==
  dependencies:
    bmaplib.texticonoverlay "^1.0.2"

bmaplib.texticonoverlay@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/bmaplib.texticonoverlay/-/bmaplib.texticonoverlay-1.0.2.tgz"
  integrity sha512-4ZTWr4ZP3B6qEWput5Tut16CfZgII38YwM3bpyb4gFTQyORlKYryFp9WHWrwZZaHlOyYDAXG9SX0hka43jTADg==

bn.js@^4.0.0:
  version "4.12.0"
  resolved "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^4.1.0:
  version "4.12.0"
  resolved "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^4.11.9:
  version "4.12.0"
  resolved "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/bn.js/download/bn.js-5.2.0.tgz"
  integrity sha1-NYhgZ0OWxpl3canQUfzBtX1K4AI=

bn.js@^5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz"
  integrity sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==

body-parser@1.20.2:
  version "1.20.2"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz"
  integrity sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/bonjour/download/bonjour-3.5.0.tgz"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.0.0, brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.nlark.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://registry.npm.taobao.org/braces/download/braces-1.8.5.tgz"
  integrity sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.2.2, braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/brorand/download/brorand-1.1.0.tgz"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz?cache=0&sync_timestamp=1618847049949&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbrowser-process-hrtime%2Fdownload%2Fbrowser-process-hrtime-1.0.0.tgz"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browser-resolve@^1.11.2:
  version "1.11.3"
  resolved "https://registry.npm.taobao.org/browser-resolve/download/browser-resolve-1.11.3.tgz"
  integrity sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=
  dependencies:
    resolve "1.1.7"

browser-stdout@1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/browser-stdout/download/browser-stdout-1.3.0.tgz"
  integrity sha1-81HTKWnTL6XXpVZxVCY9korjvR8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4, browserify-aes@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/browserify-des/download/browserify-des-1.0.2.tgz"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/browserify-rsa/download/browserify-rsa-4.1.0.tgz"
  integrity sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.3"
  resolved "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.2.3.tgz"
  integrity sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==
  dependencies:
    bn.js "^5.2.1"
    browserify-rsa "^4.1.0"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.5"
    hash-base "~3.0"
    inherits "^2.0.4"
    parse-asn1 "^5.1.7"
    readable-stream "^2.3.8"
    safe-buffer "^5.2.1"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^1.3.6, browserslist@^1.5.2, browserslist@^1.7.6:
  version "1.7.7"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-1.7.7.tgz"
  integrity sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk=
  dependencies:
    caniuse-db "^1.0.30000639"
    electron-to-chromium "^1.2.7"

browserslist@^2.11.3:
  version "2.11.3"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-2.11.3.tgz"
  integrity sha1-/jYWeu0bvN5IJ+v+cTR6LMcLmbI=
  dependencies:
    caniuse-lite "^1.0.30000792"
    electron-to-chromium "^1.3.30"

browserslist@^3.2.6:
  version "3.2.8"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-3.2.8.tgz"
  integrity sha1-sABTYdZHHw9ZUnl6dvyYXx+Xj8Y=
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

browserslist@^4.0.0:
  version "4.18.1"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-4.18.1.tgz"
  integrity sha512-8ScCzdpPwR2wQh8IT82CA2VgDwjHyqMovPBZSNH54+tm4Jk2pCuv90gmAdH6J84OCRWi0b4gMe6O6XPXuJnjgQ==
  dependencies:
    caniuse-lite "^1.0.30001280"
    electron-to-chromium "^1.3.896"
    escalade "^3.1.1"
    node-releases "^2.0.1"
    picocolors "^1.0.0"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/bser/download/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://registry.npm.taobao.org/buffer-crc32/download/buffer-crc32-0.2.13.tgz"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-from@^1.0.0, buffer-from@^1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/buffer-from/download/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.nlark.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://registry.npm.taobao.org/buffer/download/buffer-4.9.2.tgz"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz?cache=0&sync_timestamp=1637015063162&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz?cache=0&sync_timestamp=1637015063162&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.1.0.tgz"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cacache@^10.0.4:
  version "10.0.4"
  resolved "https://registry.nlark.com/cacache/download/cacache-10.0.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcacache%2Fdownload%2Fcacache-10.0.4.tgz"
  integrity sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA=
  dependencies:
    bluebird "^3.5.1"
    chownr "^1.0.1"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    lru-cache "^4.1.1"
    mississippi "^2.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.2"
    ssri "^5.2.4"
    unique-filename "^1.1.0"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz?cache=0&sync_timestamp=1636237266442&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcache-base%2Fdownload%2Fcache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/call-bind/download/call-bind-1.0.2.tgz"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1633617041481&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/caller-path/download/caller-path-0.1.0.tgz?cache=0&sync_timestamp=1633674209796&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-path%2Fdownload%2Fcaller-path-0.1.0.tgz"
  integrity sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=
  dependencies:
    callsites "^0.2.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz?cache=0&sync_timestamp=1633674209796&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-path%2Fdownload%2Fcaller-path-2.0.0.tgz"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-0.2.0.tgz?cache=0&sync_timestamp=1628464722297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-0.2.0.tgz"
  integrity sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-2.0.0.tgz?cache=0&sync_timestamp=1628464722297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-2.0.0.tgz"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1606867297052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/camelcase-keys/download/camelcase-keys-2.1.0.tgz?cache=0&sync_timestamp=1633332959770&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase-keys%2Fdownload%2Fcamelcase-keys-2.1.0.tgz"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-1.2.1.tgz?cache=0&sync_timestamp=1636945151239&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-1.2.1.tgz"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

camelcase@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/camelcase/-/camelcase-1.2.1.tgz"
  integrity sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-2.1.1.tgz?cache=0&sync_timestamp=1636945151239&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-2.1.1.tgz"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-3.0.0.tgz?cache=0&sync_timestamp=1636945151239&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-3.0.0.tgz"
  integrity sha1-MvxLn82vhF/N9+c7uXysImHwqwo=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-4.1.0.tgz?cache=0&sync_timestamp=1636945151239&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-4.1.0.tgz"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

caniuse-api@^1.5.2:
  version "1.6.1"
  resolved "https://registry.npm.taobao.org/caniuse-api/download/caniuse-api-1.6.1.tgz"
  integrity sha1-tTTnxzTE+B7F++isoq0kNUuWLGw=
  dependencies:
    browserslist "^1.3.6"
    caniuse-db "^1.0.30000529"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/caniuse-api/download/caniuse-api-3.0.0.tgz"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-db@^1.0.30000529, caniuse-db@^1.0.30000634, caniuse-db@^1.0.30000639:
  version "1.0.30001283"
  resolved "https://registry.npmmirror.com/caniuse-db/download/caniuse-db-1.0.30001283.tgz"
  integrity sha512-xEAlBjCxAdtEc05VVrNJQdDvY7jfOx1QiVbmEavio5DSM/hAypRz7pqKyVF0o9uaDX4j8bAjKieLIfTHRfANgA==

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30000792, caniuse-lite@^1.0.30000805, caniuse-lite@^1.0.30000844, caniuse-lite@^1.0.30001280:
  version "1.0.30001286"
  resolved "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001286.tgz"
  integrity sha512-zaEMRH6xg8ESMi2eQ3R4eZ5qw/hJiVsO/HlLwniIwErij0JDr9P+8V4dtx1l+kLq6j3yy8l8W4fst1lBnat5wQ==

capture-exit@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/capture-exit/download/capture-exit-1.2.0.tgz"
  integrity sha1-HF/MSJ/QqwDU8ax64QcuMXP7q28=
  dependencies:
    rsvp "^3.3.3"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.npm.taobao.org/center-align/download/center-align-0.1.3.tgz"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

cfb@^1.1.4:
  version "1.2.1"
  resolved "https://registry.nlark.com/cfb/download/cfb-1.2.1.tgz"
  integrity sha1-IJQp5MaO/TBkH2/HSy1gKL0gJAI=
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"
    printj "~1.3.0"

chai-nightwatch@~0.1.x:
  version "0.1.1"
  resolved "https://registry.nlark.com/chai-nightwatch/download/chai-nightwatch-0.1.1.tgz?cache=0&sync_timestamp=1630259114673&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchai-nightwatch%2Fdownload%2Fchai-nightwatch-0.1.1.tgz"
  integrity sha1-HKVt52jTwIaP5/wvTTLC/olOa+k=
  dependencies:
    assertion-error "1.0.0"
    deep-eql "0.1.3"

chalk@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.3.2, chalk@^2.4.1:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@~0.5.1:
  version "0.5.1"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-0.5.1.tgz"
  integrity sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==
  dependencies:
    ansi-styles "^1.1.0"
    escape-string-regexp "^1.0.0"
    has-ansi "^0.1.0"
    strip-ansi "^0.3.0"
    supports-color "^0.2.0"

chardet@^0.4.0:
  version "0.4.2"
  resolved "https://registry.npmmirror.com/chardet/download/chardet-0.4.2.tgz?cache=0&sync_timestamp=1634639141514&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchardet%2Fdownload%2Fchardet-0.4.2.tgz"
  integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=

check-types@^7.3.0:
  version "7.4.0"
  resolved "https://registry.nlark.com/check-types/download/check-types-7.4.0.tgz"
  integrity sha1-A3jsG5YW7HH3dJMaPGUW+tjBUvQ=

chokidar@^2.1.2, chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmmirror.com/chokidar/download/chokidar-2.1.8.tgz"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.1:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/chokidar/download/chokidar-3.5.2.tgz"
  integrity sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

"chokidar@>=3.0.0 <4.0.0":
  version "3.5.3"
  resolved "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.0.1:
  version "1.1.4"
  resolved "https://registry.nlark.com/chownr/download/chownr-1.1.4.tgz"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chromedriver@^2.27.2:
  version "2.46.0"
  resolved "https://registry.npmmirror.com/chromedriver/download/chromedriver-2.46.0.tgz?cache=0&sync_timestamp=1637114307032&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchromedriver%2Fdownload%2Fchromedriver-2.46.0.tgz"
  integrity sha1-PXjn65u2XdgE/jJ6a/dvztEr4FM=
  dependencies:
    del "^3.0.0"
    extract-zip "^1.6.7"
    mkdirp "^0.5.1"
    request "^2.88.0"
    tcp-port-used "^1.0.1"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/ci-info/download/ci-info-1.6.0.tgz"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/cipher-base/download/cipher-base-1.0.4.tgz"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

circular-json@^0.3.1:
  version "0.3.3"
  resolved "https://registry.npmmirror.com/circular-json/download/circular-json-0.3.3.tgz"
  integrity sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY=

clap@^1.0.9:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/clap/download/clap-1.2.3.tgz"
  integrity sha1-TzZ0WzIAhJJVf0ZBLWbVDLmbzlE=
  dependencies:
    chalk "^1.1.3"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@4.2.x:
  version "4.2.4"
  resolved "https://registry.npmmirror.com/clean-css/download/clean-css-4.2.4.tgz?cache=0&sync_timestamp=1634992314911&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclean-css%2Fdownload%2Fclean-css-4.2.4.tgz"
  integrity sha1-czv0brpOYHxokepXwkqYk1aDEXg=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-2.1.0.tgz?cache=0&sync_timestamp=1629747481175&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^1.0.1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-1.3.1.tgz?cache=0&sync_timestamp=1633109609172&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-spinners%2Fdownload%2Fcli-spinners-1.3.1.tgz"
  integrity sha1-ACwZkJEtDVlYDJO9NsBW3pnkJZo=

cli-width@^2.0.0:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/cli-width/download/cli-width-2.2.1.tgz"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cli@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/cli/download/cli-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli%2Fdownload%2Fcli-1.0.1.tgz"
  integrity sha1-IoF1NPJL+klQw01TLUjsvGIbjBQ=
  dependencies:
    exit "0.1.2"
    glob "^7.1.1"

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-2.1.0.tgz"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^3.2.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-3.2.0.tgz"
  integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^4.0.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-4.1.0.tgz"
  integrity sha1-NIQi2+gtgAswIu709qwQvy5NG0k=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.nlark.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.nlark.com/clone/download/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

clone@2.x:
  version "2.1.2"
  resolved "https://registry.nlark.com/clone/download/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npm.taobao.org/co/download/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

co@~3.0.6:
  version "3.0.6"
  resolved "https://registry.npm.taobao.org/co/download/co-3.0.6.tgz"
  integrity sha1-FEXyJsXrlWE45oyawwFn6n0ua9o=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/coa/download/coa-2.0.2.tgz"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

coa@~1.0.1:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/coa/download/coa-1.0.4.tgz"
  integrity sha1-qe8VNmDWqGqL3sAomlxoTSF0Mv0=
  dependencies:
    q "^1.1.2"

coalescy@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/coalescy/download/coalescy-1.0.0.tgz"
  integrity sha1-SwZYRrg2NhrabEtKSr9LwcrDG/E=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/code-point-at/download/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

codepage@~1.15.0:
  version "1.15.0"
  resolved "https://registry.nlark.com/codepage/download/codepage-1.15.0.tgz?cache=0&sync_timestamp=1627596487574&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcodepage%2Fdownload%2Fcodepage-1.15.0.tgz"
  integrity sha1-LgBRkCSzlCTsZu6z7AcifmkmGKs=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.3.0, color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-name@^1.0.0, color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-string@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/color-string/download/color-string-0.3.0.tgz"
  integrity sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE=
  dependencies:
    color-name "^1.0.0"

color-string@^1.6.0:
  version "1.9.0"
  resolved "https://registry.npmmirror.com/color-string/download/color-string-1.9.0.tgz"
  integrity sha512-9Mrz2AQLefkH1UvASKj6v6hj/7eWgjnT/cVsR8CumieLoT+g900exWeNogqtweI8dxloXN9BDQTYro1oWu/5CQ==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^0.11.0:
  version "0.11.4"
  resolved "https://registry.npmmirror.com/color/download/color-0.11.4.tgz"
  integrity sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q=
  dependencies:
    clone "^1.0.2"
    color-convert "^1.3.0"
    color-string "^0.3.0"

color@^3.0.0:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/color/download/color-3.2.1.tgz"
  integrity sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colormin@^1.0.5:
  version "1.1.2"
  resolved "https://registry.nlark.com/colormin/download/colormin-1.1.2.tgz"
  integrity sha1-6i90IKcrlogaOKrlnsEkpvcpgTM=
  dependencies:
    color "^0.11.0"
    css-color-names "0.0.4"
    has "^1.0.1"

colors@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/colors/download/colors-1.1.2.tgz?cache=0&sync_timestamp=1599054118809&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolors%2Fdownload%2Fcolors-1.1.2.tgz"
  integrity sha1-FopHAXVran9RoSzgyXv6KMCE7WM=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.13.0, commander@^2.15.1, commander@^2.5.0, commander@2.17.x:
  version "2.17.1"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.17.1.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.17.1.tgz"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

commander@^2.19.0:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.1.0.tgz"
  integrity sha512-J2wnb6TKniXNOtoHS8TSrG9IOQluPrsmyAJ8oCUJOBmv+uLBCyPYAZkD2jFvw2DCzIXNnISIM01NIvr35TkBMQ==

commander@~2.13.0:
  version "2.13.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.13.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.13.0.tgz"
  integrity sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w=

commander@~2.19.0:
  version "2.19.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.19.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.19.0.tgz"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commander@~2.6.0:
  version "2.6.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.6.0.tgz"
  integrity sha512-PhbTMT+ilDXZKqH8xbvuUY2ZEQNef0Q7DKxgoEKb4ccytsdvVVJmYqR0sGbi96nxU6oGrwEIQnclpK2NBZuQlg==

commander@2.9.0:
  version "2.9.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.9.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.9.0.tgz"
  integrity sha1-nJkJQXbhIkDLItbFFGCYQA/g99Q=
  dependencies:
    graceful-readlink ">= 1.0.0"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

commoner@^0.10.1:
  version "0.10.8"
  resolved "https://registry.npmmirror.com/commoner/-/commoner-0.10.8.tgz"
  integrity sha512-3/qHkNMM6o/KGXHITA14y78PcfmXh4+AOCJpSoF73h4VY1JpdGv3CHMS5+JW6SwLhfJt4RhNmLAa7+RRX/62EQ==
  dependencies:
    commander "^2.5.0"
    detective "^4.3.1"
    glob "^5.0.15"
    graceful-fs "^4.1.2"
    iconv-lite "^0.4.5"
    mkdirp "^0.5.0"
    private "^0.1.6"
    q "^1.1.2"
    recast "^0.11.17"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.nlark.com/component-emitter/download/component-emitter-1.3.0.tgz"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.nlark.com/compressible/download/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.3:
  version "1.7.4"
  resolved "https://registry.npm.taobao.org/compression/download/compression-1.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompression%2Fdownload%2Fcompression-1.7.4.tgz"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

compute-scroll-into-view@^1.0.17:
  version "1.0.17"
  resolved "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-1.0.17.tgz"
  integrity sha512-j4dx+Fb0URmzbwwMUrhqWM2BEWHdFGx+qZ9qqASHRPqvTYdqvWnHg0H1hIbcyLnvgnoNAVMlwkepyqM3DaIFUg==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0, concat-stream@^1.6.0, concat-stream@^1.6.2:
  version "1.6.2"
  resolved "https://registry.nlark.com/concat-stream/download/concat-stream-1.6.2.tgz"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

concat-typed-array@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/concat-typed-array/-/concat-typed-array-1.0.2.tgz"
  integrity sha512-aC878bxeWSlrY6h60cCDwBUXpKwovZrB7+C4+VHNO1CIXW2gBLxbQ757jWtOXUscLGgYI8R84N6uy9fTJPe+0g==

condense-newlines@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/condense-newlines/download/condense-newlines-0.2.1.tgz"
  integrity sha1-PemFVTE5R10yUCyDsC9gaE0kxV8=
  dependencies:
    extend-shallow "^2.0.1"
    is-whitespace "^0.3.0"
    kind-of "^3.0.2"

config-chain@^1.1.12:
  version "1.1.13"
  resolved "https://registry.nlark.com/config-chain/download/config-chain-1.1.13.tgz"
  integrity sha1-+tB5Wqamza/57Rto6d/5Q3LCMvQ=
  dependencies:
    ini "^1.3.4"
    proto-list "~1.2.1"

connect-history-api-fallback@^1.3.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz?cache=0&sync_timestamp=1618847040596&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fconnect-history-api-fallback%2Fdownload%2Fconnect-history-api-fallback-1.6.0.tgz"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/console-browserify/download/console-browserify-1.2.0.tgz"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

consolidate@^0.14.0:
  version "0.14.5"
  resolved "https://registry.npm.taobao.org/consolidate/download/consolidate-0.14.5.tgz"
  integrity sha1-WiUEe8dvcwcmZ8jLUsmJiI9JTGM=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/constants-browserify/download/constants-browserify-1.0.0.tgz"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/content-type/download/content-type-1.0.4.tgz"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

content-type@~1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

convert-source-map@^1.4.0, convert-source-map@^1.5.1:
  version "1.8.0"
  resolved "https://registry.nlark.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1624045304679&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz"
  integrity sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.6.tgz"
  integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
  dependencies:
    is-what "^3.14.1"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://registry.nlark.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^4.0.1:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/copy-webpack-plugin/download/copy-webpack-plugin-4.6.0.tgz"
  integrity sha1-5/QN2KaEd9QF3Rt6hUquMksVi64=
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    globby "^7.1.1"
    is-glob "^4.0.0"
    loader-utils "^1.1.0"
    minimatch "^3.0.4"
    p-limit "^1.0.0"
    serialize-javascript "^1.4.0"

core-js-pure@^3.25.1:
  version "3.29.0"
  resolved "https://registry.npmmirror.com/core-js-pure/-/core-js-pure-3.29.0.tgz"
  integrity sha512-v94gUjN5UTe1n0yN/opTihJ8QBWD2O8i19RfTZR7foONPWArnjB96QA/wk5ozu1mm6ja3udQCzOzwQXTxi3xOQ==

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz"
  integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==

core-js@^2.5.0:
  version "2.6.12"
  resolved "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz"
  integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==

core-js@^2.6.5:
  version "2.6.12"
  resolved "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz"
  integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^2.1.0:
  version "2.2.2"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-2.2.2.tgz?cache=0&sync_timestamp=1629585969900&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-2.2.2.tgz"
  integrity sha1-YXPOvVb6wELB9DkO33r2wHx8uJI=
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.4.3"
    minimist "^1.2.0"
    object-assign "^4.1.0"
    os-homedir "^1.0.1"
    parse-json "^2.2.0"
    require-from-string "^1.1.0"

cosmiconfig@^2.1.1:
  version "2.2.2"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-2.2.2.tgz?cache=0&sync_timestamp=1629585969900&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-2.2.2.tgz"
  integrity sha1-YXPOvVb6wELB9DkO33r2wHx8uJI=
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.4.3"
    minimist "^1.2.0"
    object-assign "^4.1.0"
    os-homedir "^1.0.1"
    parse-json "^2.2.0"
    require-from-string "^1.1.0"

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz?cache=0&sync_timestamp=1629585969900&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-5.2.1.tgz"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

crc-32@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/crc-32/download/crc-32-1.2.0.tgz"
  integrity sha1-yy224puIUI4y2d0OwWk+e0Ghggg=
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "https://registry.nlark.com/create-ecdh/download/create-ecdh-4.0.4.tgz"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://registry.nlark.com/create-hmac/download/create-hmac-1.1.7.tgz"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^5.0.1, cross-spawn@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

crypto-es@^1.2.4:
  version "1.2.7"
  resolved "https://registry.npmmirror.com/crypto-es/-/crypto-es-1.2.7.tgz"
  integrity sha512-UUqiVJ2gUuZFmbFsKmud3uuLcNP2+Opt+5ysmljycFCyhA0+T16XJmo1ev/t5kMChMqWh7IEvURNCqsg+SjZGQ==

css-color-names@^0.0.4, css-color-names@0.0.4:
  version "0.0.4"
  resolved "https://registry.nlark.com/css-color-names/download/css-color-names-0.0.4.tgz"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz?cache=0&sync_timestamp=1630965563343&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-declaration-sorter%2Fdownload%2Fcss-declaration-sorter-4.0.1.tgz"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-loader@^0.28.11:
  version "0.28.11"
  resolved "https://registry.npmmirror.com/css-loader/download/css-loader-0.28.11.tgz?cache=0&sync_timestamp=1635967924209&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcss-loader%2Fdownload%2Fcss-loader-0.28.11.tgz"
  integrity sha1-w/mGSnAL4nEbtaJGKyOJsaOS2rc=
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    cssnano "^3.10.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash.camelcase "^4.3.0"
    object-assign "^4.1.1"
    postcss "^5.0.6"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/css-select/download/css-select-2.1.0.tgz?cache=0&sync_timestamp=1622994276976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-select%2Fdownload%2Fcss-select-2.1.0.tgz"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^4.1.3:
  version "4.1.3"
  resolved "https://registry.nlark.com/css-select/download/css-select-4.1.3.tgz?cache=0&sync_timestamp=1622994276976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-select%2Fdownload%2Fcss-select-4.1.3.tgz"
  integrity sha1-pwRA9wMX8maRGK10/xBeZYSccGc=
  dependencies:
    boolbase "^1.0.0"
    css-what "^5.0.0"
    domhandler "^4.2.0"
    domutils "^2.6.0"
    nth-check "^2.0.0"

css-selector-tokenizer@^0.7.0:
  version "0.7.3"
  resolved "https://registry.nlark.com/css-selector-tokenizer/download/css-selector-tokenizer-0.7.3.tgz?cache=0&sync_timestamp=1622561259529&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-selector-tokenizer%2Fdownload%2Fcss-selector-tokenizer-0.7.3.tgz"
  integrity sha1-c18mGG5nx0mq8nV4NAXPBmH66PE=
  dependencies:
    cssesc "^3.0.0"
    fastparse "^1.1.2"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/css-tree/download/css-tree-1.1.3.tgz"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://registry.npmmirror.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz"
  integrity sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-3.4.2.tgz"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

css-what@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-5.1.0.tgz"
  integrity sha1-P3tweq32M7r2LCzrhXm1RbtA9/4=

css@~2.2.0:
  version "2.2.4"
  resolved "https://registry.npm.taobao.org/css/download/css-2.2.4.tgz?cache=0&sync_timestamp=1593663543619&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss%2Fdownload%2Fcss-2.2.4.tgz"
  integrity sha1-xkZ1XHOXHyu6amAeLPL9cbEpiSk=
  dependencies:
    inherits "^2.0.3"
    source-map "^0.6.1"
    source-map-resolve "^0.5.2"
    urix "^0.1.0"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-4.0.8.tgz"
  integrity sha1-kgYisfwelaNOiDggPxOXpQTy0/8=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.3"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^3.10.0:
  version "3.10.0"
  resolved "https://registry.npmmirror.com/cssnano/download/cssnano-3.10.0.tgz"
  integrity sha1-Tzj2zqK5sX+gFJDyPx3GjqZcHDg=
  dependencies:
    autoprefixer "^6.3.1"
    decamelize "^1.1.2"
    defined "^1.0.0"
    has "^1.0.1"
    object-assign "^4.0.1"
    postcss "^5.0.14"
    postcss-calc "^5.2.0"
    postcss-colormin "^2.1.8"
    postcss-convert-values "^2.3.4"
    postcss-discard-comments "^2.0.4"
    postcss-discard-duplicates "^2.0.1"
    postcss-discard-empty "^2.0.1"
    postcss-discard-overridden "^0.1.1"
    postcss-discard-unused "^2.2.1"
    postcss-filter-plugins "^2.0.0"
    postcss-merge-idents "^2.1.5"
    postcss-merge-longhand "^2.0.1"
    postcss-merge-rules "^2.0.3"
    postcss-minify-font-values "^1.0.2"
    postcss-minify-gradients "^1.0.1"
    postcss-minify-params "^1.0.4"
    postcss-minify-selectors "^2.0.4"
    postcss-normalize-charset "^1.1.0"
    postcss-normalize-url "^3.0.7"
    postcss-ordered-values "^2.1.0"
    postcss-reduce-idents "^2.2.2"
    postcss-reduce-initial "^1.0.0"
    postcss-reduce-transforms "^1.0.3"
    postcss-svgo "^2.1.1"
    postcss-unique-selectors "^2.0.2"
    postcss-value-parser "^3.2.3"
    postcss-zindex "^2.0.1"

cssnano@^4.1.10:
  version "4.1.11"
  resolved "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.11.tgz"
  integrity sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.8"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^4.0.2:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/csso/download/csso-4.2.0.tgz"
  integrity sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=
  dependencies:
    css-tree "^1.1.2"

csso@~2.3.1:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/csso/download/csso-2.3.2.tgz?cache=0&sync_timestamp=1606408777341&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcsso%2Fdownload%2Fcsso-2.3.2.tgz"
  integrity sha1-3dUsWHAz9J6Utx/FVWnyUuj/X4U=
  dependencies:
    clap "^1.0.9"
    source-map "^0.5.3"

"cssom@>= 0.3.2 < 0.4.0", cssom@0.3.x:
  version "0.3.8"
  resolved "https://registry.nlark.com/cssom/download/cssom-0.3.8.tgz"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/cssstyle/download/cssstyle-1.4.0.tgz"
  integrity sha1-nTEyginTxWXGHlhrAgQaKPzNzPE=
  dependencies:
    cssom "0.3.x"

cuint@^0.2.2:
  version "0.2.2"
  resolved "https://registry.nlark.com/cuint/download/cuint-0.2.2.tgz"
  integrity sha1-QICG1AlVDCYxFVYZ6fp7ytw7mRs=

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/currently-unhandled/download/currently-unhandled-0.4.1.tgz"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/cyclist/download/cyclist-1.0.1.tgz"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

d@^1.0.1, d@1:
  version "1.0.1"
  resolved "https://registry.nlark.com/d/download/d-1.0.1.tgz"
  integrity sha1-hpgJU3LVjb7jRv/Qxwk/mfj561o=
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

danmu.js@^1.1.2:
  version "1.1.8"
  resolved "https://registry.npmmirror.com/danmu.js/-/danmu.js-1.1.8.tgz"
  integrity sha512-GIFSHqJ+HFTGLLaL2BHMPBaOuPY1bWPwC0Pvi/V06uMIoxNTyEGxMuoO2SzNHsDvKC/r252zR9T/Gwx93AaKfw==
  dependencies:
    event-emitter "^0.3.5"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz?cache=0&sync_timestamp=1601073454623&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdashdash%2Fdownload%2Fdashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-uri-to-buffer@1:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/data-uri-to-buffer/download/data-uri-to-buffer-1.2.0.tgz?cache=0&sync_timestamp=1632737513412&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdata-uri-to-buffer%2Fdownload%2Fdata-uri-to-buffer-1.2.0.tgz"
  integrity sha1-dxY+qcINhkG0cH6PGKvfmnjzSDU=

data-urls@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/data-urls/download/data-urls-1.1.0.tgz?cache=0&sync_timestamp=1633539797628&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdata-urls%2Fdownload%2Fdata-urls-1.1.0.tgz"
  integrity sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=
  dependencies:
    abab "^2.0.0"
    whatwg-mimetype "^2.2.0"
    whatwg-url "^7.0.0"

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/de-indent/download/de-indent-1.0.2.tgz"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@^2.2.0:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^2.6.6:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^2.6.8:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@^3.1.1, debug@^3.2.6, debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.0:
  version "4.3.3"
  resolved "https://registry.npmmirror.com/debug/download/debug-4.3.3.tgz"
  integrity sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==
  dependencies:
    ms "2.1.2"

debug@^4.3.2:
  version "4.3.4"
  resolved "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@2:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.2.0.tgz"
  integrity sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=
  dependencies:
    ms "0.7.1"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/debug/download/debug-4.3.1.tgz"
  integrity sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=
  dependencies:
    ms "2.1.2"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.1.2:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1633055728451&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-eql@0.1.3:
  version "0.1.3"
  resolved "https://registry.npm.taobao.org/deep-eql/download/deep-eql-0.1.3.tgz"
  integrity sha1-71WKyrjeJSBs1xOQbXTlaTDrafI=
  dependencies:
    type-detect "0.1.1"

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/deep-equal/download/deep-equal-1.1.1.tgz?cache=0&sync_timestamp=1606859714626&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.1.1.tgz"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.4"
  resolved "https://registry.nlark.com/deep-is/download/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^1.2.0:
  version "1.5.2"
  resolved "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

deepmerge@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/deepmerge/download/deepmerge-2.2.1.tgz"
  integrity sha1-XT/yKgHAD2RUBaL7wX0HeKGAEXA=

deepmerge@1.3.2:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.3.2.tgz"
  integrity sha1-FmNpFinU2/42T6EqKk8KqGqjoFA=

default-passive-events@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/default-passive-events/-/default-passive-events-2.0.0.tgz"
  integrity sha512-eMtt76GpDVngZQ3ocgvRcNCklUMwID1PaNbCNxfpDXuiOXttSh0HzBbda1HU9SIUsDc02vb7g9+3I5tlqe/qMQ==

default-require-extensions@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/default-require-extensions/download/default-require-extensions-1.0.0.tgz"
  integrity sha1-836hXT4T/9m0N9M+GnW1+5eHTLg=
  dependencies:
    strip-bom "^2.0.0"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/define-properties/download/define-properties-1.1.3.tgz?cache=0&sync_timestamp=1618847174317&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdefine-properties%2Fdownload%2Fdefine-properties-1.1.3.tgz"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

defined@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/defined/download/defined-1.0.0.tgz"
  integrity sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM=

degenerator@~1.0.2:
  version "1.0.4"
  resolved "https://registry.nlark.com/degenerator/download/degenerator-1.0.4.tgz"
  integrity sha1-/PSQo37OJmRk2cxDGrmMWBnO0JU=
  dependencies:
    ast-types "0.x.x"
    escodegen "1.x.x"
    esprima "3.x.x"

del@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/del/download/del-3.0.0.tgz?cache=0&sync_timestamp=1601076882347&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdel%2Fdownload%2Fdel-3.0.0.tgz"
  integrity sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

des.js@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/des.js/download/des.js-1.0.1.tgz"
  integrity sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/detect-indent/download/detect-indent-4.0.0.tgz?cache=0&sync_timestamp=1628462018641&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdetect-indent%2Fdownload%2Fdetect-indent-4.0.0.tgz"
  integrity sha1-920GQ1LN9Docts5hnE7jqUdd4gg=
  dependencies:
    repeating "^2.0.0"

detect-newline@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/detect-newline/download/detect-newline-2.1.0.tgz"
  integrity sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.nlark.com/detect-node/download/detect-node-2.1.0.tgz?cache=0&sync_timestamp=1621146954463&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdetect-node%2Fdownload%2Fdetect-node-2.1.0.tgz"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

detective@^4.3.1:
  version "4.7.1"
  resolved "https://registry.npmmirror.com/detective/-/detective-4.7.1.tgz"
  integrity sha512-H6PmeeUcZloWtdt4DAkFyzFL94arpHr3NOwwmVILFiy+9Qd4JTxxXrzfyGk/lmct2qVGBwTSwSXagqu2BxmWig==
  dependencies:
    acorn "^5.2.1"
    defined "^1.0.0"

diff@^3.2.0:
  version "3.5.0"
  resolved "https://registry.npm.taobao.org/diff/download/diff-3.5.0.tgz?cache=0&sync_timestamp=1604803664325&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdiff%2Fdownload%2Fdiff-3.5.0.tgz"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

diff@1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/diff/download/diff-1.4.0.tgz?cache=0&sync_timestamp=1604803664325&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdiff%2Fdownload%2Fdiff-1.4.0.tgz"
  integrity sha1-fyjS657nsVqX79ic5j3P2qPMur8=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://registry.nlark.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0:
  version "2.2.2"
  resolved "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/dns-equal/download/dns-equal-1.0.0.tgz"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "https://registry.nlark.com/dns-packet/download/dns-packet-1.3.4.tgz"
  integrity sha1-40VQZYJKJQe6iGxVqJljuxB97G8=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/dns-txt/download/dns-txt-2.0.2.tgz"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/doctrine/download/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@^1.0.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-1.3.2.tgz?cache=0&sync_timestamp=1621256830355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-1.3.2.tgz"
  integrity sha1-YgZDfTLO767HFhgDIwx6ILwbTZE=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1621256830355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-to-image-more@^2.10.1:
  version "2.10.1"
  resolved "https://registry.npmmirror.com/dom-to-image-more/-/dom-to-image-more-2.10.1.tgz"
  integrity sha512-gMG28V47WGj5/xvrsbSPJAWSaV7CBh4teLErn1iGD1sa29HsFsHxvnoLj8VxVvfqnjPgsiUGs2IV2VAxLJGb+A==

dom-to-image@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/dom-to-image/-/dom-to-image-2.6.0.tgz"
  integrity sha512-Dt0QdaHmLpjURjU7Tnu3AgYSF2LuOmksSGsUcE6ItvJoCWTBEmiMXcqBdNSAm9+QbbwD7JMoVsuuKX6ZVQv1qA==

dom-walk@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/dom-walk/-/dom-walk-0.1.2.tgz"
  integrity sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==

dom7@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/dom7/-/dom7-3.0.0.tgz"
  integrity sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g==
  dependencies:
    ssr-window "^3.0.0-alpha.1"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/domain-browser/download/domain-browser-1.2.0.tgz?cache=0&sync_timestamp=1627591557212&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomain-browser%2Fdownload%2Fdomain-browser-1.2.0.tgz"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@^1.3.1, domelementtype@1:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.1.tgz?cache=0&sync_timestamp=1617298545989&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomelementtype%2Fdownload%2Fdomelementtype-1.3.1.tgz"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-2.2.0.tgz?cache=0&sync_timestamp=1617298545989&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomelementtype%2Fdownload%2Fdomelementtype-2.2.0.tgz"
  integrity sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc=

domexception@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/domexception/download/domexception-1.0.1.tgz?cache=0&sync_timestamp=1633538737274&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdomexception%2Fdownload%2Fdomexception-1.0.1.tgz"
  integrity sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=
  dependencies:
    webidl-conversions "^4.0.2"

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/domhandler/download/domhandler-2.4.2.tgz"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domhandler@^4.0.0, domhandler@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/domhandler/download/domhandler-4.3.0.tgz"
  integrity sha512-fC0aXNQXqKSFTr2wDNZDhsEYjCiYsDWl3D01kwt25hm1YIPyDGHvvi3rw+PLqHAl/m71MaiF7d5zvBr0p5UB2g==
  dependencies:
    domelementtype "^2.2.0"

domready@1.0.8:
  version "1.0.8"
  resolved "https://registry.npm.taobao.org/domready/download/domready-1.0.8.tgz"
  integrity sha1-kfJS5Ze2Wvd+dFriTdAYXV4m1Yw=

domutils@^1.5.1:
  version "1.7.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-1.7.0.tgz?cache=0&sync_timestamp=1630106606599&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-1.7.0.tgz?cache=0&sync_timestamp=1630106606599&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2, domutils@^2.6.0:
  version "2.8.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-2.8.0.tgz?cache=0&sync_timestamp=1630106606599&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-2.8.0.tgz"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "https://registry.nlark.com/dot-prop/download/dot-prop-5.3.0.tgz"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

dotenv@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/dotenv/-/dotenv-2.0.0.tgz"
  integrity sha512-Y+zZAmv7p2zOdpyZcSIA+aIxohsyfTcNaMeh3YJn9exq85bQhso65Wz9IhjYYNB4zyvXnfi7Ae+FuygARljVJw==

downloadjs@1.4.7:
  version "1.4.7"
  resolved "https://registry.npmmirror.com/downloadjs/-/downloadjs-1.4.7.tgz"
  integrity sha512-LN1gO7+u9xjU5oEScGFKvXhYf7Y/empUIIEAGBs1LzUq/rg5duiDrkuH5A2lQGd5jfMOb9X9usDa2oVXwJ0U/Q==

draggabilly@^2.2.0:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/draggabilly/-/draggabilly-2.4.1.tgz"
  integrity sha512-HHHLPEPZqRXIDQDFRFdK7RONZausNlJ4WkA73ST7Z6O2HPWttxFHVwHo8nccuDLzXWwiVKRVuc6fTkW+CQA++A==
  dependencies:
    get-size "^2.0.2"
    unidragger "^2.4.0"

duplexer@^0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/duplexer/download/duplexer-0.1.2.tgz"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.nlark.com/duplexify/download/duplexify-3.7.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fduplexify%2Fdownload%2Fduplexify-3.7.1.tgz"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts-amap@1.0.0-rc.6:
  version "1.0.0-rc.6"
  resolved "https://registry.npmmirror.com/echarts-amap/-/echarts-amap-1.0.0-rc.6.tgz"
  integrity sha512-cYJCKoQdnkZXrGweYrveU1HruZd1c0KmsF1U8o3FtsvgR2jVL5ZUpGFjMmFtpolHOUFqxizk+s+QBLkYuOWL6Q==

echarts-extension-gmap@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/echarts-extension-gmap/-/echarts-extension-gmap-1.5.0.tgz"
  integrity sha512-WLXYynGr1mA8O7dD/8T3fV1ddtq65u73GSmBElCbG4IRdPDhSC8qkxAHsy4FHwtTG2tshdlTMTqWTX1gmV6vZw==

echarts-liquidfill@^2.0.2:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/echarts-liquidfill/-/echarts-liquidfill-2.0.6.tgz"
  integrity sha512-p+AH0O9/BtwXMQQyhjJbMZo+GwRAgWG/DCyK5r27PQzpS0UWrgXu57MyEFc0A8Ub3sRuqEu08BuxwHICBkSWSQ==

echarts-wordcloud@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/echarts-wordcloud/-/echarts-wordcloud-1.1.3.tgz"
  integrity sha512-Et8D5xEAoYkidmHun+hEH+2lF9dhCt6D0JJ390vlr2r/1zwhhZAbcL01CEvG93QcMcJpSvSPK8vRiGkTbMHRxg==

echarts@^3.1.10:
  version "3.8.5"
  resolved "https://registry.npmmirror.com/echarts/-/echarts-3.8.5.tgz"
  integrity sha512-E+nnROMfCeiLeoT/fZyX8SE8mKzwkTjyemyoBF543oqjRtjTSKQAVDEihMXy4oC6pJS0tYGdMqCA2ATk8onyRg==
  dependencies:
    zrender "3.7.4"

echarts@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/echarts/-/echarts-5.3.1.tgz"
  integrity sha512-nWdlbgX3OVY0hpqncSvp0gDt1FRSKWn7lsWEH+PHmfCuvE0QmSw17pczQvm8AvawnLEkmf1Cts7YwQJZNC0AEQ==
  dependencies:
    tslib "2.3.0"
    zrender "5.3.1"

editorconfig@^0.15.3:
  version "0.15.3"
  resolved "https://registry.npm.taobao.org/editorconfig/download/editorconfig-0.15.3.tgz"
  integrity sha1-vvhMTnX7jcsM5c7o79UcFZmb78U=
  dependencies:
    commander "^2.19.0"
    lru-cache "^4.1.5"
    semver "^5.6.0"
    sigmund "^1.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.5.7, ejs@2.5.7:
  version "2.5.7"
  resolved "https://registry.npmmirror.com/ejs/download/ejs-2.5.7.tgz"
  integrity sha1-zIcsFoiArjxxiXYv1f/ACJbJUYo=

electron-to-chromium@^1.2.7, electron-to-chromium@^1.3.30, electron-to-chromium@^1.3.47, electron-to-chromium@^1.3.896:
  version "1.4.5"
  resolved "https://registry.npmmirror.com/electron-to-chromium/download/electron-to-chromium-1.4.5.tgz"
  integrity sha512-YKaB+t8ul5crdh6OeqT2qXdxJGI0fAYb6/X8pDIyye+c3a7ndOCk5gVeKX+ABwivCGNS56vOAif3TN0qJMpEHw==

element-resize-detector@^1.2.0:
  version "1.2.3"
  resolved "https://registry.nlark.com/element-resize-detector/download/element-resize-detector-1.2.3.tgz?cache=0&sync_timestamp=1624540261948&other_urls=https%3A%2F%2Fregistry.nlark.com%2Felement-resize-detector%2Fdownload%2Felement-resize-detector-1.2.3.tgz"
  integrity sha1-UHjZuZOY/kxYn4yN+U/5nl1BP/M=
  dependencies:
    batch-processor "1.0.0"

element-ui@^2.15.6:
  version "2.15.6"
  resolved "https://registry.npmmirror.com/element-ui/download/element-ui-2.15.6.tgz"
  integrity sha1-yWCa3TWvWmhqS3aF3B11fHXgHfM=
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"

elliptic@^6.5.3:
  version "6.5.4"
  resolved "https://registry.npm.taobao.org/elliptic/download/elliptic-6.5.4.tgz"
  integrity sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

elliptic@^6.5.5:
  version "6.5.5"
  resolved "https://registry.npmjs.org/elliptic/-/elliptic-6.5.5.tgz"
  integrity sha512-7EjbcmUm17NQFu4Pmgmq2olYMj8nwMnpcddByChSUjArp8F5DQWcIcpriwO4ZToLNAJig0yiyjswfyGNje/ixw==
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/emojis-list/download/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.nlark.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^3.4.0:
  version "3.4.1"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-3.4.1.tgz?cache=0&sync_timestamp=1632130769099&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-3.4.1.tgz"
  integrity sha1-BCHjOf1xQZs9oT0Smzl5BAIwR24=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    object-assign "^4.0.1"
    tapable "^0.2.7"

entities@^1.1.1:
  version "1.1.2"
  resolved "https://registry.nlark.com/entities/download/entities-1.1.2.tgz?cache=0&sync_timestamp=1628508189267&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fentities%2Fdownload%2Fentities-1.1.2.tgz"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/entities/download/entities-2.2.0.tgz?cache=0&sync_timestamp=1628508189267&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fentities%2Fdownload%2Fentities-2.2.0.tgz"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@~1.1.1:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/entities/-/entities-1.1.2.tgz"
  integrity sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==

envify@^3.4.0:
  version "3.4.1"
  resolved "https://registry.npmmirror.com/envify/-/envify-3.4.1.tgz"
  integrity sha512-XLiBFsLtNF0MOZl+vWU59yPb3C2JtrQY2CNJn22KH75zPlHWY5ChcAQuf4knJeWT/lLkrx3sqvhP/J349bt4Bw==
  dependencies:
    jstransform "^11.0.3"
    through "~2.3.4"

errno@^0.1.1, errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/errno/download/errno-0.1.8.tgz"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.6.tgz"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.17.2, es-abstract@^1.19.0, es-abstract@^1.19.1:
  version "1.19.1"
  resolved "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.19.1.tgz?cache=0&sync_timestamp=1633234313248&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fes-abstract%2Fdownload%2Fes-abstract-1.19.1.tgz"
  integrity sha1-1IhXlodpFpWd547aoN9FZicRXsM=
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    get-intrinsic "^1.1.1"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-symbols "^1.0.2"
    internal-slot "^1.0.3"
    is-callable "^1.2.4"
    is-negative-zero "^2.0.1"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.1"
    is-string "^1.0.7"
    is-weakref "^1.0.1"
    object-inspect "^1.11.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    string.prototype.trimend "^1.0.4"
    string.prototype.trimstart "^1.0.4"
    unbox-primitive "^1.0.1"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.46, es5-ext@^0.10.50, es5-ext@^0.10.62, es5-ext@~0.10.14:
  version "0.10.64"
  resolved "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.64.tgz"
  integrity sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    esniff "^2.0.1"
    next-tick "^1.1.0"

es5-shim@^4.5.1:
  version "4.6.7"
  resolved "https://registry.npmmirror.com/es5-shim/-/es5-shim-4.6.7.tgz"
  integrity sha512-jg21/dmlrNQI7JyyA2w7n+yifSxBng0ZralnSfVZjoCawgNTCnS+yBCyVM9DL5itm7SUnDGgv7hcq2XCZX4iRQ==

es6-iterator@^2.0.3, es6-iterator@~2.0.1:
  version "2.0.3"
  resolved "https://registry.nlark.com/es6-iterator/download/es6-iterator-2.0.3.tgz"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "https://registry.nlark.com/es6-map/download/es6-map-0.1.5.tgz"
  integrity sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA=
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-promise@^4.2.8:
  version "4.2.8"
  resolved "https://registry.npmmirror.com/es6-promise/-/es6-promise-4.2.8.tgz"
  integrity sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==

es6-set@~0.1.5:
  version "0.1.5"
  resolved "https://registry.npm.taobao.org/es6-set/download/es6-set-0.1.5.tgz"
  integrity sha1-0rPsXU2ADO2BjbU40ol02wpzzLE=
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@^3.1.1, es6-symbol@^3.1.3, es6-symbol@~3.1.1:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/es6-symbol/download/es6-symbol-3.1.3.tgz"
  integrity sha1-utXTwbzawoJp9MszHkMceKxwXRg=
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

es6-symbol@3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/es6-symbol/download/es6-symbol-3.1.1.tgz"
  integrity sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-weak-map@^2.0.1:
  version "2.0.3"
  resolved "https://registry.nlark.com/es6-weak-map/download/es6-weak-map-2.0.3.tgz"
  integrity sha1-ttofFswswNm+Q+a9v8Xn383zHVM=
  dependencies:
    d "1"
    es5-ext "^0.10.46"
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.1"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/escalade/download/escalade-3.1.1.tgz"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.0, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5, escape-string-regexp@1.0.5:
  version "1.0.5"
  resolved "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escodegen@^1.9.1, escodegen@1.x.x:
  version "1.14.3"
  resolved "https://registry.npm.taobao.org/escodegen/download/escodegen-1.14.3.tgz"
  integrity sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

escope@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/escope/download/escope-3.6.0.tgz"
  integrity sha1-4Bl16BJ4GhY6ba392AOY3GTIicM=
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-config-standard@^10.2.1:
  version "10.2.1"
  resolved "https://registry.nlark.com/eslint-config-standard/download/eslint-config-standard-10.2.1.tgz?cache=0&sync_timestamp=1621877616191&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-config-standard%2Fdownload%2Feslint-config-standard-10.2.1.tgz"
  integrity sha1-wGHk0GbzedwXzVYsZOgZtN1FRZE=

eslint-friendly-formatter@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/eslint-friendly-formatter/download/eslint-friendly-formatter-3.0.0.tgz"
  integrity sha1-J4h0Q1psRuwdlPoLH/SU4w7wQpA=
  dependencies:
    chalk "^1.0.0"
    coalescy "1.0.0"
    extend "^3.0.0"
    minimist "^1.2.0"
    text-table "^0.2.0"

eslint-import-resolver-node@^0.3.6:
  version "0.3.6"
  resolved "https://registry.nlark.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.6.tgz?cache=0&sync_timestamp=1629046536777&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-import-resolver-node%2Fdownload%2Feslint-import-resolver-node-0.3.6.tgz"
  integrity sha1-QEi5WDldqJZoJSAB29nsprg7rL0=
  dependencies:
    debug "^3.2.7"
    resolve "^1.20.0"

eslint-loader@^1.7.1:
  version "1.9.0"
  resolved "https://registry.npmmirror.com/eslint-loader/download/eslint-loader-1.9.0.tgz"
  integrity sha1-fhvp/t3KMo09z67xrUnVvv/oOhM=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-module-utils@^2.7.1:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/eslint-module-utils/download/eslint-module-utils-2.7.1.tgz?cache=0&sync_timestamp=1634151608499&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-module-utils%2Fdownload%2Feslint-module-utils-2.7.1.tgz"
  integrity sha1-tDUAHJ+N1Kt/bQ78rkuWltTCS3w=
  dependencies:
    debug "^3.2.7"
    find-up "^2.1.0"
    pkg-dir "^2.0.0"

eslint-plugin-import@^2.7.0:
  version "2.25.3"
  resolved "https://registry.npmmirror.com/eslint-plugin-import/download/eslint-plugin-import-2.25.3.tgz?cache=0&sync_timestamp=1636529216632&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-import%2Fdownload%2Feslint-plugin-import-2.25.3.tgz"
  integrity sha512-RzAVbby+72IB3iOEL8clzPLzL3wpDrlwjsTBAQXgyp5SeTqqY+0bFubwuo+y/HLhNZcXV4XqTBO4LGsfyHIDXg==
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flat "^1.2.5"
    debug "^2.6.9"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-module-utils "^2.7.1"
    has "^1.0.3"
    is-core-module "^2.8.0"
    is-glob "^4.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.5"
    resolve "^1.20.0"
    tsconfig-paths "^3.11.0"

eslint-plugin-node@^5.2.0:
  version "5.2.1"
  resolved "https://registry.nlark.com/eslint-plugin-node/download/eslint-plugin-node-5.2.1.tgz"
  integrity sha1-gN8yU8TXkBBF7If6ZgooTjK9yik=
  dependencies:
    ignore "^3.3.6"
    minimatch "^3.0.4"
    resolve "^1.3.3"
    semver "5.3.0"

eslint-plugin-promise@^3.4.0:
  version "3.8.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-promise/download/eslint-plugin-promise-3.8.0.tgz"
  integrity sha1-ZevyeoRePB6db2pWIt3TgBaUtiE=

eslint-plugin-standard@^3.0.1:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-standard/download/eslint-plugin-standard-3.1.0.tgz"
  integrity sha1-Kp4hJZukxHwC1TstDJE11LECLUc=

eslint-plugin-vue@^4.0.0:
  version "4.7.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-vue/download/eslint-plugin-vue-4.7.1.tgz"
  integrity sha1-yCm5/GJYLBiXtaC5Sv1E7MpRHmM=
  dependencies:
    vue-eslint-parser "^2.0.3"

eslint-scope@^3.7.1, eslint-scope@3.7.1:
  version "3.7.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-3.7.1.tgz?cache=0&sync_timestamp=1637466831846&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-3.7.1.tgz"
  integrity sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-visitor-keys@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint@^4.15.0:
  version "4.19.1"
  resolved "https://registry.npmmirror.com/eslint/download/eslint-4.19.1.tgz"
  integrity sha1-MtHWU+HZBAiFS/spbwdux+GGowA=
  dependencies:
    ajv "^5.3.0"
    babel-code-frame "^6.22.0"
    chalk "^2.1.0"
    concat-stream "^1.6.0"
    cross-spawn "^5.1.0"
    debug "^3.1.0"
    doctrine "^2.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.4"
    esquery "^1.0.0"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.0.1"
    ignore "^3.3.3"
    imurmurhash "^0.1.4"
    inquirer "^3.0.6"
    is-resolvable "^1.0.0"
    js-yaml "^3.9.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.4"
    minimatch "^3.0.2"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    pluralize "^7.0.0"
    progress "^2.0.0"
    regexpp "^1.0.1"
    require-uncached "^1.0.3"
    semver "^5.3.0"
    strip-ansi "^4.0.0"
    strip-json-comments "~2.0.1"
    table "4.0.2"
    text-table "~0.2.0"

esniff@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/esniff/-/esniff-2.0.1.tgz"
  integrity sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.62"
    event-emitter "^0.3.5"
    type "^2.7.2"

espree@^3.5.2, espree@^3.5.4:
  version "3.5.4"
  resolved "https://registry.npmmirror.com/espree/download/espree-3.5.4.tgz"
  integrity sha1-sPRHGHyKi+2US4FaZgvd9d610ac=
  dependencies:
    acorn "^5.5.0"
    acorn-jsx "^3.0.0"

esprima-fb@^15001.1.0-dev-harmony-fb:
  version "15001.1.0-dev-harmony-fb"
  resolved "https://registry.npmmirror.com/esprima-fb/-/esprima-fb-15001.1.0-dev-harmony-fb.tgz"
  integrity sha512-59dDGQo2b3M/JfKIws0/z8dcXH2mnVHkfSPRhCYS91JNGfGNwr7GsSF6qzWZuOGvw5Ii0w9TtylrX07MGmlOoQ==

esprima@^2.6.0:
  version "2.7.3"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-2.7.3.tgz"
  integrity sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE=

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esprima@~1.2.2:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/esprima/-/esprima-1.2.5.tgz"
  integrity sha512-S9VbPDU0adFErpDai3qDkjq8+G05ONtKzcyNrPKg/ZKa+tf879nX2KexNU95b31UoTJjRLInNBHHHjFPoCd7lQ==

esprima@~3.1.0:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/esprima/-/esprima-3.1.3.tgz"
  integrity sha512-AWwVMNxwhN8+NIPQzAQZCm7RkLC4RbM3B1OobMuyp3i+w73X57KCKaVIxaRZb+DYCojq7rspo+fmuQfAboyhFg==

esprima@3.x.x:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-3.1.3.tgz"
  integrity sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM=

esquery@^1.0.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/esquery/download/esquery-1.4.0.tgz"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237706876&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237706876&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237706876&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.nlark.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

ev-emitter@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/ev-emitter/-/ev-emitter-1.1.1.tgz"
  integrity sha512-ipiDYhdQSCZ4hSbX4rMW+XzNKMD1prg/sTvoVmSLkuQ1MVlwjJQQA+sW8tMYR3BLUr9KjodFV4pvzunvRhd33Q==

event-emitter@^0.3.5, event-emitter@~0.3.5:
  version "0.3.5"
  resolved "https://registry.npm.taobao.org/event-emitter/download/event-emitter-0.3.5.tgz"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter3@^4.0.0, eventemitter3@^4.0.7:
  version "4.0.7"
  resolved "https://registry.npm.taobao.org/eventemitter3/download/eventemitter3-4.0.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventemitter3%2Fdownload%2Feventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/events/download/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

eventsource@0.1.6:
  version "0.1.6"
  resolved "https://registry.npm.taobao.org/eventsource/download/eventsource-0.1.6.tgz?cache=0&sync_timestamp=1616041710425&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventsource%2Fdownload%2Feventsource-0.1.6.tgz"
  integrity sha1-Cs7ehJ7X3RzMMsgRuxG5RNTykjI=
  dependencies:
    original ">=0.0.5"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

exec-sh@^0.2.0:
  version "0.2.2"
  resolved "https://registry.npm.taobao.org/exec-sh/download/exec-sh-0.2.2.tgz?cache=0&sync_timestamp=1616788824594&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexec-sh%2Fdownload%2Fexec-sh-0.2.2.tgz"
  integrity sha1-Kl5//L19C6J1W97LFuWkJ9+97DY=
  dependencies:
    merge "^1.2.0"

execa@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-0.7.0.tgz?cache=0&sync_timestamp=1637147262608&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-0.7.0.tgz"
  integrity sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exit-on-epipe@~1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/exit-on-epipe/download/exit-on-epipe-1.0.1.tgz"
  integrity sha1-C92S6H1ShdJn2qgXHQ6wYVlolpI=

exit@^0.1.2, exit@0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/exit/download/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-0.1.5.tgz"
  integrity sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://registry.nlark.com/expand-range/download/expand-range-1.8.2.tgz"
  integrity sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=
  dependencies:
    fill-range "^2.1.0"

expect@^22.4.0:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/expect/download/expect-22.4.3.tgz"
  integrity sha1-1aKdCg4fshU1V8rvJnTUVH6RRnQ=
  dependencies:
    ansi-styles "^3.2.0"
    jest-diff "^22.4.3"
    jest-get-type "^22.4.3"
    jest-matcher-utils "^22.4.3"
    jest-message-util "^22.4.3"
    jest-regex-util "^22.4.3"

express@^4.14.0, express@^4.16.2:
  version "4.18.3"
  resolved "https://registry.npmjs.org/express/-/express-4.18.3.tgz"
  integrity sha512-6VyCijWQ+9O7WuVMTRBTl+cjNNIzD5cY5mQ1WM8r/LEkI2u8EYpOotESNwzNlyCn3g+dmjKYI6BmNneSr/FSRw==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.2"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext@^1.1.2:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/ext/download/ext-1.6.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fext%2Fdownload%2Fext-1.6.0.tgz"
  integrity sha1-OHHVBkHodMwXLitT+RmELRnbTFI=
  dependencies:
    type "^2.5.0"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.0, extend@~3.0.0, extend@~3.0.2, extend@3:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend/download/extend-3.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fextend%2Fdownload%2Fextend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^2.0.4:
  version "2.2.0"
  resolved "https://registry.nlark.com/external-editor/download/external-editor-2.2.0.tgz"
  integrity sha1-BFURz9jRM/OEZnPRBHwVTiFK09U=
  dependencies:
    chardet "^0.4.0"
    iconv-lite "^0.4.17"
    tmp "^0.0.33"

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://registry.nlark.com/extglob/download/extglob-0.3.2.tgz"
  integrity sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.2, extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-text-webpack-plugin@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/extract-text-webpack-plugin/download/extract-text-webpack-plugin-3.0.2.tgz"
  integrity sha1-XwQ+qgL5dQqSWLeMCm4NwUCPsvc=
  dependencies:
    async "^2.4.1"
    loader-utils "^1.1.0"
    schema-utils "^0.3.0"
    webpack-sources "^1.0.1"

extract-zip@^1.6.7:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/extract-zip/download/extract-zip-1.7.0.tgz"
  integrity sha1-VWzDrp339FLEk6DPtRzDAneUCSc=
  dependencies:
    concat-stream "^1.6.2"
    debug "^2.6.9"
    mkdirp "^0.5.4"
    yauzl "^2.10.0"

extsprintf@^1.2.0, extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.3.0.tgz?cache=0&sync_timestamp=1635889740043&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fextsprintf%2Fdownload%2Fextsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz"
  integrity sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastparse@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/fastparse/download/fastparse-1.1.2.tgz"
  integrity sha1-kXKMWllC7O2FMSg8eUQe5BIsNak=

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "https://registry.nlark.com/faye-websocket/download/faye-websocket-0.10.0.tgz"
  integrity sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.0:
  version "0.11.4"
  resolved "https://registry.nlark.com/faye-websocket/download/faye-websocket-0.11.4.tgz"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/fb-watchman/download/fb-watchman-2.0.1.tgz"
  integrity sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=
  dependencies:
    bser "2.1.1"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/fd-slicer/download/fd-slicer-1.1.0.tgz"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/figures/download/figures-2.0.0.tgz?cache=0&sync_timestamp=1625254307578&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-2.0.0.tgz"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-2.0.0.tgz?cache=0&sync_timestamp=1613794357372&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffile-entry-cache%2Fdownload%2Ffile-entry-cache-2.0.0.tgz"
  integrity sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

file-loader@^1.1.4:
  version "1.1.11"
  resolved "https://registry.nlark.com/file-loader/download/file-loader-1.1.11.tgz"
  integrity sha1-b+iGRJsPKpNuQ8q6rAzb+zaVBvg=
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^0.4.5"

file-uri-to-path@1:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/filename-regex/download/filename-regex-2.0.1.tgz"
  integrity sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=

fileset@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/fileset/download/fileset-2.0.3.tgz"
  integrity sha1-jnVIqW08wjJ+5eZ0FocjozO7oqA=
  dependencies:
    glob "^7.0.3"
    minimatch "^3.0.3"

filesize@^3.5.11:
  version "3.6.1"
  resolved "https://registry.npmmirror.com/filesize/download/filesize-3.6.1.tgz?cache=0&sync_timestamp=1635764078375&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffilesize%2Fdownload%2Ffilesize-3.6.1.tgz"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^2.1.0:
  version "2.2.4"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-2.2.4.tgz"
  integrity sha1-6x53OrsFbc2N8r/favWbizqTZWU=
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-babel-config@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/find-babel-config/download/find-babel-config-1.2.0.tgz"
  integrity sha1-qbezF+tbmGDNqdVHQKjIM3oig6I=
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-0.1.1.tgz?cache=0&sync_timestamp=1630260009898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-0.1.1.tgz"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-1.0.0.tgz?cache=0&sync_timestamp=1630260009898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffind-cache-dir%2Fdownload%2Ffind-cache-dir-1.0.0.tgz"
  integrity sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=
  dependencies:
    commondir "^1.0.1"
    make-dir "^1.0.0"
    pkg-dir "^2.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-1.1.2.tgz"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

flat-cache@^1.2.1:
  version "1.3.4"
  resolved "https://registry.nlark.com/flat-cache/download/flat-cache-1.3.4.tgz"
  integrity sha1-LC73dSXMKSkAff/6HdMUqpyd7m8=
  dependencies:
    circular-json "^0.3.1"
    graceful-fs "^4.1.2"
    rimraf "~2.6.2"
    write "^0.2.1"

flatten@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/flatten/download/flatten-1.0.3.tgz"
  integrity sha1-wSg6yfJ7Noq8HjbR/3sEUBowNWs=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

flv.js@^1.6.2:
  version "1.6.2"
  resolved "https://registry.npmmirror.com/flv.js/-/flv.js-1.6.2.tgz"
  integrity sha512-xre4gUbX1MPtgQRKj2pxJENp/RnaHaxYvy3YToVVCrSmAWUu85b9mug6pTXF6zakUjNP2lFWZ1rkSX7gxhB/2A==
  dependencies:
    es6-promise "^4.2.8"
    webworkify-webpack "^2.1.5"

follow-redirects@^1.0.0, follow-redirects@^1.14.0:
  version "1.15.6"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

fontfaceobserver@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/fontfaceobserver/-/fontfaceobserver-2.3.0.tgz"
  integrity sha512-6FPvD/IVyT4ZlNe7Wcn5Fb/4ChigpucKYSvD6a+0iMoLn2inpo711eyIcKjmDtE5XNcgAkSH9uN/nfAeZzHEfg==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.nlark.com/for-each/download/for-each-0.3.3.tgz"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

for-own@^0.1.4:
  version "0.1.5"
  resolved "https://registry.nlark.com/for-own/download/for-own-0.1.5.tgz"
  integrity sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=
  dependencies:
    for-in "^1.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/forwarded/download/forwarded-0.2.0.tgz?cache=0&sync_timestamp=1622503408398&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fforwarded%2Fdownload%2Fforwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

frac@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/frac/download/frac-1.1.2.tgz"
  integrity sha1-PXT39keMiKG1AgMG10fcYxPHTQs=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

friendly-errors-webpack-plugin@^1.6.1:
  version "1.7.0"
  resolved "https://registry.nlark.com/friendly-errors-webpack-plugin/download/friendly-errors-webpack-plugin-1.7.0.tgz"
  integrity sha1-78hsu4FiJFZYYaG+ep2E0Kr+oTY=
  dependencies:
    chalk "^1.1.3"
    error-stack-parser "^2.0.0"
    string-width "^2.0.0"

from2@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-extra@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-5.0.0.tgz"
  integrity sha512-66Pm4RYbjzdyeuqudYqhFiNBbCIuI9kgRqLPSHIlXHidW8NIQtVdkM1yeZ4lXwuhbTETv3EUGMNHAAw6hiundQ==
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@~0.16.3:
  version "0.16.5"
  resolved "https://registry.nlark.com/fs-extra/download/fs-extra-0.16.5.tgz"
  integrity sha1-GtZh+myGyWCM0bSe/G/Og0k5p1A=
  dependencies:
    graceful-fs "^3.0.5"
    jsonfile "^2.0.0"
    rimraf "^2.2.8"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

ftp@~0.3.10:
  version "0.3.10"
  resolved "https://registry.npm.taobao.org/ftp/download/ftp-0.3.10.tgz"
  integrity sha1-kZfYYa2BQvPmPVqDv+TFn3MwiF0=
  dependencies:
    readable-stream "1.1.x"
    xregexp "2.0.0"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz?cache=0&sync_timestamp=1618847182644&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffunctional-red-black-tree%2Fdownload%2Ffunctional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

generate-source-map@0.0.5:
  version "0.0.5"
  resolved "https://registry.npmmirror.com/generate-source-map/-/generate-source-map-0.0.5.tgz"
  integrity sha512-jqiE7f3FEaeMcjnMSEYLjMa39bdx+RrrdfhxdJpMm9S/8IugHF4vLQLZ9sxHylWyxpsBILukC/l/7B0/O0zhNg==
  dependencies:
    esprima "~1.2.2"
    source-map "~0.1.34"

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.3.tgz"
  integrity sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz"
  integrity sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-intrinsic@^1.1.3:
  version "1.2.4"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-size@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/get-size/-/get-size-2.0.3.tgz"
  integrity sha512-lXNzT/h/dTjTxRbm9BXb+SGxxzkm97h/PCIKtlN/CBCxxmkkIVV21udumMS93MuVTDX583gqc94v3RjuHmI+2Q==

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/get-stdin/download/get-stdin-4.0.1.tgz"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

get-uri@2:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/get-uri/download/get-uri-2.0.4.tgz"
  integrity sha1-1JN6uBniGNTLWuGOT1livvFpzGo=
  dependencies:
    data-uri-to-buffer "1"
    debug "2"
    extend "~3.0.2"
    file-uri-to-path "1"
    ftp "~0.3.10"
    readable-stream "2"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.nlark.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/glob-base/download/glob-base-0.3.0.tgz"
  integrity sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-2.0.0.tgz?cache=0&sync_timestamp=1632953697891&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-2.0.0.tgz"
  integrity sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz?cache=0&sync_timestamp=1632953697891&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-3.1.0.tgz"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob@^5.0.15:
  version "5.0.15"
  resolved "https://registry.npmmirror.com/glob/-/glob-5.0.15.tgz"
  integrity sha512-c9IPMazfRITpmAAKi22dK1VKxGDX9ehhqfABDriL/lzO92xcUKEJPQHrVA/2YHSNFB4iFlykVmWvwo48nr3OxA==
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.6:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz"
  integrity sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@7.0.5:
  version "7.0.5"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.0.5.tgz"
  integrity sha1-tCAqaQmbu00pKnwblbZoK2fr3JU=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global@^4.3.0, global@^4.3.1, global@~4.3.0, global@4.3.2:
  version "4.3.2"
  resolved "https://registry.npmmirror.com/global/-/global-4.3.2.tgz"
  integrity sha512-/4AybdwIDU4HkCUbJkZdWpe4P6vuw/CUtu+0I1YlLIPe7OlUO7KNJ+q/rO70CW2/NW6Jc6I62++Hzsf5Alu6rQ==
  dependencies:
    min-document "^2.19.0"
    process "~0.5.1"

global@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmmirror.com/global/-/global-4.4.0.tgz"
  integrity sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

global@~4.4.0:
  version "4.4.0"
  resolved "https://registry.npmmirror.com/global/-/global-4.4.0.tgz"
  integrity sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

globals@^11.0.1:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^9.18.0:
  version "9.18.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-9.18.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-9.18.0.tgz"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.nlark.com/globby/download/globby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://registry.nlark.com/globby/download/globby-7.1.1.tgz"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

google-maps@^4.3.3:
  version "4.3.3"
  resolved "https://registry.npmmirror.com/google-maps/-/google-maps-4.3.3.tgz"
  integrity sha512-MQbEgBNQbGyV7mfS2tlFgW4EoGKLia24BvAl4a+kgsYWt4283kyPpaay/yKIsScQLr7nSUONaLNfOdMsCuJDEw==
  dependencies:
    "@types/googlemaps" "^3.39.1"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^3.0.5:
  version "3.0.12"
  resolved "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-3.0.12.tgz"
  integrity sha1-ADSUfOntaV7IqwuFS8kZ6Csf+u8=
  dependencies:
    natives "^1.1.3"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6:
  version "4.2.8"
  resolved "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz"
  integrity sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo=

"graceful-readlink@>= 1.0.0":
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/graceful-readlink/download/graceful-readlink-1.0.1.tgz"
  integrity sha1-TK+tdrxi8C+gObL5Tpo906ORpyU=

growl@1.9.2:
  version "1.9.2"
  resolved "https://registry.npm.taobao.org/growl/download/growl-1.9.2.tgz"
  integrity sha1-Dqd0NxXbjY3ixe3hd14bRayFwC8=

growly@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/growly/download/growly-1.3.0.tgz"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

gzip-size@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/gzip-size/download/gzip-size-4.1.0.tgz"
  integrity sha1-iuCWJX6r59acRb4rZ8RIEk/7UXw=
  dependencies:
    duplexer "^0.1.1"
    pify "^3.0.0"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/handle-thing/download/handle-thing-2.0.1.tgz"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

handlebars@^4.0.3:
  version "4.7.7"
  resolved "https://registry.npm.taobao.org/handlebars/download/handlebars-4.7.7.tgz?cache=0&sync_timestamp=1613385269284&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhandlebars%2Fdownload%2Fhandlebars-4.7.7.tgz"
  integrity sha1-nOM0FqrQLb1sj6+oJA1dmABJRaE=
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.0"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npmmirror.com/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/has-ansi/download/has-ansi-0.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-ansi%2Fdownload%2Fhas-ansi-0.1.0.tgz"
  integrity sha1-hPJlqujA5qiKEtcCKJS3VoiUxi4=
  dependencies:
    ansi-regex "^0.2.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/has-ansi/download/has-ansi-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-ansi%2Fdownload%2Fhas-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/has-bigints/download/has-bigints-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhas-bigints%2Fdownload%2Fhas-bigints-1.0.1.tgz"
  integrity sha1-ZP5qywIGc+O3jbA1pa9pqp0HsRM=

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-1.0.0.tgz"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-2.0.0.tgz"
  integrity sha1-6CB68cx7MNRGzHC3NLXovhj4jVE=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.3.tgz"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/has-symbols/download/has-symbols-1.0.2.tgz"
  integrity sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz?cache=0&sync_timestamp=1628196402801&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-tostringtag%2Fdownload%2Fhas-tostringtag-1.0.0.tgz"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.1, has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/has/download/has-1.0.3.tgz"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/hash-base/download/hash-base-3.1.0.tgz"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-base@~3.0:
  version "3.0.4"
  resolved "https://registry.npmjs.org/hash-base/-/hash-base-3.0.4.tgz"
  integrity sha512-EeeoJKjTyt868liAlVmcv2ZsUfGHlE3Q+BICOXcZiwN3osr5Q/zFGYmTJpoIzuaSTAwndFy+GqhEwlU4L3j4Ow==
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://registry.npm.taobao.org/hash.js/download/hash.js-1.1.7.tgz"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

he@^1.1.0, he@^1.1.1, he@1.2.x:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/he/download/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/home-or-tmp/download/home-or-tmp-2.0.0.tgz?cache=0&sync_timestamp=1618599587413&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhome-or-tmp%2Fdownload%2Fhome-or-tmp-2.0.0.tgz"
  integrity sha1-42w/LSyufXRqhX440Y1fMqeILbg=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/hsl-regex/download/hsl-regex-1.0.0.tgz"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/html-comment-regex/download/html-comment-regex-1.1.2.tgz"
  integrity sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=

html-encoding-sniffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz?cache=0&sync_timestamp=1632006268441&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhtml-encoding-sniffer%2Fdownload%2Fhtml-encoding-sniffer-1.0.2.tgz"
  integrity sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=
  dependencies:
    whatwg-encoding "^1.0.1"

html-entities@^1.2.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/html-entities/download/html-entities-1.4.0.tgz"
  integrity sha1-z70bAdKvr5rcobEK59/6uYxx0tw=

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/html-tags/download/html-tags-2.0.0.tgz"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-void-elements@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/html-void-elements/-/html-void-elements-2.0.1.tgz"
  integrity sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A==

html-webpack-plugin@^2.30.1:
  version "2.30.1"
  resolved "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-2.30.1.tgz"
  integrity sha1-f5xCG36pHsRg9WUn1430hO51N9U=
  dependencies:
    bluebird "^3.4.7"
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    toposort "^1.0.0"

htmlparser2@^3.8.3:
  version "3.10.1"
  resolved "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-3.10.1.tgz?cache=0&sync_timestamp=1636640940074&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-3.10.1.tgz"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-6.1.0.tgz?cache=0&sync_timestamp=1636640940074&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-6.1.0.tgz"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.nlark.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1636932154238&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1636932154238&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-parser-js@>=0.5.1:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/http-parser-js/download/http-parser-js-0.5.5.tgz"
  integrity sha512-x+JVEkO2PoM8qqpbPbOL3cqHPwerep7OwzK7Ay+sMQjKzaKCqWvjoXm5tqMP9tXWWTnTzAjIhXg+J99XYuPhPA==

http-proxy-agent@1:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/http-proxy-agent/download/http-proxy-agent-1.0.0.tgz"
  integrity sha1-zBzjjkU7+YSg93AtLdWcc9CBKEo=
  dependencies:
    agent-base "2"
    debug "2"
    extend "3"

http-proxy-middleware@^0.19.1:
  version "0.19.2"
  resolved "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-0.19.2.tgz"
  integrity sha1-7nPcyDSBZa/v6N4v9xd1HRgWCO4=
  dependencies:
    http-proxy "^1.18.1"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.nlark.com/http-proxy/download/http-proxy-1.18.1.tgz?cache=0&sync_timestamp=1618847045732&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhttp-proxy%2Fdownload%2Fhttp-proxy-1.18.1.tgz"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1637178599208&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/https-browserify/download/https-browserify-1.0.0.tgz"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

https-proxy-agent@1:
  version "1.0.0"
  resolved "https://registry.nlark.com/https-proxy-agent/download/https-proxy-agent-1.0.0.tgz"
  integrity sha1-NffabEjOTdv6JkiRrFk+5f+GceY=
  dependencies:
    agent-base "2"
    debug "2"
    extend "3"

i18next@^20.4.0:
  version "20.6.1"
  resolved "https://registry.npmjs.org/i18next/-/i18next-20.6.1.tgz"
  integrity sha512-yCMYTMEJ9ihCwEQQ3phLo7I/Pwycf8uAx+sRHwwk5U9Aui/IZYgQRyMqXafQOw5QQ7DM1Z+WyEXWIqSuJHhG2A==
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@^0.4.17, iconv-lite@^0.4.4, iconv-lite@^0.4.5, iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz"
  integrity sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=

icss-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/icss-utils/download/icss-utils-2.1.0.tgz?cache=0&sync_timestamp=1605801291394&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficss-utils%2Fdownload%2Ficss-utils-2.1.0.tgz"
  integrity sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=
  dependencies:
    postcss "^6.0.1"

ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.nlark.com/ieee754/download/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.3, ignore@^3.3.5, ignore@^3.3.6:
  version "3.3.10"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz?cache=0&sync_timestamp=1635926632542&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-3.3.10.tgz"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

image-size@^0.5.1, image-size@~0.5.0:
  version "0.5.5"
  resolved "https://registry.npm.taobao.org/image-size/download/image-size-0.5.5.tgz?cache=0&sync_timestamp=1618422657851&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimage-size%2Fdownload%2Fimage-size-0.5.5.tgz"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immer@^9.0.6:
  version "9.0.15"
  resolved "https://registry.npmjs.org/immer/-/immer-9.0.15.tgz"
  integrity sha512-2eB/sswms9AEUSkOm4SbV5Y7Vmt/bKRwByd52jfLkW4OLYeaTP3EEiJ9agqU0O/tq6Dk62Zfj+TJSqfm1rLVGQ==

immutable@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/immutable/-/immutable-4.1.0.tgz"
  integrity sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/import-cwd/download/import-cwd-2.1.0.tgz?cache=0&sync_timestamp=1618846826220&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fimport-cwd%2Fdownload%2Fimport-cwd-2.1.0.tgz"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/import-fresh/download/import-fresh-2.0.0.tgz?cache=0&sync_timestamp=1608469561643&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-2.0.0.tgz"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/import-from/download/import-from-2.1.0.tgz"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/import-local/download/import-local-1.0.0.tgz?cache=0&sync_timestamp=1633327317807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fimport-local%2Fdownload%2Fimport-local-1.0.0.tgz"
  integrity sha1-Xk/9wD9P5sAJxnKb6yljHC+CJ7w=
  dependencies:
    pkg-dir "^2.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/indent-string/download/indent-string-2.1.0.tgz?cache=0&sync_timestamp=1618679561483&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Findent-string%2Fdownload%2Findent-string-2.1.0.tgz"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

individual@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/individual/-/individual-2.0.0.tgz"
  integrity sha512-pWt8hBCqJsUWI/HtcfWod7+N9SgAqyPEaF7JQjwzjn5vGrpg6aQ5qeAFQ7dx//UH4J1O+7xqew+gCeeFt6xN/g==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.1.tgz"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4:
  version "1.3.8"
  resolved "https://registry.nlark.com/ini/download/ini-1.3.8.tgz"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@^3.0.6:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/inquirer/download/inquirer-3.3.0.tgz?cache=0&sync_timestamp=1633472964335&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Finquirer%2Fdownload%2Finquirer-3.3.0.tgz"
  integrity sha1-ndLyrXZdyrH/BEO0kUQqILoifck=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

internal-ip@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/internal-ip/download/internal-ip-1.2.0.tgz?cache=0&sync_timestamp=1634404775654&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Finternal-ip%2Fdownload%2Finternal-ip-1.2.0.tgz"
  integrity sha1-rp+/k7mEh4eF1QqN4bNWlWBYz1w=
  dependencies:
    meow "^3.3.0"

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/internal-slot/download/internal-slot-1.0.3.tgz"
  integrity sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/interpret/download/interpret-1.4.0.tgz"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

invariant@^2.2.0, invariant@^2.2.2:
  version "2.2.4"
  resolved "https://registry.npm.taobao.org/invariant/download/invariant-2.2.4.tgz"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/invert-kv/download/invert-kv-1.0.0.tgz?cache=0&sync_timestamp=1630996775723&other_urls=https%3A%2F%2Fregistry.nlark.com%2Finvert-kv%2Fdownload%2Finvert-kv-1.0.0.tgz"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

ip-regex@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ip-regex/download/ip-regex-4.3.0.tgz"
  integrity sha1-aHJ1qw9X+naXj/j03dyKI9WZDbU=

ip@^1.1.0, ip@^1.1.4, ip@^1.1.5:
  version "1.1.9"
  resolved "https://registry.npmjs.org/ip/-/ip-1.1.9.tgz"
  integrity sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ==

ip@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/ip/download/ip-1.0.1.tgz"
  integrity sha1-x+NWzeoiWucbNtcPLnGpK6TkJZA=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz?cache=0&sync_timestamp=1628691761253&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-2.1.0.tgz"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.nlark.com/is-arguments/download/is-arguments-1.1.1.tgz?cache=0&sync_timestamp=1628201919104&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-arguments%2Fdownload%2Fis-arguments-1.1.1.tgz"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.nlark.com/is-bigint/download/is-bigint-1.0.4.tgz?cache=0&sync_timestamp=1628747504782&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-bigint%2Fdownload%2Fis-bigint-1.0.4.tgz"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.nlark.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.4:
  version "1.2.4"
  resolved "https://registry.nlark.com/is-callable/download/is-callable-1.2.4.tgz"
  integrity sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/is-ci/download/is-ci-1.2.1.tgz?cache=0&sync_timestamp=1635261061017&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-1.2.1.tgz"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-color-stop/download/is-color-stop-1.1.0.tgz"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.2.0, is-core-module@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-core-module%2Fdownload%2Fis-core-module-2.8.0.tgz"
  integrity sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.nlark.com/is-date-object/download/is-date-object-1.0.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-date-object%2Fdownload%2Fis-date-object-1.0.5.tgz"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/is-dotfile/download/is-dotfile-1.0.3.tgz"
  integrity sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://registry.nlark.com/is-equal-shallow/download/is-equal-shallow-0.1.3.tgz"
  integrity sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-extglob/download/is-extglob-1.0.0.tgz"
  integrity sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-finite/download/is-finite-1.1.0.tgz"
  integrity sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz?cache=0&sync_timestamp=1618552489864&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-fullwidth-code-point%2Fdownload%2Fis-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz?cache=0&sync_timestamp=1618552489864&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-fullwidth-code-point%2Fdownload%2Fis-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-function@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-function/-/is-function-1.0.2.tgz"
  integrity sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==

is-generator-fn@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-generator-fn/download/is-generator-fn-1.0.0.tgz?cache=0&sync_timestamp=1628686390261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-generator-fn%2Fdownload%2Fis-generator-fn-1.0.0.tgz"
  integrity sha1-lp1J4bszKfa7fwkIm+JleLLd1Go=

is-glob@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-2.0.1.tgz"
  integrity sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=
  dependencies:
    is-extglob "^1.0.0"

is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-2.0.1.tgz"
  integrity sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hotkey@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/is-hotkey/-/is-hotkey-0.2.0.tgz"
  integrity sha512-UknnZK4RakDmTgz4PI1wIph5yxSs/mvChWs9ifnlXsKuXgWmOkY/hAE0H/k2MIqH0RlRye0i1oC07MCRSD28Mw==

is-negative-zero@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/is-negative-zero/download/is-negative-zero-2.0.2.tgz"
  integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==

is-number-object@^1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/is-number-object/download/is-number-object-1.0.6.tgz"
  integrity sha1-anqvg4x/BoalC0VT9+VKlklOifA=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-2.1.0.tgz"
  integrity sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-4.0.0.tgz"
  integrity sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8=

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-obj/download/is-obj-2.0.0.tgz"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz?cache=0&sync_timestamp=1628686555851&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-cwd%2Fdownload%2Fis-path-cwd-1.0.0.tgz"
  integrity sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz?cache=0&sync_timestamp=1620047110449&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-in-cwd%2Fdownload%2Fis-path-in-cwd-1.0.1.tgz"
  integrity sha1-WsSLNF72dTOb1sekipEhELJBz1I=
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-path-inside/download/is-path-inside-1.0.1.tgz?cache=0&sync_timestamp=1620046845369&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-inside%2Fdownload%2Fis-path-inside-1.0.1.tgz"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-plain-obj@^1.0.0, is-plain-obj@^1.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz?cache=0&sync_timestamp=1618600489644&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-plain-obj%2Fdownload%2Fis-plain-obj-1.1.0.tgz"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-5.0.0.tgz"
  integrity sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/is-posix-bracket/download/is-posix-bracket-0.1.1.tgz"
  integrity sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-primitive/download/is-primitive-2.0.0.tgz"
  integrity sha1-IHurkWOEmcB7Kt8kCkGochADRXU=

is-regex@^1.0.4, is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/is-regex/download/is-regex-1.1.4.tgz?cache=0&sync_timestamp=1628221905423&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-regex%2Fdownload%2Fis-regex-1.1.4.tgz"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-shared-array-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.1.tgz"
  integrity sha1-l7DIX72stZycRG/mU7gs8rW3z+Y=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.nlark.com/is-string/download/is-string-1.0.7.tgz"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-svg@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-svg/download/is-svg-2.1.0.tgz"
  integrity sha1-z2EJDaDZ77yrhyLeum8DIgjbsOk=
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/is-symbol/download/is-symbol-1.0.4.tgz?cache=0&sync_timestamp=1620501174327&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-symbol%2Fdownload%2Fis-symbol-1.0.4.tgz"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-url@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/is-url/download/is-url-1.2.4.tgz"
  integrity sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-weakref@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-weakref/download/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-what@^3.14.1:
  version "3.14.1"
  resolved "https://registry.npmmirror.com/is-what/-/is-what-3.14.1.tgz"
  integrity sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==

is-whitespace@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/is-whitespace/download/is-whitespace-0.3.0.tgz"
  integrity sha1-Fjnssb4DauxppUy7QBz77XEUq38=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-windows/download/is-windows-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-windows%2Fdownload%2Fis-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-wsl/download/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is2@^2.0.6:
  version "2.0.7"
  resolved "https://registry.nlark.com/is2/download/is2-2.0.7.tgz"
  integrity sha1-0IThDKs71F1snf3npIWZ/LuT/Kw=
  dependencies:
    deep-is "^0.1.3"
    ip-regex "^4.1.0"
    is-url "^1.2.4"

isarray@^1.0.0, isarray@~1.0.0, isarray@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/isexe/download/isexe-2.0.0.tgz?cache=0&sync_timestamp=1618847054312&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fisexe%2Fdownload%2Fisexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-api@^1.1.14:
  version "1.3.7"
  resolved "https://registry.npmmirror.com/istanbul-api/download/istanbul-api-1.3.7.tgz"
  integrity sha1-qGx3DSsD4R4/d4zXrt2C0nIgkqo=
  dependencies:
    async "^2.1.4"
    fileset "^2.0.2"
    istanbul-lib-coverage "^1.2.1"
    istanbul-lib-hook "^1.2.2"
    istanbul-lib-instrument "^1.10.2"
    istanbul-lib-report "^1.1.5"
    istanbul-lib-source-maps "^1.2.6"
    istanbul-reports "^1.5.1"
    js-yaml "^3.7.0"
    mkdirp "^0.5.1"
    once "^1.4.0"

istanbul-lib-coverage@^1.1.1, istanbul-lib-coverage@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-1.2.1.tgz?cache=0&sync_timestamp=1634527189737&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fistanbul-lib-coverage%2Fdownload%2Fistanbul-lib-coverage-1.2.1.tgz"
  integrity sha1-zPftzQoLubj3Kf7rCTBHD5r2ZPA=

istanbul-lib-hook@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/istanbul-lib-hook/download/istanbul-lib-hook-1.2.2.tgz"
  integrity sha1-vGvwfxKmQfvxyFOR0Nqo8K6mv4Y=
  dependencies:
    append-transform "^0.4.0"

istanbul-lib-instrument@^1.10.1, istanbul-lib-instrument@^1.10.2, istanbul-lib-instrument@^1.8.0:
  version "1.10.2"
  resolved "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-1.10.2.tgz"
  integrity sha1-H1XtEKw8R/K93dUweTUSZ1TQqco=
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.18.0"
    istanbul-lib-coverage "^1.2.1"
    semver "^5.3.0"

istanbul-lib-report@^1.1.5:
  version "1.1.5"
  resolved "https://registry.nlark.com/istanbul-lib-report/download/istanbul-lib-report-1.1.5.tgz"
  integrity sha1-8qZX/GKC+WFwqvKB6zCkWPf0Fww=
  dependencies:
    istanbul-lib-coverage "^1.2.1"
    mkdirp "^0.5.1"
    path-parse "^1.0.5"
    supports-color "^3.1.2"

istanbul-lib-source-maps@^1.2.1, istanbul-lib-source-maps@^1.2.6:
  version "1.2.6"
  resolved "https://registry.npmmirror.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-1.2.6.tgz"
  integrity sha1-N7n/ZhWA+PyhEjJ1LuQuCMZnXY8=
  dependencies:
    debug "^3.1.0"
    istanbul-lib-coverage "^1.2.1"
    mkdirp "^0.5.1"
    rimraf "^2.6.1"
    source-map "^0.5.3"

istanbul-reports@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/istanbul-reports/download/istanbul-reports-1.5.1.tgz"
  integrity sha1-l+Tb87UV6MSEyuoV1lJO69P/Tho=
  dependencies:
    handlebars "^4.0.3"

jest-changed-files@^22.2.0:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-changed-files/download/jest-changed-files-22.4.3.tgz"
  integrity sha1-iIIYHgIsOL1GouTRjUTRnZCpD7I=
  dependencies:
    throat "^4.0.0"

jest-cli@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest-cli/download/jest-cli-22.4.4.tgz"
  integrity sha1-aM0qKq6YOtseZjgkjKIQgv1tnpA=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    import-local "^1.0.0"
    is-ci "^1.0.10"
    istanbul-api "^1.1.14"
    istanbul-lib-coverage "^1.1.1"
    istanbul-lib-instrument "^1.8.0"
    istanbul-lib-source-maps "^1.2.1"
    jest-changed-files "^22.2.0"
    jest-config "^22.4.4"
    jest-environment-jsdom "^22.4.1"
    jest-get-type "^22.1.0"
    jest-haste-map "^22.4.2"
    jest-message-util "^22.4.0"
    jest-regex-util "^22.1.0"
    jest-resolve-dependencies "^22.1.0"
    jest-runner "^22.4.4"
    jest-runtime "^22.4.4"
    jest-snapshot "^22.4.0"
    jest-util "^22.4.1"
    jest-validate "^22.4.4"
    jest-worker "^22.2.2"
    micromatch "^2.3.11"
    node-notifier "^5.2.1"
    realpath-native "^1.0.0"
    rimraf "^2.5.4"
    slash "^1.0.0"
    string-length "^2.0.0"
    strip-ansi "^4.0.0"
    which "^1.2.12"
    yargs "^10.0.3"

jest-config@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest-config/download/jest-config-22.4.4.tgz"
  integrity sha1-cqUhGIcgWXFpzYtP+Gk071dS2Go=
  dependencies:
    chalk "^2.0.1"
    glob "^7.1.1"
    jest-environment-jsdom "^22.4.1"
    jest-environment-node "^22.4.1"
    jest-get-type "^22.1.0"
    jest-jasmine2 "^22.4.4"
    jest-regex-util "^22.1.0"
    jest-resolve "^22.4.2"
    jest-util "^22.4.1"
    jest-validate "^22.4.4"
    pretty-format "^22.4.0"

jest-diff@^22.4.0, jest-diff@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-diff/download/jest-diff-22.4.3.tgz"
  integrity sha1-4YzD/v8K7vFZ0CMQ8mhtQGU3gDA=
  dependencies:
    chalk "^2.0.1"
    diff "^3.2.0"
    jest-get-type "^22.4.3"
    pretty-format "^22.4.3"

jest-docblock@^22.4.0, jest-docblock@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-docblock/download/jest-docblock-22.4.3.tgz"
  integrity sha1-UIhvEytCsoDJA8WSNzu26Tu2ixk=
  dependencies:
    detect-newline "^2.1.0"

jest-environment-jsdom@^22.4.1:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-environment-jsdom/download/jest-environment-jsdom-22.4.3.tgz"
  integrity sha1-1n2qQVXjNRauzdNa/YLUq/D6ih4=
  dependencies:
    jest-mock "^22.4.3"
    jest-util "^22.4.3"
    jsdom "^11.5.1"

jest-environment-node@^22.4.1:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-environment-node/download/jest-environment-node-22.4.3.tgz"
  integrity sha1-VMTqo3TIPdUqnah1m+FOvh0LkSk=
  dependencies:
    jest-mock "^22.4.3"
    jest-util "^22.4.3"

jest-get-type@^22.1.0, jest-get-type@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-22.4.3.tgz"
  integrity sha1-46hQTYR5NC3UQgI2syKGnxiQDOQ=

jest-haste-map@^22.4.2:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-22.4.3.tgz"
  integrity sha1-JYQvoro1AgB2esJ/ZY1YudXC4gs=
  dependencies:
    fb-watchman "^2.0.0"
    graceful-fs "^4.1.11"
    jest-docblock "^22.4.3"
    jest-serializer "^22.4.3"
    jest-worker "^22.4.3"
    micromatch "^2.3.11"
    sane "^2.0.0"

jest-jasmine2@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest-jasmine2/download/jest-jasmine2-22.4.4.tgz"
  integrity sha1-xV+SyWGhQfaT+Gn18IGnmhDSTiM=
  dependencies:
    chalk "^2.0.1"
    co "^4.6.0"
    expect "^22.4.0"
    graceful-fs "^4.1.11"
    is-generator-fn "^1.0.0"
    jest-diff "^22.4.0"
    jest-matcher-utils "^22.4.0"
    jest-message-util "^22.4.0"
    jest-snapshot "^22.4.0"
    jest-util "^22.4.1"
    source-map-support "^0.5.0"

jest-leak-detector@^22.4.0:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-leak-detector/download/jest-leak-detector-22.4.3.tgz"
  integrity sha1-K3smMQOvroxStrkSQaLeQBF+WzU=
  dependencies:
    pretty-format "^22.4.3"

jest-matcher-utils@^22.4.0, jest-matcher-utils@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-matcher-utils/download/jest-matcher-utils-22.4.3.tgz"
  integrity sha1-RjL+Qo68c+vBlNPHtl03sWH3EP8=
  dependencies:
    chalk "^2.0.1"
    jest-get-type "^22.4.3"
    pretty-format "^22.4.3"

jest-message-util@^22.4.0, jest-message-util@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-message-util/download/jest-message-util-22.4.3.tgz"
  integrity sha1-zz04qv5L792/xFXlfWXVI545nrc=
  dependencies:
    "@babel/code-frame" "^7.0.0-beta.35"
    chalk "^2.0.1"
    micromatch "^2.3.11"
    slash "^1.0.0"
    stack-utils "^1.0.1"

jest-mock@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-mock/download/jest-mock-22.4.3.tgz"
  integrity sha1-9jui8HoVEXcs3Hl5czOX33cKq8c=

jest-regex-util@^22.1.0, jest-regex-util@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-22.4.3.tgz"
  integrity sha1-qCbrGRzfIlAhmMVAGh/ATenO9a8=

jest-resolve-dependencies@^22.1.0:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-resolve-dependencies/download/jest-resolve-dependencies-22.4.3.tgz"
  integrity sha1-4iVqWoRnMtw5acty88mtdyWoGV4=
  dependencies:
    jest-regex-util "^22.4.3"

jest-resolve@^22.4.2:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-resolve/download/jest-resolve-22.4.3.tgz"
  integrity sha1-DOnUOMhDgimqm5FpaOxrBcGrtOo=
  dependencies:
    browser-resolve "^1.11.2"
    chalk "^2.0.1"

jest-runner@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest-runner/download/jest-runner-22.4.4.tgz"
  integrity sha1-38p7dVPg+mF+exKRrrfOg+VAqQc=
  dependencies:
    exit "^0.1.2"
    jest-config "^22.4.4"
    jest-docblock "^22.4.0"
    jest-haste-map "^22.4.2"
    jest-jasmine2 "^22.4.4"
    jest-leak-detector "^22.4.0"
    jest-message-util "^22.4.0"
    jest-runtime "^22.4.4"
    jest-util "^22.4.1"
    jest-worker "^22.2.2"
    throat "^4.0.0"

jest-runtime@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest-runtime/download/jest-runtime-22.4.4.tgz"
  integrity sha1-m6d5L8dVgqW+D3mvb4/oreoxQEg=
  dependencies:
    babel-core "^6.0.0"
    babel-jest "^22.4.4"
    babel-plugin-istanbul "^4.1.5"
    chalk "^2.0.1"
    convert-source-map "^1.4.0"
    exit "^0.1.2"
    graceful-fs "^4.1.11"
    jest-config "^22.4.4"
    jest-haste-map "^22.4.2"
    jest-regex-util "^22.1.0"
    jest-resolve "^22.4.2"
    jest-util "^22.4.1"
    jest-validate "^22.4.4"
    json-stable-stringify "^1.0.1"
    micromatch "^2.3.11"
    realpath-native "^1.0.0"
    slash "^1.0.0"
    strip-bom "3.0.0"
    write-file-atomic "^2.1.0"
    yargs "^10.0.3"

jest-serializer-vue@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/jest-serializer-vue/download/jest-serializer-vue-0.3.0.tgz"
  integrity sha1-ISjQwcg8O8svT7lhBEAmJrt1UG0=
  dependencies:
    pretty "2.0.0"

jest-serializer@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-22.4.3.tgz"
  integrity sha1-pnm4Gn8RHkdmI19PDEbSMO4PdDY=

jest-snapshot@^22.4.0:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-snapshot/download/jest-snapshot-22.4.3.tgz"
  integrity sha1-tcm0KEb/ufrMt2uEExW6Z4hzYtI=
  dependencies:
    chalk "^2.0.1"
    jest-diff "^22.4.3"
    jest-matcher-utils "^22.4.3"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    pretty-format "^22.4.3"

jest-util@^22.4.1, jest-util@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-util/download/jest-util-22.4.3.tgz"
  integrity sha1-xw/sjuxIfDexCwgJ3AZKfs9qr6w=
  dependencies:
    callsites "^2.0.0"
    chalk "^2.0.1"
    graceful-fs "^4.1.11"
    is-ci "^1.0.10"
    jest-message-util "^22.4.3"
    mkdirp "^0.5.1"
    source-map "^0.6.0"

jest-validate@^22.4.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest-validate/download/jest-validate-22.4.4.tgz"
  integrity sha1-HdC2Fu9GyZXeYYENhfVxGdu87E0=
  dependencies:
    chalk "^2.0.1"
    jest-config "^22.4.4"
    jest-get-type "^22.1.0"
    leven "^2.1.0"
    pretty-format "^22.4.0"

jest-worker@^22.2.2, jest-worker@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/jest-worker/download/jest-worker-22.4.3.tgz"
  integrity sha1-XEIUF8uhwKv2S/Vr1ft5aNed1As=
  dependencies:
    merge-stream "^1.0.1"

jest@^22.0.4:
  version "22.4.4"
  resolved "https://registry.npmmirror.com/jest/download/jest-22.4.4.tgz"
  integrity sha1-/7NsllSzOaE+ELPUszjrPp1J9us=
  dependencies:
    import-local "^1.0.0"
    jest-cli "^22.4.4"

jquery@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/jquery/download/jquery-3.6.0.tgz"
  integrity sha1-xyoJ8Vwb3OFC9J2/EXC9+K2sJHA=

js-base64@^2.1.9:
  version "2.6.4"
  resolved "https://registry.npmjs.org/js-base64/-/js-base64-2.6.4.tgz"
  integrity sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ==

js-beautify@^1.6.12, js-beautify@^1.6.14:
  version "1.14.0"
  resolved "https://registry.nlark.com/js-beautify/download/js-beautify-1.14.0.tgz"
  integrity sha1-LOeQxVXVPOHj1zYyJ6z13GkCTC0=
  dependencies:
    config-chain "^1.1.12"
    editorconfig "^0.15.3"
    glob "^7.1.3"
    nopt "^5.0.0"

js-calendar@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/js-calendar/download/js-calendar-1.2.3.tgz"
  integrity sha1-pYOwZEtOaVujlPNE0QPbzHp6fT4=

js-marker-clusterer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/js-marker-clusterer/-/js-marker-clusterer-1.0.0.tgz"
  integrity sha512-NZrs6UnoRd6Qd8dXZ4rgIxIO7is2Oa22Mp3XuczmHW+eqQYo03BoHXEzDGzpDHlFH+iuxWHFz5e7zQwoESbQIw==

js-tokens@^3.0.0, "js-tokens@^3.0.0 || ^4.0.0", js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-3.0.2.tgz?cache=0&sync_timestamp=1619345098261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-tokens%2Fdownload%2Fjs-tokens-3.0.2.tgz"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.14.1.tgz?cache=0&sync_timestamp=1618435004368&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^3.4.3, js-yaml@^3.7.0:
  version "3.14.1"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^3.9.1:
  version "3.14.1"
  resolved "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.14.1.tgz?cache=0&sync_timestamp=1618435004368&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@~3.7.0:
  version "3.7.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.7.0.tgz"
  integrity sha512-eIlkGty7HGmntbV6P/ZlAsoncFLGsNoM27lkTzS+oneY/EiNhj+geqD9ezg/ip+SW6Var0BJU2JtV0vEUZpWVQ==
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^11.5.1:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/jsdom/download/jsdom-11.12.0.tgz"
  integrity sha1-GoDUDd03ih3lllbp5txaO6hle8g=
  dependencies:
    abab "^2.0.0"
    acorn "^5.5.3"
    acorn-globals "^4.1.0"
    array-equal "^1.0.0"
    cssom ">= 0.3.2 < 0.4.0"
    cssstyle "^1.0.0"
    data-urls "^1.0.0"
    domexception "^1.0.1"
    escodegen "^1.9.1"
    html-encoding-sniffer "^1.0.2"
    left-pad "^1.3.0"
    nwsapi "^2.0.7"
    parse5 "4.0.0"
    pn "^1.1.0"
    request "^2.87.0"
    request-promise-native "^1.0.5"
    sax "^1.2.4"
    symbol-tree "^3.2.2"
    tough-cookie "^2.3.4"
    w3c-hr-time "^1.0.1"
    webidl-conversions "^4.0.2"
    whatwg-encoding "^1.0.3"
    whatwg-mimetype "^2.1.0"
    whatwg-url "^6.4.1"
    ws "^5.2.0"
    xml-name-validator "^3.0.0"

jsencrypt@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/jsencrypt/download/jsencrypt-3.2.1.tgz"
  integrity sha1-CXZpg8x2AIj/JrEv5+V0JSr5dyc=

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/jsesc/download/jsesc-1.3.0.tgz"
  integrity sha1-RsP+yMGJKxKwgz25vHYiF226s0s=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.nlark.com/jsesc/download/jsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.nlark.com/jsesc/download/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-loader@^0.5.4:
  version "0.5.7"
  resolved "https://registry.nlark.com/json-loader/download/json-loader-0.5.7.tgz"
  integrity sha1-3KFKcCNf+C8KyaOr62DTN6NlGF0=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "https://registry.nlark.com/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz"
  integrity sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/json-schema/download/json-schema-0.4.0.tgz?cache=0&sync_timestamp=1636423494254&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjson-schema%2Fdownload%2Fjson-schema-0.4.0.tgz"
  integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz"
  integrity sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json3@^3.3.2:
  version "3.3.3"
  resolved "https://registry.npmmirror.com/json3/download/json3-3.3.3.tgz"
  integrity sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=

json3@3.3.2:
  version "3.3.2"
  resolved "https://registry.npmmirror.com/json3/download/json3-3.3.2.tgz"
  integrity sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.2.3"
  resolved "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^2.0.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/jsonfile/download/jsonfile-2.4.0.tgz"
  integrity sha1-NzaitCi4e72gzIO1P6PWM6NcKug=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/jsonfile/-/jsonfile-4.0.0.tgz"
  integrity sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.nlark.com/jsonify/download/jsonify-0.0.0.tgz"
  integrity sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=

jsprim@^1.2.2:
  version "1.4.2"
  resolved "https://registry.npmmirror.com/jsprim/download/jsprim-1.4.2.tgz"
  integrity sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

jsrsasign@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmmirror.com/jsrsasign/-/jsrsasign-11.1.0.tgz"
  integrity sha512-Ov74K9GihaK9/9WncTe1mPmvrO7Py665TUfUKvraXBpu+xcTWitrtuOwcjf4KMU9maPaYn0OuaWy0HOzy/GBXg==

jssip-for-node@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmmirror.com/jssip-for-node/download/jssip-for-node-0.1.3.tgz"
  integrity sha1-M2He2nGBjhZSsnhsf71nmTBtthg=
  dependencies:
    ws "~0.4.31"

jstransform@^11.0.3:
  version "11.0.3"
  resolved "https://registry.npmmirror.com/jstransform/-/jstransform-11.0.3.tgz"
  integrity sha512-LGm87w0A8E92RrcXt94PnNHkFqHmgDy3mKHvNZOG7QepKCTCH/VB6S+IEN+bT4uLN3gVpOT0vvOOVd96osG71g==
  dependencies:
    base62 "^1.1.0"
    commoner "^0.10.1"
    esprima-fb "^15001.1.0-dev-harmony-fb"
    object-assign "^2.0.0"
    source-map "^0.4.2"

keycode@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/keycode/-/keycode-2.2.1.tgz"
  integrity sha512-Rdgz9Hl9Iv4QKi8b0OlCRQEzp4AgVxyCtz5S/+VIHezDmrDhkp2N2TqBWOLz0/gbeREXOOiI9/4b8BY9uw2vFg==

killable@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/killable/download/killable-1.0.1.tgz"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^3.0.3:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^5.0.2:
  version "5.1.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klona@^2.0.4:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/klona/download/klona-2.0.5.tgz?cache=0&sync_timestamp=1635385383825&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fklona%2Fdownload%2Fklona-2.0.5.tgz"
  integrity sha1-0WZXTZAHY5XZljqnqSj6u412r7w=

koa2-cors@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/koa2-cors/download/koa2-cors-2.0.6.tgz"
  integrity sha1-mtI986C5u4RTC0b1lE8/tXYIZVQ=

last-call-webpack-plugin@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/last-call-webpack-plugin/download/last-call-webpack-plugin-2.1.2.tgz"
  integrity sha1-rYDG4xCZgpTS7SGApo6VieR2jEQ=
  dependencies:
    lodash "^4.17.4"
    webpack-sources "^1.0.1"

layui-laydate@^5.3.1:
  version "5.3.1"
  resolved "https://registry.nlark.com/layui-laydate/download/layui-laydate-5.3.1.tgz"
  integrity sha1-hAIkBUl+cpoihvN+t12SpUwDAR4=

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/lazy-cache/download/lazy-cache-1.0.4.tgz"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/lcid/download/lcid-1.0.0.tgz"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

left-pad@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/left-pad/download/left-pad-1.3.0.tgz"
  integrity sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4=

less@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/less/-/less-4.1.2.tgz"
  integrity sha512-EoQp/Et7OSOVu0aJknJOtlXZsnr8XE8KwuzTHOLeVSEx8pVWUICc8Q0VYRHgzyjX78nMEyC/oztWFbgyhtNfDA==
  dependencies:
    copy-anything "^2.0.1"
    parse-node-version "^1.0.1"
    tslib "^2.3.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    needle "^2.5.2"
    source-map "~0.6.0"

leven@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/leven/download/leven-2.1.0.tgz?cache=0&sync_timestamp=1628597917913&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fleven%2Fdownload%2Fleven-2.1.0.tgz"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lib-flexible@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/lib-flexible/download/lib-flexible-0.3.2.tgz"
  integrity sha1-BvWnSDIxSi01wSA5vJw8otrqpCY=

linkify-it@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/linkify-it/-/linkify-it-2.2.0.tgz"
  integrity sha512-GnAl/knGn+i1U/wjBz3akz2stz+HrHLsxMwHQGofCDfPvlf+gDKN58UtfmUquTY4/MXeE2x7k19KQmeoZi94Iw==
  dependencies:
    uc.micro "^1.0.1"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/load-json-file/download/load-json-file-1.1.0.tgz"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/load-json-file/download/load-json-file-2.0.0.tgz"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.0:
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/loader-runner/download/loader-runner-2.4.0.tgz?cache=0&sync_timestamp=1610027918622&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floader-runner%2Fdownload%2Floader-runner-2.4.0.tgz"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.npmmirror.com/loader-utils/download/loader-utils-0.2.17.tgz?cache=0&sync_timestamp=1636687869321&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floader-utils%2Fdownload%2Floader-utils-0.2.17.tgz"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.0, loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.4.0:
  version "1.4.2"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz"
  integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.4.tgz"
  integrity sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-2.0.0.tgz?cache=0&sync_timestamp=1629895724478&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flocate-path%2Fdownload%2Flocate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

lodash._arraycopy@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/lodash._arraycopy/download/lodash._arraycopy-3.0.0.tgz"
  integrity sha1-due3wfH7klRzdIeKVi7Qaj5Q9uE=

lodash._arrayeach@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/lodash._arrayeach/download/lodash._arrayeach-3.0.0.tgz"
  integrity sha1-urFWsqkNPxu9XGU0AzSeXlkz754=

lodash._baseassign@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/lodash._baseassign/download/lodash._baseassign-3.2.0.tgz"
  integrity sha1-jDigmVAPIVrQnlnxci/QxSv+Ck4=
  dependencies:
    lodash._basecopy "^3.0.0"
    lodash.keys "^3.0.0"

lodash._baseclone@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npm.taobao.org/lodash._baseclone/download/lodash._baseclone-3.3.0.tgz"
  integrity sha1-MDUZv2OT/n5C802LYw73eU41Qrc=
  dependencies:
    lodash._arraycopy "^3.0.0"
    lodash._arrayeach "^3.0.0"
    lodash._baseassign "^3.0.0"
    lodash._basefor "^3.0.0"
    lodash.isarray "^3.0.0"
    lodash.keys "^3.0.0"

lodash._baseclone@^4.0.0:
  version "4.5.7"
  resolved "https://registry.npm.taobao.org/lodash._baseclone/download/lodash._baseclone-4.5.7.tgz"
  integrity sha1-zkKt4IOE711i+nfDD2GkbmhvhDQ=

lodash._basecopy@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/lodash._basecopy/download/lodash._basecopy-3.0.1.tgz"
  integrity sha1-jaDmqHbPNEwK2KVIghEd08XHyjY=

lodash._basecreate@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/lodash._basecreate/download/lodash._basecreate-3.0.3.tgz"
  integrity sha1-G8ZhYU2qf8MRt9A78WgGoCE8+CE=

lodash._basefor@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/lodash._basefor/download/lodash._basefor-3.0.3.tgz"
  integrity sha1-dVC06SGO8J+tJDQ7YSAhx5tMIMI=

lodash._bindcallback@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/lodash._bindcallback/download/lodash._bindcallback-3.0.1.tgz"
  integrity sha1-5THCdkTPi1epnhftlbNcdIeJOS4=

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "https://registry.npm.taobao.org/lodash._getnative/download/lodash._getnative-3.9.1.tgz"
  integrity sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=

lodash._isiterateecall@^3.0.0:
  version "3.0.9"
  resolved "https://registry.nlark.com/lodash._isiterateecall/download/lodash._isiterateecall-3.0.9.tgz"
  integrity sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw=

lodash._stack@^4.0.0:
  version "4.1.3"
  resolved "https://registry.npm.taobao.org/lodash._stack/download/lodash._stack-4.1.3.tgz"
  integrity sha1-dRqnbBuWSwR+dtFPxyoJP8teLdA=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clone@3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/lodash.clone/download/lodash.clone-3.0.3.tgz"
  integrity sha1-hGiMc9MrWpDKJWFpY/GJJSqZcEM=
  dependencies:
    lodash._baseclone "^3.0.0"
    lodash._bindcallback "^3.0.0"
    lodash._isiterateecall "^3.0.0"

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  integrity sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==

lodash.create@3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/lodash.create/download/lodash.create-3.1.1.tgz"
  integrity sha1-1/KEnw29p+BGgruM1yqwIkYd6+c=
  dependencies:
    lodash._baseassign "^3.0.0"
    lodash._basecreate "^3.0.0"
    lodash._isiterateecall "^3.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.defaultsdeep@4.3.2:
  version "4.3.2"
  resolved "https://registry.nlark.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.3.2.tgz"
  integrity sha1-bBpYbmxWR7DmTi15gUG4g2FYvoo=
  dependencies:
    lodash._baseclone "^4.0.0"
    lodash._stack "^4.0.0"
    lodash.isplainobject "^4.0.0"
    lodash.keysin "^4.0.0"
    lodash.mergewith "^4.0.0"
    lodash.rest "^4.0.0"

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.foreach/-/lodash.foreach-4.5.0.tgz"
  integrity sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ==

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/lodash.isarguments/download/lodash.isarguments-3.1.0.tgz"
  integrity sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/lodash.isarray/download/lodash.isarray-3.0.4.tgz"
  integrity sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  integrity sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==

lodash.isplainobject@^4.0.0:
  version "4.0.6"
  resolved "https://registry.npm.taobao.org/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.keys@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npm.taobao.org/lodash.keys/download/lodash.keys-3.1.2.tgz"
  integrity sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.keysin@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/lodash.keysin/download/lodash.keysin-4.2.0.tgz"
  integrity sha1-jMP7NcLZSsxEOhhj4C+kB5nqbyg=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.mergewith@^4.0.0:
  version "4.6.2"
  resolved "https://registry.nlark.com/lodash.mergewith/download/lodash.mergewith-4.6.2.tgz"
  integrity sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=

lodash.rest@^4.0.0:
  version "4.0.5"
  resolved "https://registry.npm.taobao.org/lodash.rest/download/lodash.rest-4.0.5.tgz"
  integrity sha1-lU73UEkmIDjJbR/Jiyj9r58Hcqo=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://registry.npm.taobao.org/lodash.sortby/download/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/lodash.toarray/-/lodash.toarray-4.4.0.tgz"
  integrity sha512-QyffEA3i5dma5q2490+SgCvDN0pXLmRGSyAANuVi0HQ01Pkfr9fuoKQW8wm1wGBnJITs/mS7wQvS6VshUEBFCw==

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.3, lodash@^4.17.4, lodash@^4.2.0, lodash@^4.3.0:
  version "4.17.21"
  resolved "https://registry.nlark.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/log-symbols/download/log-symbols-2.2.0.tgz"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

loglevel@^1.4.1:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/loglevel/download/loglevel-1.8.0.tgz"
  integrity sha512-G6A/nJLRgWOuuwdNuA6koovfEV1YpqqAG4pRUlFaz3jj2QNZ8M4vBqnVA+HBTmU/AMNUtlOsMmSpF6NyOjztbA==

longest@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/longest/download/longest-1.0.1.tgz"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/loud-rejection/download/loud-rejection-1.6.0.tgz"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1, lru-cache@^4.1.1, lru-cache@^4.1.5:
  version "4.1.5"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@~2.6.5:
  version "2.6.5"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-2.6.5.tgz"
  integrity sha1-5W1jVBSO3o13B7WNFDIg/QjfD9U=

m3u8-parser@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/m3u8-parser/-/m3u8-parser-2.1.0.tgz"
  integrity sha512-WbEpQ2FUaNGbJ0YanSeyj9D9ruu4FUvz+ZvebIzI2bSME+PUwcPXO1kKXZkjcPUAFruDikoOI5fWQNIA6JCCOQ==

m3u8-parser@4.7.1:
  version "4.7.1"
  resolved "https://registry.npmmirror.com/m3u8-parser/-/m3u8-parser-4.7.1.tgz"
  integrity sha512-pbrQwiMiq+MmI9bl7UjtPT3AK603PV9bogNlr83uC+X9IoxqL5E4k7kU7fMQ0dpRgxgeSMygqUa0IMLQNXLBNA==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^3.0.5"
    global "^4.4.0"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/make-dir/download/make-dir-1.3.0.tgz"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry.npmmirror.com/makeerror/download/makeerror-1.0.12.tgz?cache=0&sync_timestamp=1635238306211&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmakeerror%2Fdownload%2Fmakeerror-1.0.12.tgz"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.nlark.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/map-obj/download/map-obj-1.0.1.tgz?cache=0&sync_timestamp=1634552719803&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmap-obj%2Fdownload%2Fmap-obj-1.0.1.tgz"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

markdown-it@^8.4.0:
  version "8.4.2"
  resolved "https://registry.npmmirror.com/markdown-it/-/markdown-it-8.4.2.tgz"
  integrity sha512-GcRz3AWTqSUphY3vsUqQSFMbgR38a4Lh3GWlHRh/7MRwz8mcu9n2IO7HOh+bXHrR9kOPDl5RNCaEsrneb+xhHQ==
  dependencies:
    argparse "^1.0.7"
    entities "~1.1.1"
    linkify-it "^2.0.0"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

marker-clusterer-plus@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npmmirror.com/marker-clusterer-plus/-/marker-clusterer-plus-2.1.4.tgz"
  integrity sha512-4WLZnYCkgsUfSC0pftldd0YrLNupSqVIEdxL979f3sXVMBHTUOF3gDa6cEuOk2z8UGyVGcANiNZgvVc333mrHA==

math-expression-evaluator@^1.2.14:
  version "1.3.8"
  resolved "https://registry.nlark.com/math-expression-evaluator/download/math-expression-evaluator-1.3.8.tgz"
  integrity sha1-Mg2jsrwVEvT1D8MCCysc1cjp1Xc=

math-random@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/math-random/download/math-random-1.0.4.tgz"
  integrity sha1-XdaUPJOFSCZwFtTjTwV1gwgMUUw=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.14.tgz"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.4.tgz"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

mdurl@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/mdurl/-/mdurl-1.0.1.tgz"
  integrity sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

mem@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/mem/download/mem-1.1.0.tgz?cache=0&sync_timestamp=1626534352883&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmem%2Fdownload%2Fmem-1.1.0.tgz"
  integrity sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y=
  dependencies:
    mimic-fn "^1.0.0"

memory-fs@^0.4.0, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/memory-fs/download/memory-fs-0.4.1.tgz"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.3.0:
  version "3.7.0"
  resolved "https://registry.npmmirror.com/meow/download/meow-3.7.0.tgz?cache=0&sync_timestamp=1637477569022&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmeow%2Fdownload%2Fmeow-3.7.0.tgz"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-options@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/merge-options/download/merge-options-1.0.1.tgz"
  integrity sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=
  dependencies:
    is-plain-obj "^1.1"

merge-stream@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/merge-stream/download/merge-stream-1.0.1.tgz"
  integrity sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=
  dependencies:
    readable-stream "^2.0.1"

merge@^1.2.0:
  version "1.2.1"
  resolved "https://registry.nlark.com/merge/download/merge-1.2.1.tgz"
  integrity sha1-OL6/gMMiCopIe2/Ps5QbsRcgwUU=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^2.3.11:
  version "2.3.11"
  resolved "https://registry.nlark.com/micromatch/download/micromatch-2.3.11.tgz"
  integrity sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.nlark.com/micromatch/download/micromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/micromatch/download/micromatch-3.1.0.tgz"
  integrity sha1-UQLU6vILaZfWAI46z+HESj+oFeI=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.2.2"
    define-property "^1.0.0"
    extend-shallow "^2.0.1"
    extglob "^2.0.2"
    fragment-cache "^0.2.1"
    kind-of "^5.0.2"
    nanomatch "^1.2.1"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/miller-rabin/download/miller-rabin-4.0.1.tgz"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

"mime-db@>= 1.43.0 < 2", mime-db@1.51.0:
  version "1.51.0"
  resolved "https://registry.npmmirror.com/mime-db/download/mime-db-1.51.0.tgz?cache=0&sync_timestamp=1636425951030&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-db%2Fdownload%2Fmime-db-1.51.0.tgz"
  integrity sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g==

mime-match@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/mime-match/-/mime-match-1.0.2.tgz"
  integrity sha512-VXp/ugGDVh3eCLOBCiHZMYWQaTNUHv2IJrut+yXA6+JbLPXHglHwfS/5A5L0ll+jkCY7fIzRJcH6OIunF+c6Cg==
  dependencies:
    wildcard "^1.1.0"

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.34"
  resolved "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.34.tgz?cache=0&sync_timestamp=1636432355847&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.34.tgz"
  integrity sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A==
  dependencies:
    mime-db "1.51.0"

mime@^1.4.1, mime@^1.5.0, mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@1.3.x:
  version "1.3.6"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.3.6.tgz"
  integrity sha1-WR2E02U6awtKO5343lqoEI5y5eA=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

min-document@^2.19.0:
  version "2.19.0"
  resolved "https://registry.npmmirror.com/min-document/-/min-document-2.19.0.tgz"
  integrity sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==
  dependencies:
    dom-walk "^0.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.2, minimatch@^3.0.3, minimatch@^3.0.4, "minimatch@2 || 3":
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.3.tgz"
  integrity sha1-Kk5AkLlrLbBqnX3wEFWmKnfJt3Q=
  dependencies:
    brace-expansion "^1.0.0"

minimist@^1.1.1, minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minimist@~0.0.1:
  version "0.0.10"
  resolved "https://registry.nlark.com/minimist/download/minimist-0.0.10.tgz?cache=0&sync_timestamp=1618847017774&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimist%2Fdownload%2Fminimist-0.0.10.tgz"
  integrity sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8=

minimist@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/minimist/download/minimist-0.0.8.tgz?cache=0&sync_timestamp=1618847017774&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimist%2Fdownload%2Fminimist-0.0.8.tgz"
  integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=

mississippi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/mississippi/download/mississippi-2.0.0.tgz"
  integrity sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^2.0.1"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mitt@1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/mitt/download/mitt-1.1.2.tgz?cache=0&sync_timestamp=1624483449786&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmitt%2Fdownload%2Fmitt-1.1.2.tgz"
  integrity sha1-OA5hSA1qYVtmDwertg1R4KTkvtY=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.4, mkdirp@^0.5.5, mkdirp@~0.5.0, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mkdirp@0.5.1:
  version "0.5.1"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.1.tgz"
  integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
  dependencies:
    minimist "0.0.8"

mkpath@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/mkpath/download/mkpath-1.0.0.tgz"
  integrity sha1-67Opd+evHGg65v2hK1Raa6bFhT0=

mocha-nightwatch@3.2.2:
  version "3.2.2"
  resolved "https://registry.npm.taobao.org/mocha-nightwatch/download/mocha-nightwatch-3.2.2.tgz"
  integrity sha1-kby5s73gV912d8eBJeSR5Y1mZHw=
  dependencies:
    browser-stdout "1.3.0"
    commander "2.9.0"
    debug "2.2.0"
    diff "1.4.0"
    escape-string-regexp "1.0.5"
    glob "7.0.5"
    growl "1.9.2"
    json3 "3.3.2"
    lodash.create "3.1.1"
    mkdirp "0.5.1"
    supports-color "3.1.2"

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/move-concurrently/download/move-concurrently-1.0.1.tgz"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

mpd-parser@0.21.1:
  version "0.21.1"
  resolved "https://registry.npmmirror.com/mpd-parser/-/mpd-parser-0.21.1.tgz"
  integrity sha512-BxlSXWbKE1n7eyEPBnTEkrzhS3PdmkkKdM1pgKbPnPOH0WFZIc0sPOWi7m0Uo3Wd2a4Or8Qf4ZbS7+ASqQ49fw==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^3.0.5"
    "@xmldom/xmldom" "^0.7.2"
    global "^4.4.0"

ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

ms@0.7.1:
  version "0.7.1"
  resolved "https://registry.npmmirror.com/ms/download/ms-0.7.1.tgz"
  integrity sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://registry.nlark.com/mute-stream/download/mute-stream-0.0.7.tgz"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mux.js@4.3.2:
  version "4.3.2"
  resolved "https://registry.npmmirror.com/mux.js/-/mux.js-4.3.2.tgz"
  integrity sha512-g0q6DPdvb3yYcoK7ElBGobdSSrhY/RjPt19U7uUc733aqvc5bCS/aCvL9z+448y+IoCZnYDwyZfQBBXMSmGOaQ==

mux.js@6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/mux.js/-/mux.js-6.0.1.tgz"
  integrity sha512-22CHb59rH8pWGcPGW5Og7JngJ9s+z4XuSlYvnxhLuc58cA1WqGDQPzuG8I+sPm1/p0CdgpzVTaKW408k5DNn8w==
  dependencies:
    "@babel/runtime" "^7.11.2"
    global "^4.4.0"

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/namespace-emitter/-/namespace-emitter-2.0.1.tgz"
  integrity sha512-N/sMKHniSDJBjfrkbS/tpkPj4RAbvW3mr8UAzvlMHyun93XEm83IAvhWtJVHo+RHn/oO8Job5YN4b+wRjSVp5g==

nan@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/nan/download/nan-1.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnan%2Fdownload%2Fnan-1.0.0.tgz"
  integrity sha1-riT4hQgY1mL8q1rPfzuVv6oszzg=

nanoid@^3.1.25, nanoid@^3.2.0:
  version "3.3.7"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

nanomatch@^1.2.1, nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.nlark.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natives@^1.1.3:
  version "1.1.6"
  resolved "https://registry.npmmirror.com/natives/download/natives-1.1.6.tgz"
  integrity sha1-pgO0pJirdxc2ErnqGs3sTZgPALs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@^2.5.2:
  version "2.9.1"
  resolved "https://registry.npmmirror.com/needle/-/needle-2.9.1.tgz"
  integrity sha512-6R9fqJ5Zcmf+uYaFgdIHmLwNldn5HbK8L5ybn7Uz+ylX/rnOsSp1AHcvQSrCaFN+qNM1wpymHqD7mVasEOlHGQ==
  dependencies:
    debug "^3.2.6"
    iconv-lite "^0.4.4"
    sax "^1.2.4"

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

neo-async@^2.5.0, neo-async@^2.6.0, neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.nlark.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

netmask@~1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/netmask/download/netmask-1.0.6.tgz"
  integrity sha1-ICl+idhvb2QA8lDZ9Pa0wZRfzTU=

next-tick@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/next-tick/-/next-tick-1.1.0.tgz"
  integrity sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==

nightwatch@^0.9.12:
  version "0.9.21"
  resolved "https://registry.npmmirror.com/nightwatch/download/nightwatch-0.9.21.tgz"
  integrity sha1-nnlKdRS0/V9GYC02jlBRUjKrnpA=
  dependencies:
    chai-nightwatch "~0.1.x"
    ejs "2.5.7"
    lodash.clone "3.0.3"
    lodash.defaultsdeep "4.3.2"
    minimatch "3.0.3"
    mkpath "1.0.0"
    mocha-nightwatch "3.2.2"
    optimist "0.6.1"
    proxy-agent "2.0.0"
    q "1.4.1"

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-cache@^4.1.1:
  version "4.2.1"
  resolved "https://registry.npm.taobao.org/node-cache/download/node-cache-4.2.1.tgz"
  integrity sha1-79hHTe5O3sQTjN3tWA9VFlAPczQ=
  dependencies:
    clone "2.x"
    lodash "^4.17.15"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "https://registry.npm.taobao.org/node-forge/download/node-forge-0.10.0.tgz?cache=0&sync_timestamp=1599054189018&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-forge%2Fdownload%2Fnode-forge-0.10.0.tgz"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/node-int64/download/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-libs-browser@^2.0.0:
  version "2.2.1"
  resolved "https://registry.nlark.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-notifier@^5.1.2, node-notifier@^5.2.1:
  version "5.4.5"
  resolved "https://registry.nlark.com/node-notifier/download/node-notifier-5.4.5.tgz?cache=0&sync_timestamp=1621962189467&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-notifier%2Fdownload%2Fnode-notifier-5.4.5.tgz"
  integrity sha1-DLwaKw9lhJO0Ald1oTrZOOlgke8=
  dependencies:
    growly "^1.3.0"
    is-wsl "^1.1.0"
    semver "^5.5.0"
    shellwords "^0.1.1"
    which "^1.3.0"

node-releases@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/node-releases/download/node-releases-2.0.1.tgz?cache=0&sync_timestamp=1634806960337&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-releases%2Fdownload%2Fnode-releases-2.0.1.tgz"
  integrity sha1-PR05XyBPHy8ppUNYuftnh2WtL8U=

node-xlsx@^0.17.1:
  version "0.17.2"
  resolved "https://registry.nlark.com/node-xlsx/download/node-xlsx-0.17.2.tgz"
  integrity sha1-KGIVr6Y+CW1TF6fLDl1ZnPofe2I=
  dependencies:
    "@babel/runtime" "^7.15.4"
    buffer-from "^1.1.2"
    xlsx "^0.17.2"

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/nopt/download/nopt-5.0.0.tgz"
  integrity sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.5.0"
  resolved "https://registry.nlark.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1629301872905&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^1.4.0:
  version "1.9.1"
  resolved "https://registry.nlark.com/normalize-url/download/normalize-url-1.9.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-url%2Fdownload%2Fnormalize-url-1.9.1.tgz"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/normalize-url/download/normalize-url-3.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-url%2Fdownload%2Fnormalize-url-3.3.0.tgz"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

normalize-wheel@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/normalize-wheel/download/normalize-wheel-1.0.1.tgz"
  integrity sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/nth-check/download/nth-check-1.0.2.tgz"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/nth-check/download/nth-check-2.0.1.tgz"
  integrity sha1-Lv4WL1w9oGoolZ+9PbddvuqfD8I=
  dependencies:
    boolbase "^1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/number-is-nan/download/number-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

numerify@1.2.9:
  version "1.2.9"
  resolved "https://registry.npmmirror.com/numerify/-/numerify-1.2.9.tgz"
  integrity sha512-X4QzQiytV5ZN3TVLhzbtFzjTarUNnaa1pgNDFqt7u7Nqhxe7FvY2eYrGt4WYHlYXDqgtfC/n/a5nJ2y0LijV8w==

nwsapi@^2.0.7:
  version "2.2.0"
  resolved "https://registry.nlark.com/nwsapi/download/nwsapi-2.2.0.tgz"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.nlark.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/object-assign/-/object-assign-2.1.1.tgz"
  integrity sha512-CdsOUYIh5wIiozhJ3rLQgmUTgcyzFwZZrqhkKhODMoGtPKM+wt0h0CNIoauJWMsS9822EdzPsF/6mb4nLvPN5g==

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&sync_timestamp=1618847043548&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://registry.nlark.com/object-hash/download/object-hash-1.3.1.tgz"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.11.0, object-inspect@^1.9.0:
  version "1.11.1"
  resolved "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.1.tgz"
  integrity sha512-If7BjFlpkzzBeV1cqgT3OSWT3azyoxDGajR+iGnFBfVV2EWyDyWaZZW2ERDjUaY2QM8i5jI3Sj7mhsM4DDAqWA==

object-inspect@^1.13.1:
  version "1.13.1"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz"
  integrity sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==

object-is@^1.0.1:
  version "1.1.5"
  resolved "https://registry.npm.taobao.org/object-is/download/object-is-1.1.5.tgz?cache=0&sync_timestamp=1613857698573&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-is%2Fdownload%2Fobject-is-1.1.5.tgz"
  integrity sha1-ud7qpfx/GEag+uzc7sE45XePU6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/object.assign/download/object.assign-4.1.2.tgz"
  integrity sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.getownpropertydescriptors@^2.1.0, object.getownpropertydescriptors@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.3.tgz?cache=0&sync_timestamp=1633321770416&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.3.tgz"
  integrity sha1-siPPOOF/77l6Y8EMkd9yzLOG354=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/object.omit/download/object.omit-2.0.1.tgz"
  integrity sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0, object.values@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/object.values/download/object.values-1.1.5.tgz"
  integrity sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/obuf/download/obuf-1.1.2.tgz"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/on-headers/download/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

opener@^1.4.3:
  version "1.5.2"
  resolved "https://registry.npm.taobao.org/opener/download/opener-1.5.2.tgz"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

opn@^5.1.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/opn/download/opn-5.5.0.tgz"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optimist@0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/optimist/download/optimist-0.6.1.tgz"
  integrity sha1-2j6nRob6IaGaERwybpDrFaAZZoY=
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

optimize-css-assets-webpack-plugin@^3.2.0:
  version "3.2.1"
  resolved "https://registry.nlark.com/optimize-css-assets-webpack-plugin/download/optimize-css-assets-webpack-plugin-3.2.1.tgz?cache=0&sync_timestamp=1624441067275&other_urls=https%3A%2F%2Fregistry.nlark.com%2Foptimize-css-assets-webpack-plugin%2Fdownload%2Foptimize-css-assets-webpack-plugin-3.2.1.tgz"
  integrity sha1-nRhlSg4FjAkL3ZkbBLyw9vJIZXM=
  dependencies:
    cssnano "^4.1.10"
    last-call-webpack-plugin "^2.1.2"

optionator@^0.8.1, optionator@^0.8.2:
  version "0.8.3"
  resolved "https://registry.nlark.com/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

options@>=0.0.5:
  version "0.0.6"
  resolved "https://registry.nlark.com/options/download/options-0.0.6.tgz"
  integrity sha1-7CLTEoBrtT5zF3Pnza788cZDEo8=

ora@^1.2.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/ora/download/ora-1.4.0.tgz?cache=0&sync_timestamp=1631556430183&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fora%2Fdownload%2Fora-1.4.0.tgz"
  integrity sha1-iERYIVs6XUCXWSKF+TMhu3p54uU=
  dependencies:
    chalk "^2.1.0"
    cli-cursor "^2.1.0"
    cli-spinners "^1.0.1"
    log-symbols "^2.1.0"

original@>=0.0.5:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/original/download/original-1.0.2.tgz"
  integrity sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/os-browserify/download/os-browserify-0.3.0.tgz"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-locale@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/os-locale/download/os-locale-1.4.0.tgz?cache=0&sync_timestamp=1633618260196&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fos-locale%2Fdownload%2Fos-locale-1.4.0.tgz"
  integrity sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=
  dependencies:
    lcid "^1.0.0"

os-locale@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/os-locale/download/os-locale-2.1.0.tgz?cache=0&sync_timestamp=1633618260196&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fos-locale%2Fdownload%2Fos-locale-2.1.0.tgz"
  integrity sha1-QrwpAKa1uL0XN2yOiCtlr8zyS/I=
  dependencies:
    execa "^0.7.0"
    lcid "^1.0.0"
    mem "^1.1.0"

os-tmpdir@^1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^1.0.0, p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/p-limit/download/p-limit-1.3.0.tgz?cache=0&sync_timestamp=1628812766275&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-limit%2Fdownload%2Fp-limit-1.3.0.tgz"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-1.2.0.tgz"
  integrity sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-1.0.0.tgz?cache=0&sync_timestamp=1633364462890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-try%2Fdownload%2Fp-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

pac-proxy-agent@1:
  version "1.1.0"
  resolved "https://registry.nlark.com/pac-proxy-agent/download/pac-proxy-agent-1.1.0.tgz"
  integrity sha1-NKOF399h0vDsrOCIWMdF0+eR/U0=
  dependencies:
    agent-base "2"
    debug "2"
    extend "3"
    get-uri "2"
    http-proxy-agent "1"
    https-proxy-agent "1"
    pac-resolver "~2.0.0"
    raw-body "2"
    socks-proxy-agent "2"

pac-resolver@~2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/pac-resolver/download/pac-resolver-2.0.0.tgz"
  integrity sha1-mbiNLxk/ve78HJpSnB8yYKtSd80=
  dependencies:
    co "~3.0.6"
    degenerator "~1.0.2"
    ip "1.0.1"
    netmask "~1.0.4"
    thunkify "~2.1.1"

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.nlark.com/pako/download/pako-1.0.11.tgz"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.2.0.tgz"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://registry.nlark.com/param-case/download/param-case-2.1.1.tgz"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parse-asn1@^5.0.0:
  version "5.1.6"
  resolved "https://registry.nlark.com/parse-asn1/download/parse-asn1-5.1.6.tgz"
  integrity sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-asn1@^5.1.7:
  version "5.1.7"
  resolved "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.7.tgz"
  integrity sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==
  dependencies:
    asn1.js "^4.10.1"
    browserify-aes "^1.2.0"
    evp_bytestokey "^1.0.3"
    hash-base "~3.0"
    pbkdf2 "^3.1.2"
    safe-buffer "^5.2.1"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/parse-glob/download/parse-glob-3.0.4.tgz"
  integrity sha1-ssN2z7EfNVE7rdFz7wu246OIORw=
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-headers@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/parse-headers/-/parse-headers-2.0.5.tgz"
  integrity sha512-ft3iAoLOB/MlwbNXgzy43SWGP6sQki2jQvAyBg/zDFAgr9bfNWZIUj42Kw2eJIl8kEi4PbgE6U1Zau/HwI75HA==

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-2.2.0.tgz"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-node-version@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parse-node-version/-/parse-node-version-1.0.1.tgz"
  integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==

parse5@4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/parse5/download/parse5-4.0.0.tgz"
  integrity sha1-bXhlbj2o14tOwLkG98CO8d/j9gg=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npm.taobao.org/parseurl/download/parseurl-1.3.3.tgz?cache=0&sync_timestamp=1599054201722&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparseurl%2Fdownload%2Fparseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

particles.js@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/particles.js/download/particles.js-2.0.0.tgz"
  integrity sha1-IThsQyjWx/lngKIB6W7t/AnHNvY=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/path-browserify/download/path-browserify-0.0.1.tgz"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-2.1.0.tgz"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/path-is-inside/download/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1617971695678&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.5, path-parse@^1.0.6:
  version "1.0.7"
  resolved "https://registry.nlark.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.nlark.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz?cache=0&sync_timestamp=1618846809278&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/path-type/download/path-type-1.1.0.tgz?cache=0&sync_timestamp=1611752107592&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-1.1.0.tgz"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/path-type/download/path-type-2.0.0.tgz?cache=0&sync_timestamp=1611752107592&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-2.0.0.tgz"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz?cache=0&sync_timestamp=1611752107592&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-3.0.0.tgz"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

pbkdf2@^3.0.3, pbkdf2@^3.1.2:
  version "3.1.2"
  resolved "https://registry.nlark.com/pbkdf2/download/pbkdf2-3.1.2.tgz"
  integrity sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pdfjs-dist@2.6.347:
  version "2.6.347"
  resolved "https://registry.npmmirror.com/pdfjs-dist/-/pdfjs-dist-2.6.347.tgz"
  integrity sha512-QC+h7hG2su9v/nU1wEI3SnpPIrqJODL7GTDFvR74ANKGq1AFJW16PH8VWnhpiTi9YcLSFV9xLeWSgq+ckHLdVQ==

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/pend/download/pend-1.2.0.tgz"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/picocolors/download/picocolors-0.2.1.tgz?cache=0&sync_timestamp=1634093442271&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-0.2.1.tgz"
  integrity sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/download/picocolors-1.0.0.tgz?cache=0&sync_timestamp=1634093442271&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-1.0.0.tgz"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.3.0"
  resolved "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz?cache=0&sync_timestamp=1621648246651&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpicomatch%2Fdownload%2Fpicomatch-2.3.0.tgz"
  integrity sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz"
  integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz?cache=0&sync_timestamp=1618847023792&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpinkie-promise%2Fdownload%2Fpinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkcs7@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmmirror.com/pkcs7/-/pkcs7-0.2.3.tgz"
  integrity sha512-kJRwmADEQUg+qJyRgWLtpEL9q9cFjZschejTEK3GRjKvnsU9G5WWoe/wKqRgbBoqWdVSeTUKP6vIA3Y72M3rWA==

pkcs7@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/pkcs7/-/pkcs7-1.0.4.tgz"
  integrity sha512-afRERtHn54AlwaF2/+LFszyAANTCggGilmcmILUzEjvs3XgFZT+xE6+QWQcAGmu4xajy+Xtj7acLOPdx5/eXWQ==
  dependencies:
    "@babel/runtime" "^7.5.5"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-1.0.0.tgz?cache=0&sync_timestamp=1633498133295&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-1.0.0.tgz"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-2.0.0.tgz?cache=0&sync_timestamp=1633498133295&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-2.0.0.tgz"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pl-table@^2.7.5:
  version "2.7.5"
  resolved "https://registry.npmmirror.com/pl-table/-/pl-table-2.7.5.tgz"
  integrity sha512-uzFetO3nEF8C40cADJpAJPu297X/6kuOqrP8XIPsWRay4mBgNt3FSokg6s/3wpnrLdXwkz6i9FscjkEaM0A7Pw==
  dependencies:
    async-validator "~1.8.1"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    plxy-grid "^2.8.3"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"
    vuedraggable "^2.23.2"
    xe-utils "^2.3.0"

pluralize@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/pluralize/download/pluralize-7.0.0.tgz"
  integrity sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c=

plxy-grid@^2.8.3:
  version "2.8.3"
  resolved "https://registry.npmmirror.com/plxy-grid/-/plxy-grid-2.8.3.tgz"
  integrity sha512-udl+J0sCjTyLJScMVnAfSTvyqyd9VbNlZ76ywMTqaKZKLnGykuOaUr77YLKH6OMgfBoX30MftjRticvjNca98g==

pn@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/pn/download/pn-1.1.0.tgz"
  integrity sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=

popper.js@^1.14.6:
  version "1.16.1"
  resolved "https://registry.npm.taobao.org/popper.js/download/popper.js-1.16.1.tgz"
  integrity sha1-KiI8s9x7YhPXQOQDcr5A3kPmWxs=

portfinder@^1.0.13, portfinder@^1.0.9:
  version "1.0.28"
  resolved "https://registry.nlark.com/portfinder/download/portfinder-1.0.28.tgz"
  integrity sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^5.2.0:
  version "5.3.1"
  resolved "https://registry.nlark.com/postcss-calc/download/postcss-calc-5.3.1.tgz"
  integrity sha1-d7rnypKK2FcW4v2kLyYb98HWW14=
  dependencies:
    postcss "^5.0.2"
    postcss-message-helpers "^2.0.0"
    reduce-css-calc "^1.2.6"

postcss-calc@^7.0.1:
  version "7.0.5"
  resolved "https://registry.nlark.com/postcss-calc/download/postcss-calc-7.0.5.tgz"
  integrity sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4=
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-colormin@^2.1.8:
  version "2.2.2"
  resolved "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-2.2.2.tgz"
  integrity sha1-ZjFBfV8OkJo9fsJrJMio0eT5bks=
  dependencies:
    colormin "^1.0.5"
    postcss "^5.0.13"
    postcss-value-parser "^3.2.3"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^2.3.4:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-2.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-convert-values%2Fdownload%2Fpostcss-convert-values-2.6.1.tgz"
  integrity sha1-u9hZPFwf0uPRwyK7kl3K6Nrk1i0=
  dependencies:
    postcss "^5.0.11"
    postcss-value-parser "^3.1.2"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-convert-values%2Fdownload%2Fpostcss-convert-values-4.0.1.tgz"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/postcss-discard-comments/download/postcss-discard-comments-2.0.4.tgz?cache=0&sync_timestamp=1621449558287&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-comments%2Fdownload%2Fpostcss-discard-comments-2.0.4.tgz"
  integrity sha1-vv6J+v1bPazlzM5Rt2uBUUvgDj0=
  dependencies:
    postcss "^5.0.14"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz?cache=0&sync_timestamp=1621449558287&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-comments%2Fdownload%2Fpostcss-discard-comments-4.0.2.tgz"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^2.0.1:
  version "2.1.0"
  resolved "https://registry.nlark.com/postcss-discard-duplicates/download/postcss-discard-duplicates-2.1.0.tgz?cache=0&sync_timestamp=1621449558296&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-duplicates%2Fdownload%2Fpostcss-discard-duplicates-2.1.0.tgz"
  integrity sha1-uavye4isGIFYpesSq8riAmO5GTI=
  dependencies:
    postcss "^5.0.4"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz?cache=0&sync_timestamp=1621449558296&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-duplicates%2Fdownload%2Fpostcss-discard-duplicates-4.0.2.tgz"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^2.0.1:
  version "2.1.0"
  resolved "https://registry.nlark.com/postcss-discard-empty/download/postcss-discard-empty-2.1.0.tgz?cache=0&sync_timestamp=1621449733074&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-empty%2Fdownload%2Fpostcss-discard-empty-2.1.0.tgz"
  integrity sha1-0rS9nVztXr2Nyt52QMfXzX9PkrU=
  dependencies:
    postcss "^5.0.14"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz?cache=0&sync_timestamp=1621449733074&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-empty%2Fdownload%2Fpostcss-discard-empty-4.0.1.tgz"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/postcss-discard-overridden/download/postcss-discard-overridden-0.1.1.tgz?cache=0&sync_timestamp=1621449732464&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-overridden%2Fdownload%2Fpostcss-discard-overridden-0.1.1.tgz"
  integrity sha1-ix6vVU9ob7KIzYdMVWZ7CqNmjVg=
  dependencies:
    postcss "^5.0.16"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz?cache=0&sync_timestamp=1621449732464&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-overridden%2Fdownload%2Fpostcss-discard-overridden-4.0.1.tgz"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-discard-unused@^2.2.1:
  version "2.2.3"
  resolved "https://registry.nlark.com/postcss-discard-unused/download/postcss-discard-unused-2.2.3.tgz?cache=0&sync_timestamp=1621449732186&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-unused%2Fdownload%2Fpostcss-discard-unused-2.2.3.tgz"
  integrity sha1-vOMLLMWR/8Y0Mitfs0ZLbZNPRDM=
  dependencies:
    postcss "^5.0.14"
    uniqs "^2.0.0"

postcss-filter-plugins@^2.0.0:
  version "2.0.3"
  resolved "https://registry.nlark.com/postcss-filter-plugins/download/postcss-filter-plugins-2.0.3.tgz"
  integrity sha1-giRf34IzcEFkXkdxFNjlk6oYuOw=
  dependencies:
    postcss "^5.0.4"

postcss-import@^11.0.0:
  version "11.1.0"
  resolved "https://registry.nlark.com/postcss-import/download/postcss-import-11.1.0.tgz"
  integrity sha1-Vck2LJGSmU7GiGXSJEGd8dspgfA=
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.2.3"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-load-config@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/postcss-load-config/download/postcss-load-config-1.2.0.tgz"
  integrity sha1-U56a/J3chiASHr+djDZz4M5Q0oo=
  dependencies:
    cosmiconfig "^2.1.0"
    object-assign "^4.1.0"
    postcss-load-options "^1.2.0"
    postcss-load-plugins "^2.3.0"

postcss-load-config@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/postcss-load-config/download/postcss-load-config-2.1.2.tgz"
  integrity sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-load-options@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/postcss-load-options/download/postcss-load-options-1.2.0.tgz"
  integrity sha1-sJixVZ3awt8EvAuzdfmaXP4rbYw=
  dependencies:
    cosmiconfig "^2.1.0"
    object-assign "^4.1.0"

postcss-load-plugins@^2.3.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/postcss-load-plugins/download/postcss-load-plugins-2.3.0.tgz"
  integrity sha1-dFdoEWWZrKLwCfrUJrABdQSdjZI=
  dependencies:
    cosmiconfig "^2.1.1"
    object-assign "^4.1.0"

postcss-loader@^2.0.8:
  version "2.1.6"
  resolved "https://registry.npmmirror.com/postcss-loader/download/postcss-loader-2.1.6.tgz"
  integrity sha1-HX3XsXxrojS5vtWvE+C+pApC10A=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^6.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^0.4.0"

postcss-merge-idents@^2.1.5:
  version "2.1.7"
  resolved "https://registry.nlark.com/postcss-merge-idents/download/postcss-merge-idents-2.1.7.tgz"
  integrity sha1-TFUwMTwI4dWzu/PSu8dH4njuonA=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.10"
    postcss-value-parser "^3.1.1"

postcss-merge-longhand@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-2.0.2.tgz?cache=0&sync_timestamp=1637084927935&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-longhand%2Fdownload%2Fpostcss-merge-longhand-2.0.2.tgz"
  integrity sha1-I9kM0Sewp3mUkVMyc5A0oaTz1lg=
  dependencies:
    postcss "^5.0.4"

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz?cache=0&sync_timestamp=1637084927935&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-longhand%2Fdownload%2Fpostcss-merge-longhand-4.0.11.tgz"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^2.0.3:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-2.1.2.tgz?cache=0&sync_timestamp=1637085444662&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-2.1.2.tgz"
  integrity sha1-0d9d+qexrMO+VT8OnhDofGG19yE=
  dependencies:
    browserslist "^1.5.2"
    caniuse-api "^1.5.2"
    postcss "^5.0.4"
    postcss-selector-parser "^2.2.2"
    vendors "^1.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz?cache=0&sync_timestamp=1637085444662&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-4.0.3.tgz"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-message-helpers@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/postcss-message-helpers/download/postcss-message-helpers-2.0.0.tgz"
  integrity sha1-pPL0+rbk/gAvCu0ABHjN9S+bpg4=

postcss-minify-font-values@^1.0.2:
  version "1.0.5"
  resolved "https://registry.nlark.com/postcss-minify-font-values/download/postcss-minify-font-values-1.0.5.tgz?cache=0&sync_timestamp=1621449734134&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-font-values%2Fdownload%2Fpostcss-minify-font-values-1.0.5.tgz"
  integrity sha1-S1jttWZB66fIR0qzUmyv17vey2k=
  dependencies:
    object-assign "^4.0.1"
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz?cache=0&sync_timestamp=1621449734134&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-font-values%2Fdownload%2Fpostcss-minify-font-values-4.0.2.tgz"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-1.0.5.tgz"
  integrity sha1-Xb2hE3NwP4PPtKPqOIHY11/15uE=
  dependencies:
    postcss "^5.0.12"
    postcss-value-parser "^3.3.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^1.0.4:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-1.2.2.tgz?cache=0&sync_timestamp=1637084835630&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-params%2Fdownload%2Fpostcss-minify-params-1.2.2.tgz"
  integrity sha1-rSzgcTc7lDs9kwo/pZo1jCjW8fM=
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.2"
    postcss-value-parser "^3.0.2"
    uniqs "^2.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz?cache=0&sync_timestamp=1637084835630&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-params%2Fdownload%2Fpostcss-minify-params-4.0.2.tgz"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^2.0.4:
  version "2.1.1"
  resolved "https://registry.nlark.com/postcss-minify-selectors/download/postcss-minify-selectors-2.1.1.tgz?cache=0&sync_timestamp=1621449558355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-selectors%2Fdownload%2Fpostcss-minify-selectors-2.1.1.tgz"
  integrity sha1-ssapjAByz5G5MtGkllCBFDEXNb8=
  dependencies:
    alphanum-sort "^1.0.2"
    has "^1.0.1"
    postcss "^5.0.14"
    postcss-selector-parser "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz?cache=0&sync_timestamp=1621449558355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-selectors%2Fdownload%2Fpostcss-minify-selectors-4.0.2.tgz"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^1.2.0:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz?cache=0&sync_timestamp=1602588245463&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-extract-imports%2Fdownload%2Fpostcss-modules-extract-imports-1.2.1.tgz"
  integrity sha1-3IfjQUjsfqtfeR981YSYMzdbdBo=
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz"
  integrity sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz"
  integrity sha1-1upkmUx5+XtipytCb75gVqGUu5A=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz"
  integrity sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-normalize-charset@^1.1.0:
  version "1.1.1"
  resolved "https://registry.nlark.com/postcss-normalize-charset/download/postcss-normalize-charset-1.1.1.tgz?cache=0&sync_timestamp=1621449558308&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-charset%2Fdownload%2Fpostcss-normalize-charset-1.1.1.tgz"
  integrity sha1-757nEhLX/nWceO0WL2HtYrXLk/E=
  dependencies:
    postcss "^5.0.5"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz?cache=0&sync_timestamp=1621449558308&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-charset%2Fdownload%2Fpostcss-normalize-charset-4.0.1.tgz"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz?cache=0&sync_timestamp=1621449652268&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-display-values%2Fdownload%2Fpostcss-normalize-display-values-4.0.2.tgz"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz?cache=0&sync_timestamp=1621449826472&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-positions%2Fdownload%2Fpostcss-normalize-positions-4.0.2.tgz"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz?cache=0&sync_timestamp=1621449651580&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-repeat-style%2Fdownload%2Fpostcss-normalize-repeat-style-4.0.2.tgz"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz?cache=0&sync_timestamp=1621449646930&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-string%2Fdownload%2Fpostcss-normalize-string-4.0.2.tgz"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz?cache=0&sync_timestamp=1621449827577&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-timing-functions%2Fdownload%2Fpostcss-normalize-timing-functions-4.0.2.tgz"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz?cache=0&sync_timestamp=1621449825612&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-unicode%2Fdownload%2Fpostcss-normalize-unicode-4.0.1.tgz"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^3.0.7:
  version "3.0.8"
  resolved "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-3.0.8.tgz?cache=0&sync_timestamp=1637084930240&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-normalize-url%2Fdownload%2Fpostcss-normalize-url-3.0.8.tgz"
  integrity sha1-EI90s/L82viRov+j6kWSJ5/HgiI=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^1.4.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz?cache=0&sync_timestamp=1637084930240&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-normalize-url%2Fdownload%2Fpostcss-normalize-url-4.0.1.tgz"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz?cache=0&sync_timestamp=1621449646853&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-whitespace%2Fdownload%2Fpostcss-normalize-whitespace-4.0.2.tgz"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^2.1.0:
  version "2.2.3"
  resolved "https://registry.nlark.com/postcss-ordered-values/download/postcss-ordered-values-2.2.3.tgz?cache=0&sync_timestamp=1623330460500&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-ordered-values%2Fdownload%2Fpostcss-ordered-values-2.2.3.tgz"
  integrity sha1-7sbCpntsQSqNsgQud/6NpD+VwR0=
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.1"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz?cache=0&sync_timestamp=1623330460500&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-ordered-values%2Fdownload%2Fpostcss-ordered-values-4.1.2.tgz"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-prefix-selector@^1.6.0:
  version "1.14.0"
  resolved "https://registry.npmmirror.com/postcss-prefix-selector/download/postcss-prefix-selector-1.14.0.tgz"
  integrity sha512-8d5fiBQZWMtGWH/7ewEeo6RnBNyT2kLD5wTIfV2oHYqH4hjiofg/rP5X3SUwnqOINzE4mM/K/UOAiNrIaKzd4w==

postcss-reduce-idents@^2.2.2:
  version "2.4.0"
  resolved "https://registry.nlark.com/postcss-reduce-idents/download/postcss-reduce-idents-2.4.0.tgz?cache=0&sync_timestamp=1621449563205&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-reduce-idents%2Fdownload%2Fpostcss-reduce-idents-2.4.0.tgz"
  integrity sha1-wsbSDMlYKE9qv75j92Cb9AkFmtM=
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-reduce-initial@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/postcss-reduce-initial/download/postcss-reduce-initial-1.0.1.tgz"
  integrity sha1-aPgGlfBF0IJjqHmtJA343WT2ROo=
  dependencies:
    postcss "^5.0.4"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/postcss-reduce-transforms/download/postcss-reduce-transforms-1.0.4.tgz?cache=0&sync_timestamp=1621449730895&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-reduce-transforms%2Fdownload%2Fpostcss-reduce-transforms-1.0.4.tgz"
  integrity sha1-/3b02CEkN7McKYpC0uFEQCV3GuE=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.8"
    postcss-value-parser "^3.0.1"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz?cache=0&sync_timestamp=1621449730895&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-reduce-transforms%2Fdownload%2Fpostcss-reduce-transforms-4.0.2.tgz"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-selector-parser@^2.0.0, postcss-selector-parser@^2.2.2:
  version "2.2.3"
  resolved "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-2.2.3.tgz?cache=0&sync_timestamp=1620752939806&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-2.2.3.tgz"
  integrity sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A=
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz"
  integrity sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.2:
  version "6.0.7"
  resolved "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-6.0.7.tgz"
  integrity sha512-U+b/Deoi4I/UmE6KOVPpnhS7I7AYdKbhGcat+qTQ27gycvaACvNEw11ba6RrkwVmDVRW7sigWgLj4/KbbJjeDA==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^2.1.1:
  version "2.1.6"
  resolved "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-2.1.6.tgz"
  integrity sha1-tt8YqmE7Zm4TPwittSGcJoSsEI0=
  dependencies:
    is-svg "^2.0.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"
    svgo "^0.7.0"

postcss-svgo@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-4.0.3.tgz"
  integrity sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-unique-selectors@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-2.0.2.tgz?cache=0&sync_timestamp=1637084927333&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-2.0.2.tgz"
  integrity sha1-mB1X0p3csz57Hf4f1DuGSfkzyh0=
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz?cache=0&sync_timestamp=1637084927333&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-4.0.1.tgz"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-url@^7.2.1:
  version "7.3.2"
  resolved "https://registry.nlark.com/postcss-url/download/postcss-url-7.3.2.tgz"
  integrity sha1-X+onOAf7hLOMRhw8mp6KvSNfcSA=
  dependencies:
    mime "^1.4.1"
    minimatch "^3.0.4"
    mkdirp "^0.5.0"
    postcss "^6.0.1"
    xxhashjs "^0.2.1"

postcss-value-parser@^3.0.0, postcss-value-parser@^3.0.1, postcss-value-parser@^3.0.2, postcss-value-parser@^3.1.1, postcss-value-parser@^3.1.2, postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^4.0.2:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss-zindex@^2.0.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/postcss-zindex/download/postcss-zindex-2.2.0.tgz?cache=0&sync_timestamp=1621449734732&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-zindex%2Fdownload%2Fpostcss-zindex-2.2.0.tgz"
  integrity sha1-0hCd3AVbka9n/EyzsCWUZjnSryI=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss@^5.0.10:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.11:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.12:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.13:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.14, postcss@^5.2.16:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.16:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.2:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.4:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.5:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.6:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.8:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.2.17:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0.0:
  version "6.0.23"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-6.0.23.tgz"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.1:
  version "6.0.23"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-6.0.23.tgz"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.17:
  version "6.0.23"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-6.0.23.tgz"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.8:
  version "6.0.23"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-6.0.23.tgz"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^7.0.0, postcss@^7.0.27:
  version "7.0.39"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-7.0.39.tgz"
  integrity sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.1:
  version "7.0.39"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-7.0.39.tgz"
  integrity sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

posthtml-parser@^0.2.0, posthtml-parser@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/posthtml-parser/download/posthtml-parser-0.2.1.tgz?cache=0&sync_timestamp=1630307606138&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fposthtml-parser%2Fdownload%2Fposthtml-parser-0.2.1.tgz"
  integrity sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=
  dependencies:
    htmlparser2 "^3.8.3"
    isobject "^2.1.0"

posthtml-rename-id@^1.0:
  version "1.0.12"
  resolved "https://registry.nlark.com/posthtml-rename-id/download/posthtml-rename-id-1.0.12.tgz"
  integrity sha1-z39us3FGvxr6wx5o8YxswZrmFDM=
  dependencies:
    escape-string-regexp "1.0.5"

posthtml-render@^1.0.5, posthtml-render@^1.0.6:
  version "1.4.0"
  resolved "https://registry.nlark.com/posthtml-render/download/posthtml-render-1.4.0.tgz"
  integrity sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM=

posthtml-svg-mode@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/posthtml-svg-mode/download/posthtml-svg-mode-1.0.3.tgz"
  integrity sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=
  dependencies:
    merge-options "1.0.1"
    posthtml "^0.9.2"
    posthtml-parser "^0.2.1"
    posthtml-render "^1.0.6"

posthtml@^0.9.2:
  version "0.9.2"
  resolved "https://registry.nlark.com/posthtml/download/posthtml-0.9.2.tgz"
  integrity sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=
  dependencies:
    posthtml-parser "^0.2.0"
    posthtml-render "^1.0.5"

preact@^10.5.13:
  version "10.10.6"
  resolved "https://registry.npmjs.org/preact/-/preact-10.10.6.tgz"
  integrity sha512-w0mCL5vICUAZrh1DuHEdOWBjxdO62lvcO++jbzr8UhhYcTbFkpegLH9XX+7MadjTl/y0feoqwQ/zAnzkc/EGog==

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://registry.nlark.com/prepend-http/download/prepend-http-1.0.4.tgz?cache=0&sync_timestamp=1628547439455&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprepend-http%2Fdownload%2Fprepend-http-1.0.4.tgz"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/preserve/download/preserve-0.2.0.tgz"
  integrity sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=

prettier@^1.7.0:
  version "1.19.1"
  resolved "https://registry.npmmirror.com/prettier/download/prettier-1.19.1.tgz"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

pretty-error@^2.0.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/pretty-error/download/pretty-error-2.1.2.tgz"
  integrity sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=
  dependencies:
    lodash "^4.17.20"
    renderkid "^2.0.4"

pretty-format@^22.4.0, pretty-format@^22.4.3:
  version "22.4.3"
  resolved "https://registry.npmmirror.com/pretty-format/download/pretty-format-22.4.3.tgz"
  integrity sha1-+HPXgIOanALpZkyKCC6e556qwW8=
  dependencies:
    ansi-regex "^3.0.0"
    ansi-styles "^3.2.0"

pretty@2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/pretty/download/pretty-2.0.0.tgz"
  integrity sha1-rbx5YLe7/iiaVX3F9zdhmiINBqU=
  dependencies:
    condense-newlines "^0.2.1"
    extend-shallow "^2.0.1"
    js-beautify "^1.6.12"

printj@~1.1.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/printj/download/printj-1.1.2.tgz"
  integrity sha1-2Q3rKXWoufYA+zoclOP0xTx4oiI=

printj@~1.2.2:
  version "1.2.3"
  resolved "https://registry.nlark.com/printj/download/printj-1.2.3.tgz"
  integrity sha1-LPsrGSoeU4XbvltGZYrDSqgoUIo=

printj@~1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/printj/download/printj-1.3.0.tgz"
  integrity sha1-kBipGKeQ5DcH8QYl1uEBh6Nnz/Y=

prismjs@^1.23.0:
  version "1.29.0"
  resolved "https://registry.npmjs.org/prismjs/-/prismjs-1.29.0.tgz"
  integrity sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q==

private@^0.1.6, private@^0.1.8, private@~0.1.5:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/private/download/private-0.1.8.tgz"
  integrity sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npmmirror.com/process/-/process-0.11.10.tgz"
  integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==

process@~0.5.1:
  version "0.5.2"
  resolved "https://registry.npmmirror.com/process/-/process-0.5.2.tgz"
  integrity sha512-oNpcutj+nYX2FjdEW7PGltWhXulAnFlM0My/k48L90hARCOJtvBbQXc/6itV2jDvU5xAAtonP+r6wmQgCcbAUA==

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/progress/download/progress-2.0.3.tgz?cache=0&sync_timestamp=1599054255267&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprogress%2Fdownload%2Fprogress-2.0.3.tgz"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/promise-inflight/download/promise-inflight-1.0.1.tgz"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

proto-list@~1.2.1:
  version "1.2.4"
  resolved "https://registry.nlark.com/proto-list/download/proto-list-1.2.4.tgz"
  integrity sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk=

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.nlark.com/proxy-addr/download/proxy-addr-2.0.7.tgz"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-agent@2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/proxy-agent/download/proxy-agent-2.0.0.tgz?cache=0&sync_timestamp=1626290697042&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fproxy-agent%2Fdownload%2Fproxy-agent-2.0.0.tgz"
  integrity sha1-V+tTR6qAXXTsaByyVknbo5yTNJk=
  dependencies:
    agent-base "2"
    debug "2"
    extend "3"
    http-proxy-agent "1"
    https-proxy-agent "1"
    lru-cache "~2.6.5"
    pac-proxy-agent "1"
    socks-proxy-agent "2"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/prr/download/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://registry.npm.taobao.org/psl/download/psl-1.8.0.tgz"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0, pump@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-2.1.1.tgz"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.nlark.com/punycode/download/punycode-1.3.2.tgz"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

px2rem-loader@^0.1.9:
  version "0.1.9"
  resolved "https://registry.npmmirror.com/px2rem-loader/download/px2rem-loader-0.1.9.tgz"
  integrity sha512-3Ew8At5W/HHIIUe/KZk+FBRRb20KtgP1N1c/BnMlXk6LNkqrFmUIUF35GF/evzNdj/Q63iWJpkmn/c5qSMplRg==
  dependencies:
    loader-utils "^1.1.0"
    px2rem "^0.5.0"

px2rem@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.taobao.org/px2rem/download/px2rem-0.5.0.tgz"
  integrity sha1-JLOmz3TRSttO13byB4cdmJPkEOI=
  dependencies:
    chalk "~0.5.1"
    commander "~2.6.0"
    css "~2.2.0"
    extend "~3.0.0"
    fs-extra "~0.16.3"

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/q/download/q-1.5.1.tgz?cache=0&sync_timestamp=1599054212574&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fq%2Fdownload%2Fq-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

q@1.4.1:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/q/download/q-1.4.1.tgz?cache=0&sync_timestamp=1599054212574&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fq%2Fdownload%2Fq-1.4.1.tgz"
  integrity sha1-VXBbzZPF82c1MMLCy8DCs63cKG4=

qs@^6.12.0:
  version "6.12.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.12.0.tgz"
  integrity sha512-trVZiI6RMOkO476zLGaBIzszOdFPnCCXHPG9kn0yuS1uz6xdVxPfZdB3vUig9pxPFDM9BRAgz/YUIVQ1/vuiUg==
  dependencies:
    side-channel "^1.0.6"

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.npmmirror.com/qs/download/qs-6.5.2.tgz"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

qs@6.11.0:
  version "6.11.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz"
  integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
  dependencies:
    side-channel "^1.0.4"

query-string@^4.1.0, query-string@^4.3.2:
  version "4.3.4"
  resolved "https://registry.nlark.com/query-string/download/query-string-4.3.4.tgz?cache=0&sync_timestamp=1624297034221&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fquery-string%2Fdownload%2Fquery-string-4.3.4.tgz"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.nlark.com/querystring-es3/download/querystring-es3-0.2.1.tgz"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/querystringify/download/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

randomatic@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/randomatic/download/randomatic-3.1.1.tgz"
  integrity sha1-t3bvxZN1mE42xTey9RofCv8Noe0=
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/randomfill/download/randomfill-1.0.4.tgz"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.0.3, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.0.tgz?cache=0&sync_timestamp=1637116848060&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fraw-body%2Fdownload%2Fraw-body-2.4.0.tgz"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-loader@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/raw-loader/-/raw-loader-4.0.2.tgz"
  integrity sha512-ZnScIV3ag9A4wPX/ZayxL/jZH+euYb6FcUinPcgiQW0+UBtEv0O6Q3lGd3cqJ+GHH+rksEv3Pj99oxJ3u3VIKA==
  dependencies:
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/read-cache/download/read-cache-1.0.0.tgz"
  integrity sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=
  dependencies:
    pify "^2.3.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-2.0.0.tgz"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-1.1.0.tgz"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-2.0.0.tgz"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6, "readable-stream@1 || 2", readable-stream@2:
  version "2.3.7"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.3.8:
  version "2.3.8"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.0"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.1.1:
  version "3.6.0"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@1.1.x:
  version "1.1.14"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-1.1.14.tgz"
  integrity sha1-fPTFTvZI44EwhMY23SB54WbAgdk=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/readdirp/download/readdirp-2.2.1.tgz"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

realpath-native@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/realpath-native/download/realpath-native-1.1.0.tgz"
  integrity sha1-IAMpT+oj+wZy8kduviL89Jii1lw=
  dependencies:
    util.promisify "^1.0.0"

recast@^0.11.17:
  version "0.11.23"
  resolved "https://registry.npmmirror.com/recast/-/recast-0.11.23.tgz"
  integrity sha512-+nixG+3NugceyR8O1bLU45qs84JgI3+8EauyRZafLgC9XbdAOIVgwV1Pe2da0YzGo62KzWoZwUpVEQf6qNAXWA==
  dependencies:
    ast-types "0.9.6"
    esprima "~3.1.0"
    private "~0.1.5"
    source-map "~0.5.0"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz"
  integrity sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/redent/download/redent-1.0.0.tgz"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

reduce-css-calc@^1.2.6:
  version "1.3.0"
  resolved "https://registry.nlark.com/reduce-css-calc/download/reduce-css-calc-1.3.0.tgz"
  integrity sha1-dHyRTgSWFKTJz7umKYca0dKSdxY=
  dependencies:
    balanced-match "^0.4.2"
    math-expression-evaluator "^1.2.14"
    reduce-function-call "^1.0.1"

reduce-function-call@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/reduce-function-call/download/reduce-function-call-1.0.3.tgz"
  integrity sha1-YDUPf7JSwKZ+sQ/UaU0WkJlxMA8=
  dependencies:
    balanced-match "^1.0.0"

regenerate@^1.2.1:
  version "1.4.2"
  resolved "https://registry.npm.taobao.org/regenerate/download/regenerate-1.4.2.tgz?cache=0&sync_timestamp=1604218353677&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate%2Fdownload%2Fregenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz?cache=0&sync_timestamp=1626993001371&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.11.1.tgz"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.11:
  version "0.13.11"
  resolved "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.13.9.tgz?cache=0&sync_timestamp=1626993001371&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.13.9.tgz"
  integrity sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I=

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "https://registry.nlark.com/regenerator-transform/download/regenerator-transform-0.10.1.tgz?cache=0&sync_timestamp=1627057533376&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-transform%2Fdownload%2Fregenerator-transform-0.10.1.tgz"
  integrity sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0=
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "https://registry.nlark.com/regex-cache/download/regex-cache-0.4.4.tgz"
  integrity sha1-db3FiioUls7EihKDW8VMjVYjNt0=
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.1"
  resolved "https://registry.nlark.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz"
  integrity sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

regexpp@^1.0.1:
  version "1.1.0"
  resolved "https://registry.nlark.com/regexpp/download/regexpp-1.1.0.tgz"
  integrity sha1-DjUW3Qt5BPQT0tQZPc5GGMOmias=

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/regexpu-core/download/regexpu-core-2.0.0.tgz?cache=0&sync_timestamp=1631619113277&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpu-core%2Fdownload%2Fregexpu-core-2.0.0.tgz"
  integrity sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/regjsgen/download/regjsgen-0.2.0.tgz"
  integrity sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/regjsparser/download/regjsparser-0.1.5.tgz"
  integrity sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "https://registry.nlark.com/relateurl/download/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.nlark.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.4:
  version "2.0.7"
  resolved "https://registry.npmmirror.com/renderkid/download/renderkid-2.0.7.tgz"
  integrity sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^3.0.1"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.4.tgz"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/repeating/download/repeating-2.0.1.tgz"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request-promise-core@1.1.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/request-promise-core/download/request-promise-core-1.1.4.tgz"
  integrity sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=
  dependencies:
    lodash "^4.17.19"

request-promise-native@^1.0.5:
  version "1.0.9"
  resolved "https://registry.npmmirror.com/request-promise-native/download/request-promise-native-1.0.9.tgz"
  integrity sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=
  dependencies:
    request-promise-core "1.1.4"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@^2.87.0, request@^2.88.0:
  version "2.88.2"
  resolved "https://registry.npmmirror.com/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^1.1.0:
  version "1.2.1"
  resolved "https://registry.nlark.com/require-from-string/download/require-from-string-1.2.1.tgz"
  integrity sha1-UpyczvJzgK3+yaL5ZbZJu+5jZBg=

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz"
  integrity sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=

require-uncached@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/require-uncached/download/require-uncached-1.0.3.tgz"
  integrity sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-detector@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/resize-detector/-/resize-detector-0.3.0.tgz"
  integrity sha512-R/tCuvuOHQ8o2boRP6vgx8hXCCy87H1eY9V5imBYeVNyNVpuL9ciReSccLj2gDcax9+2weXy3bc8Vv+NRXeEvQ==

resize-observer-polyfill@^1.5.0:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-1.0.1.tgz"
  integrity sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-3.0.0.tgz"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.1.7, resolve@^1.10.0, resolve@^1.20.0, resolve@^1.3.3, resolve@^1.4.0:
  version "1.20.0"
  resolved "https://registry.nlark.com/resolve/download/resolve-1.20.0.tgz"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

resolve@1.1.7:
  version "1.1.7"
  resolved "https://registry.nlark.com/resolve/download/resolve-1.1.7.tgz"
  integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-2.0.0.tgz?cache=0&sync_timestamp=1629746923086&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frestore-cursor%2Fdownload%2Frestore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/rgb-regex/download/rgb-regex-1.0.1.tgz"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/rgba-regex/download/rgba-regex-1.0.0.tgz"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.nlark.com/right-align/download/right-align-0.1.3.tgz"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

rimraf@^2.2.8, rimraf@^2.5.4, rimraf@^2.6.0, rimraf@^2.6.1, rimraf@^2.6.2:
  version "2.7.1"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@~2.6.2:
  version "2.6.3"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-2.6.3.tgz"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/ripemd160/download/ripemd160-2.0.2.tgz"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rsvp@^3.3.3:
  version "3.6.2"
  resolved "https://registry.nlark.com/rsvp/download/rsvp-3.6.2.tgz"
  integrity sha1-LpZJFZmpbN4bUV1WdKj3qRRSkmo=

run-async@^2.2.0:
  version "2.4.1"
  resolved "https://registry.nlark.com/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rust-result@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/rust-result/-/rust-result-1.0.0.tgz"
  integrity sha512-6cJzSBU+J/RJCF063onnQf0cDUOHs9uZI1oroSGnHOph+CQTIJ5Pp2hK5kEQq1+7yE/EEWfulSNXAQ2jikPthA==
  dependencies:
    individual "^2.0.0"

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "https://registry.nlark.com/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz"
  integrity sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "https://registry.nlark.com/rx-lite/download/rx-lite-4.0.8.tgz"
  integrity sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=

rxjs@^7.5.5:
  version "7.5.5"
  resolved "https://registry.npmmirror.com/rxjs/-/rxjs-7.5.5.tgz"
  integrity sha512-sy+H0pQofO95VDmFLzyaw9xNJU4KTRSwQIGM6+iG3SypAtCiLDzpeG8sJrNCWn2Up9km+KhkvTdbkrdy+yzZdw==
  dependencies:
    tslib "^2.1.0"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@>=5.1.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.2.1.tgz?cache=0&sync_timestamp=1599054209520&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafe-buffer%2Fdownload%2Fsafe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz?cache=0&sync_timestamp=1599054209520&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafe-buffer%2Fdownload%2Fsafe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz?cache=0&sync_timestamp=1599054209520&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafe-buffer%2Fdownload%2Fsafe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.1.2:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz?cache=0&sync_timestamp=1599054209520&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafe-buffer%2Fdownload%2Fsafe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-json-parse@4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/safe-json-parse/-/safe-json-parse-4.0.0.tgz"
  integrity sha512-RjZPPHugjK0TOzFrLZ8inw44s9bKox99/0AZW9o/BEQVrJfhI+fIHMErnPyRa89/yRXUUr93q+tiN6zhoVV4wQ==
  dependencies:
    rust-result "^1.0.0"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

safer-buffer@^2.0.2, safer-buffer@^2.1.0, "safer-buffer@>= 2.1.2 < 3", safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^2.0.0:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/sane/download/sane-2.5.2.tgz"
  integrity sha1-tNwYYcIbQn6SlQej51HiosuKs/o=
  dependencies:
    anymatch "^2.0.0"
    capture-exit "^1.2.0"
    exec-sh "^0.2.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"
    watch "~0.18.0"
  optionalDependencies:
    fsevents "^1.2.3"

sass-loader@^12.1.0:
  version "12.4.0"
  resolved "https://registry.npmmirror.com/sass-loader/download/sass-loader-12.4.0.tgz"
  integrity sha512-7xN+8khDIzym1oL9XyS6zP6Ges+Bo2B2xbPrjdMHEYyV3AQYhd/wXeru++3ODHF0zMjYmVadblSKrPrjEkL8mg==
  dependencies:
    klona "^2.0.4"
    neo-async "^2.6.2"

sass@^1.42.1:
  version "1.55.0"
  resolved "https://registry.npmmirror.com/sass/-/sass-1.55.0.tgz"
  integrity sha512-Pk+PMy7OGLs9WaxZGJMn7S96dvlyVBwwtToX895WmCpAOr5YiJYEUJfiJidMuKb613z2xNWcXCHEuOvjZbqC6A==
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@^1.2.4, sax@~1.2.1, sax@~1.2.4:
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

sax@~1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/sax/-/sax-1.3.0.tgz"
  integrity sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==

schema-utils@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-0.3.0.tgz?cache=0&sync_timestamp=1637075997986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-0.3.0.tgz"
  integrity sha1-9YdyIs4+kx7a4DnxfrNxbnE3+M8=
  dependencies:
    ajv "^5.0.0"

schema-utils@^0.4.0:
  version "0.4.7"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-0.4.7.tgz?cache=0&sync_timestamp=1637075997986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-0.4.7.tgz"
  integrity sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

schema-utils@^0.4.5:
  version "0.4.7"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-0.4.7.tgz?cache=0&sync_timestamp=1637075997986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-0.4.7.tgz"
  integrity sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

schema-utils@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

scroll-into-view-if-needed@^2.2.28:
  version "2.2.29"
  resolved "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.29.tgz"
  integrity sha512-hxpAR6AN+Gh53AdAimHM6C8oTN1ppwVZITihix+WqalywBeFcQ6LdQP5ABNl26nX8GTEL7VT+b8lKpdqq65wXg==
  dependencies:
    compute-scroll-into-view "^1.0.17"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/select-hose/download/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selenium-server@^3.0.1:
  version "3.141.59"
  resolved "https://registry.npm.taobao.org/selenium-server/download/selenium-server-3.141.59.tgz"
  integrity sha1-y+/fUKrmNu5MZ7gZUyqCM84/1rA=

selfsigned@^1.9.1:
  version "1.10.11"
  resolved "https://registry.nlark.com/selfsigned/download/selfsigned-1.10.11.tgz"
  integrity sha1-JJKc2Qb+D0S20B+yOZmnOVN6y+k=
  dependencies:
    node-forge "^0.10.0"

semver@^5.3.0, semver@^5.5.0, semver@^5.6.0, semver@^5.7.2, "semver@2 || 3 || 4 || 5":
  version "5.7.2"
  resolved "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@~5.0.1:
  version "5.0.3"
  resolved "https://registry.nlark.com/semver/download/semver-5.0.3.tgz?cache=0&sync_timestamp=1618847119601&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.0.3.tgz"
  integrity sha1-d0Zt5YnNXTyV8TiqeLxWmjy10no=

semver@5.3.0:
  version "5.3.0"
  resolved "https://registry.nlark.com/semver/download/semver-5.3.0.tgz?cache=0&sync_timestamp=1618847119601&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.3.0.tgz"
  integrity sha1-myzl094C0XxgEq0yaqa00M9U+U8=

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npmjs.org/send/-/send-0.18.0.tgz"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^1.4.0:
  version "1.9.1"
  resolved "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-1.9.1.tgz"
  integrity sha1-z8IArvd7YAxH2pu4FJyUPnmML9s=

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/serve-index/download/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1631437857327&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-1.0.0.tgz?cache=0&sync_timestamp=1628896299850&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shelljs@^0.8.5:
  version "0.8.5"
  resolved "https://registry.npmjs.org/shelljs/-/shelljs-0.8.5.tgz"
  integrity sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/shellwords/download/shellwords-0.1.1.tgz"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/side-channel/download/side-channel-1.0.4.tgz"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

side-channel@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

sigmund@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/sigmund/download/sigmund-1.0.1.tgz"
  integrity sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.6"
  resolved "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.6.tgz?cache=0&sync_timestamp=1637255763586&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.6.tgz"
  integrity sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-1.0.0.tgz"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slate-history@^0.66.0:
  version "0.66.0"
  resolved "https://registry.npmjs.org/slate-history/-/slate-history-0.66.0.tgz"
  integrity sha512-6MWpxGQZiMvSINlCbMW43E2YBSVMCMCIwQfBzGssjWw4kb0qfvj0pIdblWNRQZD0hR6WHP+dHHgGSeVdMWzfng==
  dependencies:
    is-plain-object "^5.0.0"

slate@^0.72.0:
  version "0.72.8"
  resolved "https://registry.npmjs.org/slate/-/slate-0.72.8.tgz"
  integrity sha512-/nJwTswQgnRurpK+bGJFH1oM7naD5qDmHd89JyiKNT2oOKD8marW0QSBtuFnwEbL5aGCS8AmrhXQgNOsn4osAw==
  dependencies:
    immer "^9.0.6"
    is-plain-object "^5.0.0"
    tiny-warning "^1.0.3"

slice-ansi@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-1.0.0.tgz?cache=0&sync_timestamp=1618555008681&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fslice-ansi%2Fdownload%2Fslice-ansi-1.0.0.tgz"
  integrity sha1-BE8aSdiEL/MHqta1Be0Xi9lQE00=
  dependencies:
    is-fullwidth-code-point "^2.0.0"

smart-buffer@^1.0.13:
  version "1.1.15"
  resolved "https://registry.nlark.com/smart-buffer/download/smart-buffer-1.1.15.tgz?cache=0&sync_timestamp=1628317244992&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsmart-buffer%2Fdownload%2Fsmart-buffer-1.1.15.tgz"
  integrity sha1-fxFLW2X6s+KjWqd1uxLw0cZJvxY=

snabbdom@^3.1.0:
  version "3.5.1"
  resolved "https://registry.npmjs.org/snabbdom/-/snabbdom-3.5.1.tgz"
  integrity sha512-wHMNIOjkm/YNE5EM3RCbr/+DVgPg6AqQAX1eOxO46zYNvCXjKP5Y865tqQj3EXnaMBjkxmQA5jFuDpDK/dbfiA==

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@1.1.5:
  version "1.1.5"
  resolved "https://registry.nlark.com/sockjs-client/download/sockjs-client-1.1.5.tgz?cache=0&sync_timestamp=1629825055862&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsockjs-client%2Fdownload%2Fsockjs-client-1.1.5.tgz"
  integrity sha1-G7fA9yIsQPQq3xT0RCy9Eml3GoM=
  dependencies:
    debug "^2.6.6"
    eventsource "0.1.6"
    faye-websocket "~0.11.0"
    inherits "^2.0.1"
    json3 "^3.3.2"
    url-parse "^1.1.8"

sockjs@0.3.19:
  version "0.3.19"
  resolved "https://registry.npmmirror.com/sockjs/download/sockjs-0.3.19.tgz"
  integrity sha1-2Xa76ACve9IK4IWY1YI5NQiZPA0=
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.0.1"

socks-proxy-agent@2:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/socks-proxy-agent/download/socks-proxy-agent-2.1.1.tgz"
  integrity sha1-huuwcZMlhjeHDhO3vZnybGY989M=
  dependencies:
    agent-base "2"
    extend "3"
    socks "~1.1.5"

socks@~1.1.5:
  version "1.1.10"
  resolved "https://registry.npm.taobao.org/socks/download/socks-1.1.10.tgz"
  integrity sha1-W4t/x8jzQcU+0FbpKbe/Tei6e1o=
  dependencies:
    ip "^1.1.4"
    smart-buffer "^1.0.13"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/sort-keys/download/sort-keys-1.1.2.tgz"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

sortablejs@1.10.2:
  version "1.10.2"
  resolved "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.2.tgz"
  integrity sha512-YkPGufevysvfwn5rfdlGyrGjt7/CRHwvRPogD/lC+TnvcN29jDpCifKP+rBqf+LRldfXSTh+0CGLcSg0VIxq3A==

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/source-list-map/download/source-list-map-2.0.1.tgz"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

"source-map-js@>=0.6.2 <2.0.0":
  version "1.2.0"
  resolved "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.0.tgz"
  integrity sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==

source-map-resolve@^0.5.0, source-map-resolve@^0.5.2:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.4.15:
  version "0.4.18"
  resolved "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.4.18.tgz"
  integrity sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8=
  dependencies:
    source-map "^0.5.6"

source-map-support@^0.5.0:
  version "0.5.21"
  resolved "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.nlark.com/source-map-url/download/source-map-url-0.4.1.tgz"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.4.2:
  version "0.4.4"
  resolved "https://registry.npmmirror.com/source-map/-/source-map-0.4.4.tgz"
  integrity sha512-Y8nIfcb1s/7DcobUz1yOO1GSp7gyL+D9zLHDehT7iRESqGSxjJ448Sg7rvfgsRJCnKLdSl11uGf0s9X80cH0/A==
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6, source-map@^0.5.7, source-map@~0.5.0, source-map@~0.5.1:
  version "0.5.7"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.4"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

source-map@~0.1.34:
  version "0.1.43"
  resolved "https://registry.npmmirror.com/source-map/-/source-map-0.1.43.tgz"
  integrity sha512-VtCvB9SIQhk3aF6h+N85EaqIaBFIAfZ9Cu+NJHHVvc8BbEcnvDcFw6sqQ2dQrT6SlOrZq3tIvyD9+EGq/lJryQ==
  dependencies:
    amdefine ">=0.0.4"

source-map@~0.6.0:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.1.tgz"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.11"
  resolved "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.11.tgz?cache=0&sync_timestamp=1636978474315&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.11.tgz"
  integrity sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/spdy-transport/download/spdy-transport-3.0.0.tgz"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/spdy/download/spdy-4.0.2.tgz"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.nlark.com/sprintf-js/download/sprintf-js-1.0.3.tgz?cache=0&sync_timestamp=1618847174560&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsprintf-js%2Fdownload%2Fsprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

ssf@~0.11.2:
  version "0.11.2"
  resolved "https://registry.npm.taobao.org/ssf/download/ssf-0.11.2.tgz"
  integrity sha1-C5lpiyN1SNCI/EPN8rcMGnUSwGw=
  dependencies:
    frac "~1.1.2"

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssr-window@^3.0.0-alpha.1:
  version "3.0.0"
  resolved "https://registry.npmjs.org/ssr-window/-/ssr-window-3.0.0.tgz"
  integrity sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA==

ssri@^5.2.4:
  version "5.3.0"
  resolved "https://registry.nlark.com/ssri/download/ssri-5.3.0.tgz?cache=0&sync_timestamp=1621364626710&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fssri%2Fdownload%2Fssri-5.3.0.tgz"
  integrity sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=
  dependencies:
    safe-buffer "^5.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmmirror.com/stable/download/stable-0.1.8.tgz"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stack-utils@^1.0.1:
  version "1.0.5"
  resolved "https://registry.nlark.com/stack-utils/download/stack-utils-1.0.5.tgz"
  integrity sha1-oZsLAZR+ACnI5FHV1hpJj1uxRxs=
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/stackframe/download/stackframe-1.2.0.tgz"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2":
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/stealthy-require/download/stealthy-require-1.1.1.tgz"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.2.tgz"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz?cache=0&sync_timestamp=1618430946341&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-http%2Fdownload%2Fstream-http-2.8.3.tgz"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.1.tgz"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string_decoder@^1.0.0, string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-0.10.31.tgz"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string-length@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/string-length/download/string-length-2.0.0.tgz?cache=0&sync_timestamp=1631559499195&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstring-length%2Fdownload%2Fstring-length-2.0.0.tgz"
  integrity sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=
  dependencies:
    astral-regex "^1.0.0"
    strip-ansi "^4.0.0"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-1.0.2.tgz?cache=0&sync_timestamp=1632420968947&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632420968947&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632420968947&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632420968947&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string.prototype.trimend@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/string.prototype.trimend/download/string.prototype.trimend-1.0.4.tgz?cache=0&sync_timestamp=1614127461586&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimend%2Fdownload%2Fstring.prototype.trimend-1.0.4.tgz"
  integrity sha1-51rpDClCxjUEaGwYsoe0oLGkX4A=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/string.prototype.trimstart/download/string.prototype.trimstart-1.0.4.tgz?cache=0&sync_timestamp=1614127357785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.4.tgz"
  integrity sha1-s2OZr0qymZtMnGSL16P7K7Jv7u0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

strip-ansi@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-0.3.0.tgz"
  integrity sha1-JfSOoiynkYfzF0pNuHWTR7sSYiA=
  dependencies:
    ansi-regex "^0.2.1"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-2.0.0.tgz"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/strip-indent/download/strip-indent-1.0.1.tgz?cache=0&sync_timestamp=1620053310624&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-indent%2Fdownload%2Fstrip-indent-1.0.1.tgz"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@^2.0.0, strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

style-loader@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/style-loader/download/style-loader-3.3.1.tgz?cache=0&sync_timestamp=1634872439692&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstyle-loader%2Fdownload%2Fstyle-loader-3.3.1.tgz"
  integrity sha1-BX36az1NfHBkRigw+RE+1BfThXU=

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "https://registry.nlark.com/stylehacks/download/stylehacks-4.0.3.tgz?cache=0&sync_timestamp=1621449652268&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstylehacks%2Fdownload%2Fstylehacks-4.0.3.tgz"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

stylus-loader@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/stylus-loader/-/stylus-loader-3.0.2.tgz"
  integrity sha512-+VomPdZ6a0razP+zinir61yZgpw2NfljeSsdUF5kJuEzlo3khXhY19Fn6l8QQz1GRJGtMCo8nG5C04ePyV7SUA==
  dependencies:
    loader-utils "^1.0.2"
    lodash.clonedeep "^4.5.0"
    when "~3.6.x"

stylus@^0.63.0:
  version "0.63.0"
  resolved "https://registry.npmjs.org/stylus/-/stylus-0.63.0.tgz"
  integrity sha512-OMlgrTCPzE/ibtRMoeLVhOY0RcNuNWh0rhAVqeKnk/QwcuUKQbnqhZ1kg2vzD8VU/6h3FoPTq4RJPHgLBvX6Bw==
  dependencies:
    "@adobe/css-tools" "~4.3.3"
    debug "^4.3.2"
    glob "^7.1.6"
    sax "~1.3.0"
    source-map "^0.7.3"

supports-color@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-0.2.0.tgz"
  integrity sha1-2S3iaU6z9nMjlz1649i1W0wiGQo=

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.1.2:
  version "3.2.3"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-3.2.3.tgz"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz"
  integrity sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A==
  dependencies:
    has-flag "^1.0.0"

supports-color@^4.2.1:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-4.5.0.tgz"
  integrity sha1-vnoN5ITexcXN34s9WRJQRJEvY1s=
  dependencies:
    has-flag "^2.0.0"

supports-color@^5.1.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^5.4.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-3.1.2.tgz"
  integrity sha1-cqJiiU2dQIuVbKBf83su2KbiotU=
  dependencies:
    has-flag "^1.0.0"

svg-baker-runtime@^1.4.7:
  version "1.4.7"
  resolved "https://registry.nlark.com/svg-baker-runtime/download/svg-baker-runtime-1.4.7.tgz"
  integrity sha1-9HIGN/W2IC7vY3jYHx/q0IFfik4=
  dependencies:
    deepmerge "1.3.2"
    mitt "1.1.2"
    svg-baker "^1.7.0"

svg-baker@^1.5.0, svg-baker@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/svg-baker/download/svg-baker-1.7.0.tgz"
  integrity sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=
  dependencies:
    bluebird "^3.5.0"
    clone "^2.1.1"
    he "^1.1.1"
    image-size "^0.5.1"
    loader-utils "^1.1.0"
    merge-options "1.0.1"
    micromatch "3.1.0"
    postcss "^5.2.17"
    postcss-prefix-selector "^1.6.0"
    posthtml-rename-id "^1.0"
    posthtml-svg-mode "^1.0.3"
    query-string "^4.3.2"
    traverse "^0.6.6"

svg-sprite-loader@^6.0.9:
  version "6.0.11"
  resolved "https://registry.npmmirror.com/svg-sprite-loader/download/svg-sprite-loader-6.0.11.tgz"
  integrity sha1-pNYM7j10IyosF9Mcc6IAgpX2EiA=
  dependencies:
    bluebird "^3.5.0"
    deepmerge "1.3.2"
    domready "1.0.8"
    escape-string-regexp "1.0.5"
    loader-utils "^1.1.0"
    svg-baker "^1.5.0"
    svg-baker-runtime "^1.4.7"
    url-slug "2.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/svg-tags/download/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^0.7.0:
  version "0.7.2"
  resolved "https://registry.npmmirror.com/svgo/download/svgo-0.7.2.tgz"
  integrity sha1-n1dyQTlSE1xv779Ar+ak+qiLS7U=
  dependencies:
    coa "~1.0.1"
    colors "~1.1.2"
    csso "~2.3.1"
    js-yaml "~3.7.0"
    mkdirp "~0.5.1"
    sax "~1.2.1"
    whet.extend "~0.9.9"

svgo@^1.0.0:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/svgo/download/svgo-1.3.2.tgz"
  integrity sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

symbol-tree@^3.2.2:
  version "3.2.4"
  resolved "https://registry.npm.taobao.org/symbol-tree/download/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

table@4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/table/download/table-4.0.2.tgz"
  integrity sha1-ozRHN1OR52atNNNIbm4q7chNLjY=
  dependencies:
    ajv "^5.2.3"
    ajv-keywords "^2.1.0"
    chalk "^2.1.0"
    lodash "^4.17.4"
    slice-ansi "1.0.0"
    string-width "^2.1.1"

tapable@^0.2.7:
  version "0.2.9"
  resolved "https://registry.nlark.com/tapable/download/tapable-0.2.9.tgz"
  integrity sha1-ry2LvJsE907hevK02QSPgHrNGKg=

tcp-port-used@^1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/tcp-port-used/download/tcp-port-used-1.0.2.tgz"
  integrity sha1-llK3Q26x9M+uERx5tViiV2n2+uo=
  dependencies:
    debug "4.3.1"
    is2 "^2.0.6"

test-exclude@^4.2.1:
  version "4.2.3"
  resolved "https://registry.nlark.com/test-exclude/download/test-exclude-4.2.3.tgz"
  integrity sha1-qaXmRHTkOYM5JFoKdprXwvSpfCA=
  dependencies:
    arrify "^1.0.1"
    micromatch "^2.3.11"
    object-assign "^4.1.0"
    read-pkg-up "^1.0.1"
    require-main-filename "^1.0.1"

text-table@^0.2.0, text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/text-table/download/text-table-0.2.0.tgz?cache=0&sync_timestamp=1618847142316&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftext-table%2Fdownload%2Ftext-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throat@^4.0.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/throat/download/throat-4.1.0.tgz"
  integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=

throttle-debounce@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-1.1.0.tgz?cache=0&sync_timestamp=1604313832516&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthrottle-debounce%2Fdownload%2Fthrottle-debounce-1.1.0.tgz"
  integrity sha1-UYU9o3vmihVctugns1FKPEIuic0=

throttle-debounce@^2.0.1:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-2.3.0.tgz?cache=0&sync_timestamp=1604313832516&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthrottle-debounce%2Fdownload%2Fthrottle-debounce-2.3.0.tgz"
  integrity sha1-/TGGXmZQIHHkEYF+JBRls+nDcuI=

through@^2.3.6, through@~2.3.4:
  version "2.3.8"
  resolved "https://registry.nlark.com/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

through2@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/through2/download/through2-2.0.5.tgz"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

thunkify@~2.1.1:
  version "2.1.2"
  resolved "https://registry.nlark.com/thunkify/download/thunkify-2.1.2.tgz"
  integrity sha1-+qDp0jDFGsyVyhOjYawFyn4EVT0=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.nlark.com/thunky/download/thunky-1.1.0.tgz"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

time-stamp@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/time-stamp/download/time-stamp-2.2.0.tgz"
  integrity sha1-kX4KZpBWiHkOx7u94EBGJZr4P1c=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://registry.nlark.com/timers-browserify/download/timers-browserify-2.0.12.tgz"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/timsort/download/timsort-0.3.0.tgz"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tiny-cookie@^1.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/tiny-cookie/download/tiny-cookie-1.0.1.tgz"
  integrity sha1-dTeGB5xkKjw9CyrMrWAPjeEZrCo=

tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  integrity sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==

tinycolor@0.x:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/tinycolor/download/tinycolor-0.0.1.tgz"
  integrity sha512-+CorETse1kl98xg0WAzii8DTT4ABF4R3nquhrkIbVGcw1T8JYs5Gfx9xEfGINPUZGDj9C4BmOtuKeaTtuuRolg==

tinycolor2@^1.4.1:
  version "1.4.2"
  resolved "https://registry.npm.taobao.org/tinycolor2/download/tinycolor2-1.4.2.tgz"
  integrity sha1-P2pNEHGtB2dtf6Ry4frECnGdiAM=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry.nlark.com/tmpl/download/tmpl-1.0.5.tgz"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/to-fast-properties/download/to-fast-properties-1.0.3.tgz?cache=0&sync_timestamp=1628418855671&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fto-fast-properties%2Fdownload%2Fto-fast-properties-1.0.3.tgz"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz?cache=0&sync_timestamp=1628418855671&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fto-fast-properties%2Fdownload%2Fto-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz?cache=0&sync_timestamp=1636938489272&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftoidentifier%2Fdownload%2Ftoidentifier-1.0.0.tgz"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@^2.3.3, tough-cookie@^2.3.4, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftough-cookie%2Fdownload%2Ftough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/tr46/download/tr46-1.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftr46%2Fdownload%2Ftr46-1.0.1.tgz"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

traverse@^0.6.6:
  version "0.6.6"
  resolved "https://registry.nlark.com/traverse/download/traverse-0.6.6.tgz"
  integrity sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc=

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/trim-newlines/download/trim-newlines-1.0.0.tgz?cache=0&sync_timestamp=1623341510447&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftrim-newlines%2Fdownload%2Ftrim-newlines-1.0.0.tgz"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/trim-right/download/trim-right-1.0.1.tgz"
  integrity sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=

tryer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/tryer/download/tryer-1.0.1.tgz"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

tsconfig-paths@^3.11.0:
  version "3.12.0"
  resolved "https://registry.npmmirror.com/tsconfig-paths/download/tsconfig-paths-3.12.0.tgz"
  integrity sha512-e5adrnOYT6zqVnWqZu7i/BQ3BnhzvGbjEjejFXO20lKIKpwTaupkCPgEfv4GZK1IBciJUEhYs3J3p75FdaTFVg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tsconfig@^7.0.0:
  version "7.0.0"
  resolved "https://registry.nlark.com/tsconfig/download/tsconfig-7.0.0.tgz"
  integrity sha1-hFOIdaTcIW5cSlQys6Tew9VOkbc=
  dependencies:
    "@types/strip-bom" "^3.0.0"
    "@types/strip-json-comments" "0.0.30"
    strip-bom "^3.0.0"
    strip-json-comments "^2.0.0"

tslib@^2.0.1, tslib@^2.1.0, tslib@^2.3.0, tslib@2.3.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/tslib/download/tslib-2.3.0.tgz?cache=0&sync_timestamp=1628722556410&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftslib%2Fdownload%2Ftslib-2.3.0.tgz"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tsml@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/tsml/-/tsml-1.0.1.tgz"
  integrity sha512-3KmepnH9SUsoOVtg013CRrL7c+AK7ECaquAsJdvu4288EDJuraqBlP4PDXT/rLEJ9YDn4jqLAzRJsnFPx+V6lg==

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.nlark.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/type-detect/download/type-detect-0.1.1.tgz"
  integrity sha1-C6XsKohWQORw6k6FBZcZANrFiCI=

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.nlark.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/type/download/type-1.2.0.tgz?cache=0&sync_timestamp=1615215448661&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftype%2Fdownload%2Ftype-1.2.0.tgz"
  integrity sha1-hI3XaY2vo+VKbEeedZxLw/GIR6A=

type@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/type/download/type-2.5.0.tgz?cache=0&sync_timestamp=1615215448661&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftype%2Fdownload%2Ftype-2.5.0.tgz"
  integrity sha1-Ci54wud5B7JSq+XymMGwHGPw2z0=

type@^2.7.2:
  version "2.7.2"
  resolved "https://registry.npmjs.org/type/-/type-2.7.2.tgz"
  integrity sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw==

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/uc.micro/-/uc.micro-1.0.6.tgz"
  integrity sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==

uglify-es@^3.3.4:
  version "3.3.9"
  resolved "https://registry.npmmirror.com/uglify-es/download/uglify-es-3.3.9.tgz"
  integrity sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc=
  dependencies:
    commander "~2.13.0"
    source-map "~0.6.1"

uglify-js@^2.8.29:
  version "2.8.29"
  resolved "https://registry.npmmirror.com/uglify-js/download/uglify-js-2.8.29.tgz"
  integrity sha1-KcVzMUgFe7Th913zW3qcty5qWd0=
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-js@^3.1.4, uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://registry.npmmirror.com/uglify-js/download/uglify-js-3.4.10.tgz"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

uglifyjs-webpack-plugin@^0.4.6:
  version "0.4.6"
  resolved "https://registry.npmmirror.com/uglifyjs-webpack-plugin/download/uglifyjs-webpack-plugin-0.4.6.tgz"
  integrity sha1-uVH0q7a9YX5m9j64kUmOORdj4wk=
  dependencies:
    source-map "^0.5.6"
    uglify-js "^2.8.29"
    webpack-sources "^1.0.1"

uglifyjs-webpack-plugin@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/uglifyjs-webpack-plugin/download/uglifyjs-webpack-plugin-1.3.0.tgz"
  integrity sha1-dfVIFghYFjoIZD4IbV/v4YpdZ94=
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    schema-utils "^0.4.5"
    serialize-javascript "^1.4.0"
    source-map "^0.6.1"
    uglify-es "^3.3.4"
    webpack-sources "^1.1.0"
    worker-farm "^1.5.2"

umy-table@1.1.8:
  version "1.1.8"
  resolved "https://registry.npmmirror.com/umy-table/-/umy-table-1.1.8.tgz"
  integrity sha512-yqPMFwZ6MUNqI73KamsNKBTbHRA7VuVZDEWobaySghtf4n/s7INuTNQoADB/ROVK57WOXr99Xpnol0aMqGlASA==
  dependencies:
    async-validator "~1.8.1"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"
    vuedraggable "^2.23.2"
    xe-utils "^3.0.0-beta.12"

umy-ui@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmmirror.com/umy-ui/-/umy-ui-1.1.6.tgz"
  integrity sha512-wX0S29B5sFn8q0MvU9PEl8DGZeQmZXOJlLrwaCJ2UHiIQZCANWgXMBjjQgpaequ1gRZ8BekEu7Sn7hJccm9YMQ==
  dependencies:
    async-validator "~1.8.1"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"
    umy-table "1.1.8"
    vuedraggable "^2.23.2"
    xe-utils "^3.0.0-beta.12"

unbox-primitive@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/unbox-primitive/download/unbox-primitive-1.0.1.tgz"
  integrity sha1-CF4hViXsMWJXTciFmr7nilmxRHE=
  dependencies:
    function-bind "^1.1.1"
    has-bigints "^1.0.1"
    has-symbols "^1.0.2"
    which-boxed-primitive "^1.0.2"

unidecode@0.1.8:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/unidecode/download/unidecode-0.1.8.tgz"
  integrity sha1-77swFTi8RSRqmsjFWdcvAVMFBT4=

unidragger@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/unidragger/-/unidragger-2.4.0.tgz"
  integrity sha512-MueZK2oXuGE6OAlGKIrSXK2zCq+8yb1QUZgqyTDCSJzvwYL0g2Llrad+TtoQTYxtFnNyxxSw0IMnKNIgEMia1w==
  dependencies:
    unipointer "^2.4.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unipointer@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/unipointer/-/unipointer-2.4.0.tgz"
  integrity sha512-VjzDLPjGK7aYpQKH7bnDZS8X4axF5AFU/LQi+NQe1oyEHfaz6lWKhaQ7n4o7vJ1iJ4i2T0quCIfrQM139p05Sw==
  dependencies:
    ev-emitter "^1.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/uniq/download/uniq-1.0.1.tgz"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.nlark.com/unique-slug/download/unique-slug-2.0.2.tgz"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/universalify/-/universalify-0.1.2.tgz"
  integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/unquote/download/unquote-1.1.1.tgz"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unset-value/download/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/upath/download/upath-1.2.0.tgz"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://registry.nlark.com/upper-case/download/upper-case-1.1.3.tgz"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uppercamelcase@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/uppercamelcase/-/uppercamelcase-1.1.0.tgz"
  integrity sha512-C7YEMvhgrvTEKEEVqA7LXNID/1TvvIwYZqNIKLquS6y/MGSkRQAav9LnTTILlC1RqUM8eTVBOe1U/fnB652PRA==
  dependencies:
    camelcase "^1.2.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237624359&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^0.5.8:
  version "0.5.9"
  resolved "https://registry.npmmirror.com/url-loader/download/url-loader-0.5.9.tgz"
  integrity sha1-zI/qgse5Bud3cBklCGnlaemVwpU=
  dependencies:
    loader-utils "^1.0.2"
    mime "1.3.x"

url-parse@^1.1.8, url-parse@^1.4.3:
  version "1.5.10"
  resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-slug@2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/url-slug/download/url-slug-2.0.0.tgz"
  integrity sha1-p4nVrtSZXA2VrzM3etHVxo1NcCc=
  dependencies:
    unidecode "0.1.8"

url-toolkit@^2.1.3, url-toolkit@^2.2.1:
  version "2.2.5"
  resolved "https://registry.npmmirror.com/url-toolkit/-/url-toolkit-2.2.5.tgz"
  integrity sha512-mtN6xk+Nac+oyJ/PrI7tzfmomRVNFIWKUbG8jdYFt52hxbiReFAXIjYskvu64/dvuW71IcB7lV8l0HvZMac6Jg==

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.nlark.com/url/download/url-0.11.0.tgz?cache=0&sync_timestamp=1618847135337&other_urls=https%3A%2F%2Fregistry.nlark.com%2Furl%2Fdownload%2Furl-0.11.0.tgz"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.1.1.tgz?cache=0&sync_timestamp=1610159895694&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.1.1.tgz"
  integrity sha1-d4MvV87SyUeBdBScrpuW6ZGM1Us=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    for-each "^0.3.3"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.1"

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.1.tgz?cache=0&sync_timestamp=1610159895694&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.1.tgz"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

util@^0.11.0:
  version "0.11.1"
  resolved "https://registry.nlark.com/util/download/util-0.11.1.tgz?cache=0&sync_timestamp=1622212984161&other_urls=https%3A%2F%2Fregistry.nlark.com%2Futil%2Fdownload%2Futil-0.11.1.tgz"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

util@0.10.3:
  version "0.10.3"
  resolved "https://registry.nlark.com/util/download/util-0.10.3.tgz?cache=0&sync_timestamp=1622212984161&other_urls=https%3A%2F%2Fregistry.nlark.com%2Futil%2Fdownload%2Futil-0.10.3.tgz"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.nlark.com/utila/download/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-lite@0.1.10:
  version "0.1.10"
  resolved "https://registry.npmmirror.com/utils-lite/-/utils-lite-0.1.10.tgz"
  integrity sha512-jlHvdtI8MyWURF/3u+ufIjf1Cs5WjN6WZl9qO8dEkZsVjaI7X5YMUhaCFzkvB69ljt6fo4Dd7V/Oj2NJOFDFOQ==

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.0.1, uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v-charts@^1.19.0:
  version "1.19.0"
  resolved "https://registry.npmmirror.com/v-charts/-/v-charts-1.19.0.tgz"
  integrity sha512-vm2HBUmxAsXK0ivwce9LytcpqrItDA5JSPLYVxZXtiuoyhcn80XX1/3dPJd/1GqG1OYv3jfBo1s9ra4q8GowqA==
  dependencies:
    echarts-amap "1.0.0-rc.6"
    echarts-liquidfill "^2.0.2"
    echarts-wordcloud "^1.1.3"
    numerify "1.2.9"
    utils-lite "0.1.10"

v-click-outside-x@^3.7.1:
  version "3.7.1"
  resolved "https://registry.nlark.com/v-click-outside-x/download/v-click-outside-x-3.7.1.tgz"
  integrity sha1-qgPqoOQeRMtSB9z4bC2fDdZAhME=

v-echarts@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/v-echarts/-/v-echarts-1.0.2.tgz"
  integrity sha512-pCJGhiXze5F/UvIKT2b/SvrdxAjYa5Uxfyz+y8bFtRKbPfgANGKFXYX5KRol/zDPHwEIJqBNMCN91E/1uiqCpQ==
  dependencies:
    babel-runtime "^6.0.0"
    dotenv "^2.0.0"
    echarts "^3.1.10"
    express "^4.14.0"
    vue "^1.0.21"

v-viewer@^1.6.4:
  version "1.6.4"
  resolved "https://registry.nlark.com/v-viewer/download/v-viewer-1.6.4.tgz"
  integrity sha1-OeNrU0uqs0B2+4FnBManNN4Nxy8=
  dependencies:
    throttle-debounce "^2.0.1"
    viewerjs "^1.5.0"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.nlark.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://registry.nlark.com/vendors/download/vendors-1.0.4.tgz"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npmmirror.com/verror/download/verror-1.10.0.tgz?cache=0&sync_timestamp=1635885078723&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fverror%2Fdownload%2Fverror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

"video.js@^5.17.0 || ^6.2.0":
  version "6.13.0"
  resolved "https://registry.npmmirror.com/video.js/-/video.js-6.13.0.tgz"
  integrity sha512-36/JR/GhPQSZj0o+GNbhcEYv/b0SkV9SQsjlodAnzMQYN0TA7VhmqrKPYMCi1NGRYu7S9W3OaFCFoUxkYfSVlg==
  dependencies:
    babel-runtime "^6.9.2"
    global "4.3.2"
    safe-json-parse "4.0.0"
    tsml "1.0.1"
    videojs-font "2.1.0"
    videojs-ie8 "1.1.2"
    videojs-vtt.js "0.12.6"
    xhr "2.4.0"

"video.js@^5.19.1 || ^6.2.0":
  version "6.13.0"
  resolved "https://registry.npmmirror.com/video.js/-/video.js-6.13.0.tgz"
  integrity sha512-36/JR/GhPQSZj0o+GNbhcEYv/b0SkV9SQsjlodAnzMQYN0TA7VhmqrKPYMCi1NGRYu7S9W3OaFCFoUxkYfSVlg==
  dependencies:
    babel-runtime "^6.9.2"
    global "4.3.2"
    safe-json-parse "4.0.0"
    tsml "1.0.1"
    videojs-font "2.1.0"
    videojs-ie8 "1.1.2"
    videojs-vtt.js "0.12.6"
    xhr "2.4.0"

"video.js@^6 || ^7", video.js@^7.19.2:
  version "7.19.2"
  resolved "https://registry.npmmirror.com/video.js/-/video.js-7.19.2.tgz"
  integrity sha512-+rV/lJ1bDoMW3SbYlRp0eC9//RgvfBpEQ0USOyx44tHVxVyMjq+G9jZoiulsDXaIp4BX9q5+/y87TbZUysXBHA==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/http-streaming" "2.14.2"
    "@videojs/vhs-utils" "^3.0.4"
    "@videojs/xhr" "2.6.0"
    aes-decrypter "3.1.3"
    global "^4.4.0"
    keycode "^2.2.0"
    m3u8-parser "4.7.1"
    mpd-parser "0.21.1"
    mux.js "6.0.1"
    safe-json-parse "4.0.0"
    videojs-font "3.2.0"
    videojs-vtt.js "^0.15.3"

video.js@^6.6.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/video.js/-/video.js-6.13.0.tgz"
  integrity sha512-36/JR/GhPQSZj0o+GNbhcEYv/b0SkV9SQsjlodAnzMQYN0TA7VhmqrKPYMCi1NGRYu7S9W3OaFCFoUxkYfSVlg==
  dependencies:
    babel-runtime "^6.9.2"
    global "4.3.2"
    safe-json-parse "4.0.0"
    tsml "1.0.1"
    videojs-font "2.1.0"
    videojs-ie8 "1.1.2"
    videojs-vtt.js "0.12.6"
    xhr "2.4.0"

videojs-contrib-hls@^5.12.2, videojs-contrib-hls@^5.15.0:
  version "5.15.0"
  resolved "https://registry.npmmirror.com/videojs-contrib-hls/-/videojs-contrib-hls-5.15.0.tgz"
  integrity sha512-18zbMYZ0XRBKTPEayA9bFTWWrqhT9b4G8+zf0czJLD7Epe5PcK1I/3dflTHQeQ5rwlWir+/XnFU3sMg/B2MMcw==
  dependencies:
    aes-decrypter "1.0.3"
    global "^4.3.0"
    m3u8-parser "2.1.0"
    mux.js "4.3.2"
    url-toolkit "^2.1.3"
    video.js "^5.19.1 || ^6.2.0"
    videojs-contrib-media-sources "4.7.2"
    webwackify "0.1.6"

videojs-contrib-media-sources@4.7.2:
  version "4.7.2"
  resolved "https://registry.npmmirror.com/videojs-contrib-media-sources/-/videojs-contrib-media-sources-4.7.2.tgz"
  integrity sha512-e6iCHWBFuV05EGo7v+pS9iepObXnJ9joms467gzi8ZjpKVb3ifha9M0Ja24Rd8JfvYpzjltsgDVtGFDvIg4hQQ==
  dependencies:
    global "^4.3.0"
    mux.js "4.3.2"
    video.js "^5.17.0 || ^6.2.0"
    webwackify "0.1.6"

videojs-flash@^2.1.0:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/videojs-flash/-/videojs-flash-2.2.1.tgz"
  integrity sha512-mHu6TD12EKkxMvr8tg4AcfV/DuVLff427nneoZom3N9Dd2bv0sJOWwdLPQH1v5BCuAuXAVuAOba56ovTl+G3tQ==
  dependencies:
    global "^4.4.0"
    video.js "^6 || ^7"
    videojs-swf "5.4.2"

videojs-font@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/videojs-font/-/videojs-font-2.1.0.tgz"
  integrity sha512-zFqWpLrXf1q8NtYx5qtZhMC6SLUFScDmR6j+UGPogobxR21lvXShhnzcNNMdOxJUuFLiToJ/BPpFUQwX4xhpvA==

videojs-font@3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/videojs-font/-/videojs-font-3.2.0.tgz"
  integrity sha512-g8vHMKK2/JGorSfqAZQUmYYNnXmfec4MLhwtEFS+mMs2IDY398GLysy6BH6K+aS1KMNu/xWZ8Sue/X/mdQPliA==

videojs-hotkeys@^0.2.20:
  version "0.2.28"
  resolved "https://registry.npmmirror.com/videojs-hotkeys/-/videojs-hotkeys-0.2.28.tgz"
  integrity sha512-M8rlD5OSB3EDRdbS4MRNlGKFpA2sSIStmUPvy5zfl/NigzWaN6r4wnb32rEN0v97GiQwmUfXSmqrPNrXhiFQmQ==

videojs-ie8@1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/videojs-ie8/-/videojs-ie8-1.1.2.tgz"
  integrity sha512-0Zb2T4MLkpfZbeGMK/Z93b8Lrepr+rLFoHgQV1CoDeFqXvH7b+Vsd/VHoILGxQrgCSHFQ7mAODR6oyMjuiD4/g==
  dependencies:
    es5-shim "^4.5.1"

videojs-swf@5.4.2:
  version "5.4.2"
  resolved "https://registry.npmmirror.com/videojs-swf/-/videojs-swf-5.4.2.tgz"
  integrity sha512-FGg+Csioa8/A/EacvFefBdb9Z0rSiMlheHDunZnN3xXfUF43jvjawcWFQnZvrv1Cs1nE1LBrHyUZjF7j2mKOLw==

videojs-vtt.js@^0.15.3:
  version "0.15.3"
  resolved "https://registry.npmmirror.com/videojs-vtt.js/-/videojs-vtt.js-0.15.3.tgz"
  integrity sha512-5FvVsICuMRx6Hd7H/Y9s9GDeEtYcXQWzGMS+sl4UX3t/zoHp3y+isSfIPRochnTH7h+Bh1ILyC639xy9Z6kPag==
  dependencies:
    global "^4.3.1"

videojs-vtt.js@0.12.6:
  version "0.12.6"
  resolved "https://registry.npmmirror.com/videojs-vtt.js/-/videojs-vtt.js-0.12.6.tgz"
  integrity sha512-XFXeGBQiljnElMhwCcZst0RDbZn2n8LU7ZScXryd3a00OaZsHAjdZu/7/RdSr7Z1jHphd45FnOvOQkGK4YrWCQ==
  dependencies:
    global "^4.3.1"

view-design@^4.6.1:
  version "4.7.0"
  resolved "https://registry.npmmirror.com/view-design/download/view-design-4.7.0.tgz"
  integrity sha512-WRvVRfsZciN0aJYlz+6b1zxs5G8tLFb4OUKOu+PiN0QyIGmdgWVziEyEmioYtJahQpueWiQpRYGzyxrpz3UkWQ==
  dependencies:
    async-validator "^3.3.0"
    deepmerge "^2.2.1"
    element-resize-detector "^1.2.0"
    js-calendar "^1.2.3"
    lodash.throttle "^4.1.1"
    popper.js "^1.14.6"
    tinycolor2 "^1.4.1"
    v-click-outside-x "^3.7.1"

viewerjs@^1.5.0:
  version "1.10.2"
  resolved "https://registry.npmmirror.com/viewerjs/download/viewerjs-1.10.2.tgz?cache=0&sync_timestamp=1635058905557&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fviewerjs%2Fdownload%2Fviewerjs-1.10.2.tgz"
  integrity sha1-3hb6EGaOTaYyWWmDajJkoEbj75o=

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://registry.nlark.com/vm-browserify/download/vm-browserify-1.1.2.tgz"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-amap@^0.5.10:
  version "0.5.10"
  resolved "https://registry.npmmirror.com/vue-amap/-/vue-amap-0.5.10.tgz"
  integrity sha512-9ViNCev1vx32+zZ5RvF/TmUZNbwL9QrdA2/OnD2GlXMfQBkJy7D08Vb7379t6guqnopDPtWJ8K6gg72h9+4GUg==
  dependencies:
    uppercamelcase "^1.1.0"

vue-baidu-map@^0.21.22:
  version "0.21.22"
  resolved "https://registry.npmmirror.com/vue-baidu-map/-/vue-baidu-map-0.21.22.tgz"
  integrity sha512-WQMPCih4UTh0AZCKKH/OVOYnyAWjfRNeK6BIeoLmscyY5aF8zzlJhz/NOHLb3mdztIpB0Z6aohn4Jd9mfCSjQw==
  dependencies:
    bmaplib.curveline "^1.0.0"
    bmaplib.heatmap "^1.0.4"
    bmaplib.lushu "^1.0.7"
    bmaplib.markerclusterer "^1.0.13"
    markdown-it "^8.4.0"

vue-cookie@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/vue-cookie/download/vue-cookie-1.1.4.tgz"
  integrity sha1-uLRtESvan5Oi9HAXwu1SgtIGT9o=
  dependencies:
    tiny-cookie "^1.0"

vue-demi@^0.12.1:
  version "0.12.4"
  resolved "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.4.tgz"
  integrity sha512-ztPDkFt0TSUdoq1ZI6oD730vgztBkiByhUW7L1cOTebiSBqSYfSQgnhYakYigBkyAybqCTH7h44yZuDJf2xILQ==

vue-echarts@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.2.tgz"
  integrity sha512-9xDokauJtAc389MNKbwi1I0VDmp4Y6ndAJTQ8T9K7H0ffosTe1OJSJbUtkT7/fVLDFzlCcmg2TfAKaMzbpg5yQ==
  dependencies:
    resize-detector "^0.3.0"
    vue-demi "^0.12.1"

vue-eslint-parser@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/vue-eslint-parser/download/vue-eslint-parser-2.0.3.tgz"
  integrity sha1-wmjJbG2Uz+PZOKX3WTlZsMozYNE=
  dependencies:
    debug "^3.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.2"
    esquery "^1.0.0"
    lodash "^4.17.4"

vue-google-maps@^0.1.21:
  version "0.1.21"
  resolved "https://registry.npmmirror.com/vue-google-maps/-/vue-google-maps-0.1.21.tgz"
  integrity sha512-8IWeCupRH7Dh4jeQtlo6jBEzWSO2n/WcB0T4vK3SkZnDXiBsJoG8msT21cjTIoDiTpqgWUhSxLxdh94XVmhTYQ==
  dependencies:
    js-marker-clusterer "^1.0.0"

vue-hot-reload-api@^2.2.0:
  version "2.3.4"
  resolved "https://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

vue-i18n@^8.24.5:
  version "8.26.7"
  resolved "https://registry.npmmirror.com/vue-i18n/download/vue-i18n-8.26.7.tgz"
  integrity sha1-rf1INzRJ/jFDjEjjs71DBE3DpoE=

vue-jest@^1.0.2:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/vue-jest/download/vue-jest-1.4.0.tgz"
  integrity sha1-HWtNJ3SwrsBs/l05eJA50zgbxSg=
  dependencies:
    babel-core "^6.25.0"
    babel-preset-vue-app "^1.3.1"
    chalk "^2.1.0"
    find-babel-config "^1.1.0"
    js-beautify "^1.6.14"
    node-cache "^4.1.1"
    object-assign "^4.1.1"
    source-map "^0.5.6"
    tsconfig "^7.0.0"
    vue-template-es2015-compiler "^1.5.3"

vue-loader@^13.3.0:
  version "13.7.3"
  resolved "https://registry.npmmirror.com/vue-loader/download/vue-loader-13.7.3.tgz"
  integrity sha1-4HRA94IwpjnQCtpNp7ltDp1iA38=
  dependencies:
    consolidate "^0.14.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    lru-cache "^4.1.1"
    postcss "^6.0.8"
    postcss-load-config "^1.1.0"
    postcss-selector-parser "^2.0.0"
    prettier "^1.7.0"
    resolve "^1.4.0"
    source-map "^0.6.1"
    vue-hot-reload-api "^2.2.0"
    vue-style-loader "^3.0.0"
    vue-template-es2015-compiler "^1.6.0"

vue-mini-player@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/vue-mini-player/download/vue-mini-player-0.2.1.tgz"
  integrity sha1-YkJbtSZI6+WHhLTTDpSklfkZMCk=
  dependencies:
    core-js "^2.6.5"
    vue "^2.6.10"

vue-particles@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npm.taobao.org/vue-particles/download/vue-particles-1.0.9.tgz"
  integrity sha1-KdnMK+AVffHAqH7DuWyW8zJ5Um4=
  dependencies:
    particles.js "^2.0.0"
    vue "^2.2.6"

vue-pdf@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/vue-pdf/-/vue-pdf-4.3.0.tgz"
  integrity sha512-zd3lJj6CbtrawgaaDDciTDjkJMUKiLWtbEmBg5CvFn9Noe9oAO/GNy/fc5c59qGuFCJ14ibIV1baw4S07e5bSQ==
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    loader-utils "^1.4.0"
    pdfjs-dist "2.6.347"
    raw-loader "^4.0.2"
    vue-resize-sensor "^2.0.0"
    worker-loader "^2.0.0"

vue-resize-sensor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/vue-resize-sensor/-/vue-resize-sensor-2.0.0.tgz"
  integrity sha512-W+y2EAI/BxS4Vlcca9scQv8ifeBFck56DRtSwWJ2H4Cw1GLNUYxiZxUHHkuzuI5JPW/cYtL1bPO5xPyEXx4LmQ==

vue-router@^3.5.2:
  version "3.5.3"
  resolved "https://registry.npmmirror.com/vue-router/download/vue-router-3.5.3.tgz"
  integrity sha1-BBBIBT4zaCnQXa+s9qj7ZpoueZk=

vue-style-loader@^3.0.0, vue-style-loader@^3.0.1:
  version "3.1.2"
  resolved "https://registry.nlark.com/vue-style-loader/download/vue-style-loader-3.1.2.tgz"
  integrity sha1-a2atNJmPyVIMLx5NX6QJFkHBWXo=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.5.2:
  version "2.6.14"
  resolved "https://registry.nlark.com/vue-template-compiler/download/vue-template-compiler-2.6.14.tgz?cache=0&sync_timestamp=1623059640396&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fvue-template-compiler%2Fdownload%2Fvue-template-compiler-2.6.14.tgz"
  integrity sha1-ovDn2YVnDULJye4NBE/tdpD092M=
  dependencies:
    de-indent "^1.0.2"
    he "^1.1.0"

vue-template-es2015-compiler@^1.5.3, vue-template-es2015-compiler@^1.6.0:
  version "1.9.1"
  resolved "https://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue-video-player@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmmirror.com/vue-video-player/-/vue-video-player-5.0.2.tgz"
  integrity sha512-IZXeRGGSX4YIp54G0Q5cB7iqh6Ok6Dpa2jRkjdyvMWw7MShJuh54/d5QNb1CZ+CvZUzX/TH7osnpir7mBNcFvQ==
  dependencies:
    object-assign "^4.1.1"
    video.js "^6.6.0"
    videojs-contrib-hls "^5.12.2"
    videojs-flash "^2.1.0"
    videojs-hotkeys "^0.2.20"

vue@^1.0.21:
  version "1.0.28"
  resolved "https://registry.npmmirror.com/vue/-/vue-1.0.28.tgz"
  integrity sha512-DEwNmtns5dCjuAQbeddcZk54kGoPWJRonEsmwjiD2KfmioZ16IvCRY+RD+AUXztwJkBNUPU/V9KTK8v6EQ2g2Q==
  dependencies:
    envify "^3.4.0"

vue@^2.2.6, vue@^2.5.2, vue@^2.6.10:
  version "2.6.14"
  resolved "https://registry.npmmirror.com/vue/download/vue-2.6.14.tgz"
  integrity sha1-5RqlJQJQ1Wmj+606ilpofWA24jU=

vue2-google-maps@^0.10.7:
  version "0.10.7"
  resolved "https://registry.npmmirror.com/vue2-google-maps/-/vue2-google-maps-0.10.7.tgz"
  integrity sha512-y5aBJTAsfGvpsbfgPaeWU7o7QbOeINZKsP6SB9qrBe8U8EYBFO/bTWo3rcMq/NOtgT/XssV3Nwg/2u3no6jWbw==
  dependencies:
    babel-runtime "^6.26.0"
    marker-clusterer-plus "^2.1.4"

vuedraggable@^2.23.2:
  version "2.24.3"
  resolved "https://registry.npmmirror.com/vuedraggable/-/vuedraggable-2.24.3.tgz"
  integrity sha512-6/HDXi92GzB+Hcs9fC6PAAozK1RLt1ewPTLjK0anTYguXLAeySDmcnqE8IC0xa7shvSzRjQXq3/+dsZ7ETGF3g==
  dependencies:
    sortablejs "1.10.2"

vuex@^3.6.2:
  version "3.6.2"
  resolved "https://registry.nlark.com/vuex/download/vuex-3.6.2.tgz"
  integrity sha1-I2vAhqhww655lG8QfxbeWdWJXnE=

w3c-hr-time@^1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

walker@~1.0.5:
  version "1.0.8"
  resolved "https://registry.npmmirror.com/walker/download/walker-1.0.8.tgz?cache=0&sync_timestamp=1635238315480&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwalker%2Fdownload%2Fwalker-1.0.8.tgz"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

wangeditor@^4.7.15:
  version "4.7.15"
  resolved "https://registry.npmmirror.com/wangeditor/-/wangeditor-4.7.15.tgz"
  integrity sha512-aPTdREd8BxXVyJ5MI+LU83FQ7u1EPd341iXIorRNYSOvoimNoZ4nPg+yn3FGbB93/owEa6buLw8wdhYnMCJQLg==
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@babel/runtime-corejs3" "^7.11.2"
    tslib "^2.1.0"

watch@~0.18.0:
  version "0.18.0"
  resolved "https://registry.npmmirror.com/watch/download/watch-0.18.0.tgz"
  integrity sha1-KAlUdsbffJDJYxOJkMClQj60uYY=
  dependencies:
    exec-sh "^0.2.0"
    minimist "^1.2.0"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.4.0:
  version "1.7.5"
  resolved "https://registry.npmmirror.com/watchpack/download/watchpack-1.7.5.tgz"
  integrity sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

webpack-bundle-analyzer@^2.9.0:
  version "2.13.1"
  resolved "https://registry.npmmirror.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-2.13.1.tgz?cache=0&sync_timestamp=1634019921368&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-bundle-analyzer%2Fdownload%2Fwebpack-bundle-analyzer-2.13.1.tgz"
  integrity sha1-B9IXbG6Gw83OTCPlb64qe2tK1SY=
  dependencies:
    acorn "^5.3.0"
    bfj-node4 "^5.2.0"
    chalk "^2.3.0"
    commander "^2.13.0"
    ejs "^2.5.7"
    express "^4.16.2"
    filesize "^3.5.11"
    gzip-size "^4.1.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    opener "^1.4.3"
    ws "^4.0.0"

webpack-dev-middleware@1.12.2:
  version "1.12.2"
  resolved "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-1.12.2.tgz"
  integrity sha1-+PwRIM47T8VoDO7LQ9d3lmshEF4=
  dependencies:
    memory-fs "~0.4.1"
    mime "^1.5.0"
    path-is-absolute "^1.0.0"
    range-parser "^1.0.3"
    time-stamp "^2.0.0"

webpack-dev-server@^2.9.6:
  version "2.11.5"
  resolved "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-2.11.5.tgz"
  integrity sha1-QW+96g4E7r5EpibnkdWi6zf+jEg=
  dependencies:
    ansi-html "0.0.7"
    array-includes "^3.0.3"
    bonjour "^3.5.0"
    chokidar "^2.1.2"
    compression "^1.7.3"
    connect-history-api-fallback "^1.3.0"
    debug "^3.1.0"
    del "^3.0.0"
    express "^4.16.2"
    html-entities "^1.2.0"
    http-proxy-middleware "^0.19.1"
    import-local "^1.0.0"
    internal-ip "1.2.0"
    ip "^1.1.5"
    killable "^1.0.0"
    loglevel "^1.4.1"
    opn "^5.1.0"
    portfinder "^1.0.9"
    selfsigned "^1.9.1"
    serve-index "^1.9.1"
    sockjs "0.3.19"
    sockjs-client "1.1.5"
    spdy "^4.0.0"
    strip-ansi "^3.0.0"
    supports-color "^5.1.0"
    webpack-dev-middleware "1.12.2"
    yargs "6.6.0"

webpack-merge@^4.1.0:
  version "4.2.2"
  resolved "https://registry.nlark.com/webpack-merge/download/webpack-merge-4.2.2.tgz"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.0.1, webpack-sources@^1.1.0:
  version "1.4.3"
  resolved "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz?cache=0&sync_timestamp=1636982760786&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-1.4.3.tgz"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^3.12.0:
  version "3.12.0"
  resolved "https://registry.npmmirror.com/webpack/download/webpack-3.12.0.tgz"
  integrity sha1-P540NgNwYC/PY56Xk520hvTsDXQ=
  dependencies:
    acorn "^5.0.0"
    acorn-dynamic-import "^2.0.0"
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"
    async "^2.1.2"
    enhanced-resolve "^3.4.0"
    escope "^3.6.0"
    interpret "^1.0.0"
    json-loader "^0.5.4"
    json5 "^0.5.1"
    loader-runner "^2.3.0"
    loader-utils "^1.1.0"
    memory-fs "~0.4.1"
    mkdirp "~0.5.0"
    node-libs-browser "^2.0.0"
    source-map "^0.5.3"
    supports-color "^4.2.1"
    tapable "^0.2.7"
    uglifyjs-webpack-plugin "^0.4.6"
    watchpack "^1.4.0"
    webpack-sources "^1.0.1"
    yargs "^8.0.2"

websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://registry.nlark.com/websocket-driver/download/websocket-driver-0.7.4.tgz"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

webwackify@0.1.6:
  version "0.1.6"
  resolved "https://registry.npmmirror.com/webwackify/-/webwackify-0.1.6.tgz"
  integrity sha512-pGcw1T3HpNnM/UTRQqqRkkkzythSLts05mB+7Gr00B+0VbL0m39dFL5g20rSIEUt9Wrpw+/8k+snxRlUFHhcqA==

webworkify-webpack@^2.1.5:
  version "2.1.5"
  resolved "https://registry.npmmirror.com/webworkify-webpack/-/webworkify-webpack-2.1.5.tgz"
  integrity sha512-2akF8FIyUvbiBBdD+RoHpoTbHMQF2HwjcxfDvgztAX5YwbZNyrtfUMgvfgFVsgDhDPVTlkbb5vyasqDHfIDPQw==

whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.3:
  version "1.0.5"
  resolved "https://registry.nlark.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-mimetype@^2.1.0, whatwg-mimetype@^2.2.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz?cache=0&sync_timestamp=1631993439192&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwhatwg-mimetype%2Fdownload%2Fwhatwg-mimetype-2.3.0.tgz"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^6.4.1:
  version "6.5.0"
  resolved "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-6.5.0.tgz?cache=0&sync_timestamp=1634673646149&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-6.5.0.tgz"
  integrity sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-7.1.0.tgz?cache=0&sync_timestamp=1634673646149&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-7.1.0.tgz"
  integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

when@~3.6.x:
  version "3.6.4"
  resolved "https://registry.npmmirror.com/when/-/when-3.6.4.tgz"
  integrity sha512-d1VUP9F96w664lKINMGeElWdhhb5sC+thXM+ydZGU3ZnaE09Wv6FaS+mpM9570kcDs/xMfcXJBTLsMdHEFYY9Q==

whet.extend@~0.9.9:
  version "0.9.9"
  resolved "https://registry.nlark.com/whet.extend/download/whet.extend-0.9.9.tgz"
  integrity sha1-+HfVv2SMl+WqVC+twW1qJZucEaE=

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/which-module/download/which-module-1.0.0.tgz"
  integrity sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8=

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/which-module/download/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.12, which@^1.2.9, which@^1.3.0:
  version "1.3.1"
  resolved "https://registry.nlark.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/wildcard/-/wildcard-1.1.2.tgz"
  integrity sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng==

window-size@0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/window-size/download/window-size-0.1.0.tgz"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wmf/download/wmf-1.0.2.tgz"
  integrity sha1-fRnWIQcaCMK9xrfmiKnENSmMwto=

word-wrap@~1.2.3:
  version "1.2.5"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

word@~0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/word/download/word-0.3.0.tgz"
  integrity sha1-hUIVfk+OhJ9KNjooiZLUdhLbmWE=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/wordwrap/download/wordwrap-1.0.0.tgz"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://registry.nlark.com/wordwrap/download/wordwrap-0.0.3.tgz"
  integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://registry.nlark.com/wordwrap/download/wordwrap-0.0.2.tgz"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

worker-farm@^1.5.2:
  version "1.7.0"
  resolved "https://registry.nlark.com/worker-farm/download/worker-farm-1.7.0.tgz?cache=0&sync_timestamp=1618846953836&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fworker-farm%2Fdownload%2Fworker-farm-1.7.0.tgz"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

worker-loader@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/worker-loader/-/worker-loader-2.0.0.tgz"
  integrity sha512-tnvNp4K3KQOpfRnD20m8xltE3eWh89Ye+5oj7wXEEHKac1P4oZ6p9oTj8/8ExqoSBnk9nu5Pr4nKfQ1hn2APJw==
  dependencies:
    loader-utils "^1.0.0"
    schema-utils "^0.4.0"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-2.1.0.tgz"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.1.0:
  version "2.4.3"
  resolved "https://registry.nlark.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz?cache=0&sync_timestamp=1618847057132&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrite-file-atomic%2Fdownload%2Fwrite-file-atomic-2.4.3.tgz"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/write/download/write-0.2.1.tgz"
  integrity sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=
  dependencies:
    mkdirp "^0.5.1"

ws@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/ws/download/ws-4.1.0.tgz"
  integrity sha1-qXm119TaaL9U7+BAiWfDJIaacok=
  dependencies:
    async-limiter "~1.0.0"
    safe-buffer "~5.1.0"

ws@^5.2.0:
  version "5.2.3"
  resolved "https://registry.npmmirror.com/ws/download/ws-5.2.3.tgz"
  integrity sha1-BVQQU0FJIbwpxjvuFLiw3VCwez0=
  dependencies:
    async-limiter "~1.0.0"

ws@~0.4.31:
  version "0.4.32"
  resolved "https://registry.npmmirror.com/ws/download/ws-0.4.32.tgz"
  integrity sha512-htqsS0U9Z9lb3ITjidQkRvkLdVhQePrMeu475yEfOWkAYvJ6dSjQp1tOH6ugaddzX5b7sQjMPNtY71eTzrV/kA==
  dependencies:
    commander "~2.1.0"
    nan "~1.0.0"
    options ">=0.0.5"
    tinycolor "0.x"

xe-utils@^2.3.0:
  version "2.8.3"
  resolved "https://registry.npmmirror.com/xe-utils/-/xe-utils-2.8.3.tgz"
  integrity sha512-qjiq6juv+ZPDJC3oXr9E4wjaN73G/yeqUfozpmZOmBBTw2oH5A60Jz3AvCVLKFoTQl7qfsjefR9i2gyyMz7TQw==

xe-utils@^3.0.0-beta.12:
  version "3.5.28"
  resolved "https://registry.npmmirror.com/xe-utils/-/xe-utils-3.5.28.tgz"
  integrity sha512-oeLLJ0b54QdOSSgYQ9TiKW/xAGrc9r0weCA/5UfyGdm3n3js4cNOuuf9Tml7UwgBQpl4uWMbMwUZKLh2yqPF3A==

xgplayer-flv-live@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-flv-live/-/xgplayer-flv-live-2.5.3.tgz"
  integrity sha512-M2+tLPkQ9LX5TzuxH4w+TriAtHwVYpOXcOD+Nj8WurQkwLfJ9VfB2IyYsmvpVH1rtV4RZfO1sHOWJjjHjOx29w==
  dependencies:
    xgplayer-helper-codec "2.5.3"
    xgplayer-helper-models "2.5.3"
    xgplayer-helper-transmuxers "2.5.3"
    xgplayer-helper-utils "2.5.3"

xgplayer-flv-vod@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-flv-vod/-/xgplayer-flv-vod-2.5.3.tgz"
  integrity sha512-JMVVtMXbSY/vlam3/dFNJ1gFU2RwiOJyCrLF1Y7koIsLSMy5BWU42HJJPlnsluMt+A15jwPvnOi+AWK6zA4L3A==
  dependencies:
    xgplayer-helper-codec "2.5.3"
    xgplayer-helper-models "2.5.3"
    xgplayer-helper-transmuxers "2.5.3"
    xgplayer-helper-utils "2.5.3"

xgplayer-flv@^2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-flv/-/xgplayer-flv-2.5.3.tgz"
  integrity sha512-6uNQR4ZynOy6qdgs0IK2yfAcZ0sHWsiKg3nX3k/W0mYgWWaiiKKvafye/lz17lWZtdFnINmQmKGeijUKH+4+og==
  dependencies:
    xgplayer-flv-live "2.5.3"
    xgplayer-flv-vod "2.5.3"

xgplayer-helper-codec@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-helper-codec/-/xgplayer-helper-codec-2.5.3.tgz"
  integrity sha512-gawtEyJBItVG39heBNRn+Zn5it/GEsBzYPxfRFYd8ejFJw5/VQ2KC+R2hia7Nv9DxRww7EhJhUcM5JiLXzVwSg==
  dependencies:
    xgplayer-helper-utils "2.5.3"

xgplayer-helper-models@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-helper-models/-/xgplayer-helper-models-2.5.3.tgz"
  integrity sha512-ZBjsBFCCV5gcNA4KGHKiH3Du6LhI3PiAyg4GWcogB0d/7c6+d3HDSqYos02vnFuOoUQ4hQupsRfp1nKustHnxA==

xgplayer-helper-transmuxers@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-helper-transmuxers/-/xgplayer-helper-transmuxers-2.5.3.tgz"
  integrity sha512-5UMzyFKA1h3BNz867bmMnVApYmzL6wIT00e6f5bYAEc6ILCS2CNVuYZt0QhHt7N/MX0vLRHkwjxjdl88OTjbuw==
  dependencies:
    concat-typed-array "^1.0.2"
    eventemitter3 "^4.0.7"
    xgplayer-helper-codec "2.5.3"
    xgplayer-helper-models "2.5.3"
    xgplayer-helper-utils "2.5.3"

xgplayer-helper-utils@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmmirror.com/xgplayer-helper-utils/-/xgplayer-helper-utils-2.5.3.tgz"
  integrity sha512-w8gdjCJHICQ/X+rUawSXf852gp/uc6OwgnhpYirSVzbEz4OCHONINr3NROeN88264DvKuIpXCvbbwPVSyARAsw==
  dependencies:
    eventemitter3 "^4.0.7"
    xgplayer-helper-models "2.5.3"

xgplayer-hls@^3.0.14:
  version "3.0.14"
  resolved "https://registry.npmmirror.com/xgplayer-hls/-/xgplayer-hls-3.0.14.tgz"
  integrity sha512-UiVDiDcIzQ6kiDZitbyXqRx5nX/Pi69S9Vfh5lc+6R8SsA2ZyvxWtGOQ6XfpZyXYHfl56JMbyNObOgMT1iRNLw==
  dependencies:
    eventemitter3 "^4.0.7"
    xgplayer-streaming-shared "3.0.14"
    xgplayer-transmuxer "3.0.14"

xgplayer-streaming-shared@3.0.14:
  version "3.0.14"
  resolved "https://registry.npmmirror.com/xgplayer-streaming-shared/-/xgplayer-streaming-shared-3.0.14.tgz"
  integrity sha512-XBXNjnMfFDl15kQfXNgmAkaDqRdN0PhxVFBm7+TezillpTdLmqj+HD90F6BargdZVJ4I20/YHYdb9qBg+hUDhg==
  dependencies:
    eventemitter3 "^4.0.7"

xgplayer-subtitles@^1.0.19:
  version "1.0.24"
  resolved "https://registry.npmmirror.com/xgplayer-subtitles/-/xgplayer-subtitles-1.0.24.tgz"
  integrity sha512-RsrATeCh9BxjnP9AqPOf5uuExg8wQkyOh+lBqQP9MT3Laks+6x1X3k6mAlIfeoTVry/b7I+KmdLa58YOzHLqAw==
  dependencies:
    eventemitter3 "^4.0.7"
    generate-source-map "0.0.5"

xgplayer-transmuxer@3.0.14:
  version "3.0.14"
  resolved "https://registry.npmmirror.com/xgplayer-transmuxer/-/xgplayer-transmuxer-3.0.14.tgz"
  integrity sha512-lMS2EwuA6tToCC4NuyJ5Ax0UDaaWU/YuaSkI/Bsj+vHkEzO42vgi2EerSPr91Moz05KhG/+4Vt8qMKUVFjhGTw==
  dependencies:
    "@babel/runtime" "^7.15.3"
    concat-typed-array "^1.0.2"
    crypto-es "^1.2.4"

xgplayer@^2.32.2:
  version "2.32.2"
  resolved "https://registry.npmmirror.com/xgplayer/-/xgplayer-2.32.2.tgz"
  integrity sha512-VWK8Hp5K3Fs5YoHadpyCw2zwuiOimRjkPo2PSJZPYKj59VNnL9oLruZp6cy86LHoAgWELRIQj6Nmw5SkQNByWQ==
  dependencies:
    chalk "^2.3.2"
    commander "^2.15.1"
    danmu.js "^1.1.2"
    downloadjs "1.4.7"
    draggabilly "^2.2.0"
    event-emitter "^0.3.5"
    fs-extra "^5.0.0"
    xgplayer-subtitles "^1.0.19"

xhr@2.4.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/xhr/-/xhr-2.4.0.tgz"
  integrity sha512-TUbBsdAuJbX8olk9hsDwGK8P1ri1XlV+PdEWkYw+HQQbpkiBR8PLgD1F3kQDPBs9l4Px34hP9rCYAZOCCAENbw==
  dependencies:
    global "~4.3.0"
    is-function "^1.0.1"
    parse-headers "^2.0.0"
    xtend "^4.0.0"

xlsx@^0.17.2:
  version "0.17.4"
  resolved "https://registry.npmmirror.com/xlsx/download/xlsx-0.17.4.tgz"
  integrity sha512-9aKt8g9ZLP0CUdBX8L5xnoMDFwSiLI997eQnDThCaqQMYB9AEBIRzblSSNN/ICMGLYIHUO3VKaItcedZJ3ijIg==
  dependencies:
    adler-32 "~1.2.0"
    cfb "^1.1.4"
    codepage "~1.15.0"
    crc-32 "~1.2.0"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fxml-name-validator%2Fdownload%2Fxml-name-validator-3.0.0.tgz"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xregexp@2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/xregexp/download/xregexp-2.0.0.tgz?cache=0&sync_timestamp=1628117154407&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fxregexp%2Fdownload%2Fxregexp-2.0.0.tgz"
  integrity sha1-UqY+VsoLhKfzpfPWGHLxJq16WUM=

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/xtend/download/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

xxhashjs@^0.2.1:
  version "0.2.2"
  resolved "https://registry.npm.taobao.org/xxhashjs/download/xxhashjs-0.2.2.tgz"
  integrity sha1-imJRVnYhocRqWuIE2gJJx/jKqdg=
  dependencies:
    cuint "^0.2.2"

y18n@^3.2.1:
  version "3.2.2"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-3.2.2.tgz?cache=0&sync_timestamp=1617822684820&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-3.2.2.tgz"
  integrity sha1-hckBvWRwznH8S7cjrSCbcPfyhpY=

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-4.0.3.tgz?cache=0&sync_timestamp=1617822684820&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-4.0.3.tgz"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yargs-parser@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-4.2.1.tgz?cache=0&sync_timestamp=1637031026512&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-4.2.1.tgz"
  integrity sha1-KczqwNxPA8bIe0qfIX3RjJ90hxw=
  dependencies:
    camelcase "^3.0.0"

yargs-parser@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-7.0.0.tgz?cache=0&sync_timestamp=1637031026512&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-7.0.0.tgz"
  integrity sha1-jQrELxbqVd69MyyvTEA4s+P139k=
  dependencies:
    camelcase "^4.1.0"

yargs-parser@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-8.1.0.tgz?cache=0&sync_timestamp=1637031026512&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-8.1.0.tgz"
  integrity sha1-8TdqM7Ziml0GN4KUTacyYx6WaVA=
  dependencies:
    camelcase "^4.1.0"

yargs@^10.0.3:
  version "10.1.2"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-10.1.2.tgz"
  integrity sha1-RU0HTCsWpRpD4vt4B+T53mnMtcU=
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.1.1"
    find-up "^2.1.0"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^8.1.0"

yargs@^8.0.2:
  version "8.0.2"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-8.0.2.tgz"
  integrity sha1-YpmpBVsc78lp/355wdkY3Osiw2A=
  dependencies:
    camelcase "^4.1.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    read-pkg-up "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^7.0.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-3.10.0.tgz"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yargs@6.6.0:
  version "6.6.0"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-6.6.0.tgz"
  integrity sha1-eC7CHvQDNF+DCoCMo9UTr1YGUgg=
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^4.2.0"

yauzl@^2.10.0:
  version "2.10.0"
  resolved "https://registry.npm.taobao.org/yauzl/download/yauzl-2.10.0.tgz"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

zrender@3.7.4:
  version "3.7.4"
  resolved "https://registry.npmmirror.com/zrender/-/zrender-3.7.4.tgz"
  integrity sha512-5Nz7+L1wIoL0+Pp/iOP56jD6eD017qC9VRSgUBheXBiAHgOBJZ4uh4/g6e83acIwa8RKSyZf/FlceKu5ntUuxQ==

zrender@5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/zrender/-/zrender-5.3.1.tgz"
  integrity sha512-7olqIjy0gWfznKr6vgfnGBk7y4UtdMvdwFmK92vVQsQeDPyzkHW1OlrLEKg6GHz1W5ePf0FeN1q2vkl/HFqhXw==
  dependencies:
    tslib "2.3.0"
