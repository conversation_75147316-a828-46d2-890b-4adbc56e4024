"use strict";

/**
 * 开发环境
 */
;

(function () {
  window.SITE_CONFIG = {}; // api接口请求地址

  window.SITE_CONFIG['baseUrl'] = 'http://192.168.1.109:8080'; // websocket 通讯地址

  window.SITE_CONFIG['WSURL'] = 'ws://192.168.1.109:8080'; //人群聚集websocket

  window.SITE_CONFIG['INSIDEURL'] = 'ws://192.168.1.109:8088'; // 素材加载地址 服务器
  // window.SITE_CONFIG['FileURL'] = 'http://192.168.31.64:8080/'
  // rtmp地址

  window.SITE_CONFIG['rtmp'] = 'http://192.168.1.133:8888/live?port=1935&app=live&stream='; // window.SITE_CONFIG['rtmp'] = 'http://192.168.1.115:8888/live/'
  // 是否取消邮箱手机号验证

  window.SITE_CONFIG['isVerify'] = false; // cdn地址 = 域名 + 版本号

  window.SITE_CONFIG['domain'] = './'; // 域名

  window.SITE_CONFIG['version'] = ''; // 版本号(年月日时分)

  window.SITE_CONFIG['cdnUrl'] = window.SITE_CONFIG.domain + window.SITE_CONFIG.version;
})();
