/**
 * 开发环境
 */
;(function () {
  window.SITE_CONFIG = {}

  // api接口请求地址
  window.SITE_CONFIG['baseUrl'] = 'aips4'

  // websocket 通讯地址
  window.SITE_CONFIG['WSURL'] = 'wss://www.ledokcloud.com:8443'

  //人群聚集websocket
  window.SITE_CONFIG['INSIDEURL']='wss://www.ledokcloud.com:8488'

  // 素材加载地址 服务器
  // window.SITE_CONFIG['FileURL'] = 'https://www.ledokcloud.com/aips4/'

  // rtmp地址
  window.SITE_CONFIG['rtmp'] = '/rtmp'


  // 是否取消邮箱手机号验证
  window.SITE_CONFIG['isVerify'] = false

  // 是否显示视频教程
  window.SITE_CONFIG['isShowVideo'] = true

  // 是否开启会员
  window.SITE_CONFIG['isVip'] = false

  // cdn地址 = 域名 + 版本号
  window.SITE_CONFIG['domain'] = './' // 域名
  window.SITE_CONFIG['version'] = '' // 版本号(年月日时分)
  window.SITE_CONFIG['cdnUrl'] = window.SITE_CONFIG.domain + window.SITE_CONFIG.version
})()

/**
 * 动态加载初始资源
 */
;(function () {
  var resList = {
    icon: window.SITE_CONFIG.cdnUrl + '/static/img/favicon.ico'
  };

  // 图标
  (function () {
    var _icon = document.createElement('link')
    _icon.setAttribute('rel', 'shortcut icon')
    _icon.setAttribute('type', 'image/x-icon')
    _icon.setAttribute('href', resList.icon)
    document.getElementsByTagName('head')[0].appendChild(_icon)
  })();
})()
